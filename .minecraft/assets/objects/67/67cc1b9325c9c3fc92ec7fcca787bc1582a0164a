{"accessibility.onboarding.accessibility.button": "Barrièrefräiheet...", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON><PERSON>, fir d'Sproochausgab ze aktivéieren", "accessibility.onboarding.screen.title": "Wëllkomm a Minecraft!\n\nWëlls du d'Sproochausgab aktivéieren oder d'Accessibilitéitsastellungen besichen?", "addServer.add": "Fäerdeg", "addServer.enterIp": "Serveradress", "addServer.enterName": "Servernumm", "addServer.resourcePack": "Server-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addServer.resourcePack.disabled": "Desaktiv<PERSON><PERSON><PERSON>", "addServer.resourcePack.enabled": "Aktiv<PERSON><PERSON><PERSON>", "addServer.resourcePack.prompt": "Nof<PERSON><PERSON>", "addServer.title": "Serverinformatiounen editéieren", "advMode.command": "<PERSON><PERSON><PERSON>", "advMode.mode": "Modus", "advMode.mode.auto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "Ëmmer aktiv", "advMode.mode.conditional": "<PERSON><PERSON><PERSON>", "advMode.mode.redstone": "Impuls", "advMode.mode.redstoneTriggered": "<PERSON><PERSON><PERSON> Redstone", "advMode.mode.sequence": "Verketten", "advMode.mode.unconditional": "<PERSON><PERSON><PERSON><PERSON>", "advMode.notAllowed": "Nëmmen en Operator am Kreativ-Modus kann ee Befehl aginn", "advMode.notEnabled": "Befehlsbléck sinn op dësem Server net aktivéiert", "advMode.previousOutput": "Läscht Ausgab", "advMode.setCommand": "<PERSON><PERSON><PERSON> aginn", "advMode.setCommand.success": "Befeel gesat: %s", "advMode.trackOutput": "Ausgab verfollegen", "advMode.triggering": "Ausléisen", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Onbekannte Fortschrëtt: '%s'", "advancements.adventure.adventuring_time.description": "Entdeck all Biomer", "advancements.adventure.adventuring_time.title": "Zäit fir eng Aventure", "advancements.adventure.arbalistic.description": "Bréng fë<PERSON>f <PERSON>iddlech Kreature mat engem Armbrustschoss ëm", "advancements.adventure.arbalistic.title": "Armbrustschütze", "advancements.adventure.avoid_vibration.description": "Béck dech nobäi engem Sculk-Sensor oder Wärter fir ze verhënneren dass bemierkt gëss", "advancements.adventure.avoid_vibration.title": "Schläichen 100", "advancements.adventure.blowback.description": "Bréng eng Breeze mat engem ofgewiertem Breeze-<PERSON><PERSON><PERSON><PERSON>m", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.brush_armadillo.description": "<PERSON><PERSON><PERSON> <PERSON>, fir eng <PERSON><PERSON> vum Panzer vun engem Armadillo ze léisen", "advancements.adventure.brush_armadillo.title": "Ass dat net leiw?", "advancements.adventure.bullseye.description": "<PERSON><PERSON><PERSON>M<PERSON>tt vun engem Zielblock aus enger Distanz vun op d'mannst 30 Bléck", "advancements.adventure.bullseye.title": "Volltreffer", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Stell aus féier Dëppeschierbelen en dekoréiert Dëppen hier", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Virsiichteg Restauratioun", "advancements.adventure.crafters_crafting_crafters.description": "<PERSON><PERSON><PERSON> dech bei engem Wierker wann dësen e Wier<PERSON> hierstellt", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON><PERSON><PERSON> wier<PERSON>e Wierker", "advancements.adventure.fall_from_world_height.description": "Fal am fräie Fall vun der Spëtzt vun der Welt (Baulimitt) bis ënnen an d'Welt an iwwerlief", "advancements.adventure.fall_from_world_height.title": "Höhlen & Bierger", "advancements.adventure.heart_transplanter.description": "Setz en Knacks-<PERSON>ä<PERSON>z mat der richteger Ausriichtung tëscht zwee Bleech-Eechenholzbléck.", "advancements.adventure.heart_transplanter.title": "Häerztransplantatioun", "advancements.adventure.hero_of_the_village.description": "Verdeedeg e Duerf erfollegräich virun engem Iwwerfall", "advancements.adventure.hero_of_the_village.title": "Held vum Duerf", "advancements.adventure.honey_block_slide.description": "Sprang an een <PERSON>, fir dä<PERSON>uertz ofzefänken", "advancements.adventure.honey_block_slide.title": "Eng pecheg <PERSON>", "advancements.adventure.kill_a_mob.description": "Bréng e Monster ëm", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_all_mobs.description": "Bréng all Monster mindestens emol ëm", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Bréng eng Kreatur nobäi engem Sculk-Katalysator ëm", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Et verbreet sech", "advancements.adventure.lighten_up.description": "Kraz eng Ko<PERSON>r mat enger <PERSON> of, fir se méi hell ze maachen", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "<PERSON><PERSON><PERSON><PERSON> en Awunner vun engem ongewollte Schock ouni e Feier ze auszuléisen", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Iwwerspannungsschutz", "advancements.adventure.minecraft_trials_edition.description": "Betriet eng Prüfungskammer", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON><PERSON><PERSON>(s)-<PERSON><PERSON><PERSON>", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON>iss mat enger <PERSON>", "advancements.adventure.ol_betsy.title": "<PERSON> Klabes <PERSON> geséchert", "advancements.adventure.overoverkill.description": "Verursaach mam Sträitkolben 50 Häerze Schued an engem eenzegen Schwong", "advancements.adventure.overoverkill.title": "Kampfgroussmeeschter", "advancements.adventure.play_jukebox_in_meadows.description": "Maach d'Wise lieweg mam Geraisch vu Musek aus engem Plattespiller", "advancements.adventure.play_jukebox_in_meadows.title": "Deng Welt sinn d'Bierger", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Lies d'Signalstäerkt vun engem huele Bicherregal mat engem Komparator aus", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON><PERSON>i <PERSON> vu Bicher", "advancements.adventure.revaulting.description": "<PERSON><PERSON><PERSON><PERSON> en ominösen Tresor mat engem ominösem Schlëssel op", "advancements.adventure.revaulting.title": "Panzerknacker", "advancements.adventure.root.description": "Aventure, Erfuerschung a Kampf", "advancements.adventure.root.title": "Aventure", "advancements.adventure.salvage_sherd.description": "Pinsel e verdächte Block of, fir eng Dëppeschierbel ze fannen", "advancements.adventure.salvage_sherd.title": "Respektéiere vun den Iwwerreschter", "advancements.adventure.shoot_arrow.description": "<PERSON><PERSON><PERSON> eng <PERSON> mat engem Feil", "advancements.adventure.shoot_arrow.title": "Zielübungen", "advancements.adventure.sleep_in_bed.description": "<PERSON>hlof an engem Bett fir däi Respawnpunkt ze änneren", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "advancements.adventure.sniper_duel.description": "Bréng e Skelett aus enger Distanz vu mindestens 50 Meter ëm", "advancements.adventure.sniper_duel.title": "Sniperduell", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON> op den Enderdraach duerch e Spektiv", "advancements.adventure.spyglass_at_dragon.title": "Ass et e Fliger?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON>ck op e Ghast duerch e Spektiv", "advancements.adventure.spyglass_at_ghast.title": "Ass et e Ballon?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON> op e Papagei duerch e Spektiv", "advancements.adventure.spyglass_at_parrot.title": "Ass et e Vull?", "advancements.adventure.summon_iron_golem.description": "<PERSON>u een E<PERSON>golem, vir bei der Verdeedegung vun engem Duerf ze hëllefen", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.throw_trident.description": "<PERSON><PERSON><PERSON> d<PERSON> op eppes.\nHiweis: Et ass keng gutt Iddi, deng eenzeg Waff fort ze geheien.", "advancements.adventure.throw_trident.title": "E Witz zum ewechgeheien", "advancements.adventure.totem_of_undying.description": "Benotz en Totem vun der Onstierflechkeet fir den Doud ze hannergoen", "advancements.adventure.totem_of_undying.title": "Postmortal", "advancements.adventure.trade.description": "<PERSON><PERSON><PERSON><PERSON> en Handel mat engem <PERSON>wunner of", "advancements.adventure.trade.title": "Wat fir en Deal!", "advancements.adventure.trade_at_world_height.description": "Handel mat engem Awunner op der maximaler Bauhéicht", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> op d'mannst eemol un: <PERSON><PERSON>, <PERSON><PERSON>ut, Rib, Ward, Silence, Vex, Tide, Wayfinder", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "<PERSON><PERSON><PERSON><PERSON> mat <PERSON>", "advancements.adventure.trim_with_any_armor_pattern.description": "Maacht eng ofgeschnidden Rüstung op engem Smithing Table", "advancements.adventure.trim_with_any_armor_pattern.title": "En neit Ausgesinn", "advancements.adventure.two_birds_one_arrow.description": "Bréng zwee Phantomer mat engem Duerchschoss ëm", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON><PERSON> mat engem <PERSON>", "advancements.adventure.under_lock_and_key.description": "<PERSON><PERSON><PERSON><PERSON> en Tresor mat engem Prüfungsschlëssel op", "advancements.adventure.under_lock_and_key.title": "<PERSON><PERSON> a Schlëssel", "advancements.adventure.use_lodestone.description": "Riicht e Kompass op engem Leetsteen aus", "advancements.adventure.use_lodestone.title": "<PERSON> Klenge <PERSON>", "advancements.adventure.very_very_frightening.description": "<PERSON><PERSON><PERSON> en Duerfbewunner mat engem Blëtz", "advancements.adventure.very_very_frightening.title": "Angscht a Schrecken", "advancements.adventure.voluntary_exile.description": "Bréng e Raiberkapitän ëm.\nPass eventuell op, dech vir de Moment vu Dierfer fort ze halen …", "advancements.adventure.voluntary_exile.title": "Fräiwëllegen Exil", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Spazéier op Pudderschnéi … ouni doran ënnerzegoen", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Liicht wéi e Kanéngchen", "advancements.adventure.who_needs_rockets.description": "Benot<PERSON> e Wandstouss fir dech 8 Bléck an d'Héischt ze schleideren", "advancements.adventure.who_needs_rockets.title": "Wee braucht scho <PERSON>?", "advancements.adventure.whos_the_pillager_now.description": "Looss de Plëmmert seng eege Medezin sch<PERSON>n", "advancements.adventure.whos_the_pillager_now.title": "Ween ass elo de Plë<PERSON>?", "advancements.empty": "Hei sch<PERSON>int n<PERSON>t ze sinn ...", "advancements.end.dragon_breath.description": "Sammel Draachenotem an enger Glasfläsch", "advancements.end.dragon_breath.title": "Du brauchs eng Peffermënz", "advancements.end.dragon_egg.description": "<PERSON> un", "advancements.end.dragon_egg.title": "<PERSON><PERSON><PERSON> nächst Generatioun", "advancements.end.elytra.description": "<PERSON><PERSON>", "advancements.end.elytra.title": "B<PERSON> d'Onendlechkeet a weider", "advancements.end.enter_end_gateway.description": "Entkomm vun der Insel", "advancements.end.enter_end_gateway.title": "Am Transit", "advancements.end.find_end_city.description": "<PERSON><PERSON> eran, wat kéint scho <PERSON>é<PERSON>en?", "advancements.end.find_end_city.title": "D'Stad um Enn vum Spill", "advancements.end.kill_dragon.description": "<PERSON><PERSON>", "advancements.end.kill_dragon.title": "<PERSON><PERSON><PERSON><PERSON> d'Enn", "advancements.end.levitate.description": "Schwief duerch e Skulker-Projektil 50 Bléck erop", "advancements.end.levitate.title": "<PERSON><PERSON><PERSON><PERSON> vun hei uewen", "advancements.end.respawn_dragon.description": "<PERSON><PERSON> den Enderdraach zer<PERSON>ck an d'Liewen", "advancements.end.respawn_dragon.title": "D'Enn ... <PERSON><PERSON>n erëm ...", "advancements.end.root.description": "... oder den Ufank?", "advancements.end.root.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Looss en Hëllefsgeescht e Kuch bei engem Noteblock ofginn", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Gebuertsdagslidd", "advancements.husbandry.allay_deliver_item_to_player.description": "Looss der vun engem Hëllefsgeescht e Géigestand liwweren", "advancements.husbandry.allay_deliver_item_to_player.title": "Du hues e Frënd a mir", "advancements.husbandry.axolotl_in_a_bucket.description": "Fang en Axolotl an engem Eemer", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON> s<PERSON><PERSON>", "advancements.husbandry.balanced_diet.description": "Iess alles wat iessbar ass, souguer wann et net gutt fir dech ass", "advancements.husbandry.balanced_diet.title": "Equilibréiert Ernärung", "advancements.husbandry.breed_all_animals.description": "Ziicht all Déierenaarten!", "advancements.husbandry.breed_all_animals.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.breed_an_animal.description": "<PERSON><PERSON> zwee Deieren sech reproduzéieren", "advancements.husbandry.breed_an_animal.title": "Aus Zwee gëtt Dräi", "advancements.husbandry.complete_catalogue.description": "<PERSON>äm all d'Zorte Katzen!", "advancements.husbandry.complete_catalogue.title": "E komplette Kazelog", "advancements.husbandry.feed_snifflet.description": "Fidder e Schnoffeleschen", "advancements.husbandry.feed_snifflet.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.fishy_business.description": "<PERSON><PERSON><PERSON> ee <PERSON>", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON>glé<PERSON>", "advancements.husbandry.froglights.description": "Hief all Fräschluuchten an dengem Inventar", "advancements.husbandry.froglights.title": "<PERSON> vereen<PERSON>!", "advancements.husbandry.kill_axolotl_target.description": "Verbann dech mat engem Axolotl a gewann e Kampf", "advancements.husbandry.kill_axolotl_target.title": "D'Heelkraaft vu Frëndschaft!", "advancements.husbandry.leash_all_frog_variants.description": "Hief all Fräschenaarten un enger Lenkt", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON> den Kader an d'Stad spréngt", "advancements.husbandry.make_a_sign_glow.description": "Looss den Text vun egal wéi engem Schëld zum liichte bréngen", "advancements.husbandry.make_a_sign_glow.title": "Glanz a <PERSON>!", "advancements.husbandry.netherite_hoe.description": "Benotz e Netheritbarren, fir en He<PERSON> opzewäerten, an denk duerno iwwer deng Liewensendscheedungen no", "advancements.husbandry.netherite_hoe.title": "Eeschhaft Higab", "advancements.husbandry.obtain_sniffer_egg.description": "Beschaf e Schnoffeler-Ee", "advancements.husbandry.obtain_sniffer_egg.title": "Schnoffelt Interessant", "advancements.husbandry.place_dried_ghast_in_water.description": "Setz en gedréchente Ghast-Block an d’W<PERSON>sser.", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON><PERSON><PERSON> genuch!", "advancements.husbandry.plant_any_sniffer_seed.description": "Planz een Schnoffeler Som", "advancements.husbandry.plant_any_sniffer_seed.title": "Planz d'Vergaangenheet", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON><PERSON> e <PERSON>m a kuck em beim <PERSON> no", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "<PERSON><PERSON> <PERSON> vun engem Wollef mat enger <PERSON>é<PERSON> ewech", "advancements.husbandry.remove_wolf_armor.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.repair_wolf_armor.description": "Flek e beschiedegte Wollefspanzer mat Armadillo-Schuppen vollständeg", "advancements.husbandry.repair_wolf_armor.title": "Sou gutt wéi néi", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON><PERSON><PERSON> an e Boot a schwamm mat enger Geess", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Wat och ëmmer Är Geess schwëmmt!", "advancements.husbandry.root.description": "D'Welt ass voll mat <PERSON> an <PERSON>en", "advancements.husbandry.root.title": "Landwirtschaft", "advancements.husbandry.safely_harvest_honey.description": "Benotzt e Lagerfeier fir Hunneg aus engem Beiestack mat enger Glasfläsch ze sammelen ouni d'Beie rosen ze maachen", "advancements.husbandry.safely_harvest_honey.title": "Sssief eise Gaascht", "advancements.husbandry.silk_touch_nest.description": "Beweeg e vun dräi Beie bewunnten Nascht mat Pre<PERSON>oun", "advancements.husbandry.silk_touch_nest.title": "Beiewanderung", "advancements.husbandry.tactical_fishing.description": "Fänk e Fësch … ouni Angel!", "advancements.husbandry.tactical_fishing.title": "Taktescht fëschen", "advancements.husbandry.tadpole_in_a_bucket.description": "Fänk e Kauzekapp an engem Eemer", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON>", "advancements.husbandry.tame_an_animal.description": "Zäm en Déier", "advancements.husbandry.tame_an_animal.title": "Allerbescht Frënn", "advancements.husbandry.wax_off.description": "Kraz Wuess vun engem Kofferblock erof!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON>", "advancements.husbandry.wax_on.description": "<PERSON><PERSON><PERSON> e Kofferblock mat enger <PERSON>!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON> drun", "advancements.husbandry.whole_pack.description": "Zäm all Wollefsvarianten", "advancements.husbandry.whole_pack.title": "<PERSON> gan<PERSON>", "advancements.nether.all_effects.description": "Hief all d'Statuseffekter gläichzäiteg", "advancements.nether.all_effects.title": "Wei hu mer dat gepackt?", "advancements.nether.all_potions.description": "Hief all d'Dronkeffekter gläichzäiteg", "advancements.nether.all_potions.title": "Eng g<PERSON><PERSON><PERSON><PERSON><PERSON> Mëschung", "advancements.nether.brew_potion.description": "Brau en Dronk", "advancements.nether.brew_potion.title": "Alchimie", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON> e Séilenanker maximal op", "advancements.nether.charge_respawn_anchor.title": "Net Ganz \"Nén<PERSON>\" <PERSON>wen", "advancements.nether.create_beacon.description": "Konstruéier a placéier e Liichtfeier", "advancements.nether.create_beacon.title": "Bréng d'Liichtfeier heem", "advancements.nether.create_full_beacon.description": "Bréng e Liichtfeier op voll Stäerkt", "advancements.nether.create_full_beacon.title": "Liichttuermwärter", "advancements.nether.distract_piglin.description": "<PERSON><PERSON> mat Gold of", "advancements.nether.distract_piglin.title": "Oh glänzend", "advancements.nether.explore_nether.description": "Entdeck all Netherbiomer", "advancements.nether.explore_nether.title": "<PERSON><PERSON><PERSON> Touristes<PERSON>", "advancements.nether.fast_travel.description": "Ben<PERSON>z den Nether fir 7 km an der Uewerwelt ze reesen", "advancements.nether.fast_travel.title": "Subraumtransport", "advancements.nether.find_bastion.description": "Betriet d'Ruin vun enger Ba<PERSON>un", "advancements.nether.find_bastion.title": "Dat waren nach Zäiten", "advancements.nether.find_fortress.description": "Briech an eng Netherfestung an", "advancements.nether.find_fortress.title": "<PERSON>g schreck<PERSON>ch Festung", "advancements.nether.get_wither_skull.description": "Sammel ee Witherskelettkapp", "advancements.nether.get_wither_skull.title": "Grujelegt Skelett", "advancements.nether.loot_bastion.description": "Plëmm eng Këscht an enger Bastiounsruin", "advancements.nether.loot_bastion.title": "Kampfschwäi", "advancements.nether.netherite_armor.description": "Besuerg dir eng komplett Netheriterüstung", "advancements.nether.netherite_armor.title": "Bedeck mech mat <PERSON><PERSON><PERSON>", "advancements.nether.obtain_ancient_debris.description": "<PERSON><PERSON><PERSON>", "advancements.nether.obtain_ancient_debris.title": "Verstoppt an den Déiften", "advancements.nether.obtain_blaze_rod.description": "<PERSON><PERSON><PERSON><PERSON>er eng Lou vu senger <PERSON>", "advancements.nether.obtain_blaze_rod.title": "<PERSON><PERSON><PERSON> mam <PERSON>", "advancements.nether.obtain_crying_obsidian.description": "Beschaf kräischenden Obsidian", "advancements.nether.obtain_crying_obsidian.title": "Wien schneit Zwiwwelen?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON> e G<PERSON> mat sengen eegene Waffen", "advancements.nether.return_to_sender.title": "<PERSON>er<PERSON><PERSON> zum Absender", "advancements.nether.ride_strider.description": "Reit e Schreiter mat engem verduerwen Champignon op engem Bengel", "advancements.nether.ride_strider.title": "<PERSON><PERSON>", "advancements.nether.ride_strider_in_overworld_lava.description": "<PERSON><PERSON> e Schreiter fir eng laaaaaang Rees op engem Lavaséi an der Iwwerwelt", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON><PERSON> w<PERSON><PERSON>", "advancements.nether.root.description": "Wëllkomm an der Hell", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "Entf<PERSON>ier ee G<PERSON> aus dem Nether, bréng en sécher an d'Uewerwelt ... a bréng en dann ëm", "advancements.nether.uneasy_alliance.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.use_lodestone.description": "Benotz e Kompass un engem Leetsteen", "advancements.nether.use_lodestone.title": "<PERSON> Klenge <PERSON>", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "<PERSON><PERSON><PERSON><PERSON>ch een <PERSON> an heel en dann", "advancements.story.cure_zombie_villager.title": "Zombiedokter", "advancements.story.deflect_arrow.description": "Lenk e Geschoss mat engem <PERSON> of", "advancements.story.deflect_arrow.title": "Net haut, <PERSON><PERSON>i", "advancements.story.enchant_item.description": "Verzauber ee Géigestand op engem Zauberdësch", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "Betriet d'Endportal", "advancements.story.enter_the_end.title": "<PERSON><PERSON><PERSON><PERSON>?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON>, fänk en Netherportal un a betriet et", "advancements.story.enter_the_nether.title": "<PERSON> mussen nach méi Déif", "advancements.story.follow_ender_eye.description": "Verfolleg en Endera", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON><PERSON>", "advancements.story.form_obsidian.description": "Beschaf en Obsidianblock", "advancements.story.form_obsidian.title": "Ice Bucket Challenge", "advancements.story.iron_tools.description": "Verbesser deng <PERSON>", "advancements.story.iron_tools.title": "Ze vill Eisen am Blutt", "advancements.story.lava_bucket.description": "<PERSON><PERSON><PERSON> en <PERSON>emer <PERSON>", "advancements.story.lava_bucket.title": "<PERSON>passen, waarm!", "advancements.story.mine_diamond.description": "<PERSON><PERSON><PERSON>", "advancements.story.mine_diamond.title": "Diamanten!", "advancements.story.mine_stone.description": "<PERSON><PERSON> mat denger ne<PERSON> oof", "advancements.story.mine_stone.title": "Steenzäit", "advancements.story.obtain_armor.description": "<PERSON><PERSON><PERSON>tz dech mat engem Rüstungsdeel aus Eisen", "advancements.story.obtain_armor.title": "<PERSON>ach dech chic", "advancements.story.root.description": "De Kär an d'Geschicht vum Spill", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamantrüstung rett Liewen", "advancements.story.shiny_gear.title": "Bedeck mech mat <PERSON>", "advancements.story.smelt_iron.description": "Schmelz Réieisen am Uewen zu engem Eisebarren", "advancements.story.smelt_iron.title": "Eisezäit", "advancements.story.upgrade_tools.description": "<PERSON><PERSON> eng besser <PERSON><PERSON> hier", "advancements.story.upgrade_tools.title": "Technesche Fortschrëtt", "advancements.toast.challenge": "Aufgab ofgeschloss!", "advancements.toast.goal": "<PERSON><PERSON> areecht!", "advancements.toast.task": "Fortschrëtt erzielt!", "argument.anchor.invalid": "Ongülteg Objetankerpositioun: '%s'", "argument.angle.incomplete": "Onvollstänneg (1 Bléckwénkel erwaart)", "argument.angle.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.block.id.invalid": "Onbekannte Block: '%s'", "argument.block.property.duplicate": "Zoustand '%s' kann nëmmen emol fir de Block %s gesat ginn", "argument.block.property.invalid": "Block %s akzeptéiert '%s' fir de Blockzoustand '%s' net", "argument.block.property.novalue": "<PERSON><PERSON>ert fir Zoustand '%s' vum Block %s erwaart", "argument.block.property.unclosed": "Ofschléissend Klammer ] fir Blockzoustand erwaart", "argument.block.property.unknown": "Block %s besetzt den Zoustand '%s' net", "argument.block.tag.disallowed": "Tags sinn hei net er<PERSON>, nëmmen tatsächlech Bléck", "argument.color.invalid": "Onbekannt Faarf '%s'", "argument.component.invalid": "Ongülteg Textkomponent: '%s'", "argument.criteria.invalid": "Onbekannte Krittär: '%s'", "argument.dimension.invalid": "Onbekannt Dimensioun '%s'", "argument.double.big": "Kommazuel däerf net méi grouss wei %s sinn, %s ass ze grouss", "argument.double.low": "Kommazuel däerf net méi kleng wei %s sinn, %s ass ze kleng", "argument.entity.invalid": "Ongültegen Numm oder ongülteg UUID", "argument.entity.notfound.entity": "Et gouf keen Objet fonnt", "argument.entity.notfound.player": "Et gouf kee <PERSON>ller fonnt", "argument.entity.options.advancements.description": "<PERSON><PERSON><PERSON> <PERSON>", "argument.entity.options.distance.description": "<PERSON><PERSON><PERSON> zum Objet", "argument.entity.options.distance.negative": "<PERSON><PERSON>z kann net negativ sinn", "argument.entity.options.dx.description": "Objeten tëschent x an x + dx", "argument.entity.options.dy.description": "Objeten tëschent y an y + dy", "argument.entity.options.dz.description": "Objeten tëschent z an z + dz", "argument.entity.options.gamemode.description": "Spiller mat <PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.inapplicable": "Optioun '%s' ass hei net applizéierbar", "argument.entity.options.level.description": "Erfarungslevel", "argument.entity.options.level.negative": "Erfarungslevel däerf net negativ sinn", "argument.entity.options.limit.description": "Maximal Unzuel un Objeten déi zeréck kenne ginn", "argument.entity.options.limit.toosmall": "<PERSON>itt muss mindestens 1 sinn", "argument.entity.options.mode.invalid": "Ongültegen oder onbekannte Spillmodus '%s'", "argument.entity.options.name.description": "Objetnumm", "argument.entity.options.nbt.description": "Objete mat NBT", "argument.entity.options.predicate.description": "Benotzerdefinéiert Predikat", "argument.entity.options.scores.description": "<PERSON>b<PERSON><PERSON> mat <PERSON>", "argument.entity.options.sort.description": "Ob<PERSON><PERSON> sort<PERSON>", "argument.entity.options.sort.irreversible": "Ongülteg oder onbekannt Zortéierung '%s'", "argument.entity.options.tag.description": "Objet mat <PERSON>", "argument.entity.options.team.description": "Objeten am Team", "argument.entity.options.type.description": "Objete vum Typ", "argument.entity.options.type.invalid": "Ongültegen oder onbekannten Objettyp '%s'", "argument.entity.options.unknown": "Onbekannt Optioun '%s'", "argument.entity.options.unterminated": "Enn vun den Optiounen er<PERSON>art", "argument.entity.options.valueless": "<PERSON><PERSON><PERSON> fir Filter '%s' erwaart", "argument.entity.options.x.description": "X-Positioun", "argument.entity.options.x_rotation.description": "x-Rotatioun vum Objet", "argument.entity.options.y.description": "Y-Positioun", "argument.entity.options.y_rotation.description": "y-Rotatioun vum Objet", "argument.entity.options.z.description": "Z-Positioun", "argument.entity.selector.allEntities": "All Objeten", "argument.entity.selector.allPlayers": "All Spiller", "argument.entity.selector.missing": "<PERSON><PERSON>e Selektor-Typ", "argument.entity.selector.nearestEntity": "Nächsten Objet", "argument.entity.selector.nearestPlayer": "Nächste Spiller", "argument.entity.selector.not_allowed": "Selektor net erlaabt", "argument.entity.selector.randomPlayer": "Zoufällege Spiller", "argument.entity.selector.self": "Aktuellt Objet", "argument.entity.selector.unknown": "Onbekannte Selektor-Typ '%s'", "argument.entity.toomany": "<PERSON><PERSON><PERSON><PERSON> een Objet ass erlab, awer de Selektor deen ugi gouf kéint méi wei ee liwweren", "argument.enum.invalid": "Ongültege <PERSON> \"%s\"", "argument.float.big": "Kommazuel däerf net méi grouss wei %s sinn, %s ass ze grouss", "argument.float.low": "Kommazuel däerf net méi kleng wei %s sinn, %s ass ze kleng", "argument.gamemode.invalid": "Onbekannte Spillmodus: %s", "argument.hexcolor.invalid": "Onvalide Hex-Faarfkod.", "argument.id.invalid": "Ongülteg ID", "argument.id.unknown": "Onbekannt ID: '%s'", "argument.integer.big": "Ganzzuel däerf net méi grouss wei %s sinn, %s ass ze grouss", "argument.integer.low": "Ganzzuel däerf net méi kleng wei %s sinn, %s ass ze kleng", "argument.item.id.invalid": "Onbekannte Géigestand '%s'", "argument.item.tag.disallowed": "Tags sinn hei net er<PERSON>, nëmmen tatsächlech Objeten", "argument.literal.incorrect": "Zeechefolleg '%s' erwaart", "argument.long.big": "Laang Ganzzuel d<PERSON>f net méi grouss wei %s sinn, %s ass ze grouss", "argument.long.low": "Laang Ganzzuel däerf net méi kleng wei %s sinn, %s ass ze kleng", "argument.message.too_long": "Chatnoriicht war ze laang (%s > héchstens %s <PERSON><PERSON>chen)", "argument.nbt.array.invalid": "Ongültegen Array-Typ '%s'", "argument.nbt.array.mixed": "%s kann net an %s agefüügt ginn", "argument.nbt.expected.compound": "Verbonddaten er<PERSON>art", "argument.nbt.expected.key": "Eegenschaft erwaart", "argument.nbt.expected.value": "<PERSON><PERSON><PERSON>", "argument.nbt.list.mixed": "%s kann net zur Lëscht vun %s dobäi gefüügt ginn", "argument.nbt.trailing": "<PERSON><PERSON><PERSON><PERSON>", "argument.player.entities": "<PERSON><PERSON><PERSON> kann nëmmen op ee Spiller appliz<PERSON><PERSON>t ginn, de Selektor deen ugi gouf schléisst awer och Objeten an", "argument.player.toomany": "<PERSON><PERSON><PERSON><PERSON> ee <PERSON><PERSON> ass erlab, awer de Selektor deen ugi gouf kéint méi wei ee liwweren", "argument.player.unknown": "<PERSON><PERSON><PERSON> existéiert net", "argument.pos.missing.double": "Koordinaten er<PERSON>art", "argument.pos.missing.int": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON><PERSON>", "argument.pos.mixed": "Lokal a global Koordinaten däerfen net gemëscht ginn (entweder alles mat ^ oder ouni)", "argument.pos.outofbounds": "<PERSON><PERSON><PERSON> ass ausserhalb vun den erlaabte Grenzen.", "argument.pos.outofworld": "<PERSON><PERSON><PERSON> ass ausserhalb vun der Welt!", "argument.pos.unloaded": "<PERSON><PERSON><PERSON> ass net gelueden", "argument.pos2d.incomplete": "Onvollstänneg (2 Koordinaten erwaart)", "argument.pos3d.incomplete": "Onvollstänneg (3 Koordinaten erwaart)", "argument.range.empty": "<PERSON><PERSON><PERSON> oder Wäerteb<PERSON><PERSON><PERSON> er<PERSON>", "argument.range.ints": "Et sinn nëmme Ganzzuelen erlaabt ouni Kommastellen", "argument.range.swapped": "Minimalwäert dä<PERSON>ft net méi grouss wei de Maximalwäert sinn", "argument.resource.invalid_type": "Element ‚%s‘ huet e falschen Typ ‚%s‘ (‚%s‘ erwaart)", "argument.resource.not_found": "Element ‚%s‘ vum Typ ‚%s‘ net fonnt", "argument.resource_or_id.failed_to_parse": "Struktur konnt net analyséiert ginn: %s", "argument.resource_or_id.invalid": "Ongülteg ID oder ongültegt Etikett", "argument.resource_or_id.no_such_element": "Kann Element  am Register net fannen.", "argument.resource_selector.not_found": "Keng Iwwerastëmmmung fir Selektor ‚%s‘ vum Typ ‚%s‘", "argument.resource_tag.invalid_type": "Etikett ‚%s‘ huet en falschen Typ ‚%s‘ (‚%s‘ erwaart)", "argument.resource_tag.not_found": "Etikett ‚%s‘ vum Typ ‚%s‘ net fonnt", "argument.rotation.incomplete": "Onvollstänneg (2 Koordinaten erwaart)", "argument.scoreHolder.empty": "Et goufe keng relevant Punktentitulaire fonnt", "argument.scoreboardDisplaySlot.invalid": "Onbekannten Displayslot: '%s'", "argument.style.invalid": "Ongültege Style: %s", "argument.time.invalid_tick_count": "Unzuel vun de Ticks däerf net negativ sinn", "argument.time.invalid_unit": "Ongülteg Eenheet", "argument.time.tick_count_too_low": "Unzuel vun den Ticks däerf net méi kleng wéi %s sinn, %s ass zu kleng", "argument.uuid.invalid": "Ongülteg UUID", "argument.waypoint.invalid": "Déi gewielte Entitéit ass kee <PERSON>rä<PERSON>.", "arguments.block.tag.unknown": "Onbekannte Blocktag '%s'", "arguments.function.tag.unknown": "Onbekannte Funktiounsalias '%s'", "arguments.function.unknown": "Onbekannt Funktioun '%s'", "arguments.item.component.expected": "Géigestandskomponent erwaart", "arguments.item.component.malformed": "Feelerhaft ‚%s‘- Komponent: ‚%s‘", "arguments.item.component.repeated": "Den Elementkomponent ‚%s‘ gouf widder<PERSON>, awer nëmmen ee Wäert ka spezifizéiert ginn", "arguments.item.component.unknown": "Onbekannt Element Komponent '%s'", "arguments.item.malformed": "Feelerhafte Géigestand: ‚%s‘", "arguments.item.overstacked": "Maximal Stapelgréisst vun %s ass %s", "arguments.item.predicate.malformed": "Falschformt '%s' Prädikat: '%s'", "arguments.item.predicate.unknown": "Onbekannte Géigestandsprädikat '%s'", "arguments.item.tag.unknown": "Onbekannte Géigestandtag '%s'", "arguments.nbtpath.node.invalid": "Ongültegt Element am NBT-Pad", "arguments.nbtpath.nothing_found": "Et goufe keng Elementer fonnt déi %s entspriechen", "arguments.nbtpath.too_deep": "Resultéierenden NBT ze déif verschachtelt", "arguments.nbtpath.too_large": "Resultéierenden NBT ze grouss", "arguments.objective.notFound": "Onbekannt Ziel '%s'", "arguments.objective.readonly": "D'Ziel %s kann net ausgelies ginn", "arguments.operation.div0": "<PERSON><PERSON>ull kann net gedeelt ginn", "arguments.operation.invalid": "Ongülteg <PERSON>", "arguments.swizzle.invalid": "Ongülteg Achsekombinatioun vu x, y an z", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Rüstung", "attribute.name.armor_toughness": "<PERSON><PERSON><PERSON>ungshä<PERSON>", "attribute.name.attack_damage": "Ugrëffsschued", "attribute.name.attack_knockback": "Ugrëffsréckschlag", "attribute.name.attack_speed": "Ugrëffsgeschwindegkeet", "attribute.name.block_break_speed": "Blockofbaugeschwindegkeet", "attribute.name.block_interaction_range": "Blockinteraktiounsreechwäit", "attribute.name.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.camera_distance": "Kameraentfernung", "attribute.name.entity_interaction_range": "Entitéiteninteraktiounsreechwäit", "attribute.name.explosion_knockback_resistance": "Explosouinsréckstoussresistenz", "attribute.name.fall_damage_multiplier": "Fallschued-Multiplikator", "attribute.name.flying_speed": "Fluchgeschwindegkeet", "attribute.name.follow_range": "Kreaturen-Verfolgungsdistanz", "attribute.name.generic.armor": "Rüstung", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON>ungshä<PERSON>", "attribute.name.generic.attack_damage": "Ugrëffsschued", "attribute.name.generic.attack_knockback": "Ugrëffréckschlag", "attribute.name.generic.attack_speed": "Ugrëffsgeschwindegkeet", "attribute.name.generic.block_interaction_range": "Blockinteraktiounsreechwäit", "attribute.name.generic.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "Entitéiteinteraktiounsreechwäit", "attribute.name.generic.explosion_knockback_resistance": "Explosouinsréckstoussresistenz", "attribute.name.generic.fall_damage_multiplier": "Fallschuedmultiplikator", "attribute.name.generic.flying_speed": "Fluchgeschwindegkeet", "attribute.name.generic.follow_range": "Kreaturen-Verfolgungsdistanz", "attribute.name.generic.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "Sprongkraaft", "attribute.name.generic.knockback_resistance": "Standfestegkeet", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON>", "attribute.name.generic.max_absorption": "Maximal Absorbtioun", "attribute.name.generic.max_health": "<PERSON><PERSON>", "attribute.name.generic.movement_efficiency": "Bewegungseffizienz", "attribute.name.generic.movement_speed": "Schnellegkeet", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "Schrëtthéicht", "attribute.name.generic.water_movement_efficiency": "Waasser-Beweegungseffizienz", "attribute.name.gravity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.horse.jump_strength": "Spronghéicht vum Päerd", "attribute.name.jump_strength": "Sprongkraaft", "attribute.name.knockback_resistance": "Standfestegkeet", "attribute.name.luck": "<PERSON><PERSON><PERSON>", "attribute.name.max_absorption": "Maximal Absorbtioun", "attribute.name.max_health": "<PERSON><PERSON>", "attribute.name.mining_efficiency": "Ofbaueffizienz", "attribute.name.movement_efficiency": "Bewegungseffizienz", "attribute.name.movement_speed": "Geschwindegkeet", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "Blockofbaugeschwindegkeet", "attribute.name.player.block_interaction_range": "Blockinteraktiounsreechwäit", "attribute.name.player.entity_interaction_range": "Entitéiteninteraktiounsreechwäit", "attribute.name.player.mining_efficiency": "Ofbaueffizienz", "attribute.name.player.sneaking_speed": "Schläichgeschwindegkeet", "attribute.name.player.submerged_mining_speed": "Ënnerwasser-Ofbaugeschwindegkeet", "attribute.name.player.sweeping_damage_ratio": "Sweeping <PERSON><PERSON><PERSON>", "attribute.name.safe_fall_distance": "<PERSON><PERSON><PERSON>", "attribute.name.scale": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "Schläichgeschwindegkeet", "attribute.name.spawn_reinforcements": "Zombie-Verstäerkung", "attribute.name.step_height": "Schrëtthéicht", "attribute.name.submerged_mining_speed": "Ënnerwasser-Ofbaugeschwindegkeet", "attribute.name.sweeping_damage_ratio": "Schwongschuedverhältnis", "attribute.name.tempt_range": "Kreaturen-Ulackeldistanz", "attribute.name.water_movement_efficiency": "Waasser-Beweegungseffizienz", "attribute.name.waypoint_receive_range": "Weepräis <PERSON>bereich", "attribute.name.waypoint_transmit_range": "<PERSON>epr<PERSON><PERSON>droung<PERSON>beräich", "attribute.name.zombie.spawn_reinforcements": "Zombie Verstäerkung", "biome.minecraft.badlands": "Badlands", "biome.minecraft.bamboo_jungle": "Bambusdschungel", "biome.minecraft.basalt_deltas": "Basaltdeltas", "biome.minecraft.beach": "Plage", "biome.minecraft.birch_forest": "Bierkeb<PERSON>sch", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON>", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dark_forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON><PERSON>", "biome.minecraft.deep_frozen_ocean": "<PERSON>erä<PERSON><PERSON>", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.desert": "<PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Drëpssteenhöl", "biome.minecraft.end_barrens": "End Barrens", "biome.minecraft.end_highlands": "<PERSON>", "biome.minecraft.end_midlands": "<PERSON>", "biome.minecraft.eroded_badlands": "Ofgedroe Badlands", "biome.minecraft.flower_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.forest": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "Veräist Biergko<PERSON>n", "biome.minecraft.frozen_river": "Veräiste Floss", "biome.minecraft.grove": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.ice_spikes": "Äiszapentundra", "biome.minecraft.jagged_peaks": "Zerklüft Biergkoppen", "biome.minecraft.jungle": "D<PERSON><PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.lush_caves": "Üppeg Hölen", "biome.minecraft.mangrove_swamp": "Man<PERSON><PERSON>wes<PERSON><PERSON>", "biome.minecraft.meadow": "<PERSON><PERSON>", "biome.minecraft.mushroom_fields": "Champignonland", "biome.minecraft.nether_wastes": "Nether Ödland", "biome.minecraft.ocean": "<PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Bierken-Urwald", "biome.minecraft.old_growth_pine_taiga": "Pinien-Urtaiga", "biome.minecraft.old_growth_spruce_taiga": "Fiichten-Urtaiga", "biome.minecraft.pale_garden": "Blatze Gaart", "biome.minecraft.plains": "Flaachland", "biome.minecraft.river": "Floss", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON>", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_beach": "Verschneite Plage", "biome.minecraft.snowy_plains": "Verschneiten Flaachland", "biome.minecraft.snowy_slopes": "Verschneit Hiwwelen", "biome.minecraft.snowy_taiga": "Verschneiten Taiga", "biome.minecraft.soul_sand_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.sparse_jungle": "Liichten Dschungel", "biome.minecraft.stony_peaks": "Stengeg Biergkoppen", "biome.minecraft.stony_shore": "<PERSON><PERSON><PERSON>", "biome.minecraft.sunflower_plains": "Sonneblummeflaachland", "biome.minecraft.swamp": "Sumpf", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.the_void": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.warm_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.warped_forest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.windswept_forest": "Duerchlëfte Bësch", "biome.minecraft.windswept_gravelly_hills": "Duerchlëft Kräsihiwwelen", "biome.minecraft.windswept_hills": "Duerchlëft Hiwwelen", "biome.minecraft.windswept_savanna": "Duerchlëft Savann", "biome.minecraft.wooded_badlands": "Badlands mat <PERSON>", "block.minecraft.acacia_button": "Akazienholzknäppchen", "block.minecraft.acacia_door": "Akazienholzdier", "block.minecraft.acacia_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_hanging_sign": "Akazienholzhängeschëld", "block.minecraft.acacia_leaves": "Akazieblieder", "block.minecraft.acacia_log": "Akaziëstamm", "block.minecraft.acacia_planks": "Akazienholzbrieder", "block.minecraft.acacia_pressure_plate": "Akazienholzdrockplack", "block.minecraft.acacia_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_sign": "Akazienholzschëld", "block.minecraft.acacia_slab": "Akazienholzplack", "block.minecraft.acacia_stairs": "Akazienholztrap", "block.minecraft.acacia_trapdoor": "Akazienholzklappdier", "block.minecraft.acacia_wall_hanging_sign": "Akazienhängeschëld", "block.minecraft.acacia_wall_sign": "Akazienholzmauerschëld", "block.minecraft.acacia_wood": "Akazienholz", "block.minecraft.activator_rail": "Aktivéierungsschinn", "block.minecraft.air": "Loft", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "Amethystblock", "block.minecraft.amethyst_cluster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ancient_debris": "<PERSON><PERSON>", "block.minecraft.andesite": "Andesit", "block.minecraft.andesite_slab": "Andesitplack", "block.minecraft.andesite_stairs": "Andesittrap", "block.minecraft.andesite_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "Attachéierte <PERSON>l", "block.minecraft.attached_pumpkin_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azalea": "<PERSON><PERSON><PERSON>", "block.minecraft.azalea_leaves": "Azaleëblieder", "block.minecraft.azure_bluet": "Porzeläinsstäerchen", "block.minecraft.bamboo": "Bambus", "block.minecraft.bamboo_block": "Bambusblock", "block.minecraft.bamboo_button": "Bambusknäppchen", "block.minecraft.bamboo_door": "Bambusdier", "block.minecraft.bamboo_fence": "Bambuszonk", "block.minecraft.bamboo_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_hanging_sign": "Bambushängeschëld", "block.minecraft.bamboo_mosaic": "Bambusmosaik", "block.minecraft.bamboo_mosaic_slab": "Bambusmosaikplack", "block.minecraft.bamboo_mosaic_stairs": "Bambusmosaiktrap", "block.minecraft.bamboo_planks": "Bamb<PERSON>brieder", "block.minecraft.bamboo_pressure_plate": "Bambusdrockplack", "block.minecraft.bamboo_sapling": "Bambusschoss", "block.minecraft.bamboo_sign": "Ba<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_slab": "Bambusplack", "block.minecraft.bamboo_stairs": "Bambustrap", "block.minecraft.bamboo_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_wall_hanging_sign": "Bambuswandhängeschëld", "block.minecraft.bamboo_wall_sign": "Bambuswandschël<PERSON>", "block.minecraft.banner.base.black": "Komplett schwaarzt Feld", "block.minecraft.banner.base.blue": "<PERSON><PERSON><PERSON> blot Feld", "block.minecraft.banner.base.brown": "Komplett brongt Feld", "block.minecraft.banner.base.cyan": "Komplett turquoiset Feld", "block.minecraft.banner.base.gray": "Komplett grot Feld", "block.minecraft.banner.base.green": "Komplett gréngt Feld", "block.minecraft.banner.base.light_blue": "Ko<PERSON>tt hellblot Feld", "block.minecraft.banner.base.light_gray": "Komplett hellgrot Feld", "block.minecraft.banner.base.lime": "Komplett hellgréngt Feld", "block.minecraft.banner.base.magenta": "Komplett magentat Feld", "block.minecraft.banner.base.orange": "Komplett oranget Feld", "block.minecraft.banner.base.pink": "Komplett rosat <PERSON>ld", "block.minecraft.banner.base.purple": "<PERSON><PERSON>tt mooft Feld", "block.minecraft.banner.base.red": "Komplett rout <PERSON><PERSON>", "block.minecraft.banner.base.white": "Komplett wäisst Feld", "block.minecraft.banner.base.yellow": "Komplett gielt Feld", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.blue": "Blo <PERSON>", "block.minecraft.banner.border.brown": "Brong Bordure", "block.minecraft.banner.border.cyan": "Turquoise Bordure", "block.minecraft.banner.border.gray": "Gro Bordure", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.light_blue": "Hellblo Bordure", "block.minecraft.banner.border.light_gray": "Hellgro Bordure", "block.minecraft.banner.border.lime": "Hellgréng Bordure", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "Orange Bordure", "block.minecraft.banner.border.pink": "<PERSON>", "block.minecraft.banner.border.purple": "<PERSON><PERSON>", "block.minecraft.banner.border.red": "Rout <PERSON>", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.border.yellow": "<PERSON><PERSON>", "block.minecraft.banner.bricks.black": "<PERSON><PERSON> schwa<PERSON>z gemauert", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON> blo gemauert", "block.minecraft.banner.bricks.brown": "<PERSON>ld brong gemauert", "block.minecraft.banner.bricks.cyan": "Feld turquoise gemauert", "block.minecraft.banner.bricks.gray": "<PERSON>ld gro gemauert", "block.minecraft.banner.bricks.green": "<PERSON><PERSON> g<PERSON> gemauert", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON> hell<PERSON>lo g<PERSON>t", "block.minecraft.banner.bricks.light_gray": "<PERSON>ld hell<PERSON> gemauert", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON> gemauert", "block.minecraft.banner.bricks.magenta": "Feld magenta gemauert", "block.minecraft.banner.bricks.orange": "Feld orange gemauert", "block.minecraft.banner.bricks.pink": "<PERSON>ld rosa gemauert", "block.minecraft.banner.bricks.purple": "<PERSON>ld mof gemauert", "block.minecraft.banner.bricks.red": "<PERSON>ld rout g<PERSON><PERSON>t", "block.minecraft.banner.bricks.white": "<PERSON><PERSON> w<PERSON> gemauert", "block.minecraft.banner.bricks.yellow": "<PERSON><PERSON> giel gemauert", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.blue": "<PERSON><PERSON>", "block.minecraft.banner.circle.brown": "Brong Kugel", "block.minecraft.banner.circle.cyan": "Turquoise <PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON>", "block.minecraft.banner.circle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.light_gray": "Hellgro Kugel", "block.minecraft.banner.circle.lime": "Hellgréng Kugel", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "Orange Kugel", "block.minecraft.banner.circle.pink": "<PERSON>", "block.minecraft.banner.circle.purple": "<PERSON><PERSON>", "block.minecraft.banner.circle.red": "<PERSON><PERSON>", "block.minecraft.banner.circle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON>", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.cyan": "Turquoise Creeper", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.lime": "Hellg<PERSON>nge <PERSON>", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.orange": "Orange Creeper", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON>", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON>", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.black": "Schwaarzt Andreaskräiz", "block.minecraft.banner.cross.blue": "Blot Andreaskräiz", "block.minecraft.banner.cross.brown": "Brongt Andreaskräiz", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.gray": "Grot Andreaskräiz", "block.minecraft.banner.cross.green": "Gréngt Andreaskräiz", "block.minecraft.banner.cross.light_blue": "Hellblot Andreaskrä<PERSON>", "block.minecraft.banner.cross.light_gray": "Hellgrot Andreaskräiz", "block.minecraft.banner.cross.lime": "Hellgréngt Andreaskräiz", "block.minecraft.banner.cross.magenta": "Magentat Andreaskräiz", "block.minecraft.banner.cross.orange": "Oranget <PERSON>rä<PERSON>", "block.minecraft.banner.cross.pink": "<PERSON><PERSON>", "block.minecraft.banner.cross.purple": "Moft Andreaskräiz", "block.minecraft.banner.cross.red": "<PERSON><PERSON>", "block.minecraft.banner.cross.white": "Wäisst Andreaskräiz", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON><PERSON><PERSON> gezackte Bordure", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.curly_border.brown": "Brong gezackte Bordure", "block.minecraft.banner.curly_border.cyan": "Turquoise gezackte Bordure", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON> gez<PERSON>", "block.minecraft.banner.curly_border.light_gray": "Hellgro gezackte Bordure", "block.minecraft.banner.curly_border.lime": "Hellgréng gezackte Bordure", "block.minecraft.banner.curly_border.magenta": "Magenta g<PERSON>", "block.minecraft.banner.curly_border.orange": "Orange gezackte Bordure", "block.minecraft.banner.curly_border.pink": "<PERSON> g<PERSON>", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON>", "block.minecraft.banner.curly_border.red": "Rout g<PERSON><PERSON><PERSON>", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON> g<PERSON> Bordure", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON><PERSON> queesch lénks gede<PERSON>t", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON> queesch lénks g<PERSON>t", "block.minecraft.banner.diagonal_left.brown": "Brong queesch lénks gede<PERSON>t", "block.minecraft.banner.diagonal_left.cyan": "Turquoise queesch lénks gede<PERSON>t", "block.minecraft.banner.diagonal_left.gray": "Gro queesch lénks g<PERSON>t", "block.minecraft.banner.diagonal_left.green": "G<PERSON>ng queesch lénks gede<PERSON>t", "block.minecraft.banner.diagonal_left.light_blue": "Hellblo queesch lénks gede<PERSON>t", "block.minecraft.banner.diagonal_left.light_gray": "Hellgro queesch lénks gedeelt", "block.minecraft.banner.diagonal_left.lime": "Hellgréng queesch lénks gede<PERSON>t", "block.minecraft.banner.diagonal_left.magenta": "Magenta queesch lénks g<PERSON>t", "block.minecraft.banner.diagonal_left.orange": "Orange queesch lénks gedeelt", "block.minecraft.banner.diagonal_left.pink": "<PERSON> que<PERSON>ch l<PERSON> g<PERSON>t", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON> que<PERSON>ch lénks g<PERSON>t", "block.minecraft.banner.diagonal_left.red": "Rout queesch lénks g<PERSON>t", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> queesch lénks gede<PERSON>t", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON> que<PERSON>ch lénks g<PERSON>t", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON><PERSON><PERSON> queesch riets gede<PERSON>t", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON> queesch riets gede<PERSON>t", "block.minecraft.banner.diagonal_right.brown": "Brong queesch riets gedeelt", "block.minecraft.banner.diagonal_right.cyan": "Turquoise queesch riets gedeelt", "block.minecraft.banner.diagonal_right.gray": "G<PERSON> queesch riets gede<PERSON>t", "block.minecraft.banner.diagonal_right.green": "<PERSON><PERSON><PERSON> queesch riets gede<PERSON>t", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON> queesch riets gede<PERSON>t", "block.minecraft.banner.diagonal_right.light_gray": "Hellgro queesch riets gedeelt", "block.minecraft.banner.diagonal_right.lime": "Hellgréng queesch riets gedeelt", "block.minecraft.banner.diagonal_right.magenta": "Magenta queesch riets gede<PERSON>t", "block.minecraft.banner.diagonal_right.orange": "Orange queesch riets gedeelt", "block.minecraft.banner.diagonal_right.pink": "<PERSON> queesch riets g<PERSON>t", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON> que<PERSON>ch riets gede<PERSON>t", "block.minecraft.banner.diagonal_right.red": "Rout queesch riets gede<PERSON>t", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON> queesch riets gedeelt", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON> que<PERSON>ch riets gede<PERSON>t", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON><PERSON><PERSON> que<PERSON>ch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON> queesch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.brown": "Brong queesch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.cyan": "Turquoise queesch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.gray": "Gro queesch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON><PERSON> queesch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.light_blue": "Hell<PERSON>lo queesch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.light_gray": "Hellgro queesch riets g<PERSON>t (Inverséiert)", "block.minecraft.banner.diagonal_up_left.lime": "Hellgréng queesch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta queesch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.orange": "Orange queesch riets gede<PERSON>t (Inverséiert)", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON> que<PERSON>ch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON> que<PERSON>ch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON> queesch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_left.white": "<PERSON><PERSON><PERSON> queesch riets g<PERSON>t (Inverséiert)", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON> que<PERSON>ch riets g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON><PERSON><PERSON> que<PERSON>ch lén<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON>lo queesch lénks <PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.brown": "Brong queesch lénks g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.cyan": "Turquoise queesch lén<PERSON> g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.gray": "Gro queesch lénks g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON>ng queesch lénks g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.light_blue": "Hellblo queesch lénks g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.light_gray": "Hellgro queesch lénks g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.lime": "Hellgréng queesch lénks g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.magenta": "Magenta queesch lénks g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.orange": "Orange queesch lénks g<PERSON>t (Inverséiert)", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON> que<PERSON> l<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON> queesch lén<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON> queesch lén<PERSON> g<PERSON> (Inverséiert)", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON>ch lén<PERSON> g<PERSON> (Inverséiert)", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.brown": "<PERSON>rong<PERSON>", "block.minecraft.banner.flow.cyan": "Turquoise Floss", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.light_blue": "Hell<PERSON><PERSON><PERSON> Floss", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON> Floss", "block.minecraft.banner.flow.lime": "Hellgrénge Floss", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.orange": "Orange Floss", "block.minecraft.banner.flow.pink": "<PERSON>", "block.minecraft.banner.flow.purple": "<PERSON><PERSON>", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.blue": "<PERSON><PERSON>", "block.minecraft.banner.flower.brown": "Brong Blumm", "block.minecraft.banner.flower.cyan": "Turquoise <PERSON>", "block.minecraft.banner.flower.gray": "<PERSON><PERSON>", "block.minecraft.banner.flower.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON>mm", "block.minecraft.banner.flower.light_gray": "Hellgro Blumm", "block.minecraft.banner.flower.lime": "Hell<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.orange": "Orange Blumm", "block.minecraft.banner.flower.pink": "<PERSON>", "block.minecraft.banner.flower.purple": "<PERSON><PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON>", "block.minecraft.banner.flower.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON>", "block.minecraft.banner.globe.black": "Schwaarze Globus", "block.minecraft.banner.globe.blue": "Bloe Globus", "block.minecraft.banner.globe.brown": "Bronge Globus", "block.minecraft.banner.globe.cyan": "Turquoise Globus", "block.minecraft.banner.globe.gray": "Groe Globus", "block.minecraft.banner.globe.green": "Grénge Globus", "block.minecraft.banner.globe.light_blue": "Hellbloe Globus", "block.minecraft.banner.globe.light_gray": "Hellgroe Globus", "block.minecraft.banner.globe.lime": "Hellgrénge Globus", "block.minecraft.banner.globe.magenta": "Magentae Globus", "block.minecraft.banner.globe.orange": "Orange Globus", "block.minecraft.banner.globe.pink": "Rosae Globus", "block.minecraft.banner.globe.purple": "Mofe Globus", "block.minecraft.banner.globe.red": "Roude Globus", "block.minecraft.banner.globe.white": "Wäisse Globus", "block.minecraft.banner.globe.yellow": "Giele Globus", "block.minecraft.banner.gradient.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "Bronge <PERSON>f<PERSON>", "block.minecraft.banner.gradient.cyan": "Turquoise <PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "Hellb<PERSON>e Fu<PERSON>f<PERSON>laf", "block.minecraft.banner.gradient.light_gray": "Hellgroe Fuerfverlaf", "block.minecraft.banner.gradient.lime": "Hellgrénge Fuerfverlaf", "block.minecraft.banner.gradient.magenta": "Magentae <PERSON>", "block.minecraft.banner.gradient.orange": "Orange Fuerfverlaf", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON>", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON>", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.white": "Wäisse Fuerfverlaf", "block.minecraft.banner.gradient.yellow": "Giele Fuerfverlaf", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON><PERSON><PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON><PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.brown": "Bronge Fu<PERSON>f<PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.cyan": "Turquoise <PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.green": "<PERSON><PERSON><PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.light_blue": "Hellbloe Fu<PERSON>f<PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.light_gray": "Hellgroe Fuerfverlaf (Inverséiert)", "block.minecraft.banner.gradient_up.lime": "Hellgrénge Fuerfverlaf (Inverséiert)", "block.minecraft.banner.gradient_up.magenta": "Magentae <PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.orange": "Orange Fuerfverlaf (Inverséiert)", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.red": "R<PERSON><PERSON> Fu<PERSON>f<PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON><PERSON> (Inverséiert)", "block.minecraft.banner.gradient_up.yellow": "Giele Fu<PERSON>f<PERSON>la<PERSON> (Inverséiert)", "block.minecraft.banner.guster.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON>rquo<PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "<PERSON>", "block.minecraft.banner.guster.pink": "<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.black": "<PERSON>ewe schwaarz g<PERSON>t", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON> blo gede<PERSON>t", "block.minecraft.banner.half_horizontal.brown": "Uewe brong gedeelt", "block.minecraft.banner.half_horizontal.cyan": "Uewen turquoise gedeelt", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON>e gro g<PERSON>t", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON> g<PERSON> g<PERSON>t", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON> hellblo gede<PERSON>t", "block.minecraft.banner.half_horizontal.light_gray": "<PERSON><PERSON>en hellgro gede<PERSON>t", "block.minecraft.banner.half_horizontal.lime": "<PERSON>ewen hell<PERSON> gede<PERSON>t", "block.minecraft.banner.half_horizontal.magenta": "Uewe magenta gedeelt", "block.minecraft.banner.half_horizontal.orange": "Uewen orange gedeelt", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON> rosa g<PERSON>t", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON><PERSON> mof gedeelt", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON> rout g<PERSON><PERSON>t", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> w<PERSON><PERSON> gede<PERSON>t", "block.minecraft.banner.half_horizontal.yellow": "<PERSON><PERSON><PERSON> giel g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON> sch<PERSON>z g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON> blo g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON> brong g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.cyan": "Ennen turquoise gedeelt", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON><PERSON> gro <PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON> g<PERSON> g<PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON><PERSON> hell<PERSON> g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.light_gray": "<PERSON>nen hell<PERSON> gede<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON>nen <PERSON> g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON>ne magenta gede<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.orange": "Ennen orange gedeelt", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON> rosa g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON>ne mof gede<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON> rout g<PERSON><PERSON>t", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON> w<PERSON> g<PERSON>t", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON> giel g<PERSON>", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON><PERSON> sch<PERSON>z g<PERSON>t", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON><PERSON> blo g<PERSON><PERSON><PERSON>t", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON> brong gede<PERSON>t", "block.minecraft.banner.half_vertical.cyan": "Riets turquoise gedeelt", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON>s gro g<PERSON>t", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON> g<PERSON> g<PERSON>", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON><PERSON> hell<PERSON> g<PERSON>t", "block.minecraft.banner.half_vertical.light_gray": "<PERSON><PERSON><PERSON> g<PERSON>t", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON><PERSON> g<PERSON>t", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON>s magenta gedeelt", "block.minecraft.banner.half_vertical.orange": "Riets orange gedeelt", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON> rosa g<PERSON>t", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON><PERSON> mof gede<PERSON>t", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON> rout g<PERSON><PERSON>t", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON> w<PERSON><PERSON> g<PERSON>t", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON><PERSON> giel g<PERSON>t", "block.minecraft.banner.half_vertical_right.black": "Lénks schwaarz g<PERSON>t", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON><PERSON> blo g<PERSON>t", "block.minecraft.banner.half_vertical_right.brown": "<PERSON>énks brong gedeelt", "block.minecraft.banner.half_vertical_right.cyan": "Lénks turquoise gedeelt", "block.minecraft.banner.half_vertical_right.gray": "Lénks gro <PERSON>", "block.minecraft.banner.half_vertical_right.green": "Lénks g<PERSON> g<PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON>én<PERSON>", "block.minecraft.banner.half_vertical_right.light_gray": "Lénks hellgro <PERSON>t", "block.minecraft.banner.half_vertical_right.lime": "Lénks hellgréng g<PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "Lénks magenta gedeelt", "block.minecraft.banner.half_vertical_right.orange": "Lénks orange gedeelt", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON> rosa g<PERSON>", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON><PERSON> mof gede<PERSON>t", "block.minecraft.banner.half_vertical_right.red": "Lénks rout g<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON> w<PERSON> g<PERSON>t", "block.minecraft.banner.half_vertical_right.yellow": "Lénks giel g<PERSON>", "block.minecraft.banner.mojang.black": "Schwaarzt Mojang Logo", "block.minecraft.banner.mojang.blue": "Blot Mojang Logo", "block.minecraft.banner.mojang.brown": "Brongt Mojang Logo", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Mojang Logo", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON>", "block.minecraft.banner.mojang.green": "Gréngt Mojang Logo", "block.minecraft.banner.mojang.light_blue": "Hellblot Mojang Logo", "block.minecraft.banner.mojang.light_gray": "Hellgrot Mojang Logo", "block.minecraft.banner.mojang.lime": "Hellgréngt Mojang Logo", "block.minecraft.banner.mojang.magenta": "Magentat Mojang Logo", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON>", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON>", "block.minecraft.banner.mojang.purple": "Moft Mojang Logo", "block.minecraft.banner.mojang.red": "Rout <PERSON>", "block.minecraft.banner.mojang.white": "Wäisst Mojang Logo", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON>", "block.minecraft.banner.piglin.cyan": "Turquoise <PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON>", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.light_gray": "Hellgro Schnëss", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.orange": "Orange Schnëss", "block.minecraft.banner.piglin.pink": "<PERSON>", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON>", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "Turquoise <PERSON>ut", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_gray": "Hellgro Raut", "block.minecraft.banner.rhombus.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.orange": "Orange Raut", "block.minecraft.banner.rhombus.pink": "<PERSON>", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.red": "R<PERSON>", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON>", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "Gréngen Doudekapp", "block.minecraft.banner.skull.light_blue": "Hellb<PERSON><PERSON>", "block.minecraft.banner.skull.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.lime": "Hellgréngen Doudekapp", "block.minecraft.banner.skull.magenta": "Magentaen Doudekapp", "block.minecraft.banner.skull.orange": "<PERSON><PERSON>", "block.minecraft.banner.skull.pink": "<PERSON><PERSON>", "block.minecraft.banner.skull.purple": "Mofen Doudekapp", "block.minecraft.banner.skull.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.white": "Wäissen Doudekapp", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON> brong <PERSON>", "block.minecraft.banner.small_stripes.cyan": "V<PERSON>ier turquoise Péil", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON>ier magenta Péil", "block.minecraft.banner.small_stripes.orange": "Véier orange Péil", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON> mof <PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON> rout <PERSON>", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON> w<PERSON>", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.black": "<PERSON><PERSON><PERSON>zen Eck ënne riets", "block.minecraft.banner.square_bottom_left.blue": "Bloen Eck ënne riets", "block.minecraft.banner.square_bottom_left.brown": "Brongen Eck ënne riets", "block.minecraft.banner.square_bottom_left.cyan": "Turquoisen Eck ënne riets", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON> r<PERSON>s", "block.minecraft.banner.square_bottom_left.green": "Gréngen Eck ënne riets", "block.minecraft.banner.square_bottom_left.light_blue": "Hellbloen Eck ënne riets", "block.minecraft.banner.square_bottom_left.light_gray": "<PERSON><PERSON><PERSON> E<PERSON> riets", "block.minecraft.banner.square_bottom_left.lime": "Hellgréngen Eck ënne riets", "block.minecraft.banner.square_bottom_left.magenta": "Magentaen Eck ënne riets", "block.minecraft.banner.square_bottom_left.orange": "Orangen Eck ënne riets", "block.minecraft.banner.square_bottom_left.pink": "<PERSON><PERSON> riets", "block.minecraft.banner.square_bottom_left.purple": "Mofen Eck ënne riets", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON><PERSON> riets", "block.minecraft.banner.square_bottom_left.white": "Wäissen Eck ënne riets", "block.minecraft.banner.square_bottom_left.yellow": "Gielen Eck ënne riets", "block.minecraft.banner.square_bottom_right.black": "Sc<PERSON><PERSON>zen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.blue": "Bloen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.brown": "Brongen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.cyan": "Turquoisen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.green": "Gréngen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.light_blue": "Hellbloen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.light_gray": "Hellgroen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.lime": "Hellgréngen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.magenta": "Magentaen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.orange": "Orangen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.pink": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.purple": "Mofen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON><PERSON> E<PERSON> ë<PERSON> lén<PERSON>", "block.minecraft.banner.square_bottom_right.white": "Wäissen Eck ënne lénks", "block.minecraft.banner.square_bottom_right.yellow": "Gielen Eck ënne lénks", "block.minecraft.banner.square_top_left.black": "<PERSON><PERSON><PERSON><PERSON> uewe riets", "block.minecraft.banner.square_top_left.blue": "<PERSON><PERSON><PERSON> Eck uewe riets", "block.minecraft.banner.square_top_left.brown": "<PERSON><PERSON><PERSON> uewe riets", "block.minecraft.banner.square_top_left.cyan": "<PERSON>r<PERSON><PERSON>ise<PERSON>ck uewe riets", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON><PERSON> u<PERSON>e riets", "block.minecraft.banner.square_top_left.green": "<PERSON><PERSON><PERSON><PERSON> E<PERSON> uewe riets", "block.minecraft.banner.square_top_left.light_blue": "Hellb<PERSON><PERSON> Eck uewe riets", "block.minecraft.banner.square_top_left.light_gray": "<PERSON><PERSON><PERSON>ck uewe riets", "block.minecraft.banner.square_top_left.lime": "Hellgréngen Eck uewe riets", "block.minecraft.banner.square_top_left.magenta": "Magent<PERSON> E<PERSON> uewe riets", "block.minecraft.banner.square_top_left.orange": "<PERSON><PERSON> uewe riets", "block.minecraft.banner.square_top_left.pink": "<PERSON><PERSON> u<PERSON>e riets", "block.minecraft.banner.square_top_left.purple": "Mo<PERSON> Eck uewe riets", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON><PERSON> uewe riets", "block.minecraft.banner.square_top_left.white": "Wäissen Eck uewe riets", "block.minecraft.banner.square_top_left.yellow": "<PERSON>iel<PERSON> Eck uewe riets", "block.minecraft.banner.square_top_right.black": "<PERSON><PERSON><PERSON><PERSON> Eck uewe lénks", "block.minecraft.banner.square_top_right.blue": "B<PERSON><PERSON> Eck uewe lénks", "block.minecraft.banner.square_top_right.brown": "<PERSON><PERSON><PERSON> u<PERSON>e l<PERSON>", "block.minecraft.banner.square_top_right.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>e l<PERSON>", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON> u<PERSON> l<PERSON>", "block.minecraft.banner.square_top_right.green": "<PERSON><PERSON>nge<PERSON> E<PERSON> u<PERSON>e lén<PERSON>", "block.minecraft.banner.square_top_right.light_blue": "Hellbloen E<PERSON> uewe lén<PERSON>", "block.minecraft.banner.square_top_right.light_gray": "<PERSON><PERSON><PERSON> u<PERSON>e l<PERSON>", "block.minecraft.banner.square_top_right.lime": "Hellgréngen E<PERSON> u<PERSON>e l<PERSON>", "block.minecraft.banner.square_top_right.magenta": "Magent<PERSON> E<PERSON> u<PERSON>e l<PERSON>", "block.minecraft.banner.square_top_right.orange": "<PERSON><PERSON> u<PERSON>e l<PERSON>", "block.minecraft.banner.square_top_right.pink": "<PERSON><PERSON> u<PERSON> l<PERSON>", "block.minecraft.banner.square_top_right.purple": "Mo<PERSON> Eck uewe lénks", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON><PERSON> u<PERSON>e l<PERSON>", "block.minecraft.banner.square_top_right.white": "Wäissen Eck uewe lénks", "block.minecraft.banner.square_top_right.yellow": "Gielen Eck uewe lénks", "block.minecraft.banner.straight_cross.black": "Schwaarzt Kräiz", "block.minecraft.banner.straight_cross.blue": "Blot Kräiz", "block.minecraft.banner.straight_cross.brown": "Brongt Kräiz", "block.minecraft.banner.straight_cross.cyan": "Turquoiset Kräiz", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON>r<PERSON>", "block.minecraft.banner.straight_cross.green": "Gréngt Kräiz", "block.minecraft.banner.straight_cross.light_blue": "Hellblot Kräiz", "block.minecraft.banner.straight_cross.light_gray": "Hellgrot Kräiz", "block.minecraft.banner.straight_cross.lime": "Hellgréngt Kräiz", "block.minecraft.banner.straight_cross.magenta": "Magentat Kräiz", "block.minecraft.banner.straight_cross.orange": "Oranget Kräiz", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.purple": "Moft Kräiz", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.white": "Wäisst Kräiz", "block.minecraft.banner.straight_cross.yellow": "Gielt Kräiz", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON><PERSON> Fändelfouss", "block.minecraft.banner.stripe_bottom.cyan": "Turquoise <PERSON>lf<PERSON>s", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON><PERSON> Fändelfouss", "block.minecraft.banner.stripe_bottom.light_blue": "Hell<PERSON><PERSON><PERSON> F<PERSON>lf<PERSON>s", "block.minecraft.banner.stripe_bottom.light_gray": "Hellgroe Fändelfouss", "block.minecraft.banner.stripe_bottom.lime": "Hellgrénge Fändelfouss", "block.minecraft.banner.stripe_bottom.magenta": "Magentae Fä<PERSON>lfouss", "block.minecraft.banner.stripe_bottom.orange": "Orangë Fändelfouss", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON><PERSON>lf<PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON> F<PERSON>lf<PERSON>", "block.minecraft.banner.stripe_bottom.yellow": "Giele Fändelfouss", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.cyan": "Turquoise Poul", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.light_gray": "Hellgroe Poul", "block.minecraft.banner.stripe_center.lime": "Hellgrénge Poul", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.orange": "Orange Poul", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.yellow": "Giele <PERSON>", "block.minecraft.banner.stripe_downleft.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON> Schr<PERSON>i <PERSON>", "block.minecraft.banner.stripe_downleft.brown": "Brong Schréi l<PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "Turquoise Schr<PERSON>i l<PERSON>", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON> Schr<PERSON>i <PERSON>", "block.minecraft.banner.stripe_downleft.green": "<PERSON><PERSON><PERSON>hréi <PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "<PERSON><PERSON><PERSON> Schréi <PERSON>", "block.minecraft.banner.stripe_downleft.light_gray": "Hellgro Schréi lénks", "block.minecraft.banner.stripe_downleft.lime": "Hellgréng Schréi l<PERSON>", "block.minecraft.banner.stripe_downleft.magenta": "Ma<PERSON><PERSON> Schréi <PERSON>", "block.minecraft.banner.stripe_downleft.orange": "Orange Schréi lénks", "block.minecraft.banner.stripe_downleft.pink": "<PERSON>", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.cyan": "Turquoise <PERSON>", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.light_gray": "Hellgro Schréi", "block.minecraft.banner.stripe_downright.lime": "Hell<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.orange": "Orange Schréi", "block.minecraft.banner.stripe_downright.pink": "<PERSON>", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.black": "Schwaarz Flank riets", "block.minecraft.banner.stripe_left.blue": "Blo Flank riets", "block.minecraft.banner.stripe_left.brown": "Brong Flank riets", "block.minecraft.banner.stripe_left.cyan": "Turquoise Flank riets", "block.minecraft.banner.stripe_left.gray": "Gro Flank riets", "block.minecraft.banner.stripe_left.green": "Gréng Flank riets", "block.minecraft.banner.stripe_left.light_blue": "Hellblo Flank riets", "block.minecraft.banner.stripe_left.light_gray": "Hellgro Flank riets", "block.minecraft.banner.stripe_left.lime": "Hellgréng Flank riets", "block.minecraft.banner.stripe_left.magenta": "Magenta Flank riets", "block.minecraft.banner.stripe_left.orange": "Orange Flank riets", "block.minecraft.banner.stripe_left.pink": "<PERSON> riets", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON> Flank riets", "block.minecraft.banner.stripe_left.red": "Rout <PERSON> riets", "block.minecraft.banner.stripe_left.white": "<PERSON><PERSON>iss Flank riets", "block.minecraft.banner.stripe_left.yellow": "Giel Flank riets", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.cyan": "Turquoise <PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.orange": "Orange Balken", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_right.black": "Schwaarz Flank lénks", "block.minecraft.banner.stripe_right.blue": "Blo Flank lénks", "block.minecraft.banner.stripe_right.brown": "Brong Flank lénks", "block.minecraft.banner.stripe_right.cyan": "Turquoise Flank lénks", "block.minecraft.banner.stripe_right.gray": "Gro Flank lén<PERSON>", "block.minecraft.banner.stripe_right.green": "Gréng Flank lénks", "block.minecraft.banner.stripe_right.light_blue": "Hellblo Flank lénks", "block.minecraft.banner.stripe_right.light_gray": "Hellgro Flank lénks", "block.minecraft.banner.stripe_right.lime": "Hellgréng Flank lénks", "block.minecraft.banner.stripe_right.magenta": "Magenta Flank lénks", "block.minecraft.banner.stripe_right.orange": "Orange Flank lénks", "block.minecraft.banner.stripe_right.pink": "<PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.red": "<PERSON>out <PERSON>", "block.minecraft.banner.stripe_right.white": "<PERSON><PERSON><PERSON> Flank l<PERSON>", "block.minecraft.banner.stripe_right.yellow": "<PERSON>iel Flank l<PERSON>", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON><PERSON><PERSON> Fändelkapp", "block.minecraft.banner.stripe_top.blue": "Bloe Fändelkapp", "block.minecraft.banner.stripe_top.brown": "Bronge Fändelkapp", "block.minecraft.banner.stripe_top.cyan": "Turquoise Fändelkapp", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "Grénge Fändelkapp", "block.minecraft.banner.stripe_top.light_blue": "Hellbloe Fändelkapp", "block.minecraft.banner.stripe_top.light_gray": "Hellgroe Fändelkapp", "block.minecraft.banner.stripe_top.lime": "Hellgrénge Fändelkapp", "block.minecraft.banner.stripe_top.magenta": "Magentae Fändelkapp", "block.minecraft.banner.stripe_top.orange": "Orangë Fändelkapp", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "Mofe Fändelkapp", "block.minecraft.banner.stripe_top.red": "Roude Fändelkapp", "block.minecraft.banner.stripe_top.white": "Wäisse Fändelkapp", "block.minecraft.banner.stripe_top.yellow": "Giele Fändelkapp", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.brown": "Brong <PERSON>ëtz", "block.minecraft.banner.triangle_bottom.cyan": "Turquoise <PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.light_gray": "Hellgro Hallefspëtz", "block.minecraft.banner.triangle_bottom.lime": "Hellg<PERSON>ng <PERSON>tz", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.orange": "Orange Hallefspëtz", "block.minecraft.banner.triangle_bottom.pink": "<PERSON>", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON><PERSON><PERSON> gedréinten <PERSON>", "block.minecraft.banner.triangle_top.blue": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.brown": "Brong gedréinten <PERSON>", "block.minecraft.banner.triangle_top.cyan": "Turquoise ged<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON><PERSON> gedré<PERSON>", "block.minecraft.banner.triangle_top.light_blue": "<PERSON><PERSON><PERSON> ged<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.light_gray": "Hellgro gedréinten Halle<PERSON>ëtz", "block.minecraft.banner.triangle_top.lime": "Hellgréng gedréinten <PERSON>tz", "block.minecraft.banner.triangle_top.magenta": "Magenta ged<PERSON>", "block.minecraft.banner.triangle_top.orange": "Orange gedréinten Hallefspëtz", "block.minecraft.banner.triangle_top.pink": "<PERSON>", "block.minecraft.banner.triangle_top.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON> gedré<PERSON>en <PERSON>", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.blue": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.brown": "Brong Zacken ë<PERSON>n", "block.minecraft.banner.triangles_bottom.cyan": "Turquoise <PERSON>", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.light_blue": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.light_gray": "Hellgro Zacken ë<PERSON>n", "block.minecraft.banner.triangles_bottom.lime": "Hellgréng Zack<PERSON>", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.orange": "<PERSON>", "block.minecraft.banner.triangles_bottom.pink": "<PERSON>", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.white": "<PERSON><PERSON><PERSON> Zacken ë<PERSON>n", "block.minecraft.banner.triangles_bottom.yellow": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>en", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON> <PERSON><PERSON> uewen", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON> <PERSON>en uewen", "block.minecraft.banner.triangles_top.cyan": "Turquoise <PERSON> u<PERSON>en", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON> <PERSON><PERSON> u<PERSON>en", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON> u<PERSON>en", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON><PERSON> <PERSON><PERSON> uewen", "block.minecraft.banner.triangles_top.light_gray": "Hellgro Zacken uewen", "block.minecraft.banner.triangles_top.lime": "Hellg<PERSON>ng Zack<PERSON> uewen", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON> u<PERSON>en", "block.minecraft.banner.triangles_top.orange": "<PERSON>en uewen", "block.minecraft.banner.triangles_top.pink": "<PERSON> u<PERSON>", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> u<PERSON>en", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON> <PERSON><PERSON> uewen", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON> Zacken uewen", "block.minecraft.banner.triangles_top.yellow": "<PERSON><PERSON> u<PERSON>en", "block.minecraft.barrel": "Faass", "block.minecraft.barrier": "Barrière", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "Primärkraaft", "block.minecraft.beacon.secondary": "Sekundärkraaft", "block.minecraft.bed.no_sleep": "Du kanns nëmmen an der Nuecht oder wärend engem Donnerwieder schlofen", "block.minecraft.bed.not_safe": "Du kanns elo net schlofen, et si Monster an déi Ëmgéigend", "block.minecraft.bed.obstructed": "<PERSON><PERSON><PERSON>", "block.minecraft.bed.occupied": "<PERSON><PERSON><PERSON><PERSON> ass besat", "block.minecraft.bed.too_far_away": "Du kanns elo net schlofen, d'Bett ass ze weit fort", "block.minecraft.bedrock": "Grondsteen", "block.minecraft.bee_nest": "Beiennascht", "block.minecraft.beehive": "<PERSON><PERSON><PERSON>", "block.minecraft.beetroots": "<PERSON><PERSON>", "block.minecraft.bell": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.birch_button": "Bierkenholzknäppchen", "block.minecraft.birch_door": "Bierkenholzdier", "block.minecraft.birch_fence": "Bierkenholzzonk", "block.minecraft.birch_fence_gate": "Bierkenhol<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_hanging_sign": "Bierkenholzhängeschëld", "block.minecraft.birch_leaves": "Bierkeblieder", "block.minecraft.birch_log": "Bierkestamm", "block.minecraft.birch_planks": "Bierkenholzbrieder", "block.minecraft.birch_pressure_plate": "Bierkenholzdrockplack", "block.minecraft.birch_sapling": "Bierkesetzlek", "block.minecraft.birch_sign": "Bierkenholzschëld", "block.minecraft.birch_slab": "Bierkenholzplack", "block.minecraft.birch_stairs": "Bierkenholztrap", "block.minecraft.birch_trapdoor": "Bierkenholzklappdier", "block.minecraft.birch_wall_hanging_sign": "Bierkenholzmauerhängeschëld", "block.minecraft.birch_wall_sign": "Bierkenholzmauerschëld", "block.minecraft.birch_wood": "Bierkenholz", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON>ze Fändel", "block.minecraft.black_bed": "Schwaarzt Bett", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON>z <PERSON>", "block.minecraft.black_candle_cake": "<PERSON>ch mat sch<PERSON><PERSON><PERSON>", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> Zement", "block.minecraft.black_glazed_terracotta": "Glaséiert schwaarz Keramik", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "block.minecraft.black_stained_glass": "Schwaarzt Glas", "block.minecraft.black_stained_glass_pane": "Schwaarz Glasscheif", "block.minecraft.black_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone": "Schwaarzsteen", "block.minecraft.blackstone_slab": "Sc<PERSON><PERSON>zsteeplack", "block.minecraft.blackstone_stairs": "Schwaarzsteentrap", "block.minecraft.blackstone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blast_furnace": "Schmelzuewen", "block.minecraft.blue_banner": "Bloe Fändel", "block.minecraft.blue_bed": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON>", "block.minecraft.blue_candle_cake": "<PERSON>ch mat bloer <PERSON>", "block.minecraft.blue_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "Bloen Zement", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> blo <PERSON>", "block.minecraft.blue_ice": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blue_orchid": "Blo Orchidee", "block.minecraft.blue_shulker_box": "Blo <PERSON>lker-Këscht", "block.minecraft.blue_stained_glass": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "Blo Glasscheif", "block.minecraft.blue_terracotta": "<PERSON><PERSON>", "block.minecraft.blue_wool": "Blo Woll", "block.minecraft.bone_block": "Schankeblock", "block.minecraft.bookshelf": "Bicherregal", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_block": "Hirkoralleblock", "block.minecraft.brain_coral_fan": "Hirk<PERSON><PERSON>f<PERSON><PERSON>", "block.minecraft.brain_coral_wall_fan": "Hirkoral<PERSON>auerf<PERSON><PERSON>", "block.minecraft.brewing_stand": "Braustand", "block.minecraft.brick_slab": "Zilleplack", "block.minecraft.brick_stairs": "Zillentrap", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bricks": "Zillen", "block.minecraft.brown_banner": "Bronge Fändel", "block.minecraft.brown_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_candle": "Brong <PERSON>ä<PERSON>z", "block.minecraft.brown_candle_cake": "<PERSON>ch mat bronger <PERSON>", "block.minecraft.brown_carpet": "Brong<PERSON> Te<PERSON>", "block.minecraft.brown_concrete": "Bronge <PERSON>", "block.minecraft.brown_concrete_powder": "Brong<PERSON> Z<PERSON>", "block.minecraft.brown_glazed_terracotta": "Glas<PERSON>iert brong Keramik", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_shulker_box": "Brong Shulker-Këscht", "block.minecraft.brown_stained_glass": "Brongt Glas", "block.minecraft.brown_stained_glass_pane": "Brong Glasscheif", "block.minecraft.brown_terracotta": "Brong Keramik", "block.minecraft.brown_wool": "Brong Woll", "block.minecraft.bubble_column": "Blosesail", "block.minecraft.bubble_coral": "Blosekorall", "block.minecraft.bubble_coral_block": "Blosekoralleblock", "block.minecraft.bubble_coral_fan": "Blosekorallefächer", "block.minecraft.bubble_coral_wall_fan": "Blosekorallemauerfächer", "block.minecraft.budding_amethyst": "Amethystknospeblock", "block.minecraft.bush": "<PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cake": "<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "Kali<PERSON><PERSON><PERSON><PERSON>-Sensor", "block.minecraft.campfire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "<PERSON><PERSON> <PERSON>", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "Geschnëtzte Kürbis", "block.minecraft.cauldron": "Kessel", "block.minecraft.cave_air": "Höleloft", "block.minecraft.cave_vines": "Höleranken", "block.minecraft.cave_vines_plant": "Höhlerankeplanz", "block.minecraft.chain": "<PERSON><PERSON>", "block.minecraft.chain_command_block": "<PERSON><PERSON>", "block.minecraft.cherry_button": "Kiischtenholzknäppchen", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON>holzdier", "block.minecraft.cherry_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_hanging_sign": "Kiischtenholzhängeschëld", "block.minecraft.cherry_leaves": "Kiischteblieder", "block.minecraft.cherry_log": "Kiischtenholzstamm", "block.minecraft.cherry_planks": "Kiischtenholzbrieder", "block.minecraft.cherry_pressure_plate": "Kiischtenholzdrockplack", "block.minecraft.cherry_sapling": "Kiischtesetzlek", "block.minecraft.cherry_sign": "Ki<PERSON>tenholzschël<PERSON>", "block.minecraft.cherry_slab": "Kiischtenholzplack", "block.minecraft.cherry_stairs": "Kiischtenholztrap", "block.minecraft.cherry_trapdoor": "Kiischtenholzklappdier", "block.minecraft.cherry_wall_hanging_sign": "Kiischtenholzmauerhängeschëld", "block.minecraft.cherry_wall_sign": "Kischtenholzmauerschëld", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chest": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chipped_anvil": "Ugeschloenen <PERSON>", "block.minecraft.chiseled_bookshelf": "<PERSON><PERSON><PERSON>", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_deepslate": "Gemeesselte Déiweschifer", "block.minecraft.chiseled_nether_bricks": "Gemeesselt Netherzillen", "block.minecraft.chiseled_polished_blackstone": "Gemeesselte poléierte Schwarzsteen", "block.minecraft.chiseled_quartz_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_red_sandstone": "G<PERSON><PERSON>sel<PERSON> roude <PERSON>", "block.minecraft.chiseled_resin_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_stone_bricks": "G<PERSON>eesselt Steenzillen", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_tuff_bricks": "G<PERSON>eesselt Trasszillen", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "Chorusplanz", "block.minecraft.clay": "<PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "<PERSON><PERSON><PERSON>", "block.minecraft.coal_block": "<PERSON><PERSON><PERSON>", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "<PERSON><PERSON>", "block.minecraft.cobbled_deepslate": "Brochdéiweschifer", "block.minecraft.cobbled_deepslate_slab": "Brochdéiweschiferplack", "block.minecraft.cobbled_deepslate_stairs": "Brochdéiweschifertrap", "block.minecraft.cobbled_deepslate_wall": "Brochdéiweschifermauer", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "Brochsteeplack", "block.minecraft.cobblestone_stairs": "Brochsteentrap", "block.minecraft.cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobweb": "<PERSON>we<PERSON>", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Befeelsblock", "block.minecraft.comparator": "Redstone-Komparator", "block.minecraft.composter": "Ko<PERSON>ster", "block.minecraft.conduit": "Conduit", "block.minecraft.copper_block": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_bulb": "<PERSON><PERSON>bie<PERSON>", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "Kofferklappdier", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Briques d'ardoise profonde fissurées", "block.minecraft.cracked_deepslate_tiles": "Rësseg Déiweschiferplättercher", "block.minecraft.cracked_nether_bricks": "Rësseg Netherzillen", "block.minecraft.cracked_polished_blackstone_bricks": "Rësseg poléiert Schwarzsteenzillen", "block.minecraft.cracked_stone_bricks": "Rësseg Steenzillen", "block.minecraft.crafter": "<PERSON><PERSON><PERSON>", "block.minecraft.crafting_table": "Wierkbänk", "block.minecraft.creaking_heart": "Knarzend Häerz", "block.minecraft.creeper_head": "Creeperkapp", "block.minecraft.creeper_wall_head": "Creepermauerkapp", "block.minecraft.crimson_button": "Karmesinknäppchen", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "Karmesinzonk", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fungus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "Karmesinhängeschëld", "block.minecraft.crimson_hyphae": "Karmesinhyphen", "block.minecraft.crimson_nylium": "Karmesin-Nylium", "block.minecraft.crimson_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "Karmesindrockplack", "block.minecraft.crimson_roots": "Karmesinwuerzelen", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON><PERSON>p<PERSON><PERSON>", "block.minecraft.crimson_stairs": "Karmesintrap", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_hanging_sign": "Ka<PERSON><PERSON>nmauerhängeschëld", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crying_obsidian": "Kräischenden Obsidian", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_copper_stairs": "Geschni<PERSON><PERSON>", "block.minecraft.cut_red_sandstone": "Geschniddene roude <PERSON>", "block.minecraft.cut_red_sandstone_slab": "G<PERSON><PERSON><PERSON>dde rout <PERSON>", "block.minecraft.cut_sandstone": "Geschniddene <PERSON>", "block.minecraft.cut_sandstone_slab": "Ges<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_banner": "Turquoise Fändel", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle": "Turquoise <PERSON>", "block.minecraft.cyan_candle_cake": "<PERSON>ch mat turquoiser Kä<PERSON>z", "block.minecraft.cyan_carpet": "<PERSON>rq<PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete": "Turquoise Bëtong", "block.minecraft.cyan_concrete_powder": "Turquoisen Zement", "block.minecraft.cyan_glazed_terracotta": "Glaséiert turquoise Keramik", "block.minecraft.cyan_shulker_box": "Turquoise <PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "Turquoise <PERSON>", "block.minecraft.cyan_terracotta": "Turquoise <PERSON>", "block.minecraft.cyan_wool": "Turquoise Woll", "block.minecraft.damaged_anvil": "Beschiedegten Am<PERSON>s", "block.minecraft.dandelion": "Bettseechesch", "block.minecraft.dark_oak_button": "Schwaarzeechenholzknäppchen", "block.minecraft.dark_oak_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>dier", "block.minecraft.dark_oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_hanging_sign": "Sc<PERSON>arzeechehängeschëld", "block.minecraft.dark_oak_leaves": "Schwaarzeecheblieder", "block.minecraft.dark_oak_log": "Schwaarzeechestamm", "block.minecraft.dark_oak_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON>holzbrieder", "block.minecraft.dark_oak_pressure_plate": "Schwaarzeechenholzdrockplack", "block.minecraft.dark_oak_sapling": "Sc<PERSON><PERSON>zeechesetzlek", "block.minecraft.dark_oak_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>holzschël<PERSON>", "block.minecraft.dark_oak_slab": "Sc<PERSON><PERSON><PERSON>chenholzplack", "block.minecraft.dark_oak_stairs": "Sc<PERSON><PERSON>zeechenholztrap", "block.minecraft.dark_oak_trapdoor": "Sc<PERSON><PERSON><PERSON>chenholzklappdier", "block.minecraft.dark_oak_wall_hanging_sign": "Sc<PERSON>arzeechewandhängeschëld", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>holzmauerschëld", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON>", "block.minecraft.dark_prismarine_stairs": "<PERSON><PERSON>", "block.minecraft.daylight_detector": "Liichtsensor", "block.minecraft.dead_brain_coral": "Ofgestuerwe Hirkorall", "block.minecraft.dead_brain_coral_block": "Ofgestuerwene Hirk<PERSON>leblock", "block.minecraft.dead_brain_coral_fan": "Ofgestuerwene Hirkorallefächer", "block.minecraft.dead_brain_coral_wall_fan": "Ofgestuerwene Hirkorallemauerfächer", "block.minecraft.dead_bubble_coral": "Ofgestuerwe Blosekorall", "block.minecraft.dead_bubble_coral_block": "Ofgestuerwene Blosekoralleblock", "block.minecraft.dead_bubble_coral_fan": "Ofgestuerwene Blosekorallefächer", "block.minecraft.dead_bubble_coral_wall_fan": "Ofgestuerwene Blosekorallemauerfächer", "block.minecraft.dead_bush": "Ofgestuerwen<PERSON>", "block.minecraft.dead_fire_coral": "Ofgestuerwe Féierkorall", "block.minecraft.dead_fire_coral_block": "Ofgestuerwene <PERSON>", "block.minecraft.dead_fire_coral_fan": "Ofgestuerwene F<PERSON>korallefächer", "block.minecraft.dead_fire_coral_wall_fan": "Ofgestuerwene Féierkorallemauerfächer", "block.minecraft.dead_horn_coral": "Ofgestuerwe Geweikorall", "block.minecraft.dead_horn_coral_block": "Ofgestuerwene Geweikoralleblock", "block.minecraft.dead_horn_coral_fan": "Ofgestuerwene Geweikorallefächer", "block.minecraft.dead_horn_coral_wall_fan": "Ofgestuerwene Geweikorallemauerfächer", "block.minecraft.dead_tube_coral": "Ofgestuerwe Uergelkorall", "block.minecraft.dead_tube_coral_block": "Ofgestuerwene Uergelkoralleblock", "block.minecraft.dead_tube_coral_fan": "Ofgestuerwene Uergelkorallefächer", "block.minecraft.dead_tube_coral_wall_fan": "Ofgestuerwene Uergelkorallemauerfächer", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate": "Déiweschifer", "block.minecraft.deepslate_brick_slab": "Déiweschiferzilleplack", "block.minecraft.deepslate_brick_stairs": "Déiweschiferzilletrap", "block.minecraft.deepslate_brick_wall": "Déiweschiferzillewand", "block.minecraft.deepslate_bricks": "Déiweschiferzillen", "block.minecraft.deepslate_coal_ore": "Déiweschifer-Steekuel", "block.minecraft.deepslate_copper_ore": "Déiweschifer-Kofferäerz", "block.minecraft.deepslate_diamond_ore": "Déiweschifer-Diamantäerz", "block.minecraft.deepslate_emerald_ore": "Déiweschifer-Smaragdäerz", "block.minecraft.deepslate_gold_ore": "Déiweschifer-Goldäerz", "block.minecraft.deepslate_iron_ore": "Déiweschifer-Eisenäerz", "block.minecraft.deepslate_lapis_ore": "Déiweschifer-Lapislazuliäerz", "block.minecraft.deepslate_redstone_ore": "Déiweschifer-Redstone-Äerz", "block.minecraft.deepslate_tile_slab": "Déiweschiferplättercherplack", "block.minecraft.deepslate_tile_stairs": "Déiweschiferplättercheretrap", "block.minecraft.deepslate_tile_wall": "Déiweschiferplättercherwand", "block.minecraft.deepslate_tiles": "Déiweschiferplättercher", "block.minecraft.detector_rail": "Sensorschinn", "block.minecraft.diamond_block": "Diamantblock", "block.minecraft.diamond_ore": "Diamantä<PERSON>z", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Dioritplack", "block.minecraft.diorite_stairs": "Diorittrap", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_wall_head": "Draa<PERSON>mauerka<PERSON>", "block.minecraft.dried_ghast": "Verdréchen<PERSON>", "block.minecraft.dried_kelp_block": "Gedréchenten Algeblock", "block.minecraft.dripstone_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dropper": "<PERSON>pender", "block.minecraft.emerald_block": "Smaragdblock", "block.minecraft.emerald_ore": "Smaragdäerz", "block.minecraft.enchanting_table": "Zauberd<PERSON>sch", "block.minecraft.end_gateway": "Endtransitportal", "block.minecraft.end_portal": "Endportal", "block.minecraft.end_portal_frame": "Endportalkader", "block.minecraft.end_rod": "Endstaf", "block.minecraft.end_stone": "Endsteen", "block.minecraft.end_stone_brick_slab": "Endsteenzilleplack", "block.minecraft.end_stone_brick_stairs": "Endsteenzillentrap", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone_bricks": "Endsteenzillen", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Ausgesate gemeesselte Kofferblock", "block.minecraft.exposed_copper": "Ausgesate Kofferblock", "block.minecraft.exposed_copper_bulb": "Ausgesat Kofferbier", "block.minecraft.exposed_copper_door": "Ausgesat Kofferdier", "block.minecraft.exposed_copper_grate": "Ausgesate Koffergitter", "block.minecraft.exposed_copper_trapdoor": "Ausgesat Kofferklappdier", "block.minecraft.exposed_cut_copper": "Ausgesate geschnidden<PERSON>", "block.minecraft.exposed_cut_copper_slab": "Ausgesat geschnidde Kofferplack", "block.minecraft.exposed_cut_copper_stairs": "G<PERSON>ues<PERSON> ges<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.farmland": "<PERSON><PERSON>", "block.minecraft.fern": "Far", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_block": "Féierkoralleblock", "block.minecraft.fire_coral_fan": "Féierkorallefächer", "block.minecraft.fire_coral_wall_fan": "Féierkorallemauerfächer", "block.minecraft.firefly_bush": "Gehaansfénkelchebusch", "block.minecraft.fletching_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flower_pot": "Blummendëppen", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea_leaves": "Bléiend Azaleëblieder", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "<PERSON><PERSON><PERSON><PERSON> Eis", "block.minecraft.furnace": "<PERSON><PERSON><PERSON>", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glass": "Glas", "block.minecraft.glass_pane": "Glasscheif", "block.minecraft.glow_lichen": "Liichtflechten", "block.minecraft.glowstone": "Liichtsteen", "block.minecraft.gold_block": "Goldblock", "block.minecraft.gold_ore": "Goldäerz", "block.minecraft.granite": "Granit", "block.minecraft.granite_slab": "Granitplack", "block.minecraft.granite_stairs": "Granittrap", "block.minecraft.granite_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.grass": "Gras", "block.minecraft.grass_block": "Gras", "block.minecraft.gravel": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_bed": "<PERSON><PERSON>", "block.minecraft.gray_candle": "<PERSON><PERSON>", "block.minecraft.gray_candle_cake": "<PERSON>ch mat g<PERSON>er <PERSON>", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "Glaséiert gro <PERSON>ramik", "block.minecraft.gray_shulker_box": "Gro Shulker-Këscht", "block.minecraft.gray_stained_glass": "<PERSON><PERSON>", "block.minecraft.gray_stained_glass_pane": "Gro Glasscheif", "block.minecraft.gray_terracotta": "<PERSON><PERSON>", "block.minecraft.gray_wool": "Gro Woll", "block.minecraft.green_banner": "Grénge Fändel", "block.minecraft.green_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.green_candle_cake": "<PERSON><PERSON> mat g<PERSON><PERSON>", "block.minecraft.green_carpet": "Gréngen Teppech", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.green_concrete_powder": "Gréngen Zement", "block.minecraft.green_glazed_terracotta": "Glas<PERSON><PERSON>t g<PERSON>", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON>-K<PERSON>scht", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass_pane": "Gréng Glasscheif", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON> W<PERSON>", "block.minecraft.grindstone": "Schläifsteen", "block.minecraft.hanging_roots": "Hängend Wuerzelen", "block.minecraft.hay_block": "<PERSON><PERSON>", "block.minecraft.heavy_core": "Schwéiere Kär", "block.minecraft.heavy_weighted_pressure_plate": "Schwéier gewiichtet Drockplack", "block.minecraft.honey_block": "<PERSON><PERSON>g<PERSON>", "block.minecraft.honeycomb_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.hopper": "Triichter", "block.minecraft.horn_coral": "Geweikorall", "block.minecraft.horn_coral_block": "Geweikoralleblock", "block.minecraft.horn_coral_fan": "Geweikorallefächer", "block.minecraft.horn_coral_wall_fan": "Geweikorallemauerfächer", "block.minecraft.ice": "<PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> Steenzillen", "block.minecraft.infested_cobblestone": "<PERSON><PERSON><PERSON>", "block.minecraft.infested_cracked_stone_bricks": "<PERSON><PERSON><PERSON> rësseg Steenzillen", "block.minecraft.infested_deepslate": "Befalenen <PERSON>", "block.minecraft.infested_mossy_stone_bricks": "Be<PERSON><PERSON> bemoosst Steenzillen", "block.minecraft.infested_stone": "<PERSON><PERSON><PERSON>", "block.minecraft.infested_stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "Eisenäerz", "block.minecraft.iron_trapdoor": "Eiseklappdier", "block.minecraft.jack_o_lantern": "<PERSON><PERSON><PERSON><PERSON>lanter", "block.minecraft.jigsaw": "Puzzleblock", "block.minecraft.jukebox": "<PERSON><PERSON>ckespiller", "block.minecraft.jungle_button": "Tropenholzknäppchen", "block.minecraft.jungle_door": "Tropenholzdier", "block.minecraft.jungle_fence": "Tropenholzzonk", "block.minecraft.jungle_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_hanging_sign": "Tropenholzhängeschëld", "block.minecraft.jungle_leaves": "Tropeblieder", "block.minecraft.jungle_log": "Tropenholzstamm", "block.minecraft.jungle_planks": "Tropenholzbrieder", "block.minecraft.jungle_pressure_plate": "Tropenholzdrockplack", "block.minecraft.jungle_sapling": "Tropesetzlek", "block.minecraft.jungle_sign": "Tropenholzschëld", "block.minecraft.jungle_slab": "Tropenholzplack", "block.minecraft.jungle_stairs": "Tropenholztrap", "block.minecraft.jungle_trapdoor": "Tropenholzklappdier", "block.minecraft.jungle_wall_hanging_sign": "Tropeholzmauerhängeschëld", "block.minecraft.jungle_wall_sign": "Tropenholzmauerschëld", "block.minecraft.jungle_wood": "T<PERSON>en<PERSON><PERSON>", "block.minecraft.kelp": "Alg", "block.minecraft.kelp_plant": "Algestill", "block.minecraft.ladder": "<PERSON><PERSON>", "block.minecraft.lantern": "<PERSON><PERSON>", "block.minecraft.lapis_block": "Lapislazuliblock", "block.minecraft.lapis_ore": "Lapislazuliäerz", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON><PERSON>", "block.minecraft.large_fern": "<PERSON><PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "Lavakessel", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "Liespult", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_banner": "Hellbloe Fändel", "block.minecraft.light_blue_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_candle_cake": "<PERSON><PERSON> mat <PERSON><PERSON><PERSON>", "block.minecraft.light_blue_carpet": "Hellb<PERSON><PERSON>", "block.minecraft.light_blue_concrete": "Hellbloe B<PERSON>", "block.minecraft.light_blue_concrete_powder": "Hellbloen Zement", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>lo <PERSON>", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON>-Këscht", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_stained_glass_pane": "Hellblo Glasscheif", "block.minecraft.light_blue_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_wool": "Hellblo Woll", "block.minecraft.light_gray_banner": "Hellgroe Fändel", "block.minecraft.light_gray_bed": "Hellgrot Bett", "block.minecraft.light_gray_candle": "Hellgro Käerz", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON> mat <PERSON>", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_concrete": "Hellgroe Bëtong", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.light_gray_glazed_terracotta": "Glas<PERSON>iert hellgro Keramik", "block.minecraft.light_gray_shulker_box": "Hellgro Shulker-Këscht", "block.minecraft.light_gray_stained_glass": "Hellgrot Glas", "block.minecraft.light_gray_stained_glass_pane": "Hellgro Glasscheif", "block.minecraft.light_gray_terracotta": "Hellgro Keramik", "block.minecraft.light_gray_wool": "Hellgro Woll", "block.minecraft.light_weighted_pressure_plate": "Liicht gewiichtet Drockplack", "block.minecraft.lightning_rod": "Blëtzofleeder", "block.minecraft.lilac": "Neelchesblumm", "block.minecraft.lily_of_the_valley": "Meeréischen", "block.minecraft.lily_pad": "Séirous", "block.minecraft.lime_banner": "Hellgrénge Fändel", "block.minecraft.lime_bed": "Hellgré<PERSON><PERSON>", "block.minecraft.lime_candle": "Hellgréng Käerz", "block.minecraft.lime_candle_cake": "<PERSON><PERSON> mat <PERSON>", "block.minecraft.lime_carpet": "Hellgréngen Teppech", "block.minecraft.lime_concrete": "Hellgrénge Bëtong", "block.minecraft.lime_concrete_powder": "Hellgréngen Zement", "block.minecraft.lime_glazed_terracotta": "Glaséiert hellgréng Keramik", "block.minecraft.lime_shulker_box": "Hellgréng Shulker-Këscht", "block.minecraft.lime_stained_glass": "Hellgréngt Glas", "block.minecraft.lime_stained_glass_pane": "Hellgréng Glasscheif", "block.minecraft.lime_terracotta": "Hellgréng Keramik", "block.minecraft.lime_wool": "Hellgréng Woll", "block.minecraft.lodestone": "Leetsteen", "block.minecraft.loom": "Wiefst<PERSON>", "block.minecraft.magenta_banner": "Magentae Fändel", "block.minecraft.magenta_bed": "Magentat Bett", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle_cake": "<PERSON>ch mat mofer <PERSON>", "block.minecraft.magenta_carpet": "Ma<PERSON><PERSON>", "block.minecraft.magenta_concrete": "Magentae <PERSON>", "block.minecraft.magenta_concrete_powder": "Magentaen Zement", "block.minecraft.magenta_glazed_terracotta": "Glaséiert magenta Keramik", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass_pane": "Ma<PERSON><PERSON>", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_wool": "Ma<PERSON><PERSON>", "block.minecraft.magma_block": "Magmablock", "block.minecraft.mangrove_button": "Mangrouwenholzknäppchen", "block.minecraft.mangrove_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_hanging_sign": "Mangrouwenholzhängeschëld", "block.minecraft.mangrove_leaves": "Mangrouweblieder", "block.minecraft.mangrove_log": "Mangrouwestamm", "block.minecraft.mangrove_planks": "Man<PERSON>uwenholzplack", "block.minecraft.mangrove_pressure_plate": "Mangrouwendrockplack", "block.minecraft.mangrove_propagule": "Mangrouwen-Ke<PERSON>ling", "block.minecraft.mangrove_roots": "Mangrouwewuerzelen", "block.minecraft.mangrove_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_slab": "Man<PERSON>uwenholzplack", "block.minecraft.mangrove_stairs": "Mangrouwenholztrap", "block.minecraft.mangrove_trapdoor": "Mangrouwenholzklappdier", "block.minecraft.mangrove_wall_hanging_sign": "Mangrouwewandhängeschëld", "block.minecraft.mangrove_wall_sign": "Man<PERSON>uwenholzwandschëld", "block.minecraft.mangrove_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.medium_amethyst_bud": "Medium Amethystknosp", "block.minecraft.melon": "<PERSON><PERSON><PERSON>", "block.minecraft.melon_stem": "Melouneplanz", "block.minecraft.moss_block": "Moosblock", "block.minecraft.moss_carpet": "Moosteppech", "block.minecraft.mossy_cobblestone": "Bemoosste Brochsteen", "block.minecraft.mossy_cobblestone_slab": "Bemooste Brochsteeplack", "block.minecraft.mossy_cobblestone_stairs": "Bemooste Brochsteentrap", "block.minecraft.mossy_cobblestone_wall": "Bemoosste Brochsteemauer", "block.minecraft.mossy_stone_brick_slab": "Bemooste Steenzilleplack", "block.minecraft.mossy_stone_brick_stairs": "Bemoost <PERSON>eenzillentrap", "block.minecraft.mossy_stone_brick_wall": "Bemoost<PERSON>", "block.minecraft.mossy_stone_bricks": "Bemoosst Steenzillen", "block.minecraft.moving_piston": "Block a Beweegung", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "Bullizilleplack", "block.minecraft.mud_brick_stairs": "Bullizilletrap", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_bricks": "Bulliszillen", "block.minecraft.muddy_mangrove_roots": "Schmor<PERSON>g <PERSON>wuerzelen", "block.minecraft.mushroom_stem": "Champignongsstill", "block.minecraft.mycelium": "Pilzgeflecht", "block.minecraft.nether_brick_fence": "Netherzillenzonk", "block.minecraft.nether_brick_slab": "Netherzilleplack", "block.minecraft.nether_brick_stairs": "Netherzillentrap", "block.minecraft.nether_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_bricks": "Netherzillen", "block.minecraft.nether_gold_ore": "Nethergoldäerz", "block.minecraft.nether_portal": "Netherportal", "block.minecraft.nether_quartz_ore": "Netherquarzäerz", "block.minecraft.nether_sprouts": "Nethersprossen", "block.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_wart_block": "Netherwaarzelblock", "block.minecraft.netherite_block": "Netheritblock", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Nouteblock", "block.minecraft.oak_button": "Eechenholzknäppchen", "block.minecraft.oak_door": "Eechenholzdier", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_hanging_sign": "Eechenholzhängeschëld", "block.minecraft.oak_leaves": "Eecheblieder", "block.minecraft.oak_log": "Eechestamm", "block.minecraft.oak_planks": "Eechenholzbrieder", "block.minecraft.oak_pressure_plate": "Eechenholzdrockplack", "block.minecraft.oak_sapling": "Eechesetzlek", "block.minecraft.oak_sign": "Eechenholzsch<PERSON><PERSON>", "block.minecraft.oak_slab": "Eechenholzplack", "block.minecraft.oak_stairs": "Eechenholztrap", "block.minecraft.oak_trapdoor": "Eechenholzklappdier", "block.minecraft.oak_wall_hanging_sign": "Eechenholzwandhängeschëld", "block.minecraft.oak_wall_sign": "Eechenholz<PERSON>uerschël<PERSON>", "block.minecraft.oak_wood": "Eech<PERSON><PERSON><PERSON>", "block.minecraft.observer": "Observateur", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "<PERSON><PERSON>", "block.minecraft.ominous_banner": "O<PERSON><PERSON><PERSON>", "block.minecraft.open_eyeblossom": "<PERSON><PERSON>", "block.minecraft.orange_banner": "Orange Fändel", "block.minecraft.orange_bed": "<PERSON><PERSON>", "block.minecraft.orange_candle": "Orange Käerz", "block.minecraft.orange_candle_cake": "<PERSON><PERSON> mat <PERSON>", "block.minecraft.orange_carpet": "<PERSON><PERSON>", "block.minecraft.orange_concrete": "Orangë Bëtong", "block.minecraft.orange_concrete_powder": "<PERSON><PERSON>", "block.minecraft.orange_glazed_terracotta": "Glaséiert orange Keramik", "block.minecraft.orange_shulker_box": "Orange Shulker-Këscht", "block.minecraft.orange_stained_glass": "Orange Glas", "block.minecraft.orange_stained_glass_pane": "Orange Glasscheif", "block.minecraft.orange_terracotta": "Orange Keramik", "block.minecraft.orange_tulip": "Orange Tulp", "block.minecraft.orange_wool": "Orange Woll", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Oxidéierte gene<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper": "Oxid<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_bulb": "Oxid<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_grate": "Oxidé<PERSON><PERSON>", "block.minecraft.oxidized_copper_trapdoor": "Oxid<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_cut_copper": "Oxidéierte geschni<PERSON><PERSON>", "block.minecraft.oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oxidized_cut_copper_stairs": "Oxidéiert geschnidde Koffertrap", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.packed_mud": "Feste Bulli", "block.minecraft.pale_hanging_moss": "Hängende Blatzmoos", "block.minecraft.pale_moss_block": "Blatzmoosblock", "block.minecraft.pale_moss_carpet": "Blatzmoosteppech", "block.minecraft.pale_oak_button": "Blatzeechenholzknäppchen", "block.minecraft.pale_oak_door": "B<PERSON><PERSON>chenholzdier", "block.minecraft.pale_oak_fence": "Blatzeechenholzzonk", "block.minecraft.pale_oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_hanging_sign": "Blatzeechenholzhängeschëld", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_log": "Blatzeechestamm", "block.minecraft.pale_oak_planks": "Blatzeechenholzbrieder", "block.minecraft.pale_oak_pressure_plate": "Blatzeechenholzdrockplack", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_sign": "B<PERSON>eechenholzschël<PERSON>", "block.minecraft.pale_oak_slab": "Blatzeechenholzplack", "block.minecraft.pale_oak_stairs": "Blatzeechenholztrap", "block.minecraft.pale_oak_trapdoor": "Blatzeechenholzklappdier", "block.minecraft.pale_oak_wall_hanging_sign": "Blatzeechenholzwandhängeschëld", "block.minecraft.pale_oak_wall_sign": "Blatzeechenholzwandschëld", "block.minecraft.pale_oak_wood": "B<PERSON><PERSON>chenholz", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.peony": "Päisch<PERSON>us", "block.minecraft.petrified_oak_slab": "Verstengert Eechenholzplack", "block.minecraft.piglin_head": "Piglinkapp", "block.minecraft.piglin_wall_head": "<PERSON>lin<PERSON><PERSON><PERSON>", "block.minecraft.pink_banner": "<PERSON><PERSON>", "block.minecraft.pink_bed": "<PERSON><PERSON>", "block.minecraft.pink_candle": "<PERSON>", "block.minecraft.pink_candle_cake": "<PERSON>ch mat rosa <PERSON>", "block.minecraft.pink_carpet": "<PERSON><PERSON>", "block.minecraft.pink_concrete": "<PERSON><PERSON>", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON>", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON>iert rosa <PERSON>", "block.minecraft.pink_petals": "<PERSON>", "block.minecraft.pink_shulker_box": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass_pane": "<PERSON>", "block.minecraft.pink_terracotta": "<PERSON>", "block.minecraft.pink_tulip": "<PERSON>", "block.minecraft.pink_wool": "<PERSON>", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "<PERSON><PERSON> vum Piston", "block.minecraft.pitcher_crop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head": "Spillerkapp", "block.minecraft.player_head.named": "Dem %s s<PERSON><PERSON>", "block.minecraft.player_wall_head": "Spillermauerkapp", "block.minecraft.podzol": "Podsol", "block.minecraft.pointed_dripstone": "Spëtzen Drëpssteen", "block.minecraft.polished_andesite": "Poléierten Andesit", "block.minecraft.polished_andesite_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_andesite_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_basalt": "Poléierte Basalt", "block.minecraft.polished_blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "<PERSON><PERSON><PERSON><PERSON>enzilleplack", "block.minecraft.polished_blackstone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON>steenzillentrap", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_button": "Poléierte <PERSON>arzsteeknäppchen", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON><PERSON><PERSON><PERSON>endrockplack", "block.minecraft.polished_blackstone_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate": "Poléierten Déiweschifer", "block.minecraft.polished_deepslate_slab": "Poléierten Déiweschiferplack", "block.minecraft.polished_deepslate_stairs": "Poléierten Déiweschifertrap", "block.minecraft.polished_deepslate_wall": "Poléierten D<PERSON>iweschifermauer", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "<PERSON><PERSON><PERSON><PERSON>oritplack", "block.minecraft.polished_diorite_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_granite": "Poléierte Granit", "block.minecraft.polished_granite_slab": "<PERSON><PERSON><PERSON><PERSON>la<PERSON>", "block.minecraft.polished_granite_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_tuff_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.poppy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Gromperen", "block.minecraft.potted_acacia_sapling": "Akaziesetzlek an engem Dëppen", "block.minecraft.potted_allium": "Allium an engem Dëppen", "block.minecraft.potted_azalea_bush": "Azalee am Dëppen", "block.minecraft.potted_azure_bluet": "Porzeläinsstäerchen an engem Dëppen", "block.minecraft.potted_bamboo": "Bambus an engem Dëppen", "block.minecraft.potted_birch_sapling": "Bierkesetzlek an engem Dëppen", "block.minecraft.potted_blue_orchid": "Blo Orchidee an engem Dëppen", "block.minecraft.potted_brown_mushroom": "<PERSON><PERSON><PERSON> an engem D<PERSON>", "block.minecraft.potted_cactus": "Kaktus an engem Dëppen", "block.minecraft.potted_cherry_sapling": "Kiischtesetzleng am Dëppen", "block.minecraft.potted_closed_eyeblossom": "Agedëppent zouen Ablumm", "block.minecraft.potted_cornflower": "Karblumm an engem Dëppen", "block.minecraft.potted_crimson_fungus": "Agedëppte Karmesin <PERSON>", "block.minecraft.potted_crimson_roots": "Agedëppte Karmesinwuerzelen", "block.minecraft.potted_dandelion": "Bettseechesch an engem Dëppen", "block.minecraft.potted_dark_oak_sapling": "Schwaarzeechesetzlek an engem Dëppen", "block.minecraft.potted_dead_bush": "Ofgestuerwene Strauch an engem Dëppen", "block.minecraft.potted_fern": "Far an engem Dëppen", "block.minecraft.potted_flowering_azalea_bush": "Bléiend Azalee am Dëppen", "block.minecraft.potted_jungle_sapling": "Tropesetzlek an engem Dëppen", "block.minecraft.potted_lily_of_the_valley": "Meeréischen an engem Dëppen", "block.minecraft.potted_mangrove_propagule": "Mangrouwen-Keimling am Dëppen", "block.minecraft.potted_oak_sapling": "Eechesetzlek an engem Dëppen", "block.minecraft.potted_open_eyeblossom": "Agedëppent oppen Ablumm", "block.minecraft.potted_orange_tulip": "Orange Tulp an engem Dëppen", "block.minecraft.potted_oxeye_daisy": "<PERSON>g<PERSON><PERSON><PERSON> an engem Dëppen", "block.minecraft.potted_pale_oak_sapling": "Agedë<PERSON><PERSON>zlé<PERSON>", "block.minecraft.potted_pink_tulip": "<PERSON> an engem D<PERSON>", "block.minecraft.potted_poppy": "Feierblumm an engem Dëppen", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON><PERSON> an engem Dëppen", "block.minecraft.potted_red_tulip": "<PERSON><PERSON> an engem Dëppen", "block.minecraft.potted_spruce_sapling": "Fiichtesetzlek an engem Dëppen", "block.minecraft.potted_torchflower": "Karblumm am Dëppen", "block.minecraft.potted_warped_fungus": "Verduerwene Champignon am Dëppen", "block.minecraft.potted_warped_roots": "Verduerwe Wuerzelen am Dëppen", "block.minecraft.potted_white_tulip": "<PERSON><PERSON><PERSON> Tu<PERSON>p an engem Dëppen", "block.minecraft.potted_wither_rose": "Witherrous an engem Dëppen", "block.minecraft.powder_snow": "Polverschnéi", "block.minecraft.powder_snow_cauldron": "Polverschnéikessel", "block.minecraft.powered_rail": "Beschleunegungsschinn", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prismarinzilleplack", "block.minecraft.prismarine_brick_stairs": "Prismarinzillentrap", "block.minecraft.prismarine_bricks": "Prismarinzillen", "block.minecraft.prismarine_slab": "Prismarinplack", "block.minecraft.prismarine_stairs": "Prismarintrap", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_banner": "Mofe Fändel", "block.minecraft.purple_bed": "Mooft Bett", "block.minecraft.purple_candle": "<PERSON><PERSON>", "block.minecraft.purple_candle_cake": "<PERSON>ch mat mofer <PERSON>", "block.minecraft.purple_carpet": "Mofen Teppech", "block.minecraft.purple_concrete": "<PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "Mofen Zement", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> mof Keramik", "block.minecraft.purple_shulker_box": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass": "Moft Glas", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.purple_terracotta": "<PERSON><PERSON>", "block.minecraft.purple_wool": "<PERSON><PERSON>", "block.minecraft.purpur_block": "Purpurblock", "block.minecraft.purpur_pillar": "Purpursail", "block.minecraft.purpur_slab": "Purpurträpplek", "block.minecraft.purpur_stairs": "Purpurtrap", "block.minecraft.quartz_block": "Quarzblock", "block.minecraft.quartz_bricks": "Quarz<PERSON>llen", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "Quarzplack", "block.minecraft.quartz_stairs": "Quarztrap", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.raw_gold_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.raw_iron_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_banner": "Roude Fändel", "block.minecraft.red_bed": "<PERSON><PERSON>", "block.minecraft.red_candle": "<PERSON><PERSON>", "block.minecraft.red_candle_cake": "<PERSON><PERSON> mat r<PERSON>er <PERSON>", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete": "R<PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "Glas<PERSON>iert rout Keramik", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom_block": "<PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_slab": "<PERSON><PERSON>", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON>", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON>", "block.minecraft.red_nether_bricks": "<PERSON><PERSON>", "block.minecraft.red_sand": "Roud<PERSON> Sand", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON>", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.red_sandstone_wall": "<PERSON><PERSON>", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON>", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.red_terracotta": "<PERSON><PERSON>", "block.minecraft.red_tulip": "R<PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON>", "block.minecraft.redstone_block": "Redstone-Block", "block.minecraft.redstone_lamp": "Redstone-Luucht", "block.minecraft.redstone_ore": "Redstone-Äerz", "block.minecraft.redstone_torch": "Redstone-Fakel", "block.minecraft.redstone_wall_torch": "Redstone-Mauerfakel", "block.minecraft.redstone_wire": "Redstone-<PERSON><PERSON>", "block.minecraft.reinforced_deepslate": "Verstärkten Déiweschifer", "block.minecraft.repeater": "Redstone-Verstäerker", "block.minecraft.repeating_command_block": "Widderhuelungs Befehlsblock", "block.minecraft.resin_block": "Harzblock", "block.minecraft.resin_brick_slab": "<PERSON><PERSON><PERSON>lle<PERSON>lack", "block.minecraft.resin_brick_stairs": "Harzzilletrap", "block.minecraft.resin_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.resin_clump": "Harzklompen", "block.minecraft.respawn_anchor": "<PERSON><PERSON><PERSON>nanker", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sand": "Sand", "block.minecraft.sandstone": "Sandsteen", "block.minecraft.sandstone_slab": "Sandsteeplack", "block.minecraft.sandstone_stairs": "Sandsteentrap", "block.minecraft.sandstone_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Sculk-Katalysator", "block.minecraft.sculk_sensor": "Sculk-Sensor", "block.minecraft.sculk_shrieker": "Sculk-Kräischer", "block.minecraft.sculk_vein": "Sculk-Veen", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sea_pickle": "Miereskornischong", "block.minecraft.seagrass": "Séigras", "block.minecraft.set_spawn": "Respawnpunkt gesat", "block.minecraft.short_dry_grass": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.short_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.shroomlight": "Champignonluucht", "block.minecraft.shulker_box": "Shulker-Këscht", "block.minecraft.skeleton_skull": "Skelettkapp", "block.minecraft.skeleton_wall_skull": "Mauerskelettkapp", "block.minecraft.slime_block": "Schläimblock", "block.minecraft.small_amethyst_bud": "<PERSON>leng <PERSON>k<PERSON>p", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON>", "block.minecraft.smithing_table": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Glate Basalt", "block.minecraft.smooth_quartz": "<PERSON><PERSON>", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON>", "block.minecraft.smooth_quartz_stairs": "<PERSON><PERSON>", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON> roude <PERSON>", "block.minecraft.smooth_red_sandstone_slab": "<PERSON><PERSON> rout <PERSON>", "block.minecraft.smooth_red_sandstone_stairs": "<PERSON><PERSON> rout <PERSON>", "block.minecraft.smooth_sandstone": "<PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON>", "block.minecraft.smooth_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.smooth_stone": "<PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "<PERSON><PERSON>", "block.minecraft.sniffer_egg": "Schnoffeler-Ee", "block.minecraft.snow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_sand": "Séilesand", "block.minecraft.soul_soil": "Séilebuedem", "block.minecraft.soul_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spawn.not_valid": "Du hues kee Bett oder opgeluedene Séilenanker, oder en gouf <PERSON>t", "block.minecraft.spawner": "Spawner", "block.minecraft.spawner.desc1": "Benotz Spawn-Ee:", "block.minecraft.spawner.desc2": "Leet K<PERSON>turetyp fest", "block.minecraft.sponge": "<PERSON><PERSON><PERSON>", "block.minecraft.spore_blossom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_button": "Fiichtenholzknäppchen", "block.minecraft.spruce_door": "<PERSON><PERSON>tenholzdier", "block.minecraft.spruce_fence": "<PERSON><PERSON>tenhol<PERSON>nk", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_hanging_sign": "Fiichteholzhängeschëld", "block.minecraft.spruce_leaves": "Fiichtennolen", "block.minecraft.spruce_log": "Fiichtestamm", "block.minecraft.spruce_planks": "Fiichtenholzbrieder", "block.minecraft.spruce_pressure_plate": "Fiichtenholzdrockplack", "block.minecraft.spruce_sapling": "Fiichtesetzlek", "block.minecraft.spruce_sign": "<PERSON>ichtenholzschëld", "block.minecraft.spruce_slab": "Fiichtenholzplack", "block.minecraft.spruce_stairs": "Fiichtenholztrap", "block.minecraft.spruce_trapdoor": "Fiichtenholzklappdier", "block.minecraft.spruce_wall_hanging_sign": "<PERSON>ichtemauerhängeschëld", "block.minecraft.spruce_wall_sign": "<PERSON>ichtenholzmauerschëld", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "<PERSON><PERSON><PERSON>", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Steenzilleplack", "block.minecraft.stone_brick_stairs": "Steenzillentrap", "block.minecraft.stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_bricks": "Steenzillen", "block.minecraft.stone_button": "Steeknäppchen", "block.minecraft.stone_pressure_plate": "Steendrockplack", "block.minecraft.stone_slab": "Steeplack", "block.minecraft.stone_stairs": "Steentrap", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Akaziëstamm ouni <PERSON>", "block.minecraft.stripped_acacia_wood": "Akazienholz ou<PERSON>", "block.minecraft.stripped_bamboo_block": "Geschielte Bambusblock", "block.minecraft.stripped_birch_log": "Bierkestamm ou<PERSON>", "block.minecraft.stripped_birch_wood": "Bierkenholz ou<PERSON>", "block.minecraft.stripped_cherry_log": "Kiischtestamm ou<PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou<PERSON>", "block.minecraft.stripped_crimson_hyphae": "Geschielte Karmesinhyphen", "block.minecraft.stripped_crimson_stem": "Ges<PERSON><PERSON><PERSON>", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON><PERSON>chestam<PERSON> ou<PERSON>", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ou<PERSON>", "block.minecraft.stripped_jungle_log": "Tropestamm ouni <PERSON>", "block.minecraft.stripped_jungle_wood": "T<PERSON>en<PERSON><PERSON> ou<PERSON>", "block.minecraft.stripped_mangrove_log": "Mangrouwestamm ou<PERSON>", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ou<PERSON>", "block.minecraft.stripped_oak_log": "Eechestamm ouni <PERSON>", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.stripped_pale_oak_log": "Blatzeechestamm ou<PERSON>", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ou<PERSON>", "block.minecraft.stripped_spruce_log": "<PERSON><PERSON>testamm ou<PERSON>", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou<PERSON>", "block.minecraft.stripped_warped_hyphae": "Geschielt verduerwen Hyphen", "block.minecraft.stripped_warped_stem": "Geschielte ve<PERSON>uerwene Stamm", "block.minecraft.structure_block": "Konstruktiounsblock", "block.minecraft.structure_void": "Konstruktiounsvakuum", "block.minecraft.sugar_cane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.suspicious_gravel": "Verdächtege Kräsi", "block.minecraft.suspicious_sand": "Verdächtege Sand", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON><PERSON>bierestra<PERSON>", "block.minecraft.tall_dry_grass": "Héicht Dréchegras", "block.minecraft.tall_grass": "Héicht Gras", "block.minecraft.tall_seagrass": "Héicht Séigras", "block.minecraft.target": "<PERSON><PERSON>", "block.minecraft.terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.test_block": "Pféifblock", "block.minecraft.test_instance_block": "Testinstanzblock", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT-Explosioune sinn desaktivéiert", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.trapped_chest": "Falekëscht", "block.minecraft.trial_spawner": "Préifungs-Spawner", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "Krop", "block.minecraft.tube_coral": "Uergelkorall", "block.minecraft.tube_coral_block": "Uergelkoralleblock", "block.minecraft.tube_coral_fan": "Uergelkorallefächer", "block.minecraft.tube_coral_wall_fan": "Uergelkorallemauerfächer", "block.minecraft.tuff": "Trass", "block.minecraft.tuff_brick_slab": "Trasszilleplack", "block.minecraft.tuff_brick_stairs": "Trasszillentrap", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_bricks": "Trasszillen", "block.minecraft.tuff_slab": "Trassplack", "block.minecraft.tuff_stairs": "Trasstrap", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.turtle_egg": "Schildkrötenee", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.twisting_vines_plant": "<PERSON><PERSON><PERSON><PERSON>anenplanz", "block.minecraft.vault": "Tresor", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON> Frä<PERSON>lu<PERSON>", "block.minecraft.vine": "<PERSON><PERSON>", "block.minecraft.void_air": "Loft vum Näischt", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_button": "<PERSON>erduerwen<PERSON>", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "Verduerwenen Z<PERSON>k", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON>wen Hyphen", "block.minecraft.warped_nylium": "Verduerwenen Nylium", "block.minecraft.warped_planks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wart_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.water": "<PERSON><PERSON><PERSON>", "block.minecraft.water_cauldron": "Waasserkessel", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> oxidé<PERSON>", "block.minecraft.waxed_copper_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_cut_copper": "G<PERSON>ues<PERSON> gesch<PERSON><PERSON><PERSON>", "block.minecraft.waxed_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_cut_copper_stairs": "G<PERSON>ues<PERSON> ges<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_exposed_chiseled_copper": "Gewuessten ausgesate gemeesselte Kofferblock", "block.minecraft.waxed_exposed_copper": "Gewuessten ausgesate Kofferblock", "block.minecraft.waxed_exposed_copper_bulb": "Gewuesst ausgesat Kofferbier", "block.minecraft.waxed_exposed_copper_door": "Gewuesst ausgesat Kofferdier", "block.minecraft.waxed_exposed_copper_grate": "Gewuesst ausgesat Koffergitter", "block.minecraft.waxed_exposed_copper_trapdoor": "Gewuesst ausgesat Kofferklappdier", "block.minecraft.waxed_exposed_cut_copper": "Gewuessten ausgesate geschniddene Ko<PERSON>", "block.minecraft.waxed_exposed_cut_copper_slab": "Gewuesst ausgesat geschnidde Kofferplack", "block.minecraft.waxed_exposed_cut_copper_stairs": "Gewuesst ausgesat geschnidde Koffertrap", "block.minecraft.waxed_oxidized_chiseled_copper": "Gewuessten ausgesate gemeesselte Kofferblock", "block.minecraft.waxed_oxidized_copper": "<PERSON><PERSON><PERSON><PERSON> oxidé<PERSON>", "block.minecraft.waxed_oxidized_copper_bulb": "Gewuesst oxid<PERSON><PERSON><PERSON>", "block.minecraft.waxed_oxidized_copper_door": "<PERSON><PERSON><PERSON><PERSON> oxi<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.waxed_oxidized_copper_grate": "Gewuesst oxid<PERSON><PERSON><PERSON>", "block.minecraft.waxed_oxidized_copper_trapdoor": "Gewuesst oxid<PERSON><PERSON><PERSON>dier", "block.minecraft.waxed_oxidized_cut_copper": "Gewuessten oxidéierte geschnidden<PERSON>", "block.minecraft.waxed_oxidized_cut_copper_slab": "Gewuesst oxidéiert geschnidde Kofferplack", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Gewuesst oxidéiert geschnidde Koffertrap", "block.minecraft.waxed_weathered_chiseled_copper": "Gewuesste verwitterte gemeesselte Ko<PERSON>block", "block.minecraft.waxed_weathered_copper": "Gewues<PERSON> verwi<PERSON><PERSON>", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> ver<PERSON><PERSON><PERSON>", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON> ver<PERSON><PERSON><PERSON>", "block.minecraft.waxed_weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON> ver<PERSON><PERSON><PERSON>", "block.minecraft.waxed_weathered_copper_trapdoor": "Gewuesst verwi<PERSON><PERSON> Kofferklappdier", "block.minecraft.waxed_weathered_cut_copper": "Gewuesste verwitterte geschnidden<PERSON>", "block.minecraft.waxed_weathered_cut_copper_slab": "G<PERSON>uesst verwittert geschnidde Kofferplack", "block.minecraft.waxed_weathered_cut_copper_stairs": "Gewuesst verwittert geschnidde Koffertrap", "block.minecraft.weathered_chiseled_copper": "Verwitterte gemeesselte Kofferblock", "block.minecraft.weathered_copper": "<PERSON>erwi<PERSON><PERSON>", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>kla<PERSON>", "block.minecraft.weathered_cut_copper": "Verwitterte geschniddene <PERSON>", "block.minecraft.weathered_cut_copper_slab": "<PERSON>erwi<PERSON>t geschnidde Kofferplack", "block.minecraft.weathered_cut_copper_stairs": "Verwittert geschnidde Koffertrap", "block.minecraft.weeping_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.weeping_vines_plant": "Trauerlianenplanz", "block.minecraft.wet_sponge": "<PERSON><PERSON><PERSON>", "block.minecraft.wheat": "Weessplanzen", "block.minecraft.white_banner": "Wäisse Fändel", "block.minecraft.white_bed": "<PERSON>ä<PERSON>t Bett", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.white_candle_cake": "<PERSON>ch mat w<PERSON><PERSON><PERSON>", "block.minecraft.white_carpet": "Wäissen Teppech", "block.minecraft.white_concrete": "Wäisse <PERSON>", "block.minecraft.white_concrete_powder": "Wäissen Zement", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> wäiss Keramik", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON>sch<PERSON>", "block.minecraft.white_stained_glass": "Wäisst Glas", "block.minecraft.white_stained_glass_pane": "Wäiss Glasscheif", "block.minecraft.white_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.white_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.wildflowers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_rose": "Witherrous", "block.minecraft.wither_skeleton_skull": "Witherskelettkapp", "block.minecraft.wither_skeleton_wall_skull": "Mauerwitherskelettkapp", "block.minecraft.yellow_banner": "Giele Fändel", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_candle": "<PERSON><PERSON>", "block.minecraft.yellow_candle_cake": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete": "Giele B<PERSON>g", "block.minecraft.yellow_concrete_powder": "Gielen <PERSON>", "block.minecraft.yellow_glazed_terracotta": "Glaséiert giel <PERSON>ramik", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON>-Këscht", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "Giel <PERSON>", "block.minecraft.yellow_terracotta": "<PERSON><PERSON>", "block.minecraft.yellow_wool": "<PERSON><PERSON>", "block.minecraft.zombie_head": "Zombiekapp", "block.minecraft.zombie_wall_head": "Zombiemauerkapp", "book.byAuthor": "vun %1$s", "book.edit.title": "Buch-Bearbechtungsbildschierm", "book.editTitle": "<PERSON><PERSON> den Titel an:", "book.finalizeButton": "Endgülteg signéieren", "book.finalizeWarning": "Pass op! Wanns du d'Buch signéiers, kanns du et net méi beaarbechten.", "book.generation.0": "Original", "book.generation.1": "Kopie vum Original", "book.generation.2": "<PERSON><PERSON> vun enger <PERSON>", "book.generation.3": "<PERSON><PERSON><PERSON>", "book.invalid.tag": "*Ongülteg Buchdaten*", "book.pageIndicator": "Säit %1$s vun %2$s", "book.page_button.next": "Nächst Säit", "book.page_button.previous": "Virulegend Säit", "book.sign.title": "Buch-Ënnerschreiwungsbildschierm", "book.sign.titlebox": "Titel", "book.signButton": "Signéieren", "book.view.title": "Buch-Usiichtsbildschierm", "build.tooHigh": "D'maximal Bauhéicht ass %s", "chat.cannotSend": "Chatnoriicht kann net verschéckt ginn", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Klick fir ze teleportéieren", "chat.copy": "<PERSON>", "chat.copy.click": "Klick fir an den Tëschespäicher ze kopéieren", "chat.deleted_marker": "<PERSON>ës Cha<PERSON>noriicht gouf vum Server geläscht.", "chat.disabled.chain_broken": "Den Chat gouf wéinst enger futtisser Ketten desaktivéiert. Probéier w.e.g., dech nach eng Kéier zu verbannen.", "chat.disabled.expiredProfileKey": "Chat gouf ausgeschalt wéinst engem ofgelafen Profil ëffentleche Schlëssel. Probéiert w.e.g. nach eng Kéier ze verbannen.", "chat.disabled.invalid_command_signature": "<PERSON> Be<PERSON> hat onerwaart oder feelend Befeelsargument-Signaturen.", "chat.disabled.invalid_signature": "Den Cha<PERSON> huet eng ongülteg Signatur opgewisen. W.e.g. prob<PERSON><PERSON> dech erneit ze verbannen.", "chat.disabled.launcher": "Den Chat ass wéinst de Launcher-Optiounen ofgeschaltet, et ka keng Noriicht geschéckt ginn.", "chat.disabled.missingProfileKey": "Den Chat gouf wéinst engem fehlenden ëffentleche Profilschlëssel desaktivéiert. Probéier dech w.e.g. nach eng Kéier ze verbannen.", "chat.disabled.options": "Den Chat ass an den Client-Astellungen désaktivéiert.", "chat.disabled.out_of_order_chat": "Chat ausseruerdentlech kritt. Huet Äre System Zäit geännert?", "chat.disabled.profile": "Chat ass duerch Kontenastellungen net erlaabt. Dréckt nach eng Kéier op '%s' fir méi Informatiounen.", "chat.disabled.profile.moreInfo": "<PERSON> ", "chat.editBox": "Cha<PERSON>", "chat.filtered": "Gefiltert vum Server.", "chat.filtered_full": "De <PERSON> huet deng Noriicht fir e puer Spiller verstoppt.", "chat.link.confirm": "<PERSON>, dass de déi folgend Websäit opmaache wëlls?", "chat.link.confirmTrusted": "<PERSON><PERSON>ls du dëse Link opmaachen oder an den Tëschespäicher kopéieren?", "chat.link.open": "<PERSON>", "chat.link.warning": "<PERSON><PERSON> keng <PERSON> vu <PERSON>n ob, deenens du net vertraus!", "chat.queue": "[+%s auss<PERSON><PERSON>]", "chat.square_brackets": "[%s]", "chat.tag.error": "De <PERSON> huet eng ongülteg Noriicht geschéckt.", "chat.tag.modified": "D'Noriicht gouf vum Server verännert. Original:", "chat.tag.not_secure": "Onverifizéiert Noriicht. Kann net gemellt ginn.", "chat.tag.system": "Servernoriicht. Kann net gemellt ginn.", "chat.tag.system_single_player": "Servernoriicht.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s huet d'Aufgab %s ofgeschloss", "chat.type.advancement.goal": "%s huet d'Ziel %s areecht", "chat.type.advancement.task": "%s huet de Fortschrëtt %s areecht", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Team benoriichtegen", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s seet %s", "chat.validation_error": "Chatvalidéierungsfeeler", "chat_screen.message": "Noriicht schécken: %s", "chat_screen.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chat_screen.usage": "Schrei<PERSON> eng Noriicht an dréckt Enter fir se ze schécken", "chunk.toast.checkLog": "<PERSON><PERSON> fir méi <PERSON>", "chunk.toast.loadFailure": "Chunk bei %s konnt net geluede ginn", "chunk.toast.lowDiskSpace": "Wéineg fräi Späicherplaz!", "chunk.toast.lowDiskSpace.description": "<PERSON>ës Welt kann ënner Ëmstänn net gespäichert ginn.", "chunk.toast.saveFailure": "Chunk bei %s konnt net gespäichert ginn", "clear.failed.multiple": "Bei %s Spillern goufe keng Géigestänn fonnt", "clear.failed.single": "Beim %s goufe keng Géigestänn fonnt", "color.minecraft.black": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.blue": "Blo", "color.minecraft.brown": "Brong", "color.minecraft.cyan": "Turquoise", "color.minecraft.gray": "Gro", "color.minecraft.green": "<PERSON><PERSON><PERSON>", "color.minecraft.light_blue": "Hellblo", "color.minecraft.light_gray": "Hellgro", "color.minecraft.lime": "Hellg<PERSON>ng", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Orange", "color.minecraft.pink": "<PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "Rout", "color.minecraft.white": "<PERSON><PERSON><PERSON>", "color.minecraft.yellow": "Giel", "command.context.here": "<--[HEI]", "command.context.parse_error": "%s un Positioun %s: %s", "command.exception": "Befeel feelerhaft: %s", "command.expected.separator": "Espace virun engem neiem Argument erwaart", "command.failed": "Wärend der Ausféierung vun dësem Befeel ass ee Feeler opgetrueden", "command.forkLimit": "Maximal Unzuel vu Kontexter (%s) errecht", "command.unknown.argument": "Ongültegt Befeelsargument", "command.unknown.command": "Onbekannten oder onvollstännege Befeel, kuck dr<PERSON><PERSON> fir <PERSON>", "commands.advancement.criterionNotFound": "Krittär '%2$s' gehéiert net zum Fortschrëtt %1$s", "commands.advancement.grant.criterion.to.many.failure": "Krittär '%s' vum Fortschrëtt %s konnt %s Spillern net gewäert ginn, well se schonn erfëllt sinn", "commands.advancement.grant.criterion.to.many.success": "Krittär '%s' vum Fortschrëtt %s gouf %s Spillern gewäert", "commands.advancement.grant.criterion.to.one.failure": "Krittär '%s' vum Fortschrëtt %s konnt %s net gew<PERSON>ert ginn, wëll en schonn erfëllt ass", "commands.advancement.grant.criterion.to.one.success": "Krittär '%s' vum Fortschrëtt %s gouf dem %s gewäert", "commands.advancement.grant.many.to.many.failure": "%s Fortschrëtter konnten %s Spillern net gewäert ginn, well se schonn erreecht goufen", "commands.advancement.grant.many.to.many.success": "%s Fortschrëtter goufen %s Spillern gewäert", "commands.advancement.grant.many.to.one.failure": "%s Fortschrëtter konnten dem %s net gewäert gi wëll en déi schonns hunn", "commands.advancement.grant.many.to.one.success": "%s Fortschrëtter goufen dem %s gewäert", "commands.advancement.grant.one.to.many.failure": "<PERSON>tt '%s' konnt %s Spillern net gewäert gi wëll se en schonns hunn", "commands.advancement.grant.one.to.many.success": "Fortschrëtt %s gouf %s Spillern gewäert", "commands.advancement.grant.one.to.one.failure": "<PERSON> '%s' konnt dem %s net gewäert gi wëll hien en schonns huet", "commands.advancement.grant.one.to.one.success": "Fortschrëtt %s gouf dem %s gewäert", "commands.advancement.revoke.criterion.to.many.failure": "Krittär '%s' vum Fortschrëtt '%s' konnt %s Spillern net entzu ginn, well en net erfëllt ass", "commands.advancement.revoke.criterion.to.many.success": "Krittär '%s' vum Fortschrëtt %s gouf %s Spillern entzunn", "commands.advancement.revoke.criterion.to.one.failure": "Krittär '%s' vum Fortschrëtt '%s' konnt dem %s net entzu ginn, well en net erfëllt ass", "commands.advancement.revoke.criterion.to.one.success": "Krittär '%s' vum Fortschrëtt %s gouf dem %s entzunn", "commands.advancement.revoke.many.to.many.failure": "%s Fortschrëtter konnten %s Spillern net entzu ginn, wëll se nach net erreecht goufen", "commands.advancement.revoke.many.to.many.success": "%s Fortschrëtter goufen %s Spillern entzunn", "commands.advancement.revoke.many.to.one.failure": "%s Fortschrëtter konnten dem %s net entzu ginn, wëll se nach net erreecht goufen", "commands.advancement.revoke.many.to.one.success": "%s Fortschrëtter goufen dem %s entzunn", "commands.advancement.revoke.one.to.many.failure": "Fortschrëtt %s konnt %s Spillern net entzu ginn, wëll en nach net erreecht gouf", "commands.advancement.revoke.one.to.many.success": "Fortschrëtt %s gouf %s Spillern entzunn", "commands.advancement.revoke.one.to.one.failure": "Fortschrëtt %s konnt dem %s net entzu ginn, wëll en nach net erreecht gouf", "commands.advancement.revoke.one.to.one.success": "Fortschrëtt %s gouf dem %s entzunn", "commands.attribute.base_value.get.success": "De Basiswäert vum Attribut %s vun der Entitéit %s ass %s", "commands.attribute.base_value.reset.success": "De Grondwäert vum Attribut %s vum Objet %s gouf op den Standartwäert %s zeréckgesaat.", "commands.attribute.base_value.set.success": "De Basiswäert vum Attribut %s vun der Entitéit %s gouf op %s gesat", "commands.attribute.failed.entity": "%s ass kee gültegen Objet fir dëse <PERSON>el", "commands.attribute.failed.modifier_already_present": "Modifikateur %s ass scho beim Attribut %s vun der Entitéit %s applizéiert", "commands.attribute.failed.no_attribute": "Entitéit %s huet keen Attribut %s", "commands.attribute.failed.no_modifier": "Attribut %s vun der Entitéit %s huet kee Modifikateur %s", "commands.attribute.modifier.add.success": "Modifikateur %s gouf dem Attribut %s vun der Entitéit %s dobäigesat", "commands.attribute.modifier.remove.success": "Modifikateur %s gouf dem Attribut %s vun der Entitéit %s ewechgeholl", "commands.attribute.modifier.value.get.success": "De Wäert vum Modifikateur %s vum Attribut %s vun der Entitéit %s ass %s", "commands.attribute.value.get.success": "De Wäert vum Attribut %s vun der Entitéit %s ass %s", "commands.ban.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON><PERSON>, de <PERSON><PERSON> ass scho ges<PERSON>art", "commands.ban.success": "%s gouf gespaart: %s", "commands.banip.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON><PERSON>, d'IP-<PERSON><PERSON> ass scho ges<PERSON>art", "commands.banip.info": "Dës Spärung betrëfft %s Spiller: %s", "commands.banip.invalid": "Ongülteg IP-Adress oder onbekannte Spiller", "commands.banip.success": "IP-Adress %s gouf gespaart: %s", "commands.banlist.entry": "%s gouf vum %s gespaart: %s", "commands.banlist.entry.unknown": "(Onbekannt)", "commands.banlist.list": "Et gëtt %s Spärung(en):", "commands.banlist.none": "Et gi keng Spärungen", "commands.bossbar.create.failed": "<PERSON><PERSON> mat der ID '%s' existéiert schonn", "commands.bossbar.create.success": "Benotzerdefinéiert Bossläischt %s gouf erstallt", "commands.bossbar.get.max": "De Maximalwäert vun der benotzerdefinéierter Bossläischt %s ass %s", "commands.bossbar.get.players.none": "Benotzerdefinéiert Bossläischt %s huet aktuell keng Spiller online", "commands.bossbar.get.players.some": "Benotzerdefinéiert Bossläischt %s huet aktuell %s Spiller online: %s", "commands.bossbar.get.value": "Den aktuelle Wäert vun der benotzerdefinéierter Bossläischt %s ass %s", "commands.bossbar.get.visible.hidden": "Benotzerdefinéiert Bossläischt %s ass aktuell verstoppt", "commands.bossbar.get.visible.visible": "Benotzerdefinéiert Bossläischt %s ass aktuell siichtbar", "commands.bossbar.list.bars.none": "Et si keng benotzerdefinéiert Bossläischten aktiv", "commands.bossbar.list.bars.some": "Et ass/sinn %s benotzerdefinéiert Bossläischt(en) aktiv: %s", "commands.bossbar.remove.success": "Benotzerdefinéiert Bossläischt %s gouf fort gemaach", "commands.bossbar.set.color.success": "Faarf vun der benotzerdefinéierter Bossläischt %s gouf geännert", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, d'<PERSON><PERSON><PERSON><PERSON>t huet schonn dë<PERSON>", "commands.bossbar.set.max.success": "Maximalwäert vun der benotzerdefinéierter Bossläischt %s gouf op %s geännert", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON>, d'<PERSON><PERSON><PERSON><PERSON>t huet schonn dëse <PERSON>", "commands.bossbar.set.name.success": "Benotzerdefinéiert Bossläischt %s gouf ëmbenannt", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON>, d<PERSON><PERSON><PERSON><PERSON><PERSON>t huet schonn d<PERSON>sen <PERSON>", "commands.bossbar.set.players.success.none": "Benotzerdefinéiert Bossläischt %s huet keng Spiller méi", "commands.bossbar.set.players.success.some": "Benotzerdefinéiert Bossläischt %s huet aktuell %s Spiller: %s", "commands.bossbar.set.players.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, d<PERSON><PERSON> si schonn enger Bossläischt zougeuerdent, et gouf keen dobäi gefüügt oder ewechgeholl", "commands.bossbar.set.style.success": "Andeelung vun der benotzerdefinéierter Bossläischt %s gouf geännert", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, d'<PERSON><PERSON><PERSON><PERSON>t huet schonn dës Ënnerdeelung", "commands.bossbar.set.value.success": "<PERSON><PERSON>ert vun der benotzerdefinéierter Bossläischt %s gouf op %s geännert", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON>, d'<PERSON><PERSON><PERSON><PERSON>t huet schonn dëse <PERSON>", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ass scho verstoppt", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t ass scho <PERSON>tbar", "commands.bossbar.set.visible.success.hidden": "Benotzerdefinéiert Bossläischt %s ass elo verstoppt", "commands.bossbar.set.visible.success.visible": "Benotzerdefinéiert Bossläischt %s ass elo siichtbar", "commands.bossbar.unknown": "Et existéiert keng Bossläischt mat der ID '%s'", "commands.clear.success.multiple": "%s Geigestand/‐stänner gouf(en) vum %s Spiller ewechgeholl", "commands.clear.success.single": "%s Geigestand/‐stänner gouf(en) vum %s Spiller ewechgeholl", "commands.clear.test.multiple": "%s passend Géigestänn goufe beim %s fonnt", "commands.clear.test.single": "%s passend Géigestänn goufe beim %s fonnt", "commands.clone.failed": "Et goufe keng Blé<PERSON> kopéiert", "commands.clone.overlap": "Ursprong- an Zielberäich däerfen sech net iwwerschneiden", "commands.clone.success": "%s Bléck goufen erfollegräich kopéiert", "commands.clone.toobig": "Ze vill Bléck am Beräich deen ugi gouf (maximal %s, uginn %s)", "commands.damage.invulnerable": "Ziel ka vun dësem Schuedenstyp net verletzt ginn", "commands.damage.success": "%s <PERSON><PERSON><PERSON><PERSON> gouf op %s u<PERSON>wan<PERSON>", "commands.data.block.get": "%s vum Block bei (%s, %s, %s) mat %s multiplizéiert ass %s", "commands.data.block.invalid": "Den Zielblock huet keng Blockdate", "commands.data.block.modified": "Blockdate bei (%s, %s, %s) goufe geännert", "commands.data.block.query": "Block bei (%s, %s, %s) besetzt déi folgend Blockdata: %s", "commands.data.entity.get": "%s vun %s mat %s multiplizéiert ass %s", "commands.data.entity.invalid": "Spillerdate konnten net geännert ginn", "commands.data.entity.modified": "Objetdate vun %s goufe geännert", "commands.data.entity.query": "%s huet déi folgend Objetdate: %s", "commands.data.get.invalid": "'%s' kann net ofgefrot ginn, nëmmen numeresch Tags sinn erlaabt", "commands.data.get.multiple": "D'Argument akzeptéiert nëmmen een eenzelen NBT-Wäert", "commands.data.get.unknown": "'%s' kann net ofgefrot ginn, den Tag existéiert net", "commands.data.merge.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech geännert. D'Eegenschaften déi ugi goufen hunn schonn dës <PERSON>er", "commands.data.modify.expected_list": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>, '%s' er<PERSON>n", "commands.data.modify.expected_object": "<PERSON>b<PERSON> er<PERSON><PERSON>, '%s' er<PERSON>n", "commands.data.modify.expected_value": "<PERSON><PERSON><PERSON>, \"%s\" kritt", "commands.data.modify.invalid_index": "Ongültege Lëschtenindex: %s", "commands.data.modify.invalid_substring": "Ongülteg Ofschnëttsindizies: %s bis %s", "commands.data.storage.get": "%s am Späicher %s mat %s multiplizéiert ass %s", "commands.data.storage.modified": "Späicher %s gouf geännert", "commands.data.storage.query": "Späicher %s beinhalt: %s", "commands.datapack.create.already_exists": "Pack mam Numm  existéiert schonn.", "commands.datapack.create.invalid_full_name": "Onvalide nei Pack-Ënner Nam .", "commands.datapack.create.invalid_name": "Onvalide Zeechen am neie Packnumm ", "commands.datapack.create.io_failure": "Kann Pack mam Numm net erstellen, kuck d’Logs no.", "commands.datapack.create.metadata_encode_failure": "Kodéiere vun Metadaten fir Pack mam Numm fehlgeschloen.", "commands.datapack.create.success": "Neie eidele Pack mam Numm erstallt.", "commands.datapack.disable.failed": "Datepak '%s' ass net aktivéiert!", "commands.datapack.disable.failed.feature": "Datepak '%s' kann net desaktivéiert ginn, well et e Bestanddeel vun engem aktiven Ëmschalter ass!", "commands.datapack.enable.failed": "Datepak '%s' ass schonn aktivéiert!", "commands.datapack.enable.failed.no_flags": "Den Datepak '%s' konnt net aktivéiert ginn, well déi benéidegt Emschalter fir des Welt net aktivéiert sinn: %s!", "commands.datapack.list.available.none": "Et si keng weider Datepäck verfügbar", "commands.datapack.list.available.success": "Et ass/si(nn) %s Datepa(äc)k verfügbar: %s", "commands.datapack.list.enabled.none": "Et sinn keng Datepäck aktivéiert", "commands.datapack.list.enabled.success": "Et ass/sinn dës(t) %s <PERSON><PERSON>(ä)ck aktivéiert: %s", "commands.datapack.modify.disable": "Desaktivéier Datepäck %s", "commands.datapack.modify.enable": "Aktivéier Datepäck %s", "commands.datapack.unknown": "Onbekannten Datepak '%s'", "commands.debug.alreadyRunning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ng gouf scho gestart", "commands.debug.function.noRecursion": "Et kann net aus enger Funktioun opgezeechent ginn", "commands.debug.function.noReturnRun": "Tracing kann net mat <PERSON>tour lafen benotzt ginn", "commands.debug.function.success.multiple": "Suivi des commande(s) %s des fonctions %s vers le fichier de sortie %s", "commands.debug.function.success.single": "Suivi des commande(s) %s de la fonction '%s' au fichier de sortie %s", "commands.debug.function.traceFailed": "Funktioun konnt net opgezeechent ginn", "commands.debug.notRunning": "Tikopzeechnung ass net aktiv", "commands.debug.started": "<PERSON><PERSON><PERSON><PERSON><PERSON>nung gouf gestart", "commands.debug.stopped": "Tickopzeechnung gouf no %s Sekonnen a %s Ticks gestoppt (%s Ticks pro Sekonn)", "commands.defaultgamemode.success": "Standardspillmodus gouf op %s gesat", "commands.deop.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON><PERSON>, de Spiller ass keen Operator", "commands.deop.success": "%s ass kee Serveroperator méi", "commands.dialog.clear.multiple": "Dialog fir Spiller geläscht.", "commands.dialog.clear.single": "Dialog gelä<PERSON>t fir", "commands.dialog.show.multiple": "Dialog un d'Spiller gewisen", "commands.dialog.show.single": "Dialog un", "commands.difficulty.failure": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, d'Schwieregkeet steet schonn op %s", "commands.difficulty.query": "D'Schwieregkeet ass %s", "commands.difficulty.success": "Schwieregkeet gouf op %s gesat", "commands.drop.no_held_items": "Objet ka keng Géigestänn droen", "commands.drop.no_loot_table": "Objet %s huet keng <PERSON>bell", "commands.drop.no_loot_table.block": "Block %s huet keng <PERSON>bell", "commands.drop.success.multiple": "%s Géigestänn fale gelooss", "commands.drop.success.multiple_with_table": "%s Géigestänn aus der Beutetabell %s fale gelooss", "commands.drop.success.single": "%s %s fale gelooss", "commands.drop.success.single_with_table": "%s %s Géigestänn aus der Beutetabell %s fale gelooss", "commands.effect.clear.everything.failed": "<PERSON><PERSON> huet keen Effekt deen ewechgeholl kéint ginn", "commands.effect.clear.everything.success.multiple": "All Effekter goufe vun %s Zieler ewechgeholl", "commands.effect.clear.everything.success.single": "All Effekter goufe vum %s ewechgeholl", "commands.effect.clear.specific.failed": "<PERSON><PERSON> huet deen Effekt deen ewechgeholl sollt ginn net", "commands.effect.clear.specific.success.multiple": "Effekt %s gouf vun %s Zieler ewechgeholl", "commands.effect.clear.specific.success.single": "Effekt %s gouf vum %s ewechgeholl", "commands.effect.give.failed": "Effekt konnt net ugewannt ginn (d'Ziel ass entweder resistent oder besëtzt eppes méi <PERSON>aarkes)", "commands.effect.give.success.multiple": "Effekt %s gouf op %s Zieler ugewannt", "commands.effect.give.success.single": "Effekt %s gouf op den %s ugewannt", "commands.enchant.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, d'<PERSON><PERSON><PERSON> hunn entweder keng Géigestänn an hirer <PERSON><PERSON><PERSON><PERSON> oder d'Verzauberung konnt net applizéiert ginn", "commands.enchant.failed.entity": "%s ass keng gëlteg Entitéit fir dëse Befeel", "commands.enchant.failed.incompatible": "%s kann domat net verz<PERSON>bert ginn", "commands.enchant.failed.itemless": "%s huet kee <PERSON> a senger Haapthand", "commands.enchant.failed.level": "%s ass ze h<PERSON><PERSON>, de maximale Level fir dës Verzauberung ass %s", "commands.enchant.success.multiple": "Verzauberung %s gouf op %s Objeten ugewannt", "commands.enchant.success.single": "Verzauberung %s gouf op de Géigestand vum %s applizéiert", "commands.execute.blocks.toobig": "Ze vill Bléck am Beräich deen ugi gouf (maximal %s, uginn %s)", "commands.execute.conditional.fail": "Test feelgeschloen", "commands.execute.conditional.fail_count": "Test feelgeschloen, Unzuel: %s", "commands.execute.conditional.pass": "Test erfolleg<PERSON><PERSON><PERSON>", "commands.execute.conditional.pass_count": "Test erfo<PERSON><PERSON><PERSON><PERSON><PERSON>, Unzuel: %s", "commands.execute.function.instantiationFailure": "Funktioun %s konnt net ausgewäert ginn: %s", "commands.experience.add.levels.success.multiple": "%s Erfarungslevel goufen un %s Spiller ginn", "commands.experience.add.levels.success.single": "%s Erfarungslevel goufen dem %s ginn", "commands.experience.add.points.success.multiple": "%s Erfarungspunkte goufen un %s Spiller ginn", "commands.experience.add.points.success.single": "%s Erfarungspunkte goufen dem %s ginn", "commands.experience.query.levels": "%s huet Erfarungslevel %s", "commands.experience.query.points": "%s huet %s Erfarungspunkten", "commands.experience.set.levels.success.multiple": "Erfarungslevel vun %2$s Spiller goufen op %1$s gesat", "commands.experience.set.levels.success.single": "Erfarungslevel vum %2$s gouf op %1$s gesat", "commands.experience.set.points.invalid": "Erfarungspunkten däerfen net méi héich gesat gi wei de Maximum vum aktuellen Erfarungslevel vum Spiller", "commands.experience.set.points.success.multiple": "Erfarungspunkte vun %2$s Spillern goufen op %1$s gesat", "commands.experience.set.points.success.single": "Erfarungspunkte vum %2$s goufen op %1$s gesat", "commands.fill.failed": "Et goufe keng Blé<PERSON> placé<PERSON>t", "commands.fill.success": "%s Block/Bléck goufen erfollegräich placéiert()", "commands.fill.toobig": "Ze vill Bléck am Beräich deen ugi gouf (maximal %s, uginn %s)", "commands.fillbiome.success": "Biomer tëschent %s, %s, %s an %s, %s, %s", "commands.fillbiome.success.count": "%sbiomer tëschent %s, %s, %s an %s, %s, %s%", "commands.fillbiome.toobig": "Ze vill Bléck am Beräich deen ugi gouf (maximal %s, uginn %s)", "commands.forceload.added.failure": "<PERSON><PERSON><PERSON><PERSON> huet sech g<PERSON><PERSON>, d<PERSON><PERSON> gi scho <PERSON> gelueden", "commands.forceload.added.multiple": "%s Chunks an %s vun %s bis %s ginn elo permanent gelueden", "commands.forceload.added.none": "An %s gi keng Chunks permanent gelueden", "commands.forceload.added.single": "Chunk %s an %s gëtt permanent gelueden", "commands.forceload.list.multiple": "An %2$s ginn %1$s Chunks permanent gelueden: %s", "commands.forceload.list.single": "An %s gëtt ee Chunk permanent gelueden: %s", "commands.forceload.query.failure": "Chunk %s an %s gëtt net permanent gelueden", "commands.forceload.query.success": "Chunk %s an %s gëtt permanent gelueden", "commands.forceload.removed.all": "An %s ginn elo keng Chunks mei gelueden", "commands.forceload.removed.failure": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON>, d<PERSON><PERSON> goufe bis elo och net permanent gelueden", "commands.forceload.removed.multiple": "%s Chunks an %s vun %s bis %s ginn elo net méi permanent gelueden", "commands.forceload.removed.single": "Chunk %s an %s gëtt elo net méi permanent gelueden", "commands.forceload.toobig": "Ze vill Chunks am Beräich deen ugi gouf (maximal %s, uginn %s)", "commands.function.error.argument_not_compound": "Ongëltegen Argumententyp: ‚%s‘ ass kee Verbond", "commands.function.error.missing_argument": "Feelend Argument %2$s fir d'Funktioun %1$s", "commands.function.error.missing_arguments": "Feelend Argumenter fir d'Funktioun %s", "commands.function.error.parse": "Bei der Auswäertung vum Makro %s huet de Befeel %s e Feeler verursaacht: %s", "commands.function.instantiationFailure": "Funktioun %s konnt net ausgewäert ginn: %s", "commands.function.result": "Funktioun %s äntwert %s", "commands.function.scheduled.multiple": "Funktionen %s ginn ausgeféiert", "commands.function.scheduled.no_functions": "Et gouf keng Funktioun mam Numm %s fonnt", "commands.function.scheduled.single": "Funktionen %s ginn ausgeféiert", "commands.function.success.multiple": "%s Befeeler vu(n) %s Funktioune goufen ausgeféiert", "commands.function.success.multiple.result": "%s Funktionen ausgeféiert", "commands.function.success.single": "%s Be<PERSON>el(er) vun der Funktioun '%s' gouf(en) ausgeféiert", "commands.function.success.single.result": "Funktioun %2$s äntwert %1$s", "commands.gamemode.success.other": "Dem %s säi Spillmodus gouf op %s gesat", "commands.gamemode.success.self": "Däi Spillmodus gouf op %s gesat", "commands.gamerule.query": "D'Spillreegel %s ass am Moment '%s'", "commands.gamerule.set": "D'Spillreegel %s gouf op '%s' gesat", "commands.give.failed.toomanyitems": "Kann net mei wei %s %s ginn", "commands.give.success.multiple": "%s %s un %s Spiller ginn", "commands.give.success.single": "%s %s goufen dem %s ginn", "commands.help.failed": "Onbekannte Befeel oder net genuch Rechter", "commands.item.block.set.success": "Inventarplaz bei %s, %s, %s gouf duerch %s ersat", "commands.item.entity.set.success.multiple": "Slot mat %s Objete gouf duerch %s ersat", "commands.item.entity.set.success.single": "Inventarplaz bei %s gouf duerch %s ersat", "commands.item.source.no_such_slot": "D'Quell huet Inventarplatz %s net", "commands.item.source.not_a_container": "Quellpositioun %s, %s, %s huet keen Inventar", "commands.item.target.no_changed.known_item": "Keen Ziel huet den Objet %s um Inventarplatz %s akzeptéiert", "commands.item.target.no_changes": "Keen Ziel huet den Objet um Inventarplatz %s akzeptéiert", "commands.item.target.no_such_slot": "Den Ziel huet d'Inventarplaz %s net", "commands.item.target.not_a_container": "Zielpositioun %s, %s, %s ass kee Container", "commands.jfr.dump.failed": "Ausgefall JFR Opname net ze lueden: %s", "commands.jfr.start.failed": "Ausgefall JFR Profiléierung unzefänken", "commands.jfr.started": "JFR Profiléierung ugefaang", "commands.jfr.stopped": "JFR Profiléiere gestoppt an op %s gelueden", "commands.kick.owner.failed": "De <PERSON> be<PERSON>er vun engem LAN-Spill kann net erausgehäit ginn", "commands.kick.singleplayer.failed": "Kann kee Spiller am offline Eenzelspiller kicken", "commands.kick.success": "%s gouf erausgeworf: %s", "commands.kill.success.multiple": "%s Objete goufe besäitegt", "commands.kill.success.single": "%s ëmbruecht", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Et sinn %s vu maximal %s Spiller online: %s", "commands.locate.biome.not_found": "Et konnt kee Biom vum Typ \"%s\" an enger räsonabeler Distanz fonnt ginn", "commands.locate.biome.success": "Déi nächst %s Konstruktioun ass bei %s (%s Bléck weit)", "commands.locate.poi.not_found": "Konnt keen Interessepunkt vum Typ \"%s\" bannent enger räsonabeler Di<PERSON> fannen", "commands.locate.poi.success": "Déi nächst %s Konstruktioun ass bei %s (%s Bléck weit)", "commands.locate.structure.invalid": "Et gëtt keng Struktur vum Typ \"%s\"", "commands.locate.structure.not_found": "Konnt keng Struktur vum Typ \"%s\" an der Ëmgéigend fannen", "commands.locate.structure.success": "Déi nächst %s Konstruktioun ass bei %s (%s Bléck weit fort)", "commands.message.display.incoming": "%s huet dir geflüstert: %s", "commands.message.display.outgoing": "Du flüsters dem %s: %s", "commands.op.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON><PERSON>, de Spiller ass schonn Operator", "commands.op.success": "%s gouf zum Serveroperator genannt", "commands.pardon.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON><PERSON>, de Spiller ass net gespaart", "commands.pardon.success": "%s gouf entspäert", "commands.pardonip.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON>, dës IP-Adress ass net gespaart", "commands.pardonip.invalid": "Ongülteg IP-Adress", "commands.pardonip.success": "IP-Adress %s gouf entspaart", "commands.particle.failed": "Partikel war fir kee siichtbar", "commands.particle.success": "Partikel %s gouf ugewisen", "commands.perf.alreadyRunning": "Le profileur de performances est déjà démar<PERSON>", "commands.perf.notRunning": "Le profileur de performances n'a pas démarré", "commands.perf.reportFailed": "Debug Bericht konnt net erstallt ginn", "commands.perf.reportSaved": "Debug Bericht gouf erstallt an %s", "commands.perf.started": "Lancement d'une exécution de profilage des performances de 10 secondes (utilisez '/perf stop' pour arrêter plus tôt)", "commands.perf.stopped": "Debug-Opzeechnung gouf no %s Sekonnen an %s Ticks gestoppt (%s Ticks pro Sekonn)", "commands.place.feature.failed": "Placéieren ass feelgeschloen", "commands.place.feature.invalid": "Et gëtt keen Element vum Typ \"%s\"", "commands.place.feature.success": "\"%s\" ass bei %s, %s, %s placéiert ginn", "commands.place.jigsaw.failed": "Generéierung fir Jigsaw feelgeschloen", "commands.place.jigsaw.invalid": "<PERSON>t g<PERSON>tt kee Schablounepool mam Typ \"%s\"", "commands.place.jigsaw.success": "Verbond gouf bei %s, %s, %s, generéiert", "commands.place.structure.failed": "Placéieren vun der Struktur feelgeschloen", "commands.place.structure.invalid": "Et gëtt keng Struktur vum Typ \"%s\"", "commands.place.structure.success": "Struktur \"%s\" gouf bei %s, %s, %s gene<PERSON><PERSON>t", "commands.place.template.failed": "Schabloun konnt net placéiert ginn", "commands.place.template.invalid": "Et gëtt keng Schabloun mat ID \"%s\"", "commands.place.template.success": "Schabloun \"%s\" bei %s, %s, %s, gelueden", "commands.playsound.failed": "D'Geräisch ass ze weit fort fir gehéiert ze ginn", "commands.playsound.success.multiple": "Geräisch %s gouf fir %s Spiller ofgespillt", "commands.playsound.success.single": "Geräisch %s gouf fir %s ofgespillt", "commands.publish.alreadyPublished": "Multispiller Spill ass schonn ënner %s erreechbar", "commands.publish.failed": "Et konnt kee Multispiller Spill erstallt ginn", "commands.publish.started": "Multispiller Spill ënnert dem Port %s erreechbar", "commands.publish.success": "Multispiller Spill elo ënner Port %s erreechbar", "commands.random.error.range_too_large": "<PERSON><PERSON><PERSON><PERSON> dierf maximal 2147483646 <PERSON><PERSON><PERSON><PERSON> ëmfaassen", "commands.random.error.range_too_small": "D'Spann muss mindestens 2 Wäerter ëmfaassen", "commands.random.reset.all.success": "%s zoufälleg Sequenz zrécksetzen", "commands.random.reset.success": "Zoufälleg Sequenz %s zrécksetzen", "commands.random.roll": "%s gewalzt %s (vu %s bis %s)", "commands.random.sample.success": "Zoufallswäert: %s", "commands.recipe.give.failed": "<PERSON><PERSON> néi Rezepter goufe geleiert", "commands.recipe.give.success.multiple": "%s Rezepter goufe fir %s Spiller fräigeschalt", "commands.recipe.give.success.single": "%s Rezepter goufe fir den %s fräigeschalt", "commands.recipe.take.failed": "Et konnte keng Rezepter vergiess ginn", "commands.recipe.take.success.multiple": "%s Rezepter goufe vun %s Spiller ewechgeholl", "commands.recipe.take.success.single": "%s Rezepter goufen dem %s ewechgeholl", "commands.reload.failure": "<PERSON><PERSON><PERSON>, al <PERSON> gi gehalen", "commands.reload.success": "<PERSON><PERSON>!", "commands.ride.already_riding": "%s ass schonn op %s eropgeklommen", "commands.ride.dismount.success": "%s ass vun %s erofgeklommen", "commands.ride.mount.failure.cant_ride_players": "Spiller kënnen net gefuer ginn", "commands.ride.mount.failure.generic": "%s konnt net op %s eropgekaommen", "commands.ride.mount.failure.loop": "Kann net Entitéit op sech selwer oder ee vu senge Passagéier montéieren", "commands.ride.mount.failure.wrong_dimension": "Kann net Entitéit a verschiddene Dimensioun monté<PERSON>en", "commands.ride.mount.success": "%s ass op %s eropgeklommen", "commands.ride.not_riding": "%s ", "commands.rotate.success": "%s gouf rot<PERSON>t", "commands.save.alreadyOff": "D'Späiche<PERSON> ass schonn desaktivéiert", "commands.save.alreadyOn": "D'Späicheren ass schonn aktivéiert", "commands.save.disabled": "Automatesch Späicheren ass elo desaktivéiert", "commands.save.enabled": "Automatesch Späicheren ass elo aktivéiert", "commands.save.failed": "Spill konnt net gespäichert ginn (net genuch Späicher frei?)", "commands.save.saving": "<PERSON><PERSON><PERSON> g<PERSON>tt gesp<PERSON>ichert (dat kann ee <PERSON> daueren!)", "commands.save.success": "<PERSON>pill gouf gesp<PERSON>rt", "commands.schedule.cleared.failure": "Et ginn keng Pläng mat der Id %s", "commands.schedule.cleared.success": "Et goufe(n) %s Pläng mat der Id %s entfernt", "commands.schedule.created.function": "Funktioun '%s' gouf an %s Tick(s) zur Spillzäit %s ageplangt", "commands.schedule.created.tag": "Etikett '%s' gouf an %s Ticks zur Spillzäit %s ageplangt", "commands.schedule.macro": "Makroe kënne net geplangt ginn", "commands.schedule.same_tick": "Fir den aktuellen Tick kann näischt geplangt ginn", "commands.scoreboard.objectives.add.duplicate": "<PERSON><PERSON> mat dësem Numm existéiert schonn", "commands.scoreboard.objectives.add.success": "Néit Ziel %s gouf erstallt", "commands.scoreboard.objectives.display.alreadyEmpty": "<PERSON><PERSON><PERSON><PERSON> huet sech g<PERSON><PERSON><PERSON>, Displaypositioun ass schonn eidel", "commands.scoreboard.objectives.display.alreadySet": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, Displaypositioun wéisst schonn op d'Ziel", "commands.scoreboard.objectives.display.cleared": "All Zieler am Displayslot %s goufe geläscht", "commands.scoreboard.objectives.display.set": "Displayslot %s wéist elo d'Ziel %s un", "commands.scoreboard.objectives.list.empty": "Et gi keng <PERSON>", "commands.scoreboard.objectives.list.success": "Et gëtt%s Ziel(er): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Behënnert Display Autoupdate fir Objektiv %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Aktivéiert Display Autoupdate fir Objektiv %s", "commands.scoreboard.objectives.modify.displayname": "Den Displaynumm vum %s gouf op %s geännert", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Standard-Zueledostellung vun Pukte-Ziel &s gouf zeréckgesaat ", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Geläscht Standardnummerformat vum Objektiv %s", "commands.scoreboard.objectives.modify.rendertype": "De Render Typ vum Ziel %s gouf geännert", "commands.scoreboard.objectives.remove.success": "Ziel %s gouf geläscht", "commands.scoreboard.players.add.success.multiple": "%2$s gouf fir %2$s Objeten ëm %1$s erhéicht", "commands.scoreboard.players.add.success.single": "%2$s gouf fir %3$s ëm %1$s erhéicht (elo %4$s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Geläscht Displaynumm fir %s Entitéiten an %s", "commands.scoreboard.players.display.name.clear.success.single": "Geläscht Affichage Numm fir %s an %s", "commands.scoreboard.players.display.name.set.success.multiple": "Geännert Affichage Numm op %s fir %s Entitéiten an %s", "commands.scoreboard.players.display.name.set.success.single": "Geännert Affichage Numm op %s fir %s Entitéiten an %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Geläscht Nummerformat fir %s Entitéiten an %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Geläscht Nummerformat fir %s Entitéiten an %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Geläscht Nummerformat fir %s Entitéiten an %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Geläscht Nummerformat fir %s Entitéiten an %s", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>t, den Ausléiser ass schonn aktivéiert", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON><PERSON>t nëmme fir Ausléiser (trigger-<PERSON><PERSON>er)", "commands.scoreboard.players.enable.success.multiple": "Ausléiser %s gouf fir %s Objeten aktivéiert", "commands.scoreboard.players.enable.success.single": "Ausléiser %s fir %s aktivéiert", "commands.scoreboard.players.get.null": "<PERSON><PERSON><PERSON> vun %s fir %s kann net ofgefrot gi well kee gesat gouf", "commands.scoreboard.players.get.success": "%s huet %s %s", "commands.scoreboard.players.list.empty": "Et gi keng iwwerwaachten Objeten", "commands.scoreboard.players.list.entity.empty": "%s huet keng Punktestänn", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s huet %s Punktestand/-stänn:", "commands.scoreboard.players.list.success": "Et gëtt %s iwwerwaacht Objet(e): %s", "commands.scoreboard.players.operation.success.multiple": "%s gouf fir %s Objete geännert", "commands.scoreboard.players.operation.success.single": "%s gouf fir %s op %s geännert", "commands.scoreboard.players.remove.success.multiple": "%2$s gouf fir %3$s Objeten ëm %1$s verréngert", "commands.scoreboard.players.remove.success.single": "%2$s gouf fir %3$s ëm %1$s verréngert (elo %4$s)", "commands.scoreboard.players.reset.all.multiple": "All Punktestänn goufe fir %s Objete geläscht", "commands.scoreboard.players.reset.all.single": "All Punktestänn goufe fir %s geläscht", "commands.scoreboard.players.reset.specific.multiple": "%s gouf fir %s Objete geläscht", "commands.scoreboard.players.reset.specific.single": "%s gouf fir %s geläscht", "commands.scoreboard.players.set.success.multiple": "%s gouf fir %s Objeten op %s gesat", "commands.scoreboard.players.set.success.single": "%s gouf fir %s op %s gesat", "commands.seed.success": "Seed: %s", "commands.setblock.failed": "Block konnt net placéiert ginn", "commands.setblock.success": "Block bei (%s, %s, %s) gouf geännert", "commands.setidletimeout.success": "Inaktivitéitslimitt gouf op %s Minutte gesat", "commands.setidletimeout.success.disabled": "D'Ontätegkeetslimitt ass elo desaktivéiert", "commands.setworldspawn.failure.not_overworld": "Welt", "commands.setworldspawn.success": "Welt Spawnpunkt gouf op (%s, %s, %s) [%s] gesat", "commands.spawnpoint.success.multiple": "Spawnpunkt vun %6$s Spillern gouf op (%1$s, %2$s, %3$s) [%4$s] an %5$s gesat", "commands.spawnpoint.success.single": "Spawnpunkt gouf vum %6$s op (%1$s, %2$s, %3$s) [%4$s] an %5$s gesat", "commands.spectate.not_spectator": "%s ass net am Zuschauermodus", "commands.spectate.self": "Du kanns dech net selwer beobachten", "commands.spectate.success.started": "Du beobachts elo %s", "commands.spectate.success.stopped": "Du beobachts keen Objet méi", "commands.spreadplayers.failed.entities": "%s Objete konnten net em (%s, %s) verdeelt ginn (ze wéineg Platz fir d'Objeten, probéier maximal %s ze verdeelen)", "commands.spreadplayers.failed.invalid.height": "Ongültege Wäert fir maxHeight %s; méi héich wéi Weltminimum %s ass erwaart", "commands.spreadplayers.failed.teams": "%s Teams konnten net em (%s, %s) verdeelt ginn (ze wéineg Platz fir d'Teams, probéier maximal %s ze verdeelen)", "commands.spreadplayers.success.entities": "%s Spiller gouf(e) ronderëm (%s, %s) mat engem Ofstand vun duerchschnëttlech %s Bléck ënnertenee verdeelt", "commands.spreadplayers.success.teams": "%s Teams goufe ronderëm (%s, %s) mat engem Ofstand vun duerchschnëttlech %s Bléck ënnertenee verdeelt", "commands.stop.stopping": "Server g<PERSON>tt gestoppt", "commands.stopsound.success.source.any": "All %s Geräischer goufe gestoppt", "commands.stopsound.success.source.sound": "Geräisch %s fir Geräischaart '%s' gouf gestoppt", "commands.stopsound.success.sourceless.any": "All Geräischer goufe gestoppt", "commands.stopsound.success.sourceless.sound": "Geräisch %s gouf gestoppt", "commands.summon.failed": "Objet konnt net erstallt ginn", "commands.summon.failed.uuid": "Entitéit konnt net erschaaft ginn; déi UUID existéiert schon", "commands.summon.invalidPosition": "Ongülteg Positioun fir ze erschafen", "commands.summon.success": "%s gouf erstallt", "commands.tag.add.failed": "Entweder huet d'<PERSON><PERSON> dësen Tag schonn oder et huet ze vill Tags", "commands.tag.add.success.multiple": "Tag '%s' gouf %s Objeten dobäigesat", "commands.tag.add.success.single": "Tag '%s' gouf dem %s dobäigesat", "commands.tag.list.multiple.empty": "Déi %s ausgewielten Objeten hunn keen Tag", "commands.tag.list.multiple.success": "Déi %s ausgewielten Objeten hunn insgesamt %s Tags: %s", "commands.tag.list.single.empty": "%s huet keng <PERSON>s", "commands.tag.list.single.success": "%s huet %s Tags: %s", "commands.tag.remove.failed": "<PERSON><PERSON> huet dësen Tag net", "commands.tag.remove.success.multiple": "Tag '%s' gouf vun %s Ob<PERSON><PERSON> ewechgeholl", "commands.tag.remove.success.single": "Tag '%s' gouf vum %s ewechgeholl", "commands.team.add.duplicate": "Een Team mat dësem Numm existé<PERSON>t schonn", "commands.team.add.success": "Team %s gouf erstallt", "commands.team.empty.success": "%s Member(e) goufen aus dem Team %s ewechgeholl", "commands.team.empty.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON><PERSON>, d'Team ass schonn eidel", "commands.team.join.success.multiple": "%s <PERSON><PERSON> goufen dem Team %s dob<PERSON><PERSON> gefüügt", "commands.team.join.success.single": "%s gouf dem Team %s dob<PERSON><PERSON> gefüügt", "commands.team.leave.success.multiple": "%s <PERSON>e goufen aus dem Team fort gemaach", "commands.team.leave.success.single": "%s gouf aus dem Team fort gemaach", "commands.team.list.members.empty": "Team %s huet keng <PERSON>", "commands.team.list.members.success": "Team %s huet %s Member(en): %s", "commands.team.list.teams.empty": "Et gi keng Teams", "commands.team.list.teams.success": "Et ginn %s Teams: %s", "commands.team.option.collisionRule.success": "Kollisiounsreegel fir Team %s gouf op '%s' gesat", "commands.team.option.collisionRule.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech g<PERSON><PERSON>, <PERSON>llisiounsreegel huet schonn dëse <PERSON>", "commands.team.option.color.success": "Faarwe vum Team %s goufen op %s gesat", "commands.team.option.color.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech g<PERSON><PERSON><PERSON>, d'Team huet schonn dë<PERSON>", "commands.team.option.deathMessageVisibility.success": "Siichtbarkeet vun den Doudesmeldunge vum Team %s gouf op '%s' gesat", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, d'Siichtbarkeet vun den Doudesmeldungen huet schonn dëse <PERSON>", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON><PERSON><PERSON> huet sech g<PERSON><PERSON>, fir dëst Team ass den Eegebeschoss schonn desaktivéiert", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON><PERSON><PERSON><PERSON> huet sech g<PERSON><PERSON>, fir dëst Team ass den Eegebeschoss schonn aktivéiert", "commands.team.option.friendlyfire.disabled": "Eegebeschoss gouf fir Team %s desaktivéiert", "commands.team.option.friendlyfire.enabled": "Eegebeschoss gouf fir Team %s aktivéiert", "commands.team.option.name.success": "Numm vum Team %s gouf aktualiséiert", "commands.team.option.name.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech g<PERSON><PERSON><PERSON>, d'Team huet schonn dësen <PERSON>", "commands.team.option.nametagVisibility.success": "Sichtbarkeit vum Spillernumm vum Team %s gouf op '%s' gesaat", "commands.team.option.nametagVisibility.unchanged": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>t, d'Siichtbarkeet vum Spillernumm huet schonn dëse W<PERSON>", "commands.team.option.prefix.success": "Team-Präfix gouf op %s gesat", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "<PERSON><PERSON><PERSON><PERSON> huet sech g<PERSON><PERSON><PERSON>, d'Team ka schonn keng onsiichtbar Teammembere gesinn", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "<PERSON><PERSON><PERSON><PERSON> huet sech g<PERSON><PERSON><PERSON>, d'Team ka schonn onsiichtbar Teammembere gesinn", "commands.team.option.seeFriendlyInvisibles.disabled": "Team %s ka keng onsiichtbar Teammembere méi gesinn", "commands.team.option.seeFriendlyInvisibles.enabled": "Team %s kann elo onsiichtbar Teammembere gesinn", "commands.team.option.suffix.success": "Team-Suffix gouf op %s gesat", "commands.team.remove.success": "Team %s gouf <PERSON>", "commands.teammsg.failed.noteam": "Du muss an engem Team sinn, fir däin Team ze benoriichtegen", "commands.teleport.invalidPosition": "Ongëlteg Po<PERSON>n fir ze teleportéieren", "commands.teleport.success.entity.multiple": "%s Objete goufen bei den %s teleportéiert", "commands.teleport.success.entity.single": "%s gouf zu %s teleportéiert", "commands.teleport.success.location.multiple": "%s Objete goufen op (%s, %s, %s) teleportéiert", "commands.teleport.success.location.single": "%s gouf op %s,%s,%s teleportéiert", "commands.test.batch.starting": "Ëmgéigend %s, <PERSON><PERSON> %s gëtt gestart", "commands.test.clear.error.no_tests": "Et goufe keng Tester fir ze läsche fonnt", "commands.test.clear.success": "%s Strukture goufe geläscht", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "<PERSON><PERSON><PERSON>, fir an den Tëschespäicher ze kopéieren", "commands.test.create.success": "Testopbau gouf fir Test %s kre<PERSON>iert", "commands.test.error.no_test_containing_pos": "Et ka keng Testinstanz fonnt ginn, déi %s, %s, %s enthält", "commands.test.error.no_test_instances": "Et goufe keng Testinstanze fonnt", "commands.test.error.non_existant_test": "Test %s konnt net fonnt ginn", "commands.test.error.structure_not_found": "Testkonstruktioun %s konnt net fonnt ginn", "commands.test.error.test_instance_not_found": "Testinstanz-Blockobjet konnt net fonnt ginn", "commands.test.error.test_instance_not_found.position": "Testinstanz-Blockobjet konnt net fir Test bei %s, %s, %s fonnt ginn", "commands.test.error.too_large": "D'Konstruktioun dierf an all Richtung héchstens %s <PERSON><PERSON><PERSON> grouss sinn", "commands.test.locate.done": "<PERSON><PERSON> ofgeschloss, %s Konstruktion(e) fonnt", "commands.test.locate.found": "Konstruktioun bei %s fonnt (Distanz: %s)", "commands.test.locate.started": "Testkonstruktioun(e) gi lokaliséiert, des kéint e bëssen daueren ...", "commands.test.no_tests": "<PERSON><PERSON> zum Ausféiere besteeënd", "commands.test.relative_position": "Positioun relativ zu %s: %s", "commands.test.reset.error.no_tests": "Et goufe keng Tester zum Zerécksetze fonnt", "commands.test.reset.success": "%s Konstruktioun(e) gouf(en) zeréckgesat", "commands.test.run.no_tests": "Keng <PERSON> fonnt", "commands.test.run.running": "%s Test(er) gëtt/ginn ausgeféiert ...", "commands.test.summary": "Spilliwwerpréifung ofgeschloss! %s Test(er) ass/goufen ausgeféiert", "commands.test.summary.all_required_passed": "All erfuerderlech Tester waren erfollegräich :)", "commands.test.summary.failed": "Noutwendeg Test(er) sinn fehlgeschloen :(", "commands.test.summary.optional_failed": "Optional Test(er) sinn fehlgeschloen", "commands.tick.query.percentiles": "Perzentiller: P50: %s ms, P95: %s ms, P99: %s ms; Stéchprouf: %s", "commands.tick.query.rate.running": "Soll‐Tickrate: %s pro Sekonn.\nDuerchschnëttlech Zäit pro Tick: %s ms (Soll: %s ms)", "commands.tick.query.rate.sprinting": "Soll‐Tickrate: %s pro Sekonn (ignoréiert, nëmmen als Referenz).\nDuerchschnëttlech Zäit pro Tick: %s ms", "commands.tick.rate.success": "Zoufälleg Tick Rate gouf op %s pro Sekonn gesat", "commands.tick.sprint.report": "Beschleunegung gouf mat %s Ticks pro Sekonn respektiv %s ms pro Tick ofgeschloss", "commands.tick.sprint.stop.fail": "Et fënnt aktuell keen Tick-Fortschreiden statt", "commands.tick.sprint.stop.success": "Tick-Beschleunegung gouf ofgebrach", "commands.tick.status.frozen": "<PERSON><PERSON><PERSON><PERSON><PERSON> ass agefruer", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON><PERSON><PERSON> leeft mee et kann net mat der Tick Rate mathalen", "commands.tick.status.running": "<PERSON>'<PERSON><PERSON><PERSON> leeft normal", "commands.tick.status.sprinting": "<PERSON><PERSON><PERSON><PERSON><PERSON> leeft besch<PERSON>gt", "commands.tick.step.fail": "Spill konnt net fortgeschratt ginn – et muss als éischt agefruer ginn", "commands.tick.step.stop.fail": "Et fënnt aktuell keen Tick-Fortschreiden statt", "commands.tick.step.stop.success": "Tick-Fortschreiden gouf ofgebrach", "commands.tick.step.success": "Et gëtt em %s Tick(s) fortgeschratt", "commands.time.query": "D'aktuell Zäit ass %s", "commands.time.set": "Zäit op %s gesat", "commands.title.cleared.multiple": "All Titele goufen %s Spillern ewechgeholl", "commands.title.cleared.single": "All Titele goufen dem %s ewechgeholl", "commands.title.reset.multiple": "Titeloptioune goufe fir %s Spiller zeréckgesat", "commands.title.reset.single": "Titeloptioune goufe fir den %s zeréckgesat", "commands.title.show.actionbar.multiple": "Neien Actionbartitel gëtt fir %s Spiller ugewisen", "commands.title.show.actionbar.single": "Neien Actionbartitel gëtt fir den %s ugewisen", "commands.title.show.subtitle.multiple": "Néien Ënnertitel gëtt fir %s Spiller ugewisen", "commands.title.show.subtitle.single": "Néien Ënnertitel gëtt fir den %s ugewisen", "commands.title.show.title.multiple": "Néien Titel gëtt fir %s Spiller ugewisen", "commands.title.show.title.single": "Néien T<PERSON>l gëtt fir den %s ugewisen", "commands.title.times.multiple": "Titel-Displayzäite goufe fir %s Spiller geännert", "commands.title.times.single": "Titel-Displayzäite goufe fir den %s geännert", "commands.transfer.error.no_players": "Et muss mindestens ee Spiller zum iwwerdroen ugi ginn", "commands.transfer.success.multiple": "%s Spiller ginn u(n) %s:%s iwwerdroen", "commands.transfer.success.single": "%s gëtt u(n) %s:%s iwwerdroen", "commands.trigger.add.success": "%s gouf ausgeléist (Wäert gouf ëm %s erhéicht)", "commands.trigger.failed.invalid": "<PERSON><PERSON> ass <PERSON> (trigger-<PERSON><PERSON>)", "commands.trigger.failed.unprimed": "Ausléiser nach net aktivéiert", "commands.trigger.set.success": "%s gouf ausgeléist (<PERSON><PERSON><PERSON> gouf op %s gesat)", "commands.trigger.simple.success": "%s gouf ausgeléist", "commands.version.build_time": "Bauezäit =", "commands.version.data": "Daten =", "commands.version.header": "Server Versioun Informatioun:", "commands.version.id": "ID =", "commands.version.name": "Numm =", "commands.version.pack.data": "Packdaten =", "commands.version.pack.resource": "Packressource =", "commands.version.protocol": "Protokoll = ()", "commands.version.series": "Serie =", "commands.version.stable.no": "Stabil = Nee", "commands.version.stable.yes": "Stabil = Jo", "commands.waypoint.list.empty": "<PERSON><PERSON> an", "commands.waypoint.list.success": "Weepräis(s) an :", "commands.waypoint.modify.color": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ass elo", "commands.waypoint.modify.color.reset": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.waypoint.modify.style": "Weepräisstil geännert", "commands.weather.set.clear": "<PERSON>ieder gouf op Kloer g<PERSON>ännert", "commands.weather.set.rain": "<PERSON>ieder gouf op Reen geännert", "commands.weather.set.thunder": "Wieder gouf op Donnerwieder geännert", "commands.whitelist.add.failed": "<PERSON>piller ass schonn op der Whitelist", "commands.whitelist.add.success": "%s gouf zur Whitelist dobäi gemaach", "commands.whitelist.alreadyOff": "D'Whitelist ass schonn <PERSON>", "commands.whitelist.alreadyOn": "D'Whitelist ass schonn aktivé<PERSON>t", "commands.whitelist.disabled": "D'Whitelist ass elo desaktiv<PERSON><PERSON>t", "commands.whitelist.enabled": "D'Whitelist ass elo aktiv<PERSON>iert", "commands.whitelist.list": "Et gëtt %s Spiller op der Whitelist: %s", "commands.whitelist.none": "Et si keng Spi<PERSON> op der Whitelist", "commands.whitelist.reloaded": "Whitelist gouf nei gel<PERSON>en", "commands.whitelist.remove.failed": "Spiller ass net op der Whitelist", "commands.whitelist.remove.success": "%s gouf vun der Whitelist ewech geholl", "commands.worldborder.center.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, de Welterand huet schonn dësen <PERSON>um", "commands.worldborder.center.success": "Den Zentrum vum Welterand gouf op (%s, %s) gesat", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>t, dee vum Welterand verursaachte Sc<PERSON> huet schonn dëse <PERSON>", "commands.worldborder.damage.amount.success": "<PERSON> vum Welterand gouf op %s pro Block pro Sekonn gesat", "commands.worldborder.damage.buffer.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, d'Pufferzone vum Schued vum Welterand huet schonn dë<PERSON>", "commands.worldborder.damage.buffer.success": "D'Pufferzone vum Schued vum Welterand gouf op %s Bléck (gesat)", "commands.worldborder.get": "De Welterand ass aktuell %s Bléck(s) breet", "commands.worldborder.set.failed.big": "Weltgrenz kann net méi grouss wéi %s Blöcke breet sinn", "commands.worldborder.set.failed.far": "Weltgrenz kann net méi wéi %s Bléck grouss sinn", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON><PERSON><PERSON> huet sech ge<PERSON>, de Welter<PERSON> huet schonn dë<PERSON>", "commands.worldborder.set.failed.small": "D'Breet vum Welterand däerf net méi kleng wei ee Block sinn", "commands.worldborder.set.grow": "De Welterand gëtt ënnerhalb vun %2$s Sekonnen op eng Breet vun %1$s Bléck vergréissert", "commands.worldborder.set.immediate": "Setzt d'Weltgrenz op %s Block(en) breet", "commands.worldborder.set.shrink": "De Welterand gëtt ënnerhalb vun %2$s Sekonnen op eng Breet vun %1$s Bléck verklengert)", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>, de Warnberäich vum Welterand huet schonn dë<PERSON>", "commands.worldborder.warning.distance.success": "De Warnberäich vum Welterand gouf op %s Bléck gesat)", "commands.worldborder.warning.time.failed": "<PERSON><PERSON><PERSON><PERSON> huet sech geä<PERSON>t, d'Warnzäit vum Welterand huet schonn dëse <PERSON>", "commands.worldborder.warning.time.success": "D'Warnzäit vum Welterand gouf op %s Sekonne gesat)", "compliance.playtime.greaterThan24Hours": "Du spills scho méi wéi 24 Stonnen", "compliance.playtime.hours": "Du spills schon fir %s Stonn(en)", "compliance.playtime.message": "Exzessiv Spillowend kann den normale Alldag stéieren", "connect.aborted": "Ofgebrach", "connect.authorizing": "Aloggen ...", "connect.connecting": "Mam Server verbannen ...", "connect.encrypting": "Verschlësselen ...", "connect.failed": "Mam Server verbanne <PERSON>", "connect.failed.transfer": "D'Verbindung ass gescheitert beim Transfert op de Server", "connect.joining": "Welt gëtt betrueden …", "connect.negotiating": "Au<PERSON>delen …", "connect.reconfiging": "<PERSON><PERSON>i kon<PERSON>gu<PERSON> …", "connect.reconfiguring": "<PERSON><PERSON>i kon<PERSON>gu<PERSON> …", "connect.transferring": "Iwwerdro op néie Server …", "container.barrel": "Faass", "container.beacon": "Beacon", "container.beehive.bees": "Beien: %s/%s", "container.beehive.honey": "Hunneg: %s/%s", "container.blast_furnace": "Schmelzuewen", "container.brewing": "Braustand", "container.cartography_table": "Kart<PERSON><PERSON>", "container.chest": "<PERSON><PERSON><PERSON><PERSON>", "container.chestDouble": "<PERSON><PERSON><PERSON>", "container.crafter": "<PERSON><PERSON><PERSON>", "container.crafting": "Handwierk", "container.creative": "Géigestänn auswielen", "container.dispenser": "<PERSON><PERSON><PERSON>", "container.dropper": "<PERSON>pender", "container.enchant": "Verzauberen", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Lapislazuli", "container.enchant.lapis.one": "1 Lapislazuli", "container.enchant.level.many": "%s Erfarungslevel", "container.enchant.level.one": "1 Erfarungsstuf", "container.enchant.level.requirement": "Néideg Erfarungsstufen: %s", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.furnace": "<PERSON><PERSON><PERSON>", "container.grindstone_title": "Reparéieren an entzauberen", "container.hopper": "Triichter", "container.inventory": "Inventar", "container.isLocked": "%s ass gespaart!", "container.lectern": "Liespult", "container.loom": "Loom", "container.repair": "Flécken a benennen", "container.repair.cost": "Erfarungskäschten: %1$s", "container.repair.expensive": "Ze deier!", "container.shulkerBox": "Shulker-Këscht", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "an %s weider ...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "Kann net opgemaach ginn. Inhalt nach net generéiert.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Ausrüstung opwäerten", "container.upgrade.error_tooltip": "Géigestand kann sou net opgewäert ginn", "container.upgrade.missing_template_tooltip": "Schm<PERSON>ds<PERSON><PERSON><PERSON> bäimaachen", "controls.keybinds": "Tastebeleeung …", "controls.keybinds.duplicateKeybinds": "<PERSON><PERSON><PERSON> gëtt och benotzt fir: %s", "controls.keybinds.title": "Tastebeleeung", "controls.reset": "Standard", "controls.resetAll": "Tasten zerécks<PERSON>zen", "controls.title": "Steierung", "createWorld.customize.buffet.biome": "W.e.g. wiel een Biom", "createWorld.customize.buffet.title": "Upassung Buffet-Welt", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Buedem - %s", "createWorld.customize.flat.layer.top": "Uewerfläch - %s", "createWorld.customize.flat.removeLayer": "Schicht läschen", "createWorld.customize.flat.tile": "Schichtmaterial", "createWorld.customize.flat.title": "Flaachlandupassung", "createWorld.customize.presets": "Virlagen", "createWorld.customize.presets.list": "Alternativ, hei e puer déi mir erstallt hunn!", "createWorld.customize.presets.select": "<PERSON><PERSON><PERSON>", "createWorld.customize.presets.share": "Wëlls du deng Virlag deelen? Benotz d' Këscht ënnendrënner!", "createWorld.customize.presets.title": "Wiel eng Virlag", "createWorld.preparing": "Welterschafung gëtt preparéiert ...", "createWorld.tab.game.title": "Spill", "createWorld.tab.more.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.world.title": "Welt", "credits_and_attribution.button.attribution": "Unerkennung", "credits_and_attribution.button.credits": "Matwierkend", "credits_and_attribution.button.licenses": "<PERSON><PERSON><PERSON>", "credits_and_attribution.screen.title": "Matwierkend an Unerkennung", "dataPack.bundle.description": "Aktivéiert d'experimentell Bëndel", "dataPack.bundle.name": "Bëndel", "dataPack.locator_bar.description": "<PERSON><PERSON> <PERSON><PERSON>Richtung vun anere Spiller am Multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Verbessert Bewegung vu Buggien", "dataPack.minecart_improvements.name": "Buggiverbesserungen", "dataPack.redstone_experiments.description": "Experimentell Redstone-Ännerungen", "dataPack.redstone_experiments.name": "Redstone-Experimenter", "dataPack.title": "<PERSON><PERSON><PERSON><PERSON> auswielen", "dataPack.trade_rebalance.description": "Iwwerarbecht Offere fir Awunner", "dataPack.trade_rebalance.name": "Néigewiichtung vum Awunnerhandel", "dataPack.update_1_20.description": "<PERSON><PERSON>er an Inhalter fir Minecraft 1.20", "dataPack.update_1_20.name": "Aktualiséierung 1.20", "dataPack.update_1_21.description": "<PERSON><PERSON> an Inhalt fir Minecraft 1.21", "dataPack.update_1_21.name": "Aktualiséierung 1.21", "dataPack.validation.back": "<PERSON><PERSON><PERSON>", "dataPack.validation.failed": "Validatioun vun den Datepäck ass feelgeschloen!", "dataPack.validation.reset": "Op Standard zrécksetzen", "dataPack.validation.working": "Ausgewielt Datepäck gi validéiert ...", "dataPack.vanilla.description": "D'Standarddaten fir Minecraft", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "<PERSON><PERSON> an Inhalter fir de Wanter-Drop", "dataPack.winter_drop.name": "Wanter-Drop", "datapackFailure.safeMode": "Ofgesecherte Modus", "datapackFailure.safeMode.failed.description": "<PERSON>ës Welt huet ongëlteg oder beschiedegt Spilldaten.", "datapackFailure.safeMode.failed.title": "Luede vun der Welt am ofgesécherten Modus feelgeschloen.", "datapackFailure.title": "Feeler an de momentan ausgewielten Datepäck hunn d'Luede vun der Welt verhënnert.\nDu kanns entweder probéieren nëmmen de Vanilla <PERSON> (\"ofgesécherte Modus\") ze lueden oder z<PERSON>ck zum Haaptmenu goen an et manuell korrigéieren.", "death.attack.anvil": "%1$s ass vun engem Amboss zerquetscht ginn", "death.attack.anvil.player": "%1$s gouf während dem Kampf mam %2$s vun engem Amboss zerquëtscht", "death.attack.arrow": "%2$s huet den %1$s erschoss", "death.attack.arrow.item": "%2$s huet den %1$s mat %3$s erschoss", "death.attack.badRespawnPoint.link": "Geplangte Spilldesign", "death.attack.badRespawnPoint.message": "%1$s gouf vun %2$s ëmbruecht", "death.attack.cactus": "%1$s ass ze Dout gepickt ginn", "death.attack.cactus.player": "%1$s ass beim V<PERSON> dem %2$s ze entwëschen an ee Kaktus gerannt", "death.attack.cramming": "%1$s gouf zerquëtscht", "death.attack.cramming.player": "%1$s gouf vun %2$s zerquëtscht", "death.attack.dragonBreath": "%1$s gouf am Draachenotem geréischtert", "death.attack.dragonBreath.player": "%1$s gouf duerch %2$s am Draachenotem geréischtert", "death.attack.drown": "%1$s ass ersoff", "death.attack.drown.player": "%1$s wollt %2$s entwëschen an ass dobäi erdronk", "death.attack.dryout": "%1$s sont morts de déshydratation", "death.attack.dryout.player": "%1$s est mort de déshydratation en essayant d'échapper à %2$s", "death.attack.even_more_magic": "%1$s gouf duerch verstäerkte Magie ëmbruecht", "death.attack.explosion": "%1$s ass explodéiert", "death.attack.explosion.player": "%2$s huet den %1$s an d'Loft gesprengt", "death.attack.explosion.player.item": "%1$s gouf vum %2$s mat %3$s an d'Loft gesprengt", "death.attack.fall": "%1$s ass ze fest um Buedem opgeschloen", "death.attack.fall.player": "%1$s ass beim Versuch dem %2$s ze entkommen der Schwéierkraaft zum Opfer gefall", "death.attack.fallingBlock": "%1$s gouf vun engen Block zerquetscht", "death.attack.fallingBlock.player": "%1$s gouf während dem Kampf mam %2$s vun engem Block zerquëtscht", "death.attack.fallingStalactite": "%1$s gouf vun engem eroffalendem Stalaktit opgespiisst", "death.attack.fallingStalactite.player": "%1$s gouf wärend dem Kampf mat %2$s vun engem eroffalendem Stalaktit opgespiisst", "death.attack.fireball": "%2$s huet den %1$s flambéiert", "death.attack.fireball.item": "%2$s huet den %1$s mat %3$s flambéiert", "death.attack.fireworks": "%1$s ass mat groussem Knall explodéiert", "death.attack.fireworks.item": "%1$s ass duerch e<PERSON> Freedefeier, dat vum %2$s mat %3$s of<PERSON>cho<PERSON> gouf, mat engem <PERSON> an d'Loft gaangen", "death.attack.fireworks.player": "%1$s ass während dem Kampf mam %2$s mat engem haarde Knall duerch d'Loft geflunn", "death.attack.flyIntoWall": "%1$s huet kinetesch Energie erfuer", "death.attack.flyIntoWall.player": "%1$s huet beim Versuch dem %2$s ze entkomme kinetesch Energie ofkrut", "death.attack.freeze": "%1$s ass erfruer", "death.attack.freeze.player": "%1$s ass duerch %2$s erfruer", "death.attack.generic": "%1$s ass gestuerwen", "death.attack.generic.player": "%1$s ass wéinst %2$s gestuerwen", "death.attack.genericKill": "%1$s gouf ëmbruecht", "death.attack.genericKill.player": "%1$s  gouf am Kampf géint %2$s ëmbruecht", "death.attack.hotFloor": "%1$s huet erausfonnt, dass de Buedem Lava ass", "death.attack.hotFloor.player": "%1$s gouf vun %2$s an e Geforeberäich gedriwwen", "death.attack.inFire": "%1$s ass a Flamen opgaangen", "death.attack.inFire.player": "%1$s ass während hie géint %2$s gekämpft huet an d'Feier gelaf", "death.attack.inWall": "%1$s ass vun enger Mauer erdréckt ginn", "death.attack.inWall.player": "%1$s gouf während dem Kampf mam %2$s lieweg begruewen", "death.attack.indirectMagic": "%2$s huet den %1$s mat Zauberei ëmbruecht", "death.attack.indirectMagic.item": "%2$s huet den %1$s mat %3$s ëmbruecht", "death.attack.lava": "%1$s wollt an der Lava schwammen", "death.attack.lava.player": "%1$s huet an der Lava probéiert %2$s ze entkommen", "death.attack.lightningBolt": "%1$s gouf vum Blëtz getraff", "death.attack.lightningBolt.player": "%1$s gouf während dem Kampf mam %2$s vum Blëtz getraff", "death.attack.mace_smash": "%1$s gouf vu(n) %2$s zersch<PERSON>ert", "death.attack.mace_smash.item": "%1$s gouf vu(n) %2$s mat %3$s zersch<PERSON>ert", "death.attack.magic": "%1$s gouf duerch Zauberei ëmbruecht", "death.attack.magic.player": "%1$s ass beim V<PERSON>uch dem %2$s ze entwëschen duerch Magie ëmbruecht ginn", "death.attack.message_too_long": "Leider ass d'Meldung ze laang fir ugewisen ze ginn. Hei ass eng ofgekierzte Versioun: %s", "death.attack.mob": "%2$s huet den %1$s erschloen", "death.attack.mob.item": "%2$s huet den %1$s mat %3$s erschloen", "death.attack.onFire": "%1$s ass verbrannt", "death.attack.onFire.item": "%1$s gouf zu engem knusprech verbrannt wärend dem %2$s Kampf mat %3$s", "death.attack.onFire.player": "%1$s ass während dem Kampf géint %2$s verbrannt", "death.attack.outOfWorld": "%1$s ass aus der Welt gefall", "death.attack.outOfWorld.player": "%1$s wollt net méi an der selwechter Welt wei %2$s liewen", "death.attack.outsideBorder": "%1$s huet d'Grenze vun dëser Welt verlooss", "death.attack.outsideBorder.player": "%1$s huet d'Grenze vun dëser Welt beim <PERSON> géint %2$s verlooss", "death.attack.player": "%1$s gouf vun %2$s ëmbruecht", "death.attack.player.item": "%1$s gouf vun %2$s mat %3$s ëmbruecht", "death.attack.sonic_boom": "%1$s gouf vun engem sonesch gelueden Gejäiz geläscht", "death.attack.sonic_boom.item": "%1$s gouf geläscht vun engem sonesch gelueden Gejäiz beim Versuch, %2$s ze flüchten an %3$s", "death.attack.sonic_boom.player": "%1$s gouf vun engem sonesch gelueden Gejäiz geläscht wärend de probéiert huet %2$s ze flüchten", "death.attack.stalagmite": "%1$s ass vun enger Stalagmite opgespiist ginn", "death.attack.stalagmite.player": "%1$s gouf wärend dem Kampf mat %2$s vun engem Stalagmite opgepoult", "death.attack.starve": "%1$s ass verhongert", "death.attack.starve.player": "%1$s ass während dem Kampf mam %2$s verhongert", "death.attack.sting": "%1$s gouf dout gestach", "death.attack.sting.item": "%1$s gouf vum %2$s mat %3$s ëmbruecht", "death.attack.sting.player": "%1$s gouf vun %2$s dout gestach", "death.attack.sweetBerryBush": "%1$s gouf duerch ee Séissbierestrauch dout gestach", "death.attack.sweetBerryBush.player": "%1$s gouf beim <PERSON> %2$s ze entkommen, duerch ee Séissbierestrauch dout gestach", "death.attack.thorns": "%1$s ass be<PERSON>, %2$s ze verletzen, ë<PERSON><PERSON><PERSON> ginn", "death.attack.thorns.item": "%1$s ass beim <PERSON> den %2$s ze verletze vum %3$s ëmbruecht ginn", "death.attack.thrown": "%1$s ass vum %2$s ze Dout geschloe ginn", "death.attack.thrown.item": "%1$s ass vum %2$s mat %3$s doutgeschloe ginn", "death.attack.trident": "%1$s gouf vum %2$s opgespiisst", "death.attack.trident.item": "%1$s gouf vum %2$s mat %3$s opgespiisst", "death.attack.wither": "%1$s ass verduerwen", "death.attack.wither.player": "%1$s ass während dem Kampf mam %2$s verduerwen", "death.attack.witherSkull": "%1$s gouf vum %2$s mat enger <PERSON><PERSON><PERSON><PERSON> erschoss", "death.attack.witherSkull.item": "%1$s gouf vun engem Schädel vun %2$s mat %3$s erschoss", "death.fell.accident.generic": "%1$s ass vun ze héich erofgefall", "death.fell.accident.ladder": "%1$s ass vun der Leeder getrollt", "death.fell.accident.other_climbable": "%1$s ass beim <PERSON> gefall", "death.fell.accident.scaffolding": "%1$s ass vun engem Gerëscht erofgefall", "death.fell.accident.twisting_vines": "%1$s ass vun enger W<PERSON><PERSON><PERSON>ane erofgefall", "death.fell.accident.vines": "%1$s ass vun enger <PERSON>ne erofgetrollt", "death.fell.accident.weeping_vines": "%1$s ass vun enger Trauerliane erofgefall", "death.fell.assist": "%1$s ass vum %2$s zum Fale condamnéiert ginn", "death.fell.assist.item": "%2$s huet %1$s mat %3$s zum Fale condamnéiert", "death.fell.finish": "%1$s ass ze déif gefall an gouf vum %2$s erleedegt", "death.fell.finish.item": "%1$s ass ze déif gefall. %2$s huet e mat %3$s ëmbruecht", "death.fell.killer": "%1$s gouf zum Fale condamnéiert", "deathScreen.quit.confirm": "<PERSON> du s<PERSON>cher, dass du d'Spill verloosse wëlls?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "<PERSON><PERSON><PERSON>", "deathScreen.score.value": "Punktestand: %s", "deathScreen.spectate": "Spectateur ginn", "deathScreen.title": "Du bass gestuerwen!", "deathScreen.title.hardcore": "Game Over!", "deathScreen.titleScreen": "Haaptmenü", "debug.advanced_tooltips.help": "F3 + H = Erwei<PERSON>t Schnellinfos", "debug.advanced_tooltips.off": "Erweidert Schnellinfos: verstoppt", "debug.advanced_tooltips.on": "Erweidert Schnellinfos: si<PERSON><PERSON><PERSON>", "debug.chunk_boundaries.help": "F3 + G = Chunkgrenzen uweisen", "debug.chunk_boundaries.off": "Chunkgrenzen: verstoppt", "debug.chunk_boundaries.on": "Chunkgrenzen: si<PERSON><PERSON><PERSON>", "debug.clear_chat.help": "F3 + D = <PERSON><PERSON> eidel maachen", "debug.copy_location.help": "F3 + C = Positioun als /tp-Befeel kopéieren oder gedré<PERSON>t halen, fir een Ofstuerz ze erzwéngen", "debug.copy_location.message": "Positioun gouf an den Tëschespäicher kopéieren", "debug.crash.message": "F3 + C si ged<PERSON><PERSON>t. <PERSON>t wä<PERSON> d'<PERSON><PERSON> ofst<PERSON><PERSON> lo<PERSON>en, wann net lassgelooss g<PERSON>tt.", "debug.crash.warning": "Ofstuerz an %s ...", "debug.creative_spectator.error": "Spillmodus konnt net geännert ginn; keng Berechtegung", "debug.creative_spectator.help": "F3 + N = Tëschent leschtem Spillmodus an Zuschauermodus wiesselen", "debug.dump_dynamic_textures": "Dynamesch Texturen an %s gespäichert", "debug.dump_dynamic_textures.help": "F3 + S = Dynamesch Texturen ausginn", "debug.gamemodes.error": "Spillmodusauswiel konnt net opgemaach ginn; keng Berechtegung", "debug.gamemodes.help": "F3 + F4 = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Nächsten", "debug.help.help": "F3 + Q = <PERSON><PERSON><PERSON>", "debug.help.message": "Tastaturlayout:", "debug.inspect.client.block": "Clientsäiteg Blockdate goufen an den Tëschespäicher kopéiert", "debug.inspect.client.entity": "Clientsäiteg Objetdate goufen an den Tëschespäicher kopéiert", "debug.inspect.help": "F3 + I = Block- oder Objetdaten an de Tëschespäicher kopéieren", "debug.inspect.server.block": "Serversäiteg Blockdate goufen an den Tëschespäicher kopéiert", "debug.inspect.server.entity": "Serversäiteg Objetdate goufen an den Tëschespäicher kopéiert", "debug.pause.help": "F3 + Esc = <PERSON><PERSON>ll ouni Pa<PERSON> unhalen (falls méiglech)", "debug.pause_focus.help": "F3 + P = Pause bei Fokusverloscht", "debug.pause_focus.off": "Pause bei Fokusverloscht: desaktivéiert", "debug.pause_focus.on": "Pause bei Fokusverloscht: aktivéiert", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Profiler starten / ophalen", "debug.profiling.start": "Profiléiere huet fir %s Sekonne ugefaang. Benotzt F3 + L fir fréi ze stoppen", "debug.profiling.stop": "Profiling opgehalen. Gespäichert Resultater op %s", "debug.reload_chunks.help": "F3 + A = Chunks néi lueden", "debug.reload_chunks.message": "D'Chunks ginn all néi gelueden", "debug.reload_resourcepacks.help": "F3 + T = Ressourcepäck néi lueden", "debug.reload_resourcepacks.message": "Ressourcepäck néi gel<PERSON>en", "debug.show_hitboxes.help": "F3 + B = Hitboxen uweisen", "debug.show_hitboxes.off": "Hitboxen: verstoppt", "debug.show_hitboxes.on": "Hitboxen: si<PERSON><PERSON><PERSON>", "debug.version.header": "Client Versioun Informatioun:", "debug.version.help": "F3 + V = Client Versioun Informatioun", "demo.day.1": "Dës Demo dauert 5 Spilldeeg. Maach dat bescht draus!", "demo.day.2": "<PERSON><PERSON>", "demo.day.3": "<PERSON><PERSON>", "demo.day.4": "<PERSON><PERSON>", "demo.day.5": "Dat ass däi leschten <PERSON>g!", "demo.day.6": "De fënnefte Spilldag ass eriwwer. Dréck %s fir ee Screenshot vun denger Kreatioun ze maachen.", "demo.day.warning": "Deng <PERSON> ass bal ëm!", "demo.demoExpired": "D'Demozäit ass ofgelaf!", "demo.help.buy": "Elo kafen!", "demo.help.fullWrapped": "Dës Demo dauert 5 Spilldeeg (ca. 1 Stonn a 40 Minutten an Echtzäit). <PERSON><PERSON> dir d'Fortschrëtter u fir Hiweiser! Vill Spaass!", "demo.help.inventory": "Benotz %1$s fir däin Inventar opzemaachen", "demo.help.jump": "Fir ze sprangen, dréck %1$s", "demo.help.later": "Weiderspillen!", "demo.help.movement": "Benotz %1$s, %2$s, %3$s, %4$s an deng Maus fir ronderëm ze goen", "demo.help.movementMouse": "<PERSON><PERSON> mat der Ma<PERSON> ronderë<PERSON>", "demo.help.movementShort": "Trëppel mat %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft Demo-Modus", "demo.remainingTime": "Zäit iwwereg: %s", "demo.reminder": "<PERSON><PERSON><PERSON><PERSON> ass ofgelaf. <PERSON><PERSON>'<PERSON>ll fir weiderzemaachen oder start eng nei Welt!", "difficulty.lock.question": "<PERSON><PERSON> <PERSON>, d'Schwieregkeet vun dëser Welt ze fixéieren? Dat wärt d'Welt fir ëmmer op \"%1$s\" sëtzen an du kanns déi Astellung net méi änneren.", "difficulty.lock.title": "Weltschwieregkeet fixéieren", "disconnect.endOfStream": "<PERSON><PERSON><PERSON> vum Stream", "disconnect.exceeded_packet_rate": "Wéinst Iwwerschreidung vum Debitlimite vun de Päck erausgeworf", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Statusufro gouf net beäntwert", "disconnect.loginFailedInfo": "Alogge feelgeschloen: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Multispiller ass desaktivéiert. Iwwerpréif w.e.g d'Astellunge vun dengem Microsoft Konto.", "disconnect.loginFailedInfo.invalidSession": "Ong<PERSON><PERSON><PERSON> (Prob<PERSON><PERSON>, d'Spill an de Launcher néi ze starten)", "disconnect.loginFailedInfo.serversUnavailable": "D‘Authentifikatiounsserveren sinn a moment net erreechbar. Probéiert nach emol.", "disconnect.loginFailedInfo.userBanned": "Ierch ass et verbueden online ze spillen", "disconnect.lost": "Verbindung ënnerbrach", "disconnect.packetError": "Feeler am Netzwierkprotokoll", "disconnect.spam": "Erausgeworf wéinst Spam", "disconnect.timeout": "Zäit iwwerschratt", "disconnect.transfer": "Op en anere Server iwwerdroen", "disconnect.unknownHost": "Onbekannten Host", "download.pack.failed": "%s vu(n) %s Pak(e) konnten net erofgeluede ginn", "download.pack.progress.bytes": "Fortschrëtt: %s (Gesamtgréisst onbekannt)", "download.pack.progress.percent": "Fortschritt: %s %%", "download.pack.title": "Ressourcepak %s&%s gëtt erofgelueden", "editGamerule.default": "Standard: %s", "editGamerule.title": "Spillreegelen änneren", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorbtioun", "effect.minecraft.bad_omen": "Schlecht Omen", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Miereskraaft", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Gonscht vum Delfin", "effect.minecraft.fire_resistance": "Feierresistenz", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON>", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "Extra Liewen", "effect.minecraft.hero_of_the_village": "Held vum Duerf", "effect.minecraft.hunger": "<PERSON><PERSON>", "effect.minecraft.infested": "Befal", "effect.minecraft.instant_damage": "Direktschued", "effect.minecraft.instant_health": "Direktheelung", "effect.minecraft.invisibility": "Onsiichtbarkeet", "effect.minecraft.jump_boost": "Sprongkraaft", "effect.minecraft.levitation": "Schwiewen", "effect.minecraft.luck": "<PERSON><PERSON><PERSON>", "effect.minecraft.mining_fatigue": "Ofbau Middegkeet", "effect.minecraft.nausea": "Iwwelzegkeet", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.oozing": "Schläimen", "effect.minecraft.poison": "Gëft", "effect.minecraft.raid_omen": "Droenden Iwwerfall", "effect.minecraft.regeneration": "Regeneratioun", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "Siedegung", "effect.minecraft.slow_falling": "<PERSON><PERSON>", "effect.minecraft.slowness": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.speed": "Geschwindegkeet", "effect.minecraft.strength": "<PERSON><PERSON><PERSON>", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON>", "effect.minecraft.unluck": "Pech", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.weakness": "Schwächt", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.wither": "Ausdierung", "effect.none": "Ouni Effekt", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "Waasseraffinitéit", "enchantment.minecraft.bane_of_arthropods": "Arthropodeje<PERSON>er", "enchantment.minecraft.binding_curse": "<PERSON><PERSON><PERSON> vun der Bindung", "enchantment.minecraft.blast_protection": "Explosiounschutz", "enchantment.minecraft.breach": "Duerchbroch", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "Efficacitéit", "enchantment.minecraft.feather_falling": "Fiederfall", "enchantment.minecraft.fire_aspect": "Verbrennung", "enchantment.minecraft.fire_protection": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.flame": "Flam", "enchantment.minecraft.fortune": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Onendlechkeet", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Plëmmen", "enchantment.minecraft.loyalty": "Loyalitéit", "enchantment.minecraft.luck_of_the_sea": "<PERSON><PERSON><PERSON>g<PERSON><PERSON>", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "Reparatur", "enchantment.minecraft.multishot": "Multischoss", "enchantment.minecraft.piercing": "Duerchschoss", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.respiration": "Ootmung", "enchantment.minecraft.riptide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sharpness": "Schäerft", "enchantment.minecraft.silk_touch": "Precautioun", "enchantment.minecraft.smite": "Péngeg", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping": "Schwongkraaft", "enchantment.minecraft.sweeping_edge": "Schwongkraaft", "enchantment.minecraft.swift_sneak": "Huschen", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Fluch vum Verschwannen", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.acacia_boat": "Akazieholz<PERSON>t", "entity.minecraft.acacia_chest_boat": "Aka<PERSON><PERSON><PERSON><PERSON><PERSON> mat <PERSON>", "entity.minecraft.allay": "Hëllefsgeescht", "entity.minecraft.area_effect_cloud": "Partikelwollek", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Rüstungsstänner", "entity.minecraft.arrow": "<PERSON><PERSON>", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bambusflooss mat <PERSON>", "entity.minecraft.bamboo_raft": "Bambusflooss", "entity.minecraft.bat": "Flantermaus", "entity.minecraft.bee": "Bei", "entity.minecraft.birch_boat": "Bierkenholzboot", "entity.minecraft.birch_chest_boat": "Bierkenholzboot mat <PERSON>", "entity.minecraft.blaze": "<PERSON>", "entity.minecraft.block_display": "Blockrepresentatioun", "entity.minecraft.boat": "Boot", "entity.minecraft.bogged": "Sumpfskelett", "entity.minecraft.breeze": "<PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.camel": "<PERSON><PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Höhlespann", "entity.minecraft.cherry_boat": "Kiischtenholzboot", "entity.minecraft.cherry_chest_boat": "Kischtenholzboot mat <PERSON>", "entity.minecraft.chest_boat": "<PERSON>ot <PERSON>", "entity.minecraft.chest_minecart": "Gidderbuggi", "entity.minecraft.chicken": "Hong", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Befeelsblockbuggi", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Knarzenden", "entity.minecraft.creaking_transient": "Knarzenden", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>holzboot", "entity.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mat <PERSON>", "entity.minecraft.dolphin": "Delfin", "entity.minecraft.donkey": "<PERSON><PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Dr<PERSON><PERSON> Fe<PERSON>", "entity.minecraft.drowned": "Erdronkenen", "entity.minecraft.egg": "Geworfen Ee", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "Geworfen Enderpärel", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Magier", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.experience_bottle": "Geworfen Erfarungsfläsch", "entity.minecraft.experience_orb": "Erfarungskugel", "entity.minecraft.eye_of_ender": "<PERSON><PERSON> v<PERSON>", "entity.minecraft.falling_block": "Falende Block", "entity.minecraft.falling_block_type": "Faalende(n) %s", "entity.minecraft.fireball": "Feierball", "entity.minecraft.firework_rocket": "Feierwierk Rakéit", "entity.minecraft.fishing_bobber": "Schwëmmer", "entity.minecraft.fox": "<PERSON><PERSON>", "entity.minecraft.frog": "<PERSON><PERSON><PERSON>", "entity.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON> mat <PERSON>", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Titan", "entity.minecraft.glow_item_frame": "Liichtkader", "entity.minecraft.glow_squid": "Liichttënte<PERSON><PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON>", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Triichterbuggi", "entity.minecraft.horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.husk": "Mumienzombie", "entity.minecraft.illusioner": "Illusionist", "entity.minecraft.interaction": "Interaktiounsobjet", "entity.minecraft.iron_golem": "Eisegolem", "entity.minecraft.item": "Géigestand", "entity.minecraft.item_display": "Géigestandsduersteller", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "Tropenholzboot", "entity.minecraft.jungle_chest_boat": "Tropenholzboot mat <PERSON>", "entity.minecraft.killer_bunny": "D'Killer-<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "Léngteknuet", "entity.minecraft.lightning_bolt": "<PERSON><PERSON><PERSON>tz", "entity.minecraft.lingering_potion": "Undauernden Dronk", "entity.minecraft.llama": "<PERSON>", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON>", "entity.minecraft.magma_cube": "Magmawierfel", "entity.minecraft.mangrove_boat": "Man<PERSON><PERSON>wen<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mat <PERSON>", "entity.minecraft.marker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.oak_boat": "Eechenholzboot", "entity.minecraft.oak_chest_boat": "Eecheboot mat <PERSON>", "entity.minecraft.ocelot": "Ozelot", "entity.minecraft.ominous_item_spawner": "Onheilvolle Géigestands-Spawner", "entity.minecraft.painting": "Molerei", "entity.minecraft.pale_oak_boat": "Blatzeechenholzboot", "entity.minecraft.pale_oak_chest_boat": "Blatzeechenholz<PERSON>t mat <PERSON>", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "Schwäin", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON>", "entity.minecraft.pillager": "Plëmmert", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "Äisbier", "entity.minecraft.potion": "Dronk", "entity.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "Verwüster", "entity.minecraft.salmon": "<PERSON><PERSON><PERSON>", "entity.minecraft.sheep": "Schof", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON>er-Geschoss", "entity.minecraft.silverfish": "Sëlwerfëschelchen", "entity.minecraft.skeleton": "Skelett", "entity.minecraft.skeleton_horse": "Skelettpäerd", "entity.minecraft.slime": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.small_fireball": "Klenge Feierball", "entity.minecraft.sniffer": "Sc<PERSON>offeler", "entity.minecraft.snow_golem": "Schnéigolem", "entity.minecraft.snowball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spawner_minecart": "Spawner-<PERSON><PERSON><PERSON>", "entity.minecraft.spectral_arrow": "Spektralfeil", "entity.minecraft.spider": "<PERSON>nn", "entity.minecraft.splash_potion": "Worfdronk", "entity.minecraft.spruce_boat": "Fiichtenholzboot", "entity.minecraft.spruce_chest_boat": "<PERSON>ichtenholz<PERSON>t mat <PERSON>", "entity.minecraft.squid": "Tëntefësch", "entity.minecraft.stray": "Vagabund", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "Textrepresentatioun", "entity.minecraft.tnt": "Gezünten TNT", "entity.minecraft.tnt_minecart": "TNT-Buggi", "entity.minecraft.trader_llama": "Händlerlama", "entity.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish": "T<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON><PERSON><PERSON>flossendokter", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "Orangegesträifte Päiperleksfësch", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.13": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "Routlepsen <PERSON>ai<PERSON>f<PERSON>", "entity.minecraft.tropical_fish.predefined.16": "Nërdleche Schnapert", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Baliste", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.20": "Papageiefësch mat gielem <PERSON>", "entity.minecraft.tropical_fish.predefined.21": "Giele Seegelflossendokter", "entity.minecraft.tropical_fish.predefined.3": "Päiperleksfësch", "entity.minecraft.tropical_fish.predefined.4": "Bontpiisch", "entity.minecraft.tropical_fish.predefined.5": "Clownsfësch", "entity.minecraft.tropical_fish.predefined.6": "Zockerwatt Kampffësch", "entity.minecraft.tropical_fish.predefined.7": "Zwergepiisch", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.dasher": "Flitzer", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "Trommelfësch", "entity.minecraft.tropical_fish.type.snooper": "Sc<PERSON>offeler", "entity.minecraft.tropical_fish.type.spotty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.stripey": "Strä<PERSON>ler", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.turtle": "<PERSON><PERSON>dkr<PERSON><PERSON>", "entity.minecraft.vex": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "Panzermécher", "entity.minecraft.villager.butcher": "<PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Kartograph", "entity.minecraft.villager.cleric": "Geeschtlechen", "entity.minecraft.villager.farmer": "<PERSON>", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "Bibliothekär", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "<PERSON><PERSON>", "entity.minecraft.villager.toolsmith": "Geschirschmadd", "entity.minecraft.villager.weaponsmith": "Waffeschmadd", "entity.minecraft.vindicator": "Lakai", "entity.minecraft.wandering_trader": "<PERSON><PERSON>nh<PERSON><PERSON><PERSON>", "entity.minecraft.warden": "<PERSON><PERSON><PERSON>", "entity.minecraft.wind_charge": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.witch": "Hex", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "Witherskelett", "entity.minecraft.wither_skull": "Wither<PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombified_piglin": "Zombifizéierte <PERSON>", "entity.not_summonable": "Objet vum Typ %s konnt net erzeugt ginn", "event.minecraft.raid": "Iwwerfall", "event.minecraft.raid.defeat": "Defaite", "event.minecraft.raid.defeat.full": "Iwwerfall - Néierlag", "event.minecraft.raid.raiders_remaining": "Verbleiwend Raiber: %s", "event.minecraft.raid.victory": "<PERSON><PERSON>", "event.minecraft.raid.victory.full": "Iwwerfall - Vic<PERSON>", "filled_map.buried_treasure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.explorer_jungle": "Dschungel-Entdeckerkaart", "filled_map.explorer_swamp": "Sumpf-Entdeckerkaart", "filled_map.id": "Nr. %s", "filled_map.level": "(Stuf %s/%s)", "filled_map.locked": "<PERSON><PERSON><PERSON><PERSON>", "filled_map.mansion": "Bësch-Entdeckungskaart", "filled_map.monument": "Ozean-Entdeckungskaart", "filled_map.scale": "Moossstaf 1:%s", "filled_map.trial_chambers": "Prüfungskammerkaart", "filled_map.unknown": "<PERSON><PERSON><PERSON><PERSON>", "filled_map.village_desert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_plains": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_savanna": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_snowy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filled_map.village_taiga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou<PERSON>", "flat_world_preset.minecraft.classic_flat": "Klassescht Flaachland", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Uewerwelt", "flat_world_preset.minecraft.redstone_ready": "Bereet fir Redstone", "flat_world_preset.minecraft.snowy_kingdom": "Schnéikinnekräich", "flat_world_preset.minecraft.the_void": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON> vun all <PERSON>", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "Avent<PERSON><PERSON><PERSON>", "gameMode.changed": "Däi Spillmodus gouf zu %s geännert", "gameMode.creative": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "gameMode.hardcore": "Hardcoremodus!", "gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.survival": "Iwwerliewensmodus", "gamerule.allowFireTicksAwayFromPlayer": "<PERSON><PERSON><PERSON><PERSON> Feier wäit vun de Spiller un", "gamerule.allowFireTicksAwayFromPlayer.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ob <PERSON><PERSON> a Lava weider wéi 8 Chunk wäit vun engem Spiller weider ticken däerfen.", "gamerule.announceAdvancements": "Fortschrëtter annoncéieren", "gamerule.blockExplosionDropDecay": "Blockexplosiouns-<PERSON><PERSON> verschwannen", "gamerule.blockExplosionDropDecay.description": "E puer vun den Drëpsen vu Bléck zerstéiert duerch Explosiounen verursaacht duerch Blockinteraktiounen sinn an der Explosioun verluer.", "gamerule.category.chat": "Cha<PERSON>", "gamerule.category.drops": "Drops", "gamerule.category.misc": "Verschiddenes", "gamerule.category.mobs": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Erschafen", "gamerule.category.updates": "Weltaktualiséierungen", "gamerule.commandBlockOutput": "Befehlsblock Ausgab uweisen", "gamerule.commandModificationBlockLimit": "Blockgrenz fir Befeelsännerungen", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON> v<PERSON>, di mat engem Befeel wéi \"fill\" oder \"clone\" gläichzäiteg geännert gi kënnen.", "gamerule.disableElytraMovementCheck": "Desaktivéier Fluchkontroll vun den Elytra", "gamerule.disablePlayerMovementCheck": "Spillerbeweegungsiwwerpréifung desaktivéieren", "gamerule.disableRaids": "Iwwerfäll desaktivéieren", "gamerule.doDaylightCycle": "<PERSON><PERSON><PERSON> weiderla<PERSON> lo<PERSON>en", "gamerule.doEntityDrops": "Entitéiten-Drops", "gamerule.doEntityDrops.description": "<PERSON><PERSON><PERSON><PERSON>iert Drops vu Buggien (Inventar abegraff), <PERSON><PERSON><PERSON>, <PERSON><PERSON> asw.", "gamerule.doFireTick": "Féierausbreedung", "gamerule.doImmediateRespawn": "<PERSON><PERSON><PERSON> respawnen", "gamerule.doInsomnia": "Phantomer erschafen", "gamerule.doLimitedCrafting": "Erfuerdert Rezepter zum hierstellen", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON>, kënne Spiller nëmme scho fräigeschalt Rezepter notzen.", "gamerule.doMobLoot": "Kreaturen-Drops", "gamerule.doMobLoot.description": "Kontrollen Ressource Drëpsen aus mobs, dorënner Erfahrung Orbs.", "gamerule.doMobSpawning": "Kreaturen erschafen", "gamerule.doMobSpawning.description": "E puer Entitéite kënnen separat Regelen hunn.", "gamerule.doPatrolSpawning": "Plëmmert Patrullen erschafen", "gamerule.doTileDrops": "Block-Drops", "gamerule.doTileDrops.description": "Kont<PERSON><PERSON>iert Drops vu <PERSON>, <PERSON><PERSON><PERSON> anerem och Erfarungskugelen.", "gamerule.doTraderSpawning": "Nomadenhändler erschafen", "gamerule.doVinesSpread": "Rankenausbreedung", "gamerule.doVinesSpread.description": "Kontrolléiert ob de Vines Block zoufälleg op ugrenzend Blöcke verbreet oder net. Beaflosst net aner Aarte vu Rebeblocken wéi Weeping Vines, Twisting Vines, etc.", "gamerule.doWardenSpawning": "W<PERSON>rter erschafen", "gamerule.doWeatherCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.drowningDamage": "<PERSON><PERSON><PERSON> <PERSON>", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON> beim <PERSON>", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON>, ob vum Spiller geheiten Enderpärele ve<PERSON>, wann dëse stierft.", "gamerule.entitiesWithPassengersCanUsePortals": "Objet<PERSON> mat <PERSON>agéier kënne Portaler ben<PERSON>", "gamerule.entitiesWithPassengersCanUsePortals.description": "Erméiglecht et Objete mat <PERSON>, s<PERSON><PERSON> <PERSON>, End- an Endtransitportaler ze teleportéieren.", "gamerule.fallDamage": "<PERSON><PERSON><PERSON> <PERSON>", "gamerule.fireDamage": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "gamerule.forgiveDeadPlayers.description": "Rosen neutral Kreaturen halen op rosen ze si wann den Zielspiller an der Géigend stierft.", "gamerule.freezeDamage": "Erfréierungsschued", "gamerule.globalSoundEvents": "Global Geräischevenementer", "gamerule.globalSoundEvents.description": "<PERSON><PERSON>mmte Spillevenementer geschéien, wé<PERSON> e <PERSON> spawnt, g<PERSON><PERSON> den Toun iwwerall héieren.", "gamerule.keepInventory": "Inventar nom Doud halen", "gamerule.lavaSourceConversion": "Lava-Ern<PERSON>ierbark<PERSON>t", "gamerule.lavaSourceConversion.description": "Wa fléissend Lava op zwou Säiten vu Lavaquellen ëmginn ass, verwandelt se sech an eng Quell.", "gamerule.locatorBar": "Aktivéiert d’Spiller-Lokatorbalk", "gamerule.locatorBar.description": "<PERSON>, g<PERSON><PERSON> eng Balk op dem Bildschierm gewisen, d<PERSON><PERSON> d<PERSON> vun de <PERSON> weist.", "gamerule.logAdminCommands": "Administrateur Be<PERSON><PERSON><PERSON>en", "gamerule.maxCommandChainLength": "Iewescht Grenz fir Befeelsketten", "gamerule.maxCommandChainLength.description": "Gëllt fir Kommandoblockketten a Funktiounen.", "gamerule.maxCommandForkCount": "Limitt fir Befeelskontext", "gamerule.maxCommandForkCount.description": "Maximal Unzuel vu Befeelskontexter, déi vu Befeeler wéi „execute as“ benotzt gi kënnen.", "gamerule.maxEntityCramming": "Grenz fir d'Entitéitegedrécks", "gamerule.minecartMaxSpeed": "Maximal Buggigeschwindegkeet", "gamerule.minecartMaxSpeed.description": "Maximal standardméisseg Geschwindegkeet, mat der sech e Buggi u Land beweege kann", "gamerule.mobExplosionDropDecay": "Kreaturexplosiouns-<PERSON><PERSON> versch<PERSON>nen", "gamerule.mobExplosionDropDecay.description": "E puer vun den Drëpsen aus Blocken, déi duerch Explosiounen zerstéiert ginn, verursaacht duerch Mob sinn an der Explosioun verluer.", "gamerule.mobGriefing": "Zerstéierung duerch Kreaturen", "gamerule.naturalRegeneration": "Gesondheet regeneréieren", "gamerule.playersNetherPortalCreativeDelay": "Netherportal-Verzögerung am Kreativmodus", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON><PERSON> (a Ticks), d<PERSON>i e Spiller am Kreativmodus an engem Netherportal verbrénge muss, bevir dë<PERSON> d'<PERSON><PERSON><PERSON> w<PERSON>.", "gamerule.playersNetherPortalDefaultDelay": "Netherportal-Verzögerung", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON><PERSON> (a Ticks), d<PERSON><PERSON> e Spiller ausserhalb vum Kreativmodus an engem Netherportal verbrénge muss, bevir d<PERSON><PERSON> d'<PERSON><PERSON><PERSON><PERSON> w<PERSON>.", "gamerule.playersSleepingPercentage": "Schlofprozentsaz", "gamerule.playersSleepingPercentage.description": "De Prozentsaz vun <PERSON>, d<PERSON><PERSON><PERSON><PERSON> m<PERSON>, fir des Nuecht kënnen ze iwwersprangen.", "gamerule.projectilesCanBreakBlocks": "Blockzerstéierung duerch Geschosser", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON><PERSON>, op opprallend Geschosser Bléck zerstéieren, déi duerch si zersté<PERSON>bar wären.", "gamerule.randomTickSpeed": "Heefegkeet vun Zoufallsevenementer", "gamerule.reducedDebugInfo": "Reduzéiert Debug Info", "gamerule.reducedDebugInfo.description": "Begrenzt d'Inhalter vum Debugbildschierm.", "gamerule.sendCommandFeedback": "Befeelsausgab uweisen", "gamerule.showDeathMessages": "Doudesmeldungen uweisen", "gamerule.snowAccumulationHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON> et schneit, entsteet um Buedem eng Schnéidecken, déi héchstens dës Unzuel vu Sc<PERSON>hten erreecht.", "gamerule.spawnChunkRadius": "Spawn‐Chunk‐Radius", "gamerule.spawnChunkRadius.description": "Unzuel vun den Chunks ëm de Spawn an der Iwwerwelt, déi dauerhaft geluede bleiwen.", "gamerule.spawnRadius": "Radius vum Respawnberäich", "gamerule.spawnRadius.description": "Kontrolléiert d'Gréisst vum Gebitt ronderëm de Spawnpunkt an deem d'Spiller spawne kënnen.", "gamerule.spectatorsGenerateChunks": "<PERSON><PERSON> generé<PERSON>", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "TNT-Explosiouns-<PERSON><PERSON> verschwannen", "gamerule.tntExplosionDropDecay.description": "E puer vun den Drops aus Bléck, déi duerch TNT-Explosiounen zerstéiert ginn, gi verluer.", "gamerule.universalAnger": "Allgemeng <PERSON>", "gamerule.universalAnger.description": "Rosen neutral Kreaturen attackéiere all d'Spiller an der Géigend, net nëmmen de<PERSON>, deen se rose gemaach huet. Funktionéiert am beschte wann forgiveDeadPlayers desaktivéiert ass.", "gamerule.waterSourceConversion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.waterSourceConversion.description": "<PERSON>n fléissend Waasser op zwou Sä<PERSON> vu Waasserquellen ëmginn ass, g<PERSON><PERSON> et zu enger <PERSON>ll.", "generator.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.customized": "<PERSON><PERSON><PERSON><PERSON> (al)", "generator.minecraft.amplified": "AMPLIFIED", "generator.minecraft.amplified.info": "Remark: <PERSON><PERSON><PERSON><PERSON> zum <PERSON>, er<PERSON><PERSON><PERSON> ee gudde Computer.", "generator.minecraft.debug_all_block_states": "Testmodus", "generator.minecraft.flat": "Flaachland", "generator.minecraft.large_biomes": "<PERSON><PERSON>", "generator.minecraft.normal": "Standard", "generator.minecraft.single_biome_surface": "<PERSON><PERSON>zelt Biom", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "Schwiewend Inselen", "gui.abuseReport.attestation": "<PERSON>, best<PERSON><PERSON><PERSON> du, dass deng Angaben no beschtem Wësse Wouerechtsgetrei a vollständeg sinn.", "gui.abuseReport.comments": "Kommentären", "gui.abuseReport.describe": "Detailer de<PERSON>n hëlleft eis eng gutt fundéiert Entscheedung ze treffen.", "gui.abuseReport.discard.content": "<PERSON><PERSON> <PERSON> verl<PERSON>, verl<PERSON><PERSON> du dës <PERSON>ng an deng <PERSON>.\n<PERSON>cher, dat du verloosse w<PERSON><PERSON><PERSON>?", "gui.abuseReport.discard.discard": "Verloossen a Meldung verwerfen", "gui.abuseReport.discard.draft": "Als Entworf späicheren", "gui.abuseReport.discard.return": "Beaarbechtung fortsetzen", "gui.abuseReport.discard.title": "Meldung a Kommentarer verwerfen?", "gui.abuseReport.draft.content": "Wël<PERSON> du d'firhande Meldung wieder beaarbechten oder si verwerfen an eng néi Meldung kreéieren?", "gui.abuseReport.draft.discard": "Verwerfen", "gui.abuseReport.draft.edit": "Weider änneren", "gui.abuseReport.draft.quittotitle.content": "W<PERSON><PERSON>t du weider beaarbechten oder verwerfen?", "gui.abuseReport.draft.quittotitle.title": "Du hues e <PERSON>, dee verluer geet, wann s du d'<PERSON>pill verlé<PERSON>t", "gui.abuseReport.draft.title": "Meldungsentworf beaarbechten?", "gui.abuseReport.error.title": "Problem beim sch<PERSON> vun denger <PERSON>", "gui.abuseReport.message": "Wou hues du onerwënscht Verhale bemierkt?\nDat hëlleft eis, dengem Fall nozegoen.", "gui.abuseReport.more_comments": "W.e.g. beschreif wat geschitt ass:", "gui.abuseReport.name.comment_box_label": "W.e.g. <PERSON><PERSON><PERSON>, fir<PERSON> du dëse Profilnumm melle wëlls:", "gui.abuseReport.name.reporting": "Du bass um gaangen \"%s\" ze mellen.", "gui.abuseReport.name.title": "Profilnumm vum Spiller mellen", "gui.abuseReport.observed_what": "Wat ass de Grond fir dës <PERSON>?", "gui.abuseReport.read_info": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Dr<PERSON> oder Alkohol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON>en encouragéiert anerer un illegalen Drogenbezunnen Aktivitéiten deelzehuelen oder encouragéiert mannerjä<PERSON>g drénken.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Kand sexuell Ausbeutung oder Mëssbrauch", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON>en schwätzt iwwer oder soss fördert onschëlleg Verhalen mat <PERSON>.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Impersonatioun oder falsch Informatioun", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON>en beschied<PERSON>t engem anere s<PERSON><PERSON>, mécht sech als een deen se net sinn, oder deelt falsch Informatioun mam Zil anerer auszenotzen oder ze täuschen.", "gui.abuseReport.reason.description": "Beschreiwung:", "gui.abuseReport.reason.false_reporting": "Falsch Berichterstattung", "gui.abuseReport.reason.generic": "<PERSON>ch well d<PERSON><PERSON> mellen", "gui.abuseReport.reason.generic.description": "Mech nervt dë<PERSON> Spiller respektiv hien huet eppes gemaach, wat mir net gefält.", "gui.abuseReport.reason.harassment_or_bullying": "Belästegung oder Mobbing", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON>, attack<PERSON><PERSON><PERSON> oder mobbéiert Iech oder een aneren. <PERSON>ëst beinhalt wann een ëmmer erëm probéiert Iech oder een aneren ouni Zoustëmmung ze kontaktéieren oder privat perséinlech Informatioun iwwer Iech oder een aneren ouni Zoustëmmung ze posten (\"Doxing\").", "gui.abuseReport.reason.hate_speech": "Hate-Speech", "gui.abuseReport.reason.hate_speech.description": "<PERSON><PERSON> attack<PERSON><PERSON>t I<PERSON> oder en anere Spiller baséiert op Charakteristike vun hirer <PERSON><PERSON>, w<PERSON><PERSON>, <PERSON><PERSON> oder Sexualitéit.", "gui.abuseReport.reason.imminent_harm": "Imminent Schued - Bed<PERSON>hung fir anerer ze schueden", "gui.abuseReport.reason.imminent_harm.description": "<PERSON>en bedroht <PERSON>ech oder een aneren am richtege Liewen ze schueden.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Net konsensuell intim Bildmaterial", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON>en schwätzt iwwer, deelt oder fördert soss privat an intim Biller.", "gui.abuseReport.reason.self_harm_or_suicide": "Imminent Schued - Selbstschued oder Suizid", "gui.abuseReport.reason.self_harm_or_suicide.description": "Een menacéiert sech selwer am richtege Liewen ze schueden oder schwätzt iwwer sech selwer am richtege Liewen ze schueden.", "gui.abuseReport.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.sexually_inappropriate.description": "<PERSON>s, di sex<PERSON><PERSON>, Geschlechtsorganer an/oder sexuell Gewalt grafesch duerstellen.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorismus oder gewaltsam Extremismus", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON> schw<PERSON> iwwer, fö<PERSON><PERSON> oder bedroht Terrorismusakte oder gewaltsam Extremismus aus politeschen, reliéisen, ideologeschen oder anere Grënn ze maachen.", "gui.abuseReport.reason.title": "Wiel Rapport Kategorie", "gui.abuseReport.report_sent_msg": "Mir hunn deng Meldung erfollegräich kritt. Merci!\n\nEis Equipe wäert et sou séier wéi méiglech iwwerpréiwen.", "gui.abuseReport.select_reason": "Meldungskategorie auswielen", "gui.abuseReport.send": "Meldung ofschécken", "gui.abuseReport.send.comment_too_long": "W.e.g. kierz de Kommentar", "gui.abuseReport.send.error_message": "<PERSON> <PERSON>er gouf beim <PERSON> vun dengem Bericht zeréckginn:\n'%s'", "gui.abuseReport.send.generic_error": "En onerwaarte Feeler beim <PERSON> vun dengem Bericht .", "gui.abuseReport.send.http_error": "En onerwaarten HTTP-Fehler ass geschitt beim <PERSON>n vun Ärem Bericht.", "gui.abuseReport.send.json_error": "Feelforméiert Notzlaascht begéint beim Schécken vun Ärem Bericht.", "gui.abuseReport.send.no_reason": "W.e.g. wiel eng Reportkategorie", "gui.abuseReport.send.not_attested": "W.e.g. lies den ierwegtenText an hak d'Kontrollkëscht of, fir d'Meldung ofsch<PERSON>cken zu kennen.", "gui.abuseReport.send.service_unavailable": "<PERSON>nn d'Meldung vu Feelverhalen net areechen. Stell w.e.g. s<PERSON>cher dass du mam Internet verbonne bass a probéiert nach eng Kéier.", "gui.abuseReport.sending.title": "<PERSON><PERSON> g<PERSON><PERSON> ve<PERSON> …", "gui.abuseReport.sent.title": "Rapport geschéckt", "gui.abuseReport.skin.title": "Skin vum Spiller mellen", "gui.abuseReport.title": "<PERSON><PERSON><PERSON> mellen", "gui.abuseReport.type.chat": "Chatnoriichten", "gui.abuseReport.type.name": "Spillernumm", "gui.abuseReport.type.skin": "Skin", "gui.acknowledge": "Verstan", "gui.advancements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.all": "All", "gui.back": "<PERSON><PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nErfuer méi enner folgenden Link: %s", "gui.banned.description.permanent": "<PERSON><PERSON><PERSON> ass permanent verbannt, dat he<PERSON><PERSON>, dass du net online spille kanns oder Realms bäitriede kanns.", "gui.banned.description.reason": "Mir hunn viru kuerzem e Bericht fir schlecht Verhalen vun Ärem Kont kritt. Eis Moderatoren hunn elo Äre Fall iwwerpréift an en als %s identifizéiert, wat géint d'Minecraft Community Standards geet.", "gui.banned.description.reason_id": "Code: %s", "gui.banned.description.reason_id_message": "Code: %s - %s", "gui.banned.description.temporary": "%s Bis dohin kanns du net online spillen oder Realms bäitrieden.", "gui.banned.description.temporary.duration": "Äre Kont ass temporär suspendéiert a gëtt am %s nei aktivéiert.", "gui.banned.description.unknownreason": "Mir hunn viru kuerzem e Bericht fir schlecht Verhalen vun Ärem Kont kritt. Eis Moderatoren hunn elo Äre Fall iwwerpréift an identifizéiert datt et géint d'Minecraft Community Standards geet.", "gui.banned.name.description": "Däi aktuellen Numm - \"%s\" - verstéisst géint eis Community-Standards. Du kanns am Eenzelspillermodus spillen, musst awer däi Profilnumm änneren, fir online spillen ze kennen\n\nErfuer méi oder looss eng Eenzelfallspféifung aleeden op: %s", "gui.banned.name.title": "Profilnumm am Multispillermodus net erlaabt", "gui.banned.reason.defamation_impersonation_false_information": "Identitéitsdéifstall oder Verbreedung vun Informationen, fir anerer ze täuschen oder ze bedréien", "gui.banned.reason.drugs": "<PERSON><PERSON><PERSON><PERSON> zu <PERSON>", "gui.banned.reason.extreme_violence_or_gore": "Duerstellunge vun exzessiver Gewalt oder bluddege Szenen aus dem wierkleche Liewen", "gui.banned.reason.false_reporting": "Iwwerméisseg vill falsch oder ongenee Meldungen", "gui.banned.reason.fraud": "Bedruchlech Acquisitioun oder Notzung vum Inhalt", "gui.banned.reason.generic_violation": "Verletzung vun den Community-Standards", "gui.banned.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, déi op eng direkt, s<PERSON><PERSON><PERSON><PERSON> Manéier benotzt gouf", "gui.banned.reason.hate_speech": "Haassried oder Diskriminéierung", "gui.banned.reason.hate_terrorism_notorious_figure": "Referenzen op Haassgruppen, Terrororganisatiounen oder n", "gui.banned.reason.imminent_harm_to_person_or_property": "Absicht fir richtege Liewensschued u Persounen oder Immobilie ze verur", "gui.banned.reason.nudity_or_pornography": "Schlecht oder pornografescht Material weisen", "gui.banned.reason.sexually_inappropriate": "Themen oder Inhalter mat sexuellem Bezug", "gui.banned.reason.spam_or_advertising": "Spam oder Reklammen", "gui.banned.skin.description": "Däi momentane Skin verstéisst géint eis Community-Standards. Du kanns mat engem Standard-Skin weiderspillen oder en néie Skin auswielen\n\nErfuer méi oder appeléier do géint: %s", "gui.banned.skin.title": "Skin net erlaabt", "gui.banned.title.permanent": "<PERSON><PERSON> dauerhaft gespäert", "gui.banned.title.temporary": "<PERSON><PERSON> tempor<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON>", "gui.cancel": "Ofbriechen", "gui.chatReport.comments": "Kommentären", "gui.chatReport.describe": "Detailer de<PERSON><PERSON> h<PERSON>ft eis eng gutt informéiert Entscheedung ze treffen.", "gui.chatReport.discard.content": "<PERSON><PERSON> <PERSON> verl<PERSON>, verl<PERSON><PERSON> du dës Meldung an deng <PERSON>.\n<PERSON> du <PERSON>cher, dass du verloosse w<PERSON><PERSON><PERSON>?", "gui.chatReport.discard.discard": "Verloossen a verwerfen Bericht", "gui.chatReport.discard.draft": "Späicheren als Entworf", "gui.chatReport.discard.return": "Weiderspillen", "gui.chatReport.discard.title": "Rapport a Kommentaren ofginn?", "gui.chatReport.draft.content": "Wël<PERSON> du d'firhande Meldung wieder beaarbechten oder si verwerfen an eng néi Meldung kreéieren?", "gui.chatReport.draft.discard": "Wegwerfen", "gui.chatReport.draft.edit": "Beaarbechtung fortsetzen", "gui.chatReport.draft.quittotitle.content": "Wël<PERSON> du weider beaarbechten oder verwerfen?", "gui.chatReport.draft.quittotitle.title": "Du hues en Entworf vun enger <PERSON>, dee verluer geet, wann du d'Spill verléisst.", "gui.chatReport.draft.title": "Entworf Chat Bericht änneren?", "gui.chatReport.more_comments": "Be<PERSON><PERSON>iw weg wat geschitt ass:", "gui.chatReport.observed_what": "Wat ass de Grond fir dës <PERSON>?", "gui.chatReport.read_info": "Léiert iwwer Berichterstattung", "gui.chatReport.report_sent_msg": "Mir hunn Äre Bericht erfollegräich kritt. Merci!\n\nEis Equipe wäert et sou séier wéi méiglech iwwerpréiwen.", "gui.chatReport.select_chat": "Chatnoriichte fir ze mellen auswielen", "gui.chatReport.select_reason": "Wielt Rapport Kategorie", "gui.chatReport.selected_chat": "%s Chat Message(n) gewielt fir ze berichten", "gui.chatReport.send": "Schécken Rapport", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON> kuerz de Kommentar", "gui.chatReport.send.no_reason": "Wielt weg eng Rapportkategorie", "gui.chatReport.send.no_reported_messages": "Wielt weg. op d'mannst ee Chat Message fir ze berichten", "gui.chatReport.send.too_many_messages": "Probéiert ze vill Messagen an de Bericht opzehuelen", "gui.chatReport.title": "<PERSON><PERSON><PERSON> Spiller", "gui.chatSelection.context": "Messagen ronderëm dës Auswiel ginn abegraff fir zousätzlech Kontext ze bidden", "gui.chatSelection.fold": "%s Messagen verstoppt", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s huet sech beim Chat ugeschloss", "gui.chatSelection.message.narrate": "%s sot: %s um %s", "gui.chatSelection.selected": "%s/%s Message(n) ausgewielt", "gui.chatSelection.title": "<PERSON><PERSON><PERSON> Chat Messagen fir ze mellen", "gui.continue": "Weiderfueren", "gui.copy_link_to_clipboard": "<PERSON>", "gui.days": "%s Dag/Deeg", "gui.done": "Fäerdeg", "gui.down": "Rof", "gui.entity_tooltip.type": "Typ: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s <PERSON><PERSON> ofgeleent", "gui.fileDropFailure.title": "Bäimaache vun den Dateie feelgeschloen", "gui.hours": "%s Stonn(en)", "gui.loadingMinecraft": "Minecraft gëtt gelueden", "gui.minutes": "%s Minutt(en)", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s Knäppchen", "gui.narrate.editBox": "%s Inputfeld: %s", "gui.narrate.slider": "%s Slider", "gui.narrate.tab": "%s Tab", "gui.no": "<PERSON><PERSON>", "gui.none": "<PERSON><PERSON>", "gui.ok": "Ok", "gui.open_report_dir": "Meldungsordner opmaachen", "gui.proceed": "Virufueren", "gui.recipebook.moreRecipes": "Rietsklick fir méi", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Sichen ...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON> g<PERSON> u<PERSON>n", "gui.recipebook.toggleRecipes.blastable": "Alles wat schm<PERSON><PERSON><PERSON> ass", "gui.recipebook.toggleRecipes.craftable": "Alles wat hierstellbar ass", "gui.recipebook.toggleRecipes.smeltable": "Alles wat schm<PERSON><PERSON><PERSON> ass", "gui.recipebook.toggleRecipes.smokable": "Alles wat kachbar ass", "gui.report_to_server": "Un de Server mellen", "gui.socialInteractions.blocking_hint": "<PERSON> verwalten", "gui.socialInteractions.empty_blocked": "Keng blockéiert Spiller am Chat", "gui.socialInteractions.empty_hidden": "Keng Spiller am Chat ausgeblent", "gui.socialInteractions.hidden_in_chat": "Chatnoriichte vum %s ginn ausgeblent", "gui.socialInteractions.hide": "Am Chat ausblennen", "gui.socialInteractions.narration.hide": "Verstoppen Messagen vum %s", "gui.socialInteractions.narration.report": "Rapport Spiller %s", "gui.socialInteractions.narration.show": "Verstoppen Messagen vum %s", "gui.socialInteractions.report": "<PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON><PERSON> mat dësem Numm fonnt", "gui.socialInteractions.search_hint": "<PERSON><PERSON> …", "gui.socialInteractions.server_label.multiple": "%s - %s Spiller", "gui.socialInteractions.server_label.single": "%s - %s Spiller", "gui.socialInteractions.show": "<PERSON> Chat uweisen", "gui.socialInteractions.shown_in_chat": "Chatnoriichte vum %s ginn ugewisen", "gui.socialInteractions.status_blocked": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_blocked_offline": "Blockéiert - Offline", "gui.socialInteractions.status_hidden": "Verstoppt", "gui.socialInteractions.status_hidden_offline": "Ausgeblent - Offline", "gui.socialInteractions.status_offline": "Offline", "gui.socialInteractions.tab_all": "All", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "Verstoppt", "gui.socialInteractions.title": "Sozial Interaktiounen", "gui.socialInteractions.tooltip.hide": "Verstoppen messagen", "gui.socialInteractions.tooltip.report": "Rapport Spiller", "gui.socialInteractions.tooltip.report.disabled": "De Berichtservice ass net verfügbar", "gui.socialInteractions.tooltip.report.no_messages": "Keng Nor<PERSON>e vum Spiller %s, déi gemellt gi kënnen", "gui.socialInteractions.tooltip.report.not_reportable": "<PERSON><PERSON><PERSON> kann net gemellt ginn, well hir <PERSON><PERSON> Messagen kënnen net op dësem Server verifizéiert ginn", "gui.socialInteractions.tooltip.show": "Noriichte weisen", "gui.stats": "Statistiken", "gui.toMenu": "Zeréck zur Serverlëscht", "gui.toRealms": "Zeréck zur Realms-Lëscht", "gui.toTitle": "Zeréck zum Haaptmenü", "gui.toWorld": "Zeréck zur Weltlëscht", "gui.togglable_slot": "<PERSON><PERSON>, fir Inventarplaz ze spären", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "<PERSON>", "hanging_sign.edit": "Hängeschëldbeschrëftung beaarbechten", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "Iwwerleeung", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>ang<PERSON>", "inventory.binSlot": "Géigestand zerstéieren", "inventory.hotbarInfo": "Hotbar mat %1$s+%2$s späicheren", "inventory.hotbarSaved": "Hotbar gespäichert (mat %1$s+%2$s zerècksetzen)", "item.canBreak": "Kann <PERSON><PERSON>uen:", "item.canPlace": "Ka placéiert ginn op:", "item.canUse.unknown": "Onbekannt", "item.color": "Faarf: %s", "item.components": "%s Komponent(en)", "item.disabled": "Behënnert Element", "item.durability": "Halbarkeet: %s / %s", "item.dyed": "Gefierft", "item.minecraft.acacia_boat": "Akazienholzboot", "item.minecraft.acacia_chest_boat": "Akazienholz<PERSON>t mat <PERSON>", "item.minecraft.allay_spawn_egg": "Hëllefsgeescht-Spawn-Ee", "item.minecraft.amethyst_shard": "Amethystschierbel", "item.minecraft.angler_pottery_shard": "Aangeler-Dëppeschierbel", "item.minecraft.angler_pottery_sherd": "Aangeler-Dëppeschierbel", "item.minecraft.apple": "Apel", "item.minecraft.archer_pottery_shard": "Schëtzen-Dëppeschierbel", "item.minecraft.archer_pottery_sherd": "Schëtzen-Dëppeschierbel", "item.minecraft.armadillo_scute": "Armadillo-Hornschëld", "item.minecraft.armadillo_spawn_egg": "Armadillo-Spawn-Ee", "item.minecraft.armor_stand": "Rüstungsstänner", "item.minecraft.arms_up_pottery_shard": "Gebärden-Dëppeschierbel", "item.minecraft.arms_up_pottery_sherd": "Gebärden-Dëppeschierbel", "item.minecraft.arrow": "<PERSON><PERSON>", "item.minecraft.axolotl_bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_spawn_egg": "Axolotl-Spawn-Ee", "item.minecraft.baked_potato": "<PERSON><PERSON><PERSON>", "item.minecraft.bamboo_chest_raft": "Bambusflooss", "item.minecraft.bamboo_raft": "Bambusflooss", "item.minecraft.bat_spawn_egg": "Flantermaus-Spawn-Ee", "item.minecraft.bee_spawn_egg": "Beie-Spawn-Ee", "item.minecraft.beef": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot": "R<PERSON>", "item.minecraft.beetroot_seeds": "Rout <PERSON>", "item.minecraft.beetroot_soup": "Rout <PERSON>", "item.minecraft.birch_boat": "Bierkenholzboot", "item.minecraft.birch_chest_boat": "Bierkenholzboot mat <PERSON>", "item.minecraft.black_bundle": "<PERSON><PERSON><PERSON>ze Bëndel", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "Klengen-Dëppeschiebel", "item.minecraft.blade_pottery_sherd": "Klengen-Dëppeschiebel", "item.minecraft.blaze_powder": "Louestë<PERSON>", "item.minecraft.blaze_rod": "<PERSON><PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "Lou-Spawn-Ee", "item.minecraft.blue_bundle": "Bloe Bëndel", "item.minecraft.blue_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.blue_egg": "<PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "Sumpfskelett-Spawn-Ee", "item.minecraft.bolt_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolzen-Rüstungsbesaz", "item.minecraft.bone": "<PERSON><PERSON><PERSON>", "item.minecraft.bone_meal": "Schan<PERSON><PERSON><PERSON>", "item.minecraft.book": "<PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Bordür-Fändelvirlag", "item.minecraft.bow": "<PERSON><PERSON>", "item.minecraft.bowl": "<PERSON><PERSON><PERSON>", "item.minecraft.bread": "Brout", "item.minecraft.breeze_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "Breeze-Spawn-Ee", "item.minecraft.brewer_pottery_shard": "<PERSON><PERSON>uer-<PERSON><PERSON><PERSON>schierbel", "item.minecraft.brewer_pottery_sherd": "<PERSON><PERSON>uer-<PERSON><PERSON><PERSON>schierbel", "item.minecraft.brewing_stand": "Braustand", "item.minecraft.brick": "<PERSON><PERSON>", "item.minecraft.brown_bundle": "Bronge Bëndel", "item.minecraft.brown_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_egg": "Brongt Ee", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "Biischt", "item.minecraft.bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle": "Bëndel", "item.minecraft.bundle.empty": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Bitt Plaz fir e gemëschte Stapel vu Géigestänn", "item.minecraft.bundle.full": "Voll", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Flamen-<PERSON><PERSON><PERSON><PERSON>bel", "item.minecraft.burn_pottery_sherd": "Flamen-<PERSON><PERSON><PERSON><PERSON>bel", "item.minecraft.camel_spawn_egg": "Kaméil-Spawn-Ee", "item.minecraft.carrot": "<PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cat_spawn_egg": "Kaze-Spawn-Ee", "item.minecraft.cauldron": "Kessel", "item.minecraft.cave_spider_spawn_egg": "Höhlespann-Spawn-Ee", "item.minecraft.chainmail_boots": "Kettestiwwelen", "item.minecraft.chainmail_chestplate": "Ketten<PERSON><PERSON>", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.chainmail_leggings": "Kettebox", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "Kiischtenholzboot", "item.minecraft.cherry_chest_boat": "Kischtenholzboot mat <PERSON>", "item.minecraft.chest_minecart": "Gidderbuggi", "item.minecraft.chicken": "<PERSON><PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Hong-Spawn-Ee", "item.minecraft.chorus_fruit": "Chorus<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.clay_ball": "Tounklompen", "item.minecraft.clock": "<PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.coast_armor_trim_smithing_template.new": "Küsten-Rüstungsbesaz", "item.minecraft.cocoa_beans": "Ka<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod_spawn_egg": "Cabillaud-Spawn-Ee", "item.minecraft.command_block_minecart": "Befeelsblockbuggi", "item.minecraft.compass": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Büfdeck", "item.minecraft.cooked_chicken": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "Geb<PERSON>ent Hämmelsfleesch", "item.minecraft.cooked_porkchop": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "Kou-Spawn-Ee", "item.minecraft.creaking_spawn_egg": "Knarzenden-Spawn-Ee", "item.minecraft.creeper_banner_pattern": "Fändelvirlag", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Creeper-Fändelmuster", "item.minecraft.creeper_spawn_egg": "Creeper-Spawn-Ee", "item.minecraft.crossbow": "Armbrust", "item.minecraft.crossbow.projectile": "Geschoss:", "item.minecraft.crossbow.projectile.multiple": "Geschoss: %s× %s", "item.minecraft.crossbow.projectile.single": "Geschoss: %s", "item.minecraft.cyan_bundle": "Turquoise Bëndel", "item.minecraft.cyan_dye": "<PERSON>rquo<PERSON>", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Gefoer-Dëppeschierbel", "item.minecraft.danger_pottery_sherd": "Gefoer-Dëppeschierbel", "item.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>holzboot", "item.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mat <PERSON>", "item.minecraft.debug_stick": "Debug-Staf", "item.minecraft.debug_stick.empty": "%s huet keng Blockzoustänn", "item.minecraft.debug_stick.select": "'%s' ausgewielt (%s)", "item.minecraft.debug_stick.update": "'%s' ass elo %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "Diamantstiwwelen", "item.minecraft.diamond_chestplate": "Diamantbroschtpanzer", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Diamante Päerdsrüstung", "item.minecraft.diamond_leggings": "Diamantbeeschutz", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5": "Schallplattefragment", "item.minecraft.disc_fragment_5.desc": "Schallplack - 5", "item.minecraft.dolphin_spawn_egg": "Delfin-Spawn-Ee", "item.minecraft.donkey_spawn_egg": "Iesel-Spawn-Ee", "item.minecraft.dragon_breath": "Draachenotem", "item.minecraft.dried_kelp": "Gedréchent Alg", "item.minecraft.drowned_spawn_egg": "Erdronkenen-Spawn-Ee", "item.minecraft.dune_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.dune_armor_trim_smithing_template.new": "Dünen-Rüstungsbesaz", "item.minecraft.echo_shard": "Echoschierbel", "item.minecraft.egg": "Ee", "item.minecraft.elder_guardian_spawn_egg": "Grousswiechter-Spawn-Ee", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "Smaragd", "item.minecraft.enchanted_book": "Verzaubert Buch", "item.minecraft.enchanted_golden_apple": "Verzauberte gëllenen Apel", "item.minecraft.end_crystal": "Endkristall", "item.minecraft.ender_dragon_spawn_egg": "Enderdraachen-Spawn-Ee", "item.minecraft.ender_eye": "<PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "Enderman-Spawn-Ee", "item.minecraft.endermite_spawn_egg": "Endermite-Spawn-Ee", "item.minecraft.evoker_spawn_egg": "Magier-Spawn-Ee", "item.minecraft.experience_bottle": "Erfarungsfläsch", "item.minecraft.explorer_pottery_shard": "Entdecker-Dëppeschierbel", "item.minecraft.explorer_pottery_sherd": "Entdecker-Dëppeschierbel", "item.minecraft.eye_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.eye_armor_trim_smithing_template.new": "Aen-Rüstungsbesaz", "item.minecraft.feather": "<PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Spa<PERSON>", "item.minecraft.field_masoned_banner_pattern": "Mauerung-Fändelmuster", "item.minecraft.filled_map": "<PERSON><PERSON>", "item.minecraft.fire_charge": "Feier<PERSON><PERSON>", "item.minecraft.firework_rocket": "Feierwierksrakéit", "item.minecraft.firework_rocket.flight": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "item.minecraft.firework_rocket.multiple_stars": "%s× %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Feierwierksstär", "item.minecraft.firework_star.black": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.blue": "Blo", "item.minecraft.firework_star.brown": "Brong", "item.minecraft.firework_star.custom_color": "Benot<PERSON>de<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.cyan": "Turquoise", "item.minecraft.firework_star.fade_to": "<PERSON>wwer<PERSON><PERSON> zu", "item.minecraft.firework_star.flicker": "Fonkelen", "item.minecraft.firework_star.gray": "Gro", "item.minecraft.firework_star.green": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_blue": "Hellblo", "item.minecraft.firework_star.light_gray": "Hellgro", "item.minecraft.firework_star.lime": "Hellg<PERSON>ng", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "Orange", "item.minecraft.firework_star.pink": "<PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON>", "item.minecraft.firework_star.red": "Rout", "item.minecraft.firework_star.shape": "Onbekannt Form", "item.minecraft.firework_star.shape.burst": "Explosioun", "item.minecraft.firework_star.shape.creeper": "Creeperfërmeg", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "Stärefërmeg", "item.minecraft.firework_star.trail": "Sch<PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "Giel", "item.minecraft.fishing_rod": "<PERSON><PERSON><PERSON>", "item.minecraft.flint": "<PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "Bri<PERSON>", "item.minecraft.flow_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.flow_armor_trim_smithing_template.new": "Floss-Rüstungsbesaz", "item.minecraft.flow_banner_pattern": "Fändelvirlag", "item.minecraft.flow_banner_pattern.desc": "Floss", "item.minecraft.flow_banner_pattern.new": "Floss-Fändelmuster", "item.minecraft.flow_pottery_sherd": "Floss-Dëppeschierbel", "item.minecraft.flower_banner_pattern": "Fändelvirlag", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "Blummen-Fändelmuster", "item.minecraft.flower_pot": "Blummendëppen", "item.minecraft.fox_spawn_egg": "<PERSON>uss-Spawn-Ee", "item.minecraft.friend_pottery_shard": "Frënd-<PERSON><PERSON><PERSON>schierbel", "item.minecraft.friend_pottery_sherd": "Frënd-<PERSON><PERSON><PERSON>schierbel", "item.minecraft.frog_spawn_egg": "Fräsch-Spawn-Ee", "item.minecraft.furnace_minecart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ghast_spawn_egg": "Ghast-Spawn-Ee", "item.minecraft.ghast_tear": "Ghas<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "Glas<PERSON><PERSON>ä<PERSON>", "item.minecraft.glistering_melon_slice": "Blénkeg Melounescheif", "item.minecraft.globe_banner_pattern": "Fändelvirlag", "item.minecraft.globe_banner_pattern.desc": "Globus", "item.minecraft.globe_banner_pattern.new": "Äerdball-Fändelmuster", "item.minecraft.glow_berries": "Liichtbieren", "item.minecraft.glow_ink_sac": "Liichttëntesak", "item.minecraft.glow_item_frame": "Liichtkader", "item.minecraft.glow_squid_spawn_egg": "<PERSON><PERSON><PERSON>sch-Spawn-Ee", "item.minecraft.glowstone_dust": "Liichtsteestëbs", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Geessen-Spawn-Ee", "item.minecraft.gold_ingot": "Gold<PERSON>ren", "item.minecraft.gold_nugget": "Goldklompen", "item.minecraft.golden_apple": "Gëllenen Apel", "item.minecraft.golden_axe": "Goldaaxt", "item.minecraft.golden_boots": "Goldstiwwelen", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "Goldbroschtpanzer", "item.minecraft.golden_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_hoe": "Goldheel", "item.minecraft.golden_horse_armor": "Gëlle Päerdsrüstung", "item.minecraft.golden_leggings": "Goldbeeschutz", "item.minecraft.golden_pickaxe": "Goldpickaaxt", "item.minecraft.golden_shovel": "Goldsch<PERSON><PERSON>", "item.minecraft.golden_sword": "Goldsch<PERSON><PERSON><PERSON>", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_harness": "Gro Geschir", "item.minecraft.green_bundle": "Grénge Bëndel", "item.minecraft.green_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "Wiechter-Spawn-Ee", "item.minecraft.gunpowder": "Schéisspolver", "item.minecraft.guster_banner_pattern": "Fändelvirlag", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "Wandstoussert-Fändelmuster", "item.minecraft.guster_pottery_sherd": "Wandstoussert-<PERSON><PERSON><PERSON>schierbel", "item.minecraft.happy_ghast_spawn_egg": "Happy Ghast Spawn Egg", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Häerz vum Mier", "item.minecraft.heart_pottery_shard": "Häerz-Dëppeschierbel", "item.minecraft.heart_pottery_sherd": "Häerz-Dëppeschierbel", "item.minecraft.heartbreak_pottery_shard": "Häerzschmäerz-Dëppeschierbel", "item.minecraft.heartbreak_pottery_sherd": "Häerzschmäerz-Dëppeschierbel", "item.minecraft.hoglin_spawn_egg": "Hoglin-Spawn-Ee", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.honeycomb": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.hopper_minecart": "Triichterbuggi", "item.minecraft.horse_spawn_egg": "Päerd-Spawn-Ee", "item.minecraft.host_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.host_armor_trim_smithing_template.new": "Restaurateur-Rüstungsbesaz", "item.minecraft.howl_pottery_shard": "Geheil-Dëppeschierbel", "item.minecraft.howl_pottery_sherd": "Geheil-Dëppeschierbel", "item.minecraft.husk_spawn_egg": "Wüstenzombie-Spawn-Ee", "item.minecraft.ink_sac": "Tëntesak", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "Eisestiwwelen", "item.minecraft.iron_chestplate": "Eisebroschtpanzer", "item.minecraft.iron_golem_spawn_egg": "Eisegolem-Spawn-Ee", "item.minecraft.iron_helmet": "Eisenhelm", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "Eise Päerdsrüstung", "item.minecraft.iron_ingot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_leggings": "Eisebeeschutz", "item.minecraft.iron_nugget": "Eiseklompen", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.item_frame": "<PERSON><PERSON>", "item.minecraft.jungle_boat": "Tropenholzboot", "item.minecraft.jungle_chest_boat": "Tropenholzboot mat <PERSON>", "item.minecraft.knowledge_book": "Buch vum Wëssen", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lead": "<PERSON><PERSON><PERSON>", "item.minecraft.leather": "<PERSON>der", "item.minecraft.leather_boots": "Liedersstiwwelen", "item.minecraft.leather_chestplate": "Liedersjackett", "item.minecraft.leather_helmet": "Liederskap", "item.minecraft.leather_horse_armor": "Lieder Päerdsrüstung", "item.minecraft.leather_leggings": "Liedersbox", "item.minecraft.light_blue_bundle": "Hellbloe Bëndel", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "Hellgroe Bëndel", "item.minecraft.light_gray_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "Hellgrénge Bëndel", "item.minecraft.lime_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "Undauernden Dronk", "item.minecraft.lingering_potion.effect.awkward": "Komeschen undauernden Dronk", "item.minecraft.lingering_potion.effect.empty": "Net produzéierbaren undauernden Dronk", "item.minecraft.lingering_potion.effect.fire_resistance": "Undauernden Dronk vun der Féierresistenz", "item.minecraft.lingering_potion.effect.harming": "Undauernden Dronk vum Schued", "item.minecraft.lingering_potion.effect.healing": "Undauernden Dronk vun der Heelung", "item.minecraft.lingering_potion.effect.infested": "Undauernden Dronk vum Befal", "item.minecraft.lingering_potion.effect.invisibility": "Undauernden Dronk vun der Onsiichtbarkeet", "item.minecraft.lingering_potion.effect.leaping": "Undauernden Dronk vun der Sprongkraaft", "item.minecraft.lingering_potion.effect.levitation": "Undauernden Dronk vum Schwiewen", "item.minecraft.lingering_potion.effect.luck": "Undauernden Dronk vum Gléck", "item.minecraft.lingering_potion.effect.mundane": "Gewéinlechen undauernden Dronk", "item.minecraft.lingering_potion.effect.night_vision": "Undauernden Dronk vun der Nuechtsiicht", "item.minecraft.lingering_potion.effect.oozing": "Undauernden Dronk vum Schleimen", "item.minecraft.lingering_potion.effect.poison": "Undauernden Dronk vum Gëft", "item.minecraft.lingering_potion.effect.regeneration": "Undauernden Dronk vun der Regeneratioun", "item.minecraft.lingering_potion.effect.slow_falling": "Undauernden Dronk vum luese Fall", "item.minecraft.lingering_potion.effect.slowness": "Undauernden Dronk vun der Luesheet", "item.minecraft.lingering_potion.effect.strength": "Undauernden Dronk vun der Kraaft", "item.minecraft.lingering_potion.effect.swiftness": "Undauernden Dronk vun der Schnellegkeet", "item.minecraft.lingering_potion.effect.thick": "Déckflëssegen undauernden Dronk", "item.minecraft.lingering_potion.effect.turtle_master": "Undauernden Dronk vum Schildkrötenmeeschter", "item.minecraft.lingering_potion.effect.water": "Verbleiwend Waasserfläsch", "item.minecraft.lingering_potion.effect.water_breathing": "Undauernden Dronk vun der Waasserootmung", "item.minecraft.lingering_potion.effect.weakness": "Undauernden Dronk vun der Schwächt", "item.minecraft.lingering_potion.effect.weaving": "Undauernden Dronk vum Wiewen", "item.minecraft.lingering_potion.effect.wind_charged": "Undauernden Dronk vum Wandstouss", "item.minecraft.llama_spawn_egg": "Lama-Spawn-Ee", "item.minecraft.lodestone_compass": "Leetsteen-Kompass", "item.minecraft.mace": "Sträitkolben", "item.minecraft.magenta_bundle": "Magenta Bëndel", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Magmacrème", "item.minecraft.magma_cube_spawn_egg": "Magmawierfel-Spawn-Ee", "item.minecraft.mangrove_boat": "Man<PERSON><PERSON>wen<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mangrove_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mat <PERSON>", "item.minecraft.map": "<PERSON><PERSON><PERSON>", "item.minecraft.melon_seeds": "Melounekären", "item.minecraft.melon_slice": "Melounescheif", "item.minecraft.milk_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.minecart": "<PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "Minnet-Dëppeschierbel", "item.minecraft.miner_pottery_sherd": "Minnet-Dëppeschierbel", "item.minecraft.mojang_banner_pattern": "Fändelvirlag", "item.minecraft.mojang_banner_pattern.desc": "Déngen", "item.minecraft.mojang_banner_pattern.new": "Dengens-Fändelmuster", "item.minecraft.mooshroom_spawn_egg": "Mooshroom-Spawn-Ee", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>schierbel", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>schierbel", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON>esel-Spawn-Ee", "item.minecraft.mushroom_stew": "Champignonszopp", "item.minecraft.music_disc_11": "Schallplack", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "Schallplack", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Schallplack", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Schallplack", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Schallplack", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Schallplack", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Schallplack", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Schallplack", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Schallplack", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Schallplack", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Schallplack", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Schallplack", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Schallplack", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Schallplack", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Perci<PERSON>", "item.minecraft.music_disc_relic": "Schallplack", "item.minecraft.music_disc_relic.desc": "<PERSON> <PERSON>", "item.minecraft.music_disc_stal": "Schallplack", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "Schallplack", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Schallplack", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Schallplack", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON><PERSON>", "item.minecraft.name_tag": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.nautilus_shell": "Nautilusschuel", "item.minecraft.nether_brick": "Netherzill", "item.minecraft.nether_star": "Netherstär", "item.minecraft.nether_wart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.netherite_boots": "Netheritstiwwelen", "item.minecraft.netherite_chestplate": "Netheritebroschtpanzer", "item.minecraft.netherite_helmet": "Netherithelm", "item.minecraft.netherite_hoe": "Netheriteheel", "item.minecraft.netherite_ingot": "Netheritbarren", "item.minecraft.netherite_leggings": "Netheritbeeschutz", "item.minecraft.netherite_pickaxe": "Netheritepic<PERSON><PERSON><PERSON>", "item.minecraft.netherite_scrap": "Netheritfragment", "item.minecraft.netherite_shovel": "Netheritschëpp", "item.minecraft.netherite_sword": "Netheritschwäert", "item.minecraft.netherite_upgrade_smithing_template": "Schmaddschabloun", "item.minecraft.netherite_upgrade_smithing_template.new": "Netheritopwäertung", "item.minecraft.oak_boat": "Eechenholzboot", "item.minecraft.oak_chest_boat": "Eecheboot mat <PERSON>", "item.minecraft.ocelot_spawn_egg": "Ozelot-Spawn-Ee", "item.minecraft.ominous_bottle": "Onheilvoll Fläsch", "item.minecraft.ominous_trial_key": "Onheilvolle Préifungsschlëssel", "item.minecraft.orange_bundle": "Orange Bëndel", "item.minecraft.orange_dye": "<PERSON> Fuerfstoff", "item.minecraft.orange_harness": "Orange Geschier", "item.minecraft.painting": "Bild", "item.minecraft.pale_oak_boat": "Blatzeechenholzboot", "item.minecraft.pale_oak_chest_boat": "Blatzeechenholz<PERSON>t mat <PERSON>", "item.minecraft.panda_spawn_egg": "Panda-Spawn-Ee", "item.minecraft.paper": "<PERSON><PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "Papagei-Spawn-Ee", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON>", "item.minecraft.phantom_spawn_egg": "Phantom-Spawn-Ee", "item.minecraft.pig_spawn_egg": "Schwäin-Spawn-Ee", "item.minecraft.piglin_banner_pattern": "Fändelvirlag", "item.minecraft.piglin_banner_pattern.desc": "Schnëss", "item.minecraft.piglin_banner_pattern.new": "Schnëss-Fändelmuster", "item.minecraft.piglin_brute_spawn_egg": "Piglin-Barbaren-Spawn-Ee", "item.minecraft.piglin_spawn_egg": "Piglin-Spawn-Ee", "item.minecraft.pillager_spawn_egg": "Plëmmert-Spawn-Ee", "item.minecraft.pink_bundle": "<PERSON><PERSON>", "item.minecraft.pink_dye": "<PERSON>", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pitcher_pod": "Kaneplanzekapsel", "item.minecraft.plenty_pottery_shard": "Räichtums-Dëppeschierbel", "item.minecraft.plenty_pottery_sherd": "Räichtum-Dëppeschierbel", "item.minecraft.poisonous_potato": "Vergëfte Gromper", "item.minecraft.polar_bear_spawn_egg": "Äisbier-Spawn-Ee", "item.minecraft.popped_chorus_fruit": "Geplatzte Chorusfruucht", "item.minecraft.porkchop": "<PERSON><PERSON><PERSON>", "item.minecraft.potato": "<PERSON><PERSON><PERSON>", "item.minecraft.potion": "Dronk", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.empty": "Net produzéierbaren Dronk", "item.minecraft.potion.effect.fire_resistance": "Dronk vun der Féierresistenz", "item.minecraft.potion.effect.harming": "Dronk vum Schued", "item.minecraft.potion.effect.healing": "Dronk vun der Heelung", "item.minecraft.potion.effect.infested": "Dronk vum Befal", "item.minecraft.potion.effect.invisibility": "Dronk vun der Onsiichtbarkeet", "item.minecraft.potion.effect.leaping": "Dronk vun der Sprongkraaft", "item.minecraft.potion.effect.levitation": "Dronk vum Schwiewen", "item.minecraft.potion.effect.luck": "Dronk vum Gléck", "item.minecraft.potion.effect.mundane": "Gewéinlechen Dronk", "item.minecraft.potion.effect.night_vision": "Dronk vun der Nuechtsiicht", "item.minecraft.potion.effect.oozing": "Dronk vum Schleimen", "item.minecraft.potion.effect.poison": "Dronk vum Gëft", "item.minecraft.potion.effect.regeneration": "Dronk vun der Regeneratioun", "item.minecraft.potion.effect.slow_falling": "Dronk vum luese Fall", "item.minecraft.potion.effect.slowness": "Dronk vun <PERSON>", "item.minecraft.potion.effect.strength": "Dronk vun der Kraaft", "item.minecraft.potion.effect.swiftness": "Dronk vun der Schnellegkeet", "item.minecraft.potion.effect.thick": "Déckflëssegen Dronk", "item.minecraft.potion.effect.turtle_master": "Dronk vum Schildkrötenmeeschter", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "Dron<PERSON> vun der <PERSON>sserootmung", "item.minecraft.potion.effect.weakness": "Dronk vun der Schwächt", "item.minecraft.potion.effect.weaving": "Dronk vum Wiewen", "item.minecraft.potion.effect.wind_charged": "Dronk vum Wandstouss", "item.minecraft.pottery_shard_archer": "Schëtzen-Dëppeschierbel", "item.minecraft.pottery_shard_arms_up": "Gebärden-Dëppeschierbel", "item.minecraft.pottery_shard_prize": "Edelsteen-Dëppeschierbel", "item.minecraft.pottery_shard_skull": "Doudekapp-Dëppeschierbel", "item.minecraft.powder_snow_bucket": "Polverschnéieemer", "item.minecraft.prismarine_crystals": "Prismarinkristall", "item.minecraft.prismarine_shard": "Prismarins<PERSON>erbel", "item.minecraft.prize_pottery_shard": "Edelsteen-Dëppeschierbel", "item.minecraft.prize_pottery_sherd": "Edelsteen-Dëppeschierbel", "item.minecraft.pufferfish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pufferfish_spawn_egg": "Kugelfësch-Spawn-Ee", "item.minecraft.pumpkin_pie": "Kürbiskuch", "item.minecraft.pumpkin_seeds": "Kürbiskären", "item.minecraft.purple_bundle": "Mofe B<PERSON>l", "item.minecraft.purple_dye": "<PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "item.minecraft.rabbit_foot": "Kanéngecherspatt", "item.minecraft.rabbit_hide": "Kanéngecherspelz", "item.minecraft.rabbit_spawn_egg": "Kanéngchen-Spawn-Ee", "item.minecraft.rabbit_stew": "Kanéngechersragoût", "item.minecraft.raiser_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.raiser_armor_trim_smithing_template.new": "Opzéier-Rüstungsbesaz", "item.minecraft.ravager_spawn_egg": "Verwüster-Spawn-Ee", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_gold": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_iron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.recovery_compass": "Rettungskompass", "item.minecraft.red_bundle": "Roude Bëndel", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "Redstone-Stëbs", "item.minecraft.resin_brick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.resin_clump": "Harzklompen", "item.minecraft.rib_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.rib_armor_trim_smithing_template.new": "Rëppen-Rüstungsbesaz", "item.minecraft.rotten_flesh": "Verfaultent Fleesch", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON><PERSON>", "item.minecraft.salmon_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.salmon_spawn_egg": "Saumon-Spawn-Ee", "item.minecraft.scrape_pottery_sherd": "Schréibs-<PERSON><PERSON>sch<PERSON>bel", "item.minecraft.scute": "<PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.sentry_armor_trim_smithing_template.new": "Wachen-Rüstungsbesaz", "item.minecraft.shaper_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.shaper_armor_trim_smithing_template.new": "Gestalter-Rüstungsbesaz", "item.minecraft.sheaf_pottery_shard": "Garben-Dëppeschierbel", "item.minecraft.sheaf_pottery_sherd": "Garben-Dëppeschierbel", "item.minecraft.shears": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "Schof-Spawn-Ee", "item.minecraft.shelter_pottery_shard": "Zouflocht-Dëppeschierbel", "item.minecraft.shelter_pottery_sherd": "Zouflocht-Dëppeschierbel", "item.minecraft.shield": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "Schwaarzt Schëld", "item.minecraft.shield.blue": "<PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON>", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.light_gray": "Hellgrot Schëld", "item.minecraft.shield.lime": "Hellgréng<PERSON>", "item.minecraft.shield.magenta": "Magentat Schëld", "item.minecraft.shield.orange": "<PERSON><PERSON>", "item.minecraft.shield.pink": "<PERSON><PERSON>", "item.minecraft.shield.purple": "Moft Schëld", "item.minecraft.shield.red": "<PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON><PERSON><PERSON><PERSON> Schëld", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "Shulker-Spawn-Ee", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.silence_armor_trim_smithing_template.new": "Stëll-Rüstungsbesaz", "item.minecraft.silverfish_spawn_egg": "Sëlwerfëschelchen-Spawn-Ee", "item.minecraft.skeleton_horse_spawn_egg": "Skelettpäerd-Spawn-Ee", "item.minecraft.skeleton_spawn_egg": "Skelett-Spawn-Ee", "item.minecraft.skull_banner_pattern": "Fändelvirlag", "item.minecraft.skull_banner_pattern.desc": "Doudeka<PERSON>", "item.minecraft.skull_banner_pattern.new": "Doudekapp-Fändelmuster", "item.minecraft.skull_pottery_shard": "Doudekapp-Dëppeschierbel", "item.minecraft.skull_pottery_sherd": "Doudekapp-Dëppeschierbel", "item.minecraft.slime_ball": "Schläimball", "item.minecraft.slime_spawn_egg": "Schläim-Spawn-Ee", "item.minecraft.smithing_template": "Schmaddschabloun", "item.minecraft.smithing_template.applies_to": "<PERSON><PERSON><PERSON><PERSON> op:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Barren oder Kristall bäimaachen", "item.minecraft.smithing_template.armor_trim.applies_to": "Rüstung", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Rüstungsdeel bäimachen", "item.minecraft.smithing_template.armor_trim.ingredients": "Barren & Kristaller", "item.minecraft.smithing_template.ingredients": "<PERSON><PERSON>ks<PERSON><PERSON>:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Netherritbarre bäimaachen", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamantenausrüstung", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Geschier oder Schwäert aus Diamant bäimachen", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netheritbarren", "item.minecraft.smithing_template.upgrade": "Opwäerten: ", "item.minecraft.sniffer_spawn_egg": "Schnoffeler-Spawn-Ee", "item.minecraft.snort_pottery_shard": "Schnaub-Dëppeschierbel", "item.minecraft.snort_pottery_sherd": "Schnaub-Dëppeschierbel", "item.minecraft.snout_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.snout_armor_trim_smithing_template.new": "Schnëss-Rüstungsbesaz", "item.minecraft.snow_golem_spawn_egg": "Schnéigolem-Spawn-Ee", "item.minecraft.snowball": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spectral_arrow": "Spektral Feil", "item.minecraft.spider_eye": "Spannena", "item.minecraft.spider_spawn_egg": "Spann-Spawn-Ee", "item.minecraft.spire_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.spire_armor_trim_smithing_template.new": "Tuermspëtzten-Rüstungsbesaz", "item.minecraft.splash_potion": "Worfdronk", "item.minecraft.splash_potion.effect.awkward": "Komesche Worfdronk", "item.minecraft.splash_potion.effect.empty": "Net produzéierbare Worfdronk", "item.minecraft.splash_potion.effect.fire_resistance": "Worfdronk vun der Féierresistenz", "item.minecraft.splash_potion.effect.harming": "Worfdronk vum Schued", "item.minecraft.splash_potion.effect.healing": "Worfdronk vun der Waasserootmung", "item.minecraft.splash_potion.effect.infested": "Worfdronk vum Befal", "item.minecraft.splash_potion.effect.invisibility": "Worfdronk vun der Onsiichtbarkeet", "item.minecraft.splash_potion.effect.leaping": "Worfdronk vun der Sprongkraaft", "item.minecraft.splash_potion.effect.levitation": "Worfdronk vum Schwiewen", "item.minecraft.splash_potion.effect.luck": "Worfdronk vum Gléck", "item.minecraft.splash_potion.effect.mundane": "Gewéinleche Worfdronk", "item.minecraft.splash_potion.effect.night_vision": "Worfdronk vun der Nuechtsiicht", "item.minecraft.splash_potion.effect.oozing": "Worfdronk vum Schleimen", "item.minecraft.splash_potion.effect.poison": "Worfdronk vum Gëft", "item.minecraft.splash_potion.effect.regeneration": "Worfdronk vun der Regeneratioun", "item.minecraft.splash_potion.effect.slow_falling": "Worfdronk vum luese Fall", "item.minecraft.splash_potion.effect.slowness": "Worfdronk vun der Luesheet", "item.minecraft.splash_potion.effect.strength": "Worfdronk vun der Kraaft", "item.minecraft.splash_potion.effect.swiftness": "Worfdronk vun der Schnellegkeet", "item.minecraft.splash_potion.effect.thick": "Déckflëssege Worfdronk", "item.minecraft.splash_potion.effect.turtle_master": "Worfdronk vum Schildkrötenmeeschter", "item.minecraft.splash_potion.effect.water": "Werfbar Waasserfläsch", "item.minecraft.splash_potion.effect.water_breathing": "Worfdronk vun der Luesheet", "item.minecraft.splash_potion.effect.weakness": "Worfdronk vun der Schwächt", "item.minecraft.splash_potion.effect.weaving": "Worfdronk vum Wiewen", "item.minecraft.splash_potion.effect.wind_charged": "Worfdronk vum Wandstouss", "item.minecraft.spruce_boat": "Fiichtenholzboot", "item.minecraft.spruce_chest_boat": "<PERSON>ichtenholz<PERSON>t mat <PERSON>", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "Tëntefësch-Spawn-Ee", "item.minecraft.stick": "<PERSON><PERSON>", "item.minecraft.stone_axe": "Steeaaxt", "item.minecraft.stone_hoe": "Steenheel", "item.minecraft.stone_pickaxe": "Steepickaaxt", "item.minecraft.stone_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stray_spawn_egg": "Vagabund-Spawn-Ee", "item.minecraft.strider_spawn_egg": "<PERSON><PERSON>eiter-Spawn-Ee", "item.minecraft.string": "Fu<PERSON><PERSON>", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "Suspekt Zopp", "item.minecraft.sweet_berries": "Séissbieren", "item.minecraft.tadpole_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tadpole_spawn_egg": "Kauzekapp-Spawn-Ee", "item.minecraft.tide_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.tide_armor_trim_smithing_template.new": "Gezeiten-Rüstungsbesaz", "item.minecraft.tipped_arrow": "Gedränkte Feil", "item.minecraft.tipped_arrow.effect.awkward": "Gedränkte Feil", "item.minecraft.tipped_arrow.effect.empty": "Net hierstellbare gedränkte Feil", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON>il vun der Féierresistenz", "item.minecraft.tipped_arrow.effect.harming": "Feil vum Schued", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON> vun der Heelung", "item.minecraft.tipped_arrow.effect.infested": "Feil vum Befal", "item.minecraft.tipped_arrow.effect.invisibility": "<PERSON>il vun der Onsiichtbarkeet", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON> vun der Sprongkraaft", "item.minecraft.tipped_arrow.effect.levitation": "Feil vum Schwiewen", "item.minecraft.tipped_arrow.effect.luck": "Feil vum Gléck", "item.minecraft.tipped_arrow.effect.mundane": "Gedränkte Feil", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON> vun der Nuechtsiicht", "item.minecraft.tipped_arrow.effect.oozing": "Feil vum Schläimen", "item.minecraft.tipped_arrow.effect.poison": "Feil vum Gëft", "item.minecraft.tipped_arrow.effect.regeneration": "<PERSON><PERSON> vun der Regeneratioun", "item.minecraft.tipped_arrow.effect.slow_falling": "Feil vum luese Fall", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON> v<PERSON> der <PERSON>", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON> vun der Kraaft", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON>il vun der Schnellegkeet", "item.minecraft.tipped_arrow.effect.thick": "Gedränkte Feil", "item.minecraft.tipped_arrow.effect.turtle_master": "Feil vum Schildkrötenmeeschter", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON><PERSON> vun der <PERSON>", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON>il vun der Schwächt", "item.minecraft.tipped_arrow.effect.weaving": "Feil vum Wiewen", "item.minecraft.tipped_arrow.effect.wind_charged": "<PERSON><PERSON> vum Wan<PERSON>uss", "item.minecraft.tnt_minecart": "TNT-Buggi", "item.minecraft.torchflower_seeds": "Karblumm Som", "item.minecraft.totem_of_undying": "Totem vun der Onstierflechkeet", "item.minecraft.trader_llama_spawn_egg": "Händlerlama-Spawn-Ee", "item.minecraft.trial_key": "Préifungsschlëssel", "item.minecraft.trident": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish": "T<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tropical_fish_spawn_egg": "Tropefësch-Spawn-Ee", "item.minecraft.turtle_helmet": "<PERSON><PERSON><PERSON>kr<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.turtle_scute": "Schildkrö<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.turtle_spawn_egg": "Schildkröten-Spawn-Ee", "item.minecraft.vex_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.vex_armor_trim_smithing_template.new": "Plogegeescht-Rüstungsbesaz", "item.minecraft.vex_spawn_egg": "Plog<PERSON>chter-Spawn-Ee", "item.minecraft.villager_spawn_egg": "Awunner-Spawn-Ee", "item.minecraft.vindicator_spawn_egg": "Lakai-Spawn-Ee", "item.minecraft.wandering_trader_spawn_egg": "Nomadenhändler-Spawn-Ee", "item.minecraft.ward_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.ward_armor_trim_smithing_template.new": "Wart-Rüstungsbesaz", "item.minecraft.warden_spawn_egg": "Wärter-Spawn-Ee", "item.minecraft.warped_fungus_on_a_stick": "<PERSON><PERSON><PERSON><PERSON><PERSON> op engem Bengel", "item.minecraft.water_bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Weefanner-Rüstungsbesaz", "item.minecraft.wheat": "Weess", "item.minecraft.wheat_seeds": "Weesskären", "item.minecraft.white_bundle": "Wäisse Bëndel", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "Schmaddschabloun", "item.minecraft.wild_armor_trim_smithing_template.new": "Wildnis-Rüstungsbesaz", "item.minecraft.wind_charge": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.witch_spawn_egg": "Hexen-Spawn-Ee", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON>-<PERSON><PERSON>ett-Spawn-Ee", "item.minecraft.wither_spawn_egg": "Wither-Spawn-Ee", "item.minecraft.wolf_armor": "<PERSON>ollefss<PERSON>zer", "item.minecraft.wolf_spawn_egg": "Wollefs-Spawn-Ee", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.writable_book": "Buch a Fieder", "item.minecraft.written_book": "Beschriwwent Buch", "item.minecraft.yellow_bundle": "Giele Bëndel", "item.minecraft.yellow_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "Zoglin-Spawn-Ee", "item.minecraft.zombie_horse_spawn_egg": "Zombiepäerd-Spawn-Ee", "item.minecraft.zombie_spawn_egg": "Zombie-Spawn-Ee", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON><PERSON>ner-Spawn-Ee", "item.minecraft.zombified_piglin_spawn_egg": "Zombifizéierten-Piglin-Spawn-Ee", "item.modifiers.any": "Ausgerüst:", "item.modifiers.armor": "Ugedoen:", "item.modifiers.body": "<PERSON>:", "item.modifiers.chest": "<PERSON>:", "item.modifiers.feet": "Un de Féiss:", "item.modifiers.hand": "Gehalen:", "item.modifiers.head": "<PERSON>:", "item.modifiers.legs": "<PERSON> de Been:", "item.modifiers.mainhand": "<PERSON> <PERSON>:", "item.modifiers.offhand": "<PERSON> der Niewenhand:", "item.modifiers.saddle": "<PERSON> R<PERSON>ck:", "item.nbt_tags": "NBT: %s Eegenschaft(en)", "item.op_block_warning.line1": "Warnung:", "item.op_block_warning.line2": "Dëse Block kéint direkt e Befeel ausféieren.", "item.op_block_warning.line3": "<PERSON><PERSON><PERSON> des just, wann du de geneeën Inhalt kenns!", "item.unbreakable": "Onzerstéierbar", "itemGroup.buildingBlocks": "Konstruktiounsbléck", "itemGroup.coloredBlocks": "Faarweg <PERSON>", "itemGroup.combat": "<PERSON><PERSON><PERSON>", "itemGroup.consumables": "Iessen & Gedrénks", "itemGroup.crafting": "Handwierk", "itemGroup.foodAndDrink": "Iessen & Gedrénks", "itemGroup.functional": "Gebrauchsbléck", "itemGroup.hotbar": "Gespäichert Hotbars", "itemGroup.ingredients": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.inventory": "Iwwerliewens Inventar", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.op": "Operatorhëllefsmëttel", "itemGroup.redstone": "Redstone", "itemGroup.search": "<PERSON><PERSON>", "itemGroup.spawnEggs": "Spawn-Eer", "itemGroup.tools": "Geschier & Hëllefsmëttel", "item_modifier.unknown": "Onbekannte Géigestandsmodifikateur: %s", "jigsaw_block.final_state": "<PERSON><PERSON><PERSON> <PERSON>:", "jigsaw_block.generate": "<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Ausgeriicht", "jigsaw_block.joint.rollable": "Dréibar", "jigsaw_block.joint_label": "Lien:", "jigsaw_block.keep_jigsaws": "Puzzle halen", "jigsaw_block.levels": "Niveauen: %s", "jigsaw_block.name": "Numm:", "jigsaw_block.placement_priority": "Placéierungsprioritéit:", "jigsaw_block.placement_priority.tooltip": "<PERSON><PERSON> sech dëse Verbundblock mat engem <PERSON><PERSON> verb<PERSON>, ass d'Reiefolleg, an der d'Stéck op Verbindungen zur Gesamtkonstruktioun veraarbecht gëtt.", "jigsaw_block.pool": "Bezuchsquell:", "jigsaw_block.selection_priority": "Auswielprioritéit:", "jigsaw_block.selection_priority.tooltip": "<PERSON>n d'zougehéiereg Stéck op Verbindunge veraarbecht gëtt, ass des an der Reiefolleg, an der dëse Verbundblock versicht, sech mat dengem Zielstéck ze verbannen.", "jigsaw_block.target": "Zielnumm:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> (Spillauer)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Perci<PERSON>", "jukebox_song.minecraft.relic": "<PERSON> <PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.attack": "Attackéieren/Zerstéieren", "key.back": "Hannerzeg", "key.categories.creative": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "key.categories.gameplay": "Spillmechanik", "key.categories.inventory": "Inventar", "key.categories.misc": "Verschiddenes", "key.categories.movement": "Bewegung", "key.categories.multiplayer": "Multispiller", "key.categories.ui": "Spillsteierung", "key.chat": "<PERSON><PERSON>", "key.command": "Befehlszeil opmaachen", "key.drop": "Géigestand fale loossen", "key.forward": "Vijenzeg", "key.fullscreen": "Vollbildmodus", "key.hotbar.1": "Schnellzougrëff 1", "key.hotbar.2": "Schnellzougrëff 2", "key.hotbar.3": "Schnellzougrëff 3", "key.hotbar.4": "Schnellzougrëff 4", "key.hotbar.5": "Schnellzougrëff 5", "key.hotbar.6": "Schnellzougrëff 6", "key.hotbar.7": "Schnellzougrëff 7", "key.hotbar.8": "Schnellzougrëff 8", "key.hotbar.9": "Schnellzougrëff 9", "key.inventory": "Inventar opmaachen/zoumaachen", "key.jump": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Läschen", "key.keyboard.down": "<PERSON>il erof", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Escape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Num 0", "key.keyboard.keypad.1": "Num 1", "key.keyboard.keypad.2": "Num 2", "key.keyboard.keypad.3": "Num 3", "key.keyboard.keypad.4": "Num 4", "key.keyboard.keypad.5": "Num 5", "key.keyboard.keypad.6": "Num 6", "key.keyboard.keypad.7": "Num 7", "key.keyboard.keypad.8": "Num 8", "key.keyboard.keypad.9": "Num 9", "key.keyboard.keypad.add": "Num +", "key.keyboard.keypad.decimal": "Num .", "key.keyboard.keypad.divide": "Num /", "key.keyboard.keypad.enter": "<PERSON><PERSON>", "key.keyboard.keypad.equal": "Num =", "key.keyboard.keypad.multiply": "Num *", "key.keyboard.keypad.subtract": "Num -", "key.keyboard.left": "Feil no lénks", "key.keyboard.left.alt": "Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ctrl lénks", "key.keyboard.left.shift": "Shift lénks", "key.keyboard.left.win": "<PERSON> lénks", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Print Screen", "key.keyboard.right": "Feil no riets", "key.keyboard.right.alt": "Alt Gr", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Ctrl riets", "key.keyboard.right.shift": "Shift riets", "key.keyboard.right.win": "Win riets", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Space", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Net festgeluecht", "key.keyboard.up": "<PERSON>il erop", "key.keyboard.world.1": "Welt 1", "key.keyboard.world.2": "Welt 2", "key.left": "<PERSON><PERSON><PERSON>", "key.loadToolbarActivator": "<PERSON><PERSON> lueden", "key.mouse": "Maustast %1$s", "key.mouse.left": "Lénksklick", "key.mouse.middle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.mouse.right": "Rietsklick", "key.pickItem": "Block auswielen", "key.playerlist": "<PERSON><PERSON><PERSON> oplëschten", "key.quickActions": "Quick Actions", "key.right": "<PERSON><PERSON><PERSON>", "key.saveToolbarActivator": "<PERSON>bar späicheren", "key.screenshot": "Screenshot huelen", "key.smoothCamera": "Kameraverhale wiesselen", "key.sneak": "Schläichen", "key.socialInteractions": "Sozialinteraktiounsfënster", "key.spectatorOutlines": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> (Zuschauer)", "key.sprint": "Sprinten", "key.swapOffhand": "Géigestand mat <PERSON> tauschen", "key.togglePerspective": "Perspektiv ëmschalten", "key.use": "Géigestand/Block benotzen", "known_server_link.announcements": "Ukënnegung", "known_server_link.community": "Gemengschaft", "known_server_link.community_guidelines": "Gemengschaftsrichtlinnen", "known_server_link.feedback": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "known_server_link.forums": "<PERSON>en", "known_server_link.news": "Neiegkeeten", "known_server_link.report_bug": "Feeler um <PERSON> mellen", "known_server_link.status": "Status", "known_server_link.support": "Hëllef/Support", "known_server_link.website": "Website", "lanServer.otherPlayers": "Astellunge fir an<PERSON>", "lanServer.port": "Portnummer", "lanServer.port.invalid": "Kee gültege Port.\nLooss d'Eingabefeld eidel oder gëff eng Nummer tëschent 1024 a 65535 an.", "lanServer.port.invalid.new": "Falsche Port.\nLooss d'Këscht eidel oder gëf eng Nummer zwëschen %s an %s an.", "lanServer.port.unavailable": "Port net verfügbar.\nLooss d'Këscht eidel oder gëf eng aner Nummer zwëschen 1024 an 65535 an.", "lanServer.port.unavailable.new": "Port net verfügbar.\nLooss d'Këscht eidel oder gëf eng aner Nummer zwëschen %s an %s an.", "lanServer.scanning": "Sichen no Spiller am lokalen Netzwierk", "lanServer.start": "D'LAN-Welt starten", "lanServer.title": "LAN-Welt", "language.code": "ltz_LU", "language.name": "Lëtzebuergesch", "language.region": "Lëtzebuerg", "lectern.take_book": "<PERSON>uch huelen", "loading.progress": "%s %%", "mco.account.privacy.info": "Méi iwwer Mojang an d'Dateschutzgesetzer gewuer ginn", "mco.account.privacy.info.button": "Erfuer méi iwwer d'DSGVO", "mco.account.privacy.information": "<PERSON>jang <PERSON><PERSON><PERSON><PERSON>, fir Kanner an hier Privatsphär ze schützen; esou ginn dat \"<PERSON>esetz zum Schutz vun der Privatsphär vu Kanner am Internet\" (COPPA) an der genereller Dateschutzregulatioun (GDPR) agehalen.\n\nMéiglecherweis muss du d'Zoustëmmung vun dengen Elteren erëmkréien, fir op däi Realms-Konto zougräifen ze kënnen.", "mco.account.privacyinfo": "Mojang implementé<PERSON>, fir <PERSON><PERSON> an hier Privatsphären ze schützen. Esou ginn ënnert anerem \"Children’s Online Privacy Protection Act\" (COPPA) an \"General Data Protection Regulation\" (GDPR) agehalen.\n\nMéiglecherweis brauchs du d'Zoustëmmung vun dengen Elteren, fir op däi Realms-Konto zouzegräifen.\n\nFalls du een ale Minecraft Konto hues (mells dech mat dengem Benotzernumm un), muss du dësen an ee Mojang-Konto ëmwandelen, fir op Realms zougräifen ze kënnen.", "mco.account.update": "Konto aktualiséieren", "mco.activity.noactivity": "Keng Aktivitéit an de läschte(n) %s Deeg", "mco.activity.title": "Spilleraktivitéit", "mco.backup.button.download": "Erof<PERSON>den", "mco.backup.button.reset": "Welt zerécksetzen", "mco.backup.button.restore": "<PERSON><PERSON><PERSON>", "mco.backup.button.upload": "Welt eroplueden", "mco.backup.changes.tooltip": "Ännerungen", "mco.backup.entry": "Sécherung (%s)", "mco.backup.entry.description": "Descriptioun", "mco.backup.entry.enabledPack": "Aktivéiert(e) Pak/<PERSON>äck", "mco.backup.entry.gameDifficulty": "Spillschwéieregkeet", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Spillserverversioun", "mco.backup.entry.name": "Numm", "mco.backup.entry.seed": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.templateName": "Schablounenumm", "mco.backup.entry.undefined": "Onbestëmmt Ännerung", "mco.backup.entry.uploaded": "Eropgelueden", "mco.backup.entry.worldType": "Welttyp", "mco.backup.generate.world": "Welt generéieren", "mco.backup.info.title": "Ännerunge géigeniwwer vun der leschter Sécherung", "mco.backup.narration": "Sécherung vum: %s", "mco.backup.nobackups": "Dëse Realm huet zur Zäit keng Sécherungen.", "mco.backup.restoring": "<PERSON>äin Realm gëtt rëm hiergestallt", "mco.backup.unknown": "ONBEKANNT", "mco.brokenworld.download": "Erof<PERSON>den", "mco.brokenworld.downloaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.message.line1": "W.e.g. setz se zer<PERSON>ck oder wiel eng aner Welt aus.", "mco.brokenworld.message.line2": "Du kanns d'Welt och an den Eenzelspillermodus eroflueden.", "mco.brokenworld.minigame.title": "Dat Minispill gëtt net méi ënnerstëtzt", "mco.brokenworld.nonowner.error": "W.e.g. waart bis de Realm-Besëtzer d'Welt zeréckgesat huet", "mco.brokenworld.nonowner.title": "D'Welt ass net méi aktuell", "mco.brokenworld.play": "<PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "Zeréckstellen", "mco.brokenworld.title": "Deng momentan Welt gëtt net méi ënnerstëtzt", "mco.client.incompatible.msg.line1": "<PERSON><PERSON>in Client ass net kompatibel mat Realms.", "mco.client.incompatible.msg.line2": "W.e.g. ben<PERSON>z déi neiste Versioun vu Minecraft.", "mco.client.incompatible.msg.line3": "Realms ass net kompatibel mat Snapshotversiounen.", "mco.client.incompatible.title": "Client inkompatibel!", "mco.client.outdated.stable.version": "D'Versioun vun dengem Client (%s) ass net mat Realms kompatibel.\n\nW.e.g. benotz d'neist Versioun vu Minecraft.", "mco.client.unsupported.snapshot.version": "D'Versioun vun dengem Client (%s) ass net mat Realms kompatibel.\n\nRealms ass fir dës Entwécklungsversioun net verfügbar.", "mco.compatibility.downgrade": "Ofwäerten", "mco.compatibility.downgrade.description": "Dës Welt gouf zuslescht an der Versioun %s gespillt; du befënns dech op Versioun %s. Eng <PERSON>lt erofzestufen, kann dës beschiedegen - mir kënnen net garantéieren, dat si geluede gëtt oder funktionéiert.\n\nËnner \"Sécherungen\" gëtt eng Sécherheetskopie vun denger Welt gespäichert. W.e.g. setz si wann neidesch dorop zeréck.", "mco.compatibility.incompatible.popup.title": "Inkompatibel Versioun", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON> <PERSON> bet<PERSON><PERSON> w<PERSON>, ass mat denger aktueller Versioun net kompatibel.", "mco.compatibility.incompatible.series.popup.message": "Dës Welt gouf zuslescht an der Versioun %s gespillt; du befënns dech op Versioun %s.\n\nD'Versiounsstänner sinn net matenee kompatibel. Fir op dëser Versioun ze spille gëtt eng nei Welt benéidegt.", "mco.compatibility.unverifiable.message": "Et konnt net festgestallt ginn, a wéi enger Versioun dës Welt zuslescht gespillt gouf. Wann du si erop- respektiv erofstuufs, gëtt automatesch eng Sécherungskopie kreéiert an ënner \"Sécherungen\" gespäichert.", "mco.compatibility.unverifiable.title": "Kompatibilitéit net iwwerpréifbar", "mco.compatibility.upgrade": "Opwäerten", "mco.compatibility.upgrade.description": "Dës Welt gouf zuslescht an der Versioun %s gespillt; du befenns dech op Versioun %s.\n\n<PERSON><PERSON> \"Sécherungen\" gëtt eng Sécherheetskopie vun denger Welt gespäichert.\n\nW.e.g. setz deng Welt am Bedarfsfall dorop zeréck.", "mco.compatibility.upgrade.friend.description": "Dës Welt gouf zuslescht an der Versioun %s gespillt; du befënns dech op Versioun %s.", "mco.compatibility.upgrade.title": "W<PERSON><PERSON> du dës Welt wierklech eropstufen?", "mco.configure.current.minigame": "Aktuell", "mco.configure.world.activityfeed.disabled": "Display vun der Spilleraktivitéit temporär desaktivéiert", "mco.configure.world.backup": "Sécherungen", "mco.configure.world.buttons.activity": "Spilleraktivitéit", "mco.configure.world.buttons.close": "Realm zoumaachen", "mco.configure.world.buttons.delete": "Läschen", "mco.configure.world.buttons.done": "Fäerdeg", "mco.configure.world.buttons.edit": "Astellungen", "mco.configure.world.buttons.invite": "Alueden", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON>", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Realm opmaachen", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "Welt zerécksetzen", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "Astellungen", "mco.configure.world.buttons.subscription": "Abonnement", "mco.configure.world.buttons.switchminigame": "Minispill wiesselen", "mco.configure.world.close.question.line1": "<PERSON>äin Realm wäert net méi verfügbar sinn.", "mco.configure.world.close.question.line2": "Bas du <PERSON>cher, dass du dat maache w<PERSON><PERSON>?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "<PERSON> g<PERSON>tt <PERSON> …", "mco.configure.world.commandBlocks": "Be<PERSON><PERSON>blé<PERSON>", "mco.configure.world.delete.button": "Realm läschen", "mco.configure.world.delete.question.line1": "<PERSON>äin Realm gëtt fir ëmmer geläscht", "mco.configure.world.delete.question.line2": "Bas du <PERSON>cher, dass du dat maache w<PERSON><PERSON>?", "mco.configure.world.description": "Beschreiwung vum Realm", "mco.configure.world.edit.slot.name": "Numm vun der Welt", "mco.configure.world.edit.subscreen.adventuremap": "E puer Astellunge sinn desaktivéiert, well déi aktuell Welt eng Aventure ass", "mco.configure.world.edit.subscreen.experience": "E puer Astellunge sinn desaktivéiert, well déi aktuell Welt eng Erfahrungswelt ass", "mco.configure.world.edit.subscreen.inspiration": "Ee puer Astellunge sinn desaktivéiert well déng Welt eng Inspiratiounswelt ass", "mco.configure.world.forceGameMode": "Spillmo<PERSON> er<PERSON>", "mco.configure.world.invite.narration": "Du hues %s nei Invitatioun(en)", "mco.configure.world.invite.profile.name": "Numm", "mco.configure.world.invited": "<PERSON><PERSON><PERSON>", "mco.configure.world.invited.number": "Invi<PERSON>iert (%s)", "mco.configure.world.invites.normal.tooltip": "Normale Spiller", "mco.configure.world.invites.ops.tooltip": "Operator", "mco.configure.world.invites.remove.tooltip": "Läschen", "mco.configure.world.leave.question.line1": "Wanns du dëse Realm verléisst, kanns du et net méi betrieden, bis du erëm ageluede g<PERSON>ss", "mco.configure.world.leave.question.line2": "Bas du <PERSON>cher, dass du dat maache w<PERSON><PERSON>?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "Plaz", "mco.configure.world.minigame": "Aktuell: %s", "mco.configure.world.name": "Numm vum Realm", "mco.configure.world.opening": "Realm gëtt opge<PERSON>ach …", "mco.configure.world.players.error": "Et existé<PERSON>t kee <PERSON> mat dësem Numm", "mco.configure.world.players.inviting": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> in<PERSON>  …", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PvP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Deng Welt gëtt regeneréiert an deng aktuell Welt wärt verluer goen", "mco.configure.world.reset.question.line2": "Bas du s<PERSON>cher, dass du weiderfueren wëlls?", "mco.configure.world.resourcepack.question": "Du benéidegs e benotzerdefinéierte Ressourcepak fir op dësem Real spillen zu kënnen.\n\nWëlls du et eroflueden an domat spillen?", "mco.configure.world.resourcepack.question.line1": "D'Welt erfuerdert ee bestëmmte Ressourcepak.", "mco.configure.world.resourcepack.question.line2": "Wëlls du et automatesch eroflueden an installéiere fir ze spillen?", "mco.configure.world.restore.download.question.line1": "D'Welt gëtt erofgelueden an zu dengen Eenzelspiller Welte bäigefügt.", "mco.configure.world.restore.download.question.line2": "W<PERSON><PERSON> du weiderfueren?", "mco.configure.world.restore.question.line1": "Deng Welt gëtt op de Stand vum '%s' (%s) rëm hiergestallt", "mco.configure.world.restore.question.line2": "Bas du <PERSON>cher, dass du dat maache w<PERSON><PERSON>?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "Astellungen", "mco.configure.world.slot": "Welt %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "<PERSON>äin Realm gëtt op eng aner Welt gewiesselt", "mco.configure.world.slot.switch.question.line2": "Bas du <PERSON>cher, dass du dat maache w<PERSON><PERSON>?", "mco.configure.world.slot.tooltip": "Welt wiesselen", "mco.configure.world.slot.tooltip.active": "Bäitrieden", "mco.configure.world.slot.tooltip.minigame": "<PERSON><PERSON>sel zum Mini-Spill", "mco.configure.world.spawnAnimals": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawnMonsters": "Monster spawnen", "mco.configure.world.spawnNPCs": "Duerfbewunner", "mco.configure.world.spawnProtection": "Spawnpunktschutz", "mco.configure.world.spawn_toggle.message": "<PERSON><PERSON> du dës <PERSON> au<PERSON>, ginn all firhanden Objete vun dësem Typ ewechgemaach", "mco.configure.world.spawn_toggle.message.npc": "<PERSON><PERSON> du dë<PERSON>, ginn all firhanden Objete vun dë<PERSON>, w<PERSON><PERSON>, e<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawn_toggle.title": "Warnung!", "mco.configure.world.status": "Status", "mco.configure.world.subscription.day": "<PERSON><PERSON>", "mco.configure.world.subscription.days": "<PERSON><PERSON>", "mco.configure.world.subscription.expired": "Ofgelaf", "mco.configure.world.subscription.extend": "Abonnement verlängeren", "mco.configure.world.subscription.less_than_a_day": "<PERSON>er wei een Dag", "mco.configure.world.subscription.month": "Mount", "mco.configure.world.subscription.months": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.recurring.daysleft": "Automatesch verlängert an", "mco.configure.world.subscription.recurring.info": "Ännerungen un dengem Realms-Abonnement, wéi d'Verlängerung vun der Lafzäit oder der Desaktivéierung vun der reegelméissegen Ofrechnung, ginn <PERSON>t vun dengem nächsten Ofrechnungstermin berécksiichtegt.", "mco.configure.world.subscription.remaining.days": "%1$s Dag/Deeg", "mco.configure.world.subscription.remaining.months": "%1$s Mount/Méint", "mco.configure.world.subscription.remaining.months.days": "%1$s Mount/Méint, %2$s Dag/Deeg", "mco.configure.world.subscription.start": "Startzäitpunkt", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "Verbleiwend Zäit", "mco.configure.world.subscription.title": "Abonnement Informatiounen", "mco.configure.world.subscription.unknown": "Onbekannt", "mco.configure.world.switch.slot": "<PERSON>lt erstellen", "mco.configure.world.switch.slot.subtitle": "D'Welt ass eidel, w.e.g. wiel aus wat ze maachen ass", "mco.configure.world.title": "Realm konfiguréieren:", "mco.configure.world.uninvite.player": "<PERSON> du s<PERSON>cher, dass du d'Invitatioun vu(n) %s widderruffe wëlls?", "mco.configure.world.uninvite.question": "Ba<PERSON> <PERSON>, dass du dëse Spiller ausluede wëlls:", "mco.configure.worlds.title": "Welten", "mco.connect.authorizing": "<PERSON><PERSON><PERSON> …", "mco.connect.connecting": "Mam <PERSON> verbannen …", "mco.connect.failed": "Konnt net mam Realm verbannen", "mco.connect.region": "Server region: %s", "mco.connect.success": "Fäerdeg", "mco.create.world": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.create.world.error": "Du muss een Numm uginn!", "mco.create.world.failed": "Weltkreéierung feelgeschloen!", "mco.create.world.reset.title": "Welt gëtt erstallt ...", "mco.create.world.skip": "Iwwersprangen", "mco.create.world.subtitle": "Wiel eng Welt aus déi op däin neie Realm geluede soll ginn (optional)", "mco.create.world.wait": "<PERSON> gëtt erstallt …", "mco.download.cancelled": "Eroflueden ofgebrach", "mco.download.confirmation.line1": "D'Welt déi du erofluede wëlls ass méi grouss wei %s", "mco.download.confirmation.line2": "Du wärst déi Welt net nach eng kéier op Realms eropluede kennen", "mco.download.confirmation.oversized": "<PERSON><PERSON><PERSON><PERSON>, déi du erofluede wël<PERSON>, ass méi grouss wéi %s.", "mco.download.done": "Eroflueden ofgeschloss", "mco.download.downloading": "<PERSON><PERSON><PERSON><PERSON><PERSON> vun", "mco.download.extracting": "Entpaken", "mco.download.failed": "Eroflueden ass feelgeschloen", "mco.download.percent": "%s %%", "mco.download.preparing": "Erofluede vir<PERSON>eeden", "mco.download.resourcePack.fail": "Erofluede vum Ressourcepak feelgeschloen!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Neiste Welt eroflueden", "mco.error.invalid.session.message": "Probéier w.e.g. Minecraft nei ze starten", "mco.error.invalid.session.title": "Ongülteg <PERSON>", "mco.errorMessage.6001": "Vereelzte Client", "mco.errorMessage.6002": "Notzungsbedéngungen net akzeptéiert", "mco.errorMessage.6003": "Downloadlimit areecht", "mco.errorMessage.6004": "Uploadlimit areecht", "mco.errorMessage.6005": "<PERSON>lt ges<PERSON>ert", "mco.errorMessage.6006": "D'Welt ass net méi aktuell", "mco.errorMessage.6007": "<PERSON><PERSON><PERSON> ass a ze ville Realms", "mco.errorMessage.6008": "Ongültege Realm-Numm", "mco.errorMessage.6009": "Ongülteg Realm-Descriptioun", "mco.errorMessage.connectionFailure": "<PERSON><PERSON>, prob<PERSON><PERSON> w.e.g. méi spéit nach eng kéier.", "mco.errorMessage.generic": "<PERSON> <PERSON>er ass geschitt: ", "mco.errorMessage.initialize.failed": "Realm-Initialiséierung feelgeschloen", "mco.errorMessage.noDetails": "<PERSON><PERSON>chreiwung firhanden", "mco.errorMessage.realmsService": "E Feeler ass opgetrueden (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Connectioun zu Realms konnt net hiergestallt ginn: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Kompatibel Versioun konnt net iwwerpréif ginn; erhalen Äntwert: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>", "mco.errorMessage.serviceBusy": "Realms ass am Moment beschäftegt.\nProbéiert wannechgelift an e puer Minutten <PERSON>ech nach eng Kéier mat Ärem Realm ze verbannen.", "mco.gui.button": "Knäppchen", "mco.gui.ok": "Ok", "mco.info": "Hiweis!", "mco.invited.player.narration": "%s gouf invi<PERSON><PERSON>t", "mco.invites.button.accept": "<PERSON><PERSON><PERSON><PERSON>", "mco.invites.button.reject": "<PERSON><PERSON><PERSON>", "mco.invites.nopending": "<PERSON>g ausstoend Invitatiounen!", "mco.invites.pending": "<PERSON>éi <PERSON>(en)!", "mco.invites.title": "Ausstoend Invitatiounen", "mco.minigame.world.changeButton": "<PERSON><PERSON>", "mco.minigame.world.info.line1": "Deng Welt gëtt temporär mat engem Minispill ersat!", "mco.minigame.world.info.line2": "Du kanns herno zu denger Welt zeréck goen ouni eppes ze verléieren.", "mco.minigame.world.noSelection": "<PERSON><PERSON> w.e.g. <PERSON><PERSON><PERSON>", "mco.minigame.world.restore": "Minispill gëtt gestoppt …", "mco.minigame.world.restore.question.line1": "D'Minispill gëtt gestoppt an däi Realm erëm hiergestallt.", "mco.minigame.world.restore.question.line2": "Bas du <PERSON>cher, dass du dat maache w<PERSON><PERSON>?", "mco.minigame.world.selected": "Ausgewielte Minispill:", "mco.minigame.world.slot.screen.title": "Welt g<PERSON>tt gew<PERSON>t …", "mco.minigame.world.startButton": "Wiesselen", "mco.minigame.world.starting.screen.title": "Minispill gëtt gestart …", "mco.minigame.world.stopButton": "Minispill stoppen", "mco.minigame.world.switch.new": "<PERSON><PERSON> w<PERSON>?", "mco.minigame.world.switch.title": "Minispill wiesselen", "mco.minigame.world.title": "<PERSON> zu Minispill wiesselen", "mco.news": "Realms Neiegkeeten", "mco.notification.dismiss": "Zoumaachen", "mco.notification.transferSubscription.buttonText": "Elo iwwerdroen", "mco.notification.transferSubscription.message": "Java-Realms-Abonementer ginn op de Microsoft Store beweegt. Looss däin Abonement net auslafen!\nElo iwwerdroen an 30 Deeg Realms gratis kréien.\nGéi zum Profil op minecraft.net, fir däin Abonnement ze iwwerdroen.", "mco.notification.visitUrl.buttonText.default": "<PERSON>", "mco.notification.visitUrl.message.default": "W.e.g. be<PERSON><PERSON> de follgende Link", "mco.onlinePlayers": "Spiller online", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "<PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON>", "mco.reset.world.experience": "Erfahrungswelten", "mco.reset.world.generate": "<PERSON><PERSON>", "mco.reset.world.inspiration": "Inspiratiounswelten", "mco.reset.world.resetting.screen.title": "Welt gëtt zeréckgesat ...", "mco.reset.world.seed": "Seed (Optional)", "mco.reset.world.template": "Realm-Virlagen", "mco.reset.world.title": "Welt zerécksetzen", "mco.reset.world.upload": "Welt eroplueden", "mco.reset.world.warning": "Dat ersetzt déi aktuell Welt vun dengem Realm fir ëmmer!", "mco.selectServer.buy": "<PERSON><PERSON> <PERSON>!", "mco.selectServer.close": "Zoumaachen", "mco.selectServer.closed": "Zouene Realm", "mco.selectServer.closeserver": "Realm zoumaachen", "mco.selectServer.configure": "Konfiguréieren", "mco.selectServer.configureRealm": "Realm konfiguréieren", "mco.selectServer.create": "Realm erstellen", "mco.selectServer.create.subtitle": "<PERSON><PERSON> aus, wei eng Welt op däin neie Realm geluede gi soll", "mco.selectServer.expired": "Ofgelafene Realm", "mco.selectServer.expiredList": "<PERSON><PERSON><PERSON> ass ofgelaf", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Abonn<PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "Deng Testzäit ass eriwwer", "mco.selectServer.expires.day": "Leeft an engem Dag of", "mco.selectServer.expires.days": "Leeft an %s Deeg of", "mco.selectServer.expires.soon": "<PERSON><PERSON> gl<PERSON>ich of", "mco.selectServer.leave": "<PERSON> verl<PERSON>sen", "mco.selectServer.loading": "Realm-<PERSON><PERSON><PERSON><PERSON> g<PERSON>tt <PERSON>en", "mco.selectServer.mapOnlySupportedForVersion": "Dës Welt ass net ënnerstëtzt an %s", "mco.selectServer.minigame": "Minispill:", "mco.selectServer.minigameName": "Minispill: %s", "mco.selectServer.minigameNotSupportedInVersion": "Dëst Minispill kann net mat %s gespillt ginn", "mco.selectServer.noRealms": "<PERSON> s<PERSON>t kee <PERSON> ze hunn. <PERSON><PERSON> e <PERSON> b<PERSON>, fir mat denge <PERSON> ze spillen.", "mco.selectServer.note": "Notiz:", "mco.selectServer.open": "Oppene Realm", "mco.selectServer.openserver": "Realm opmaachen", "mco.selectServer.play": "<PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms ass ee sécheren an e<PERSON><PERSON>che Wee, Minecraft mat bis zu zéng <PERSON> gläichzäiteg ze spillen. Et gi Minispiller an extra Welten! Nëmmen de Besëtzer vum Realm muss bezuelen.", "mco.selectServer.purchase": "Realm grënnen", "mco.selectServer.trial": "Testen!", "mco.selectServer.uninitialized": "Realm kreéieren!", "mco.snapshot.createSnapshotPopup.text": "Du bass em gaangen, e gratis <PERSON> fir Entwécklungsversiounen ze kreéieren, de mat dengem bezuelungsflichtege Realms-Abonnement verknüpft gëtt. Dësen néien Entwécklungsversiounen-Realm kann sou laang genotzt ginn, wéi de bezuelungsflichtegen Abonnement aktiv ass. Un dengem bezuelte Realm ännert sech doduerch näischt.", "mco.snapshot.createSnapshotPopup.title": "Entwécklungsversioun-Realm kreéieren?", "mco.snapshot.creating": "Entwécklungsversioun-Realm gëtt kre<PERSON>t …", "mco.snapshot.description": "Verknüpft mat \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Du benéidegs d'Versioun %s, fir dëse Realm bäizetrieden", "mco.snapshot.friendsRealm.upgrade": "%s muss de <PERSON>, fir dat du op dëser Versioun drop spille kanns", "mco.snapshot.paired": "<PERSON><PERSON><PERSON>wécklungsversioun-Realm ass mat \"%s\" verknüpft", "mco.snapshot.parent.tooltip": "Benotz d'aktuell Vollversioun, fir op dësem Realm ze spillen", "mco.snapshot.start": "<PERSON><PERSON><PERSON>cklungsversioun-Realm ufänken", "mco.snapshot.subscription.info": "<PERSON><PERSON><PERSON> ass e Realm fir Entwécklungsversiounen, de mat dem Abonnement vun dengem Realm \"%s\" verbonnen ass. En bleift sou laang aktiv, wéi den domat verbonnene Realm aktiv ass.", "mco.snapshot.tooltip": "Entwécklungsversiounen-Realms verschafen dir en Abléck an zoukënfteg Versioune vu Minecraft, déi méiglecherweis néi Spillelementer a aner Ännerungen enthalen.\n\nDeng normale Realms kanns du an der aktueller Vollversioun opruffen.", "mco.snapshotRealmsPopup.message": "Realms sinn elo och an Entwécklungsversioune vun 23w41a un verfügbar. All Realms-Abonnement gëtt em e gratis Realm fir Entwécklungsversiounen ergänzt, de vun dengem normale Java-Realm getrennt ass!", "mco.snapshotRealmsPopup.title": "Realms sinn elo an Entwécklungsversioune verfügbar", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON><PERSON> gewuer ginn", "mco.template.button.publisher": "Editeur", "mco.template.button.select": "Auswielen", "mco.template.button.trailer": "Virs<PERSON><PERSON>", "mco.template.default.name": "Weltvirlag", "mco.template.info.tooltip": "Internetsäit vum Kreateur", "mco.template.name": "Virlag", "mco.template.select.failure": "D'Lëscht mat den Inhalter vun dëser Kategorie konnten net ofgeruff ginn. W.e.g. iwwerpréif deng Internetverbindung oder probéier méi spéit nach eng kéier.", "mco.template.select.narrate.authors": "Auteuren: %s", "mco.template.select.narrate.version": "Versioun %s", "mco.template.select.none": "Oops, et gesäit esou aus wei wann dës Inhaltskategorie aktuell eidel wär. Kuck méi spéit nach eng keier Laanscht oder falls du de Createur bass %s.", "mco.template.select.none.linkTitle": "iwwerlee dir fir selwer eppes anzereechen", "mco.template.title": "Weltvirlagen", "mco.template.title.minigame": "Minispiller", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "mco.terms.buttons.agree": "Acceptéieren", "mco.terms.buttons.disagree": "<PERSON><PERSON><PERSON><PERSON>", "mco.terms.sentence.1": "<PERSON>ch akzeptéieren d'Minecraft Realms", "mco.terms.sentence.2": "Notzungskonditiounen", "mco.terms.title": "Realms Notzungskonditiounen", "mco.time.daysAgo": "Virun %1$s Dag/Deeg", "mco.time.hoursAgo": "Virun %1$s Stonn(en)", "mco.time.minutesAgo": "Virun %1$s Minutt(en)", "mco.time.now": "grad elo", "mco.time.secondsAgo": "Virun %1$s Sekonn(en)", "mco.trial.message.line1": "<PERSON><PERSON><PERSON> du däin eegene Realm kréien?", "mco.trial.message.line2": "Klick hei fir méi Informatiounen!", "mco.upload.button.name": "Eroplueden", "mco.upload.cancelled": "Eroplueden ofgebrach", "mco.upload.close.failure": "<PERSON>äin Realm konnt net zou gemaach ginn, prob<PERSON><PERSON> w.e.g. méi spéit nach eng kéier", "mco.upload.done": "Fäerdeg eropgelueden", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Eroplueden ass feelgeschloen (%s)", "mco.upload.failed.too_big.description": "D'ausgewielt Welt ass ze grouss. D'maximal zoulässeg Géisst bedréit %s.", "mco.upload.failed.too_big.title": "Welt ze grouss", "mco.upload.hardcore": "Hardcore Welte kennen net eropgeluede ginn!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Deng Weltdate gi preparé<PERSON>t", "mco.upload.select.world.none": "<PERSON>g <PERSON>zelspiller Welte fonnt!", "mco.upload.select.world.subtitle": "W.e.g. wiel eng Eenzelspiller Welt aus fir eropzelueden", "mco.upload.select.world.title": "Welt eroplueden", "mco.upload.size.failure.line1": "'%s' ass ze grouss!", "mco.upload.size.failure.line2": "Et ass %s. Déi maximal erlaabte Gréisst ass %s.", "mco.upload.uploading": "'%s' g<PERSON><PERSON> er<PERSON>", "mco.upload.verifying": "Deng Welt gëtt iwwerpréift", "mco.version": "Versioun: %s", "mco.warning": "Warnung!", "mco.worldSlot.minigame": "Minispill", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Ausloggen", "menu.feedback": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> …", "menu.feedback.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.game": "Spillmenü", "menu.modded": " (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "menu.multiplayer": "Multispiller", "menu.online": "Minecraft Realms", "menu.options": "Op<PERSON><PERSON><PERSON> ...", "menu.paused": "<PERSON><PERSON><PERSON> gepaust", "menu.playdemo": "Demowelt spillen", "menu.playerReporting": "<PERSON><PERSON><PERSON> melden", "menu.preparingSpawn": "Spawnberäich gëtt preparéiert: %s%%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "<PERSON><PERSON><PERSON> ve<PERSON><PERSON><PERSON>", "menu.reportBugs": "<PERSON><PERSON>", "menu.resetdemo": "Demowelt zerécksetzen", "menu.returnToGame": "<PERSON>er<PERSON>ck an d'Spill", "menu.returnToMenu": "Sp<PERSON><PERSON><PERSON> an d'<PERSON><PERSON><PERSON> verl<PERSON>sen", "menu.savingChunks": "Chunks gi gespäichert", "menu.savingLevel": "Welt gëtt gespäichert", "menu.sendFeedback": "Feedback of<PERSON><PERSON>", "menu.server_links": "Server-<PERSON><PERSON> …", "menu.server_links.title": "Server-<PERSON><PERSON>", "menu.shareToLan": "Am LAN opmaachen", "menu.singleplayer": "Eenzelspiller", "menu.working": "<PERSON><PERSON><PERSON> beaar<PERSON>cht ...", "merchant.deprecated": "D'Awunner fëllen hier Bestänn bis zu zweemol am Dag op.", "merchant.level.1": "Noviz", "merchant.level.2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON><PERSON>", "merchant.level.4": "Expert", "merchant.level.5": "<PERSON><PERSON><PERSON><PERSON>", "merchant.title": "%s – %s", "merchant.trades": "Offeren", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Dréck %1$s fir erof ze klammen", "multiplayer.applyingPack": "Ressource Pack uwenden", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "D'Authentifizéierungsserver sinn am Moment net erreechbar. W.e.g. prob<PERSON><PERSON> et méi spéit nach eng kéier!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "<PERSON> goufs vun dësem Server g<PERSON><PERSON><PERSON>", "multiplayer.disconnect.banned.expiration": "Deng Spär gëtt den %s opgehuewen", "multiplayer.disconnect.banned.reason": "<PERSON> goufs vun dësem Server gespaart.\nGrond: %s", "multiplayer.disconnect.banned_ip.expiration": "\nDéng <PERSON>päer gëtt den %s opgehuewen", "multiplayer.disconnect.banned_ip.reason": "Deng IP-Ad<PERSON> gouf op dësem Server gespaart. \nGrond: %s", "multiplayer.disconnect.chat_validation_failed": "Ausgefall Chat Message ze validéieren", "multiplayer.disconnect.duplicate_login": "Du hues dech mat engem zweete Client ageloggt", "multiplayer.disconnect.expired_public_key": "Den öffentleche Profil vun dengem Schlëssel ass ofgelaf. Kuck ob op dengem Computer déi richteg Zäit agestallt ass, a probéier d'Spill nei ze starten.", "multiplayer.disconnect.flying": "Fléien ass op dësem Server net erlaabt", "multiplayer.disconnect.generic": "Verbindung getrennt", "multiplayer.disconnect.idling": "Du waars ze laang inaktiv!", "multiplayer.disconnect.illegal_characters": "Ongülteg Zeechen am Chat", "multiplayer.disconnect.incompatible": "Client net kompatibel! Benotz w.e.g. %s", "multiplayer.disconnect.invalid_entity_attacked": "Et gouf versicht een ongültegen Objet unzegräifen", "multiplayer.disconnect.invalid_packet": "De <PERSON> huet en ongültege Pak geschéckt", "multiplayer.disconnect.invalid_player_data": "Ongülteg Spillerdaten", "multiplayer.disconnect.invalid_player_movement": "Ongültege Pak zur Spillerbeweegung empfaangen", "multiplayer.disconnect.invalid_public_key_signature": "Ongëlteg Ënnerschrëft fir dem Profil säi Public Key. Probéier d'Spill nei ze starten.", "multiplayer.disconnect.invalid_public_key_signature.new": "Ongëlteg Signatur fir dem Profil säin ëffentleche Schlëssel. Probéier d'Spill nei ze starten.", "multiplayer.disconnect.invalid_vehicle_movement": "Ongültege Pak zur Gefierbeweegung empfaangen", "multiplayer.disconnect.ip_banned": "Deng IP-<PERSON><PERSON> gouf op dësem Server gespaart", "multiplayer.disconnect.kicked": "Een Operator huet dech vum Server geworf", "multiplayer.disconnect.missing_tags": "Onvollstännegen Etiketten-Saz vum Server empfaangen.\nWann ech gelift de Server Besëtzer kontaktéieren.", "multiplayer.disconnect.name_taken": "<PERSON><PERSON>m ass scho verginn", "multiplayer.disconnect.not_whitelisted": "Du bass net op der Whitelist vum Server!", "multiplayer.disconnect.out_of_order_chat": "Falschen Chat-Pak kritt. Go<PERSON> d'Auer op dengem Computer ëmgestallt?", "multiplayer.disconnect.outdated_client": "Inkompatible Client! Benotz w. e. g. %s", "multiplayer.disconnect.outdated_server": "Client onkompatibel! Benotz %s", "multiplayer.disconnect.server_full": "De Server ass voll!", "multiplayer.disconnect.server_shutdown": "Server zouge<PERSON>", "multiplayer.disconnect.slow_login": "D'Alog<PERSON> huet ze laang gedauert", "multiplayer.disconnect.too_many_pending_chats": "Ze vill unconfirm<PERSON><PERSON>t Chat <PERSON>n", "multiplayer.disconnect.transfers_disabled": "De Server akzeptéiert keng Iwwerdroung", "multiplayer.disconnect.unexpected_query_response": "<PERSON><PERSON><PERSON><PERSON> ben<PERSON>zerdefinéiert Donnéeë vum Client empfaangen", "multiplayer.disconnect.unsigned_chat": "<PERSON>t <PERSON>et kritt mat fehlend oder ongëlteg Ënnerschrëft.", "multiplayer.disconnect.unverified_username": "Benotzernumm konnt net verifizéiert ginn!", "multiplayer.downloadingStats": "Statistike ginn ofgeruff ...", "multiplayer.downloadingTerrain": "Landschaft gëtt gelueden ...", "multiplayer.lan.server_found": "Neie Server fonnt: %s", "multiplayer.message_not_delivered": "Chatnoriicht konnt net zougestallt ginn, w.e.g. Logdateie vum Server consultéieren: %s", "multiplayer.player.joined": "%s ass dem S<PERSON>ll bäigetrueden", "multiplayer.player.joined.renamed": "%s (fréier bekannt als %s) ass dem Spill bäigetrueden", "multiplayer.player.left": "%s huet d'<PERSON>pill verlooss", "multiplayer.player.list.hp": "%s LP", "multiplayer.player.list.narration": "Spiller online: %s", "multiplayer.requiredTexturePrompt.disconnect": "Server erfuerdert ee benotzerdefinéierte Ressourcëpack", "multiplayer.requiredTexturePrompt.line1": "Dëse Server er<PERSON><PERSON><PERSON> d'Benotzung vun engem benotzerdefinéierte Ressourcëpack.", "multiplayer.requiredTexturePrompt.line2": "Oflehnung vun dësem personaliséierte Ressource Pack trennt Iech vun dësem Server.", "multiplayer.socialInteractions.not_available": "Sozial Interaktioune sinn nëmmen a Multispillerwelte verfügbar", "multiplayer.status.and_more": "… a(n) %s weider …", "multiplayer.status.cancelled": "Ofgebrach", "multiplayer.status.cannot_connect": "Et konnt net mam Server verbonne ginn", "multiplayer.status.cannot_resolve": "D'Serveradress konnt net opgeléist ginn", "multiplayer.status.finished": "Fäerdeg", "multiplayer.status.incompatible": "Versioun net kompatibel!", "multiplayer.status.motd.narration": "Noriicht vum Dag: %s", "multiplayer.status.no_connection": "(<PERSON><PERSON>)", "multiplayer.status.old": "Vereelzt", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping: %s <PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.pinging": "<PERSON> gëtt ausgeféiert ...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s vun %s Spiller online", "multiplayer.status.quitting": "Verloossen", "multiplayer.status.request_handled": "<PERSON><PERSON><PERSON> gouf beaar<PERSON>", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Status ouni Ufro erhal", "multiplayer.status.version.narration": "Server Versioun: %s", "multiplayer.stopSleeping": "<PERSON><PERSON> ve<PERSON><PERSON><PERSON>", "multiplayer.texturePrompt.failure.line1": "Serverressourcepack konnt net ugewannt ginn", "multiplayer.texturePrompt.failure.line2": "All Funktionalitéit déi personaliséiert Ressourcen erfuerdert funktionnéieren net wéi erwaart", "multiplayer.texturePrompt.line1": "Dëse Server proposéiert een eegene Ressourcepack.", "multiplayer.texturePrompt.line2": "Wëlls du et eroflueden an automagesch installéieren?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nMessage vum Server:\n%s", "multiplayer.title": "Multispillermodus", "multiplayer.unsecureserver.toast": "D'Noriichte déi op dësem Server versch<PERSON>t ginn, goufe vl<PERSON>icht ëmgeännert a musse net zwangsméisseg déi ursprünglech Noricht erëmginn.", "multiplayer.unsecureserver.toast.title": "Chat Messagen kënnen net iwwerpréift ginn", "multiplayerWarning.check": "<PERSON><PERSON><PERSON> net méi uweisen", "multiplayerWarning.header": "Opgepasst: Drëtt-Partei Online Spill", "multiplayerWarning.message": "Opgepasst: D'Online Spill gëtt vun Drëttpartei-Serveren ugebueden, déi Mojang Studios oder Microsoft weder gehéieren, nach vun hinne bedriwwen oder iwwerwaacht ginn. Wärend dem Online Spill kanns du onmoderéiert Chat-Noriichten oder aner Zorte vu Benotzer generéiertem Inhalt ausgesat ginn, dee vläicht net fir jidderee passend ass.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Maustast: %s", "narration.button.usage.focused": "Dr<PERSON><PERSON>t Enter fir ze aktivéieren", "narration.button.usage.hovered": "Lénks Klick fir ze aktivéieren", "narration.checkbox": "Checkbox: %s", "narration.checkbox.usage.focused": "Dr<PERSON><PERSON>t Enter fir ze aktivéieren", "narration.checkbox.usage.hovered": "Lénks Klick fir ze aktivéieren", "narration.component_list.usage": "<PERSON><PERSON><PERSON>t Tab fir op dat nächst Element ze navigéieren", "narration.cycle_button.usage.focused": "Dréckt Enter fir op %s ze wiesselen", "narration.cycle_button.usage.hovered": "Lénks Klick fir op %s ze wiesselen", "narration.edit_box": "Inputfeld änneren: %s", "narration.item": "Item: %s", "narration.recipe": "Rezept fir %s", "narration.recipe.usage": "Lénks Klick fir auszewielen", "narration.recipe.usage.more": "<PERSON><PERSON><PERSON><PERSON>, fir méi Rezepter ze weisen", "narration.selection.usage": "<PERSON><PERSON><PERSON>t Tasten no uewen an no ënnen fir an en anert Element ze goen", "narration.slider.usage.focused": "<PERSON><PERSON><PERSON>t Tastaturknäpp no lénks oder riets fir de Wäert ze änneren", "narration.slider.usage.hovered": "Zitt de Slider fir de Wäert ze änneren", "narration.suggestion": "Virschlag %s vun %s ausgewielt: %s", "narration.suggestion.tooltip": "Virschlag %s vun %s ausgewielt: %s (%s)", "narration.suggestion.usage.cycle.fixed": "<PERSON><PERSON><PERSON>, fir zum nächste Virschlag ze bliederen", "narration.suggestion.usage.cycle.hidable": "<PERSON><PERSON><PERSON>, fir zum nächste Virschlag ze bliederen, oder Escape, fir d'Virschläger ze verloosen", "narration.suggestion.usage.fill.fixed": "<PERSON><PERSON><PERSON>, fir den Virschlag ze verwenden", "narration.suggestion.usage.fill.hidable": "<PERSON><PERSON><PERSON>, fir dëse Virschlag ze benotzen, oder Escape, fir di Virschläger ze verloossen", "narration.tab_navigation.usage": "<PERSON><PERSON><PERSON> an <PERSON>'Tabulator<PERSON>, fir tëschent de Reiteren ze wiesselen", "narrator.button.accessibility": "Barrièrefräiheet", "narrator.button.difficulty_lock": "Schwieregkeetsspär", "narrator.button.difficulty_lock.locked": "<PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.unlocked": "Fräigestallt", "narrator.button.language": "<PERSON><PERSON><PERSON><PERSON>", "narrator.controls.bound": "%s ass %s zougewisen", "narrator.controls.reset": "%s op Standard zerécksetzen", "narrator.controls.unbound": "%s ass net zougewisen", "narrator.joining": "Bäitrieden", "narrator.loading": "Lueden: %s", "narrator.loading.done": "Fäerdeg", "narrator.position.list": "Lëschtenzeil %s aus %s", "narrator.position.object_list": "Zeilenelement %s aus %s", "narrator.position.screen": "Bildschiermelement %s aus %s", "narrator.position.tab": "Reiter %s aus %s ausgewielt", "narrator.ready_to_play": "Prett ze spillen", "narrator.screen.title": "Haaptmenü", "narrator.screen.usage": "<PERSON><PERSON><PERSON> Mauszeiger oder d'Tabulatortast fir en Element auszewielen", "narrator.select": "Ausgewielt: %s", "narrator.select.world": "%s ausgewielt, als läscht gespillt: %s, %s, %s, Versioun: %s", "narrator.select.world_info": "Ausgewielt %s, zuslescht gespillt: %s, %s", "narrator.toast.disabled": "Sproochausgab aus", "narrator.toast.enabled": "Sproo<PERSON><PERSON>gab un", "optimizeWorld.confirm.description": "D'Welt gëtt duerch Späichere vun den Donnéeën am neiste Format optiméiert. Jee no Gréisst vun der Welt kann dat ganz laang daueren. Eng kéier duerchgefouert kann deng Welt am Spill méi séier sinn, kann awer net méi mat méi ale Versioune vum Spill gespillt ginn. Bas du sécher, dass du fortfuere wëlls?", "optimizeWorld.confirm.proceed": "<PERSON>pie kreéieren an optiméieren", "optimizeWorld.confirm.title": "Welt optiméieren", "optimizeWorld.info.converted": "Aktualiséiert Chunks: %s", "optimizeWorld.info.skipped": "Iwwerspronge Chunks: %s", "optimizeWorld.info.total": "Gesamt Chunks: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s %%", "optimizeWorld.stage.counting": "Chunks gi gezielt …", "optimizeWorld.stage.failed": "Feelgeschloen! :(", "optimizeWorld.stage.finished": "Finaliséieren …", "optimizeWorld.stage.finished.chunks": "Chunk-Aktualiséierung gëtt finaliséiert …", "optimizeWorld.stage.finished.entities": "Objetaktialiséierung gëtt ofgeschloss  …", "optimizeWorld.stage.finished.poi": "Zielpunktaktualiséierung gëtt ofgeschloss  …", "optimizeWorld.stage.upgrading": "All Chunks ginn aktualiséiert  …", "optimizeWorld.stage.upgrading.chunks": "Chunks ginn aktualiséiert  …", "optimizeWorld.stage.upgrading.entities": "All Objete ginn aktualiséiert  …", "optimizeWorld.stage.upgrading.poi": "All Zielpunkter ginn aktualiséiert  …", "optimizeWorld.title": "Welt '%s' g<PERSON><PERSON> optiméiert", "options.accessibility": "Barrièrefräiheet …", "options.accessibility.high_contrast": "<PERSON><PERSON><PERSON>", "options.accessibility.high_contrast.error.tooltip": "De Ressourcepak fir héige Kontrast ass net verfügbar.", "options.accessibility.high_contrast.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de Kontrast vun Elementer vun der Benotzeriwwerfläch.", "options.accessibility.high_contrast_block_outline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.high_contrast_block_outline.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de Kontrast vun den Ëmrësslinnen vum uviséierte Block.", "options.accessibility.link": "Accessibilitéits Guide", "options.accessibility.menu_background_blurriness": "Menühannergrond-Onschäerft", "options.accessibility.menu_background_blurriness.tooltip": "Stéiert d'Verschwommenheet vun de Hannergrënn.", "options.accessibility.narrator_hotkey": "Sproochausgab-<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "<PERSON><PERSON>éiglecht et, d'Sproo<PERSON>usgab mat [⌘] + [B] un- an auszeschalten.", "options.accessibility.narrator_hotkey.tooltip": "Erméiglecht et, d'Sproochausgab mat [Ctrl] + [B] un- an auszeschalten.", "options.accessibility.panorama_speed": "Panorama-Bildlafgeschwindegkeet", "options.accessibility.text_background": "Texthannergrond", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background_opacity": "Texthg. <PERSON>", "options.accessibility.title": "Barrièrefräiheetsastellungen", "options.allowServerListing": "Server-Op<PERSON>ëschtun<PERSON>", "options.allowServerListing.tooltip": "Servere kënnen Online-Spiller als Deel vun hirem ëffentleche Status oplëschten.\nWann dës Optioun desaktivéiert ass, g<PERSON>tt däin Numm net op esou Lëschten ugewisen.", "options.ao": "<PERSON><PERSON>", "options.ao.max": "Maximum", "options.ao.min": "Minimum", "options.ao.off": "Aus", "options.attack.crosshair": "<PERSON><PERSON><PERSON><PERSON>", "options.attack.hotbar": "Hotbar", "options.attackIndicator": "Attackenindicateur", "options.audioDevice": "Apparat", "options.audioDevice.default": "System Virastellung", "options.autoJump": "Automatesch sprangen", "options.autoSuggestCommands": "Befeelsvirschléi", "options.autosaveIndicator": "Autospäicher Indicateur", "options.biomeBlendRadius": "Biomiwwergang", "options.biomeBlendRadius.1": "Aus (Am Schnellsten)", "options.biomeBlendRadius.11": "11x11 (Extrem)", "options.biomeBlendRadius.13": "13x13 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.15": "15x15 (Maximal)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (Normal)", "options.biomeBlendRadius.7": "7x7 (<PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON><PERSON>)", "options.chat": "Chatastellungen  …", "options.chat.color": "<PERSON><PERSON><PERSON>", "options.chat.delay": "Chatverzögerung: %s Sekonn(en)", "options.chat.delay_none": "Chatverzögerung: <PERSON>g", "options.chat.height.focused": "Héicht am Fokus", "options.chat.height.unfocused": "<PERSON><PERSON><PERSON><PERSON> ouni <PERSON>", "options.chat.line_spacing": "Zeilenofstand", "options.chat.links": "Weblinken", "options.chat.links.prompt": "Linken bestätege loossen", "options.chat.opacity": "Chat Text Deckkraaft", "options.chat.scale": "<PERSON><PERSON>", "options.chat.title": "Chatastellungen", "options.chat.visibility": "Cha<PERSON>", "options.chat.visibility.full": "Siich<PERSON>bar", "options.chat.visibility.hidden": "Verstoppt", "options.chat.visibility.system": "<PERSON><PERSON><PERSON>", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s Chunks", "options.clouds.fancy": "<PERSON><PERSON><PERSON><PERSON>", "options.clouds.fast": "<PERSON><PERSON><PERSON>", "options.controls": "Steierung ...", "options.credits_and_attribution": "Matwierkend & Unerkennung  …", "options.damageTiltStrength": "Schuedensneigung", "options.damageTiltStrength.tooltip": "D'Stäerkt vum Kamerawackeln, di beim E<PERSON> vu <PERSON><PERSON><PERSON> op<PERSON>.", "options.darkMojangStudiosBackgroundColor": "Schwaarzwäiss-Logo", "options.darkMojangStudiosBackgroundColor.tooltip": "Ännert d'Hannergrondfaarw beim Luedbildschierm vu Mojang Studios op Schwaarz.", "options.darknessEffectScale": "Pulséierend <PERSON>", "options.darknessEffectScale.tooltip": "<PERSON><PERSON><PERSON>, wéi staark den Effekt \"Däischtert\" pulséiert wann e Wärter oder e Sculk-Kräischer dir e gëtt.", "options.difficulty": "Schwieregkeet", "options.difficulty.easy": "<PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "<PERSON><PERSON>, awer maache w<PERSON><PERSON><PERSON>. D'Hongerlee<PERSON><PERSON> geet eidel, <PERSON><PERSON><PERSON>t all bis op fënnef Häerzer vun der Gesondheet.", "options.difficulty.hard": "Schwéier", "options.difficulty.hard.info": "<PERSON><PERSON> er<PERSON>én<PERSON> a maachen h<PERSON><PERSON><PERSON>. D'Hongerleescht geet eidel a kann zum Hongerdoud féieren.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "<PERSON>ind Kreaturen erschéngen a maachen normale Schued. D'Hungerbar geet eidel, drain<PERSON>iert alles bis op en halleft Häerz vu Gesondheet.", "options.difficulty.online": "Schwieregkeet vum Server", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "<PERSON><PERSON> Kreaturen an nëmmen e puer neutral Kreaturen erschéngen. D'Hungerbar dréit net a verléierte Gesondheet erholl mat der Zäit.", "options.directionalAudio": "<PERSON><PERSON><PERSON><PERSON>", "options.directionalAudio.off.tooltip": "Klassesche Stereoklang", "options.directionalAudio.on.tooltip": "Benotzt HRTF-baséierte Raumklang fir d'Simulatioun vum 3D-Toun ze verbesseren. Erfuerdert HRTF-kompatibel Audiogeräter a léisst sech am beschte mat Kopfhöreren erliewen.", "options.discrete_mouse_scroll": "Schrëttweist Scrollen", "options.entityDistanceScaling": "Objetdistanz", "options.entityShadows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.font": "Schrëftaartenastellungen  …", "options.font.title": "Schrëftaartenastellungen", "options.forceUnicodeFont": "Unicode erzwéngen", "options.fov": "FOV", "options.fov.max": "Quake Pro", "options.fov.min": "Normal", "options.fovEffectScale": "Siichtfeldeffekter", "options.fovEffectScale.tooltip": "Kontrolléiert wéi vill d'Sichtfeld ka mat Spilleffekter änneren.", "options.framerate": "%s FPS", "options.framerateLimit": "Max. FPS", "options.framerateLimit.max": "Onbegrenzt", "options.fullscreen": "Vollbild", "options.fullscreen.current": "Aktuell", "options.fullscreen.entry": "%s × %s (%s Hz, %s Bit)", "options.fullscreen.resolution": "Vollbildopléisung", "options.fullscreen.unavailable": "Astellung net verfügbar", "options.gamma": "Hellegkeet", "options.gamma.default": "Standard", "options.gamma.max": "Hell", "options.gamma.min": "<PERSON><PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "Schimmergeschwindegkeet", "options.glintSpeed.tooltip": "Kontrolléiert wéi séier sech de Schimmer op verzauberte Géigestänn beweegt.", "options.glintStrength": "Schimmerkraaft", "options.glintStrength.tooltip": "Kontrolléiert wéi kloer de Schimmer op verzauberte Saachen sichtbar ass.", "options.graphics": "Graphik", "options.graphics.fabulous": "Fantastesch!", "options.graphics.fabulous.tooltip": "De Grafikmodus \"%s\" benotzt Shader fir <PERSON>, Wolleken a Partikel och hannert duerchsiichtege Bléck a Waasser duerzestellen.\nDëst kann eng staark Auswierkung op d'Performance bei portabele Geräter a 4K-Bildschiermer hunn.", "options.graphics.fancy": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "<PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" balancéiert d'Performance an d'Qualitéit fir d'Majoritéit vun de Maschinnen.\n<PERSON><PERSON><PERSON>, Wolleken a Partikel kënnen eventuell net hannert duerchsiichtege Bléck oder Waasser ugewise ginn.", "options.graphics.fast": "<PERSON><PERSON><PERSON>", "options.graphics.fast.tooltip": "<PERSON> \"<PERSON><PERSON><PERSON>\" reduzéiert d'Quantitéit vum siichtbare Reen a Schnéi.\nTransparenz Effekter si fir verschidde Bléck wéi Blieder vun de Beem ausgeschalt.", "options.graphics.warning.accept": "<PERSON><PERSON> Ënnerstëtzung weiderfueren", "options.graphics.warning.cancel": "Bréng mech zréck", "options.graphics.warning.message": "Et gouf erkannt dat deng Grafikkaart de Grafikmodus \"%s\" net ënnerstëtzt.\n\nDu kanns dës Meldung ignoréieren a weiderfueren, awer fir däin Apparat gëtt keng Ënnerstëtzung ugebuede falls du dech dofir entscheets de Grafikmodus \"%s\" schnell ze notzen.", "options.graphics.warning.renderer": "Renderer detektéiert: [%s]", "options.graphics.warning.title": "Grafikkaart net ënnerstëtzt", "options.graphics.warning.vendor": "Hi<PERSON><PERSON><PERSON> erkannt: [%s]", "options.graphics.warning.version": "OpenGL Versioun detektéiert: [%s]", "options.guiScale": "GUI-Gréisst", "options.guiScale.auto": "Auto", "options.hidden": "Verstoppt", "options.hideLightningFlashes": "Verstopp Blëtzer", "options.hideLightningFlashes.tooltip": "Verhënnert dat Blëtzer den Himmel erhellen. D'Blëtzer selwer wäerte nach ëmmer sichtbar sinn.", "options.hideMatchedNames": "No Numm filteren", "options.hideMatchedNames.tooltip": "E puer Server vun Drëttparteie schécke Chatnoriichten an engem net standardméissege Format.\nWann dës Optioun aktivéiert ass, ginn ausgeblent Spiller a Funktioun vum Ofsendernumm am Chat zougeuerdent.", "options.hideSplashTexts": "Menüsprécher verstoppen", "options.hideSplashTexts.tooltip": "Verstoppt di giel Sprécher am Haaptmenü.", "options.inactivityFpsLimit": "FPS reduzéieren, wann", "options.inactivityFpsLimit.afk": "<PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.afk.tooltip": "Limitéiert d'Bildfrequenz op 30, wann d'<PERSON>pill fir méi wéi eng Minutt keng Eingabe vum Spiller kritt. No weidere 9 Minutte gëtt d'Bildfrequenz op 10 limitéiert.", "options.inactivityFpsLimit.minimized": "<PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.minimized.tooltip": "Limitéiert d'Bildfrequenz just dann, wann d'Spillfënster miniméiert ass.", "options.invertMouse": "<PERSON><PERSON>", "options.japaneseGlyphVariants": "<PERSON><PERSON><PERSON>", "options.japaneseGlyphVariants.tooltip": "Verwent japanesch Variante vun CJK-Zeechen an der Standardschrëftaart.", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "Ëmschalten", "options.language": "Sprooch ...", "options.language.title": "<PERSON><PERSON><PERSON><PERSON>", "options.languageAccuracyWarning": "(Et ka sinn, dass d'Iwwersetzungen net zu 100%% korrekt sinn)", "options.languageWarning": "<PERSON>t ka sinn, dass d'Iwwersetzungen net zu 100%% korrekt sinn", "options.mainHand": "<PERSON><PERSON><PERSON><PERSON>", "options.mainHand.left": "<PERSON><PERSON><PERSON>", "options.mainHand.right": "<PERSON><PERSON><PERSON>", "options.mipmapLevels": "Mipmap Stufen", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "Lénkst Boxebeen", "options.modelPart.left_sleeve": "Lénken Aarm", "options.modelPart.right_pants_leg": "Rietst Boxebeen", "options.modelPart.right_sleeve": "Rietsen Aarm", "options.mouseWheelSensitivity": "Mausradempfindlechkeet", "options.mouse_settings": "Mausastellungen …", "options.mouse_settings.title": "Mausastellungen", "options.multiplayer.title": "Multispillerastellungen ...", "options.multiplier": "%s×", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "Sproochausgab", "options.narrator.all": "<PERSON><PERSON> alles", "options.narrator.chat": "<PERSON><PERSON>", "options.narrator.notavailable": "Net verfügbar", "options.narrator.off": "OFF", "options.narrator.system": "Liest System", "options.notifications.display_time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.notifications.display_time.tooltip": "Beaflosst d'Zäitspann di all Notifikatiounen um Ecran siichtbar bleiwen.", "options.off": "Aus", "options.off.composed": "%s: Aus", "options.on": "Un", "options.on.composed": "%s: Un", "options.online": "Online  …", "options.online.title": "Online-Optiounen", "options.onlyShowSecureChat": "Show n<PERSON><PERSON><PERSON>", "options.onlyShowSecureChat.tooltip": "Nëmmen Noriichte vun anere Spiller weisen, déi verifizéiert kënne ginn, dass se vun deem Spiller geschéckt goufen, an net geännert goufen.", "options.operatorItemsTab": "Operatorhëllefsmëttel-Reiter", "options.particles": "Partikelen", "options.particles.all": "All", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "Minimal", "options.percent_add_value": "%s: +%s %%", "options.percent_value": "%s: %s %%", "options.pixel_value": "%s: %s px", "options.prioritizeChunkUpdates": "Chunk Builder", "options.prioritizeChunkUpdates.byPlayer": "Semiblockéieren", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Verschidden Aktiounen déi an engem Chunk ausgeféiert ginn, féieren zou enger soforteger Recompilatioun vum Chunk. Dozou gehéieren d'Placéieren esou wéi Zerstéieren vu Bléck.", "options.prioritizeChunkUpdates.nearby": "Komplett Blockéierend", "options.prioritizeChunkUpdates.nearby.tooltip": "Nopesch Chunks ginn ëmmer direkt compiléiert. <PERSON><PERSON><PERSON> kann d'Leeschtung vum Spill <PERSON>, wa <PERSON><PERSON><PERSON> placéiert oder zerstéiert ginn.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "Nopesch Chunks ginn paralell kompiléiert. <PERSON><PERSON><PERSON> kann zu kuerze visuelle Lächer féieren, wann d'<PERSON><PERSON>ck zerstéiert ginn.", "options.rawMouseInput": "<PERSON><PERSON><PERSON>", "options.realmsNotifications": "Realms-Neiegkeeten & Invitatiounen", "options.realmsNotifications.tooltip": "Rifft Realms-Neiegkeeten an -Invitatiounen am Haaptmenu of a weist hier jeeweileg Symboler op der Schaltfläch \"Realms\" un.", "options.reducedDebugInfo": "Reduzéiert Debug Info", "options.renderClouds": "<PERSON><PERSON>ek<PERSON>", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "Renderdistanz", "options.resourcepack": "Ressourcep<PERSON><PERSON> ...", "options.rotateWithMinecart": "<PERSON>", "options.rotateWithMinecart.tooltip": "<PERSON><PERSON><PERSON><PERSON>, op sech d'Bléckrichtung vum Spiller an engem Buggi matdr<PERSON>ine soll. St<PERSON>t just a Welten zur Verfügung, an deenen d'experimentell Astellung 'Buggiverbesserungen' ugeschalt ass.", "options.screenEffectScale": "Verzerrungseffekter", "options.screenEffectScale.tooltip": "Stäerkt vun Iwwelzegkeet an Netherportal Verzerrungseffekter. Bei niddrege Wäerter gëtt den Iwwelzegkeets Effekt duerch eng gréng Iwwerlagerung ersat.", "options.sensitivity": "Sensibilitéit", "options.sensitivity.max": "LIICHTGESCHWINDEGKEET!!!", "options.sensitivity.min": "*gaaps*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "Ënnertitel uweisen", "options.simulationDistance": "Simulatiouns Distanz", "options.skinCustomisation": "Skin upassen ...", "options.skinCustomisation.title": "Skin upassen", "options.sounds": "Musek a Geräischer ...", "options.sounds.title": "Musek- a Geräischoptiounen", "options.telemetry": "Telemetriedaten  …", "options.telemetry.button": "Datensammlung", "options.telemetry.button.tooltip": "\"%s\" enthält just erfuerderlech Daten.\n\"%s\" enthält souwéi optionell als och erfuerderlech Daten.", "options.telemetry.disabled": "Telemetrie ass desaktivéiert.", "options.telemetry.state.all": "Alles", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "<PERSON><PERSON>", "options.title": "<PERSON><PERSON><PERSON><PERSON>", "options.touchscreen": "Touchscreen Modus", "options.video": "Videoastellungen ...", "options.videoTitle": "Videoastellungen", "options.viewBobbing": "Kappbeweegungen", "options.visible": "<PERSON><PERSON><PERSON><PERSON>", "options.vsync": "VSync", "outOfMemory.message": "Minecraft huet keen Aarbechtsspäicher méi.\n\nDat kann un engem Feeler um Spill leien, oder dass der Java Virtual Machine net genuch Aarbechtsspäicher zougewise gouf.\n\nFir d'Welt net ze beschiedegen, gouf dat aktuellt Spill zougemaach. Mir hu prob<PERSON><PERSON><PERSON>, genuch Aarbechtsspäicher fräizemaachen, domat du zer<PERSON>ck an de Spillmenü kenns a weider spille kanns, mee dat schéngt net funktionéiert ze hunn.\n\nW.e.g. start d<PERSON><PERSON>ll nei, wann du dës Meldung erëm gesäis.", "outOfMemory.title": "Keen Arbeschtsspäicher méi!", "pack.available.title": "Verfügbar", "pack.copyFailure": "Feeler beim kop<PERSON><PERSON>e vun <PERSON>", "pack.dropConfirm": "Wëlls du déi folgend <PERSON> zu Minecraft dobäifügen?", "pack.dropInfo": "<PERSON><PERSON><PERSON> an <PERSON>, <PERSON>r <PERSON><PERSON><PERSON> b<PERSON>.", "pack.dropRejected.message": "D'follgend Androungen ware keng gülteg Päck a goufen net kopéiert:\n%s", "pack.dropRejected.title": "Net-Pak-Androungen", "pack.folderInfo": "(Pakdateien hei afügen)", "pack.incompatible": "Inkompatibel", "pack.incompatible.confirm.new": "<PERSON><PERSON><PERSON> Pak gouf fir eng méi nei Minecraft Versioun gemaach a funktionéiert eventuell net richteg.", "pack.incompatible.confirm.old": "<PERSON>ëse Pak gouf fir eng méi al Minecraft Versioun gemaach a funktionéiert eventuell net méi richteg.", "pack.incompatible.confirm.title": "Bass du sécher dëse Pak ze lueden?", "pack.incompatible.new": "(Fir eng méi nei Minecraft Versioun gemaach)", "pack.incompatible.old": "(Fir eng méi al Minecraft Versioun gemaach)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON>ner opmaachen", "pack.selected.title": "Ausgewielt", "pack.source.builtin": "matgeliwwer<PERSON>", "pack.source.feature": "<PERSON><PERSON><PERSON><PERSON>", "pack.source.local": "lokal", "pack.source.server": "Server", "pack.source.world": "Welt", "painting.dimensions": "%s × %s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "<PERSON>", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON>iel erfollegräich bombardéiert", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON> a Flamen", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "<PERSON>", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "D'Begéinung", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "<PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Bescheiden", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "En Doudekapp a Rousen", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ass prett", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Onverpaakt", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON>d", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>", "painting.random": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsing.bool.expected": "Boolean-<PERSON><PERSON><PERSON>", "parsing.bool.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>: '%s' ass weder 'true' nach 'false'", "parsing.double.expected": "<PERSON><PERSON><PERSON><PERSON>", "parsing.double.invalid": "Ongülteg Kommazuel '%s'", "parsing.expected": "'%s' erwaart", "parsing.float.expected": "<PERSON><PERSON><PERSON><PERSON>", "parsing.float.invalid": "Ongülteg Kommazuel '%s'", "parsing.int.expected": "<PERSON><PERSON><PERSON><PERSON>", "parsing.int.invalid": "Ongülteg Ganzzuel '%s'", "parsing.long.expected": "<PERSON>ang G<PERSON><PERSON>", "parsing.long.invalid": "Ongülteg laang Ganzzuel '%s'", "parsing.quote.escape": "Ongülteg Escape-Sequenz '\\%s' an Zeecheketten", "parsing.quote.expected.end": "Um Ënn vun der Zeechekette feelt ee Gänseféisschen", "parsing.quote.expected.start": "Am Ufank vun der Zeechekette feelt ee Gänseféisschen", "particle.invalidOptions": "Partikeleegenschafte konnten net ausgewäert ginn: %s", "particle.notFound": "Onbekannte Partikel: '%s'", "permissions.requires.entity": "<PERSON><PERSON><PERSON> kann nëmme vun engem Objet ausgeféiert ginn", "permissions.requires.player": "<PERSON><PERSON><PERSON> kann nëmme vun engem Spiller ausgeféiert ginn", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Auswierkungen:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Onbekannt Predikat: %s", "quickplay.error.invalid_identifier": "Et konnt keng Welt mat der ugi Kennung fonnt ginn", "quickplay.error.realm_connect": "Connectioun mam Realm konnt net hiergestallt ginn", "quickplay.error.realm_permission": "<PERSON><PERSON> Berechtegung, fir eng Verbindung mat dësem Realm hierzestellen", "quickplay.error.title": "Schnellspille feelgeschloen", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Realms gëtt am Moment net a Snapshots ënnerstëtzt", "recipe.notFound": "Onbekannt Rezept: '%s'", "recipe.toast.description": "<PERSON>ck an däi Rezeptbuch", "recipe.toast.title": "<PERSON><PERSON>!", "record.nowPlaying": "Elo spillt: %s", "recover_world.bug_tracker": "<PERSON> <PERSON><PERSON> mellen", "recover_world.button": "Restauréierung versichen", "recover_world.done.failed": "D'Welt konnt net aus fréierem Zoustand restauréiert ginn.", "recover_world.done.success": "D'Welt gouf erfollegräich récre<PERSON>t!", "recover_world.done.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ofgeschloss", "recover_world.issue.missing_file": "<PERSON><PERSON>", "recover_world.issue.none": "<PERSON><PERSON>", "recover_world.message": "<PERSON><PERSON>, de Weltfichier \"%s\" <PERSON><PERSON><PERSON><PERSON>, sinn di ënnen ugi Feeler opgetrueden.\nMéiglecherweis léisst sech d'Welt aus engem eeleren Zoustand relancéieren, oder du kanns dese Problem am Bugtracker mellen.", "recover_world.no_fallback": "<PERSON><PERSON> fir eng Restauréierung firhanden", "recover_world.restore": "Restauréierung versichen", "recover_world.restoring": "Et gett prob<PERSON><PERSON>t, d'Welt zu restauréieren  …", "recover_world.state_entry": "Zoustand vum %s: ", "recover_world.state_entry.unknown": "onbekannt", "recover_world.title": "Lu<PERSON> vun der Welt feelgeschloen", "recover_world.warning": "Lu<PERSON> vun der Weltbeschreiwung feelgeschloen", "resourcePack.broken_assets": "DEFEKT RESSOURCEN ERKANNT", "resourcePack.high_contrast.name": "<PERSON><PERSON><PERSON>", "resourcePack.load_fail": "<PERSON><PERSON><PERSON> v<PERSON> de Ressource feelgeschloen", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "Feeler am Ressourcepak erkannt", "resourcePack.server.name": "Weltspezifesch Ressourcen", "resourcePack.title": "Wiel ee Ressourcepak aus", "resourcePack.vanilla.description": "De Standard Look a Gefill vu Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON>", "resourcepack.downloading": "Ressourcepak gëtt erofgelueden", "resourcepack.progress": "Datei gëtt erofgelueden (%s MB) ...", "resourcepack.requesting": "Ufro gëtt geschéckt ...", "screenshot.failure": "Screenshot konnt net gespäichert ginn: %s", "screenshot.success": "Screenshot gouf ënner %s gespäichert", "selectServer.add": "Server bäifügen", "selectServer.defaultName": "Minecraftserver", "selectServer.delete": "Läschen", "selectServer.deleteButton": "Läschen", "selectServer.deleteQuestion": "Bass du sécher dass du dëse Server läsche wëlls?", "selectServer.deleteWarning": "'%s' wäert fir ëmmer verschwonne sinn! (Eng laang Zäit!)", "selectServer.direct": "<PERSON><PERSON><PERSON> verbannen", "selectServer.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.hiddenAddress": "(Verstoppt)", "selectServer.refresh": "<PERSON><PERSON>", "selectServer.select": "<PERSON> b<PERSON><PERSON><PERSON><PERSON>", "selectWorld.access_failure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> op d'Welt ass feelgeschloen", "selectWorld.allowCommands": "Cheats er<PERSON>", "selectWorld.allowCommands.info": "Be<PERSON>eler wei /gamemode, /experience, ...", "selectWorld.allowCommands.new": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.backupEraseCache": "Tëschegespäichert Donnée läschen", "selectWorld.backupJoinConfirmButton": "Kopie erstellen a lueden", "selectWorld.backupJoinSkipButton": "Ech weess wat ech maachen!", "selectWorld.backupQuestion.customized": "Ugepasste Welte ginn net méi ënnerstëtzt", "selectWorld.backupQuestion.downgrade": "Downgraden vun Spillwelten gëtt net ënnerstëtzt", "selectWorld.backupQuestion.experimental": "Welten déi experimentell Astellunge benotzen, ginn net ënnerstëtzt", "selectWorld.backupQuestion.snapshot": "Wëllt Dir wierklech dës Welt lueden?", "selectWorld.backupWarning.customized": "Leider ginn ugepasste Welten an dëser Versioun vu Minecraft net ënnerstëtzt. D'Welt ka weiderhi benotzt ginn an et bleift alles wei et war, awer nei generéiert Landschaft wäert net méi ugepasst sinn. Mir entschëllegen eis fir all Onannehmlechkeeten!", "selectWorld.backupWarning.downgrade": "Des Welt ass fir d‘lescht op der Versioun %s gespillt ginn; du bass op der Version %s. Op dëser Versioun kéint et zu Ënnerbriechungen kommen - mir kënnen net sécherstellen op et lued oder funktionéieren wäert. Wann dir trotzdem weider maachen wellt, maacht een Backup!", "selectWorld.backupWarning.experimental": "Dës Welt benotzt experimentell Astellungen déi zu all Moment ophale kënnen ze funktionéieren. Mir kënnen net garantéieren dass et geluede gëtt oder funktionéiert. Op eegene Risiko!", "selectWorld.backupWarning.snapshot": "Dës Welt gouf fiert d'lëscht a Versioun %s gespillt, du hues am Moment d'Versioun %s. W. e. g. maach ee Backup fir de Fall wou d'Welt beschiedegt géif ginn!", "selectWorld.bonusItems": "Bonuskëscht", "selectWorld.cheats": "Cheats", "selectWorld.commands": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "Muss konvertéiert ginn!", "selectWorld.conversion.tooltip": "<PERSON><PERSON><PERSON>lt muss an enger e<PERSON>rer Vers<PERSON>un (z. B 1.6.4) opgemaach gin, fir sécher konvertéiert kënnen ze ginn", "selectWorld.create": "<PERSON><PERSON><PERSON><PERSON> eng nei Welt", "selectWorld.customizeType": "Upassen", "selectWorld.dataPacks": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.data_read": "Weltdate gi gelies ...", "selectWorld.delete": "Läschen", "selectWorld.deleteButton": "Läschen", "selectWorld.deleteQuestion": "Bass du dir sécher, dass du déi Welt läsche wëlls?", "selectWorld.deleteWarning": "'%s' wäert fir ëmmer fort sinn! (Eng laang Zäit!)", "selectWorld.delete_failure": "Läsche vun der Welt ass feelgeschloen", "selectWorld.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON><PERSON> sp<PERSON><PERSON>", "selectWorld.edit.backupCreated": "Gespäichert: %s", "selectWorld.edit.backupFailed": "Backup feelgeschloen", "selectWorld.edit.backupFolder": "Backups-<PERSON><PERSON><PERSON>", "selectWorld.edit.backupSize": "Gréisst: %s MB", "selectWorld.edit.export_worldgen_settings": "Generéierungsastellungen exportéiren", "selectWorld.edit.export_worldgen_settings.failure": "Export ass gescheitert", "selectWorld.edit.export_worldgen_settings.success": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.openFolder": "Weltfichier opmaachen", "selectWorld.edit.optimize": "Welt optiméieren", "selectWorld.edit.resetIcon": "Symbol zerécksetzen", "selectWorld.edit.save": "Späicheren", "selectWorld.edit.title": "Welt editéieren", "selectWorld.enterName": "Numm vun der Welt", "selectWorld.enterSeed": "Seed fir de Weltgenerator", "selectWorld.experimental": "Experimentell", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "Erfuerderlech experimentell Funktiounen: %s", "selectWorld.experimental.details.title": "Ufuederung un experimentell Funktiounen", "selectWorld.experimental.message": "Sief virsichteg!\nDes Konfiguratioun erfuerdert <PERSON>, di sech nach an der Entwécklung befannen. Deng Welt ké<PERSON>, futti goen oder mat zoukënftegen Aktualiséierungen net méi funktionéieren.", "selectWorld.experimental.title": "Experimentell Funktiouns <PERSON>", "selectWorld.experiments": "Experimenter", "selectWorld.experiments.info": "Experimenter sinn nei méiglech Spillelementer. Sief virsiichteg well deng Welt beschiedegt ka ginn. Wann d'Welt erstallt ass, kënnen se net méi desaktivéiert ginn.", "selectWorld.futureworld.error.text": "Eppes ass schif gaange beim luede vun enger Welt aus enger méi neier Versioun. Dat war vun Ufank un eng riskant Operatioun; pardon, et huet leider net geklappt.", "selectWorld.futureworld.error.title": "Ee Feeler ass opgetrueden!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "Aventure", "selectWorld.gameMode.adventure.info": "<PERSON><PERSON><PERSON> den Iwwerliewensmodus, awer Bleck kënnen net placéiert oder ofgebaut ginn.", "selectWorld.gameMode.adventure.line1": "Selwecht wei den Iwwerliewensmodus, awer Bléck kënnen net", "selectWorld.gameMode.adventure.line2": "placéiert oder zerstéiert ginn", "selectWorld.gameMode.creative": "K<PERSON><PERSON>v", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON><PERSON>, bau an entdeck ouni Grenzen. <PERSON> kanns fl<PERSON>ien, hues onendlech Materialien zur Verfügung a Monstere kënnen dir net schueden.", "selectWorld.gameMode.creative.line1": "Onendlech Ressourcen, d'Méiglechkeet ze", "selectWorld.gameMode.creative.line2": "Bléck direkt zerstéieren", "selectWorld.gameMode.hardcore": "Hardcore", "selectWorld.gameMode.hardcore.info": "<PERSON>wensmodus, g<PERSON><PERSON><PERSON> op der Schwieregkeet \"Schwéier.\" Du kanns dech net erëmbeliewen wanns du stierfs.", "selectWorld.gameMode.hardcore.line1": "Iwwerliewensmodus um héchsten", "selectWorld.gameMode.hardcore.line2": "Schwieregkeetsgrad an nëmmen engem Liewen", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "Just kucken, nä<PERSON>t upaken.", "selectWorld.gameMode.spectator.line1": "<PERSON> kanns kucken, awer nä<PERSON>t beré<PERSON>en", "selectWorld.gameMode.survival": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.info": "Ent<PERSON><PERSON> eng myster<PERSON><PERSON><PERSON>, wous du bauen, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> machen a Monstere bekämpfen kanns.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON>, b<PERSON>, sammel", "selectWorld.gameMode.survival.line2": "Erfarung a probéier ze Iwwerliewen", "selectWorld.gameRules": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "Astellungen importéieren", "selectWorld.import_worldgen_settings.failure": "Feeler beim importéiere vun den Astellungen", "selectWorld.import_worldgen_settings.select_file": "Astellungsdatei auswielen (.json)", "selectWorld.incompatible.description": "Dës Welt kann an der aktueller Versioun net opgemaach ginn.\nSi gouf zuslescht an der Versioun %s gespillt.", "selectWorld.incompatible.info": "Inkompatibel Versioun: %s", "selectWorld.incompatible.title": "Inkompatibel Versioun", "selectWorld.incompatible.tooltip": "<PERSON><PERSON><PERSON> Welt kann net opgemaach ginn, well si mat enger inkompatibeler Versioun kreéiert gouf.", "selectWorld.incompatible_series": "<PERSON> enger ink<PERSON>beler Versioun kre<PERSON>t", "selectWorld.load_folder_access": "Op den Ordner an dem d'Welte gespäichert sinn, konnt net zougegraff ginn!", "selectWorld.loading_list": "Weltlëscht gëtt gelueden", "selectWorld.locked": "<PERSON><PERSON><PERSON><PERSON> eng aner lafend Instanz vu Minecraft", "selectWorld.mapFeatures": "Strukture generé<PERSON>", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON>, Schëffswrack, asw.", "selectWorld.mapType": "Welttyp", "selectWorld.mapType.normal": "Normal", "selectWorld.moreWorldOptions": "Weider Weltoptiounen ...", "selectWorld.newWorld": "<PERSON><PERSON>", "selectWorld.recreate": "<PERSON><PERSON>", "selectWorld.recreate.customized.text": "Ugepasste Welte ginn an dëser Versioun vu Minecraft net méi ënnerstëtzt. Et gëtt probéiert d'Welt mat dem selwechte Seed an Eegenschaften ze rekonstruéieren awer all d'Landschaftsupassunge gi verluer. Mir entschëllegen eis fir d'Onannehmlechkeeten!", "selectWorld.recreate.customized.title": "Benotzerdefinéiert Welten ginn net méi ënnerstëtzt", "selectWorld.recreate.error.text": "Bei der Rekreatioun vun enger Welt ass eppes feelgeschloen.", "selectWorld.recreate.error.title": "E Feeler ass geschitt!", "selectWorld.resource_load": "Ressourcë gi virbereet  …", "selectWorld.resultFolder": "Gëtt gespäichert an:", "selectWorld.search": "No Welten sichen", "selectWorld.seedInfo": "<PERSON><PERSON><PERSON> loosse fir en zoufällege Seed", "selectWorld.select": "D'ausgewielt Welt spillen", "selectWorld.targetFolder": "Späicherclasseur: %s", "selectWorld.title": "Welt wielen", "selectWorld.tooltip.fromNewerVersion1": "D'Welt gouf an enger méi neier Versioun gespäichert,", "selectWorld.tooltip.fromNewerVersion2": "d'luede kéint <PERSON> verursaachen!", "selectWorld.tooltip.snapshot1": "Vergiesst net dës Welt ze sécheren", "selectWorld.tooltip.snapshot2": "éiers du se an dësem Snapshot opméchs.", "selectWorld.unable_to_load": "D'Welte konnten net geluede ginn", "selectWorld.version": "Versioun:", "selectWorld.versionJoinButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "selectWorld.versionQuestion": "W<PERSON><PERSON> du wierklech des Welt lueden?", "selectWorld.versionUnknown": "onbekannt", "selectWorld.versionWarning": "Dës Welt gouf fiert d'lëscht an Versioun %s gespillt, d'lueden an dëser Versioun kéint d'Welt beschiedegen!", "selectWorld.warning.deprecated.question": "Verschiddener vun dene benotzte Funktioune si vereelst a wäerten an Zukunft net méi funktionnéieren. Wëlls du weider fueren?", "selectWorld.warning.deprecated.title": "Opgepasst! Dës Astellunge gebrauche vereelste Funktiounen", "selectWorld.warning.experimental.question": "Dës Astellunge sinn experimentell a kéinten iergendwann ophalen ze fonktionnéieren. <PERSON>, dass de weider fuere wëlls?", "selectWorld.warning.experimental.title": "Opgepasst! Dës Astellunge benotzen experimentell Funktiounen", "selectWorld.warning.lowDiskSpace.description": "Op dengem Gerät ass net méi vill fräi Späicherplaz verfügbar.\nWann d'Späicherplaz beim Spillen opgebraucht ass, kann dat dozou feieren, dass deng Welt beschiedegt gëtt.", "selectWorld.warning.lowDiskSpace.title": "Oppassen! Wéineg fräi Späicherplaz!", "selectWorld.world": "Welt", "sign.edit": "Schëldbeschrëftung beaarbechten", "sleep.not_possible": "<PERSON><PERSON> kann dës <PERSON> passéieren", "sleep.players_sleeping": "%s/%s Spiller sinn um schlofen", "sleep.skipping_night": "Des Nuecht duerchschlofen", "slot.only_single_allowed": "Just eenzel Inventarplaze sinn erla<PERSON>t, ‚%s‘ erhalen", "slot.unknown": "Onbekannte Slot: '%s'", "snbt.parser.empty_key": "Schlëssel dierf net eidel sinn", "snbt.parser.expected_binary_numeral": "<PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_decimal_numeral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_float_type": "<PERSON>le<PERSON><PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_integer_type": "<PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_non_negative_number": "Netnegativ Zuel <PERSON>art", "snbt.parser.expected_number_or_boolean": "<PERSON><PERSON> oder Wouerechtsw<PERSON><PERSON>", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "Eng Operatioun ‚%s‘ existéiert net", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Atmosphäre/Ëmwelt", "soundCategory.block": "<PERSON><PERSON><PERSON>", "soundCategory.hostile": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.master": "Gesamtlautstäerkt", "soundCategory.music": "Muse<PERSON>", "soundCategory.neutral": "Frënd<PERSON><PERSON> Kreaturen", "soundCategory.player": "<PERSON><PERSON><PERSON>", "soundCategory.record": "Museksbléck", "soundCategory.ui": "UI", "soundCategory.voice": "Stemm/Sproch", "soundCategory.weather": "<PERSON><PERSON><PERSON>", "spectatorMenu.close": "<PERSON><PERSON>", "spectatorMenu.next_page": "Nächst Säit", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.root.prompt": "<PERSON><PERSON><PERSON> eng Tast fir ee Be<PERSON>hl auszewielen, dréck se nach enger kéier fir en auszeféieren.", "spectatorMenu.team_teleport": "Zu Teammember teleportéieren", "spectatorMenu.team_teleport.prompt": "Wiel dat Team, zu dem du dech teleportéiere wëlls", "spectatorMenu.teleport": "Zu Spiller teleportéieren", "spectatorMenu.teleport.prompt": "Wiel ee Spiller aus fir bei hien ze teleportéieren", "stat.generalButton": "<PERSON><PERSON>", "stat.itemsButton": "Géigestänn", "stat.minecraft.animals_bred": "<PERSON><PERSON><PERSON><PERSON> gez<PERSON>", "stat.minecraft.aviate_one_cm": "<PERSON><PERSON><PERSON> mat <PERSON> g<PERSON>", "stat.minecraft.bell_ring": "<PERSON><PERSON><PERSON> <PERSON>aut", "stat.minecraft.boat_one_cm": "<PERSON><PERSON><PERSON> an engem Boot", "stat.minecraft.clean_armor": "Rüstungsdeeler gewäsch", "stat.minecraft.clean_banner": "Fändel gebotzt", "stat.minecraft.clean_shulker_box": "Shulker-Këschte gewäsch", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>ch", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON> ma<PERSON>", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON> au<PERSON>", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON> (absorbéiert)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> (resistéiert)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON>", "stat.minecraft.deaths": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.drop": "Falegeloosse Géigestänn", "stat.minecraft.eat_cake_slice": "Kuchstécker giess", "stat.minecraft.enchant_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON> gefall", "stat.minecraft.fill_cauldron": "<PERSON><PERSON><PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON><PERSON> gefaangen", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Distanz op engem Päerd", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON><PERSON>", "stat.minecraft.inspect_dropper": "Spender duerchsicht", "stat.minecraft.inspect_hopper": "Triichter duerchsicht", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_beacon": "Liichtfeier <PERSON>", "stat.minecraft.interact_with_blast_furnace": "Schmelzuewe benotzt", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_campfire": "Lager<PERSON><PERSON>", "stat.minecraft.interact_with_cartography_table": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_crafting_table": "Wierkbänk benotzt", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_grindstone": "Schläifstee ben<PERSON>zt", "stat.minecraft.interact_with_lectern": "Liespult benotzt", "stat.minecraft.interact_with_loom": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smithing_table": "Schmadddësch ben<PERSON>t", "stat.minecraft.interact_with_smoker": "Reech<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON><PERSON> ben<PERSON>t", "stat.minecraft.jump": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.leave_game": "Spiller verlooss", "stat.minecraft.minecart_one_cm": "<PERSON><PERSON><PERSON> an engem Buggi", "stat.minecraft.mob_kills": "Kreaturen ëmbruecht", "stat.minecraft.open_barrel": "<PERSON><PERSON><PERSON> opgemaach", "stat.minecraft.open_chest": "Këschten opgemaach", "stat.minecraft.open_enderchest": "Enderkë<PERSON>ten opgemaach", "stat.minecraft.open_shulker_box": "Shulker-Këschten opgemaach", "stat.minecraft.pig_one_cm": "Distanz op Schwäi geridden", "stat.minecraft.play_noteblock": "<PERSON>utebléck ges<PERSON>llt", "stat.minecraft.play_record": "Schallplacke gespillt", "stat.minecraft.play_time": "Gespillten Zäit", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON> ëmbruecht", "stat.minecraft.pot_flower": "Planzen an een <PERSON> gemaach", "stat.minecraft.raid_trigger": "Iwwerfäll ausgeléist", "stat.minecraft.raid_win": "Iwwerfäll gewonnen", "stat.minecraft.sleep_in_bed": "An engem Bett geschlof", "stat.minecraft.sneak_time": "Geschlachen Zäit", "stat.minecraft.sprint_one_cm": "Distanz gesprint", "stat.minecraft.strider_one_cm": "Distanz op engem Strider g<PERSON>", "stat.minecraft.swim_one_cm": "Distanz geschwommen", "stat.minecraft.talked_to_villager": "<PERSON> geschwat", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON>", "stat.minecraft.time_since_death": "<PERSON><PERSON>it s<PERSON>it dem leschtem Doud", "stat.minecraft.time_since_rest": "Zäit s<PERSON>it dem leschtem Ausrouen", "stat.minecraft.total_world_time": "<PERSON><PERSON>it mat oppener Welt", "stat.minecraft.traded_with_villager": "<PERSON> g<PERSON>", "stat.minecraft.trigger_trapped_chest": "Falekëschten ausgeléist", "stat.minecraft.tune_noteblock": "Noutebléck gestëmmt", "stat.minecraft.use_cauldron": "Waasser aus Kessele geschäfft", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON><PERSON> um <PERSON> gelaf", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON><PERSON> gelaf", "stat.mobsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Ver<PERSON><PERSON>t", "stat_type.minecraft.crafted": "Wéi oft hiergestallt", "stat_type.minecraft.dropped": "Fale gelooss", "stat_type.minecraft.killed": "Du hues %s %s ëmbruecht", "stat_type.minecraft.killed.none": "%s gouf ni vun dir ë<PERSON>ruecht", "stat_type.minecraft.killed_by": "%s huet dech %s mol ëmbruecht", "stat_type.minecraft.killed_by.none": "%s huet dech nach ni ëmbruecht", "stat_type.minecraft.mined": "<PERSON>é<PERSON> oft ofgebaut", "stat_type.minecraft.picked_up": "Agesammelt", "stat_type.minecraft.used": "Wéi oft benotzt", "stats.none": "X", "structure_block.button.detect_size": "Erkennen", "structure_block.button.load": "<PERSON><PERSON><PERSON>", "structure_block.button.save": "S<PERSON><PERSON>en", "structure_block.custom_data": "Benotzerdefinéierten NBT-Daten Numm", "structure_block.detect_size": "Strukturegréisst a -positioun erkennen:", "structure_block.hover.corner": "Eck: %s", "structure_block.hover.data": "Daten: %s", "structure_block.hover.load": "Lueden: %s", "structure_block.hover.save": "Späicheren: %s", "structure_block.include_entities": "Entitéiten abezéien:", "structure_block.integrity": "Integritéit a Startwäert vun der Konstruktioun", "structure_block.integrity.integrity": "Konstruktiounsvollstännegkeet", "structure_block.integrity.seed": "Konstruk<PERSON><PERSON><PERSON>", "structure_block.invalid_structure_name": "Ongültege Konstruktiounsnumm '%s'", "structure_block.load_not_found": "Konstruktioun '%s' net fonnt ", "structure_block.load_prepare": "Positioun vu Konstruktioun '%s' gëtt virbereet", "structure_block.load_success": "Konstruktioun '%s' gelueden", "structure_block.mode.corner": "<PERSON><PERSON>", "structure_block.mode.data": "Daten", "structure_block.mode.load": "<PERSON><PERSON><PERSON>", "structure_block.mode.save": "Spueren", "structure_block.mode_info.corner": "Eckmodus - Markéierung vu Positioun a Gréisst", "structure_block.mode_info.data": "Datemodus - Markéierung vun der Spilllogik", "structure_block.mode_info.load": "Luedmodus - vun <PERSON> lueden", "structure_block.mode_info.save": "Späichermodus - an <PERSON>i schreiwen", "structure_block.position": "Relativ Positioun", "structure_block.position.x": "Relativ X-Positioun", "structure_block.position.y": "Relativ Y-Positioun", "structure_block.position.z": "Relativ Z-Positioun", "structure_block.save_failure": "Konstruktioun '%s' konnt net gespäichert ginn", "structure_block.save_success": "Konstruktioun als '%s' gespäichert", "structure_block.show_air": "Onsiichtbar Blé<PERSON> uweisen:", "structure_block.show_boundingbox": "Begrenzungszone uweisen:", "structure_block.size": "Konstruktiounsgréisst", "structure_block.size.x": "Konstruk<PERSON><PERSON><PERSON><PERSON><PERSON> (X)", "structure_block.size.y": "Konstruktiounsgréisst (Y)", "structure_block.size.z": "Konstruktiounsg<PERSON> (Z)", "structure_block.size_failure": "Konstruktiounsgréisst konnt net erkannt ginn. Füg <PERSON> mat dem selwechte Konstruktiounsnumm dobäi", "structure_block.size_success": "Gréisst vu '%s' gouf erfollegräich erkannt", "structure_block.strict": "Streng Placéierung:", "structure_block.structure_name": "Konstruktiounsnumm", "subtitles.ambient.cave": "Grujelegt Geräisch", "subtitles.ambient.sound": "Grujelegt Geräisch", "subtitles.block.amethyst_block.chime": "Amethyst kléngt", "subtitles.block.amethyst_block.resonate": "<PERSON><PERSON><PERSON><PERSON> schw<PERSON>gt mat", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON>", "subtitles.block.anvil.land": "Amboss gelant", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.barrel.close": "<PERSON>aass geet zou", "subtitles.block.barrel.open": "Faass geet op", "subtitles.block.beacon.activate": "Liichtfeier aktivéiert", "subtitles.block.beacon.ambient": "Liichtfeier brummt", "subtitles.block.beacon.deactivate": "Liichtfeier desaktivéiert", "subtitles.block.beacon.power_select": "Liichtfeierkraaft ausgewielt", "subtitles.block.beehive.drip": "<PERSON><PERSON><PERSON>", "subtitles.block.beehive.enter": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.block.beehive.exit": "<PERSON><PERSON> verlé<PERSON><PERSON> Stack", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON><PERSON> schappt", "subtitles.block.beehive.work": "<PERSON><PERSON> s<PERSON>ffen", "subtitles.block.bell.resonate": "Klack haalt no", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON> laut", "subtitles.block.big_dripleaf.tilt_down": "Drëpsblat kippt", "subtitles.block.big_dripleaf.tilt_up": "Drëpsblat riicht sech", "subtitles.block.blastfurnace.fire_crackle": "Schmelzuewe knistert", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON> blubbert", "subtitles.block.bubble_column.bubble_pop": "Blose platzen", "subtitles.block.bubble_column.upwards_ambient": "Blose spruddelen", "subtitles.block.bubble_column.upwards_inside": "Blose stréimen", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON> wierbelen", "subtitles.block.bubble_column.whirlpool_inside": "Blosen zéien", "subtitles.block.button.click": "<PERSON><PERSON><PERSON> klickt", "subtitles.block.cake.add_candle": "Kuch schwabbelt", "subtitles.block.campfire.crackle": "Lagerfeier knistert", "subtitles.block.candle.crackle": "Käerz knistert", "subtitles.block.candle.extinguish": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.chest.close": "Këscht klappt zou", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "subtitles.block.chest.open": "Këscht gëtt opgemaach", "subtitles.block.chorus_flower.death": "Chorusbléi verwi<PERSON>cht", "subtitles.block.chorus_flower.grow": "Chorusbléi wiisst", "subtitles.block.comparator.click": "Komp<PERSON><PERSON> klickt", "subtitles.block.composter.empty": "Komposter eidel gemaach", "subtitles.block.composter.fill": "Komposter gefëllt", "subtitles.block.composter.ready": "Komposter kompostéiert", "subtitles.block.conduit.activate": "Conduit aktivéiert", "subtitles.block.conduit.ambient": "Conduit puls<PERSON><PERSON>t", "subtitles.block.conduit.attack.target": "Conduit attack<PERSON>iert", "subtitles.block.conduit.deactivate": "Conduit desaktivéiert", "subtitles.block.copper_bulb.turn_off": "Kofferbier schalt aus", "subtitles.block.copper_bulb.turn_on": "<PERSON><PERSON><PERSON><PERSON> schalt un", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON><PERSON><PERSON> geet zou", "subtitles.block.copper_trapdoor.open": "<PERSON><PERSON><PERSON><PERSON> geet op", "subtitles.block.crafter.craft": "<PERSON><PERSON><PERSON> wierkelt", "subtitles.block.crafter.fail": "<PERSON><PERSON><PERSON> versot", "subtitles.block.creaking_heart.hurt": "Knarzend Häerz jäizt", "subtitles.block.creaking_heart.idle": "Grujelegt Geräisch", "subtitles.block.creaking_heart.spawn": "Knarzend Häerz erwächt", "subtitles.block.deadbush.idle": "Straiss knisteren", "subtitles.block.decorated_pot.insert": "De<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.decorated_pot.insert_fail": "De<PERSON><PERSON><PERSON><PERSON> wackelt", "subtitles.block.decorated_pot.shatter": "Dekoréiert Dëppen zerbrécht", "subtitles.block.dispenser.dispense": "Géigestand geworf", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON> vers<PERSON>", "subtitles.block.door.toggle": "<PERSON><PERSON>", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "<PERSON>auber<PERSON><PERSON><PERSON>", "subtitles.block.end_portal.spawn": "Endportal geet op", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON>at", "subtitles.block.eyeblossom.close": "<PERSON>b<PERSON><PERSON> geet zou", "subtitles.block.eyeblossom.idle": "<PERSON>b<PERSON><PERSON> fl<PERSON>", "subtitles.block.eyeblossom.open": "Ablumm geet op", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON><PERSON><PERSON><PERSON> qui<PERSON>", "subtitles.block.fire.ambient": "<PERSON><PERSON> knistert", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Gehaansfénkelcher schwirren", "subtitles.block.frogspawn.hatch": "Kauzekapp schlüpft", "subtitles.block.furnace.fire_crackle": "Uewen knistert", "subtitles.block.generic.break": "<PERSON>", "subtitles.block.generic.fall": "<PERSON><PERSON>s fällt op e Block", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Block brécht", "subtitles.block.generic.place": "Block placéiert", "subtitles.block.grindstone.use": "Schläifstee ben<PERSON>zt", "subtitles.block.growing_plant.crop": "<PERSON><PERSON> geschnidden", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON><PERSON> wackelt", "subtitles.block.honey_block.slide": "Hunnegblock erofrutschen", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON><PERSON> geet zou", "subtitles.block.iron_trapdoor.open": "<PERSON><PERSON><PERSON><PERSON> geet op", "subtitles.block.lava.ambient": "<PERSON><PERSON> blubbert", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON>", "subtitles.block.note_block.note": "Nouteblock spillt", "subtitles.block.pale_hanging_moss.idle": "Grujelegt Geräisch", "subtitles.block.piston.move": "<PERSON><PERSON> scha<PERSON>t", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON>va dr<PERSON>t a Kessel", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "L'eau coule dans le chaudron", "subtitles.block.pointed_dripstone.land": "Stalaktit fält erof", "subtitles.block.portal.ambient": "<PERSON> da<PERSON>", "subtitles.block.portal.travel": "Portaldausche léisst no", "subtitles.block.portal.trigger": "Portal<PERSON>us<PERSON> h<PERSON>ou", "subtitles.block.pressure_plate.click": "Drockplack klickt", "subtitles.block.pumpkin.carve": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> z<PERSON>t", "subtitles.block.respawn_anchor.ambient": "S<PERSON><PERSON>nan<PERSON> wabert", "subtitles.block.respawn_anchor.charge": "Séilenanker opgelueden", "subtitles.block.respawn_anchor.deplete": "Séilenanker verbraucht", "subtitles.block.respawn_anchor.set_spawn": "Séilenanker setzt Spawnpunkt", "subtitles.block.sand.idle": "<PERSON> b<PERSON>", "subtitles.block.sand.wind": "<PERSON><PERSON>", "subtitles.block.sculk.charge": "Sculk brodelt", "subtitles.block.sculk.spread": "Sculk verbreet sech", "subtitles.block.sculk_catalyst.bloom": "Sculk‐Katalysator bléit", "subtitles.block.sculk_sensor.clicking": "Sculk Sensor fänkt un ze klicken", "subtitles.block.sculk_sensor.clicking_stop": "Sculk Sensor hält op ze klicken", "subtitles.block.sculk_shrieker.shriek": "Sculk-Kräischer kräischt", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> geet zou", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON> geet op", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON><PERSON> wackelt", "subtitles.block.smithing_table.use": "Schmadddësch ben<PERSON>t", "subtitles.block.smoker.smoke": "Reecherue<PERSON> dämpt", "subtitles.block.sniffer_egg.crack": "Schnoffeler-<PERSON><PERSON> bascht", "subtitles.block.sniffer_egg.hatch": "Schnoffeler-Ee s<PERSON>ü<PERSON>t", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON><PERSON><PERSON> plumpst", "subtitles.block.sponge.absorb": "<PERSON><PERSON><PERSON> saugt op", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON><PERSON> qui<PERSON>", "subtitles.block.trial_spawner.about_to_spawn_item": "Onheilvolle Géigestand bereet fir", "subtitles.block.trial_spawner.ambient": "Prüfungs-Spawner knistert", "subtitles.block.trial_spawner.ambient_charged": "Onheilvollt Geknister", "subtitles.block.trial_spawner.ambient_ominous": "Onheilvollt Geknister", "subtitles.block.trial_spawner.charge_activate": "<PERSON><PERSON><PERSON> Prüfungs-Spawner", "subtitles.block.trial_spawner.close_shutter": "Prüfungs-Spawner geet zou", "subtitles.block.trial_spawner.detect_player": "Prüfungs-Spawner lued op", "subtitles.block.trial_spawner.eject_item": "Prüfungs-Spawner keit Beut aus", "subtitles.block.trial_spawner.ominous_activate": "<PERSON><PERSON><PERSON> Prüfungs-Spawner", "subtitles.block.trial_spawner.open_shutter": "Prüfungs-Spawner geet op", "subtitles.block.trial_spawner.spawn_item": "Onheilvolle Géigestand fale gelooss", "subtitles.block.trial_spawner.spawn_item_begin": "Onheilvolle Géigestand erscheint", "subtitles.block.trial_spawner.spawn_mob": "Prüfungs-Spawner erschaaft eng Kreatur", "subtitles.block.tripwire.attach": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.block.tripwire.click": "<PERSON><PERSON><PERSON> klickt", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON> ents<PERSON><PERSON>", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON> f<PERSON><PERSON><PERSON>", "subtitles.block.vault.ambient": "Tresor knistert", "subtitles.block.vault.close_shutter": "Tresor geet zou", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.eject_item": "Tresor kéit Géigestand aus", "subtitles.block.vault.insert_item": "Tresor späert op", "subtitles.block.vault.insert_item_fail": "Tresor akzeptéiert Opmacher net", "subtitles.block.vault.open_shutter": "Tresor geet op", "subtitles.block.vault.reject_rewarded_player": "Tresor akzeptéiert Spiller net", "subtitles.block.water.ambient": "<PERSON><PERSON><PERSON> fl<PERSON>", "subtitles.block.wet_sponge.dries": "<PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.insert": "<PERSON>uch eragestallt", "subtitles.chiseled_bookshelf.insert_enchanted": "Verzaubert Buch eragestallt", "subtitles.chiseled_bookshelf.take": "<PERSON><PERSON> geholl", "subtitles.chiseled_bookshelf.take_enchanted": "Verzaubert Buch geholl", "subtitles.enchant.thorns.hit": "<PERSON><PERSON><PERSON> picken", "subtitles.entity.allay.ambient_with_item": "Allay sicht", "subtitles.entity.allay.ambient_without_item": "<PERSON>ay verlaangt", "subtitles.entity.allay.death": "Allay stierft", "subtitles.entity.allay.hurt": "Allay deet wéi", "subtitles.entity.allay.item_given": "<PERSON>ay chortles", "subtitles.entity.allay.item_taken": "Allay Allays", "subtitles.entity.allay.item_thrown": "Hëllefsgeescht schleidert", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.armadillo.brush": "<PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.armadillo.eat": "Armadillo frësst", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.armadillo.hurt_reduced": "Armadillo schützt sech", "subtitles.entity.armadillo.land": "<PERSON><PERSON><PERSON> lant", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> lu<PERSON>", "subtitles.entity.armadillo.roll": "<PERSON><PERSON><PERSON> rullt sech an", "subtitles.entity.armadillo.scute_drop": "<PERSON><PERSON><PERSON> schuppt", "subtitles.entity.armadillo.unroll_finish": "<PERSON><PERSON><PERSON> entrullt sech", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> lu<PERSON>", "subtitles.entity.armor_stand.fall": "Rüstungsstänner fëllt em", "subtitles.entity.arrow.hit": "<PERSON><PERSON>", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> getraff", "subtitles.entity.arrow.shoot": "<PERSON><PERSON>", "subtitles.entity.axolotl.attack": "Axolotl gräift un", "subtitles.entity.axolotl.death": "Axolotl stierft", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON> bless<PERSON><PERSON><PERSON>", "subtitles.entity.axolotl.idle_air": "Axolotl jiipst", "subtitles.entity.axolotl.idle_water": "Axolotl jiipst", "subtitles.entity.axolotl.splash": "Axolotl sprutzt", "subtitles.entity.axolotl.swim": "Axolotl schwëmmt", "subtitles.entity.bat.ambient": "Flantermaus kr<PERSON>", "subtitles.entity.bat.death": "Flantermaus stierft", "subtitles.entity.bat.hurt": "Flante<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bat.takeoff": "Flantermaus flitt fort", "subtitles.entity.bee.ambient": "Bei summt", "subtitles.entity.bee.death": "<PERSON><PERSON> s<PERSON>ft", "subtitles.entity.bee.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.bee.loop": "Bei summt", "subtitles.entity.bee.loop_aggressive": "Bei summt rosen", "subtitles.entity.bee.pollinate": "Bei summt g<PERSON>ch", "subtitles.entity.bee.sting": "<PERSON><PERSON>", "subtitles.entity.blaze.ambient": "<PERSON> o<PERSON>", "subtitles.entity.blaze.burn": "<PERSON>t", "subtitles.entity.blaze.death": "<PERSON>", "subtitles.entity.blaze.hurt": "<PERSON>", "subtitles.entity.blaze.shoot": "<PERSON>", "subtitles.entity.boat.paddle_land": "<PERSON><PERSON><PERSON>", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Sumpfskelett klappert", "subtitles.entity.bogged.death": "Sumpfskelett stierft", "subtitles.entity.bogged.hurt": "Sumpfskelett h<PERSON><PERSON>", "subtitles.entity.breeze.charge": "Breeze lued op", "subtitles.entity.breeze.death": "<PERSON>ze stierft", "subtitles.entity.breeze.deflect": "<PERSON><PERSON> wiert of", "subtitles.entity.breeze.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.breeze.idle_air": "<PERSON>ze flitt", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON> schwirrt", "subtitles.entity.breeze.inhale": "<PERSON><PERSON> h<PERSON>t Loft", "subtitles.entity.breeze.jump": "<PERSON>ze spréngt", "subtitles.entity.breeze.land": "<PERSON>ze lant", "subtitles.entity.breeze.shoot": "<PERSON><PERSON> s<PERSON>t", "subtitles.entity.breeze.slide": "Breeze gleit", "subtitles.entity.breeze.whirl": "Breeze wirbelt", "subtitles.entity.breeze.wind_burst": "<PERSON><PERSON><PERSON><PERSON> platzt", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON> sech", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON> f<PERSON>", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON> leet sech hin", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON> steet op", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.step_sand": "Ka<PERSON>il le<PERSON>t am Sand", "subtitles.entity.cat.ambient": "<PERSON><PERSON>", "subtitles.entity.cat.beg_for_food": "<PERSON>", "subtitles.entity.cat.death": "<PERSON><PERSON> s<PERSON>ft", "subtitles.entity.cat.eat": "Katz frësst", "subtitles.entity.cat.hiss": "<PERSON> faucht", "subtitles.entity.cat.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.cat.purr": "<PERSON> schnurrt", "subtitles.entity.chicken.ambient": "<PERSON> g<PERSON>", "subtitles.entity.chicken.death": "<PERSON> stierft", "subtitles.entity.chicken.egg": "<PERSON> leet Ee", "subtitles.entity.chicken.hurt": "<PERSON>", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON><PERSON> stierft", "subtitles.entity.cod.flop": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cow.ambient": "<PERSON><PERSON> muet", "subtitles.entity.cow.death": "<PERSON><PERSON> stierft", "subtitles.entity.cow.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.cow.milk": "<PERSON><PERSON> g<PERSON> gest<PERSON>h", "subtitles.entity.creaking.activate": "<PERSON><PERSON><PERSON>", "subtitles.entity.creaking.ambient": "Knarzer knarzt", "subtitles.entity.creaking.attack": "Knarzer gräift un", "subtitles.entity.creaking.deactivate": "<PERSON><PERSON><PERSON>aktiv<PERSON>", "subtitles.entity.creaking.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.creaking.freeze": "<PERSON><PERSON><PERSON> ersteift", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON> lieft", "subtitles.entity.creaking.sway": "Knarzer gëtt ofgeschiermt", "subtitles.entity.creaking.twitch": "<PERSON><PERSON><PERSON> zuckt", "subtitles.entity.creaking.unfreeze": "<PERSON><PERSON><PERSON> beweegt sech", "subtitles.entity.creeper.death": "Creeper stierft", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.creeper.primed": "C<PERSON>per z<PERSON>t", "subtitles.entity.dolphin.ambient": "<PERSON><PERSON>", "subtitles.entity.dolphin.ambient_water": "Delfin päift", "subtitles.entity.dolphin.attack": "Delfin attackéiert", "subtitles.entity.dolphin.death": "Delfin stierft", "subtitles.entity.dolphin.eat": "Delfin frësst", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON>", "subtitles.entity.dolphin.jump": "Delfin spréngt", "subtitles.entity.dolphin.play": "Delfin spillt", "subtitles.entity.dolphin.splash": "Delfin platscht", "subtitles.entity.dolphin.swim": "Del<PERSON> schwemmt", "subtitles.entity.donkey.ambient": "<PERSON><PERSON>l iat", "subtitles.entity.donkey.angry": "<PERSON><PERSON><PERSON> wiehert", "subtitles.entity.donkey.chest": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.donkey.eat": "Iesel frësst", "subtitles.entity.donkey.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.donkey.jump": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.drowned.ambient": "Erdronkene guergelt", "subtitles.entity.drowned.ambient_water": "Erdronkene guergelt", "subtitles.entity.drowned.death": "Erdronkene stierft", "subtitles.entity.drowned.hurt": "<PERSON>rd<PERSON><PERSON><PERSON> h<PERSON><PERSON>", "subtitles.entity.drowned.shoot": "Erdronkene werft Dräizack", "subtitles.entity.drowned.step": "Erdronkene leeft", "subtitles.entity.drowned.swim": "Erdronkene schwemmt", "subtitles.entity.egg.throw": "<PERSON>e flitt", "subtitles.entity.elder_guardian.ambient": "Grousswiechter stöhnt", "subtitles.entity.elder_guardian.ambient_land": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zap<PERSON>t", "subtitles.entity.elder_guardian.curse": "Grousswiechter verflucht", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stierft", "subtitles.entity.elder_guardian.flop": "<PERSON>rous<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON>", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> s<PERSON>ft", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON><PERSON> flattert", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON><PERSON> faucht", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ender_eye.death": "<PERSON><PERSON> p<PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON> flitt", "subtitles.entity.enderman.ambient": "Enderman vwoopt", "subtitles.entity.enderman.death": "<PERSON><PERSON> stierft", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.enderman.stare": "<PERSON><PERSON> j<PERSON>", "subtitles.entity.enderman.teleport": "Enderman teleportéiert", "subtitles.entity.endermite.ambient": "Endermite krabbelt", "subtitles.entity.endermite.death": "Endermite stierft", "subtitles.entity.endermite.hurt": "<PERSON><PERSON><PERSON> h<PERSON><PERSON>", "subtitles.entity.evoker.ambient": "Ma<PERSON>r murmelt", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON> z<PERSON><PERSON>", "subtitles.entity.evoker.celebrate": "Ma<PERSON>r jubelt", "subtitles.entity.evoker.death": "Magier stierft", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.evoker.prepare_attack": "<PERSON><PERSON><PERSON> bereet <PERSON> fir", "subtitles.entity.evoker.prepare_summon": "<PERSON><PERSON>r bereet <PERSON> fir", "subtitles.entity.evoker.prepare_wololo": "Magier bereet een <PERSON> fir", "subtitles.entity.evoker_fangs.attack": "Räisszänn schnapen zou", "subtitles.entity.experience_orb.pickup": "Erfarung krut", "subtitles.entity.firework_rocket.blast": "Feierwierk explodéiert", "subtitles.entity.firework_rocket.launch": "Feierwierk zündt", "subtitles.entity.firework_rocket.twinkle": "Feierwierk fonkelt", "subtitles.entity.fish.swim": "Plätschen", "subtitles.entity.fishing_bobber.retrieve": "Schwëmmer ageholl", "subtitles.entity.fishing_bobber.splash": "Schwëmmer platscht", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON><PERSON> ausgeworf", "subtitles.entity.fox.aggro": "<PERSON><PERSON> g<PERSON> rosen", "subtitles.entity.fox.ambient": "<PERSON><PERSON> q<PERSON>", "subtitles.entity.fox.bite": "<PERSON><PERSON> b<PERSON>", "subtitles.entity.fox.death": "<PERSON><PERSON> stierft", "subtitles.entity.fox.eat": "<PERSON><PERSON> fr<PERSON>t", "subtitles.entity.fox.hurt": "<PERSON><PERSON>", "subtitles.entity.fox.screech": "<PERSON><PERSON>", "subtitles.entity.fox.sleep": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.fox.sniff": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.fox.spit": "<PERSON><PERSON>", "subtitles.entity.fox.teleport": "<PERSON><PERSON> teleport<PERSON>", "subtitles.entity.frog.ambient": "<PERSON><PERSON><PERSON> quakt", "subtitles.entity.frog.death": "<PERSON><PERSON><PERSON> s<PERSON>ft", "subtitles.entity.frog.eat": "Fräsch frësst", "subtitles.entity.frog.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON><PERSON>", "subtitles.entity.frog.long_jump": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.generic.big_fall": "<PERSON><PERSON>s ass gefall", "subtitles.entity.generic.burn": "Um brennen", "subtitles.entity.generic.death": "<PERSON> stierwen", "subtitles.entity.generic.drink": "Um schlappen", "subtitles.entity.generic.eat": "Um iessen", "subtitles.entity.generic.explode": "Explosioun", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON>", "subtitles.entity.generic.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON>s ass <PERSON>mgefall", "subtitles.entity.generic.splash": "Platschen", "subtitles.entity.generic.swim": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "<PERSON><PERSON><PERSON><PERSON> platzt", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON>", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "Liichtkader gefëllt", "subtitles.entity.glow_item_frame.break": "Liichtkader zerbrécht", "subtitles.entity.glow_item_frame.place": "Liichtkader placéiert", "subtitles.entity.glow_item_frame.remove_item": "Liichtkader eidel gemaach", "subtitles.entity.glow_item_frame.rotate_item": "Liichtkader klickt", "subtitles.entity.glow_squid.ambient": "Glousen Tëntefësch schwemmt", "subtitles.entity.glow_squid.death": "<PERSON>lousen Tëntefësch stierft", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_squid.squirt": "Glousen Tëntefësch sprëtzt Tënt", "subtitles.entity.goat.ambient": "<PERSON>ss meckert", "subtitles.entity.goat.death": "<PERSON><PERSON> stierft", "subtitles.entity.goat.eat": "Geess frësst", "subtitles.entity.goat.horn_break": "<PERSON><PERSON><PERSON> br<PERSON> of", "subtitles.entity.goat.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> sp<PERSON>t", "subtitles.entity.goat.milk": "<PERSON><PERSON> g<PERSON>tt gem<PERSON>", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON> rammt", "subtitles.entity.goat.screaming.ambient": "<PERSON>ss j<PERSON>izt", "subtitles.entity.goat.step": "<PERSON><PERSON> leeft", "subtitles.entity.guardian.ambient": "Wiechter stöhnt", "subtitles.entity.guardian.ambient_land": "Wiechter zappelt", "subtitles.entity.guardian.attack": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t", "subtitles.entity.guardian.death": "<PERSON><PERSON>chter stierft", "subtitles.entity.guardian.flop": "Wiechter platscht", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> g<PERSON>t", "subtitles.entity.hoglin.angry": "<PERSON><PERSON>n grunzt rosen", "subtitles.entity.hoglin.attack": "<PERSON><PERSON>n gräift un", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON> zitt sech zeréck", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> leeft", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON> w<PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON> w<PERSON>", "subtitles.entity.horse.armor": "Päerdsrüstungs ugeluecht", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.husk.ambient": "Wüstenzombie keimt", "subtitles.entity.husk.converted_to_zombie": "Wüstenzombie gouf zu engem Zombie", "subtitles.entity.husk.death": "Wüstenzombie stierft", "subtitles.entity.husk.hurt": "Wüstenzombie h<PERSON>", "subtitles.entity.illusioner.ambient": "Illusionist mur<PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "Illusionist <PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.death": "Illusionist stierft", "subtitles.entity.illusioner.hurt": "Illusionist <PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.mirror_move": "Illusionist <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.prepare_blindness": "Illusionist be<PERSON> vir", "subtitles.entity.illusioner.prepare_mirror": "Illusionist be<PERSON> vir", "subtitles.entity.iron_golem.attack": "Eisegolem attackéiert", "subtitles.entity.iron_golem.damage": "Eisegolem gëtt beschiedegt", "subtitles.entity.iron_golem.death": "Eisegolem stierft", "subtitles.entity.iron_golem.hurt": "<PERSON>isegol<PERSON><PERSON>", "subtitles.entity.iron_golem.repair": "Eisegolem reparéiert", "subtitles.entity.item.break": "Géigestand geet futti", "subtitles.entity.item.pickup": "Géigestand opgehuewen", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON>umm eidel gemaach", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.leash_knot.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.leash_knot.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.lightning_bolt.impact": "Blëtz ass ageschloen", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON> grollt", "subtitles.entity.llama.ambient": "<PERSON> bockt", "subtitles.entity.llama.angry": "<PERSON> bockt rosen", "subtitles.entity.llama.chest": "<PERSON>", "subtitles.entity.llama.death": "<PERSON> stier<PERSON>", "subtitles.entity.llama.eat": "<PERSON> f<PERSON>", "subtitles.entity.llama.hurt": "<PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.llama.spit": "<PERSON>", "subtitles.entity.llama.step": "<PERSON> le<PERSON>t", "subtitles.entity.llama.swag": "<PERSON> <PERSON><PERSON>", "subtitles.entity.magma_cube.death": "Magmawierfel stierft", "subtitles.entity.magma_cube.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.magma_cube.squish": "Magmawierfel spréngt", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> r<PERSON>t", "subtitles.entity.minecart.inside_underwater": "<PERSON><PERSON><PERSON> r<PERSON>t <PERSON>", "subtitles.entity.minecart.riding": "<PERSON><PERSON><PERSON> r<PERSON>t", "subtitles.entity.mooshroom.convert": "Moosh<PERSON> verwandelt sech", "subtitles.entity.mooshroom.eat": "Mooshroom frësst", "subtitles.entity.mooshroom.milk": "Mooshroom gëtt gestrach", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom gëtt komesch gestrach", "subtitles.entity.mule.ambient": "<PERSON><PERSON><PERSON><PERSON> iat", "subtitles.entity.mule.angry": "<PERSON><PERSON><PERSON><PERSON> wiehert", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ft", "subtitles.entity.mule.eat": "<PERSON><PERSON><PERSON><PERSON> frësst", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.painting.break": "Bild <PERSON>cht", "subtitles.entity.painting.place": "Bild pla<PERSON>", "subtitles.entity.panda.aggressive_ambient": "<PERSON><PERSON> br<PERSON>", "subtitles.entity.panda.ambient": "<PERSON><PERSON> hechelt", "subtitles.entity.panda.bite": "<PERSON><PERSON> b<PERSON>", "subtitles.entity.panda.cant_breed": "Panda bockt", "subtitles.entity.panda.death": "Panda stierft", "subtitles.entity.panda.eat": "Panda frësst", "subtitles.entity.panda.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.panda.pre_sneeze": "Panda muss n<PERSON>", "subtitles.entity.panda.sneeze": "Panda néitscht", "subtitles.entity.panda.step": "Panda leeft", "subtitles.entity.panda.worried_ambient": "<PERSON><PERSON> w<PERSON>", "subtitles.entity.parrot.ambient": "Papa<PERSON>i s<PERSON>wätzt", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.eats": "Papagei frësst", "subtitles.entity.parrot.fly": "<PERSON><PERSON><PERSON> flattert", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.bogged": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON><PERSON> schwi<PERSON>t", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON><PERSON> knarzt", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.endermite": "Papagei krab<PERSON>", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON> mur<PERSON>t", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON> mur<PERSON>t", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON>i schwabbelt", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.piglin": "<PERSON><PERSON><PERSON> s<PERSON>t", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> s<PERSON>t", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON> mur<PERSON>t", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.shulker": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON>i schwabbelt", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.stray": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON> mur<PERSON>t", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON> rifft", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON> kick<PERSON>t", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON> reegt sech ob", "subtitles.entity.parrot.imitate.wither_skeleton": "<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.phantom.ambient": "Phantom kräischt", "subtitles.entity.phantom.bite": "Phantom bäisst", "subtitles.entity.phantom.death": "Phantom stierft", "subtitles.entity.phantom.flap": "Phantom flattert", "subtitles.entity.phantom.hurt": "<PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.phantom.swoop": "Phantom stéisst erof", "subtitles.entity.pig.ambient": "Schwäi grunzt", "subtitles.entity.pig.death": "Schwäi stierft", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pig.saddle": "Schwäi g<PERSON>uedelt", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> bewonnert Géigestand", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> ronkt", "subtitles.entity.piglin.angry": "<PERSON>lin ronkt rosen", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> feiert", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> g<PERSON>", "subtitles.entity.piglin.death": "<PERSON><PERSON> stierft", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.piglin.jealous": "<PERSON>lin ronkt neidesch", "subtitles.entity.piglin.retreat": "<PERSON><PERSON> zitt sech zeréck", "subtitles.entity.piglin.step": "<PERSON><PERSON> leeft", "subtitles.entity.piglin_brute.ambient": "<PERSON>lin Barbar ronkt", "subtitles.entity.piglin_brute.angry": "<PERSON>lin Barbar ronkt rosen", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON>lin Barbar g<PERSON><PERSON> z<PERSON>", "subtitles.entity.piglin_brute.death": "<PERSON>lin-<PERSON><PERSON> stierft", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON> h<PERSON><PERSON>", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON> Barbar leeft", "subtitles.entity.pillager.ambient": "Plëmmert murmelt", "subtitles.entity.pillager.celebrate": "Plëmmert jubelt", "subtitles.entity.pillager.death": "Plëmmert stierft", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.knockback": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.strong": "Staarken Urgëff", "subtitles.entity.player.attack.sweep": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.burp": "<PERSON><PERSON>", "subtitles.entity.player.death": "Spiller stierft", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON> f<PERSON><PERSON>", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON> erdrén<PERSON>", "subtitles.entity.player.hurt_on_fire": "Spiller brennt", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON> bimmelt", "subtitles.entity.player.teleport": "Spiller teleportéiert", "subtitles.entity.polar_bear.ambient": "Äisbier grommelt", "subtitles.entity.polar_bear.ambient_baby": "Äisbier brummt", "subtitles.entity.polar_bear.death": "Äisbier stierft", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.warning": "<PERSON>isbier br<PERSON>", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON><PERSON> geworf", "subtitles.entity.puffer_fish.blow_out": "Kugelfësch schrumpft", "subtitles.entity.puffer_fish.blow_up": "<PERSON><PERSON><PERSON><PERSON><PERSON> bléist sech op", "subtitles.entity.puffer_fish.death": "Kugelfësch stierft", "subtitles.entity.puffer_fish.flop": "Kugelfësch platscht", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.sting": "Kugelf<PERSON><PERSON> s<PERSON>cht", "subtitles.entity.rabbit.ambient": "Kanéngchen piipst", "subtitles.entity.rabbit.attack": "Kanéng<PERSON>", "subtitles.entity.rabbit.death": "Kanéngchen stierft", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "Kanéngchen hoppelt", "subtitles.entity.ravager.ambient": "Verwüster grunzt", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "subtitles.entity.ravager.celebrate": "Verwüster jubelt", "subtitles.entity.ravager.death": "Verwüster stierft", "subtitles.entity.ravager.hurt": "<PERSON>er<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.roar": "Verwüster brëllt", "subtitles.entity.ravager.step": "Verwüster trë<PERSON>t", "subtitles.entity.ravager.stunned": "Verwüster betäubt", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.salmon.flop": "<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.salmon.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.sheep.ambient": "Schof mät", "subtitles.entity.sheep.death": "Schof stierft", "subtitles.entity.sheep.hurt": "Sc<PERSON> h<PERSON><PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> la<PERSON>t", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> geet zou", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> geet op", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.shulker.teleport": "Shulker teleportéiert", "subtitles.entity.shulker_bullet.hit": "Shulker-Geschoss explodéiert", "subtitles.entity.shulker_bullet.hurt": "Shulker-Geschoss zerfält", "subtitles.entity.silverfish.ambient": "Sëlwerfëschelchen z<PERSON>t", "subtitles.entity.silverfish.death": "Sëlwerfëschelchen stierft", "subtitles.entity.silverfish.hurt": "Sëlwerfësche<PERSON><PERSON>", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON> klap<PERSON>", "subtitles.entity.skeleton.converted_to_stray": "Skelett verwandelt séch zu Vagabund", "subtitles.entity.skeleton.death": "Skelett stierft", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.skeleton.shoot": "Skelett schéisst", "subtitles.entity.skeleton_horse.ambient": "Skelettpäerd j<PERSON>", "subtitles.entity.skeleton_horse.death": "Skelettpäerd s<PERSON>ft", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton_horse.jump_water": "Skelettpäerd spréngt", "subtitles.entity.skeleton_horse.swim": "Skelettpäerd schw<PERSON>mm<PERSON>", "subtitles.entity.slime.attack": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ft", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.slime.squish": "<PERSON><PERSON><PERSON><PERSON> schwab<PERSON>", "subtitles.entity.sniffer.death": "Schnoffeler stierft", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON><PERSON><PERSON> buddelt", "subtitles.entity.sniffer.digging_stop": "Schnoffeler steet op", "subtitles.entity.sniffer.drop_seed": "Schnoffeler geheit Som", "subtitles.entity.sniffer.eat": "Schnoffeler frësst", "subtitles.entity.sniffer.egg_crack": "Schnoffeler-<PERSON><PERSON> bascht", "subtitles.entity.sniffer.egg_hatch": "Schnoffeler-Ee s<PERSON>ü<PERSON>t", "subtitles.entity.sniffer.happy": "<PERSON><PERSON>offeler freet sech", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.idle": "Schnoffeler grunzt", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON><PERSON>t", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON><PERSON> sicht", "subtitles.entity.sniffer.sniffing": "<PERSON>hnoffele<PERSON> schnoffelt", "subtitles.entity.sniffer.step": "<PERSON>hnoffeler <PERSON>", "subtitles.entity.snow_golem.death": "Schnéigolem stierft", "subtitles.entity.snow_golem.hurt": "Schnéigolem h<PERSON><PERSON>", "subtitles.entity.snowball.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON> flitt", "subtitles.entity.spider.ambient": "Spann zischt", "subtitles.entity.spider.death": "<PERSON>nn stierft", "subtitles.entity.spider.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.squid.ambient": "Tëntefësch schwëmmt", "subtitles.entity.squid.death": "Tëntefësch stierft", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.squid.squirt": "Tëntefësch sprëtzt Tënt", "subtitles.entity.stray.ambient": "Vagabund klappert", "subtitles.entity.stray.death": "Vagabund stierft", "subtitles.entity.stray.hurt": "Vagabund <PERSON><PERSON><PERSON>", "subtitles.entity.strider.death": "<PERSON>ride<PERSON> stierft", "subtitles.entity.strider.eat": "Strider frësst", "subtitles.entity.strider.happy": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.strider.idle": "St<PERSON><PERSON> p<PERSON>t", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON> weecht zer<PERSON>ck", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> stierft", "subtitles.entity.tadpole.flop": "Kauzekapp platscht", "subtitles.entity.tadpole.grow_up": "Kauzekapp wiisst", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.tnt.primed": "TNT zischt", "subtitles.entity.tropical_fish.death": "Tropefësch stierft", "subtitles.entity.tropical_fish.flop": "Tropefësch zappelt", "subtitles.entity.tropical_fish.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.ambient_land": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.death": "<PERSON><PERSON>dk<PERSON><PERSON><PERSON> s<PERSON>ft", "subtitles.entity.turtle.death_baby": "Schildkrötebëbee stierft", "subtitles.entity.turtle.egg_break": "Schildkrötenee zerbrécht", "subtitles.entity.turtle.egg_crack": "Schildkrötenee brécht", "subtitles.entity.turtle.egg_hatch": "Schildkrötejongt schlüft", "subtitles.entity.turtle.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.hurt_baby": "Schildkrötebëbee h<PERSON><PERSON>", "subtitles.entity.turtle.lay_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> leet een Ee", "subtitles.entity.turtle.shamble": "Sc<PERSON>dkr<PERSON><PERSON> watschelt", "subtitles.entity.turtle.shamble_baby": "Schildkrötebaby watschelt", "subtitles.entity.turtle.swim": "Schildkrö<PERSON> schwemmt", "subtitles.entity.vex.ambient": "Plogeescht belästegt", "subtitles.entity.vex.charge": "Plogeescht jäizt", "subtitles.entity.vex.death": "Plogeescht stierft", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON> knoutert", "subtitles.entity.villager.celebrate": "<PERSON><PERSON><PERSON> jubelt", "subtitles.entity.villager.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.villager.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.villager.no": "<PERSON><PERSON><PERSON> refus<PERSON><PERSON><PERSON>", "subtitles.entity.villager.trade": "<PERSON><PERSON><PERSON> handelt", "subtitles.entity.villager.work_armorer": "Panzermécher schafft", "subtitles.entity.villager.work_butcher": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.villager.work_cartographer": "Kart<PERSON> schafft", "subtitles.entity.villager.work_cleric": "Geeschtlechen schafft", "subtitles.entity.villager.work_farmer": "<PERSON> s<PERSON>", "subtitles.entity.villager.work_fisherman": "<PERSON><PERSON><PERSON> scha<PERSON>t", "subtitles.entity.villager.work_fletcher": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t", "subtitles.entity.villager.work_leatherworker": "<PERSON><PERSON><PERSON> scha<PERSON>t", "subtitles.entity.villager.work_librarian": "Bibliothekä<PERSON>", "subtitles.entity.villager.work_mason": "<PERSON><PERSON><PERSON><PERSON> schafft", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON> s<PERSON>t", "subtitles.entity.villager.work_toolsmith": "Geschirschmadd scha<PERSON>t", "subtitles.entity.villager.work_weaponsmith": "Rüstungsschmadd schafft", "subtitles.entity.villager.yes": "Awunner akzeptéiert", "subtitles.entity.vindicator.ambient": "Lakai murmelt", "subtitles.entity.vindicator.celebrate": "Lakai jubelt", "subtitles.entity.vindicator.death": "Lakai stierft", "subtitles.entity.vindicator.hurt": "Lakai h<PERSON><PERSON> schued", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> knoutert", "subtitles.entity.wandering_trader.death": "Nomadenhändler stierft", "subtitles.entity.wandering_trader.disappeared": "Nomadenhändler verschwënnt", "subtitles.entity.wandering_trader.drink_milk": "Nomadenhändler drénk<PERSON>", "subtitles.entity.wandering_trader.drink_potion": "Nomadenhändler drénkt Dronk", "subtitles.entity.wandering_trader.hurt": "Nomadenhänd<PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.no": "Nomadenhänd<PERSON> refus<PERSON><PERSON>t", "subtitles.entity.wandering_trader.reappeared": "Nomade<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.trade": "Nomadenhändler handelt", "subtitles.entity.wandering_trader.yes": "Nomadenhändler akzeptéiert", "subtitles.entity.warden.agitated": "<PERSON><PERSON>rter kräizt rosen", "subtitles.entity.warden.ambient": "<PERSON><PERSON><PERSON> jammert", "subtitles.entity.warden.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.attack_impact": "<PERSON><PERSON><PERSON> lant <PERSON>", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON> stierfrt", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON> grueft", "subtitles.entity.warden.emerge": "<PERSON><PERSON>rter taucht op", "subtitles.entity.warden.heartbeat": "Wärterhäerz klappt", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON> h<PERSON> schued", "subtitles.entity.warden.listening": "Wärter mierkt", "subtitles.entity.warden.listening_angry": "W<PERSON>rter mierkt rosen", "subtitles.entity.warden.nearby_close": "<PERSON><PERSON><PERSON> kënnt méi no", "subtitles.entity.warden.nearby_closer": "<PERSON><PERSON><PERSON> r<PERSON>t fir", "subtitles.entity.warden.nearby_closest": "Wärter zitt no", "subtitles.entity.warden.roar": "<PERSON><PERSON><PERSON>", "subtitles.entity.warden.sniff": "<PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON><PERSON> knallt", "subtitles.entity.warden.sonic_charge": "W<PERSON>rter lued op", "subtitles.entity.warden.step": "<PERSON><PERSON><PERSON> leeft", "subtitles.entity.warden.tendril_clicks": "Wärterranke klicken", "subtitles.entity.wind_charge.throw": "<PERSON><PERSON><PERSON><PERSON> flitt", "subtitles.entity.wind_charge.wind_burst": "<PERSON><PERSON><PERSON><PERSON> platzt", "subtitles.entity.witch.ambient": "Hex kickelt", "subtitles.entity.witch.celebrate": "<PERSON><PERSON> jubelt", "subtitles.entity.witch.death": "Hex stierft", "subtitles.entity.witch.drink": "<PERSON><PERSON> d<PERSON>", "subtitles.entity.witch.hurt": "<PERSON><PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON> werft", "subtitles.entity.wither.ambient": "<PERSON>er reegt sech ob", "subtitles.entity.wither.death": "<PERSON>er stierft", "subtitles.entity.wither.hurt": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wither.spawn": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON><PERSON><PERSON> klappert", "subtitles.entity.wither_skeleton.death": "Witherskelett stierft", "subtitles.entity.wither_skeleton.hurt": "Witherskelett <PERSON><PERSON><PERSON>", "subtitles.entity.wolf.ambient": "<PERSON><PERSON><PERSON> <PERSON><PERSON>t", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON> <PERSON>t", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON> knurrt", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.wolf.pant": "<PERSON><PERSON><PERSON> <PERSON><PERSON>t", "subtitles.entity.wolf.shake": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sech", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> g<PERSON>zt", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> grunzt rosen", "subtitles.entity.zoglin.attack": "Zoglin gräift un", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> stierft", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> leeft", "subtitles.entity.zombie.ambient": "Zombie keimt", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON>", "subtitles.entity.zombie.converted_to_drowned": "Zombie gëtt zu engem Erdronkenen", "subtitles.entity.zombie.death": "Zombie stierft", "subtitles.entity.zombie.destroy_egg": "Schildkröten E<PERSON> zertr<PERSON>", "subtitles.entity.zombie.hurt": "<PERSON> h<PERSON><PERSON>", "subtitles.entity.zombie.infect": "<PERSON> in<PERSON>z<PERSON><PERSON>t", "subtitles.entity.zombie_horse.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie_villager.ambient": "Zombieawunner keimt", "subtitles.entity.zombie_villager.converted": "<PERSON><PERSON><PERSON><PERSON> geh<PERSON>t", "subtitles.entity.zombie_villager.cure": "Zombieawunner zischt", "subtitles.entity.zombie_villager.death": "Zombieawunner stierft", "subtitles.entity.zombie_villager.hurt": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.zombified_piglin.ambient": "Zombifizéierte Piglin grunzt", "subtitles.entity.zombified_piglin.angry": "Zombifizéierte Piglin grunzt rosen", "subtitles.entity.zombified_piglin.death": "Zombifizéierte Piglin stierft", "subtitles.entity.zombified_piglin.hurt": "Zombifizéierte <PERSON>", "subtitles.event.mob_effect.bad_omen": "<PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON>", "subtitles.event.mob_effect.raid_omen": "Iwwerfall noht", "subtitles.event.mob_effect.trial_omen": "Onheilvoll Prüfung noht", "subtitles.event.raid.horn": "O<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip": "Ausrüstung ugeluecht", "subtitles.item.armor.equip_chain": "Ketterüstung rëselt", "subtitles.item.armor.equip_diamond": "Diamantrüstung klimpert", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON> raschelt", "subtitles.item.armor.equip_gold": "Goldrüstung klimpert", "subtitles.item.armor.equip_iron": "Eiserüstung klimpert", "subtitles.item.armor.equip_leather": "Liederrüstung grätscht", "subtitles.item.armor.equip_netherite": "Netheritrüstung scheppert", "subtitles.item.armor.equip_turtle": "<PERSON>hildkr<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_wolf": "Wollefspanzer festgemaach", "subtitles.item.armor.unequip_wolf": "Wolfspanzer ofgesträift", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON> schappt", "subtitles.item.axe.strip": "Axt Sträifen", "subtitles.item.axe.wax_off": "La cire désactivé", "subtitles.item.bone_meal.use": "Knochen Miel <PERSON>", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON> raschelt", "subtitles.item.book.put": "Buch klatscht", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON><PERSON> eidel gemaach", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "Ofpinselen", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON><PERSON><PERSON>pinselen", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON><PERSON><PERSON> <PERSON>pinselen ofgeschloss", "subtitles.item.brush.brushing.sand": "Sand ofpinselen", "subtitles.item.brush.brushing.sand.complete": "Sand ofpinselen ofgeschloss", "subtitles.item.bucket.empty": "<PERSON><PERSON><PERSON> eidel gema<PERSON>", "subtitles.item.bucket.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.bucket.fill_axolotl": "Axolotl sch<PERSON>t", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON><PERSON> gefaangen", "subtitles.item.bucket.fill_tadpole": "Kauzekapp gefaangen", "subtitles.item.bundle.drop_contents": "Bëndel eidel", "subtitles.item.bundle.insert": "<PERSON><PERSON><PERSON> g<PERSON>", "subtitles.item.bundle.insert_fail": "Bëndel voll", "subtitles.item.bundle.remove_one": "Artikel ausgepakt", "subtitles.item.chorus_fruit.teleport": "Spiller teleportéiert", "subtitles.item.crop.plant": "Planz gesat", "subtitles.item.crossbow.charge": "Armb<PERSON> spaant", "subtitles.item.crossbow.hit": "<PERSON><PERSON>", "subtitles.item.crossbow.load": "<PERSON><PERSON><PERSON> luet", "subtitles.item.crossbow.shoot": "Armb<PERSON> sch<PERSON>t", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "Feierball ugefaangen", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON> klickt", "subtitles.item.glow_ink_sac.use": "Liichttëntesak", "subtitles.item.goat_horn.play": "<PERSON><PERSON><PERSON>t", "subtitles.item.hoe.till": "<PERSON><PERSON> plout", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON> drun", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Taches de sac d'encre", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Kompass op Leetsteen ausgeriicht", "subtitles.item.mace.smash_air": "Sträitkolben zerschmettert", "subtitles.item.mace.smash_ground": "Sträitkolben zerschmettert", "subtitles.item.nether_wart.plant": "Planz geplanzt", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>t", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON><PERSON> h<PERSON><PERSON><PERSON>", "subtitles.item.shovel.flatten": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.spyglass.stop_using": "Spyglas retracts", "subtitles.item.spyglass.use": "Spektiv fiert aus", "subtitles.item.totem.use": "Totem ausgeléist", "subtitles.item.trident.hit": "Dräizack spiisst op", "subtitles.item.trident.hit_ground": "<PERSON><PERSON><PERSON><PERSON> vib<PERSON><PERSON>", "subtitles.item.trident.return": "Dräizack kënnt zeréck", "subtitles.item.trident.riptide": "Dräizack zitt", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON><PERSON> kli<PERSON>t", "subtitles.item.trident.thunder": "Dr<PERSON><PERSON><PERSON><PERSON><PERSON> grellt", "subtitles.item.wolf_armor.break": "Wollefspanzer zerbrécht", "subtitles.item.wolf_armor.crack": "Wollefspanzer kraacht", "subtitles.item.wolf_armor.damage": "<PERSON><PERSON><PERSON><PERSON><PERSON> h<PERSON><PERSON>", "subtitles.item.wolf_armor.repair": "Wollef<PERSON><PERSON> gefléckt", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON>", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON>", "subtitles.ui.hud.bubble_pop": "Loftreserv hëlt of", "subtitles.ui.loom.take_result": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON><PERSON> ben<PERSON>t", "subtitles.weather.rain": "<PERSON><PERSON> fält", "symlink_warning.message": "Welten aus Ordneren mat symbolesche Verknëpfungen ze lueden, stellt e Risiko do, wann du dech net genee domat auskenns.\nW.e.g. besich %s, fir méi ze erfueren.", "symlink_warning.message.pack": "<PERSON>äck mat symbolesche Verkneppungen ze lueden, stellt e Risiko do, wann du dech net genee domat auskenns. W.e.g. besich %s, fir méi ze erfueren.", "symlink_warning.message.world": "Welten aus Ordneren mat symbolesche Verknëpfungen ze lueden, stellt e Risiko do, wann du dech net genee domat auskenns.\nW.e.g. besich %s, fir méi ze erfueren.", "symlink_warning.more_info": "<PERSON><PERSON><PERSON>", "symlink_warning.title": "Weltodner enthält symbol<PERSON>ch Shortcutten", "symlink_warning.title.pack": "Bäigefüü<PERSON>(e) <PERSON>/Päck enthält/enthale symbol<PERSON><PERSON>", "symlink_warning.title.world": "Weltodner enthält symbol<PERSON>ch Shortcutten", "team.collision.always": "Ëmmer", "team.collision.never": "<PERSON>", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON> d<PERSON> aner <PERSON>", "team.collision.pushOwnTeam": "<PERSON><PERSON><PERSON> déi eegen Teammemberen", "team.notFound": "Onbekannt Team: '%s'", "team.visibility.always": "Ëmmer", "team.visibility.hideForOtherTeams": "Fir aner Teams verstoppen", "team.visibility.hideForOwnTeam": "Fir dat eegen Team verstoppen", "team.visibility.never": "<PERSON>", "telemetry.event.advancement_made.description": "<PERSON><PERSON>, <PERSON><PERSON> wéi enge Viraussetzunge Fortschrëtter erzielt ginn, kann eis dobäi h<PERSON>, <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> besser ze verstinn an en ze verbesseren.", "telemetry.event.advancement_made.title": "Fortschr<PERSON>tt er<PERSON>lt", "telemetry.event.game_load_times.description": "Dëst Evenement kann eis h<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, wou Verbesserunge vum Startvirgang noutwendeg sinn, andeems et d'Ausféierungszäite vun den eenzelnen Startphase mësst.", "telemetry.event.game_load_times.title": "Spillluetzäiten", "telemetry.event.optional": "%s (optional)", "telemetry.event.optional.disabled": "%s (optional) - Desaktiv<PERSON><PERSON><PERSON>", "telemetry.event.performance_metrics.description": "Di genee Ermëttlung vum allgemenge Leeschtungsprofil vu Minecraft hëlleft eis, d'Spill an eng Villzuel vu Gerätereegenschaften a Betribssystemer ofzestëmmen an dofir ze optiméieren.\nD'Angabe vun der Spillversioun hëlleft eis, d'Leeschtungsprofil fir nei Versioune vu Minecraft ze vergläichen.", "telemetry.event.performance_metrics.title": "Leeschtungsmetriken", "telemetry.event.required": "%s (benéidegt)", "telemetry.event.world_load_times.description": "Et ass fir eis wichteg ze verstoen, wéi laang et dauert, eng Welt ze betrieden a wéi sech dat am Laf vun der Zäit ännert. Wa mer beispillsweis nei Spillelementer bäimaachen oder méi grouss technesch Ännerunge maachen, musse mir g<PERSON>, wéi eng Auswierkungen dat op Luetzäiten huet.", "telemetry.event.world_load_times.title": "Weltluetzäiten", "telemetry.event.world_loaded.description": "<PERSON><PERSON> <PERSON>, w<PERSON><PERSON>ller Minecraft spillen (z. <PERSON><PERSON>, Spillversioun a modifizéierte Client respektiv Server), er<PERSON><PERSON><PERSON> et eis, Spillaktualiséierungen op des Beräicher auszeriichten, di hinne vu Bedeitung sinn.\nD'Evenement \"Welt gelueden\" ass mat dem Evenement \"Welt entlueden\" gekoppelt, fir ze berechnen, wéi laang d'Spillsëtzung gedauert huet.", "telemetry.event.world_loaded.title": "Welt gelueden", "telemetry.event.world_unloaded.description": "Des Evenement ass mat dem Evenement \"Welt gelueden\" g<PERSON><PERSON><PERSON><PERSON>, fir ze berechnen, wéi laang d'Weltsëtzung gedauert huet. \nWann eng Weltsëtzung beend gouf (Verloosse vun der Welt respektiv Trennen vun der Verbindung zu engem Server), g<PERSON><PERSON> (a Sekonnen an Ticks) gem<PERSON>s.", "telemetry.event.world_unloaded.title": "<PERSON>lt entlueden", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Ticks)", "telemetry.property.advancement_id.title": "Fortschrëtts-ID", "telemetry.property.client_id.title": "Client-ID", "telemetry.property.client_modded.title": "Client modifizéiert", "telemetry.property.dedicated_memory_kb.title": "Dediéierten Aarbechtsspäicher (KB)", "telemetry.property.event_timestamp_utc.title": "Zäitpunkt vum Evenement (UTC)", "telemetry.property.frame_rate_samples.title": "Datewäerter fir d'Bildfrequenz (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.game_version.title": "Spillversioun", "telemetry.property.launcher_name.title": "Launcher-Numm", "telemetry.property.load_time_bootstrap_ms.title": "Initialiséierungszäit (Millisekonnen)", "telemetry.property.load_time_loading_overlay_ms.title": "Zäit am Luedbildschierm (Millisekonnen)", "telemetry.property.load_time_pre_window_ms.title": "<PERSON><PERSON><PERSON>, ier d'Spillfënster opgeet (Millisekonnen)", "telemetry.property.load_time_total_time_ms.title": "Gesamt Luetzäit (Millisekonnen)", "telemetry.property.minecraft_session_id.title": "Minecraft-Sëtzungs-ID", "telemetry.property.new_world.title": "<PERSON><PERSON><PERSON>", "telemetry.property.number_of_samples.title": "Unzuel vun den Datëwäerter", "telemetry.property.operating_system.title": "Betribssystem", "telemetry.property.opt_in.title": "Erfuerderlecht/optionellt <PERSON>", "telemetry.property.platform.title": "Plattform", "telemetry.property.realms_map_content.title": "Realms-Weltinhalt (Numm vum Minispill)", "telemetry.property.render_distance.title": "Renderdistanz", "telemetry.property.render_time_samples.title": "Datewäerter fir d'Renderzäit", "telemetry.property.seconds_since_load.title": "<PERSON>ä<PERSON> zënter dem Lueden (Sekonnen)", "telemetry.property.server_modded.title": "Server modifiz<PERSON><PERSON>t", "telemetry.property.server_type.title": "Servertyp", "telemetry.property.ticks_since_load.title": "<PERSON><PERSON><PERSON> zënter dem Lueden (Ticks)", "telemetry.property.used_memory_samples.title": "Benotzten Aarbechtsspäicher", "telemetry.property.user_id.title": "Benotzer-ID", "telemetry.property.world_load_time_ms.title": "Weltluetzäit (Millisekonnen)", "telemetry.property.world_session_id.title": "Welt-Sëtzungs-ID", "telemetry_info.button.give_feedback": "Feedback of<PERSON><PERSON>", "telemetry_info.button.privacy_statement": "Dateschutzerklärung", "telemetry_info.button.show_data": "<PERSON><PERSON>", "telemetry_info.opt_in.description": "Ech stëmmen der Iwwermëttelung vun optionalen Telemetriedaten zou", "telemetry_info.property_title": "Includ<PERSON><PERSON><PERSON>", "telemetry_info.screen.description": "D'Sammele vun dëse <PERSON> h<PERSON> eis, Minecraft ze verbesseren, andeem mir eis domat an d'Richtunge weise loossen, déi eise Spiller wichteg sinn.\nDu kanns och zousätzlecht Feedback ofginn, domat mer Minecraft weider verbessere kënnen.", "telemetry_info.screen.title": "Telemetrie-Datesammelung", "test.error.block_property_mismatch": "Zoustand %s gouf als %s erwaart, war awer %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Objet %s erfëllt Test net: %s", "test.error.entity_property_details": "Objet %s huet Test %s net erfëllt: %s erwaart, war awer %s", "test.error.expected_block": "Block %s erwaart, war awer %s", "test.error.expected_block_tag": "Block gouf an #%s erwaart, war awer %s", "test.error.expected_container_contents": "Be<PERSON><PERSON>lter muss enthalen: %s", "test.error.expected_container_contents_single": "Be<PERSON><PERSON>lter muss e(n) %s enthalen", "test.error.expected_empty_container": "<PERSON><PERSON><PERSON><PERSON> muss eidel sinn", "test.error.expected_entity": "%s erwaart", "test.error.expected_entity_around": "%s gouf bei %s, %s, %s erwaart", "test.error.expected_entity_count": "%s Objet<PERSON> vum Typ %s erwa<PERSON>, %s fonnt", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Objetdate fir %s stemmen net iwwerdeen", "test.error.expected_entity_effect": "Efekkt fir %s gouf als %s %s erwaart", "test.error.expected_entity_having": "Objetinventar hätt %s enthale sollen", "test.error.expected_entity_holding": "Objet hätt %s hale sollen", "test.error.expected_entity_in_test": "Erwaart %s am Test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Géigestand vum Typ %s erwaart", "test.error.expected_items_count": "%s Geigestänn vum Typ %s erwa<PERSON>, awer %s fonnt", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Onerwaarte Blocktyp fonnt: %s", "test.error.missing_block_entity": "<PERSON><PERSON>e <PERSON>objet", "test.error.position": "%s bei %s, %s, %s (relativ: %s, %s, %s) an Tick %s", "test.error.sequence.condition_already_triggered": "<PERSON><PERSON><PERSON><PERSON><PERSON> schonn an Tick %s ausgeléist", "test.error.sequence.condition_not_triggered": "Konditioun net ausgeléist", "test.error.sequence.invalid_tick": "An ongültegem Tick stattfonnt: %s gouf erwaart", "test.error.sequence.not_completed": "Test virum Ofschloss vum Virgang ofgelaf", "test.error.set_biome": "Biom fir Test konnt net festgeluecht ginn", "test.error.spawn_failure": "Objet %s konnt net erzeegt ginn", "test.error.state_not_equal": "Falschen Zoustand. %s erwa<PERSON>, war %s", "test.error.structure.failure": "Testkonstruktioun fir %s konnt net placéiert ginn", "test.error.tick": "%s bei Tick %s", "test.error.ticking_without_structure": "Testausféierung goung un, ier Konstruktioun placéiert gouf", "test.error.timeout.no_result": "Weder Erfolleg nach Feelschlag bannent %s Ticks", "test.error.timeout.no_sequences_finished": "Keng <PERSON>of<PERSON>f bannent %s Ticks ofgeschlass", "test.error.too_many_entities": "Just en %s ëm %s, %s, %s erwaart, mais %s fonnt", "test.error.unexpected_block": "Block gouf net als %s erwaart", "test.error.unexpected_entity": "<PERSON> erwaart, %s virzefannen", "test.error.unexpected_item": "Géigestand vum Typ %s gouf net erwaart", "test.error.unknown": "Onbekannten interne Feeler: %s", "test.error.value_not_equal": "%s gouf als %s erwaart, war awer %s", "test.error.wrong_block_entity": "Falsche Blockobjettyp: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Ze vill %s-Bléck", "test_block.invalid_timeout": "Ongültegen Timeout (%s) – muss eng positiv Unzuel vun Ticks sinn", "test_block.message": "Noriicht:", "test_block.mode.accept": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test_block.mode.fail": "Feeler", "test_block.mode.log": "Protokoll", "test_block.mode.start": "Ufank", "test_block.mode_info.accept": "Akzeptanzmodus – Hellt (deelweisen) Erfolleg vun engem Test entgéint", "test_block.mode_info.fail": "Fehlermodus – Léisst den Test Feelschloen", "test_block.mode_info.log": "Protokollmodus – Protokollier eng Noriicht", "test_block.mode_info.start": "Startmodus – Den Ufankspunkt vun engem Test", "test_instance.action.reset": "Zerécksetzen a lueden", "test_instance.action.run": "Lueden an ausféieren", "test_instance.action.save": "Konstruktioun späicheren", "test_instance.description.batch": "Deel: %s", "test_instance.description.failed": "Feelgeschloen: %s", "test_instance.description.function": "Funktioun: %s", "test_instance.description.invalid_id": "Ongülteg Préifkennung", "test_instance.description.no_test": "Dësen Test existéiert net", "test_instance.description.structure": "Konstruktioun: %s", "test_instance.description.type": "Typ: %s", "test_instance.type.block_based": "<PERSON><PERSON><PERSON><PERSON><PERSON> Test", "test_instance.type.function": "Integréierte Funktiounstest", "test_instance_block.entities": "Objeten:", "test_instance_block.error.no_test": "Testinstanz bei %s, %s, %s kann net ausgeféiert ginn, well hir keen Test zougewisen ass", "test_instance_block.error.no_test_structure": "Testinstanz bei %s, %s, %s konnt net ausgeféiert ginn, well hir Testkonstruktion feelt", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[on<PERSON><PERSON><PERSON><PERSON>]", "test_instance_block.reset_success": "Test %s gouf erfollegräich zeréckgesat", "test_instance_block.rotation": "Rotatioun:", "test_instance_block.size": "Gréisst vun der Testkonstruktioun", "test_instance_block.starting": "Test %s g<PERSON>tt gestart", "test_instance_block.test_id": "Préifinstanz-ID", "title.32bit.deprecation": "32-Bit System entdeckt: dë<PERSON> kann Iech verhënneren an Zuku<PERSON>ft ze spillen well e 64-Bit System erfuerderlech ass!", "title.32bit.deprecation.realms": "Minecraft wäert geschwënn e 64-Bit System erfuerderen, wat dir verhënnert Realms op dësem Apparat ze spillen oder ze benotzen. Du muss all Realms-Abonnementer manuell kënnegen.", "title.32bit.deprecation.realms.check": "<PERSON><PERSON>ran net erëm", "title.32bit.deprecation.realms.header": "32-Bit System entdeckt", "title.credits": "Copyright Mojang AB. Net verbreeden!", "title.multiplayer.disabled": "Multiplayer ass behënnert. Kuckt w. e. g. Är Microsoft Kont Astellungen.", "title.multiplayer.disabled.banned.name": "Du muss däi Profilnumm änneren, bevir du online spille kanns", "title.multiplayer.disabled.banned.permanent": "Äre Kont ass permanent vum Online Spill suspendéiert", "title.multiplayer.disabled.banned.temporary": "Äre Kont ass temporär vum Online Spill suspendéiert", "title.multiplayer.lan": "Multispiller (LAN)", "title.multiplayer.other": "Multispiller (Drëtt-Parteie Server)", "title.multiplayer.realms": "Multispiller (Realms)", "title.singleplayer": "Eenzelspiller", "translation.test.args": "%s %s", "translation.test.complex": "Präfix, %s%2$s nach eng kéier %s an %1$s an zum Schluss nach %s an nach %1$s!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "moien %", "translation.test.invalid2": "hi %s", "translation.test.none": "<PERSON><PERSON>, Welt!", "translation.test.world": "Welt", "trim_material.minecraft.amethyst": "Amethyst", "trim_material.minecraft.copper": "<PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "Smaragd", "trim_material.minecraft.gold": "Gold", "trim_material.minecraft.iron": "Eisen", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "Netherit", "trim_material.minecraft.quartz": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.redstone": "Redstone", "trim_material.minecraft.resin": "<PERSON><PERSON>", "trim_pattern.minecraft.bolt": "Bolzen-Rüstungsbesaz", "trim_pattern.minecraft.coast": "Küsten-Rüstungsbesaz", "trim_pattern.minecraft.dune": "Dünen-Rüstungsbesaz", "trim_pattern.minecraft.eye": "Aen-Rüstungsbesaz", "trim_pattern.minecraft.flow": "Floss-Rüstungsbesaz", "trim_pattern.minecraft.host": "Restaurateur-Rüstungsbesaz", "trim_pattern.minecraft.raiser": "Opzéier-Rüstungsbesaz", "trim_pattern.minecraft.rib": "Rëppen-Rüstungsbesaz", "trim_pattern.minecraft.sentry": "Wachen-Rüstungsbesaz", "trim_pattern.minecraft.shaper": "Gestalter-Rüstungsbesaz", "trim_pattern.minecraft.silence": "Stëll-Rüstungsbesaz", "trim_pattern.minecraft.snout": "Schnëss-Rüstungsbesaz", "trim_pattern.minecraft.spire": "Tuermspëtzten-Rüstungsbesaz", "trim_pattern.minecraft.tide": "Gezeiten-Rüstungsbesaz", "trim_pattern.minecraft.vex": "Plogegeescht-Rüstungsbesaz", "trim_pattern.minecraft.ward": "Wart-Rüstungsbesaz", "trim_pattern.minecraft.wayfinder": "Weefanner-Rüstungsbesaz", "trim_pattern.minecraft.wild": "Wildnis-Rüstungsbesaz", "tutorial.bundleInsert.description": "<PERSON><PERSON><PERSON> fir Elementer derbäizefügen", "tutorial.bundleInsert.title": "Benotzt e Package", "tutorial.craft_planks.description": "D'Rezeptbuch hëlleft dir", "tutorial.craft_planks.title": "<PERSON><PERSON>briede<PERSON> hier", "tutorial.find_tree.description": "Schlo op en fir Holz", "tutorial.find_tree.title": "<PERSON><PERSON> ee <PERSON>", "tutorial.look.description": "<PERSON><PERSON><PERSON> deng <PERSON>", "tutorial.look.title": "<PERSON><PERSON> dech", "tutorial.move.description": "Sprang mat %s", "tutorial.move.title": "Laf mat %s, %s, %s an %s", "tutorial.open_inventory.description": "Dréck %s", "tutorial.open_inventory.title": "<PERSON><PERSON> däin <PERSON>ar op", "tutorial.punch_tree.description": "Hal %s fest", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON>", "tutorial.socialInteractions.description": "Dréck %s fir opzemaachen", "tutorial.socialInteractions.title": "Sozial Interaktiounen", "upgrade.minecraft.netherite_upgrade": "Netheritopwäertung"}