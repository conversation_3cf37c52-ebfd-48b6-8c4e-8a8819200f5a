{"accessibility.onboarding.accessibility.button": "Esteettömyysasetukset...", "accessibility.onboarding.screen.narrator": "Paina enteriä ottaaksesi kertojan käyttöön", "accessibility.onboarding.screen.title": "Tervetuloa Minecraftiin!\n\nHaluaisitko ottaa kertojan käyttöön tai vierailla esteettömyysasetuksissa?", "addServer.add": "Val<PERSON>", "addServer.enterIp": "Palvelimen osoite", "addServer.enterName": "Palvelimen nimi", "addServer.resourcePack": "Palvelimen resurssipaketit", "addServer.resourcePack.disabled": "Poissa k<PERSON>ö<PERSON>", "addServer.resourcePack.enabled": "Käytössä", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON>", "addServer.title": "Muokkaa palvelimen tietoja", "advMode.command": "Konsolikomento", "advMode.mode": "Tila", "advMode.mode.auto": "<PERSON>ist<PERSON>", "advMode.mode.autoexec.bat": "<PERSON><PERSON>", "advMode.mode.conditional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstone": "<PERSON><PERSON><PERSON><PERSON>", "advMode.mode.redstoneTriggered": "Tarvitsee redstonea", "advMode.mode.sequence": "<PERSON><PERSON><PERSON>", "advMode.mode.unconditional": "<PERSON><PERSON><PERSON><PERSON>", "advMode.notAllowed": "<PERSON><PERSON><PERSON><PERSON> on oltava operaattori luovuustilassa", "advMode.notEnabled": "Komentokuutiot eivät ole käytössä tällä palvelimella", "advMode.previousOutput": "Edellinen suorite", "advMode.setCommand": "Aseta kuutiolle konsolikomento", "advMode.setCommand.success": "Komento asetettu: %s", "advMode.trackOutput": "Jäljitä ulostulo<PERSON>", "advMode.triggering": "<PERSON><PERSON><PERSON><PERSON>", "advMode.type": "Tyyppi", "advancement.advancementNotFound": "Tuntematon edistysaskel: %s", "advancements.adventure.adventuring_time.description": "Löydä jokainen biomi", "advancements.adventure.adventuring_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> aika", "advancements.adventure.arbalistic.description": "Tapa viisi eri olentoa y<PERSON> varsijousiammuksella", "advancements.adventure.arbalistic.title": "Varsijousimies", "advancements.adventure.avoid_vibration.description": "<PERSON><PERSON>i sculk-sensorin tai Wardenin ohi estääksesi niitä havaitsemasta sinua", "advancements.adventure.avoid_vibration.title": "Hiiviskely 100", "advancements.adventure.blowback.description": "Tapa viima torjumalla sen itsensä ampuma puhuripallo sitä päin", "advancements.adventure.blowback.title": "<PERSON><PERSON> puhallus", "advancements.adventure.brush_armadillo.description": "Hanki suomuja vyötiäiseltä käyttämällä sivellintä", "advancements.adventure.brush_armadillo.title": "Suo mulle suomuja", "advancements.adventure.bullseye.description": "<PERSON><PERSON> ma<PERSON>ulukuution napakymppiin vähintään 30 kuution päästä", "advancements.adventure.bullseye.title": "Napakymppi", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Valmista koristeltu ruukku neljästä ruukunsirpaleesta", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON><PERSON><PERSON> en<PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Ole työstimen lähellä kun se työstää työstimen", "advancements.adventure.crafters_crafting_crafters.title": "Työstimet työstämässä työstimiä", "advancements.adventure.fall_from_world_height.description": "Putoa ma<PERSON>man k<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> (rakennusyläraj<PERSON>a) matalimpaan kuolematta", "advancements.adventure.fall_from_world_height.title": "Lu<PERSON>t ja jyrkänteet", "advancements.adventure.heart_transplanter.description": "<PERSON><PERSON> na<PERSON>eva sydän o<PERSON> asentoon kahden kalvas<PERSON> väliin", "advancements.adventure.heart_transplanter.title": "Sydämensiirtäjä", "advancements.adventure.hero_of_the_village.description": "Puolusta kylää onnistuneesti hyökkäykseltä", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.honey_block_slide.description": "Hyppää hunajakuutiolle pehmentääksesi pudotusta", "advancements.adventure.honey_block_slide.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.kill_a_mob.description": "<PERSON><PERSON> mikä tahansa vih<PERSON>en hirviö", "advancements.adventure.kill_a_mob.title": "Hirviönmetsästäjä", "advancements.adventure.kill_all_mobs.description": "Tapa yksi jokaista vihamielistä hirviötä", "advancements.adventure.kill_all_mobs.title": "Hirviöitä tapettu", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "<PERSON><PERSON> olento sculk-herättimen lähellä", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Se leviää", "advancements.adventure.lighten_up.description": "Raaputa kuparilamppua kirveellä tehdäksesi siitä kirk<PERSON>", "advancements.adventure.lighten_up.title": "<PERSON><PERSON><PERSON> valo", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "<PERSON><PERSON><PERSON> k<PERSON>äist<PERSON> ei-toivotulta salamaniskulta aiheuttamatta tulipaloa", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Ylijännitesuoja", "advancements.adventure.minecraft_trials_edition.description": "<PERSON><PERSON> kamm<PERSON>", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Koetusversio", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON>", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON>", "advancements.adventure.overoverkill.description": "Tee 50 sydämen verran vahinkoa yhdellä nuijan iskulla", "advancements.adventure.overoverkill.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "<PERSON><PERSON> ni<PERSON><PERSON> eloa soittamalla musiikkia levysoittimella", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON><PERSON> taikaa", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "<PERSON><PERSON> täytettävän kirjah<PERSON>lyn virtasignaali komparaattorilla", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> voima", "advancements.adventure.revaulting.description": "<PERSON><PERSON> pahaenteinen holvi pahaenteisellä koetuksen avaimella", "advancements.adventure.revaulting.title": "<PERSON>l<PERSON>? Ei, jotain parempaa", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tut<PERSON><PERSON> ja taistelua", "advancements.adventure.root.title": "Se<PERSON><PERSON><PERSON>", "advancements.adventure.salvage_sherd.description": "<PERSON><PERSON><PERSON><PERSON> epäilyttävää kuutiota saadaksesi ruukunsirpaleen", "advancements.adventure.salvage_sherd.title": "Jäännöksiä kunnio<PERSON>aen", "advancements.adventure.shoot_arrow.description": "Ammu jotain nuolella", "advancements.adventure.shoot_arrow.title": "Tähtää", "advancements.adventure.sleep_in_bed.description": "Nuku sängys<PERSON>ä vaihta<PERSON>esi uudelleensyntymispisteesi", "advancements.adventure.sleep_in_bed.title": "<PERSON><PERSON><PERSON> unia", "advancements.adventure.sniper_duel.description": "Surmaa lu<PERSON>nko vähintään 50 metrin päästä", "advancements.adventure.sniper_duel.title": "Tarkka-ammuntaa", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON> ender-lohikäärmettä kaukoputken läpi", "advancements.adventure.spyglass_at_dragon.title": "Onko se lentokone?", "advancements.adventure.spyglass_at_ghast.description": "<PERSON><PERSON> g<PERSON> kauk<PERSON> läpi", "advancements.adventure.spyglass_at_ghast.title": "Onko se ilmapallo?", "advancements.adventure.spyglass_at_parrot.description": "<PERSON><PERSON> pap<PERSON>a kaukop<PERSON> läpi", "advancements.adventure.spyglass_at_parrot.title": "Onko se lintu?", "advancements.adventure.summon_iron_golem.description": "Kutsu rautagolem auttamaan kylän pu<PERSON>", "advancements.adventure.summon_iron_golem.title": "Järjestyksenvalvoja", "advancements.adventure.throw_trident.description": "<PERSON><PERSON><PERSON> atrain jotain päin.\nHuomaa: <PERSON><PERSON> pois heittäminen ei ole hyvä idea.", "advancements.adventure.throw_trident.title": "Heittovitsi", "advancements.adventure.totem_of_undying.description": "Käytä kuolemattomuuden toteemia huijataksesi kuolemaa", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oh<PERSON>", "advancements.adventure.trade.description": "<PERSON><PERSON> vaiht<PERSON>uppa kyläläisen kanssa", "advancements.adventure.trade.title": "Mi<PERSON><PERSON>!", "advancements.adventure.trade_at_world_height.description": "<PERSON><PERSON><PERSON> ka<PERSON><PERSON>a kyläläisen kanssa rakentamisen korkeusrajalla", "advancements.adventure.trade_at_world_height.title": "Tähtikauppias", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON>, ward<PERSON><PERSON>, hi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON>, vuo<PERSON>tten sekä tiennäyttäjän haarniskakoristeiden taontamalleja vähintään kerran", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Taontaa tyylillä", "advancements.adventure.trim_with_any_armor_pattern.description": "Työstä koristeltu haarniska takomispöydällä", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.description": "Ta<PERSON> kaksi aavetta lävistävällä nuolella", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON><PERSON> kärpästä y<PERSON>dellä iskulla", "advancements.adventure.under_lock_and_key.description": "<PERSON><PERSON> holvi k<PERSON> a<PERSON>", "advancements.adventure.under_lock_and_key.title": "Lukkojen takana", "advancements.adventure.use_lodestone.description": "Käytä kompassia magneettikiveen", "advancements.adventure.use_lodestone.title": "<PERSON> mun ajaa m<PERSON>", "advancements.adventure.very_very_frightening.description": "<PERSON><PERSON> k<PERSON> sa<PERSON>a", "advancements.adventure.very_very_frightening.title": "Todella, todella karmivaa", "advancements.adventure.voluntary_exile.description": "Surmaa hyökkäyksen kapteeni.\nKylien välttelemistä kannattaa harkita toista<PERSON>i...", "advancements.adventure.voluntary_exile.title": "Vapaaehtoinen <PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "<PERSON><PERSON><PERSON><PERSON> puuterilumella... uppo<PERSON><PERSON> siihen", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "<PERSON><PERSON><PERSON> kuin j<PERSON>nis", "advancements.adventure.who_needs_rockets.description": "Käytä puhuripalloa laukaistaksesi itsesi 8 kuutiota ylöspäin", "advancements.adventure.who_needs_rockets.title": "Kuka tarvitsee raketteja?", "advancements.adventure.whos_the_pillager_now.description": "<PERSON><PERSON><PERSON>n ma<PERSON>a omaa l<PERSON>äkettään", "advancements.adventure.whos_the_pillager_now.title": "<PERSON>ka nyt on ryöstäjä?", "advancements.empty": "Täällä ei näytä olevan mitään...", "advancements.end.dragon_breath.description": "Kerää lohikäärmeen henk<PERSON><PERSON>", "advancements.end.dragon_breath.title": "Tarvitset minttua", "advancements.end.dragon_egg.description": "<PERSON>ele <PERSON>ärmeen munaa", "advancements.end.dragon_egg.title": "<PERSON><PERSON><PERSON>", "advancements.end.elytra.description": "Löydä elytra", "advancements.end.elytra.title": "<PERSON>ain taivas on rajana", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON>", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON> pak<PERSON>", "advancements.end.find_end_city.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> vain, mitä voisi sattua?", "advancements.end.find_end_city.title": "Kaupunki pelin lopussa", "advancements.end.kill_dragon.description": "Lykkyä tykö", "advancements.end.kill_dragon.title": "Vapauta End", "advancements.end.levitate.description": "Levitoi 50 kuutiota ylöspäin shulkerin hyökkäyksistä", "advancements.end.levitate.title": "Näköalapaikka", "advancements.end.respawn_dragon.description": "Uudelleensynnytä ender-lohikäärme", "advancements.end.respawn_dragon.title": "Loppu... jälleen kerran...", "advancements.end.root.description": "Vai alku?", "advancements.end.root.title": "End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON> viedä kakku sointukuution luo", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Syntymäpäivälaulu", "advancements.husbandry.allay_deliver_item_to_player.description": "<PERSON> a<PERSON>in tuoda sinulle esine", "advancements.husbandry.allay_deliver_item_to_player.title": "Saat minusta ystävän", "advancements.husbandry.axolotl_in_a_bucket.description": "Pyydystä aksolotli ämpäriin", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON><PERSON> kaik<PERSON><PERSON>, vaikka se ei olisi sinulle hyväksi", "advancements.husbandry.balanced_diet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruokavalio", "advancements.husbandry.breed_all_animals.description": "<PERSON>rita kaikkia eläimiä!", "advancements.husbandry.breed_all_animals.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.breed_an_animal.description": "<PERSON><PERSON> kaksi el<PERSON>", "advancements.husbandry.breed_an_animal.title": "Papukaijat ja lepakot", "advancements.husbandry.complete_catalogue.description": "Kesytä kaikki kissarodut!", "advancements.husbandry.complete_catalogue.title": "Kokoon kehr<PERSON><PERSON> k<PERSON>", "advancements.husbandry.feed_snifflet.description": "Syötä pikkunuuhkua", "advancements.husbandry.feed_snifflet.title": "Pieniä nuuh<PERSON>suja", "advancements.husbandry.fishy_business.description": "<PERSON><PERSON><PERSON> kala", "advancements.husbandry.fishy_business.title": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON>", "advancements.husbandry.froglights.description": "<PERSON><PERSON> kaikki sa<PERSON>", "advancements.husbandry.froglights.title": "Yhdistyköön voimamme!", "advancements.husbandry.kill_axolotl_target.description": "Voita taistelu yhdessä aksolotlin kanssa", "advancements.husbandry.kill_axolotl_target.title": "Ystävyyden parantava voima!", "advancements.husbandry.leash_all_frog_variants.description": "Hanki yksi joka<PERSON> sammakkotyyppiä ta<PERSON>", "advancements.husbandry.leash_all_frog_variants.title": "<PERSON><PERSON> on mukana mun lössi", "advancements.husbandry.make_a_sign_glow.description": "<PERSON>ta millaisen tahansa k<PERSON>tin te<PERSON>", "advancements.husbandry.make_a_sign_glow.title": "<PERSON>ta ja katso!", "advancements.husbandry.netherite_hoe.description": "Käytä netheriittiharkkoa kuokan päivittämiseen ja arvioi sitten elämänvalintojasi uudelleen", "advancements.husbandry.netherite_hoe.title": "Todellista omistautumista", "advancements.husbandry.obtain_sniffer_egg.description": "<PERSON><PERSON> nuuhkun muna", "advancements.husbandry.obtain_sniffer_egg.title": "Mielenkiintoinen tuoksu", "advancements.husbandry.place_dried_ghast_in_water.description": "<PERSON><PERSON> kuivunut ghast veteen", "advancements.husbandry.place_dried_ghast_in_water.title": "<PERSON><PERSON>y nestey<PERSON>tty<PERSON>ä!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON><PERSON><PERSON> mik<PERSON> tahansa nuuhkulta saatu siemen", "advancements.husbandry.plant_any_sniffer_seed.title": "Menneisy<PERSON><PERSON> istutus", "advancements.husbandry.plant_seed.description": "Kylvä siemen ja katso sen kasvavan", "advancements.husbandry.plant_seed.title": "Totuuden siemen", "advancements.husbandry.remove_wolf_armor.description": "Poista sudelta haarniska käyttämällä saksia", "advancements.husbandry.remove_wolf_armor.title": "Nips, naps!", "advancements.husbandry.repair_wolf_armor.description": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>itt<PERSON>ut suden haarniska vyötiäisen suomuilla", "advancements.husbandry.repair_wolf_armor.title": "<PERSON><PERSON> u<PERSON>i", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON>e veneeseen ja kellu vuohen kanssa", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Ei vuohi venettä kaada!", "advancements.husbandry.root.description": "<PERSON><PERSON><PERSON> on täynnä ystäviä ja ruokaa", "advancements.husbandry.root.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.safely_harvest_honey.description": "Käytä nuotiota kerätäksesi hunajaa mehiläispesästä pullon avulla suututtamatta mehiläisiä", "advancements.husbandry.safely_harvest_honey.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.silk_touch_nest.description": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>, j<PERSON> on 3 me<PERSON><PERSON><PERSON><PERSON>, silkkisen kosketuksen avulla", "advancements.husbandry.silk_touch_nest.title": "Mehiläisiä <PERSON>", "advancements.husbandry.tactical_fishing.description": "Nappaa kala... käyttämättä onki<PERSON>a!", "advancements.husbandry.tactical_fishing.title": "Taktista kalastelua", "advancements.husbandry.tadpole_in_a_bucket.description": "Pyydystä nuijapää ämpäriin", "advancements.husbandry.tadpole_in_a_bucket.title": "<PERSON><PERSON> sammakot", "advancements.husbandry.tame_an_animal.description": "Kesytä eläin", "advancements.husbandry.tame_an_animal.title": "Parhaat ystävät ikuisesti", "advancements.husbandry.wax_off.description": "<PERSON><PERSON> vaha pois kupariku<PERSON>a!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON> pois", "advancements.husbandry.wax_on.description": "Levitä hunajaken<PERSON>a kupari<PERSON>utioon!", "advancements.husbandry.wax_on.title": "Vahaa päälle", "advancements.husbandry.whole_pack.description": "Kesytä kaikki su<PERSON>", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON> lauma kasa<PERSON>", "advancements.nether.all_effects.description": "<PERSON> joka<PERSON>n vaikutu<PERSON>en alaisena samaan a<PERSON>an", "advancements.nether.all_effects.title": "<PERSON><PERSON>a päädyimme tänne?", "advancements.nether.all_potions.description": "<PERSON> joka<PERSON>n ta<PERSON> vaikutuksen alaisena samaan aikaan", "advancements.nether.all_potions.title": "<PERSON><PERSON><PERSON>", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON>", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON><PERSON>yntymisank<PERSON><PERSON> ma<PERSON>n", "advancements.nether.charge_respawn_anchor.title": "Ei aivan \"yhdeksää\" elämää", "advancements.nether.create_beacon.description": "Työst<PERSON> ja aseta me<PERSON>o", "advancements.nether.create_beacon.title": "Valomer<PERSON><PERSON>", "advancements.nether.create_full_beacon.description": "Nosta merkkivalo täyteen tehoon", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "<PERSON><PERSON><PERSON><PERSON> pig<PERSON> kullalla", "advancements.nether.distract_piglin.title": "Oi, kiiltävää", "advancements.nether.explore_nether.description": "<PERSON><PERSON><PERSON> kaikki <PERSON> biomit", "advancements.nether.explore_nether.title": "Suosittuja turistikohteita", "advancements.nether.fast_travel.description": "Matkusta Netherin kautta 7km ylämaailmassa", "advancements.nether.fast_travel.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.find_bastion.description": "<PERSON><PERSON> si<PERSON>n linn<PERSON>en raunio<PERSON>in", "advancements.nether.find_bastion.title": "Oi niitä aikoja", "advancements.nether.find_fortress.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.find_fortress.title": "<PERSON><PERSON><PERSON> linn<PERSON>us", "advancements.nether.get_wither_skull.description": "<PERSON><PERSON> wither-lu<PERSON><PERSON><PERSON> kallo", "advancements.nether.get_wither_skull.title": "<PERSON><PERSON><PERSON> pelo<PERSON> lu<PERSON>", "advancements.nether.loot_bastion.description": "Ryöstä arkku linnakkeen raunioista", "advancements.nether.loot_bastion.title": "Sodanlietsojat", "advancements.nether.netherite_armor.description": "<PERSON><PERSON> k<PERSON>nen Netheriittihaarniska", "advancements.nether.netherite_armor.title": "<PERSON><PERSON><PERSON> minut romulla", "advancements.nether.obtain_ancient_debris.description": "<PERSON><PERSON> muinaista romua", "advancements.nether.obtain_ancient_debris.title": "Pi<PERSON>ssa <PERSON>", "advancements.nether.obtain_blaze_rod.description": "Päästä roihu sau<PERSON>", "advancements.nether.obtain_blaze_rod.title": "<PERSON><PERSON><PERSON> tulen", "advancements.nether.obtain_crying_obsidian.description": "Hanki itkevää obsidiaania", "advancements.nether.obtain_crying_obsidian.title": "<PERSON>ka leik<PERSON>a sipuleita?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON> ghast tulipallolla", "advancements.nether.return_to_sender.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.ride_strider.description": "Ratsasta rämpijällä pitäen kummasientä kepin päässä", "advancements.nether.ride_strider.title": "<PERSON><PERSON><PERSON><PERSON> veneellä on jalat", "advancements.nether.ride_strider_in_overworld_lava.description": "Vie rämpijä piiiitkälle lenkille laavajärvelle ylämaailmassa", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON> koti kullan kallis", "advancements.nether.root.description": "<PERSON><PERSON> mukaan k<PERSON>t", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON> k<PERSON>", "advancements.nether.uneasy_alliance.description": "<PERSON><PERSON><PERSON>, tuo se turvallisesti y<PERSON>ämaailma<PERSON>... ja surmaa se", "advancements.nether.uneasy_alliance.title": "Epämukava liitto", "advancements.nether.use_lodestone.description": "Käytä kompassia magneettikiveen", "advancements.nether.use_lodestone.title": "<PERSON> mun ajaa m<PERSON>", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Heikennä ja paranna zomb<PERSON>ä<PERSON>", "advancements.story.cure_zombie_villager.title": "Zombitohtori", "advancements.story.deflect_arrow.description": "Torju ammus kilvellä", "advancements.story.deflect_arrow.title": "<PERSON>i tä<PERSON>, kiitos", "advancements.story.enchant_item.description": "<PERSON><PERSON><PERSON> esine lum<PERSON>yd<PERSON>ll<PERSON>", "advancements.story.enchant_item.title": "<PERSON><PERSON><PERSON>", "advancements.story.enter_the_end.description": "<PERSON><PERSON> si<PERSON>n end-portaaliin", "advancements.story.enter_the_end.title": "Loppu?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON> ja syt<PERSON><PERSON>-port<PERSON><PERSON>, ja astu si<PERSON>n", "advancements.story.enter_the_nether.title": "Meidän on mentävä syvemmälle", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON> ender-silm<PERSON><PERSON>", "advancements.story.follow_ender_eye.title": "Kylmällä silmällä", "advancements.story.form_obsidian.description": "<PERSON><PERSON> obs<PERSON>", "advancements.story.form_obsidian.title": "Ice Bucket Challenge", "advancements.story.iron_tools.description": "Päivitä hakkusi", "advancements.story.iron_tools.title": "Rautaista ammattitaitoa", "advancements.story.lava_bucket.description": "Täytä ämpäri <PERSON>", "advancements.story.lava_bucket.title": "<PERSON><PERSON><PERSON> kamaa", "advancements.story.mine_diamond.description": "<PERSON><PERSON>", "advancements.story.mine_diamond.title": "Timantteja!", "advancements.story.mine_stone.description": "<PERSON><PERSON> kive<PERSON> uudella hakullasi", "advancements.story.mine_stone.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.obtain_armor.description": "Suojaa itseäsi rautahaarniskan osalla", "advancements.story.obtain_armor.title": "<PERSON><PERSON><PERSON><PERSON> tyylik<PERSON>sti", "advancements.story.root.description": "<PERSON><PERSON> s<PERSON>n ja tarina", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Timanttihaarniska pelastaa henkiä", "advancements.story.shiny_gear.title": "Peitä minut timanteilla", "advancements.story.smelt_iron.description": "<PERSON><PERSON> r<PERSON>", "advancements.story.smelt_iron.title": "<PERSON><PERSON>", "advancements.story.upgrade_tools.description": "Työstä parempi hakku", "advancements.story.upgrade_tools.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.toast.challenge": "<PERSON>te suoritettu!", "advancements.toast.goal": "Tavoite saavutettu!", "advancements.toast.task": "<PERSON><PERSON><PERSON><PERSON><PERSON> ansaittu!", "argument.anchor.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> kohdeankkurin sijainti '%s'", "argument.angle.incomplete": "Keskeneräinen (odotettiin yhtä kul<PERSON>)", "argument.angle.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "argument.block.id.invalid": "Tuntematon kuutiotyyppi '%s'", "argument.block.property.duplicate": "Ominaisuus '%s' voidaan asettaa ainoastaan kerran kuutiolle %s", "argument.block.property.invalid": "Kuutio %s ei hyväksy '%s' %s-ominaisuudelle", "argument.block.property.novalue": "Odotettu arvo ominaisuudelle '%s' kuutiossa %s", "argument.block.property.unclosed": "Odotettu <PERSON> ] kuutiotilaominaisuuksille", "argument.block.property.unknown": "Kuutiolla %s ei ole ominaisuutta '%s'", "argument.block.tag.disallowed": "<PERSON><PERSON><PERSON><PERSON><PERSON> eivät ole sallittuja tääll<PERSON>, vain oikeat kuutiot", "argument.color.invalid": "Tuntematon väri '%s'", "argument.component.invalid": "Virheellinen keskustelukomponentti: %s", "argument.criteria.invalid": "Tuntematon kriteeri '%s'", "argument.dimension.invalid": "<PERSON><PERSON><PERSON><PERSON> ulottuvuus '%s'", "argument.double.big": "Desimaali ei voi olla suurempi kuin %s, sinulla on %s", "argument.double.low": "Desimaali ei voi olla pienempi kuin %s, sinulla on%s", "argument.entity.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi tai UUID", "argument.entity.notfound.entity": "Kohdetta ei lö<PERSON>yt", "argument.entity.notfound.player": "Pelaajaa ei lö<PERSON>", "argument.entity.options.advancements.description": "Pelaajat edistysaskeleilla", "argument.entity.options.distance.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>n", "argument.entity.options.distance.negative": "Etäisyys ei voi olla negatiivinen", "argument.entity.options.dx.description": "Koht<PERSON>t x:n ja x + dx:n välillä", "argument.entity.options.dy.description": "<PERSON><PERSON><PERSON><PERSON> y:n ja y + dy:n välillä", "argument.entity.options.dz.description": "Kohteet z:n ja z + dz:n välillä", "argument.entity.options.gamemode.description": "<PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.inapplicable": "Asetus '%s' ei ole soveltuva tässä", "argument.entity.options.level.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.level.negative": "<PERSON><PERSON> ei kuuluisi olla negatiivinen", "argument.entity.options.limit.description": "<PERSON><PERSON><PERSON><PERSON> kohteiden maksimimäärä", "argument.entity.options.limit.toosmall": "Rajan täytyy olla ainakin 1", "argument.entity.options.mode.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> tai tuntematon pelitila '%s'", "argument.entity.options.name.description": "<PERSON><PERSON><PERSON> nimi", "argument.entity.options.nbt.description": "Kohteet NBT:llä", "argument.entity.options.predicate.description": "<PERSON><PERSON><PERSON><PERSON>u predikaatti", "argument.entity.options.scores.description": "<PERSON><PERSON><PERSON><PERSON> pisteillä", "argument.entity.options.sort.description": "Lajittele koh<PERSON>t", "argument.entity.options.sort.irreversible": "<PERSON><PERSON><PERSON><PERSON>en tai tuntematon lajittelutyyppi '%s'", "argument.entity.options.tag.description": "<PERSON><PERSON><PERSON><PERSON> tun<PERSON>", "argument.entity.options.team.description": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "argument.entity.options.type.description": "Kohteiden tyyppi", "argument.entity.options.type.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> tai tuntematon kohteen tyyppi '%s'", "argument.entity.options.unknown": "Tuntematon valinta '%s'", "argument.entity.options.unterminated": "<PERSON><PERSON><PERSON><PERSON> as<PERSON> lo<PERSON>ua", "argument.entity.options.valueless": "Odotettu arvo vaihtoehdolle '%s'", "argument.entity.options.x.description": "x-si<PERSON><PERSON><PERSON>", "argument.entity.options.x_rotation.description": "Kohteen x-kierto", "argument.entity.options.y.description": "y-si<PERSON><PERSON>i", "argument.entity.options.y_rotation.description": "Kohteen y-kierto", "argument.entity.options.z.description": "z-sijainti", "argument.entity.selector.allEntities": "<PERSON><PERSON><PERSON> k<PERSON>t", "argument.entity.selector.allPlayers": "<PERSON><PERSON><PERSON> p<PERSON>", "argument.entity.selector.missing": "<PERSON><PERSON><PERSON><PERSON><PERSON> vali<PERSON>", "argument.entity.selector.nearestEntity": "<PERSON><PERSON><PERSON> kohde", "argument.entity.selector.nearestPlayer": "<PERSON><PERSON><PERSON> p<PERSON>", "argument.entity.selector.not_allowed": "Valitsinta ei sallita", "argument.entity.selector.randomPlayer": "<PERSON><PERSON><PERSON><PERSON> pela<PERSON>", "argument.entity.selector.self": "Nyk<PERSON><PERSON> kohde", "argument.entity.selector.unknown": "Tuntematon valitsimen tyyppi '%s'", "argument.entity.toomany": "Vain yksi olio on sallittu, mutta tarjottu valitsija sallii enemmän kuin yhden", "argument.enum.invalid": "Tuntematon arvo \"%s\"", "argument.float.big": "Liukuluku ei voi olla suurempi kuin %s, sinulla on %s", "argument.float.low": "Liukuluku ei voi olla pienempi kuin %s, sinulla on %s", "argument.gamemode.invalid": "Tuntematon pelitila: %s", "argument.hexcolor.invalid": "Virheellinen he<PERSON>avä<PERSON>oodi '%s'", "argument.id.invalid": "Virheellinen ID", "argument.id.unknown": "Tuntematon ID: %s", "argument.integer.big": "Kokonaisluku ei voi olla suurempi kuin %s, sinulla on %s", "argument.integer.low": "Kokonaisluku ei voi olla pienempi kuin %s, sinulla on %s", "argument.item.id.invalid": "Tuntematon esine '%s'", "argument.item.tag.disallowed": "<PERSON><PERSON><PERSON><PERSON><PERSON> eivät ole sallittu täällä, vain oikeat esineet", "argument.literal.incorrect": "Odotettiin k<PERSON> %s", "argument.long.big": "Pitkä kokonai<PERSON>luku ei voi olla suurempi kuin %s, sinulla on %s", "argument.long.low": "Pitkä kokonai<PERSON>luku ei voi olla pienempi kuin %s, sinulla on %s", "argument.message.too_long": "Keskusteluviesti oli liian pitk<PERSON> (%s > enintään %s merkkiä)", "argument.nbt.array.invalid": "<PERSON>ir<PERSON>llinen ta<PERSON>kotyyppi '%s'", "argument.nbt.array.mixed": "Kohdetta %s ei voi sijoittaa kohteeseen %s", "argument.nbt.expected.compound": "Odotetiin compound-tunnistetta", "argument.nbt.expected.key": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>a", "argument.nbt.expected.value": "Odotettiin ar<PERSON>", "argument.nbt.list.mixed": "Kohdetta %s ei voi sijoittaa listaan %s", "argument.nbt.trailing": "Odottamatonta j<PERSON>", "argument.player.entities": "Tämä komento vaikuttaa vain pelaajiin, mutta tarjottu valitsija käsittää myös muutkin o<PERSON>t", "argument.player.toomany": "Vain yksi pelaaja on sallittu, mutta tarjottu valitsija sallii enemmän kuin yhden", "argument.player.unknown": "Tät<PERSON> pela<PERSON>a ei ole o<PERSON>a", "argument.pos.missing.double": "<PERSON><PERSON><PERSON><PERSON>", "argument.pos.missing.int": "<PERSON><PERSON><PERSON><PERSON>", "argument.pos.mixed": "Maailmaa ja paikallisia koordinaatteja ei voi sekoittaa (kaiken täytyy joko käyttää tai olla käyttämättä ^ -merkkiä)", "argument.pos.outofbounds": "Tämä sijainti ylittää sallitut rajat.", "argument.pos.outofworld": "Tämä asento on taivaallinen!", "argument.pos.unloaded": "Tätä asentoa ei ole ladattu", "argument.pos2d.incomplete": "Ke<PERSON>neräinen (odote<PERSON>in kahta koordinaattia)", "argument.pos3d.incomplete": "Keskeneräinen (odotettiin kolmea koordinaattia)", "argument.range.empty": "Odotettiin arvoa tai arvojen vaihteluväliä", "argument.range.ints": "<PERSON><PERSON> koko<PERSON>t ovat sallitt<PERSON>, ei desimaaleja", "argument.range.swapped": "Minimi ei voi olla suurempi kuin maksimi", "argument.resource.invalid_type": "Elementillä '%s' on väärä tyyppi '%s' (odotettu: '%s')", "argument.resource.not_found": "Tyypin '%2$s' elementtiä '%1$s' ei löydy", "argument.resource_or_id.failed_to_parse": "Rakennelman parsiminen epäonnistui: %s", "argument.resource_or_id.invalid": "Tuntematon id tai tunniste", "argument.resource_or_id.no_such_element": "Elementtiä '%s' ei löydy rekisteristä '%s'", "argument.resource_selector.not_found": "Ei osumia tyypin '%2$s' valitsimelle '%1$s'", "argument.resource_tag.invalid_type": "<PERSON>nn<PERSON><PERSON><PERSON> '%s' on väärä tyyppi '%s' (odotettu: '%s')", "argument.resource_tag.not_found": "Tyypin '%2$s' tunnistetta '%1$s' ei löydy", "argument.rotation.incomplete": "Ke<PERSON>neräinen (odote<PERSON>in kahta koordinaattia)", "argument.scoreHolder.empty": "Olennaisia pisteidenhallitsijoita ei löydetty", "argument.scoreboardDisplaySlot.invalid": "Tuntematon näyttölokero '%s'", "argument.style.invalid": "Virheellinen tyyli: %s", "argument.time.invalid_tick_count": "Pykälämäärän täyt<PERSON>y olla epänegatiivinen", "argument.time.invalid_unit": "Virheellinen yksikkö", "argument.time.tick_count_too_low": "Pykälien määrä ei voi olla pienempi kuin %s, sinulla on %s", "argument.uuid.invalid": "Virheellinen UUID", "argument.waypoint.invalid": "<PERSON><PERSON>u kohde ei ole reittipiste", "arguments.block.tag.unknown": "Tuntematon kuution tunniste '%s'", "arguments.function.tag.unknown": "Tuntematon toimintotunniste '%s'", "arguments.function.unknown": "Tuntematon toiminto %s", "arguments.item.component.expected": "Odotettu esinekomponentti", "arguments.item.component.malformed": "<PERSON><PERSON><PERSON><PERSON> muotoiltu '%s' komponentti: '%s'", "arguments.item.component.repeated": "<PERSON><PERSON><PERSON> kompo<PERSON> '%s' to<PERSON><PERSON><PERSON>, mutta vain yksi arvo <PERSON>aan määritellä", "arguments.item.component.unknown": "Tuntematon esinekomponentti '%s'", "arguments.item.malformed": "<PERSON><PERSON><PERSON><PERSON> muoto<PERSON>u esine: '%s'", "arguments.item.overstacked": "%s voi pinoutua vain lukuun %s", "arguments.item.predicate.malformed": "<PERSON><PERSON><PERSON><PERSON> muotoiltu '%s' predikaatti: '%s'", "arguments.item.predicate.unknown": "Tuntematon esineen predikaatti '%s'", "arguments.item.tag.unknown": "Tuntematon esinetunniste '%s'", "arguments.nbtpath.node.invalid": "Virheellinen NBT-alustan elementti", "arguments.nbtpath.nothing_found": "Elementtejä ei löytynyt haulla %s", "arguments.nbtpath.too_deep": "Saatu NBT sisältää liikaa sisäkkäistä dataa", "arguments.nbtpath.too_large": "Saatu NBT on liian suuri", "arguments.objective.notFound": "Tuntematon pistetaulukon tavoite '%s'", "arguments.objective.readonly": "Pistetaulukon tavoite '%s' on vain luettavissa", "arguments.operation.div0": "<PERSON>i pystytä jakamaan nollalla", "arguments.operation.invalid": "<PERSON><PERSON><PERSON>llinen operaatio", "arguments.swizzle.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>, o<PERSON><PERSON><PERSON> 'x', 'y' ja 'z' -koor<PERSON><PERSON><PERSON>", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Haarniska", "attribute.name.armor_toughness": "Haarniskan kovuus", "attribute.name.attack_damage": "Hyökkäysvahinko", "attribute.name.attack_knockback": "Hyökkäyksen työntövoima", "attribute.name.attack_speed": "Hyökkäysnopeus", "attribute.name.block_break_speed": "<PERSON><PERSON><PERSON><PERSON> tuh<PERSON>us", "attribute.name.block_interaction_range": "Kuutioiden käyttöetäisyys", "attribute.name.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.camera_distance": "Kamer<PERSON> et<PERSON>", "attribute.name.entity_interaction_range": "O<PERSON>iden k<PERSON>öetäisyys", "attribute.name.explosion_knockback_resistance": "Räjähdyksestä joh<PERSON><PERSON> taaks<PERSON><PERSON>", "attribute.name.fall_damage_multiplier": "Putoamisvahingon kerroin", "attribute.name.flying_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.follow_range": "<PERSON><PERSON>", "attribute.name.generic.armor": "Haarniska", "attribute.name.generic.armor_toughness": "Haarniskan kovuus", "attribute.name.generic.attack_damage": "Hyökkäysvahinko", "attribute.name.generic.attack_knockback": "Hyökkäyksen työntövoima", "attribute.name.generic.attack_speed": "Hyökkäysnopeus", "attribute.name.generic.block_interaction_range": "Kuutioiden käyttöetäisyys", "attribute.name.generic.burning_time": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.entity_interaction_range": "O<PERSON>iden k<PERSON>öetäisyys", "attribute.name.generic.explosion_knockback_resistance": "Räjähdyksestä joh<PERSON><PERSON> taaks<PERSON><PERSON>", "attribute.name.generic.fall_damage_multiplier": "Putoamisvahingon kerroin", "attribute.name.generic.flying_speed": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.follow_range": "<PERSON><PERSON>", "attribute.name.generic.gravity": "Painovoima", "attribute.name.generic.jump_strength": "Hypyn voima<PERSON>", "attribute.name.generic.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.luck": "<PERSON><PERSON>", "attribute.name.generic.max_absorption": "Enimmäisime<PERSON>yminen", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.movement_efficiency": "Liikkumistehokkuus", "attribute.name.generic.movement_speed": "<PERSON>us", "attribute.name.generic.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.safe_fall_distance": "Tu<PERSON><PERSON>en putoamisetäisyys", "attribute.name.generic.scale": "<PERSON><PERSON>", "attribute.name.generic.step_height": "Askelkorkeus", "attribute.name.generic.water_movement_efficiency": "Vedessä liikkumisen tehokkuus", "attribute.name.gravity": "Painovoima", "attribute.name.horse.jump_strength": "<PERSON><PERSON><PERSON> h<PERSON>", "attribute.name.jump_strength": "Hypyn voima<PERSON>", "attribute.name.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.luck": "<PERSON><PERSON>", "attribute.name.max_absorption": "Enimmäisime<PERSON>yminen", "attribute.name.max_health": "Enimmäiselinvoima", "attribute.name.mining_efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.movement_efficiency": "Liikkumistehokkuus", "attribute.name.movement_speed": "<PERSON>us", "attribute.name.oxygen_bonus": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.block_break_speed": "<PERSON><PERSON><PERSON><PERSON> tuh<PERSON>us", "attribute.name.player.block_interaction_range": "Kuutioiden käyttöetäisyys", "attribute.name.player.entity_interaction_range": "O<PERSON>iden k<PERSON>öetäisyys", "attribute.name.player.mining_efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.sneaking_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.player.submerged_mining_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> up<PERSON>", "attribute.name.player.sweeping_damage_ratio": "Kiitävän iskun vahinkosuhde", "attribute.name.safe_fall_distance": "Tu<PERSON><PERSON>en putoamisetäisyys", "attribute.name.scale": "<PERSON><PERSON>", "attribute.name.sneaking_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.spawn_reinforcements": "Zombivahvistusjoukot", "attribute.name.step_height": "Askelkorkeus", "attribute.name.submerged_mining_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> up<PERSON>", "attribute.name.sweeping_damage_ratio": "Kiitävän iskun vahinkosuhde", "attribute.name.tempt_range": "<PERSON><PERSON><PERSON>", "attribute.name.water_movement_efficiency": "Vedessä liikkumisen tehokkuus", "attribute.name.waypoint_receive_range": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attribute.name.waypoint_transmit_range": "Reittipisteen lähetysetäisyys", "attribute.name.zombie.spawn_reinforcements": "Zombivahvistusjoukot", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "Bambuviidakko", "biome.minecraft.basalt_deltas": "Basalttisuistot", "biome.minecraft.beach": "<PERSON><PERSON>", "biome.minecraft.birch_forest": "Koivumetsä", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON><PERSON>eh<PERSON>", "biome.minecraft.cold_ocean": "Kylmä valtameri", "biome.minecraft.crimson_forest": "Karmiinimetsä", "biome.minecraft.dark_forest": "<PERSON><PERSON> metsä", "biome.minecraft.deep_cold_ocean": "Syvä kylmä valtameri", "biome.minecraft.deep_dark": "Syvä pimeys", "biome.minecraft.deep_frozen_ocean": "Syvä j<PERSON>ätynyt valtameri", "biome.minecraft.deep_lukewarm_ocean": "Syvä haalea valtameri", "biome.minecraft.deep_ocean": "Syvä valtameri", "biome.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.dripstone_caves": "Tippukivil<PERSON>lat", "biome.minecraft.end_barrens": "<PERSON><PERSON>", "biome.minecraft.end_highlands": "<PERSON><PERSON>", "biome.minecraft.end_midlands": "<PERSON><PERSON> k<PERSON>", "biome.minecraft.eroded_badlands": "<PERSON><PERSON><PERSON> auti<PERSON>a", "biome.minecraft.flower_forest": "Kukkametsä", "biome.minecraft.forest": "Metsä", "biome.minecraft.frozen_ocean": "Jäätynyt valtameri", "biome.minecraft.frozen_peaks": "Jäätyneet huiput", "biome.minecraft.frozen_river": "Jäätynyt joki", "biome.minecraft.grove": "Lehto", "biome.minecraft.ice_spikes": "Jääpiikit", "biome.minecraft.jagged_peaks": "<PERSON><PERSON><PERSON><PERSON> hui<PERSON>", "biome.minecraft.jungle": "Viidakko", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON><PERSON> valtameri", "biome.minecraft.lush_caves": "Rehe<PERSON><PERSON> luolasto", "biome.minecraft.mangrove_swamp": "Mangrovesuo", "biome.minecraft.meadow": "<PERSON><PERSON><PERSON>", "biome.minecraft.mushroom_fields": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.nether_wastes": "<PERSON><PERSON>-auti<PERSON><PERSON>", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "Aarnioitunut koivumetsä", "biome.minecraft.old_growth_pine_taiga": "Aarnioitunut mäntytaiga", "biome.minecraft.old_growth_spruce_taiga": "Aarnioitunut kuusitaiga", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.river": "<PERSON><PERSON>", "biome.minecraft.savanna": "<PERSON><PERSON><PERSON>", "biome.minecraft.savanna_plateau": "Savanniylänkö", "biome.minecraft.small_end_islands": "Pienet Endin saaret", "biome.minecraft.snowy_beach": "<PERSON><PERSON><PERSON> ranta", "biome.minecraft.snowy_plains": "<PERSON><PERSON><PERSON>", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON> rinteet", "biome.minecraft.snowy_taiga": "Luminen taiga", "biome.minecraft.soul_sand_valley": "Sieluhiekkalaakso", "biome.minecraft.sparse_jungle": "Harva viidakko", "biome.minecraft.stony_peaks": "<PERSON><PERSON><PERSON> hui<PERSON>", "biome.minecraft.stony_shore": "<PERSON><PERSON><PERSON>", "biome.minecraft.sunflower_plains": "Auringonkukkatasan<PERSON>", "biome.minecraft.swamp": "<PERSON><PERSON>", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "End", "biome.minecraft.the_void": "Tyhjyys", "biome.minecraft.warm_ocean": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "biome.minecraft.warped_forest": "Ku<PERSON>metsä", "biome.minecraft.windswept_forest": "Tuulenpieksemä metsä", "biome.minecraft.windswept_gravelly_hills": "Tuulenpieksemät soraiset kukkulat", "biome.minecraft.windswept_hills": "Tuulenpieksemät kukkulat", "biome.minecraft.windswept_savanna": "Tuulenpieksemä savanni", "biome.minecraft.wooded_badlands": "<PERSON><PERSON><PERSON> auti<PERSON>", "block.minecraft.acacia_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_log": "<PERSON><PERSON><PERSON>ahalk<PERSON>", "block.minecraft.acacia_planks": "Akaasialankut", "block.minecraft.acacia_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>att<PERSON>", "block.minecraft.acacia_sapling": "<PERSON><PERSON><PERSON><PERSON> taimi", "block.minecraft.acacia_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_stairs": "Akaasiaportaat", "block.minecraft.acacia_trapdoor": "Akaasialuuk<PERSON>", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.acacia_wood": "<PERSON><PERSON>asiap<PERSON><PERSON>", "block.minecraft.activator_rail": "Aktivointiraide", "block.minecraft.air": "Ilma", "block.minecraft.allium": "<PERSON><PERSON>", "block.minecraft.amethyst_block": "Ametistikuutio", "block.minecraft.amethyst_cluster": "Ametistirykelmä", "block.minecraft.ancient_debris": "<PERSON><PERSON><PERSON> romu", "block.minecraft.andesite": "<PERSON><PERSON><PERSON>", "block.minecraft.andesite_slab": "Andesiittilaatta", "block.minecraft.andesite_stairs": "Andesiittiportaat", "block.minecraft.andesite_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.anvil": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_melon_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON> melonin varsi", "block.minecraft.attached_pumpkin_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> varsi", "block.minecraft.azalea": "Atsalea", "block.minecraft.azalea_leaves": "Atsal<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.azure_bluet": "Keijunsilmä", "block.minecraft.bamboo": "Bambu", "block.minecraft.bamboo_block": "Bambukuutio", "block.minecraft.bamboo_button": "Bambunappi", "block.minecraft.bamboo_door": "Bamb<PERSON><PERSON>", "block.minecraft.bamboo_fence": "Bambuaita", "block.minecraft.bamboo_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_mosaic": "Bambumosaiikki", "block.minecraft.bamboo_mosaic_slab": "Bambumosaiik<PERSON>laatta", "block.minecraft.bamboo_mosaic_stairs": "Bambumosaiikkiportaat", "block.minecraft.bamboo_planks": "Bambulankut", "block.minecraft.bamboo_pressure_plate": "Bamb<PERSON><PERSON><PERSON>atta", "block.minecraft.bamboo_sapling": "Bambunverso", "block.minecraft.bamboo_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_slab": "Bambulaatta", "block.minecraft.bamboo_stairs": "Bambuportaat", "block.minecraft.bamboo_trapdoor": "Bambuluukku", "block.minecraft.bamboo_wall_hanging_sign": "R<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> bam<PERSON><PERSON>", "block.minecraft.banner.base.black": "<PERSON><PERSON><PERSON><PERSON> musta pohja", "block.minecraft.banner.base.blue": "<PERSON><PERSON><PERSON><PERSON> sin<PERSON> pohja", "block.minecraft.banner.base.brown": "<PERSON><PERSON><PERSON><PERSON> ruskea pohja", "block.minecraft.banner.base.cyan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> p<PERSON>ja", "block.minecraft.banner.base.gray": "<PERSON><PERSON><PERSON><PERSON> harmaa pohja", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON><PERSON> vihreä pohja", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON><PERSON><PERSON> pohja", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> pohja", "block.minecraft.banner.base.lime": "<PERSON><PERSON><PERSON><PERSON> pohja", "block.minecraft.banner.base.magenta": "<PERSON><PERSON><PERSON><PERSON> purp<PERSON> pohja", "block.minecraft.banner.base.orange": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ja", "block.minecraft.banner.base.pink": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> pohja", "block.minecraft.banner.base.purple": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.base.red": "<PERSON><PERSON><PERSON><PERSON> pu<PERSON> pohja", "block.minecraft.banner.base.white": "<PERSON><PERSON><PERSON><PERSON> v<PERSON> pohja", "block.minecraft.banner.base.yellow": "<PERSON><PERSON><PERSON><PERSON> kel<PERSON>en pohja", "block.minecraft.banner.border.black": "<PERSON><PERSON> reunus", "block.minecraft.banner.border.blue": "<PERSON><PERSON> reunus", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON> re<PERSON>us", "block.minecraft.banner.border.gray": "<PERSON><PERSON><PERSON> re<PERSON>us", "block.minecraft.banner.border.green": "<PERSON><PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.border.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.border.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.border.lime": "Limetinvihr<PERSON><PERSON> reunus", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.border.orange": "<PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.border.purple": "<PERSON><PERSON>", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.border.white": "<PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.border.yellow": "<PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.bricks.black": "<PERSON><PERSON> tii<PERSON>", "block.minecraft.banner.bricks.blue": "<PERSON><PERSON> tii<PERSON>", "block.minecraft.banner.bricks.brown": "Ruskea tiilikuvio", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.green": "Vihreä tiilikuvio", "block.minecraft.banner.bricks.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> tii<PERSON>vio", "block.minecraft.banner.bricks.light_gray": "Vaaleanharmaa tii<PERSON>", "block.minecraft.banner.bricks.lime": "Limetinvihreä tiilikuvio", "block.minecraft.banner.bricks.magenta": "Purppura tiilikuvio", "block.minecraft.banner.bricks.orange": "Oranssi tiilikuvio", "block.minecraft.banner.bricks.pink": "Vaaleanpunainen tiilikuvio", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON>", "block.minecraft.banner.bricks.red": "Punainen tiilikuvio", "block.minecraft.banner.bricks.white": "Valkoinen tiilikuvio", "block.minecraft.banner.bricks.yellow": "Keltainen tiilikuvio", "block.minecraft.banner.circle.black": "Musta ympyrä", "block.minecraft.banner.circle.blue": "Sininen ympyrä", "block.minecraft.banner.circle.brown": "Ruskea ympyrä", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>ani ympyr<PERSON>", "block.minecraft.banner.circle.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.green": "Vihreä ympyrä", "block.minecraft.banner.circle.light_blue": "Vaaleansininen ympyrä", "block.minecraft.banner.circle.light_gray": "Vaaleanharmaa ympyrä", "block.minecraft.banner.circle.lime": "Limetinvihreä ympyrä", "block.minecraft.banner.circle.magenta": "Purppura ympyrä", "block.minecraft.banner.circle.orange": "Oranssi ympyrä", "block.minecraft.banner.circle.pink": "Vaaleanpunainen ympyrä", "block.minecraft.banner.circle.purple": "<PERSON><PERSON>", "block.minecraft.banner.circle.red": "Punainen ympyrä", "block.minecraft.banner.circle.white": "Valkoinen ympyrä", "block.minecraft.banner.circle.yellow": "Keltainen ympyrä", "block.minecraft.banner.creeper.black": "<PERSON>a creeper", "block.minecraft.banner.creeper.blue": "Sininen creeper", "block.minecraft.banner.creeper.brown": "Ruskea creeper", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.gray": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.green": "Vih<PERSON><PERSON> creeper", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.lime": "Limetinvihreä creeper", "block.minecraft.banner.creeper.magenta": "P<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.pink": "Vaaleanpunainen creeper", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON> creeper", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.yellow": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.cross.black": "<PERSON><PERSON> vinoristi", "block.minecraft.banner.cross.blue": "<PERSON><PERSON> vinoristi", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON> vino<PERSON>i", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON><PERSON> vino<PERSON>i", "block.minecraft.banner.cross.gray": "<PERSON><PERSON><PERSON> vino<PERSON>i", "block.minecraft.banner.cross.green": "Vihreä vinoristi", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> vinoristi", "block.minecraft.banner.cross.light_gray": "<PERSON><PERSON>ean<PERSON><PERSON><PERSON> vinoristi", "block.minecraft.banner.cross.lime": "Limetinvihreä vinoristi", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON><PERSON> vinoristi", "block.minecraft.banner.cross.orange": "Oranssi vinoristi", "block.minecraft.banner.cross.pink": "Vaaleanpunainen vinoristi", "block.minecraft.banner.cross.purple": "<PERSON><PERSON> vino<PERSON>i", "block.minecraft.banner.cross.red": "<PERSON><PERSON><PERSON> vinoristi", "block.minecraft.banner.cross.white": "<PERSON><PERSON><PERSON> vinoristi", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON><PERSON> vinoristi", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON> ha<PERSON> reunus", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON> hamma<PERSON>nen reunus", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON><PERSON> ha<PERSON> reunus", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.curly_border.gray": "<PERSON><PERSON><PERSON> reunus", "block.minecraft.banner.curly_border.green": "Vihreä hammaskoroinen reunus", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> hammaskoroinen reunus", "block.minecraft.banner.curly_border.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON> reunus", "block.minecraft.banner.curly_border.lime": "Limetinvihreä hammaskoroinen reunus", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON><PERSON> hammaskoroinen reunus", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON><PERSON> ha<PERSON> reunus", "block.minecraft.banner.curly_border.pink": "Vaaleanpunainen hammaskoroinen reunus", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON> reunus", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON> hammaskoroinen reunus", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON><PERSON> hammaskoroinen reunus", "block.minecraft.banner.curly_border.yellow": "<PERSON><PERSON><PERSON> hammaskoroinen reunus", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.green": "Vihreä <PERSON><PERSON>", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.lime": "Limetinvihreä la<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.pink": "Vaalean<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.black": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.green": "Vihreä <PERSON>", "block.minecraft.banner.diagonal_right.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.lime": "Limetinvihreä <PERSON>", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.orange": "Orans<PERSON>", "block.minecraft.banner.diagonal_right.pink": "Vaaleanpunainen <PERSON>", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_right.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.black": "<PERSON><PERSON> t<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.blue": "Sininen tyviölohkoinen", "block.minecraft.banner.diagonal_up_left.brown": "Ruskea ty<PERSON>ö<PERSON>", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.green": "Vihreä tyviölohkoinen", "block.minecraft.banner.diagonal_up_left.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> tyviölohkoinen", "block.minecraft.banner.diagonal_up_left.light_gray": "Vaaleanharmaa <PERSON>", "block.minecraft.banner.diagonal_up_left.lime": "Limetinvihreä tyviölohkoinen", "block.minecraft.banner.diagonal_up_left.magenta": "Purppura tyviölohkoinen", "block.minecraft.banner.diagonal_up_left.orange": "Oranssi tyviölohkoinen", "block.minecraft.banner.diagonal_up_left.pink": "Vaaleanpunainen tyviölohkoinen", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_left.red": "Punainen tyviölohkoinen", "block.minecraft.banner.diagonal_up_left.white": "Valkoinen tyviölohkoinen", "block.minecraft.banner.diagonal_up_left.yellow": "Ke<PERSON>inen tyviölohkoinen", "block.minecraft.banner.diagonal_up_right.black": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON> tyviövas<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.brown": "Ruskea ty<PERSON>ö<PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.green": "Vihreä tyviövastaloh<PERSON>inen", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> tyviövas<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON><PERSON>eanhar<PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.lime": "Limetinvihreä tyviövastalohkoinen", "block.minecraft.banner.diagonal_up_right.magenta": "Purppura tyviö<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.orange": "Oranssi tyviövas<PERSON>inen", "block.minecraft.banner.diagonal_up_right.pink": "Vaaleanpunainen  tyviövastalohkoinen", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON> tyviövas<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.white": "Valkoinen tyviövas<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON> tyviövas<PERSON><PERSON><PERSON>", "block.minecraft.banner.flow.black": "<PERSON>a virtaus", "block.minecraft.banner.flow.blue": "<PERSON><PERSON> virtaus", "block.minecraft.banner.flow.brown": "R<PERSON><PERSON> virtaus", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON> virtaus", "block.minecraft.banner.flow.gray": "<PERSON><PERSON><PERSON> virtaus", "block.minecraft.banner.flow.green": "Vihreä virtaus", "block.minecraft.banner.flow.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> virtaus", "block.minecraft.banner.flow.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> virtaus", "block.minecraft.banner.flow.lime": "Limetinvihreä virtaus", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON><PERSON> virtaus", "block.minecraft.banner.flow.orange": "Or<PERSON><PERSON> virtaus", "block.minecraft.banner.flow.pink": "Vaaleanpunainen virtaus", "block.minecraft.banner.flow.purple": "<PERSON><PERSON> virtaus", "block.minecraft.banner.flow.red": "<PERSON><PERSON><PERSON> virtaus", "block.minecraft.banner.flow.white": "<PERSON><PERSON><PERSON> virtaus", "block.minecraft.banner.flow.yellow": "<PERSON><PERSON><PERSON> virtaus", "block.minecraft.banner.flower.black": "<PERSON><PERSON> kukka", "block.minecraft.banner.flower.blue": "<PERSON><PERSON> kukka", "block.minecraft.banner.flower.brown": "Ruskea kukka", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON> k<PERSON>ka", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.flower.green": "Vihreä kukka", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> kukka", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kukka", "block.minecraft.banner.flower.lime": "Limetinvihreä kukka", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON><PERSON> kukka", "block.minecraft.banner.flower.orange": "Oranssi kukka", "block.minecraft.banner.flower.pink": "Vaaleanpunainen kukka", "block.minecraft.banner.flower.purple": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.flower.red": "<PERSON><PERSON><PERSON> kukka", "block.minecraft.banner.flower.white": "Valkoinen kukka", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON> kukka", "block.minecraft.banner.globe.black": "<PERSON><PERSON> kart<PERSON>", "block.minecraft.banner.globe.blue": "<PERSON><PERSON> kartta<PERSON>o", "block.minecraft.banner.globe.brown": "Ruskea kartta<PERSON>o", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.banner.globe.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.green": "Vihreä karttakuutio", "block.minecraft.banner.globe.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> kartta<PERSON>utio", "block.minecraft.banner.globe.light_gray": "<PERSON><PERSON>ean<PERSON><PERSON><PERSON> kart<PERSON>", "block.minecraft.banner.globe.lime": "Limetinvihreä karttakuutio", "block.minecraft.banner.globe.magenta": "<PERSON><PERSON><PERSON><PERSON> kartta<PERSON>o", "block.minecraft.banner.globe.orange": "Oranssi karttakuutio", "block.minecraft.banner.globe.pink": "Vaaleanpunainen karttakuutio", "block.minecraft.banner.globe.purple": "<PERSON><PERSON>", "block.minecraft.banner.globe.red": "<PERSON><PERSON><PERSON> kartta<PERSON>utio", "block.minecraft.banner.globe.white": "Valkoinen kartta<PERSON>utio", "block.minecraft.banner.globe.yellow": "<PERSON><PERSON><PERSON> karttakuutio", "block.minecraft.banner.gradient.black": "<PERSON><PERSON> h<PERSON> alas", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON> h<PERSON>ivytys alas", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON> hä<PERSON>ytys alas", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON> h<PERSON> alas", "block.minecraft.banner.gradient.gray": "<PERSON><PERSON><PERSON> h<PERSON> alas", "block.minecraft.banner.gradient.green": "Vihreä häivytys alas", "block.minecraft.banner.gradient.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> häivytys alas", "block.minecraft.banner.gradient.light_gray": "<PERSON><PERSON>eanhar<PERSON><PERSON> h<PERSON> alas", "block.minecraft.banner.gradient.lime": "Limetinvihreä häivytys alas", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON><PERSON> häivytys alas", "block.minecraft.banner.gradient.orange": "Oranssi h<PERSON> alas", "block.minecraft.banner.gradient.pink": "Vaaleanpunainen häivytys alas", "block.minecraft.banner.gradient.purple": "<PERSON><PERSON> alas", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON> häivytys alas", "block.minecraft.banner.gradient.white": "Valkoinen häivytys alas", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON> häivytys alas", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON> h<PERSON>", "block.minecraft.banner.gradient_up.blue": "<PERSON><PERSON> häivytys ylö<PERSON>", "block.minecraft.banner.gradient_up.brown": "R<PERSON><PERSON> h<PERSON>", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON><PERSON> h<PERSON>", "block.minecraft.banner.gradient_up.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient_up.green": "Vihreä häivytys ylös", "block.minecraft.banner.gradient_up.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> häivytys ylö<PERSON>", "block.minecraft.banner.gradient_up.light_gray": "Vaaleanharma<PERSON> h<PERSON>", "block.minecraft.banner.gradient_up.lime": "Limetinvihreä häivytys ylös", "block.minecraft.banner.gradient_up.magenta": "Pur<PERSON><PERSON> häivytys yl<PERSON>", "block.minecraft.banner.gradient_up.orange": "Oranssi häiv<PERSON> ylö<PERSON>", "block.minecraft.banner.gradient_up.pink": "Vaaleanpunainen häivytys ylös", "block.minecraft.banner.gradient_up.purple": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.red": "<PERSON><PERSON>inen häivytys ylös", "block.minecraft.banner.gradient_up.white": "Valkoinen häivytys ylös", "block.minecraft.banner.gradient_up.yellow": "Ke<PERSON>inen häivytys ylös", "block.minecraft.banner.guster.black": "<PERSON><PERSON> tu<PERSON>", "block.minecraft.banner.guster.blue": "<PERSON><PERSON> tu<PERSON>", "block.minecraft.banner.guster.brown": "<PERSON><PERSON><PERSON> tuula<PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.guster.gray": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.guster.green": "Vih<PERSON><PERSON> tuulahdus", "block.minecraft.banner.guster.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> tu<PERSON>", "block.minecraft.banner.guster.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tu<PERSON>", "block.minecraft.banner.guster.lime": "Limetinvihreä tuulahdus", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON><PERSON> tuula<PERSON>", "block.minecraft.banner.guster.orange": "<PERSON><PERSON><PERSON> tu<PERSON>", "block.minecraft.banner.guster.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tu<PERSON>", "block.minecraft.banner.guster.purple": "<PERSON><PERSON>", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON> tu<PERSON>", "block.minecraft.banner.guster.white": "<PERSON><PERSON><PERSON> tu<PERSON>", "block.minecraft.banner.guster.yellow": "<PERSON><PERSON><PERSON> tu<PERSON>", "block.minecraft.banner.half_horizontal.black": "Musta <PERSON>", "block.minecraft.banner.half_horizontal.blue": "Sin<PERSON> y<PERSON>", "block.minecraft.banner.half_horizontal.brown": "Ruskea ylä<PERSON>oli", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.green": "Vihreä yläpuoli", "block.minecraft.banner.half_horizontal.light_blue": "V<PERSON><PERSON><PERSON>inen ylä<PERSON>oli", "block.minecraft.banner.half_horizontal.light_gray": "Vaaleanharmaa <PERSON>", "block.minecraft.banner.half_horizontal.lime": "Limetinvihreä yläpuoli", "block.minecraft.banner.half_horizontal.magenta": "Purppura yläpuoli", "block.minecraft.banner.half_horizontal.orange": "Oranssi ylä<PERSON>oli", "block.minecraft.banner.half_horizontal.pink": "Vaaleanpunainen yläpuoli", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.red": "<PERSON>unainen yläpuoli", "block.minecraft.banner.half_horizontal.white": "Valkoinen yläpuoli", "block.minecraft.banner.half_horizontal.yellow": "Keltainen yläpuoli", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON><PERSON> alapuoli", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "Vihreä alapuoli", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>", "block.minecraft.banner.half_horizontal_bottom.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>", "block.minecraft.banner.half_horizontal_bottom.lime": "Limetinvihreä alapuoli", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>uoli", "block.minecraft.banner.half_horizontal_bottom.orange": "<PERSON><PERSON><PERSON> al<PERSON>", "block.minecraft.banner.half_horizontal_bottom.pink": "Vaaleanpunainen al<PERSON>uoli", "block.minecraft.banner.half_horizontal_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON><PERSON> al<PERSON>", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON> al<PERSON>uo<PERSON>", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON><PERSON> al<PERSON>", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.green": "Vihreä halkoinen", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.lime": "Limetinvihreä halkoinen", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.pink": "Vaaleanpunainen halkoinen", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.black": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.green": "Vihreä <PERSON>", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.lime": "Limetinvihreä vast<PERSON>alkoinen", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.pink": "Vaaleanpunainen <PERSON>", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.black": "<PERSON><PERSON>-logo", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON>-logo", "block.minecraft.banner.mojang.brown": "Ruskea Mojang-logo", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON><PERSON>-logo", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON><PERSON>-logo", "block.minecraft.banner.mojang.green": "Vihreä Mo<PERSON>g-logo", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> Mojang-logo", "block.minecraft.banner.mojang.light_gray": "Vaaleanharmaa Mo<PERSON>g-logo", "block.minecraft.banner.mojang.lime": "Limetinvihreä Mojang-logo", "block.minecraft.banner.mojang.magenta": "P<PERSON><PERSON>ura Mojang-logo", "block.minecraft.banner.mojang.orange": "Oranssi <PERSON>-logo", "block.minecraft.banner.mojang.pink": "Vaaleanpunainen Mojang-logo", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON>-logo", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON> Mojang-logo", "block.minecraft.banner.mojang.white": "<PERSON><PERSON><PERSON>-logo", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON><PERSON> Mojang-logo", "block.minecraft.banner.piglin.black": "<PERSON><PERSON> k<PERSON>", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON> kä<PERSON>", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON><PERSON> kä<PERSON>", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.green": "Vihreä kärsä", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> kärsä", "block.minecraft.banner.piglin.light_gray": "<PERSON><PERSON>eanhar<PERSON><PERSON> kä<PERSON>", "block.minecraft.banner.piglin.lime": "Limetinvihreä kärsä", "block.minecraft.banner.piglin.magenta": "Pur<PERSON><PERSON> kärsä", "block.minecraft.banner.piglin.orange": "Oranssi kärsä", "block.minecraft.banner.piglin.pink": "Vaaleanpunainen kärsä", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON>", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON> kärsä", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON> kä<PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON> kärsä", "block.minecraft.banner.rhombus.black": "<PERSON>a ruutu", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON> ruutu", "block.minecraft.banner.rhombus.brown": "Rusk<PERSON> ruutu", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON> ruutu", "block.minecraft.banner.rhombus.gray": "<PERSON><PERSON><PERSON> ru<PERSON>u", "block.minecraft.banner.rhombus.green": "Vihreä ruutu", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruutu", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruutu", "block.minecraft.banner.rhombus.lime": "Limetinvihreä ruutu", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON><PERSON> ruutu", "block.minecraft.banner.rhombus.orange": "Orans<PERSON> ruutu", "block.minecraft.banner.rhombus.pink": "Vaaleanpunainen ruutu", "block.minecraft.banner.rhombus.purple": "<PERSON><PERSON> ru<PERSON>u", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON><PERSON> ruutu", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON> ruutu", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON> ruutu", "block.minecraft.banner.skull.black": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.skull.blue": "Sininen pääkallo", "block.minecraft.banner.skull.brown": "Ruskea pääkallo", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.green": "Vihreä pääkallo", "block.minecraft.banner.skull.light_blue": "Vaaleansininen pääkallo", "block.minecraft.banner.skull.light_gray": "Vaaleanharmaa p<PERSON>äkallo", "block.minecraft.banner.skull.lime": "Limetinvihreä pääkallo", "block.minecraft.banner.skull.magenta": "Purppura pääkallo", "block.minecraft.banner.skull.orange": "Oranssi pääkallo", "block.minecraft.banner.skull.pink": "Vaaleanpunainen pääkallo", "block.minecraft.banner.skull.purple": "<PERSON><PERSON>", "block.minecraft.banner.skull.red": "Punainen pääkallo", "block.minecraft.banner.skull.white": "Valkoinen pääkallo", "block.minecraft.banner.skull.yellow": "Keltainen pääkallo", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.gray": "<PERSON><PERSON><PERSON> p<PERSON>u", "block.minecraft.banner.small_stripes.green": "Vihreä paalu", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.lime": "Limetinvihreä paalu", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.orange": "<PERSON><PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.pink": "Vaalean<PERSON>nai<PERSON> paalu", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON> paalu", "block.minecraft.banner.small_stripes.yellow": "<PERSON><PERSON><PERSON> paalu", "block.minecraft.banner.square_bottom_left.black": "<PERSON>a vasen alak<PERSON>ma", "block.minecraft.banner.square_bottom_left.blue": "Sininen vasen alakulma", "block.minecraft.banner.square_bottom_left.brown": "Ruskea vasen alakulma", "block.minecraft.banner.square_bottom_left.cyan": "<PERSON><PERSON><PERSON> <PERSON>n al<PERSON>", "block.minecraft.banner.square_bottom_left.gray": "<PERSON><PERSON><PERSON> vasen al<PERSON>", "block.minecraft.banner.square_bottom_left.green": "Vihreä vasen alakulma", "block.minecraft.banner.square_bottom_left.light_blue": "Vaaleansininen vasen alakulma", "block.minecraft.banner.square_bottom_left.light_gray": "Vaaleanharmaa vasen alakulma", "block.minecraft.banner.square_bottom_left.lime": "Limetinvihreä vasen alakulma", "block.minecraft.banner.square_bottom_left.magenta": "<PERSON><PERSON><PERSON><PERSON> vasen alakulma", "block.minecraft.banner.square_bottom_left.orange": "Oranssi vasen alak<PERSON>ma", "block.minecraft.banner.square_bottom_left.pink": "Vaaleanpunainen vasen alakulma", "block.minecraft.banner.square_bottom_left.purple": "<PERSON><PERSON> vasen al<PERSON>", "block.minecraft.banner.square_bottom_left.red": "Punainen vasen alakulma", "block.minecraft.banner.square_bottom_left.white": "Valkoinen vasen alakulma", "block.minecraft.banner.square_bottom_left.yellow": "Keltainen vasen alakulma", "block.minecraft.banner.square_bottom_right.black": "<PERSON><PERSON> o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.blue": "<PERSON><PERSON> o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON><PERSON> o<PERSON>a alakulma", "block.minecraft.banner.square_bottom_right.cyan": "<PERSON><PERSON><PERSON> o<PERSON> al<PERSON>", "block.minecraft.banner.square_bottom_right.gray": "<PERSON><PERSON><PERSON> o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.green": "Vihreä o<PERSON>a al<PERSON>ma", "block.minecraft.banner.square_bottom_right.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>a al<PERSON>ma", "block.minecraft.banner.square_bottom_right.lime": "Limetinvihreä o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.orange": "<PERSON><PERSON><PERSON> o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.pink": "Vaaleanpunainen o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.purple": "<PERSON><PERSON> al<PERSON>", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON><PERSON> o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.white": "<PERSON><PERSON><PERSON> o<PERSON>a al<PERSON>", "block.minecraft.banner.square_bottom_right.yellow": "<PERSON><PERSON><PERSON> o<PERSON>a al<PERSON>", "block.minecraft.banner.square_top_left.black": "Musta vasen yläkulma", "block.minecraft.banner.square_top_left.blue": "Sininen vasen yläkulma", "block.minecraft.banner.square_top_left.brown": "Ruskea vasen yläkulma", "block.minecraft.banner.square_top_left.cyan": "Syaani vasen yläkulma", "block.minecraft.banner.square_top_left.gray": "<PERSON><PERSON><PERSON> vasen yl<PERSON>lma", "block.minecraft.banner.square_top_left.green": "Vihreä vasen yläkulma", "block.minecraft.banner.square_top_left.light_blue": "Vaaleansininen vasen yläkulma", "block.minecraft.banner.square_top_left.light_gray": "Vaaleanharmaa vasen yl<PERSON>kulma", "block.minecraft.banner.square_top_left.lime": "Limetinvihreä vasen yläkulma", "block.minecraft.banner.square_top_left.magenta": "Purppura vasen yläkulma", "block.minecraft.banner.square_top_left.orange": "Oranssi vasen yläkulma", "block.minecraft.banner.square_top_left.pink": "Vaaleanpunainen vasen yläkulma", "block.minecraft.banner.square_top_left.purple": "Violetti vasen yläkulma", "block.minecraft.banner.square_top_left.red": "Punainen vasen yläkulma", "block.minecraft.banner.square_top_left.white": "Valkoinen vasen yläkulma", "block.minecraft.banner.square_top_left.yellow": "Keltainen vasen yläkulma", "block.minecraft.banner.square_top_right.black": "<PERSON>a o<PERSON>a y<PERSON>", "block.minecraft.banner.square_top_right.blue": "<PERSON><PERSON> oikea yl<PERSON>kulma", "block.minecraft.banner.square_top_right.brown": "R<PERSON><PERSON> o<PERSON>a y<PERSON>", "block.minecraft.banner.square_top_right.cyan": "<PERSON><PERSON><PERSON> o<PERSON> y<PERSON>", "block.minecraft.banner.square_top_right.gray": "<PERSON><PERSON><PERSON> o<PERSON> y<PERSON>", "block.minecraft.banner.square_top_right.green": "Vihreä oikea y<PERSON>kulma", "block.minecraft.banner.square_top_right.light_blue": "<PERSON><PERSON><PERSON><PERSON>inen oikea y<PERSON>kulma", "block.minecraft.banner.square_top_right.light_gray": "<PERSON><PERSON>ean<PERSON><PERSON><PERSON> o<PERSON>a y<PERSON>", "block.minecraft.banner.square_top_right.lime": "Limetinvihreä oikea y<PERSON>", "block.minecraft.banner.square_top_right.magenta": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>a y<PERSON>", "block.minecraft.banner.square_top_right.orange": "Oranssi o<PERSON>a y<PERSON>", "block.minecraft.banner.square_top_right.pink": "Vaaleanpunainen oikea y<PERSON>äkulma", "block.minecraft.banner.square_top_right.purple": "<PERSON><PERSON> o<PERSON>", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON><PERSON> oikea yläkulma", "block.minecraft.banner.square_top_right.white": "Val<PERSON>inen o<PERSON>a yläkulma", "block.minecraft.banner.square_top_right.yellow": "<PERSON><PERSON><PERSON> oikea ylä<PERSON>lma", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON> risti", "block.minecraft.banner.straight_cross.blue": "<PERSON><PERSON> risti", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON>i", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON><PERSON> r<PERSON>i", "block.minecraft.banner.straight_cross.gray": "<PERSON><PERSON><PERSON>i", "block.minecraft.banner.straight_cross.green": "Vihreä risti", "block.minecraft.banner.straight_cross.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> risti", "block.minecraft.banner.straight_cross.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> risti", "block.minecraft.banner.straight_cross.lime": "Limetinvihreä risti", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON><PERSON> risti", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON><PERSON> risti", "block.minecraft.banner.straight_cross.pink": "Vaaleanpunainen risti", "block.minecraft.banner.straight_cross.purple": "<PERSON><PERSON>i", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON><PERSON> risti", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON><PERSON> risti", "block.minecraft.banner.straight_cross.yellow": "<PERSON><PERSON><PERSON> risti", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON> tyviö", "block.minecraft.banner.stripe_bottom.blue": "Sininen tyviö", "block.minecraft.banner.stripe_bottom.brown": "Ruskea tyviö", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.banner.stripe_bottom.green": "Vihreä tyviö", "block.minecraft.banner.stripe_bottom.light_blue": "V<PERSON><PERSON>sininen tyviö", "block.minecraft.banner.stripe_bottom.light_gray": "Vaaleanharmaa ty<PERSON>ö", "block.minecraft.banner.stripe_bottom.lime": "Limetinvihreä tyviö", "block.minecraft.banner.stripe_bottom.magenta": "Purppura tyviö", "block.minecraft.banner.stripe_bottom.orange": "Oranssi tyviö", "block.minecraft.banner.stripe_bottom.pink": "Vaaleanpunainen tyviö", "block.minecraft.banner.stripe_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.red": "Punainen tyviö", "block.minecraft.banner.stripe_bottom.white": "Valkoinen tyviö", "block.minecraft.banner.stripe_bottom.yellow": "Keltainen tyviö", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON> pys<PERSON>ta", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON> pystyraita", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_center.gray": "<PERSON><PERSON><PERSON> p<PERSON>ta", "block.minecraft.banner.stripe_center.green": "Vihreä pystyraita", "block.minecraft.banner.stripe_center.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> pys<PERSON>ta", "block.minecraft.banner.stripe_center.light_gray": "<PERSON><PERSON>ean<PERSON><PERSON><PERSON> pys<PERSON>ta", "block.minecraft.banner.stripe_center.lime": "Limetinvihreä pystyraita", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON><PERSON> pystyraita", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON><PERSON> pys<PERSON>ta", "block.minecraft.banner.stripe_center.pink": "Vaaleanpunainen pystyraita", "block.minecraft.banner.stripe_center.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_center.white": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_downleft.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.green": "Vihreä <PERSON>", "block.minecraft.banner.stripe_downleft.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.lime": "Limetinvihreä vastapalkki", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.orange": "Oranssi <PERSON>", "block.minecraft.banner.stripe_downleft.pink": "Vaaleanpunainen <PERSON>", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downleft.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.blue": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.brown": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "Vihreä palkki", "block.minecraft.banner.stripe_downright.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_downright.lime": "Limetinvihreä palkki", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_downright.orange": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_downright.pink": "Vaaleanpunainen palkki", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_downright.yellow": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.stripe_left.black": "Musta vasen reuna", "block.minecraft.banner.stripe_left.blue": "Sininen vasen reuna", "block.minecraft.banner.stripe_left.brown": "Ruskea vasen reuna", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON><PERSON> vasen reuna", "block.minecraft.banner.stripe_left.gray": "<PERSON><PERSON>a vasen reuna", "block.minecraft.banner.stripe_left.green": "Vihreä vasen reuna", "block.minecraft.banner.stripe_left.light_blue": "Vaaleansininen vasen reuna", "block.minecraft.banner.stripe_left.light_gray": "Vaaleanharmaa vasen reuna", "block.minecraft.banner.stripe_left.lime": "Limetinvihreä vasen reuna", "block.minecraft.banner.stripe_left.magenta": "Purppura vasen reuna", "block.minecraft.banner.stripe_left.orange": "Oranssi vasen reuna", "block.minecraft.banner.stripe_left.pink": "Vaaleanpunainen vasen reuna", "block.minecraft.banner.stripe_left.purple": "<PERSON><PERSON> vasen reuna", "block.minecraft.banner.stripe_left.red": "Punainen vasen reuna", "block.minecraft.banner.stripe_left.white": "Valkoinen vasen reuna", "block.minecraft.banner.stripe_left.yellow": "Keltainen vasen reuna", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON> vaa<PERSON>ita", "block.minecraft.banner.stripe_middle.blue": "<PERSON><PERSON> vaa<PERSON>", "block.minecraft.banner.stripe_middle.brown": "Ruskea vaakaraita", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON><PERSON> vaa<PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON><PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.green": "Vihreä vaakaraita", "block.minecraft.banner.stripe_middle.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> vaakaraita", "block.minecraft.banner.stripe_middle.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vaakaraita", "block.minecraft.banner.stripe_middle.lime": "Limetinvihreä vaakaraita", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON><PERSON> vaakaraita", "block.minecraft.banner.stripe_middle.orange": "Oranssi vaakaraita", "block.minecraft.banner.stripe_middle.pink": "Vaaleanpunainen vaakaraita", "block.minecraft.banner.stripe_middle.purple": "<PERSON><PERSON> v<PERSON>", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON> vaakaraita", "block.minecraft.banner.stripe_middle.white": "Valkoinen vaakaraita", "block.minecraft.banner.stripe_middle.yellow": "<PERSON><PERSON><PERSON> vaakaraita", "block.minecraft.banner.stripe_right.black": "<PERSON>a oikea reuna", "block.minecraft.banner.stripe_right.blue": "<PERSON><PERSON> oikea reuna", "block.minecraft.banner.stripe_right.brown": "Ruskea o<PERSON>a reuna", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON><PERSON> o<PERSON> reuna", "block.minecraft.banner.stripe_right.gray": "<PERSON><PERSON><PERSON> o<PERSON>a reuna", "block.minecraft.banner.stripe_right.green": "Vihreä oikea reuna", "block.minecraft.banner.stripe_right.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> oikea reuna", "block.minecraft.banner.stripe_right.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>a reuna", "block.minecraft.banner.stripe_right.lime": "Limetinvihreä oikea reuna", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>a reuna", "block.minecraft.banner.stripe_right.orange": "Oranssi o<PERSON>a reuna", "block.minecraft.banner.stripe_right.pink": "Vaaleanpunainen oikea reuna", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON> o<PERSON> reuna", "block.minecraft.banner.stripe_right.red": "<PERSON><PERSON>inen oikea reuna", "block.minecraft.banner.stripe_right.white": "Valkoinen oikea reuna", "block.minecraft.banner.stripe_right.yellow": "Ke<PERSON>inen oikea reuna", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON> lakio", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON> la<PERSON>o", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON> lakio", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON> la<PERSON>o", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON><PERSON> la<PERSON>o", "block.minecraft.banner.stripe_top.green": "Vihreä lakio", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> lakio", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>o", "block.minecraft.banner.stripe_top.lime": "Limetinvihreä lakio", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON><PERSON> lakio", "block.minecraft.banner.stripe_top.orange": "Oranssi lakio", "block.minecraft.banner.stripe_top.pink": "Vaaleanpunainen lakio", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON> lakio", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON> la<PERSON>o", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON><PERSON> lakio", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.blue": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.green": "Vihreä polviorsi", "block.minecraft.banner.triangle_bottom.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.triangle_bottom.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.lime": "Limetinvihreä polviorsi", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON><PERSON> p<PERSON>", "block.minecraft.banner.triangle_bottom.pink": "Vaaleanpunainen polviorsi", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON> pol<PERSON>", "block.minecraft.banner.triangle_bottom.white": "Valko<PERSON> pol<PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON><PERSON><PERSON> pol<PERSON>", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON> ka<PERSON> p<PERSON>", "block.minecraft.banner.triangle_top.blue": "<PERSON><PERSON> kaateinen pol<PERSON>", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON><PERSON> ka<PERSON> p<PERSON>", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON><PERSON> ka<PERSON> p<PERSON>", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON><PERSON> ka<PERSON> p<PERSON>", "block.minecraft.banner.triangle_top.green": "Vihreä kaateinen polviorsi", "block.minecraft.banner.triangle_top.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaateinen polviorsi", "block.minecraft.banner.triangle_top.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> p<PERSON>", "block.minecraft.banner.triangle_top.lime": "Limetinvihreä kaateinen polviorsi", "block.minecraft.banner.triangle_top.magenta": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>inen p<PERSON>", "block.minecraft.banner.triangle_top.orange": "Oranssi kaateinen polviorsi", "block.minecraft.banner.triangle_top.pink": "Vaaleanpunainen kaateinen polviorsi", "block.minecraft.banner.triangle_top.purple": "<PERSON><PERSON> ka<PERSON> p<PERSON>", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON><PERSON> kaateinen pol<PERSON>", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON> kaateinen pol<PERSON>", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON><PERSON> kaateinen pol<PERSON>", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON> hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.blue": "Sininen hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.brown": "Ruskea hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON><PERSON> hamma<PERSON> tyviö", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON><PERSON> ha<PERSON> tyviö", "block.minecraft.banner.triangles_bottom.green": "Vihreä hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.light_blue": "Vaaleansininen hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.light_gray": "Vaaleanharmaa hamma<PERSON> tyviö", "block.minecraft.banner.triangles_bottom.lime": "Limetinvihreä hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.magenta": "Purppura hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.orange": "Oranssi hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.pink": "Vaaleanpunainen hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON> tyviö", "block.minecraft.banner.triangles_bottom.red": "Punainen hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.white": "Valkoinen hammaskoroinen tyviö", "block.minecraft.banner.triangles_bottom.yellow": "Keltainen hammaskoroinen tyviö", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON> hamma<PERSON> lakio", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON> hammaskoroinen lakio", "block.minecraft.banner.triangles_top.brown": "R<PERSON><PERSON> hamma<PERSON>nen lakio", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON><PERSON> ha<PERSON> lakio", "block.minecraft.banner.triangles_top.gray": "<PERSON><PERSON><PERSON> ha<PERSON> la<PERSON>o", "block.minecraft.banner.triangles_top.green": "Vihreä hammaskoroinen lakio", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON><PERSON><PERSON>inen hammaskoroinen lakio", "block.minecraft.banner.triangles_top.light_gray": "<PERSON><PERSON>ean<PERSON><PERSON><PERSON> hamma<PERSON> lakio", "block.minecraft.banner.triangles_top.lime": "Limetinvihreä hammaskoroinen lakio", "block.minecraft.banner.triangles_top.magenta": "Pur<PERSON><PERSON> hammaskoroinen lakio", "block.minecraft.banner.triangles_top.orange": "Oranssi hammaskoroinen lakio", "block.minecraft.banner.triangles_top.pink": "Vaaleanpunainen hammaskoroinen lakio", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON> la<PERSON>o", "block.minecraft.banner.triangles_top.red": "Punainen hammaskoroinen lakio", "block.minecraft.banner.triangles_top.white": "Valkoinen hammaskoroinen lakio", "block.minecraft.banner.triangles_top.yellow": "Ke<PERSON>inen hammaskoroinen lakio", "block.minecraft.barrel": "<PERSON><PERSON><PERSON>", "block.minecraft.barrier": "Este", "block.minecraft.basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon": "Merkkivalo", "block.minecraft.beacon.primary": "Ensisijainen voima", "block.minecraft.beacon.secondary": "<PERSON><PERSON><PERSON><PERSON> voima", "block.minecraft.bed.no_sleep": "Voit nukkua vain öisin ja ukkosmyrskyjen aikana", "block.minecraft.bed.not_safe": "Et voi levätä nyt; lähistöllä on hirviöitä", "block.minecraft.bed.obstructed": "<PERSON><PERSON><PERSON><PERSON> sänky on estetty", "block.minecraft.bed.occupied": "Tämä sänky on varattu", "block.minecraft.bed.too_far_away": "Et voi levätä nyt; sä<PERSON> on liian kaukana", "block.minecraft.bedrock": "Peruskallio", "block.minecraft.bee_nest": "Luonnollinen mehiläispesä", "block.minecraft.beehive": "Keinotekoinen mehiläispesä", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bell": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "<PERSON><PERSON>", "block.minecraft.big_dripleaf_stem": "Iso tippulehtivarsi", "block.minecraft.birch_button": "Koivunappi", "block.minecraft.birch_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_fence": "Koivuaita", "block.minecraft.birch_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_hanging_sign": "Roikkuva koivukyltti", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_planks": "Koivulankut", "block.minecraft.birch_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_sapling": "<PERSON><PERSON><PERSON> taimi", "block.minecraft.birch_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_stairs": "Koivuportaat", "block.minecraft.birch_trapdoor": "Koivuluukku", "block.minecraft.birch_wall_hanging_sign": "Riippuva koivu<PERSON>ti", "block.minecraft.birch_wall_sign": "R<PERSON>ust<PERSON><PERSON> koivu<PERSON>ti", "block.minecraft.birch_wood": "Koivupuu", "block.minecraft.black_banner": "Musta viiri", "block.minecraft.black_bed": "<PERSON><PERSON>", "block.minecraft.black_candle": "<PERSON><PERSON>", "block.minecraft.black_candle_cake": "<PERSON><PERSON><PERSON> mustalla k<PERSON>", "block.minecraft.black_carpet": "<PERSON><PERSON> matto", "block.minecraft.black_concrete": "<PERSON><PERSON> betoni", "block.minecraft.black_concrete_powder": "<PERSON><PERSON>", "block.minecraft.black_glazed_terracotta": "Musta lasitettu terrakotta", "block.minecraft.black_shulker_box": "<PERSON><PERSON>-la<PERSON><PERSON><PERSON>", "block.minecraft.black_stained_glass": "<PERSON><PERSON> lasi", "block.minecraft.black_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.black_terracotta": "Musta terrakotta", "block.minecraft.black_wool": "Musta villa", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_stairs": "Mustakiviportaat", "block.minecraft.blackstone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blast_furnace": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_banner": "Sininen viiri", "block.minecraft.blue_bed": "<PERSON><PERSON> s<PERSON>", "block.minecraft.blue_candle": "<PERSON><PERSON> k<PERSON>ttil<PERSON>", "block.minecraft.blue_candle_cake": "Kakku sinisellä kynttilällä", "block.minecraft.blue_carpet": "<PERSON><PERSON> matto", "block.minecraft.blue_concrete": "<PERSON><PERSON> betoni", "block.minecraft.blue_concrete_powder": "<PERSON><PERSON>", "block.minecraft.blue_glazed_terracotta": "Sininen lasitettu terrakotta", "block.minecraft.blue_ice": "<PERSON><PERSON> j<PERSON>", "block.minecraft.blue_orchid": "Sininen orkidea", "block.minecraft.blue_shulker_box": "<PERSON><PERSON> s<PERSON>-la<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass": "<PERSON><PERSON>", "block.minecraft.blue_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.blue_terracotta": "<PERSON><PERSON> terrakotta", "block.minecraft.blue_wool": "Sininen villa", "block.minecraft.bone_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bookshelf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral": "Aivokoralli", "block.minecraft.brain_coral_block": "Aivokorallikuutio", "block.minecraft.brain_coral_fan": "Aivokoralliviuhka", "block.minecraft.brain_coral_wall_fan": "Seinäaivokoralliviuhka", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "T<PERSON><PERSON><PERSON>att<PERSON>", "block.minecraft.brick_stairs": "Tiiliportaat", "block.minecraft.brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bricks": "Tiilet", "block.minecraft.brown_banner": "Ruskea viiri", "block.minecraft.brown_bed": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.brown_candle_cake": "<PERSON><PERSON><PERSON> rusk<PERSON><PERSON> k<PERSON>ttilällä", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON> matto", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete_powder": "<PERSON><PERSON><PERSON> bet<PERSON>e", "block.minecraft.brown_glazed_terracotta": "Ruskea lasitettu terrakotta", "block.minecraft.brown_mushroom": "<PERSON><PERSON><PERSON> sieni", "block.minecraft.brown_mushroom_block": "R<PERSON><PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON> shul<PERSON>-la<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON><PERSON> lasi", "block.minecraft.brown_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_terracotta": "Rusk<PERSON> terrakotta", "block.minecraft.brown_wool": "Ruskea villa", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_wall_fan": "Seinäku<PERSON>lakoralliviuhka", "block.minecraft.budding_amethyst": "<PERSON><PERSON><PERSON><PERSON> ameti<PERSON>i", "block.minecraft.bush": "Pensas", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "<PERSON><PERSON><PERSON>sen kukka", "block.minecraft.cake": "Ka<PERSON><PERSON>", "block.minecraft.calcite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON><PERSON><PERSON> sculk-sensori", "block.minecraft.campfire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle": "Kynttilä", "block.minecraft.candle_cake": "Kynttiläkakku", "block.minecraft.carrots": "Porkkana", "block.minecraft.cartography_table": "Karttapöytä", "block.minecraft.carved_pumpkin": "Kaiverrettu kur<PERSON>", "block.minecraft.cauldron": "<PERSON><PERSON>", "block.minecraft.cave_air": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cave_vines_plant": "Luolaköynnöskasvi", "block.minecraft.chain": "<PERSON><PERSON><PERSON>", "block.minecraft.chain_command_block": "Sarjakomentokuutio", "block.minecraft.cherry_button": "Kirsikkapuunappi", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_hanging_sign": "Roik<PERSON><PERSON> k<PERSON>", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_planks": "Kirsikkapuulankut", "block.minecraft.cherry_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> taimi", "block.minecraft.cherry_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_stairs": "Ki<PERSON>ikkapuuportaa<PERSON>", "block.minecraft.cherry_trapdoor": "Kirsikkapuuluukku", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chest": "Arkku", "block.minecraft.chipped_anvil": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alasin", "block.minecraft.chiseled_bookshelf": "Täytettävä kirjahylly", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> kup<PERSON>", "block.minecraft.chiseled_deepslate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON><PERSON><PERSON>tii<PERSON>", "block.minecraft.chiseled_polished_blackstone": "Kai<PERSON><PERSON><PERSON> kii<PERSON>u must<PERSON>", "block.minecraft.chiseled_quartz_block": "Kaiverrettu k<PERSON>tsik<PERSON>o", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_resin_bricks": "Kaiverretut p<PERSON>katiilet", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.chiseled_stone_bricks": "Kaiverretut kivitiilet", "block.minecraft.chiseled_tuff": "<PERSON><PERSON><PERSON><PERSON> tuffi", "block.minecraft.chiseled_tuff_bricks": "<PERSON><PERSON><PERSON><PERSON> tuffitiilet", "block.minecraft.chorus_flower": "Chorus-<PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "Chorus-<PERSON><PERSON><PERSON>", "block.minecraft.clay": "<PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Sulkeutunut silm<PERSON>", "block.minecraft.coal_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.coarse_dirt": "<PERSON><PERSON><PERSON> multa", "block.minecraft.cobbled_deepslate": "Murtunut p<PERSON>", "block.minecraft.cobbled_deepslate_slab": "Murtunut p<PERSON>vilaatt<PERSON>", "block.minecraft.cobbled_deepslate_stairs": "Murtuneet pohjaliuskekiviportaat", "block.minecraft.cobbled_deepslate_wall": "Murt<PERSON>ut p<PERSON>", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_stairs": "Mukulakiviportaat", "block.minecraft.cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobweb": "Hämähäkinseit<PERSON>", "block.minecraft.cocoa": "<PERSON><PERSON><PERSON>", "block.minecraft.command_block": "Komentokuutio", "block.minecraft.comparator": "Redstone-komparaattori", "block.minecraft.composter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.conduit": "Kanavoija", "block.minecraft.copper_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_bulb": "Kuparilamppu", "block.minecraft.copper_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "Ku<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cornflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Murtuneet p<PERSON>lius<PERSON>ti<PERSON>t", "block.minecraft.cracked_deepslate_tiles": "Murtuneet pohjaliuskekivikaakelit", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON><PERSON> Nether-tiilet", "block.minecraft.cracked_polished_blackstone_bricks": "Murtuneet kiillote<PERSON>t must<PERSON>t", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON><PERSON> kivitiilet", "block.minecraft.crafter": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crafting_table": "Työpöytä", "block.minecraft.creaking_heart": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.creeper_head": "<PERSON><PERSON><PERSON><PERSON> pä<PERSON>", "block.minecraft.creeper_wall_head": "Ripustettu creeperin pää", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_fence": "Karm<PERSON>niaita", "block.minecraft.crimson_fence_gate": "Karm<PERSON>niportti", "block.minecraft.crimson_fungus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>att<PERSON>", "block.minecraft.crimson_roots": "Karm<PERSON>niju<PERSON>t", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stairs": "Karmiiniportaat", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "Karmiiniluukku", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crying_obsidian": "Itkevä obsidiaani", "block.minecraft.cut_copper": "<PERSON><PERSON><PERSON>u kupari", "block.minecraft.cut_copper_slab": "<PERSON><PERSON><PERSON>u kupari<PERSON>a", "block.minecraft.cut_copper_stairs": "Leikatut kupariportaat", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON>u pu<PERSON>", "block.minecraft.cut_red_sandstone_slab": "Le<PERSON>ttu pu<PERSON>a", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_sandstone_slab": "<PERSON><PERSON><PERSON>u hi<PERSON>", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON> viiri", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.cyan_candle_cake": "<PERSON><PERSON><PERSON> s<PERSON><PERSON>lla k<PERSON>ttilällä", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON> matto", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON> las<PERSON>u terrakotta", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON>-la<PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON> villa", "block.minecraft.damaged_anvil": "Vaurioit<PERSON>ut alasin", "block.minecraft.dandelion": "Voikukka", "block.minecraft.dark_oak_button": "<PERSON><PERSON> tammina<PERSON>i", "block.minecraft.dark_oak_door": "<PERSON><PERSON> tammio<PERSON>", "block.minecraft.dark_oak_fence": "<PERSON>mma tammiaita", "block.minecraft.dark_oak_fence_gate": "<PERSON><PERSON> tamm<PERSON>", "block.minecraft.dark_oak_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> tumma ta<PERSON>", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON>n tammen lehdet", "block.minecraft.dark_oak_log": "<PERSON><PERSON> tamm<PERSON>o", "block.minecraft.dark_oak_planks": "<PERSON><PERSON><PERSON> ta<PERSON>", "block.minecraft.dark_oak_pressure_plate": "<PERSON><PERSON> tammip<PERSON>a", "block.minecraft.dark_oak_sapling": "<PERSON><PERSON>n tammen taimi", "block.minecraft.dark_oak_sign": "<PERSON><PERSON>", "block.minecraft.dark_oak_slab": "<PERSON><PERSON> tammilaatta", "block.minecraft.dark_oak_stairs": "<PERSON><PERSON><PERSON> tamm<PERSON>t", "block.minecraft.dark_oak_trapdoor": "<PERSON>mma tammiluukku", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> tumma ta<PERSON>", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> tumma tamm<PERSON>", "block.minecraft.dark_oak_wood": "<PERSON>mma tammipuu", "block.minecraft.dark_prismarine": "<PERSON><PERSON> prismari<PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON>mma prismariinilaatta", "block.minecraft.dark_prismarine_stairs": "Tummat prismariiniportaat", "block.minecraft.daylight_detector": "Päivänvalotunnistin", "block.minecraft.dead_brain_coral": "Ku<PERSON>ut a<PERSON>", "block.minecraft.dead_brain_coral_block": "Kuollut aivokorallikuutio", "block.minecraft.dead_brain_coral_fan": "Kuollut aivokoralliviuhka", "block.minecraft.dead_brain_coral_wall_fan": "Kuollut seinäaivokoralliviuhka", "block.minecraft.dead_bubble_coral": "Ku<PERSON>ut k<PERSON>", "block.minecraft.dead_bubble_coral_block": "Kuollut k<PERSON>lakorallikuutio", "block.minecraft.dead_bubble_coral_fan": "Ku<PERSON>ut k<PERSON>uhka", "block.minecraft.dead_bubble_coral_wall_fan": "Kuollut seinäkuplak<PERSON>liviuhka", "block.minecraft.dead_bush": "Kuollut pensas", "block.minecraft.dead_fire_coral": "Kuollut tuli<PERSON>alli", "block.minecraft.dead_fire_coral_block": "Kuollut tulikorallikuutio", "block.minecraft.dead_fire_coral_fan": "Kuollut tulikoralliviuhka", "block.minecraft.dead_fire_coral_wall_fan": "Kuollut seinätulikoralliviuhka", "block.minecraft.dead_horn_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_block": "Kuollut torvikorallikuutio", "block.minecraft.dead_horn_coral_fan": "Kuollut to<PERSON>uhka", "block.minecraft.dead_horn_coral_wall_fan": "Kuollut seinätorvikoralliviuhka", "block.minecraft.dead_tube_coral": "Ku<PERSON>ut <PERSON>all<PERSON>", "block.minecraft.dead_tube_coral_block": "Kuollut putkikorallikuutio", "block.minecraft.dead_tube_coral_fan": "Kuollut putkikoralliviuhka", "block.minecraft.dead_tube_coral_wall_fan": "Kuollut seinäputkikoralliviuhka", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON><PERSON> ruukku", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "Pohjaliuskekiviti<PERSON>", "block.minecraft.deepslate_brick_stairs": "Pohjaliuskekivitiiliportaat", "block.minecraft.deepslate_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_bricks": "Pohjaliuskeki<PERSON>", "block.minecraft.deepslate_coal_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_diamond_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_emerald_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_iron_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_redstone_ore": "Pohjaliuskeredstone-malmi", "block.minecraft.deepslate_tile_slab": "Pohjaliuskekivikaakelilaatta", "block.minecraft.deepslate_tile_stairs": "Pohjaliuskekivikaakeliportaat", "block.minecraft.deepslate_tile_wall": "Po<PERSON><PERSON>liuskekivik<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_tiles": "Pohjaliuskekivikaakelit", "block.minecraft.detector_rail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diamond_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diamond_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "Dioriitt<PERSON>att<PERSON>", "block.minecraft.diorite_stairs": "Dioriittiportaat", "block.minecraft.diorite_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dirt": "Multa", "block.minecraft.dirt_path": "Multapolku", "block.minecraft.dispenser": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dragon_egg": "Lohikäärmeen muna", "block.minecraft.dragon_head": "Lohikäärmeen pää", "block.minecraft.dragon_wall_head": "Ripustettu lohikäärmeen pää", "block.minecraft.dried_ghast": "<PERSON><PERSON><PERSON><PERSON> ghast", "block.minecraft.dried_kelp_block": "Kuivattu rakkoleväkuutio", "block.minecraft.dripstone_block": "Tippukivikuutio", "block.minecraft.dropper": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Smaragdikuutio", "block.minecraft.emerald_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.enchanting_table": "Lumouspöytä", "block.minecraft.end_gateway": "End-portti", "block.minecraft.end_portal": "End-portaali", "block.minecraft.end_portal_frame": "End-port<PERSON><PERSON> kehys", "block.minecraft.end_rod": "End-sauva", "block.minecraft.end_stone": "End-kivi", "block.minecraft.end_stone_brick_slab": "End-k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_stairs": "End-kivitiiliportaat", "block.minecraft.end_stone_brick_wall": "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone_bricks": "End-kiv<PERSON><PERSON>t", "block.minecraft.ender_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> kupari", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON><PERSON> kup<PERSON>", "block.minecraft.exposed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> kup<PERSON>", "block.minecraft.exposed_copper_door": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.exposed_copper_grate": "Altistunut kupariritilä", "block.minecraft.exposed_copper_trapdoor": "Altistunut kup<PERSON>", "block.minecraft.exposed_cut_copper": "Altistunut leikattu kupari", "block.minecraft.exposed_cut_copper_slab": "Altistunut leikattu kupari<PERSON>a", "block.minecraft.exposed_cut_copper_stairs": "Altistuneet leikatut kupariportaat", "block.minecraft.farmland": "Vil<PERSON>lysmaa", "block.minecraft.fern": "<PERSON><PERSON><PERSON>", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_block": "Tulikorallikuutio", "block.minecraft.fire_coral_fan": "Tulikoralliviuhka", "block.minecraft.fire_coral_wall_fan": "Seinätulikoralliviuhka", "block.minecraft.firefly_bush": "Tulikärpäspensas", "block.minecraft.fletching_table": "Nuolipöytä", "block.minecraft.flower_pot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea": "Kukkiva atsalea", "block.minecraft.flowering_azalea_leaves": "Kukkivan atsalean lehdet", "block.minecraft.frogspawn": "Sammakonkutu", "block.minecraft.frosted_ice": "Huurteinen jää", "block.minecraft.furnace": "<PERSON><PERSON>", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glass": "<PERSON><PERSON>", "block.minecraft.glass_pane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glow_lichen": "Hehkujäkälä", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "Kultakuutio", "block.minecraft.gold_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite": "<PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "Graniittilaatta", "block.minecraft.granite_stairs": "Graniittiportaat", "block.minecraft.granite_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gravel": "<PERSON>ra", "block.minecraft.gray_banner": "<PERSON>rmaa viiri", "block.minecraft.gray_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_candle_cake": "<PERSON><PERSON><PERSON> harm<PERSON> k<PERSON>ttiläll<PERSON>", "block.minecraft.gray_carpet": "<PERSON><PERSON><PERSON> matto", "block.minecraft.gray_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_glazed_terracotta": "<PERSON><PERSON><PERSON> las<PERSON>ttu terrakotta", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON><PERSON> lasi", "block.minecraft.gray_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_terracotta": "<PERSON><PERSON><PERSON> terrakotta", "block.minecraft.gray_wool": "<PERSON>rma<PERSON> villa", "block.minecraft.green_banner": "Vihreä viiri", "block.minecraft.green_bed": "Vihreä sänky", "block.minecraft.green_candle": "Vihreä kynttilä", "block.minecraft.green_candle_cake": "Kakku vihreällä kynttilällä", "block.minecraft.green_carpet": "Vih<PERSON><PERSON> matto", "block.minecraft.green_concrete": "Vihre<PERSON> betoni", "block.minecraft.green_concrete_powder": "Vihreä betoni<PERSON>uhe", "block.minecraft.green_glazed_terracotta": "Vihreä lasitettu terrakotta", "block.minecraft.green_shulker_box": "Vihreä shulker-laatikko", "block.minecraft.green_stained_glass": "Vihreä lasi", "block.minecraft.green_stained_glass_pane": "Vihreä <PERSON>", "block.minecraft.green_terracotta": "Vihreä terrakotta", "block.minecraft.green_wool": "Vihreä villa", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON>", "block.minecraft.hanging_roots": "Roikkuvat juuret", "block.minecraft.hay_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "Raskas ydin", "block.minecraft.heavy_weighted_pressure_plate": "<PERSON><PERSON>a", "block.minecraft.honey_block": "Hu<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.honeycomb_block": "Hunajak<PERSON>no<PERSON>uti<PERSON>", "block.minecraft.hopper": "<PERSON><PERSON><PERSON>", "block.minecraft.horn_coral": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.horn_coral_block": "Torvikorallikuuti<PERSON>", "block.minecraft.horn_coral_fan": "Torvikoralliviuhka", "block.minecraft.horn_coral_wall_fan": "Seinätorvikoralliviuhka", "block.minecraft.ice": "Jää", "block.minecraft.infested_chiseled_stone_bricks": "Saastuneet kaiverretut kivitiilet", "block.minecraft.infested_cobblestone": "<PERSON><PERSON><PERSON><PERSON> muku<PERSON>", "block.minecraft.infested_cracked_stone_bricks": "Saastuneet murtuneet kivitiilet", "block.minecraft.infested_deepslate": "Saastunut p<PERSON>", "block.minecraft.infested_mossy_stone_bricks": "Saastuneet sammaleiset kivitiilet", "block.minecraft.infested_stone": "Saastunut kivi", "block.minecraft.infested_stone_bricks": "Saastuneet kivitiilet", "block.minecraft.iron_bars": "Ka<PERSON><PERSON>", "block.minecraft.iron_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_ore": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jack_o_lantern": "Kurpitsalyhty", "block.minecraft.jigsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jukebox": "Levysoitin", "block.minecraft.jungle_button": "Viidakkopuunappi", "block.minecraft.jungle_door": "Viidakkopuuovi", "block.minecraft.jungle_fence": "Viidakkopuuaita", "block.minecraft.jungle_fence_gate": "Viidakkopuuportti", "block.minecraft.jungle_hanging_sign": "Roikkuva viidakkopuukyltti", "block.minecraft.jungle_leaves": "Viidakko<PERSON><PERSON> le<PERSON>det", "block.minecraft.jungle_log": "Viidakkopuuhalko", "block.minecraft.jungle_planks": "Viidakkopuulankut", "block.minecraft.jungle_pressure_plate": "Viidakkopuupainelaatta", "block.minecraft.jungle_sapling": "V<PERSON>dak<PERSON><PERSON><PERSON> taimi", "block.minecraft.jungle_sign": "Viidakkopuuk<PERSON><PERSON>", "block.minecraft.jungle_slab": "Viidakkopuulaatta", "block.minecraft.jungle_stairs": "Viidakkopuuportaat", "block.minecraft.jungle_trapdoor": "Viidakkopuuluukku", "block.minecraft.jungle_wall_hanging_sign": "Riippuva viidakkopuukyltti", "block.minecraft.jungle_wall_sign": "Ripustettu viidakkopuukyltti", "block.minecraft.jungle_wood": "Viidakkopuu", "block.minecraft.kelp": "Rakkolevä", "block.minecraft.kelp_plant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ladder": "<PERSON><PERSON><PERSON>", "block.minecraft.lantern": "Lyhty", "block.minecraft.lapis_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lapis_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.large_amethyst_bud": "<PERSON><PERSON> ameti<PERSON><PERSON>", "block.minecraft.large_fern": "<PERSON><PERSON>", "block.minecraft.lava": "Laava", "block.minecraft.lava_cauldron": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "<PERSON><PERSON><PERSON>", "block.minecraft.lever": "Vipu", "block.minecraft.light": "Valo", "block.minecraft.light_blue_banner": "Vaaleansininen viiri", "block.minecraft.light_blue_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.light_blue_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>ttilä", "block.minecraft.light_blue_candle_cake": "Ka<PERSON><PERSON> vaaleansinisellä kynttilällä", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON> matto", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON> betoni", "block.minecraft.light_blue_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON> bet<PERSON>e", "block.minecraft.light_blue_glazed_terracotta": "V<PERSON>eansininen lasitettu terrakotta", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>-la<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON> lasi", "block.minecraft.light_blue_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_blue_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> terrakotta", "block.minecraft.light_blue_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON> villa", "block.minecraft.light_gray_banner": "Vaaleanharmaa viiri", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_candle": "<PERSON><PERSON>ean<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.light_gray_candle_cake": "<PERSON><PERSON><PERSON> vaaleanhar<PERSON><PERSON>a k<PERSON>ttilällä", "block.minecraft.light_gray_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> matto", "block.minecraft.light_gray_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> betoni", "block.minecraft.light_gray_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_glazed_terracotta": "V<PERSON>eanharmaa lasitettu terrakotta", "block.minecraft.light_gray_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-la<PERSON><PERSON>", "block.minecraft.light_gray_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lasi", "block.minecraft.light_gray_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.light_gray_terracotta": "Vaaleanharmaa terrakotta", "block.minecraft.light_gray_wool": "Vaaleanharmaa villa", "block.minecraft.light_weighted_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.lightning_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lilac": "Syreeni", "block.minecraft.lily_of_the_valley": "<PERSON><PERSON>", "block.minecraft.lily_pad": "Lumpeenlehti", "block.minecraft.lime_banner": "Limetinvihreä viiri", "block.minecraft.lime_bed": "Limetinvihreä sänky", "block.minecraft.lime_candle": "Limetinvihreä kynttilä", "block.minecraft.lime_candle_cake": "Kakku limetinvihreällä kynttilällä", "block.minecraft.lime_carpet": "Limetinvihreä matto", "block.minecraft.lime_concrete": "Limetinvihreä betoni", "block.minecraft.lime_concrete_powder": "Limetinvihreä betonijauhe", "block.minecraft.lime_glazed_terracotta": "Limetinvihreä lasitettu terrakotta", "block.minecraft.lime_shulker_box": "Limetinvihreä shulker-laatikko", "block.minecraft.lime_stained_glass": "Limetinvihreä lasi", "block.minecraft.lime_stained_glass_pane": "Limetinvihreä las<PERSON>eli", "block.minecraft.lime_terracotta": "Limetinvihreä terrakotta", "block.minecraft.lime_wool": "Limetinvihreä villa", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_banner": "Purppura viiri", "block.minecraft.magenta_bed": "P<PERSON><PERSON><PERSON> sänky", "block.minecraft.magenta_candle": "P<PERSON><PERSON><PERSON> kynttilä", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON><PERSON> purp<PERSON>lla k<PERSON>ttilällä", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON><PERSON> matto", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON><PERSON> betoni", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> bet<PERSON>e", "block.minecraft.magenta_glazed_terracotta": "Pur<PERSON>ura lasitettu terrakotta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON><PERSON> shul<PERSON>-la<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON><PERSON> lasi", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON><PERSON> terrakotta", "block.minecraft.magenta_wool": "Pur<PERSON>ura villa", "block.minecraft.magma_block": "Magmakuutio", "block.minecraft.mangrove_button": "Mangrovepuunappi", "block.minecraft.mangrove_door": "Mangrovepuuovi", "block.minecraft.mangrove_fence": "Mangrovepuuaita", "block.minecraft.mangrove_fence_gate": "Man<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_hanging_sign": "Roikkuva mangrovepuukyltti", "block.minecraft.mangrove_leaves": "Mangrove<PERSON><PERSON> lehdet", "block.minecraft.mangrove_log": "Mangrovepuuhalko", "block.minecraft.mangrove_planks": "Mangrovepuulankut", "block.minecraft.mangrove_pressure_plate": "Mangrovepuupainelaatta", "block.minecraft.mangrove_propagule": "Mangrovepuun leviäin", "block.minecraft.mangrove_roots": "Mangrovepuun juurakko", "block.minecraft.mangrove_sign": "Mangrovepu<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_slab": "Mangrovepuulaatta", "block.minecraft.mangrove_stairs": "Mangrovepuuportaat", "block.minecraft.mangrove_trapdoor": "Mangrovepuuluukku", "block.minecraft.mangrove_wall_hanging_sign": "Riippuva mangrovepuukyltti", "block.minecraft.mangrove_wall_sign": "Ripustettu mangrovepuukyltti", "block.minecraft.mangrove_wood": "Mangrovepuu", "block.minecraft.medium_amethyst_bud": "Keskikokoinen ametistinuppu", "block.minecraft.melon": "<PERSON><PERSON>", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON> varsi", "block.minecraft.moss_block": "Sammalkuutio", "block.minecraft.moss_carpet": "Sammalmatto", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON><PERSON><PERSON> muku<PERSON>i", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "block.minecraft.mossy_cobblestone_stairs": "Sammaleiset mukulakiviportaat", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_brick_slab": "Sammaleinen kivitiili<PERSON>atta", "block.minecraft.mossy_stone_brick_stairs": "Sammaleiset kivitiiliportaat", "block.minecraft.mossy_stone_brick_wall": "Sammalein<PERSON> kivitiilimuuri", "block.minecraft.mossy_stone_bricks": "Sammaleiset kivitiilet", "block.minecraft.moving_piston": "Liikkuva mäntä", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_brick_stairs": "Mutatiiliportaat", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mud_bricks": "Mutat<PERSON>let", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON> mangrove<PERSON>un juurakko", "block.minecraft.mushroom_stem": "<PERSON><PERSON><PERSON> var<PERSON>", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Nether-tiiliaita", "block.minecraft.nether_brick_slab": "Nether-tii<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_stairs": "Nether-tiiliportaat", "block.minecraft.nether_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_bricks": "Nether-tiilet", "block.minecraft.nether_gold_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_portal": "Nether-portaali", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_sprouts": "Nether-verso", "block.minecraft.nether_wart": "Nether-pahka", "block.minecraft.nether_wart_block": "Nether-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.netherite_block": "Netheriittikuutio", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Sointukuutio", "block.minecraft.oak_button": "Tamminappi", "block.minecraft.oak_door": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_fence": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_fence_gate": "Tammiport<PERSON>", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_log": "<PERSON><PERSON>hal<PERSON>", "block.minecraft.oak_planks": "Tammilankut", "block.minecraft.oak_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_sapling": "<PERSON><PERSON> taimi", "block.minecraft.oak_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_stairs": "Tammiportaat", "block.minecraft.oak_trapdoor": "Tam<PERSON>luuk<PERSON>", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_wood": "Tam<PERSON><PERSON><PERSON>", "block.minecraft.observer": "Tarkkailija", "block.minecraft.obsidian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "<PERSON><PERSON>el<PERSON><PERSON>", "block.minecraft.ominous_banner": "Pahaenteinen viiri", "block.minecraft.open_eyeblossom": "Avautunut silm<PERSON>", "block.minecraft.orange_banner": "Oranssi viiri", "block.minecraft.orange_bed": "Oranssi s<PERSON>", "block.minecraft.orange_candle": "Oranssi k<PERSON>ttilä", "block.minecraft.orange_candle_cake": "<PERSON><PERSON><PERSON> or<PERSON><PERSON><PERSON> k<PERSON>ttilällä", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON> matto", "block.minecraft.orange_concrete": "<PERSON><PERSON><PERSON> betoni", "block.minecraft.orange_concrete_powder": "Or<PERSON><PERSON> bet<PERSON>", "block.minecraft.orange_glazed_terracotta": "Oranssi lasitettu terrakotta", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON> s<PERSON>-la<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass": "Or<PERSON><PERSON> lasi", "block.minecraft.orange_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_terracotta": "Oranssi terrakotta", "block.minecraft.orange_tulip": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_wool": "Oranssi villa", "block.minecraft.oxeye_daisy": "Päivänkakkara", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>tu kupari", "block.minecraft.oxidized_copper": "<PERSON><PERSON><PERSON><PERSON> kupari", "block.minecraft.oxidized_copper_bulb": "Ha<PERSON><PERSON><PERSON> kup<PERSON>", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON><PERSON><PERSON> kup<PERSON>", "block.minecraft.oxidized_copper_grate": "Hapettunut kupariritilä", "block.minecraft.oxidized_copper_trapdoor": "Hapettunut kup<PERSON>ukku", "block.minecraft.oxidized_cut_copper": "Hapet<PERSON>ut leikattu kupari", "block.minecraft.oxidized_cut_copper_slab": "Ha<PERSON><PERSON>ut leikattu kupari<PERSON>a", "block.minecraft.oxidized_cut_copper_stairs": "Hapettuneet leikatut kupariportaat", "block.minecraft.packed_ice": "Pakkautunut jää", "block.minecraft.packed_mud": "Pakkautunut muta", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.pale_moss_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_moss_carpet": "Ka<PERSON>vassammalmatto", "block.minecraft.pale_oak_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>pp<PERSON>", "block.minecraft.pale_oak_door": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_hanging_sign": "R<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.pale_oak_leaves": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>det", "block.minecraft.pale_oak_log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_planks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON> taimi", "block.minecraft.pale_oak_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_stairs": "Kalvastammiportaat", "block.minecraft.pale_oak_trapdoor": "Ka<PERSON><PERSON><PERSON><PERSON>luuk<PERSON>", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.pale_oak_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.pale_oak_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.peony": "<PERSON><PERSON>", "block.minecraft.petrified_oak_slab": "<PERSON><PERSON><PERSON><PERSON> tamm<PERSON>a", "block.minecraft.piglin_head": "Piglinin pää", "block.minecraft.piglin_wall_head": "Ripustettu piglinin pää", "block.minecraft.pink_banner": "Vaaleanpunainen viiri", "block.minecraft.pink_bed": "Vaaleanpunainen sänky", "block.minecraft.pink_candle": "Vaaleanpunainen kynttilä", "block.minecraft.pink_candle_cake": "Ka<PERSON><PERSON> vaaleanpunaisella kynttilällä", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> matto", "block.minecraft.pink_concrete": "Vaalean<PERSON><PERSON><PERSON> betoni", "block.minecraft.pink_concrete_powder": "Vaaleanpunainen bet<PERSON>e", "block.minecraft.pink_glazed_terracotta": "Vaaleanpunainen lasitettu terrakotta", "block.minecraft.pink_petals": "Vaaleanpunaiset terälehdet", "block.minecraft.pink_shulker_box": "Vaaleanpunainen shulker-laatikko", "block.minecraft.pink_stained_glass": "Vaalean<PERSON>nai<PERSON> lasi", "block.minecraft.pink_stained_glass_pane": "V<PERSON>ean<PERSON><PERSON><PERSON>", "block.minecraft.pink_terracotta": "Vaaleanpunainen terrakotta", "block.minecraft.pink_tulip": "<PERSON><PERSON>ean<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.pink_wool": "Vaaleanpunainen villa", "block.minecraft.piston": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON><PERSON><PERSON> pää", "block.minecraft.pitcher_crop": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.player_head": "Pelaajan pää", "block.minecraft.player_head.named": "Pelaajan %s pää", "block.minecraft.player_wall_head": "Ripustettu pelaajan pää", "block.minecraft.podzol": "Podsoli", "block.minecraft.pointed_dripstone": "Terävä tippukivi", "block.minecraft.polished_andesite": "Kiillotettu andesiitti", "block.minecraft.polished_andesite_slab": "Kiillotettu andesiittilaatta", "block.minecraft.polished_andesite_stairs": "Kiillotetut andesiittiportaat", "block.minecraft.polished_basalt": "Kiillotettu basaltti", "block.minecraft.polished_blackstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_brick_stairs": "Kiillotetut mustakivitiiliportaat", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_bricks": "Kiillote<PERSON>t <PERSON>", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_blackstone_stairs": "Kiillotetut mustakiviportaat", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.polished_deepslate": "Kiillotettu <PERSON>", "block.minecraft.polished_deepslate_slab": "Kiillotettu p<PERSON>vilaatt<PERSON>", "block.minecraft.polished_deepslate_stairs": "Kiillotetut pohjaliuskekiviportaat", "block.minecraft.polished_deepslate_wall": "Kiillotettu p<PERSON>", "block.minecraft.polished_diorite": "Kiillotettu di<PERSON>itti", "block.minecraft.polished_diorite_slab": "Kiillotettu dioriittilaatta", "block.minecraft.polished_diorite_stairs": "Kiillotetut dioriittiportaat", "block.minecraft.polished_granite": "Kiillotettu grani<PERSON>i", "block.minecraft.polished_granite_slab": "Kiillotettu graniitt<PERSON>atta", "block.minecraft.polished_granite_stairs": "Kiillotetut graniittiportaat", "block.minecraft.polished_tuff": "Kiillotettu tuffi", "block.minecraft.polished_tuff_slab": "Kiillotettu tuffilaatta", "block.minecraft.polished_tuff_stairs": "Kiillotetut tuffiportaat", "block.minecraft.polished_tuff_wall": "Kiillotett<PERSON> tuffim<PERSON>", "block.minecraft.poppy": "<PERSON><PERSON><PERSON>", "block.minecraft.potatoes": "Peruna", "block.minecraft.potted_acacia_sapling": "<PERSON><PERSON><PERSON><PERSON><PERSON> akaasian taimi", "block.minecraft.potted_allium": "Istutettu la<PERSON>ka", "block.minecraft.potted_azalea_bush": "Istutettu atsalea", "block.minecraft.potted_azure_bluet": "Istutettu keijunsilmä", "block.minecraft.potted_bamboo": "Istutettu bambu", "block.minecraft.potted_birch_sapling": "Istutettu koivun taimi", "block.minecraft.potted_blue_orchid": "Istutettu sininen orkidea", "block.minecraft.potted_brown_mushroom": "Istutettu ruskea sieni", "block.minecraft.potted_cactus": "Istutettu kaktus", "block.minecraft.potted_cherry_sapling": "Istu<PERSON><PERSON><PERSON> kirs<PERSON> taimi", "block.minecraft.potted_closed_eyeblossom": "Istutettu sulkeutunut silm<PERSON>kka", "block.minecraft.potted_cornflower": "Istutettu ruiskaunokki", "block.minecraft.potted_crimson_fungus": "Istutettu karm<PERSON>i", "block.minecraft.potted_crimson_roots": "Istutetut karmiiniju<PERSON>t", "block.minecraft.potted_dandelion": "Istutettu voikukka", "block.minecraft.potted_dark_oak_sapling": "Is<PERSON><PERSON><PERSON><PERSON> tumman tammen taimi", "block.minecraft.potted_dead_bush": "Istutettu kuollut pensas", "block.minecraft.potted_fern": "Istute<PERSON><PERSON> sa<PERSON>", "block.minecraft.potted_flowering_azalea_bush": "Istutettu kukkiva atsalea", "block.minecraft.potted_jungle_sapling": "Istutettu viidakkopuun taimi", "block.minecraft.potted_lily_of_the_valley": "Istutettu kielo", "block.minecraft.potted_mangrove_propagule": "Istutettu mangrovepuun leviäin", "block.minecraft.potted_oak_sapling": "Istute<PERSON><PERSON> tammen taimi", "block.minecraft.potted_open_eyeblossom": "Istutettu avautunut silm<PERSON>kka", "block.minecraft.potted_orange_tulip": "Istutettu orans<PERSON> tulp<PERSON>ani", "block.minecraft.potted_oxeye_daisy": "Istutettu päivänkakkara", "block.minecraft.potted_pale_oak_sapling": "Istute<PERSON><PERSON> kalvastammen taimi", "block.minecraft.potted_pink_tulip": "Istutettu vaaleanpunainen tulppaani", "block.minecraft.potted_poppy": "Istutettu unikko", "block.minecraft.potted_red_mushroom": "Istutettu kärpässieni", "block.minecraft.potted_red_tulip": "Istutettu punainen tulppaani", "block.minecraft.potted_spruce_sapling": "Istutettu kuusen taimi", "block.minecraft.potted_torchflower": "Istutettu soih<PERSON>lilja", "block.minecraft.potted_warped_fungus": "Istutettu kum<PERSON>i", "block.minecraft.potted_warped_roots": "Istutetut kum<PERSON>t", "block.minecraft.potted_white_tulip": "Istutettu valkoinen tulp<PERSON>ani", "block.minecraft.potted_wither_rose": "Istutettu wither-ruusu", "block.minecraft.powder_snow": "P<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow_cauldron": "Puuterilumipa<PERSON>", "block.minecraft.powered_rail": "<PERSON><PERSON><PERSON>tin<PERSON><PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Prismariiniti<PERSON><PERSON>atta", "block.minecraft.prismarine_brick_stairs": "Prismariinitiiliportaat", "block.minecraft.prismarine_bricks": "Prism<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_slab": "Prismariinilaatta", "block.minecraft.prismarine_stairs": "Prismariiniportaat", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin_stem": "<PERSON><PERSON><PERSON><PERSON> varsi", "block.minecraft.purple_banner": "Violetti viiri", "block.minecraft.purple_bed": "<PERSON><PERSON>", "block.minecraft.purple_candle": "<PERSON><PERSON>", "block.minecraft.purple_candle_cake": "Kak<PERSON> <PERSON>illa k<PERSON>ttilällä", "block.minecraft.purple_carpet": "<PERSON><PERSON> matto", "block.minecraft.purple_concrete": "<PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON>", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON> terra<PERSON>", "block.minecraft.purple_shulker_box": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass": "<PERSON><PERSON>", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.purple_terracotta": "<PERSON><PERSON>", "block.minecraft.purple_wool": "<PERSON>ti villa", "block.minecraft.purpur_block": "Purpur-kuutio", "block.minecraft.purpur_pillar": "Purpur-pilari", "block.minecraft.purpur_slab": "Purpur-laatta", "block.minecraft.purpur_stairs": "Purpur-portaat", "block.minecraft.quartz_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_bricks": "Kvartsitiilet", "block.minecraft.quartz_pillar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.quartz_slab": "Kvar<PERSON><PERSON>att<PERSON>", "block.minecraft.quartz_stairs": "Kvartsiportaat", "block.minecraft.rail": "Raide", "block.minecraft.raw_copper_block": "<PERSON><PERSON>", "block.minecraft.raw_gold_block": "<PERSON><PERSON>", "block.minecraft.raw_iron_block": "<PERSON><PERSON>", "block.minecraft.red_banner": "Punainen viiri", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON> s<PERSON>nky", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON> kynttilä", "block.minecraft.red_candle_cake": "<PERSON><PERSON><PERSON> pu<PERSON><PERSON> k<PERSON>ttilällä", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON> matto", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON> bet<PERSON>e", "block.minecraft.red_glazed_terracotta": "Punainen lasitettu terrakotta", "block.minecraft.red_mushroom": "Kärpässieni", "block.minecraft.red_mushroom_block": "Kärpässienikuutio", "block.minecraft.red_nether_brick_slab": "<PERSON><PERSON><PERSON>her-tii<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_stairs": "Punaiset Nether-tiiliportaat", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON><PERSON>her-t<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON><PERSON>-tii<PERSON>", "block.minecraft.red_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "P<PERSON><PERSON>ek<PERSON><PERSON>att<PERSON>", "block.minecraft.red_sandstone_stairs": "Punahiekkakiviportaat", "block.minecraft.red_sandstone_wall": "P<PERSON>hiek<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON> shulker-la<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> lasi", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.red_terracotta": "<PERSON><PERSON>inen terrakotta", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON> villa", "block.minecraft.redstone_block": "Redstone-kuutio", "block.minecraft.redstone_lamp": "Redstone-lamppu", "block.minecraft.redstone_ore": "Redstone-malmi", "block.minecraft.redstone_torch": "Redstone-soihtu", "block.minecraft.redstone_wall_torch": "Se<PERSON>älle asetettu redstone-soihtu", "block.minecraft.redstone_wire": "Redstone-johdin", "block.minecraft.reinforced_deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.repeater": "Redstone-toistin", "block.minecraft.repeating_command_block": "Toistava komentokuutio", "block.minecraft.resin_block": "Pihkakuutio", "block.minecraft.resin_brick_slab": "Pihkatiililaatta", "block.minecraft.resin_brick_stairs": "Pihkatiiliportaat", "block.minecraft.resin_brick_wall": "Pihkatiilimuuri", "block.minecraft.resin_bricks": "Pihkatiilet", "block.minecraft.resin_clump": "Pihkakimpale", "block.minecraft.respawn_anchor": "U<PERSON><PERSON>ensyntymisankku<PERSON>", "block.minecraft.rooted_dirt": "<PERSON><PERSON><PERSON><PERSON> multa", "block.minecraft.rose_bush": "Ruusupensas", "block.minecraft.sand": "<PERSON><PERSON><PERSON>", "block.minecraft.sandstone": "Hiekka<PERSON><PERSON>", "block.minecraft.sandstone_slab": "Hiek<PERSON><PERSON><PERSON>att<PERSON>", "block.minecraft.sandstone_stairs": "Hiekkakiviportaat", "block.minecraft.sandstone_wall": "Hiekka<PERSON>vimu<PERSON>", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "<PERSON>ulk<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sculk_sensor": "Sculk-sensori", "block.minecraft.sculk_shrieker": "Sculk-kirkuja", "block.minecraft.sculk_vein": "Sculk-suoni", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.set_spawn": "Uudelleensyntymispis<PERSON> asete<PERSON>u", "block.minecraft.short_dry_grass": "Lyhyt kuiva ruoho", "block.minecraft.short_grass": "Lyhyt ruoho", "block.minecraft.shroomlight": "Sienivalo", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "<PERSON><PERSON><PERSON><PERSON> kallo", "block.minecraft.skeleton_wall_skull": "<PERSON><PERSON><PERSON><PERSON><PERSON> luurangon kallo", "block.minecraft.slime_block": "Limakuutio", "block.minecraft.small_amethyst_bud": "<PERSON><PERSON> ameti<PERSON>", "block.minecraft.small_dripleaf": "<PERSON><PERSON>", "block.minecraft.smithing_table": "Takomispöytä", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz": "Si<PERSON>ä k<PERSON>o", "block.minecraft.smooth_quartz_slab": "<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.smooth_quartz_stairs": "Sileät kvartsiportaat", "block.minecraft.smooth_red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_red_sandstone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_red_sandstone_stairs": "Sileät punahiekkakiviportaat", "block.minecraft.smooth_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.smooth_sandstone_stairs": "Sileät hiekkakiviportaat", "block.minecraft.smooth_stone": "<PERSON><PERSON><PERSON> kivi", "block.minecraft.smooth_stone_slab": "<PERSON><PERSON><PERSON> kiv<PERSON>a", "block.minecraft.sniffer_egg": "<PERSON>uuhkun muna", "block.minecraft.snow": "<PERSON><PERSON>", "block.minecraft.snow_block": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_campfire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_fire": "Sielutuli", "block.minecraft.soul_lantern": "Sielulyhty", "block.minecraft.soul_sand": "Sieluhiekka", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON><PERSON> as<PERSON>ttu siel<PERSON>u", "block.minecraft.spawn.not_valid": "<PERSON>ulla ei ole kotisänkyä eikä ladattua uudelleensyntymisankkuria, tai niiden edessä oli este", "block.minecraft.spawner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spawner.desc1": "Käytä luomismunaa:", "block.minecraft.spawner.desc2": "<PERSON><PERSON><PERSON> o<PERSON> t<PERSON>", "block.minecraft.sponge": "P<PERSON>us<PERSON><PERSON>", "block.minecraft.spore_blossom": "Itiökukka", "block.minecraft.spruce_button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_leaves": "Kuusenhavut", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_planks": "Kuusilankut", "block.minecraft.spruce_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_sapling": "<PERSON><PERSON><PERSON> taimi", "block.minecraft.spruce_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_stairs": "Kuusiportaa<PERSON>", "block.minecraft.spruce_trapdoor": "Kuusiluuk<PERSON>", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.spruce_wood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sticky_piston": "Tarttumamäntä", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_stairs": "Kivitiiliportaat", "block.minecraft.stone_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_stairs": "Kiviportaat", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_wood": "<PERSON><PERSON><PERSON><PERSON> akaasia", "block.minecraft.stripped_bamboo_block": "<PERSON><PERSON><PERSON><PERSON> bam<PERSON>", "block.minecraft.stripped_birch_log": "Kuorittu koivuhalko", "block.minecraft.stripped_birch_wood": "Kuorittu koivu", "block.minecraft.stripped_cherry_log": "Ku<PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.stripped_cherry_wood": "Ku<PERSON>ttu k<PERSON>u", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON><PERSON> karm<PERSON>", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON>u tumma tammihalko", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON>u tumma tammi", "block.minecraft.stripped_jungle_log": "Kuorittu viidakkopuuhalko", "block.minecraft.stripped_jungle_wood": "Kuorittu viidakkopuu", "block.minecraft.stripped_mangrove_log": "Kuorittu mangrovepuuhalko", "block.minecraft.stripped_mangrove_wood": "Kuorittu mangrovepuu", "block.minecraft.stripped_oak_log": "<PERSON><PERSON><PERSON><PERSON> tammih<PERSON>o", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON><PERSON>u tammi", "block.minecraft.stripped_pale_oak_log": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.stripped_pale_oak_wood": "<PERSON><PERSON><PERSON>u kal<PERSON>", "block.minecraft.stripped_spruce_log": "<PERSON><PERSON><PERSON><PERSON> kuus<PERSON>o", "block.minecraft.stripped_spruce_wood": "<PERSON><PERSON><PERSON>u kuusi", "block.minecraft.stripped_warped_hyphae": "<PERSON><PERSON><PERSON><PERSON> kummar<PERSON>ma", "block.minecraft.stripped_warped_stem": "<PERSON><PERSON><PERSON><PERSON> kumma<PERSON>", "block.minecraft.structure_block": "Rakennelmakuutio", "block.minecraft.structure_void": "Rakennelmatyhjiö", "block.minecraft.sugar_cane": "So<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sunflower": "Auringonkukka", "block.minecraft.suspicious_gravel": "Epäilyttävä sora", "block.minecraft.suspicious_sand": "Epäilyttävä hiekka", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON>", "block.minecraft.tall_dry_grass": "Pitkä kuiva ruoho", "block.minecraft.tall_grass": "Pitk<PERSON> ruoho", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON> me<PERSON>o", "block.minecraft.target": "Maalitaulu", "block.minecraft.terracotta": "Terrakotta", "block.minecraft.test_block": "Testikuutio", "block.minecraft.test_instance_block": "Testitapauskuutio", "block.minecraft.tinted_glass": "<PERSON><PERSON>nnettu lasi", "block.minecraft.tnt": "TNT", "block.minecraft.tnt.disabled": "TNT:n räjähdykset ovat poissa käytöstä", "block.minecraft.torch": "<PERSON><PERSON><PERSON>", "block.minecraft.torchflower": "Soih<PERSON>lilja", "block.minecraft.torchflower_crop": "Soih<PERSON>lilja", "block.minecraft.trapped_chest": "Ansoitettu arkku", "block.minecraft.trial_spawner": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "Ansalankakoukku", "block.minecraft.tube_coral": "Putkikoralli", "block.minecraft.tube_coral_block": "Putkikorallikuutio", "block.minecraft.tube_coral_fan": "Putkikoralliviuhka", "block.minecraft.tube_coral_wall_fan": "Seinäputkikoralliviuhka", "block.minecraft.tuff": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_brick_stairs": "Tuffitiiliportaat", "block.minecraft.tuff_brick_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tuff_slab": "Tuff<PERSON>att<PERSON>", "block.minecraft.tuff_stairs": "Tuffiportaat", "block.minecraft.tuff_wall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.turtle_egg": "Kilpikonnan muna", "block.minecraft.twisting_vines": "Kieroköynn<PERSON><PERSON>", "block.minecraft.twisting_vines_plant": "Kieroköynnöskasvi", "block.minecraft.vault": "<PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "Vihertäv<PERSON> samma<PERSON>", "block.minecraft.vine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.void_air": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON><PERSON> as<PERSON> so<PERSON>", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fence_gate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_fungus": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_hanging_sign": "Roik<PERSON><PERSON> kum<PERSON>", "block.minecraft.warped_hyphae": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_nylium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_planks": "Kummalan<PERSON><PERSON>", "block.minecraft.warped_pressure_plate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_stairs": "Kummaportaat", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.warped_wall_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON> kum<PERSON>", "block.minecraft.warped_wart_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.water": "<PERSON><PERSON>", "block.minecraft.water_cauldron": "Vesipata", "block.minecraft.waxed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>tu kupari", "block.minecraft.waxed_copper_block": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> kup<PERSON>", "block.minecraft.waxed_copper_door": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON><PERSON> kup<PERSON>ilä", "block.minecraft.waxed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> kup<PERSON>ku", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON><PERSON>u leikattu kupari", "block.minecraft.waxed_cut_copper_slab": "Vahattu leikattu kupari<PERSON>atta", "block.minecraft.waxed_cut_copper_stairs": "Vahatut leikatut kupariportaat", "block.minecraft.waxed_exposed_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> ka<PERSON> kupari", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> kup<PERSON>", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> k<PERSON>", "block.minecraft.waxed_exposed_copper_door": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> k<PERSON>", "block.minecraft.waxed_exposed_copper_grate": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> k<PERSON>", "block.minecraft.waxed_exposed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> k<PERSON>", "block.minecraft.waxed_exposed_cut_copper": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>ut leikattu kupari", "block.minecraft.waxed_exposed_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>ut leikattu kupari<PERSON>a", "block.minecraft.waxed_exposed_cut_copper_stairs": "Vahatut al<PERSON>et leikatut kupariportaat", "block.minecraft.waxed_oxidized_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> hapettunut ka<PERSON>rettu kupari", "block.minecraft.waxed_oxidized_copper": "<PERSON><PERSON><PERSON><PERSON> hapet<PERSON>ut kupari", "block.minecraft.waxed_oxidized_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> hapet<PERSON>ut kup<PERSON>", "block.minecraft.waxed_oxidized_copper_door": "<PERSON><PERSON><PERSON><PERSON> hapet<PERSON> kup<PERSON>", "block.minecraft.waxed_oxidized_copper_grate": "Vahatt<PERSON> hapettunut kupariritilä", "block.minecraft.waxed_oxidized_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> hapettunut kupari<PERSON>ku", "block.minecraft.waxed_oxidized_cut_copper": "<PERSON><PERSON><PERSON>u hapettunut leikattu kupari", "block.minecraft.waxed_oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON>u hapettunut leikattu kupari<PERSON>atta", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Vahatut hapettuneet leikatut kupariportaat", "block.minecraft.waxed_weathered_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> rapautunut ka<PERSON><PERSON>tu kupari", "block.minecraft.waxed_weathered_copper": "<PERSON><PERSON><PERSON><PERSON> rap<PERSON> kupari", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> rap<PERSON> kup<PERSON>", "block.minecraft.waxed_weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON> rap<PERSON> kup<PERSON>", "block.minecraft.waxed_weathered_copper_grate": "<PERSON>ahattu rap<PERSON>ut kup<PERSON>ilä", "block.minecraft.waxed_weathered_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> rap<PERSON>ut kup<PERSON>ku", "block.minecraft.waxed_weathered_cut_copper": "<PERSON><PERSON><PERSON><PERSON> rapautunut leikattu kupari", "block.minecraft.waxed_weathered_cut_copper_slab": "<PERSON><PERSON><PERSON>u rapautunut leikattu kupari<PERSON>atta", "block.minecraft.waxed_weathered_cut_copper_stairs": "Vahatut rapautuneet leikatut kupariportaat", "block.minecraft.weathered_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON><PERSON> kupari", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON><PERSON> kup<PERSON>", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.weathered_copper_door": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "block.minecraft.weathered_copper_grate": "Rapautunut kup<PERSON>ilä", "block.minecraft.weathered_copper_trapdoor": "Rapaut<PERSON>ut kup<PERSON>", "block.minecraft.weathered_cut_copper": "Rapautunut leikattu kupari", "block.minecraft.weathered_cut_copper_slab": "Rapautunut leikattu kupari<PERSON>a", "block.minecraft.weathered_cut_copper_stairs": "Rapautuneet leikatut kupariportaat", "block.minecraft.weeping_vines": "Itkuköynnös", "block.minecraft.weeping_vines_plant": "Itkuköynnöskasvi", "block.minecraft.wet_sponge": "Märkä pesusieni", "block.minecraft.wheat": "Vehnä", "block.minecraft.white_banner": "Valkoinen viiri", "block.minecraft.white_bed": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.white_candle": "Val<PERSON><PERSON> kynttilä", "block.minecraft.white_candle_cake": "<PERSON><PERSON><PERSON> valk<PERSON><PERSON> k<PERSON>ttilällä", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON> matto", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.white_glazed_terracotta": "Valkoinen lasitettu terrakotta", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON> shulker-laatikko", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.white_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.white_terracotta": "Valkoinen terrakotta", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.white_wool": "Valkoinen villa", "block.minecraft.wildflowers": "Luonnonkukat", "block.minecraft.wither_rose": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "With<PERSON>-l<PERSON><PERSON><PERSON><PERSON> kallo", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON><PERSON><PERSON><PERSON> wither-lu<PERSON><PERSON>n kallo", "block.minecraft.yellow_banner": "Keltainen viiri", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON> s<PERSON>", "block.minecraft.yellow_candle": "<PERSON><PERSON><PERSON> kynttilä", "block.minecraft.yellow_candle_cake": "Ka<PERSON><PERSON> keltaisel<PERSON> kynttilällä", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON> matto", "block.minecraft.yellow_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "Keltainen lasitettu terrakotta", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON> shulker-la<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass_pane": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_terracotta": "Ke<PERSON>inen terrakotta", "block.minecraft.yellow_wool": "<PERSON><PERSON><PERSON> villa", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON> pä<PERSON>", "block.minecraft.zombie_wall_head": "R<PERSON>ustettu zombin pää", "book.byAuthor": "kirjoittanut %1$s", "book.edit.title": "<PERSON><PERSON><PERSON>", "book.editTitle": "<PERSON> k<PERSON>:", "book.finalizeButton": "Allekirjoita ja sulje", "book.finalizeWarning": "Huom! Allekirjoitettuasi kirjan sitä ei voi enää muokata.", "book.generation.0": "Alkuperäinen", "book.generation.1": "Alkuperäisen kopio", "book.generation.2": "<PERSON><PERSON> k<PERSON>io", "book.generation.3": "<PERSON><PERSON><PERSON>", "book.invalid.tag": "* <PERSON><PERSON><PERSON><PERSON>en k<PERSON> *", "book.pageIndicator": "Sivu %1$s/%2$s", "book.page_button.next": "<PERSON><PERSON><PERSON> sivu", "book.page_button.previous": "<PERSON><PERSON><PERSON> sivu", "book.sign.title": "<PERSON><PERSON><PERSON>", "book.sign.titlebox": "<PERSON><PERSON>", "book.signButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "book.view.title": "<PERSON><PERSON><PERSON>", "build.tooHigh": "Rakentamisen korkeusrajoitus on %s kuutiota", "chat.cannotSend": "Viestiä ei voida lähettää", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Klikkaa teleportataksesi", "chat.copy": "<PERSON><PERSON><PERSON>yd<PERSON>", "chat.copy.click": "Klikkaa kopioidaksesi leikepöydälle", "chat.deleted_marker": "<PERSON><PERSON><PERSON> on poistanut tämän keskusteluviestin.", "chat.disabled.chain_broken": "<PERSON><PERSON><PERSON><PERSON><PERSON> on poissa käytöstä, koska ketju on katkennut. Yritä yhdistää uudelleen.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON><PERSON><PERSON><PERSON> on poissa k<PERSON>ä, koska profiilisi julkinen avain on vanhentunut. Yritä yhdistää uudelleen.", "chat.disabled.invalid_command_signature": "Ko<PERSON><PERSON><PERSON> oli odottamattomia tai puuttuvia komentoargumenttien allekirjoituksia.", "chat.disabled.invalid_signature": "Virhellinen allekirjoitus keskustelussa. Yritä yhdistää uudelleen.", "chat.disabled.launcher": "Keskustelu on poistettu käytöstä käynnistysohjelmassa. Viestiä ei voida lähettää.", "chat.disabled.missingProfileKey": "Ke<PERSON><PERSON><PERSON>u on poissa k<PERSON>, koska profiilisi julkinen avain puuttuu. Yritä yhdistää uudelleen.", "chat.disabled.options": "Keskustelu on poistettu käytöstä asetuksissa.", "chat.disabled.out_of_order_chat": "Keskustelu vastaanotettiin epäjärjestyksessä. Muuttuiko järjestelmäsi aika?", "chat.disabled.profile": "Keskustelu ei ole sallittu tilin asetuksissa. Paina '%s' uudelleen saadaksesi lisätietoa.", "chat.disabled.profile.moreInfo": "Keskustelu ei ole sallittu tilin asetuksissa. Viestejä ei voida lähettää tai näyttää.", "chat.editBox": "keskustelu", "chat.filtered": "Palve<PERSON>en suodattama.", "chat.filtered_full": "<PERSON><PERSON><PERSON> on piilottanut viestisi joiltain pelaajilta.", "chat.link.confirm": "<PERSON><PERSON><PERSON><PERSON> varmasti avata se<PERSON> ve<PERSON>?", "chat.link.confirmTrusted": "Haluatko avata tämän linkin vai kopioida sen leikepöydälle?", "chat.link.open": "<PERSON><PERSON> se<PERSON>", "chat.link.warning": "<PERSON><PERSON><PERSON> koskaan avaa linkkejä ihmisiltä, joihin et luota!", "chat.queue": "[+%s rivi(ä) jonossa]", "chat.square_brackets": "[%s]", "chat.tag.error": "<PERSON><PERSON><PERSON> lä<PERSON><PERSON> vir<PERSON>llis<PERSON> viestin.", "chat.tag.modified": "Palvelimen muokkaama viesti. Alkuperäinen:", "chat.tag.not_secure": "Todentamaton viesti. Ilmoitusta ei voi luoda.", "chat.tag.system": "Palvelimen viesti. Ilmoitusta ei voi luoda.", "chat.tag.system_single_player": "Palvelimen viesti.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s on suorittanut haasteen %s", "chat.type.advancement.goal": "%s on saavuttanut tavoitteen %s", "chat.type.advancement.task": "%s on saavuttanut edistysaskeleen %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Viesti ryhmälle", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s sanoo %s", "chat.validation_error": "Keskustelun vahvistamisen virhe", "chat_screen.message": "Lähetettävä viesti: %s", "chat_screen.title": "Keskustelunäyttö", "chat_screen.usage": "Syötä viesti ja paina Enter lähettääksesi", "chunk.toast.checkLog": "Katso lokitiedot saada<PERSON>esi lisätietoja", "chunk.toast.loadFailure": "Sijainnissa %s sijaitsevan lohkon lataaminen epäonnistui", "chunk.toast.lowDiskSpace": "<PERSON><PERSON>a vähissä!", "chunk.toast.lowDiskSpace.description": "Ma<PERSON>maa ei ehkä voida tallentaa.", "chunk.toast.saveFailure": "Sijainnissa %s sijaitsevan lohkon tallentaminen epäonnistui", "clear.failed.multiple": "Yhtään esinettä ei löydetty %s pelaajalta", "clear.failed.single": "Yhtään esinettä ei löydetty pelaajalta %s", "color.minecraft.black": "<PERSON><PERSON>", "color.minecraft.blue": "<PERSON><PERSON>", "color.minecraft.brown": "R<PERSON><PERSON>", "color.minecraft.cyan": "<PERSON><PERSON><PERSON>", "color.minecraft.gray": "<PERSON><PERSON><PERSON>", "color.minecraft.green": "Vihreä", "color.minecraft.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.lime": "Limetinvihreä", "color.minecraft.magenta": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.orange": "Orans<PERSON>", "color.minecraft.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.purple": "<PERSON><PERSON>", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "<PERSON><PERSON><PERSON>", "color.minecraft.yellow": "<PERSON><PERSON><PERSON>", "command.context.here": "<--[TÄSSÄ]", "command.context.parse_error": "%s sijainnissa %s: %s", "command.exception": "Komentoa %s ei voitu jäsentää", "command.expected.separator": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>den <PERSON>, mutta j<PERSON><PERSON><PERSON><PERSON><PERSON> olevia tietoja l<PERSON>yi", "command.failed": "Odottamaton virhe tapahtui yritettäessä suorittaa komentoa", "command.forkLimit": "Asiayhteyksien enimmäismäärä (%s) saavutettu", "command.unknown.argument": "Väärä <PERSON>ti komennolle", "command.unknown.command": "Tuntematon tai keskeneräinen komento, tarkista alla oleva virhe", "commands.advancement.criterionNotFound": "Edistysaskel %1$s ei sisällä kriteeriä '%2$s'", "commands.advancement.grant.criterion.to.many.failure": "Edistysaskeleen %s kriteeriä '%s' ei voida täyttää pelaajalle %s koska se on jo täyt<PERSON>yt", "commands.advancement.grant.criterion.to.many.success": "<PERSON><PERSON>in k<PERSON>eri '%s' edistysaskeleesta %s %s pelaajalle", "commands.advancement.grant.criterion.to.one.failure": "Kriteeriä '%s' edistysaskeleesta %s ei voida täyttää pelaajalle %s koska se on jo täyttynyt", "commands.advancement.grant.criterion.to.one.success": "Anne<PERSON>in k<PERSON>eri '%s' edistysaskeleesta %s pelaajalle %s", "commands.advancement.grant.many.to.many.failure": "Ei voitu myöntää %s edistysaskelta %s pelaajalle koska heillä on jo ne", "commands.advancement.grant.many.to.many.success": "Annettiin %s edistysaskelta %s pelaajalle", "commands.advancement.grant.many.to.one.failure": "Ei voitu antaa %s edistysaskelta pelaajalle %s koska hänell<PERSON> on jo ne", "commands.advancement.grant.many.to.one.success": "Annettiin %s edistysaskelta pelaajalle %s", "commands.advancement.grant.one.to.many.failure": "Ei voitu antaa edistysaskelta %s %s pelaajalle koska heillä on se jo", "commands.advancement.grant.one.to.many.success": "<PERSON><PERSON>in edistys<PERSON>el %s %s pelaajalle", "commands.advancement.grant.one.to.one.failure": "Ei voitu antaa edistysaskelta %s pelaajalle %s koska hänellä on se jo", "commands.advancement.grant.one.to.one.success": "<PERSON><PERSON>in ed<PERSON>el %s pelaajalle %s", "commands.advancement.revoke.criterion.to.many.failure": "Kriteeriä '%s' ei voitu kumota edistysaskeleesta %s %s pelaajalta koska heillä ei ole sitä", "commands.advancement.revoke.criterion.to.many.success": "Kumottiin kriteeri '%s' edistysaskeleesta %s %s pelaajalta", "commands.advancement.revoke.criterion.to.one.failure": "Kriteeriä '%s' ei voitu kumota edistysaskeleesta %s pelaajalta %s koska hänellä ei ole sitä", "commands.advancement.revoke.criterion.to.one.success": "Kumottiin kriteeri '%s' edistysaskeleesta %s pelaajalta %s", "commands.advancement.revoke.many.to.many.failure": "%s edistysaskelta ei voitu kumota %s pelaajalta koska heillä ei ole niitä", "commands.advancement.revoke.many.to.many.success": "Kumottiin %s edistysaskelta %s pelaajalta", "commands.advancement.revoke.many.to.one.failure": "%s edistysaskelta ei voitu kumota pelaajalta %s koska hänellä ei ole niitä", "commands.advancement.revoke.many.to.one.success": "Kumottiin %s edistysaskelta pelaajalta %s", "commands.advancement.revoke.one.to.many.failure": "Edistysaskelta %s ei voitu kumota %s pelaajalta koska heillä ei ole sitä", "commands.advancement.revoke.one.to.many.success": "Kumottiin edistysaskel %s %s pelaajalta", "commands.advancement.revoke.one.to.one.failure": "Ei voitu kumota edistysaskelta %s pelaajalta %s koska hänellä ei ole sitä", "commands.advancement.revoke.one.to.one.success": "Kumottiin edistysaskel %s pelaajalta %s", "commands.attribute.base_value.get.success": "Oletusarvo kohteen %2$s ominaisuudelle %1$s on %3$s", "commands.attribute.base_value.reset.success": "Kohteen %2$s ominaisuuden %1$s pohja-arvo on nollattu oletukseksi %3$s", "commands.attribute.base_value.set.success": "Kohteen %2$s ominaisuuden %1$s oletusarvoksi on asetettu %3$s", "commands.attribute.failed.entity": "%s ei ole kelvollinen kohde tälle komennolle", "commands.attribute.failed.modifier_already_present": "Kohteen %3$s ominaisuudella %2$s on jo olemassa määrite %1$s", "commands.attribute.failed.no_attribute": "Kohteella %s ei ole ominaisuutta %s", "commands.attribute.failed.no_modifier": "Kohteen %2$s ominaisuudella %1$s ei ole määritettä %3$s", "commands.attribute.modifier.add.success": "Kohteen %3$s ominaisuudelle %2$s lisättiin määrite %1$s", "commands.attribute.modifier.remove.success": "Kohteen %3$s ominaisuudelta %2$s poistettiin määrite %1$s", "commands.attribute.modifier.value.get.success": "Kohteen %3$s ominaisuuden %2$s määritteen %1$s arvo on %4$s", "commands.attribute.value.get.success": "Kohteen %2$s ominaisuuden %1$s arvo on %3$s", "commands.ban.failed": "<PERSON><PERSON><PERSON>än ei muutettu. Tä<PERSON><PERSON> p<PERSON> on jo portti<PERSON>o", "commands.ban.success": "<PERSON><PERSON>in porttikielto pelaajalle %s: %s", "commands.banip.failed": "Mitään ei muutettu. Tällä IP-osoitteella on jo portti<PERSON>o", "commands.banip.info": "Tämä porttikielto vaikuttaa %s pelaajaan: %s", "commands.banip.invalid": "Virheellinen IP-osoite tai tuntematon pelaaja", "commands.banip.success": "Annettiin porttikielto IP-osoitteelle %s: %s", "commands.banlist.entry": "Pelaaja %s sai porttikiellon pelaajalta %s: %s", "commands.banlist.entry.unknown": "(Tuntematon)", "commands.banlist.list": "Löydettiin %s porttikielto(a):", "commands.banlist.none": "<PERSON>i yht<PERSON>än port<PERSON>", "commands.bossbar.create.failed": "Päävastus<PERSON>japalkki on jo olemassa ID:llä '%s'", "commands.bossbar.create.success": "Luotiin mukautettu päävastustajapalkki %s", "commands.bossbar.get.max": "Mukautetun päävastustajapalkin %s maksimi on %s", "commands.bossbar.get.players.none": "Mukautetulla päävastustajapalkilla %s ei ole yhtään pelaajaa onlinessa", "commands.bossbar.get.players.some": "Mukautettu päävastustajapalkki %s näkyy %s paikalla olevalle pelaajalle: %s", "commands.bossbar.get.value": "Mukautetun päävastustajapalkin %s arvo on %s", "commands.bossbar.get.visible.hidden": "Mukautettu päävastustajapalkki %s on piilotettu tällä hetkellä", "commands.bossbar.get.visible.visible": "Mukautettu päävastustajapalkki %s näkyy tällä hetkellä", "commands.bossbar.list.bars.none": "Ei aktiivisia mukautettuja päävastustajapalkkeja", "commands.bossbar.list.bars.some": "Löydettiin %s mukautettu(a) päävastustajapalkki(a): %s", "commands.bossbar.remove.success": "Poistettiin mukautettu päävastustajapalkki %s", "commands.bossbar.set.color.success": "Mukautetun päävastustajapalkin %s väri vaihtui", "commands.bossbar.set.color.unchanged": "Mit<PERSON>än ei muutettu. Tämä on jo päävastustajapal<PERSON> väri", "commands.bossbar.set.max.success": "Mukautetun päävastustajapalkin %s maksimi on nyt %s", "commands.bossbar.set.max.unchanged": "Mit<PERSON><PERSON>n ei muutettu. Tämä on jo päävastus<PERSON>ja<PERSON> maksimi", "commands.bossbar.set.name.success": "Mukautettu päävastustajapalkki %s nimettiin uudelleen", "commands.bossbar.set.name.unchanged": "Mit<PERSON><PERSON>n ei muutettu. Tämä on jo päävastusta<PERSON> nimi", "commands.bossbar.set.players.success.none": "Mukautettu päävastustajapalkki %s ei näy yhdelläkään pelaajalla", "commands.bossbar.set.players.success.some": "Mukautettu päävastustajapalkki %s näkyy nyt %s pelaajalle: %s", "commands.bossbar.set.players.unchanged": "Mitään ei muutettu. Nuo pelaajat ovat jo päävastustajapalkilla eikä kukaan voi lisätä tai poistaa", "commands.bossbar.set.style.success": "Mukautetun päävastustajapalkin %s tyyli vaihtui", "commands.bossbar.set.style.unchanged": "Mit<PERSON><PERSON>n ei muutettu. Tämä on jo päävastustaja<PERSON> tyyli", "commands.bossbar.set.value.success": "Mukautetun päävastustajapalkin %s arvo on nyt %s", "commands.bossbar.set.value.unchanged": "Mit<PERSON><PERSON>n ei muutettu. Tämä on jo päävastustajapal<PERSON> arvo", "commands.bossbar.set.visibility.unchanged.hidden": "Mitään ei muutettu. Päävastustajapalkki on jo pii<PERSON>ettu", "commands.bossbar.set.visibility.unchanged.visible": "Mitään ei muutettu. Päävastustajapalkki on jo näkyvillä", "commands.bossbar.set.visible.success.hidden": "Mukautettu päävastustajapalkki %s on nyt piilotettu", "commands.bossbar.set.visible.success.visible": "Mukautettu päävastustajapalkki %s on nyt näkyvissä", "commands.bossbar.unknown": "Päävastustajapalkkia ei ole olemassa ID:llä '%s'", "commands.clear.success.multiple": "Poistettiin %s esine(ttä) %s pelaajalta", "commands.clear.success.single": "Poistettiin %s esine(ttä) pelaajalta %s", "commands.clear.test.multiple": "Löydettiin %s vastaava(a) esine(ttä) %s pelaajalta", "commands.clear.test.single": "Löydettiin %s vastaava(a) esine(ttä) pelaajalta %s", "commands.clone.failed": "Kuutioita ei kloonattu", "commands.clone.overlap": "Lähde- ja kohdea<PERSON>et eivät saa mennä päällekkäin", "commands.clone.success": "Onnistuneesti kloonattu %s kuutio(ta)", "commands.clone.toobig": "<PERSON>ian monta kuutiota määritetyllä alueella (maksimi %s, määritelty %s)", "commands.damage.invulnerable": "<PERSON><PERSON><PERSON> on ha<PERSON><PERSON><PERSON>aton annet<PERSON>e v<PERSON>nkotyy<PERSON>", "commands.damage.success": "Aiheutettiin %s v<PERSON><PERSON>a k<PERSON>lle %s", "commands.data.block.get": "%s kuutiolla %s, %s, %s on skaalauskertoimen %s jälkeen %s", "commands.data.block.invalid": "Ko<PERSON><PERSON><PERSON>utio ei ole kuutio-objekti", "commands.data.block.modified": "Muutettu kuutiodataa sijainnista %s, %s, %s", "commands.data.block.query": "Kuutiolla %s, %s, %s on seuraava data: %s", "commands.data.entity.get": "%s oliolla %s on skaalauskertoimen %s jälkeen %s", "commands.data.entity.invalid": "<PERSON><PERSON>ajadataa ei voi muuttaa", "commands.data.entity.modified": "Muutettu kohteen %s dataa", "commands.data.entity.query": "Kohteella %s on seuraava data: %s", "commands.data.get.invalid": "Ei voida saada %s; vain numeraaliset tunnisteet ovat sallittuja", "commands.data.get.multiple": "Tämä argumentti hyväksyy yhden NBT-arvon", "commands.data.get.unknown": "Ei voida saada %s; tunnist<PERSON> ei ole olemassa", "commands.data.merge.failed": "Mitään ei muutettu. Määritetyillä ominaisuuksilla on jo nämä arvot", "commands.data.modify.expected_list": "Odotettiin listaa, saatiin: %s", "commands.data.modify.expected_object": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, sa<PERSON>in: %s", "commands.data.modify.expected_value": "Odotettiin arvoa, saatiin: %s", "commands.data.modify.invalid_index": "<PERSON><PERSON><PERSON><PERSON><PERSON> listain<PERSON><PERSON>: %s", "commands.data.modify.invalid_substring": "Virheelliset osamerkkijonoindeksit: %s - %s", "commands.data.storage.get": "%s muistissa %s on skaalauskertoimen %s jälkeen %s", "commands.data.storage.modified": "Muistia muokattiin %s", "commands.data.storage.query": "Muisti %s sisältää: %s", "commands.datapack.create.already_exists": "<PERSON><PERSON> '%s' on jo o<PERSON><PERSON>a", "commands.datapack.create.invalid_full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> uuden paketin nimi '%s'", "commands.datapack.create.invalid_name": "Virheellisiä merkkejä uudessa paketin nimessä '%s'", "commands.datapack.create.io_failure": "<PERSON>i voida luoda pakettia <PERSON> '%s', katso lo<PERSON>t", "commands.datapack.create.metadata_encode_failure": "Metadatan enkoodaaminen paketille nimellä '%s' epäonnistui: %s", "commands.datapack.create.success": "Luotiin uusi tyhjä paketti nimell<PERSON> '%s'", "commands.datapack.disable.failed": "Paketti ’%s’ ei ole k<PERSON>ä!", "commands.datapack.disable.failed.feature": "Pakettia '%s' ei voida poistaa käytöstä, sillä se on osa käytössä olevaa kokonaisuutta!", "commands.datapack.enable.failed": "<PERSON>etti ’%s’ on jo k<PERSON>!", "commands.datapack.enable.failed.no_flags": "Pakettia '%s' ei voi ottaa k<PERSON>öö<PERSON>, sillä vaaditut tunnistetiedot eivät ole käytössä tässä maailmassa: %s!", "commands.datapack.list.available.none": "Datapaketteja ei ole enempää käytettävissä", "commands.datapack.list.available.success": "Käytettävissäsi on %s datapaketti(a): %s", "commands.datapack.list.enabled.none": "Datapaketteja ei ole k<PERSON>ytössä", "commands.datapack.list.enabled.success": "Käytössä on %s datapaketti(a): %s", "commands.datapack.modify.disable": "Poistetaan datapakettia %s käytöstä", "commands.datapack.modify.enable": "Otetaan datapakettia %s käyttöön", "commands.datapack.unknown": "Tuntematon datapaketti '%s'", "commands.debug.alreadyRunning": "Pykäläprofilointi on jo aloitettu", "commands.debug.function.noRecursion": "Toiminnon sisältä jäljittäminen ei onnistu", "commands.debug.function.noReturnRun": "Jäljitystä ei voi käyttää komennolle /return run", "commands.debug.function.success.multiple": "Jäljitettiin %s komento(a) %s toiminnosta tiedostoon %s", "commands.debug.function.success.single": "Jäljitettiin %s komento(a) toiminnosta '%s' tiedostoon %s", "commands.debug.function.traceFailed": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>äminen epäonnistui", "commands.debug.notRunning": "Pykäläprofilointia ei ole aloitettu", "commands.debug.started": "Pykäläprofilointi aloitettu", "commands.debug.stopped": "Pysäytettiin pykäläprofilointi %s sekunnin ja %s pykälän jälkeen (%s pykälää sekunnissa)", "commands.defaultgamemode.success": "Oletuspelitila on nyt %s", "commands.deop.failed": "Mitään ei muutettu. Pelaaja ei ole operaattori", "commands.deop.success": "Poistettiin pelaajalta %s palvelimen operaattorin oikeudet", "commands.dialog.clear.multiple": "Poistettiin dialogi %s pelaajalta", "commands.dialog.clear.single": "Poistettiin dialogi pelaajalta %s", "commands.dialog.show.multiple": "Näytettiin dialogi %s pelaajalle", "commands.dialog.show.single": "Näytettiin dialogi pelaajalle %s", "commands.difficulty.failure": "Vaikeustaso ei muuttunut; se on jo asetettu asteelle %s", "commands.difficulty.query": "Vaikeustaso on %s", "commands.difficulty.success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> (%s)", "commands.drop.no_held_items": "Ko<PERSON><PERSON> ei voi pitää tavaroita", "commands.drop.no_loot_table": "Kohteella %s ei ole saalisluetteloa", "commands.drop.no_loot_table.block": "Kuutiolla %s ei ole saalisluetteloa", "commands.drop.success.multiple": "Pudotettiin %s tavaraa", "commands.drop.success.multiple_with_table": "Pudotettiin %s esinettä saalisluettelosta %s", "commands.drop.success.single": "Pudotettu %s %s", "commands.drop.success.single_with_table": "Pudotettiin %s %s saalisluettelosta %s", "commands.effect.clear.everything.failed": "Kohteella ei ole efektejä poistettavaksi", "commands.effect.clear.everything.success.multiple": "Po<PERSON><PERSON><PERSON> j<PERSON> vaikutus %s kohteelta", "commands.effect.clear.everything.success.single": "<PERSON><PERSON><PERSON><PERSON> joka<PERSON> vaikutus koh<PERSON>lta %s", "commands.effect.clear.specific.failed": "Ko<PERSON>eella ei ole pyydettyä vaikutusta", "commands.effect.clear.specific.success.multiple": "Poistettiin vaikutus %s %s kohteelta", "commands.effect.clear.specific.success.single": "Poistettiin vaikutus %s kohteelta %s", "commands.effect.give.failed": "Vaikutusta ei pystytty k<PERSON>ämää<PERSON> (kohde on joko immuuni vaikutuksille, tai sill<PERSON> on jotain vahvempaa)", "commands.effect.give.success.multiple": "Lisättiin vaikutus %s %s kohteelle", "commands.effect.give.success.single": "Lisättiin vaikutus %s kohteelle %s", "commands.enchant.failed": "Mit<PERSON>än ei muutettu. <PERSON><PERSON> k<PERSON> ei joko ole esinettä kädessään tai lumousta ei voitu lisätä", "commands.enchant.failed.entity": "%s ei ole kelvollinen kohde tälle komennolle", "commands.enchant.failed.incompatible": "%s ei tue tuota lumousta", "commands.enchant.failed.itemless": "%s ei pitele yhtään esinettä", "commands.enchant.failed.level": "%s on suurempi kuin lumouksen maksimitaso %s", "commands.enchant.success.multiple": "Lisättiin lumous %s %s kohteelle", "commands.enchant.success.single": "Lisättiin lumous %s pelaajan %s esineeseen", "commands.execute.blocks.toobig": "Liikaa kuutioita määritellyllä alueella (maksimi %s, määritelty %s)", "commands.execute.conditional.fail": "<PERSON>i e<PERSON>", "commands.execute.conditional.fail_count": "Testi e<PERSON>, määrä: %s", "commands.execute.conditional.pass": "<PERSON><PERSON>", "commands.execute.conditional.pass_count": "<PERSON>i l<PERSON>, määrä: %s", "commands.execute.function.instantiationFailure": "Funktion %s suorittaminen epäonnistui: %s", "commands.experience.add.levels.success.multiple": "Annettiin %s kokemustasoa %s pelaajalle", "commands.experience.add.levels.success.single": "Annettiin %s kokemustasoa p<PERSON>lle %s", "commands.experience.add.points.success.multiple": "Annettiin %s kokemuspistettä %s pelaajalle", "commands.experience.add.points.success.single": "Annettiin %s kokemuspistettä pelaajalle %s", "commands.experience.query.levels": "Pelaajalla %s on %s kokemustasoa", "commands.experience.query.points": "Pelaajalla %s on %s kokemuspistettä", "commands.experience.set.levels.success.multiple": "Asetettiin %s kokemustasoa %s pelaajalle", "commands.experience.set.levels.success.single": "Asetettiin %s kokemustasoa pelaajalle %s", "commands.experience.set.points.invalid": "Kokemuspisteitä ei voi asettaa pelaajan nykyisen tason maksimipistemäärän yläpu<PERSON>lle", "commands.experience.set.points.success.multiple": "Asetettiin %s kokemuspistettä %s pelaajalle", "commands.experience.set.points.success.single": "Asetettiin %s kokemuspistettä pelaajalle %s", "commands.fill.failed": "<PERSON><PERSON><PERSON><PERSON> ei t<PERSON>", "commands.fill.success": "Onnistuneesti täytetty %s kuutio(ta)", "commands.fill.toobig": "Liikaa kuutioita määritellyllä alueella (maksimi %s, määritelty %s)", "commands.fillbiome.success": "Biomi asetettu välille %s, %s, %s ja %s, %s, %s", "commands.fillbiome.success.count": "%s biomi(a) asetettu välille %s, %s, %s ja %s, %s, %s", "commands.fillbiome.toobig": "<PERSON>ian monta kuutiota määritetyssä tilassa (maksimi %s, määritelty %s)", "commands.forceload.added.failure": "Yhtään lohkoa ei merkitty pak<PERSON>latautuvaksi", "commands.forceload.added.multiple": "Merkittiin %s lohkoa %s:ssä %s:stä %s:n pakkolatautuviksi", "commands.forceload.added.none": "Pakkolatautuvia lohkoja ei löydetty %s:ssä", "commands.forceload.added.single": "Merkittiin lohko %s %s:ssä pakkolatautuvaksi", "commands.forceload.list.multiple": "%s pakkolatautuvaa lohkoa löydetty %s %s:ssä", "commands.forceload.list.single": "Pakkolatautuva lohko löydetty %s %s:ssä", "commands.forceload.query.failure": "Lohko %s %s:ssä ei ole merkitty pakkolatautuvaksi", "commands.forceload.query.success": "Lohko %s %s:ssä on merkitty pakkolatautuvaksi", "commands.forceload.removed.all": "Poistettiin merkintä kaikista pakkolatautuvista lohkoista %s:ssä", "commands.forceload.removed.failure": "Lohkoja ei poistettu pakkolatautumistilasta", "commands.forceload.removed.multiple": "Poistettiin merkinnät %s lohkosta %s:ssä %s:stä %s:n pakkolatautuvasta", "commands.forceload.removed.single": "Poistettiin lohkon %s merkintä %s:ssä pakkolatautuvasta", "commands.forceload.toobig": "<PERSON><PERSON> monta lohkoa määritetyllä alueella (maksimi %s, määritelty %s)", "commands.function.error.argument_not_compound": "Virheellinen argumenttityyppi: %s, odotettiin tyyppiä Compound", "commands.function.error.missing_argument": "Puuttuva argumentti %2$s funktiossa %1$s", "commands.function.error.missing_arguments": "Puuttuvia argumentteja funktiossa %s", "commands.function.error.parse": "Su<PERSON><PERSON><PERSON>a ma<PERSON> %s: Komento '%s' aih<PERSON>tti virheen: %s", "commands.function.instantiationFailure": "Funktion %s suorittaminen epäonnistui: %s", "commands.function.result": "Funktio %s palautti arvon %s", "commands.function.scheduled.multiple": "Käynnissä olevat toiminnot %s", "commands.function.scheduled.no_functions": "Nimellä %s ei löydetty yhtään funktiota", "commands.function.scheduled.single": "Käynnissä oleva toiminto %s", "commands.function.success.multiple": "Suoritettiin %s komento(a) %s toiminnosta", "commands.function.success.multiple.result": "Suoritettiin %s toimintoa", "commands.function.success.single": "Suoritettiin %s komento(a) toiminnosta '%s'", "commands.function.success.single.result": "Funktio '%2$s' palautti arvon %1$s", "commands.gamemode.success.other": "Pelaajan %s pelitilaksi asetettiin %s", "commands.gamemode.success.self": "Omaksi pelitilaksi asetettiin %s", "commands.gamerule.query": "Pelisääntö %s on tällä hetkellä asetettu tilaan: %s", "commands.gamerule.set": "Pelisääntö %s on nyt asetettu tilaan: %s", "commands.give.failed.toomanyitems": "Voidaan antaa enintään %s kappaletta esinettä %s", "commands.give.success.multiple": "Annettiin %s %s %s pelaajalle", "commands.give.success.single": "Annettiin %s %s pelaajalle %s", "commands.help.failed": "Tuntematon komento tai puutteelliset luvat", "commands.item.block.set.success": "Lokero kohteessa %s, %s, %s korvat<PERSON><PERSON> se<PERSON>: %s", "commands.item.entity.set.success.multiple": "Lokero %s kohteessa korvattiin esineellä %s", "commands.item.entity.set.success.single": "Lokero kohteessa %s korvattiin esineellä %s", "commands.item.source.no_such_slot": "Lähteellä ei ole lokeroa %s", "commands.item.source.not_a_container": "Lähdesijainti %s, %s, %s ei ole säiliö", "commands.item.target.no_changed.known_item": "Mikään kohteista ei hyväksynyt esinettä %s lokeroon %s", "commands.item.target.no_changes": "Mikään kohteista ei hyväksynyt esinettä lokeroon %s", "commands.item.target.no_such_slot": "Kohteella ei ole lokeroa %s", "commands.item.target.not_a_container": "Kohdesijainti %s, %s, %s ei ole säiliö", "commands.jfr.dump.failed": "JFR-tietojen siirtäminen epäonnistui: %s", "commands.jfr.start.failed": "JFR-profiloinnin aloittaminen epäonnistui", "commands.jfr.started": "JFR-profilointi al<PERSON>", "commands.jfr.stopped": "JFR-profilointi pysäytettiin ja tiedot siirrettiin sijaintiin %s", "commands.kick.owner.failed": "Palvelimen omistajaa ei voida potkia LAN-pelistä", "commands.kick.singleplayer.failed": "Pelaajia ei voida potkia offline-yksinpelistä", "commands.kick.success": "Potkaistiin %s: %s", "commands.kill.success.multiple": "Tapettiin %s oliota", "commands.kill.success.single": "%s tapettiin", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Tällä hetkellä %s %s pelaajan maksimimäärästä on paikalla: %s", "commands.locate.biome.not_found": "Biomia \"%s\" ei löytynyt lähialueelta", "commands.locate.biome.success": "Lähin %s sijaitsee koordinaateissa %s (%s kuution päässä)", "commands.locate.poi.not_found": "Kiinnostavaa kohdetta \"%s\" ei löytynyt lähialueelta", "commands.locate.poi.success": "Lähin %s sijaitsee koordinaateissa %s (%s kuution päässä)", "commands.locate.structure.invalid": "Rakennelmaa \"%s\" ei ole olemassa", "commands.locate.structure.not_found": "Ei löydetty rakennelmaa \"%s\" läheltä", "commands.locate.structure.success": "Lähin %s sijaitsee koordinaateissa %s (%s kuution päässä)", "commands.message.display.incoming": "%s kuiskasi sinulle: %s", "commands.message.display.outgoing": "Kuiskasit pelaajalle %s: %s", "commands.op.failed": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. Pelaaja on jo operaattori", "commands.op.success": "Tehtiin pelaajasta %s palvelimen operaattori", "commands.pardon.failed": "Mitään ei muutettu. Pelaajalla ei ole portti<PERSON>ltoa", "commands.pardon.success": "Poistettiin porttikielto pelaajalta %s", "commands.pardonip.failed": "Mitään ei muutettu. Tällä IP-osoitteella ei ole porttikieltoa", "commands.pardonip.invalid": "Virheellinen IP-osoite", "commands.pardonip.success": "Poistettiin IP-osoitteen %s porttikielto", "commands.particle.failed": "Partikkeli ei näkynyt kenellekään", "commands.particle.success": "Näytetään partikkeli %s", "commands.perf.alreadyRunning": "<PERSON><PERSON><PERSON><PERSON><PERSON> profilointi on jo k<PERSON>ynniss<PERSON>", "commands.perf.notRunning": "Suorityskyvyn profilointi ei ole k<PERSON>ynn<PERSON>ä", "commands.perf.reportFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> luominen ep<PERSON>", "commands.perf.reportSaved": "Virheenkorjaus<PERSON>ort<PERSON> luotu kohteeseen %s", "commands.perf.started": "Aloitettiin 10 sekunnin suorituskyvyn profilointi (käytä komentoa '/perf stop' pysäyt<PERSON>ää<PERSON><PERSON>)", "commands.perf.stopped": "Pysäytettiin suorituskyvyn profilointi %s sekunnin ja %s pykälän jälkeen (%s pykälää sekunnissa)", "commands.place.feature.failed": "Ominaisuuden asettaminen epäon<PERSON>ui", "commands.place.feature.invalid": "Ominaisuutta tyypillä \"%s\" ei ole olemassa", "commands.place.feature.success": "Asetettiin \"%s\" sijaintiin %s, %s, %s", "commands.place.jigsaw.failed": "Palapelikuution luominen epäonnistui", "commands.place.jigsaw.invalid": "Malliryhmää tyypillä \"%s\" ei ole olemassa", "commands.place.jigsaw.success": "Luotiin palapelikuutio sijaintiin %s, %s, %s", "commands.place.structure.failed": "Rakennelman as<PERSON><PERSON><PERSON> e<PERSON>", "commands.place.structure.invalid": "Rakennelmaa \"%s\" ei ole olemassa", "commands.place.structure.success": "Luotiin rakennelma \"%s\" sijaintiin %s, %s, %s", "commands.place.template.failed": "<PERSON><PERSON> as<PERSON><PERSON><PERSON> e<PERSON>", "commands.place.template.invalid": "<PERSON>ia tunnuksella \"%s\" ei ole olemassa", "commands.place.template.success": "Ladattiin malli \"%s\" sijaintiin %s, %s, %s", "commands.playsound.failed": "<PERSON><PERSON><PERSON> on liian kaukana ku<PERSON>i", "commands.playsound.success.multiple": "Soitettiin ääni %s %s pelaajalle", "commands.playsound.success.single": "Soitettiin ääni %s pelaajalle %s", "commands.publish.alreadyPublished": "Moninpeli on jo isännöity portin %s kautta", "commands.publish.failed": "Ma<PERSON><PERSON> jaka<PERSON>n lähiverkkoon epäonnistui", "commands.publish.started": "Paikallinen peli luotu portilla %s", "commands.publish.success": "Moninpeli on nyt isännöity portin %s kautta", "commands.random.error.range_too_large": "Satunnainen arvo voi olla enintään 2147483646", "commands.random.error.range_too_small": "Satunnaisen arvon tulee olla vähintään 2", "commands.random.reset.all.success": "Nollattiin %s satunnaista sarjaa", "commands.random.reset.success": "<PERSON><PERSON><PERSON><PERSON> satunnainen sarja %s", "commands.random.roll": "%s pyöritti luvun %s (väliltä %s–%s)", "commands.random.sample.success": "Satunnainen arvo: %s", "commands.recipe.give.failed": "Uusia reseptejä ei opittu", "commands.recipe.give.success.multiple": "Avattiin %s resepti(ä) %s pelaajalle", "commands.recipe.give.success.single": "Avattiin %s resepti(ä) pelaajalle %s", "commands.recipe.take.failed": "Reseptejä ei pystytty unohtamaan", "commands.recipe.take.success.multiple": "Otettiin pois %s resepti(ä) %s pelaajalta", "commands.recipe.take.success.single": "Otettiin pois %s resepti(ä) pelaajalta %s", "commands.reload.failure": "Uudelleenlataus epäonnistui; <PERSON><PERSON> tiedot säilytetään", "commands.reload.success": "Uudelleenladataan!", "commands.ride.already_riding": "%s <PERSON><PERSON>aa jo koh<PERSON>lla %s", "commands.ride.dismount.success": "%s lopetti <PERSON><PERSON><PERSON> kohteella %s", "commands.ride.mount.failure.cant_ride_players": "Pelaajilla ei voi ratsastaa", "commands.ride.mount.failure.generic": "%s ei voi ratsastaa kohteella %s", "commands.ride.mount.failure.loop": "<PERSON><PERSON><PERSON><PERSON> ei voi as<PERSON><PERSON> rats<PERSON>an itsellään tai rats<PERSON>an", "commands.ride.mount.failure.wrong_dimension": "<PERSON>i voi <PERSON><PERSON>aa koh<PERSON>, joka on to<PERSON><PERSON> ul<PERSON>", "commands.ride.mount.success": "%s alkoi ratsastaa kohteella %s", "commands.ride.not_riding": "%s ei ratsasta mill<PERSON><PERSON>n koh<PERSON>lla", "commands.rotate.success": "Käännetty %s", "commands.save.alreadyOff": "Tallentaminen on jo poissa k<PERSON>ä", "commands.save.alreadyOn": "Tallentaminen on jo k<PERSON>ä", "commands.save.disabled": "Automaattinen tallennus on nyt poissa käytöstä", "commands.save.enabled": "Automaattinen tallennus on nyt käytössä", "commands.save.failed": "<PERSON><PERSON>ä ei pystytä tall<PERSON> (onko levylläsi tarpeeksi tilaa?)", "commands.save.saving": "Tallennetaan peliä (tämä voi viedä hetken!)", "commands.save.success": "<PERSON><PERSON>", "commands.schedule.cleared.failure": "Ei aikatauluja tunnuksella %s", "commands.schedule.cleared.success": "Poistettiin %s aikataulu(a) tunnuksella %s", "commands.schedule.created.function": "A<PERSON><PERSON><PERSON><PERSON> toim<PERSON> '%s' %s pykälään peliajassa %s", "commands.schedule.created.tag": "Ajoite<PERSON>in tunniste '%s' %s pykälään peliajassa %s", "commands.schedule.macro": "Et voi ajastaa makroa", "commands.schedule.same_tick": "Nykyistä pykälää ei voida ajoittaa", "commands.scoreboard.objectives.add.duplicate": "Tavoite on jo olemassa tuolla ni<PERSON>", "commands.scoreboard.objectives.add.success": "Luotiin uusi tavoite %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Mit<PERSON>än ei muutettu. Tämä näyttölokero on jo tyhjä", "commands.scoreboard.objectives.display.alreadySet": "Mitään ei muutettu. Tämä näyttölokero näyttää jo tavoitetta", "commands.scoreboard.objectives.display.cleared": "Poistettiin kaikki tavoitteet näyttölokerosta %s", "commands.scoreboard.objectives.display.set": "Asetettiin näyttölokero %s näyttämään tavoite %s", "commands.scoreboard.objectives.list.empty": "Pistetaulukossa ei ole tavoitteita", "commands.scoreboard.objectives.list.success": "Pistetaulukossa on %s tavoite(tta): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Näytön automaattipäivitys kytketty pois päältä tavoitteelle %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Näytön automaattipäivitys kytketty päälle tavoitteelle %s", "commands.scoreboard.objectives.modify.displayname": "Vaihdettiin tavoitteen %s näyttönimeksi %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Tavoitteen %s oletusnumeromuotoilu tyhjennetty", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Tavoitteen %s oletusnumeromuotoilu muutettu", "commands.scoreboard.objectives.modify.rendertype": "Vaihdettiin tavoitteen %s renderöintityyppi", "commands.scoreboard.objectives.remove.success": "Poistettiin tavoite %s", "commands.scoreboard.players.add.success.multiple": "Lisättiin %s tavoitteeseen %s %s kohteelle", "commands.scoreboard.players.add.success.single": "Lisättiin %s tavoitteeseen %s kohteelle %s (on nyt %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Tyhjennettiin %s kohteen näyttönimi tavoitteessa %s", "commands.scoreboard.players.display.name.clear.success.single": "Tyhjennettiin k<PERSON> %s näyttönimi tavoitteessa %s", "commands.scoreboard.players.display.name.set.success.multiple": "Vaihdettiin %2$s kohteen näyttönimeksi %1$s tavoitteessa %3$s", "commands.scoreboard.players.display.name.set.success.single": "<PERSON>ai<PERSON><PERSON><PERSON><PERSON> k<PERSON> %2$s näyttönimeksi %1$s tavoitteessa %3$s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Tyhjennettiin %s kohteen numeromuotoilu tavoitteessa %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Tyhjenne<PERSON>in koh<PERSON> %s numeromuotoilu tavoitteessa %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Vaihdettiin %s kohteen numeromuotoilua tavoitteessa %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> %s numeromuotoilua tavoitteessa %s", "commands.scoreboard.players.enable.failed": "Mit<PERSON>än ei muutettu. Tämä laukaisin on jo käyt<PERSON>ssä", "commands.scoreboard.players.enable.invalid": "K<PERSON><PERSON><PERSON>ö<PERSON><PERSON><PERSON> toimii vain laukaisutavoitteisiin", "commands.scoreboard.players.enable.success.multiple": "Sallittu laukaisu %s %s kohteelle", "commands.scoreboard.players.enable.success.single": "Käynnistettiin laukaisin %s pelaajalle %s", "commands.scoreboard.players.get.null": "Ei voida saada arvoa %s kohteelle %s; mitään ei ole asetettu", "commands.scoreboard.players.get.success": "Pelaajalla %s on %s %s", "commands.scoreboard.players.list.empty": "Tä<PERSON><PERSON> hetkellä ei ole seurattuja kohteita", "commands.scoreboard.players.list.entity.empty": "%s ei ole tuloksia näytettävänä", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "Kohteella %s on %s tulos(ta):", "commands.scoreboard.players.list.success": "Seurataan %s kohdetta: %s", "commands.scoreboard.players.operation.success.multiple": "Päivitettiin %s %s kohteelle", "commands.scoreboard.players.operation.success.single": "Asetettiin %3$s tavoitteeseen %1$s kohteelle %2$s", "commands.scoreboard.players.remove.success.multiple": "Poistettiin %s tavoitteesta %s %s kohteelta", "commands.scoreboard.players.remove.success.single": "Poistettiin %s tavoitteesta %s kohteelta %s (on nyt %s)", "commands.scoreboard.players.reset.all.multiple": "<PERSON>lla<PERSON>in kaikki pisteet %s kohteelta", "commands.scoreboard.players.reset.all.single": "<PERSON>lla<PERSON>in kaikki pisteet kohteelta %s", "commands.scoreboard.players.reset.specific.multiple": "Nollattiin %s %s kohteelta", "commands.scoreboard.players.reset.specific.single": "Nollattiin %s kohteelta %s", "commands.scoreboard.players.set.success.multiple": "Asetettiin %3$s tavoitteeseen %1$s %2$s kohteelle", "commands.scoreboard.players.set.success.single": "Asetettiin %3$s tavoitteeseen %1$s kohteelle %2$s", "commands.seed.success": "Lähtöarvo: %s", "commands.setblock.failed": "Kuutiota ei voitu asettaa", "commands.setblock.success": "<PERSON>ai<PERSON><PERSON><PERSON>in ku<PERSON>o kohdassa %s, %s, %s", "commands.setidletimeout.success": "<PERSON><PERSON><PERSON><PERSON> to<PERSON><PERSON><PERSON><PERSON> on nyt %s minuutti(a)", "commands.setidletimeout.success.disabled": "<PERSON><PERSON><PERSON><PERSON> to<PERSON><PERSON><PERSON><PERSON><PERSON> on nyt poissa käytöstä", "commands.setworldspawn.failure.not_overworld": "<PERSON><PERSON><PERSON> al<PERSON><PERSON><PERSON><PERSON>aan as<PERSON><PERSON> vain y<PERSON>", "commands.setworldspawn.success": "Maailman syntymäpisteeksi asetettiin %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Aseta syntymäpiste kohtaan %s, %s, %s [%s] paikkaan %s %s pelaajalle", "commands.spawnpoint.success.single": "Asetettiin syntymäpiste kohtaan %s, %s, %s [%s] paikkaan %s pelaajalle %s", "commands.spectate.not_spectator": "%s ei ole ka<PERSON>a", "commands.spectate.self": "Et voi katsoa itseäsi", "commands.spectate.success.started": "Katsot nyt kohdetta %s", "commands.spectate.success.stopped": "Et enää katso kohdetta", "commands.spreadplayers.failed.entities": "Ei voitu levittää %s kohdetta koordinaatteihin %s, %s (liian monta kohdetta tilaan – kokeile käyttää levityksenä korkeintaan arvoa %s)", "commands.spreadplayers.failed.invalid.height": "Enimmäiskorkeus %s ei kelpaa; odotettiin korkeamp<PERSON> kuin vähimmäiskorkeus %s", "commands.spreadplayers.failed.teams": "Ei voitu levittää %s joukkuetta koordinaatteihin %s, %s (liian monta kohdetta tilaan – kokeile käyttää levityksenä korkeintaan arvoa %s)", "commands.spreadplayers.success.entities": "Levitettiin %s pelaaja(a) koordinaattipisteen %s, %s ympärille keskimäärin %s kuution välein", "commands.spreadplayers.success.teams": "Levitettiin %s joukkue(tta) koordinaattipisteen %s, %s ympärille keskimäärin %s ku<PERSON> välein", "commands.stop.stopping": "Pysäytetää<PERSON> palvel<PERSON>a", "commands.stopsound.success.source.any": "Pys<PERSON><PERSON><PERSON>in kaikki '%s' äänet", "commands.stopsound.success.source.sound": "Pysäytetiin ääni '%s' lähteestä '%s'", "commands.stopsound.success.sourceless.any": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "commands.stopsound.success.sourceless.sound": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '%s'", "commands.summon.failed": "<PERSON><PERSON><PERSON><PERSON> ei pysty<PERSON> luoda", "commands.summon.failed.uuid": "Kohdetta ei voida kutsua samannimisen UUID:n takia", "commands.summon.invalidPosition": "Sijainti ei kelpaa kutsumiseen", "commands.summon.success": "Kutsuttiin uusi %s", "commands.tag.add.failed": "<PERSON><PERSON><PERSON><PERSON> on jo kyseinen tunniste tai liian monta tunnistetta", "commands.tag.add.success.multiple": "Lisättiin tunniste '%s' %s oliolle", "commands.tag.add.success.single": "Lisättiin tunniste '%s' kohteelle %s", "commands.tag.list.multiple.empty": "%s oliolla ei ole yhtään tunnistetta", "commands.tag.list.multiple.success": "%s oliolla on yhteensä %s tunnistetta: %s", "commands.tag.list.single.empty": "Kohteella %s ei ole yhtään tunnistetta", "commands.tag.list.single.success": "Kohteella %s on %s tunnistetta: %s", "commands.tag.remove.failed": "Kohteella ei ole tätä tunnistetta", "commands.tag.remove.success.multiple": "Poistettiin tunniste '%s' %s oliolta", "commands.tag.remove.success.single": "Poistettiin tunniste '%s' kohteelta %s", "commands.team.add.duplicate": "<PERSON><PERSON><PERSON><PERSON> on jo olemassa tuolla ni<PERSON>", "commands.team.add.success": "Luotiin joukkue %s", "commands.team.empty.success": "Poistettiin %s pelaaja(a) joukkueesta %s", "commands.team.empty.unchanged": "Mit<PERSON><PERSON>n ei muutettu. Tämä joukkue on jo tyhjä", "commands.team.join.success.multiple": "Lisättiin %s jäsentä joukkueeseen %s", "commands.team.join.success.single": "%s lisät<PERSON>in j<PERSON>en %s", "commands.team.leave.success.multiple": "Poistettiin %s jäsentä mistä tahansa jouk<PERSON>ta", "commands.team.leave.success.single": "Poistettiin %s <PERSON><PERSON> ta<PERSON> j<PERSON>", "commands.team.list.members.empty": "Joukkueessa %s ei ole yhtään jäsentä", "commands.team.list.members.success": "Joukkueessa %s on %s pelaaja(a): %s", "commands.team.list.teams.empty": "Joukkueita ei ole", "commands.team.list.teams.success": "Löydettiin %s joukkue(tta): %s", "commands.team.option.collisionRule.success": "Törmäyssääntö joukkueelle %s on nyt \"%s\"", "commands.team.option.collisionRule.unchanged": "Mitään ei muutettu. Törmäyssääntö on jo tälle arvolle", "commands.team.option.color.success": "Joukkeen %s väri on nyt %s", "commands.team.option.color.unchanged": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. Täll<PERSON> jouk<PERSON> on jo tuo väri", "commands.team.option.deathMessageVisibility.success": "Kuolinviestin näkyvyys joukkueella %s on nyt \"%s\"", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. <PERSON><PERSON><PERSON><PERSON> on jo tälle arvolle", "commands.team.option.friendlyfire.alreadyDisabled": "Mitään ei muutettu. Ystävällinen vahinko on jo poissa käytöstä tälle joukku<PERSON>le", "commands.team.option.friendlyfire.alreadyEnabled": "Mitään ei muutettu. Ystävällinen vahinko on jo käytössä tälle joukku<PERSON>le", "commands.team.option.friendlyfire.disabled": "Ystävällinen vahinko poistettu käytöstä joukkueelta %s", "commands.team.option.friendlyfire.enabled": "Ystävällinen vahinko otettu käyttöön joukkueelle %s", "commands.team.option.name.success": "Päivitettiin j<PERSON> %s nimi", "commands.team.option.name.unchanged": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. Tä<PERSON><PERSON> jouk<PERSON> on jo tuo nimi", "commands.team.option.nametagVisibility.success": "Nimilapun näkyvyys joukkueella %s on nyt \"%s\"", "commands.team.option.nametagVisibility.unchanged": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. <PERSON><PERSON><PERSON><PERSON> nä<PERSON> on jo tälle arvolle", "commands.team.option.prefix.success": "Joukkueen etuliite on nyt %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Mitään ei muutettu. Joukkue ei voi nähdä näkymättömiä joukkuetovereitaan jo ennestään", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Mitään ei muutettu. Tämä joukkue voi jo nähdä näkymättömät joukkuetoverit", "commands.team.option.seeFriendlyInvisibles.disabled": "Joukkue %s ei voi enää nähdä näkymättömiä joukkuetovereita", "commands.team.option.seeFriendlyInvisibles.enabled": "Joukkue %s voi nyt nähdä näkymättömiä joukkuetovereita", "commands.team.option.suffix.success": "Joukkueen jälkiliite on nyt %s", "commands.team.remove.success": "Joukkue %s on poistettu", "commands.teammsg.failed.noteam": "<PERSON>un on kuulutta<PERSON> johonkin joukku<PERSON>en void<PERSON><PERSON> viestiä omalle joukku<PERSON>i", "commands.teleport.invalidPosition": "Sijainti ei kelpaa teleporttaamiseen", "commands.teleport.success.entity.multiple": "Teleportattiin %s kohdetta kohteeseen %s", "commands.teleport.success.entity.single": "%s teleportattiin pelaajan %s luokse", "commands.teleport.success.location.multiple": "Teleportattiin %s kohdetta kohteeseen %s, %s, %s", "commands.teleport.success.location.single": "%s teleportattiin koordinaatteihin %s,%s,%s", "commands.test.batch.starting": "Aloitusympäristö %s erä %s", "commands.test.clear.error.no_tests": "<PERSON>i lö<PERSON>etty pyyhittävi<PERSON> testejä", "commands.test.clear.success": "Pyyhitty %s rakennelma(a)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Klikkaa kopioidaksesi leikepöydälle", "commands.test.create.success": "Testiasetukset luotu testille %s", "commands.test.error.no_test_containing_pos": "Ei löydetty testitapausta, joka sisältää %s, %s, %s", "commands.test.error.no_test_instances": "Testitapauksia ei löydetty", "commands.test.error.non_existant_test": "Testiä %s ei löydetty", "commands.test.error.structure_not_found": "Testirakennelmaa %s ei löydetty", "commands.test.error.test_instance_not_found": "Testitapauskuution kohdetta ei löydetty", "commands.test.error.test_instance_not_found.position": "Testitapauskuution kohdetta ei löydetty testille sijainnissa %s, %s, %s", "commands.test.error.too_large": "Rakennelman koon tulee olla vähemmän kuin %s kuutiota kunkin aks<PERSON> su<PERSON>ti", "commands.test.locate.done": "<PERSON><PERSON><PERSON><PERSON>, löydettiin %s rakenne<PERSON>(a)", "commands.test.locate.found": "Löydettiin rakenne sijainnista %s (etäisyys %s)", "commands.test.locate.started": "Aloitettiin testirakennelmien paikannus, tämä saattaa kestää hetken...", "commands.test.no_tests": "<PERSON>i testejä suori<PERSON>vana", "commands.test.relative_position": "Sijainti suhteessa %s: %s", "commands.test.reset.error.no_tests": "Nollattavia testejä ei l<PERSON>yt", "commands.test.reset.success": "Nollattu %s rakennelma(a)", "commands.test.run.no_tests": "Testejä ei l<PERSON>yt", "commands.test.run.running": "Suoritetaan %s testiä...", "commands.test.summary": "Pelitesti valmis! %s testiä suoritettiin", "commands.test.summary.all_required_passed": "<PERSON><PERSON><PERSON> vaaditut testit läpäisty :)", "commands.test.summary.failed": "%s vaadittua testiä epäonnistui :(", "commands.test.summary.optional_failed": "%s valinnaista testiä epäonnistui", "commands.tick.query.percentiles": "Persentiilit: P50: %s ms; P95: %s ms; P99: %sms; otos: %s", "commands.tick.query.rate.running": "Kohdepykälänopeus: %s sekunnissa.\nKeskimääräinen pykäläaika: %s ms (Kohde: %s ms)", "commands.tick.query.rate.sprinting": "Kohdepykälänopeus: %s sekunnissa (ohitettu, vain viitteellinen).\nKeskimääräinen pykäläaika: %s ms", "commands.tick.rate.success": "Asetettiin kohdepykälänopeudeksi %s sekunnissa", "commands.tick.sprint.report": "Juoksutettiin %s pykälää sekunnissa tai %s ms pykälää kohden", "commands.tick.sprint.stop.fail": "<PERSON>i pykäläjuoksutusta k<PERSON>ynnissä", "commands.tick.sprint.stop.success": "Pykä<PERSON>ä<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "commands.tick.status.frozen": "<PERSON><PERSON> on jäädytetty", "commands.tick.status.lagging": "<PERSON><PERSON> on k<PERSON><PERSON><PERSON><PERSON>, mutta ei pysy kohdepykälänopeuden perässä", "commands.tick.status.running": "<PERSON><PERSON> to<PERSON>ii <PERSON>", "commands.tick.status.sprinting": "Pykäläjuok<PERSON><PERSON> k<PERSON>", "commands.tick.step.fail": "<PERSON><PERSON> ask<PERSON>taminen ei onnistu – peli tulee jäädyttää ensin", "commands.tick.step.stop.fail": "<PERSON><PERSON> pykäläaskellusta k<PERSON>ynnissä", "commands.tick.step.stop.success": "Pykäläaskellus kes<PERSON>ttiin", "commands.tick.step.success": "Askelletaan %s pykälä(ä)", "commands.time.query": "Aika on nyt %s", "commands.time.set": "Asetettiin a<PERSON>si %s", "commands.title.cleared.multiple": "Poistettiin nimikkeet %s pelaajalta", "commands.title.cleared.single": "Poistettiin nimikkeet pelaajalta %s", "commands.title.reset.multiple": "Palautettiin %s pelaajan otsikkoasetukset", "commands.title.reset.single": "Palautettiin pelaajan %s otsikkoasetukset", "commands.title.show.actionbar.multiple": "Näytetään uusi toimintopalkin otsikko %s pelaajalle", "commands.title.show.actionbar.single": "Näytetään uusi toimintopalkin otsikko pelaajalle %s", "commands.title.show.subtitle.multiple": "Näytetään uusi alaotsikko %s pelaajalle", "commands.title.show.subtitle.single": "Näytetään uusi alaotsikko pelaajalle %s", "commands.title.show.title.multiple": "Näytetään uusi otsikko %s pelaajalle", "commands.title.show.title.single": "Näytetään uusi otsikko pelaajalle %s", "commands.title.times.multiple": "Muutettiin otsikon näyttökertoja %s pelaajalle", "commands.title.times.single": "Muutettiin otsikon näyttökertoja pelaajalle %s", "commands.transfer.error.no_players": "Vähintään yksi siirrettävä pelaaja täytyy määrittää", "commands.transfer.success.multiple": "Siirretään %s pelaajaa kohteeseen %s:%s", "commands.transfer.success.single": "Siirretään %s kohteeseen %s:%s", "commands.trigger.add.success": "Laukaistiin %s (lisättiin %s arvoon)", "commands.trigger.failed.invalid": "Voit laukaista vain tavoitteita jotka ovat 'laukaisu' tyyppiä", "commands.trigger.failed.unprimed": "Et voi laukaista tätä tavoitetta vielä", "commands.trigger.set.success": "Laukaistiin %s (asetettiin arvoksi %s)", "commands.trigger.simple.success": "Laukaistiin %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Ei reittipisteitä sijainnissa %s", "commands.waypoint.list.success": "%s reittipiste(ttä) sijainnissa %s: %s", "commands.waypoint.modify.color": "Reittipisteen väri on nyt %s", "commands.waypoint.modify.color.reset": "<PERSON><PERSON><PERSON> reittip<PERSON> v<PERSON>ri", "commands.waypoint.modify.style": "Reittipisteen tyyli<PERSON> muutettu", "commands.weather.set.clear": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> selk<PERSON>i", "commands.weather.set.rain": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>i", "commands.weather.set.thunder": "<PERSON><PERSON><PERSON>in s<PERSON>kosmyrskyksi", "commands.whitelist.add.failed": "<PERSON><PERSON><PERSON> on jo valk<PERSON><PERSON> listalla", "commands.whitelist.add.success": "%s l<PERSON><PERSON><PERSON><PERSON> v<PERSON> listalle", "commands.whitelist.alreadyOff": "Valkoinen lista on jo poissa käytöstä", "commands.whitelist.alreadyOn": "Valkoinen lista on jo käytössä", "commands.whitelist.disabled": "Valkoinen lista on nyt poistettu käytöstä", "commands.whitelist.enabled": "Valkoinen lista on nyt otettu käyttöön", "commands.whitelist.list": "<PERSON><PERSON><PERSON><PERSON> listall<PERSON> on %s pelaaja(a): %s", "commands.whitelist.none": "<PERSON><PERSON><PERSON><PERSON> listalla ei ole y<PERSON>än pela<PERSON>a", "commands.whitelist.reloaded": "Valkoinen lista uudelleenladattiin", "commands.whitelist.remove.failed": "<PERSON><PERSON>ajaa ei ole valkoisella listalla", "commands.whitelist.remove.success": "Poistettiin %s valkoiselta listalta", "commands.worldborder.center.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> ei muutettu. <PERSON><PERSON><PERSON> raja on jo keski<PERSON>tty valittuun sijaintiin", "commands.worldborder.center.success": "<PERSON><PERSON><PERSON>in ma<PERSON>man rajan reunan keskus kohtaan %s, %s", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. Ma<PERSON><PERSON> rajasta a<PERSON>tu<PERSON> vahi<PERSON> on jo tuon verran", "commands.worldborder.damage.amount.success": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>man rajan vahi<PERSON> %s sekuntiin", "commands.worldborder.damage.buffer.failed": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. <PERSON><PERSON><PERSON> rajan v<PERSON><PERSON><PERSON> on jo asetettu tuolle etäisyydelle", "commands.worldborder.damage.buffer.success": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>man rajan v<PERSON> %s ku<PERSON><PERSON>", "commands.worldborder.get": "Maailman rajan leveys on tällä hetkellä %s kuutio(ta)", "commands.worldborder.set.failed.big": "Maailman raja voi olla enintään %s kuutiota leveä", "commands.worldborder.set.failed.far": "Maailman raja voi olla enintään %s kuution etäisyydellä", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. <PERSON><PERSON><PERSON> raja on jo tämän kokoinen", "commands.worldborder.set.failed.small": "Maailman raja ei voi olla leveydeltään 1 kuutiota pienempi", "commands.worldborder.set.grow": "Kasvatetaan maailman rajaa %s kuutiolla %s sekunnin ajan", "commands.worldborder.set.immediate": "Asetettiin ma<PERSON>man rajan leveydeksi %s kuutio(ta)", "commands.worldborder.set.shrink": "Pienennetään ma<PERSON>man rajaa %s kuution levyiseksi %s sekunnin ajan", "commands.worldborder.warning.distance.failed": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. <PERSON><PERSON><PERSON> rajan varoitus on jo asetettu tuolle etäisyydelle", "commands.worldborder.warning.distance.success": "Asete<PERSON>in ma<PERSON>man rajan varoitusetäisyys %s kuutioon", "commands.worldborder.warning.time.failed": "<PERSON><PERSON><PERSON><PERSON>n ei muutettu. <PERSON><PERSON><PERSON> rajan varoitus on jo asetettu tuolle ajalle", "commands.worldborder.warning.time.success": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>man rajan var<PERSON> %s sekuntiin", "compliance.playtime.greaterThan24Hours": "<PERSON><PERSON> pelannut yli 24 tuntia", "compliance.playtime.hours": "Olet pelannut %s tuntia", "compliance.playtime.message": "Liiallinen pelaaminen voi häiritä normaalia päivittäistä elämää", "connect.aborted": "<PERSON><PERSON><PERSON><PERSON>", "connect.authorizing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "connect.connecting": "Yhdistetään palvelimeen...", "connect.encrypting": "Salataan...", "connect.failed": "Palvelimeen yhdistäminen epäonnistui", "connect.failed.transfer": "Yht<PERSON>den muodostaminen epäonnistui siirrettäessä palvelimelle", "connect.joining": "Li<PERSON><PERSON><PERSON><PERSON><PERSON>...", "connect.negotiating": "Neuvotellaan...", "connect.reconfiging": "Uudelleenjärjestellään...", "connect.reconfiguring": "Uudelleenjärjestellään...", "connect.transferring": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uudelle palvelimelle...", "container.barrel": "<PERSON><PERSON><PERSON>", "container.beacon": "Merkkivalo", "container.beehive.bees": "Mehiläisiä: %s/%s", "container.beehive.honey": "Hunajaa: %s/%s", "container.blast_furnace": "<PERSON><PERSON><PERSON>", "container.brewing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.cartography_table": "Karttapöytä", "container.chest": "Arkku", "container.chestDouble": "<PERSON><PERSON>", "container.crafter": "<PERSON><PERSON><PERSON><PERSON>", "container.crafting": "Työstäminen", "container.creative": "Esineiden valitseminen", "container.dispenser": "<PERSON><PERSON><PERSON><PERSON>", "container.dropper": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant": "<PERSON><PERSON><PERSON>", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s lasuriittia", "container.enchant.lapis.one": "1 lasuriitti", "container.enchant.level.many": "%s lumoustasoa", "container.enchant.level.one": "1 lumoustaso", "container.enchant.level.requirement": "<PERSON><PERSON><PERSON><PERSON> koke<PERSON>: %s", "container.enderchest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.furnace": "<PERSON><PERSON>", "container.grindstone_title": "<PERSON><PERSON><PERSON><PERSON> ja poista lumou<PERSON>ia", "container.hopper": "<PERSON><PERSON><PERSON>", "container.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.isLocked": "%s on lukittu!", "container.lectern": "<PERSON><PERSON><PERSON>", "container.loom": "<PERSON><PERSON><PERSON><PERSON>", "container.repair": "<PERSON><PERSON><PERSON><PERSON> ja nime<PERSON>", "container.repair.cost": "Lu<PERSON><PERSON>sen hinta: %1$s", "container.repair.expensive": "<PERSON><PERSON> ka<PERSON>!", "container.shulkerBox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "ja %s lisää...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "<PERSON><PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "Ei voi avata. Sisältöä ei vielä ole luotu.", "container.stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.upgrade": "Päivitä varusteita", "container.upgrade.error_tooltip": "Esinettä ei voi päivittää tällä tavalla", "container.upgrade.missing_template_tooltip": "<PERSON><PERSON><PERSON><PERSON>", "controls.keybinds": "Näppäinmääritykset...", "controls.keybinds.duplicateKeybinds": "Tätä näppäintä käyttää myös:\n%s", "controls.keybinds.title": "Näppäinmääritykset", "controls.reset": "<PERSON><PERSON><PERSON>", "controls.resetAll": "<PERSON><PERSON><PERSON>", "controls.title": "Näppäimet", "createWorld.customize.buffet.biome": "Valitse biomi", "createWorld.customize.buffet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON> muo<PERSON>n", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "<PERSON><PERSON> k<PERSON> – %s", "createWorld.customize.flat.layer.top": "<PERSON><PERSON> kerros – %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON> kerros", "createWorld.customize.flat.tile": "<PERSON><PERSON><PERSON> materiaali", "createWorld.customize.flat.title": "<PERSON><PERSON><PERSON> ma<PERSON> mukautus", "createWorld.customize.presets": "<PERSON><PERSON>setukset", "createWorld.customize.presets.list": "Voit myös valita jonkin meidän tekemistämme!", "createWorld.customize.presets.select": "Käytä es<PERSON>", "createWorld.customize.presets.share": "Haluatko jakaa esiaset<PERSON>ses<PERSON> muillekin? Käytä alla olevaa kenttää!", "createWorld.customize.presets.title": "Valitse es<PERSON>etus", "createWorld.preparing": "Valmistellaan maailman luontia...", "createWorld.tab.game.title": "<PERSON><PERSON>", "createWorld.tab.more.title": "Lisää", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "Attribuutio", "credits_and_attribution.button.credits": "Tekijät", "credits_and_attribution.button.licenses": "Lisenssit", "credits_and_attribution.screen.title": "Tekijät ja attribuutio", "dataPack.bundle.description": "<PERSON><PERSON><PERSON> k<PERSON>öön koke<PERSON>sen esineen: <PERSON><PERSON><PERSON>", "dataPack.bundle.name": "<PERSON><PERSON><PERSON>", "dataPack.locator_bar.description": "Näytä muiden pelaajien suunta moninpelissä", "dataPack.locator_bar.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataPack.minecart_improvements.description": "Paranneltu kaivosvaunujen liikkuminen", "dataPack.minecart_improvements.name": "Kaivosvaunuparannukset", "dataPack.redstone_experiments.description": "Kokeelliset redstone-muutokset", "dataPack.redstone_experiments.name": "Kokeellinen redstone", "dataPack.title": "Valitse datapaketit", "dataPack.trade_rebalance.description": "P<PERSON>iv<PERSON><PERSON> ka<PERSON><PERSON><PERSON>ti kyläläisille", "dataPack.trade_rebalance.name": "Kyläläist<PERSON> vai<PERSON><PERSON> ta<PERSON>", "dataPack.update_1_20.description": "Minecraft-version 1.20 uudet ominaisuudet ja sisältö", "dataPack.update_1_20.name": "Päivitys 1.20", "dataPack.update_1_21.description": "Minecraft-version 1.21 uudet ominaisuudet ja sisältö", "dataPack.update_1_21.name": "Päivitys 1.21", "dataPack.validation.back": "<PERSON><PERSON><PERSON>", "dataPack.validation.failed": "Datapaketin vahvistaminen epäonnistui!", "dataPack.validation.reset": "<PERSON><PERSON><PERSON>", "dataPack.validation.working": "Vahvistetaan valittuja datapaketteja...", "dataPack.vanilla.description": "<PERSON><PERSON><PERSON> o<PERSON>", "dataPack.vanilla.name": "<PERSON><PERSON>", "dataPack.winter_drop.description": "Talvipä<PERSON>ks<PERSON> uudet ominaisuudet ja sisältö", "dataPack.winter_drop.name": "Talvipäivitys", "datapackFailure.safeMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datapackFailure.safeMode.failed.description": "Tämä maailma sisältää virheellistä tai korruptoitunutta tallennusdataa.", "datapackFailure.safeMode.failed.title": "Ma<PERSON><PERSON> la<PERSON><PERSON><PERSON> vika<PERSON><PERSON>til<PERSON><PERSON> e<PERSON>.", "datapackFailure.title": "Virheet valituissa datapaketeissa estävät maailmaa latautumasta.\nVoit joko yrittää ladata vain tavallisen datapaketin (\"vikasietotila\"), tai palata takaisin päävalikkoon ja korjata virheet manuaalisesti.", "death.attack.anvil": "%1$s murska<PERSON><PERSON> putoavan alasimen alle", "death.attack.anvil.player": "%1$s liiska<PERSON><PERSON> putoavan alasimen alle taist<PERSON>saan kohteen %2$s kanssa", "death.attack.arrow": "%2$s ampui pelaajan %1$s", "death.attack.arrow.item": "%2$s ampui pelaajan %1$s, käyttäen esinettä %3$s", "death.attack.badRespawnPoint.link": "Tarkoituksellinen pelisuunnittelu", "death.attack.badRespawnPoint.message": "%2$s tappoi pelaajan %1$s", "death.attack.cactus": "%1$s piikittyi hengiltä", "death.attack.cactus.player": "%1$s käveli kaktukseen yrittäessään paeta kohdetta %2$s", "death.attack.cramming": "%1$s joutui liialliseen puristukseen", "death.attack.cramming.player": "%2$s murs<PERSON><PERSON> pelaajan %1$s", "death.attack.dragonBreath": "%1$s paahtui lohikäärmeen henkäyksessä", "death.attack.dragonBreath.player": "%2$s paahtoi pelaajan %1$s lohikäärmeen henkäyksessä", "death.attack.drown": "%1$s hukkui", "death.attack.drown.player": "%1$s hukkui yrittäessään paeta kohdetta %2$s", "death.attack.dryout": "%1$s kuivui hengiltä", "death.attack.dryout.player": "%1$s kuivui hengiltä yrittäessään paeta kohdetta %2$s", "death.attack.even_more_magic": "%1$s tapettiin use<PERSON><PERSON> ta<PERSON>la", "death.attack.explosion": "%1$s räjähti", "death.attack.explosion.player": "%2$s räjäytti pelaajan %1$s", "death.attack.explosion.player.item": "%2$s räjäytti pelaajan %1$s, käyttäen esinettä %3$s", "death.attack.fall": "%1$s iskey<PERSON>i maahan liian lujaa", "death.attack.fall.player": "%1$s is<PERSON><PERSON>i maahan liian lujaa yrittäessään paeta kohdetta %2$s", "death.attack.fallingBlock": "%1$s murska<PERSON>ui putoavan kuution alle", "death.attack.fallingBlock.player": "%1$s liiska<PERSON>ui putoavan kuution alle taistellessaan kohteen %2$s kanssa", "death.attack.fallingStalactite": "%1$s seivästyi putoavasta stalaktiitista", "death.attack.fallingStalactite.player": "Putoava stalaktiitti lävisti pelaajan %1$s hänen taistellessa kohteen %2$s kanssa", "death.attack.fireball": "%2$s tulipal<PERSON><PERSON> pelaajan %1$s", "death.attack.fireball.item": "%2$s tulipallot<PERSON> pelaajan %1$s, käyttäen esinettä %3$s", "death.attack.fireworks": "%1$s kuoli pamaukseen", "death.attack.fireworks.item": "%1$s kuoli pamaukseen pelaajan %2$s ammuttua tätä kohti ilotulitteen esineellä %3$s", "death.attack.fireworks.player": "%1$s kuoli pamaukseen taist<PERSON>saan kohteen %2$s kanssa", "death.attack.flyIntoWall": "%1$s tutustui liike-energiaan", "death.attack.flyIntoWall.player": "%1$s tutustui liike-energiaan yrittäessään paeta kohdetta %2$s", "death.attack.freeze": "%1$s jä<PERSON>yi hengiltä", "death.attack.freeze.player": "%1$s jäätyi hengiltä kohteen %2$s toimesta", "death.attack.generic": "%1$s kuoli", "death.attack.generic.player": "%2$s aiheutti pelaajan %1$s kuoleman", "death.attack.genericKill": "%1$s tapettiin", "death.attack.genericKill.player": "%1$s kuoli taist<PERSON><PERSON><PERSON> kohteen %2$s kanssa", "death.attack.hotFloor": "%1$s totesi lattian olevan laavaa", "death.attack.hotFloor.player": "%2$s ajoi pelaajan %1$s vaaravyöhykkeelle", "death.attack.inFire": "%1$s syttyi liekkeihin", "death.attack.inFire.player": "%1$s käveli tuleen taistellessaan kohteen %2$s kanssa", "death.attack.inWall": "%1$s tukehtui seinän sis<PERSON>n", "death.attack.inWall.player": "%1$s tukehtui seinän sis<PERSON>än ta<PERSON><PERSON><PERSON><PERSON> kohteen %2$s kanssa", "death.attack.indirectMagic": "%2$s taikoi pelaajan %1$s hengiltä", "death.attack.indirectMagic.item": "%2$s tappoi pelaajan %1$s, käyttäen esinettä %3$s", "death.attack.lava": "%1$s yritti uida laavassa", "death.attack.lava.player": "%1$s yritti uida laavassa paetakseen kohdetta %2$s", "death.attack.lightningBolt": "%1$s sai salamaniskun", "death.attack.lightningBolt.player": "%1$s sai salamaniskun taist<PERSON>saan kohteen %2$s kanssa", "death.attack.mace_smash": "%2$s murs<PERSON><PERSON> pelaajan %1$s", "death.attack.mace_smash.item": "%2$s murskasi pelaajan %1$s käyttäen esinettä %3$s", "death.attack.magic": "%1$s tapettiin ta<PERSON>la", "death.attack.magic.player": "%1$s kuoli taikaan yrittäessään paeta kohdetta %2$s", "death.attack.message_too_long": "Itse asiassa viesti oli liian pitkä näytettäväksi kokonaan. Pahoittelut! Tässä lyhennetty versio: %s", "death.attack.mob": "%2$s surmasi pelaajan %1$s", "death.attack.mob.item": "%2$s surmasi pela<PERSON>n %1$s, käyttäen esinettä %3$s", "death.attack.onFire": "%1$s paloi hengiltä", "death.attack.onFire.item": "%1$s paloi poroksi ta<PERSON>sa<PERSON> koh<PERSON> %2$s vastaan, j<PERSON> k<PERSON> asetta %3$s", "death.attack.onFire.player": "%1$s paloi poroksi taist<PERSON>saan kohteen %2$s kanssa", "death.attack.outOfWorld": "%1$s putosi ulos ma<PERSON>ta", "death.attack.outOfWorld.player": "%1$s ei halunnut elää samassa maailmassa kuin %2$s", "death.attack.outsideBorder": "%1$s j<PERSON><PERSON> tä<PERSON><PERSON>n ma<PERSON>man rajat", "death.attack.outsideBorder.player": "%1$s poistui tästä maailmasta taistellessaan kohdetta %2$s vastaan", "death.attack.player": "%2$s surmasi pelaajan %1$s", "death.attack.player.item": "%2$s surmasi pela<PERSON>n %1$s, käyttäen esinettä %3$s", "death.attack.sonic_boom": "%1$s tuhoutui ääniaallon voimasta", "death.attack.sonic_boom.item": "%1$s tuhoutui ääniaallon voimasta yrittäessään paeta kohdetta %2$s, j<PERSON> kä<PERSON> asetta %3$s", "death.attack.sonic_boom.player": "%1$s tuhoutui ääniaallon voimasta yrittäessään paeta kohdetta %2$s", "death.attack.stalagmite": "%1$s seivästyi stalagmiittiin", "death.attack.stalagmite.player": "Stalagmiitti seivästi pelaajan %1$s hänen taistellessa kohteen %2$s kanssa", "death.attack.starve": "%1$s nääntyi nälkään", "death.attack.starve.player": "%1$s nääntyi nälkään taistellessaan kohteen %2$s kanssa", "death.attack.sting": "%1$s pisteltiin heng<PERSON>", "death.attack.sting.item": "%2$s pisteli pelaajan %1$s hengiltä, käyttäen esinettä %3$s", "death.attack.sting.player": "%2$s pisteli pelaajan %1$s hengiltä", "death.attack.sweetBerryBush": "Makea marjapensas pisteli pelaajan %1$s hengiltä", "death.attack.sweetBerryBush.player": "Makea marjapensas pisteli pelaajan %1$s hengiltä hänen yrittäessä paeta kohdetta %2$s", "death.attack.thorns": "%1$s tapettiin yrit<PERSON>ess<PERSON>än vahingoittaa kohdetta %2$s", "death.attack.thorns.item": "%1$s tapettiin esineellä %3$s hänen yrittäessä vahingoittaa kohdetta %2$s", "death.attack.thrown": "%2$s viskoi pelaajan %1$s hengiltä", "death.attack.thrown.item": "%2$s viskoi pelaajan %1$s hengiltä, käyttäen esinettä %3$s", "death.attack.trident": "%2$s seivästi pelaajan %1$s", "death.attack.trident.item": "%2$s seivästi pelaajan %1$s, käyttäen esinettä %3$s", "death.attack.wither": "%1$s kuihtui pois", "death.attack.wither.player": "%1$s kuihtui pois taist<PERSON><PERSON>an kohteen %2$s kanssa", "death.attack.witherSkull": "%2$s ampui pelaajan %1$s kallolla", "death.attack.witherSkull.item": "%2$s ampui pelaajan %1$s kallo<PERSON>, käyttäen esinettä %3$s", "death.fell.accident.generic": "%1$s putosi kor<PERSON>ta", "death.fell.accident.ladder": "%1$s putosi tikkailta", "death.fell.accident.other_climbable": "%1$s putosi kiivetessään", "death.fell.accident.scaffolding": "%1$s putosi rakennustelineeltä", "death.fell.accident.twisting_vines": "%1$s putosi kieroköynnöksiltä", "death.fell.accident.vines": "%1$s putosi köynnöksiltä", "death.fell.accident.weeping_vines": "%1$s putosi itkuköynnöksiltä", "death.fell.assist": "%1$s oli tuo<PERSON><PERSON> putoam<PERSON> kohteen %2$s toimesta", "death.fell.assist.item": "%1$s oli tuo<PERSON><PERSON> putoam<PERSON> kohteen %2$s osuttua häneen esineellä %3$s", "death.fell.finish": "%1$s putosi liian korkealta ja %2$s viimeisteli hänet", "death.fell.finish.item": "%1$s putosi liian korkealta ja %2$s viimeisteli hänet esineellä %3$s", "death.fell.killer": "%1$s oli tuo<PERSON><PERSON> putoamaan", "deathScreen.quit.confirm": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> lo<PERSON>?", "deathScreen.respawn": "<PERSON><PERSON><PERSON>", "deathScreen.score": "<PERSON><PERSON><PERSON>", "deathScreen.score.value": "Pisteet: %s", "deathScreen.spectate": "<PERSON><PERSON><PERSON>", "deathScreen.title": "<PERSON><PERSON><PERSON>!", "deathScreen.title.hardcore": "<PERSON>eli p<PERSON>ättyi!", "deathScreen.titleScreen": "Päävalikko", "debug.advanced_tooltips.help": "F3 + H = Edistyneemmät vinkit", "debug.advanced_tooltips.off": "Edistyneet vinkit: piilossa", "debug.advanced_tooltips.on": "Edistyneet vinkit: näkyvillä", "debug.chunk_boundaries.help": "F3 + G = Näytä lohkon rajat", "debug.chunk_boundaries.off": "Lohkon rajat: pii<PERSON><PERSON>u", "debug.chunk_boundaries.on": "Lohkojen rajat: näkyvillä", "debug.clear_chat.help": "F3 + D = Tyhjennä keskustelu", "debug.copy_location.help": "F3 + C = <PERSON><PERSON><PERSON> si<PERSON>i /tp kome<PERSON>, pidä F3 + C painettuna kaataaksesi pelin", "debug.copy_location.message": "Sijainti kopioitu leikepöydälle", "debug.crash.message": "F3 + C on painettuna. Tämä kaataa pelin ellei näppäimiä vapauteta.", "debug.crash.warning": "Kaatuu %s...", "debug.creative_spectator.error": "Pelitilaa ei voi vaihtaa; ei oikeuksia", "debug.creative_spectator.help": "F3 + N = <PERSON><PERSON><PERSON><PERSON> pelitila <-> ka<PERSON><PERSON>", "debug.dump_dynamic_textures": "Dynaamiset tekstuurit tallennettu kohteeseen %s", "debug.dump_dynamic_textures.help": "F3 + S = Vedosta dynaamiset tekstuurit", "debug.gamemodes.error": "Pelimuodon valikkoa ei voida avata; ei oikeuksia", "debug.gamemodes.help": "F3 + F4 = <PERSON><PERSON>", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s seuraava", "debug.help.help": "F3 + Q = Näytä tämä lista", "debug.help.message": "Näppäinmääritykset:", "debug.inspect.client.block": "Asiakasohjelman kuutiotiedot kopioitu leikepöydälle", "debug.inspect.client.entity": "Asiakas<PERSON><PERSON>lman kohdetiedot kopioitu leikepöydälle", "debug.inspect.help": "F3 + I = <PERSON><PERSON><PERSON> kohde- tai ku<PERSON>ot<PERSON>ot leikepöydälle", "debug.inspect.server.block": "Palvelimen kuutiotiedot kopioitu leikepöydälle", "debug.inspect.server.entity": "Palvelimen kohdetiedot kopioitu leikepöydälle", "debug.pause.help": "F3 + Esc = Pysäytä ilman valikkoa (jos pys<PERSON><PERSON><PERSON> on mah<PERSON>llista)", "debug.pause_focus.help": "F3 + P = Pysäytä kun tarkennus katoaa", "debug.pause_focus.off": "Pysäytä kun tarkennus katoaa: poissa käytöstä", "debug.pause_focus.on": "Pysäytä kun tarkennus katoaa: käytössä", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Aloita/lopeta profilointi", "debug.profiling.start": "Profilointi aloitettu %s:ksi sekunniksi. Käytä F3 + L pysäyttääksesi.", "debug.profiling.stop": "Profilointi päättynyt. Tulokset tallennettu sijaintiin %s", "debug.reload_chunks.help": "F3 + A = Lataa lohkot uudelleen", "debug.reload_chunks.message": "<PERSON>ude<PERSON><PERSON><PERSON><PERSON><PERSON> kaikki lo<PERSON>kot", "debug.reload_resourcepacks.help": "F3 + T = Lataa resurssipaketit uudelleen", "debug.reload_resourcepacks.message": "Resurssipaketit uudelleenladattiin", "debug.show_hitboxes.help": "F3 + B = Näytä osuma-alueet", "debug.show_hitboxes.off": "Osuma-alueet: p<PERSON><PERSON><PERSON>u", "debug.show_hitboxes.on": "Osuma-alueet: näkyvillä", "debug.version.header": "Asiakasohjelman version tiedot:", "debug.version.help": "F3 + V = Asiakas<PERSON>jelman version tiedot", "demo.day.1": "Tämä kokeiluversio kestää viisi Minecraft-päivää. Tee parhaasi!", "demo.day.2": "<PERSON><PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON>", "demo.day.4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "demo.day.5": "Tämä on viimeinen päiväsi!", "demo.day.6": "Viides päivä on kulunut. Paina %s tallentaaksesi ku<PERSON>appauksen luomuksestasi.", "demo.day.warning": "<PERSON><PERSON><PERSON> on melkein lopussa!", "demo.demoExpired": "Kokeiluaika on päättynyt!", "demo.help.buy": "Osta nyt!", "demo.help.fullWrapped": "Tämä kokeiluversio kestää 5 Minecraft-vuorokautta (noin 1 tunti ja 40 minuuttia oikeassa ajassa). <PERSON><PERSON> saavutukset saadaksesi vinkkejä! Pidä hauskaa!", "demo.help.inventory": "Paina %1$s avat<PERSON><PERSON> tavaral<PERSON>ttelon", "demo.help.jump": "Hyppää painamalla %1$s", "demo.help.later": "Jatka pelaamista!", "demo.help.movement": "Käytä %1$s, %2$s, %3$s, %4$s ja hiirtä liikkuaksesi", "demo.help.movementMouse": "Katso ympärillesi käyttämällä hiirtä", "demo.help.movementShort": "<PERSON><PERSON><PERSON> %1$s, %2$s, %3$s, %4$s", "demo.help.title": "Minecraft kokeilutila", "demo.remainingTime": "Aikaa jäljellä: %s", "demo.reminder": "Kokeiluaika on päättynyt. Osta peli jatka<PERSON>esi tai aloita uusi ma<PERSON>ma!", "difficulty.lock.question": "Haluatko varmasti lukita vaikeustason %1$s tässä maailmassa? Sen vaihtaminen ei ole mahdollista lukitsemisen jälkeen.", "difficulty.lock.title": "Lu<PERSON>tse maailman vai<PERSON><PERSON>o", "disconnect.endOfStream": "Tietovirran loppu", "disconnect.exceeded_packet_rate": "<PERSON><PERSON><PERSON><PERSON> katkai<PERSON>iin pakettirajan ylittä<PERSON>n vuoksi", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ohitetaan tilapyyntö", "disconnect.loginFailedInfo": "Sisäänkirjautuminen epäonnistui: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "<PERSON><PERSON><PERSON><PERSON> on poissa k<PERSON>ytöstä. Ole hyvä ja tarkista Microsoft-tilisi asetukset.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON><PERSON><PERSON> istunto (Yritä käynnistää peli ja käynnistin uudelleen)", "disconnect.loginFailedInfo.serversUnavailable": "Todentamispalvelimet eivät ole juuri nyt saavutettavissa. Yritä uudes<PERSON>an.", "disconnect.loginFailedInfo.userBanned": "Sin<PERSON><PERSON> on esto verkkopelaamisesta", "disconnect.lost": "<PERSON><PERSON><PERSON><PERSON>", "disconnect.packetError": "Verkkoprotokollavirhe", "disconnect.spam": "Potkittiin pelistä roskapostin vuoksi", "disconnect.timeout": "Aikakatkaistu", "disconnect.transfer": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> pal<PERSON>lle", "disconnect.unknownHost": "<PERSON><PERSON><PERSON><PERSON> palvelin", "download.pack.failed": "%s/%s paketin lataaminen epäonnistui", "download.pack.progress.bytes": "Edistyminen: %s (tunt<PERSON><PERSON> koko)", "download.pack.progress.percent": "Edistyminen: %s%%", "download.pack.title": "Ladataan resurssipakettia %s/%s", "editGamerule.default": "Oletus: %s", "editGamerule.title": "Muokkaa pelisääntöjä", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.bad_omen": "<PERSON><PERSON> enne", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "Kanavoijan voima", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "<PERSON><PERSON><PERSON>n armo", "effect.minecraft.fire_resistance": "Tulenkestävyys", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.haste": "<PERSON><PERSON>", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>tus", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.hunger": "Nälkä", "effect.minecraft.infested": "Sa<PERSON><PERSON><PERSON>", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON><PERSON><PERSON> parannus", "effect.minecraft.invisibility": "Näkymättömyys", "effect.minecraft.jump_boost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.levitation": "Levitaatio", "effect.minecraft.luck": "<PERSON><PERSON>", "effect.minecraft.mining_fatigue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.nausea": "Pahoinvointi", "effect.minecraft.night_vision": "Hämäränäkö", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.poison": "<PERSON><PERSON><PERSON>", "effect.minecraft.raid_omen": "Hyökk<PERSON><PERSON><PERSON> enne", "effect.minecraft.regeneration": "Elpyminen", "effect.minecraft.resistance": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.saturation": "Kylläisyys", "effect.minecraft.slow_falling": "<PERSON><PERSON>", "effect.minecraft.slowness": "Hitaus", "effect.minecraft.speed": "<PERSON>us", "effect.minecraft.strength": "Voimak<PERSON><PERSON>", "effect.minecraft.trial_omen": "<PERSON><PERSON><PERSON><PERSON> enne", "effect.minecraft.unluck": "<PERSON><PERSON> on<PERSON>", "effect.minecraft.water_breathing": "Vedenhengitys", "effect.minecraft.weakness": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "<PERSON><PERSON><PERSON> lad<PERSON>u", "effect.minecraft.wither": "<PERSON><PERSON><PERSON><PERSON>", "effect.none": "Ei vaikutuksia", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.bane_of_arthropods": "Niveljalkaisten kirous", "enchantment.minecraft.binding_curse": "<PERSON><PERSON><PERSON><PERSON> kirous", "enchantment.minecraft.blast_protection": "Räjähdyssuojaus", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "Kanavointi", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.feather_falling": "Höyhenkevyt", "enchantment.minecraft.fire_aspect": "Liekehtivä", "enchantment.minecraft.fire_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.fortune": "<PERSON><PERSON>", "enchantment.minecraft.frost_walker": "Pakkaskävelijä", "enchantment.minecraft.impaling": "Seivästys", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "<PERSON><PERSON>", "enchantment.minecraft.lure": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.punch": "Is<PERSON>", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.respiration": "Hengitys", "enchantment.minecraft.riptide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sharpness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.silk_touch": "<PERSON><PERSON><PERSON> kosketus", "enchantment.minecraft.smite": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.sweeping": "Kiitävä reuna", "enchantment.minecraft.sweeping_edge": "Terävä reuna", "enchantment.minecraft.swift_sneak": "<PERSON><PERSON>", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Katoamisen kirous", "enchantment.minecraft.wind_burst": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.acacia_boat": "Akaasiavene", "entity.minecraft.acacia_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.allay": "Apuri", "entity.minecraft.area_effect_cloud": "<PERSON><PERSON><PERSON>", "entity.minecraft.armadillo": "<PERSON><PERSON><PERSON><PERSON>äinen", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bamboo_chest_raft": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>a", "entity.minecraft.bamboo_raft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.bat": "Lepak<PERSON>", "entity.minecraft.bee": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.birch_boat": "Koivuvene", "entity.minecraft.birch_chest_boat": "Koivuvene arkulla", "entity.minecraft.blaze": "<PERSON><PERSON><PERSON>", "entity.minecraft.block_display": "Kuutionäyttö", "entity.minecraft.boat": "<PERSON><PERSON>", "entity.minecraft.bogged": "Rämeinen", "entity.minecraft.breeze": "Viima", "entity.minecraft.breeze_wind_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.camel": "<PERSON><PERSON><PERSON>", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Luolahämähäkki", "entity.minecraft.cherry_boat": "Kirsikkapuuvene", "entity.minecraft.cherry_chest_boat": "Kirsikkapuuvene arkulla", "entity.minecraft.chest_boat": "<PERSON><PERSON>", "entity.minecraft.chest_minecart": "<PERSON><PERSON><PERSON> ka<PERSON>", "entity.minecraft.chicken": "<PERSON><PERSON>", "entity.minecraft.cod": "<PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Komentokuutiokaivosvaunu", "entity.minecraft.cow": "<PERSON><PERSON><PERSON>", "entity.minecraft.creaking": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "<PERSON><PERSON> tammivene", "entity.minecraft.dark_oak_chest_boat": "<PERSON>mma tammivene a<PERSON>a", "entity.minecraft.dolphin": "<PERSON><PERSON><PERSON>", "entity.minecraft.donkey": "Aasi", "entity.minecraft.dragon_fireball": "Lohikää<PERSON><PERSON> tulipallo", "entity.minecraft.drowned": "Hukkunut", "entity.minecraft.egg": "<PERSON><PERSON><PERSON> muna", "entity.minecraft.elder_guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.end_crystal": "End-k<PERSON><PERSON><PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON> ender-helmi", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "<PERSON><PERSON><PERSON>", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON>", "entity.minecraft.experience_bottle": "<PERSON><PERSON><PERSON> lumottu pullo", "entity.minecraft.experience_orb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.eye_of_ender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.falling_block": "<PERSON><PERSON><PERSON> k<PERSON>o", "entity.minecraft.falling_block_type": "Putoava %s", "entity.minecraft.fireball": "<PERSON><PERSON><PERSON>", "entity.minecraft.firework_rocket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.fishing_bobber": "<PERSON><PERSON>", "entity.minecraft.fox": "Kettu", "entity.minecraft.frog": "Sammakko", "entity.minecraft.furnace_minecart": "Moottoroitu ka<PERSON>u", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON><PERSON>", "entity.minecraft.guardian": "Vartija", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON> g<PERSON>t", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Suppilokaivosvaunu", "entity.minecraft.horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.husk": "<PERSON><PERSON><PERSON>", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "<PERSON>su<PERSON>-al<PERSON>-en<PERSON><PERSON>", "entity.minecraft.iron_golem": "Rautagolem", "entity.minecraft.item": "Esine", "entity.minecraft.item_display": "Esinenäyttö", "entity.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.jungle_boat": "Viidakkopuuvene", "entity.minecraft.jungle_chest_boat": "Viidakkopuuvene arkulla", "entity.minecraft.killer_bunny": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.leash_knot": "<PERSON><PERSON><PERSON><PERSON><PERSON> solmu", "entity.minecraft.lightning_bolt": "Salama", "entity.minecraft.lingering_potion": "Viipyvä taika<PERSON>oma", "entity.minecraft.llama": "Laama", "entity.minecraft.llama_spit": "<PERSON><PERSON> sylki", "entity.minecraft.magma_cube": "Magmakuutio", "entity.minecraft.mangrove_boat": "Mangrovepuuvene", "entity.minecraft.mangrove_chest_boat": "Mangrovepuuvene arkulla", "entity.minecraft.marker": "Merk<PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Tammivene", "entity.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ocelot": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.ominous_item_spawner": "Pahaenteinen esineiden luoja", "entity.minecraft.painting": "Ma<PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>ven<PERSON>", "entity.minecraft.pale_oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Papu<PERSON><PERSON>", "entity.minecraft.phantom": "Aave", "entity.minecraft.pig": "Sika", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.pillager": "Ryöstäjä", "entity.minecraft.player": "<PERSON><PERSON><PERSON>", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.potion": "Taikajuoma", "entity.minecraft.pufferfish": "Pallokala", "entity.minecraft.rabbit": "<PERSON><PERSON><PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Lo<PERSON>", "entity.minecraft.sheep": "<PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Shulkerin ammus", "entity.minecraft.silverfish": "Sokeritoukka", "entity.minecraft.skeleton": "<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.slime": "Lima", "entity.minecraft.small_fireball": "<PERSON><PERSON> tulipallo", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Lumigolem", "entity.minecraft.snowball": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.spawner_minecart": "Luojakaivosvaunu", "entity.minecraft.spectral_arrow": "Spektraalinuoli", "entity.minecraft.spider": "Hämähäk<PERSON>", "entity.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "entity.minecraft.spruce_boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON>", "entity.minecraft.squid": "<PERSON><PERSON><PERSON>", "entity.minecraft.stray": "Vaeltaja", "entity.minecraft.strider": "Rämpijä", "entity.minecraft.tadpole": "Nuijapää", "entity.minecraft.text_display": "Tekstinäyttö", "entity.minecraft.tnt": "<PERSON><PERSON><PERSON><PERSON> dyna<PERSON>i", "entity.minecraft.tnt_minecart": "TNT-kaivosvaunu", "entity.minecraft.trader_llama": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laama", "entity.minecraft.trident": "Atrain", "entity.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> kala", "entity.minecraft.tropical_fish.predefined.0": "Vuokkokala", "entity.minecraft.tropical_fish.predefined.1": "Nokkavälskäri", "entity.minecraft.tropical_fish.predefined.10": "Sarvikala", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.12": "Papukaijakala", "entity.minecraft.tropical_fish.predefined.13": "Kuningatarenkelikala", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.16": "Punanapsija", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON>ev<PERSON>", "entity.minecraft.tropical_fish.predefined.18": "Punavuokkokala", "entity.minecraft.tropical_fish.predefined.19": "S<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.2": "Palettivälskäri", "entity.minecraft.tropical_fish.predefined.20": "Keltahäntä papukaijakala", "entity.minecraft.tropical_fish.predefined.21": "Keltavälskäri", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON> kala", "entity.minecraft.tropical_fish.predefined.6": "Hattarataistelukala", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Keisarinapsija", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "Taistelukala", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Savikala", "entity.minecraft.tropical_fish.type.dasher": "Syöksijä", "entity.minecraft.tropical_fish.type.flopper": "Sätkijä", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Nuuskija", "entity.minecraft.tropical_fish.type.spotty": "Täplikä<PERSON>", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Aurin<PERSON><PERSON><PERSON>", "entity.minecraft.turtle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vex": "Pi<PERSON>", "entity.minecraft.villager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.armorer": "Haarniskaseppä", "entity.minecraft.villager.butcher": "Teurastaja", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "Maanviljelijä", "entity.minecraft.villager.fisherman": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.fletcher": "Jous<PERSON><PERSON><PERSON>", "entity.minecraft.villager.leatherworker": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.librarian": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.mason": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.nitwit": "Pölkkypää", "entity.minecraft.villager.none": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.shepherd": "Lammaspaimen", "entity.minecraft.villager.toolsmith": "Työkaluseppä", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.witch": "<PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "Zombihevonen", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.zombified_piglin": "Zombipiglin", "entity.not_summonable": "Tyypin %s oliota ei voi kutsua", "event.minecraft.raid": "Hyökkäys", "event.minecraft.raid.defeat": "Häviö", "event.minecraft.raid.defeat.full": "Hyökkäys – Tappio", "event.minecraft.raid.raiders_remaining": "Hyökkääjiä jäljellä: %s", "event.minecraft.raid.victory": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.victory.full": "Hyökkäys – Voitto", "filled_map.buried_treasure": "Ha<PERSON>tun aarteen kartta", "filled_map.explorer_jungle": "Viidakon se<PERSON>lijak<PERSON>", "filled_map.explorer_swamp": "<PERSON><PERSON>", "filled_map.id": "Id #%s", "filled_map.level": "(Taso %s/%s)", "filled_map.locked": "Lukittu", "filled_map.mansion": "<PERSON><PERSON><PERSON><PERSON>", "filled_map.monument": "Valtameren se<PERSON>kailijakartta", "filled_map.scale": "Mittakaavassa 1:%s", "filled_map.trial_chambers": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "filled_map.unknown": "Tuntematon kartta", "filled_map.village_desert": "Aavikkokylän kartta", "filled_map.village_plains": "Tasankokylän kartta", "filled_map.village_savanna": "<PERSON>van<PERSON><PERSON><PERSON>n kartta", "filled_map.village_snowy": "Lumikylän kartta", "filled_map.village_taiga": "Taigakylän kartta", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "flat_world_preset.minecraft.classic_flat": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.overworld": "Y<PERSON>ä<PERSON>ailma", "flat_world_preset.minecraft.redstone_ready": "<PERSON><PERSON><PERSON> redstonelle", "flat_world_preset.minecraft.snowy_kingdom": "Luminen valtakunta", "flat_world_preset.minecraft.the_void": "Tyhjyys", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON><PERSON> une<PERSON>", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "Seikkailutila", "gameMode.changed": "<PERSON><PERSON><PERSON><PERSON><PERSON> on vaihdettu %s", "gameMode.creative": "Luovuustila", "gameMode.hardcore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.spectator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gameMode.survival": "Selviytymistila", "gamerule.allowFireTicksAwayFromPlayer": "Päivitä tuli kaukana pelaajista", "gamerule.allowFireTicksAwayFromPlayer.description": "Hallitsee tulen ja laavan vaikutusten päivittymistä yli 8 lohkon etäisyydellä pelaajista", "gamerule.announceAdvancements": "<PERSON><PERSON><PERSON>", "gamerule.blockExplosionDropDecay": "Esineiden katoaminen kuutioiden käyttämisestä seuraavissa räjähdyksissä", "gamerule.blockExplosionDropDecay.description": "Osa kuutioiden käyttämisen seurauksena räjähtävien kuutioiden pudottamista esineistä katoaa räjähdyksessä.", "gamerule.category.chat": "Keskustelu", "gamerule.category.drops": "<PERSON><PERSON><PERSON>", "gamerule.category.misc": "Sekalaiset", "gamerule.category.mobs": "<PERSON><PERSON><PERSON>", "gamerule.category.player": "<PERSON><PERSON><PERSON>", "gamerule.category.spawning": "Syntyminen", "gamerule.category.updates": "Maailmapäivitykset", "gamerule.commandBlockOutput": "Näytä komentokuutioiden ulostulo", "gamerule.commandModificationBlockLimit": "<PERSON><PERSON><PERSON><PERSON> muokattavien kuutioiden raj<PERSON>us", "gamerule.commandModificationBlockLimit.description": "<PERSON><PERSON><PERSON><PERSON> komennolla, kuten /fill tai /clone, muokattavien kuutioiden enimmäismäärä.", "gamerule.disableElytraMovementCheck": "Poista elytran lii<PERSON>ku<PERSON>", "gamerule.disablePlayerMovementCheck": "Poista pelaajan liikkumistarkistus", "gamerule.disableRaids": "Poista hyökkäykset käytöstä", "gamerule.doDaylightCycle": "<PERSON><PERSON> ajan kulku", "gamerule.doEntityDrops": "<PERSON><PERSON><PERSON> k<PERSON> varustus", "gamerule.doEntityDrops.description": "Hallitsee ka<PERSON>skärry<PERSON>ä (mukaan lukien tavaraluetteloista), k<PERSON><PERSON><PERSON><PERSON>, veneistä yms. putoavia esineitä", "gamerule.doFireTick": "Päivitä tuli", "gamerule.doImmediateRespawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.doInsomnia": "<PERSON><PERSON> a<PERSON>", "gamerule.doLimitedCrafting": "Vaadi resepti työstämiseen", "gamerule.doLimitedCrafting.description": "<PERSON><PERSON>, pela<PERSON>t voivat työstää vain avattuja reseptejä.", "gamerule.doMobLoot": "<PERSON><PERSON><PERSON> o<PERSON> sa<PERSON>", "gamerule.doMobLoot.description": "<PERSON><PERSON><PERSON> o<PERSON><PERSON>n pu<PERSON> resursseja, mukaan lukien koke<PERSON>.", "gamerule.doMobSpawning": "<PERSON><PERSON>", "gamerule.doMobSpawning.description": "Jo<PERSON>kin olennoilla saattaa olla erilliset säännöt.", "gamerule.doPatrolSpawning": "<PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON>", "gamerule.doTileDrops": "<PERSON><PERSON><PERSON> palikat", "gamerule.doTileDrops.description": "<PERSON><PERSON><PERSON> kuutioiden pudottamia resursseja, mukaan lukien kokemuspalloja.", "gamerule.doTraderSpawning": "<PERSON><PERSON>", "gamerule.doVinesSpread": "Köynnökset leviävät", "gamerule.doVinesSpread.description": "Hallitsee tavallisten köynnösten leviämistä viereisiin kuuti<PERSON>hin. Ei vaikuta muuntyyppisiin köynnöksiin, kuten itku<PERSON>ynnöksiin, kieroköynnöksiin jne.", "gamerule.doWardenSpawning": "<PERSON><PERSON><PERSON>", "gamerule.doWeatherCycle": "<PERSON>li säätilojen muutokset", "gamerule.drowningDamage": "<PERSON><PERSON>", "gamerule.enderPearlsVanishOnDeath": "He<PERSON><PERSON>t ender-helmet ka<PERSON><PERSON><PERSON> k<PERSON>", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON><PERSON> pelaajan heittämät ender-helmet, kun pelaaja kuolee?", "gamerule.entitiesWithPassengersCanUsePortals": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON> on ratsastajia, voivat käyttää portaaleja", "gamerule.entitiesWithPassengersCanUsePortals.description": "<PERSON><PERSON> o<PERSON> teleporttaaminen nether-portaalien, end-portaalien ja end-port<PERSON> läpi ratsast<PERSON>ien kanssa.", "gamerule.fallDamage": "<PERSON><PERSON>", "gamerule.fireDamage": "<PERSON><PERSON> t<PERSON>", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> p<PERSON>t", "gamerule.forgiveDeadPlayers.description": "Vihaiset neutraaliolennot lopettavat vihais<PERSON> o<PERSON>, jos kohdis<PERSON>ttu pelaaja kuolee l<PERSON>.", "gamerule.freezeDamage": "<PERSON><PERSON>", "gamerule.globalSoundEvents": "Globaalit äänitapahtumat", "gamerule.globalSoundEvents.description": "Tiet<PERSON>jen peli<PERSON>htumien, kuten päävastustajan luomisen yhteydessä toistettavat äänet kuuluvat kaik<PERSON>alla.", "gamerule.keepInventory": "<PERSON><PERSON><PERSON> tavarat kuoleman j<PERSON>lk<PERSON>", "gamerule.lavaSourceConversion": "<PERSON><PERSON> muuttuu l<PERSON>", "gamerule.lavaSourceConversion.description": "<PERSON>n virtaavaa laavaa ympä<PERSON>öi lähde kahdella sivulla, se muuttuu myö<PERSON> läht<PERSON>.", "gamerule.locatorBar": "<PERSON><PERSON> p<PERSON>palk<PERSON> k<PERSON>yttöön", "gamerule.locatorBar.description": "<PERSON><PERSON> käyt<PERSON>ä, näytöllä näytetään palkki pelaajien suunnan osoittamiseksi.", "gamerule.logAdminCommands": "Kuuluta ylläpitäjäkomennot", "gamerule.maxCommandChainLength": "Komentoketjujen enimmäispituus", "gamerule.maxCommandChainLength.description": "<PERSON>ai<PERSON><PERSON>a komentokuution komentoketjuihin ja -to<PERSON><PERSON><PERSON><PERSON>.", "gamerule.maxCommandForkCount": "Komentoasiayhteyksien yläraja", "gamerule.maxCommandForkCount.description": "Enimmäismäär<PERSON> as<PERSON>, joita <PERSON>aan käyttää komennoissa kuten 'execute as'.", "gamerule.maxEntityCramming": "Kasautuvien kohteiden enimmäismäärä", "gamerule.minecartMaxSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> enim<PERSON>us", "gamerule.minecartMaxSpeed.description": "<PERSON><PERSON><PERSON> lii<PERSON> ka<PERSON>unun oletusenimmäisnopeus.", "gamerule.mobExplosionDropDecay": "Esineiden katoaminen olennoista johtuvissa räjähdyksissä", "gamerule.mobExplosionDropDecay.description": "<PERSON><PERSON> o<PERSON>n toim<PERSON>an se<PERSON>uksena räjähtävien kuutioiden pudottamista esineistä katoaa räjähdyksessä.", "gamerule.mobGriefing": "<PERSON><PERSON> tuh<PERSON>at olent<PERSON>ot", "gamerule.naturalRegeneration": "Sydämien palautuminen", "gamerule.playersNetherPortalCreativeDelay": "<PERSON><PERSON><PERSON><PERSON>-portaaliviive luovassa tilassa", "gamerule.playersNetherPortalCreativeDelay.description": "<PERSON><PERSON> (pyk<PERSON><PERSON><PERSON>), jonka luo<PERSON><PERSON> tilassa olevan pelaajan tulee seistä Nether-portaalissa ennen toiseen ulottuvuuteen siirtymistä.", "gamerule.playersNetherPortalDefaultDelay": "<PERSON><PERSON><PERSON><PERSON>-portaaliviive muussa kuin luovassa tilassa", "gamerule.playersNetherPortalDefaultDelay.description": "<PERSON><PERSON> (pyk<PERSON><PERSON><PERSON>), jonka muussa kuin luovassa tilassa olevan pelaajan tulee seistä Nether-portaalissa ennen toiseen ulottuvuuteen siirtymistä.", "gamerule.playersSleepingPercentage": "<PERSON>uk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.playersSleepingPercentage.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joiden t<PERSON> olla nuk<PERSON> yön <PERSON>.", "gamerule.projectilesCanBreakBlocks": "Ammukset voivat rikkoa kuutioita", "gamerule.projectilesCanBreakBlocks.description": "<PERSON><PERSON><PERSON>, tuhoavatko ammukset niiden tuhottavissa olevia kuutioita.", "gamerule.randomTickSpeed": "Satunnainen pykälänopeustaajuus", "gamerule.reducedDebugInfo": "Vähennä virheenkorjaustietoja", "gamerule.reducedDebugInfo.description": "<PERSON><PERSON><PERSON><PERSON> vir<PERSON>nkorjausnäkymän si<PERSON>ältöä.", "gamerule.sendCommandFeedback": "Lähetä komentopalautetta", "gamerule.showDeathMessages": "Näytä kuolinviestit", "gamerule.snowAccumulationHeight": "<PERSON><PERSON> k<PERSON>", "gamerule.snowAccumulationHeight.description": "Satava lumi kasautuu maahan enintään näin moneksi kerrokseksi.", "gamerule.spawnChunkRadius": "Syntymälohkon koko", "gamerule.spawnChunkRadius.description": "Ylämaailman syntymäpaikan lähellä ladattuna pysyvien lohkojen määrä.", "gamerule.spawnRadius": "Uudelleensyntymissi<PERSON><PERSON><PERSON>", "gamerule.spawnRadius.description": "Hallitsee syntymäpisteen ympärillä olevan al<PERSON>en koko<PERSON>, johon pela<PERSON>t voivat syntyä.", "gamerule.spectatorsGenerateChunks": "<PERSON><PERSON> luoda maastoa", "gamerule.tntExplodes": "Salli TNT:n aktivoiminen ja räjähtäminen", "gamerule.tntExplosionDropDecay": "Esineiden katoaminen TNT:n räjähdyksissä", "gamerule.tntExplosionDropDecay.description": "Osa TNT:n räjäyttämien kuutioiden pudottamista esineistä katoaa räjähdyksessä.", "gamerule.universalAnger": "Universaali viha", "gamerule.universalAnger.description": "Vihaiset neutraaliolennot hyökkäävät kaikkia lähellä olevia pelaajia vastaan, eikä vain ne suututtaneeseen pelaajaan. <PERSON><PERSON><PERSON>, jos forgiveDeadPlayers on poissa käytöstä.", "gamerule.waterSourceConversion": "<PERSON>esi muuttuu l<PERSON>", "gamerule.waterSourceConversion.description": "Kun virtaavaa vettä ympäröi lähde kahdella sivulla, se muuttuu myö<PERSON> läht<PERSON>.", "generator.custom": "<PERSON><PERSON>", "generator.customized": "<PERSON><PERSON> m<PERSON>u", "generator.minecraft.amplified": "LAAJENNETTU", "generator.minecraft.amplified.info": "Huomio: <PERSON><PERSON> vuo<PERSON>! Vaatii tehokkaan tie<PERSON>en.", "generator.minecraft.debug_all_block_states": "Debug-tila", "generator.minecraft.flat": "<PERSON><PERSON><PERSON>", "generator.minecraft.large_biomes": "Suuret biomit", "generator.minecraft.normal": "<PERSON><PERSON>", "generator.minecraft.single_biome_surface": "Yksi biomi", "generator.single_biome_caves": "<PERSON><PERSON><PERSON>", "generator.single_biome_floating_islands": "Leijuvat saaret", "gui.abuseReport.attestation": "Lähettämällä tämän raportin vahvistat, että antamasi tiedot ovat parhaan tietä<PERSON>ks<PERSON> mukaan oikeita ja täydellisiä.", "gui.abuseReport.comments": "Ko<PERSON>ntit", "gui.abuseReport.describe": "Yks<PERSON><PERSON><PERSON><PERSON> kertominen voi auttaa meitä parhaan mahdollisen valinnan tekemisessä.", "gui.abuseReport.discard.content": "<PERSON><PERSON>, menetät tämän ilmoit<PERSON>sen ja siihen liittyvät kommentit.\n<PERSON><PERSON><PERSON><PERSON> varmasti poistua?", "gui.abuseReport.discard.discard": "<PERSON><PERSON>u ja hylkää ilmoitus", "gui.abuseReport.discard.draft": "<PERSON><PERSON><PERSON>", "gui.abuseReport.discard.return": "Jat<PERSON> muokkausta", "gui.abuseReport.discard.title": "Hylk<PERSON>ä ilmoitus ja kommentit?", "gui.abuseReport.draft.content": "Haluaisitko jatkaa olemassa olevan il<PERSON> muokaamista vai luoda kokonaan uuden?", "gui.abuseReport.draft.discard": "Hylkää", "gui.abuseReport.draft.edit": "Jat<PERSON> muokkausta", "gui.abuseReport.draft.quittotitle.content": "Haluaisitko jatkaa sen muokkaamista vai hylätä sen?", "gui.abuseReport.draft.quittotitle.title": "<PERSON><PERSON><PERSON> on keskustelun il<PERSON> luonn<PERSON>, jonka menetät poistuessasi", "gui.abuseReport.draft.title": "Muokkaa keskustelun ilmoituksen luonnosta?", "gui.abuseReport.error.title": "Ongelma ilmoituksesi lähettämisessä", "gui.abuseReport.message": "Missä kohtasit sopimatonta käytöstä?\nTämä auttaa meitä tutkimaan tapaustasi.", "gui.abuseReport.more_comments": "<PERSON><PERSON><PERSON> tapah<PERSON>ia:", "gui.abuseReport.name.comment_box_label": "<PERSON><PERSON>, miksi haluat ilmoittaa tästä nimestä:", "gui.abuseReport.name.reporting": "<PERSON><PERSON> pela<PERSON> \"%s\".", "gui.abuseReport.name.title": "<PERSON><PERSON><PERSON> p<PERSON>n ni<PERSON>", "gui.abuseReport.observed_what": "Miksi il<PERSON>t tästä?", "gui.abuseReport.read_info": "Lue lisää ilmoittamisesta", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Huumeet tai alkoholi", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Jo<PERSON> kannustaa muita osallistumaan laittomiin huumeisiin liittyvään toimintaan tai rohkaisee alaikäisiä juomaan alkoholia.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Lapsen seksuaalinen hyväksikäyttö tai pahoinpitely", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Joku puhuu lapsiin liittyvästä sopimattomasta käyttäytymisestä tai rohkaisee siihen.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Joku vahingoittaa sinun tai jonkun toisen mainetta esimerkiksi jakamalla väärää tietoa tarkoituksenaan hyväksikäyttää tai harhaanjohtaa muita.", "gui.abuseReport.reason.description": "Kuvaus:", "gui.abuseReport.reason.false_reporting": "V<PERSON><PERSON><PERSON><PERSON> il<PERSON>uk<PERSON>", "gui.abuseReport.reason.generic": "<PERSON>uan il<PERSON> hänestä", "gui.abuseReport.reason.generic.description": "<PERSON>än ärs<PERSON>ä minua/ hän on tehnyt jotain, mistä en pidä.", "gui.abuseReport.reason.harassment_or_bullying": "Häirintä tai kiusaaminen", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ah<PERSON><PERSON><PERSON> tai kiusaa sinua tai jotakuta toista. <PERSON><PERSON><PERSON><PERSON><PERSON> lukeutuvat my<PERSON> til<PERSON>, joissa joku yrittää toistuvasti ottaa yhteyttä sinuun tai johonkuhun toiseen ilman suostumusta tai julkaisee yksityisiä henkilötietoja sinusta tai jostakusta toisesta ilman suostumusta (nk. \"doksaus\").", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.reason.hate_speech.description": "Joku h<PERSON>ökkää sinua tai toista pelaajaa kohtaan identiteettiin liittyvien ominaisuuksien, kuten us<PERSON>, rodun tai seksuaalisuuden perust<PERSON>.", "gui.abuseReport.reason.imminent_harm": "<PERSON><PERSON> vahi<PERSON>aa muita", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON> uhkaa vahingoittaa sinua tai jotakuta toista pelin ulkopuolella.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Suostumuksenvastainen intiimi kuvasto", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Joku puhuu yksityisistä ja intiimeistä kuvista taikka jakaa tai mainostaa niihin liittyvää sisältöä.", "gui.abuseReport.reason.self_harm_or_suicide": "Itsensä vahingoittaminen tai itsemurha", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON><PERSON> uhkaa vahingoittavansa itse<PERSON>än pelin ulkopuolella tai puhuu itsensä vahingoittamisesta pelin ulkopuolella.", "gui.abuseReport.reason.sexually_inappropriate": "Seksuaalisesti sopimaton", "gui.abuseReport.reason.sexually_inappropriate.description": "Seks<PERSON><PERSON><PERSON><PERSON> te<PERSON>, su<PERSON><PERSON>olielimiin ja seksuaaliseen väkivaltaan liittyvät graafiset skinit.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Terrorismi tai väkivaltaiset ääriliikkeet", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Jo<PERSON> puhuu terroriteoista taikka poliitt<PERSON>, uskonnollisesta, ideologisesta tai muusta väkivaltaisesta ääriliikkeestä, kanna<PERSON>a niitä tai uhkailee niihin liittyvillä teoilla.", "gui.abuseReport.reason.title": "Valitse ilmoituksen syy", "gui.abuseReport.report_sent_msg": "Olemme vastaanott<PERSON>et ilmoituksesi onnistuneesti. Kiitos!\n\nTiimimme tarkastaa sen mahdollisimman pian.", "gui.abuseReport.select_reason": "Valitse ilmoituskategoria", "gui.abuseReport.send": "Lähetä ilmoitus", "gui.abuseReport.send.comment_too_long": "Lyhennä kommenttia", "gui.abuseReport.send.error_message": "Ilmoituksesi lähettämisessä tapahtui virhe:\n'%s'", "gui.abuseReport.send.generic_error": "Ilmoituksesi lähettämisessä tapahtui odottamaton virhe.", "gui.abuseReport.send.http_error": "Ilmoituksesi lähettämisessä tapahtui odottamaton HTTP-virhe.", "gui.abuseReport.send.json_error": "Ilmoitustasi lähettäessä havaittiin virheellistä tietosisältöä.", "gui.abuseReport.send.no_reason": "Valitse ilmoituskategoria", "gui.abuseReport.send.not_attested": "Lue yläpuolella oleva teksti ja klikkaa valintaruutua voidaksesi lähettää ilmoituksen", "gui.abuseReport.send.service_unavailable": "Väärinkäyttöilmoituspalvelun tavoittaminen epäonnistui. Varmista, että olet yhteydessä internetiin, ja yritä uudelleen.", "gui.abuseReport.sending.title": "Lähetetään ilmoitustasi...", "gui.abuseReport.sent.title": "<PERSON><PERSON><PERSON><PERSON>", "gui.abuseReport.skin.title": "<PERSON><PERSON><PERSON> p<PERSON> skin<PERSON>", "gui.abuseReport.title": "<PERSON><PERSON><PERSON>", "gui.abuseReport.type.chat": "Keskusteluviestit", "gui.abuseReport.type.name": "<PERSON><PERSON><PERSON><PERSON> nimi", "gui.abuseReport.type.skin": "<PERSON><PERSON><PERSON><PERSON> skini", "gui.acknowledge": "Hyväksy", "gui.advancements": "Edistysaskeleet", "gui.all": "<PERSON><PERSON><PERSON>", "gui.back": "<PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n\n%s\n\nLue lisää seuraavasta linkistä: %s", "gui.banned.description.permanent": "T<PERSON>i on estetty pysyvästi, joten et voi pelata verkossa tai liittyä Realmsiin.", "gui.banned.description.reason": "Olemme vastaanott<PERSON>et ilmoituksen käyttäjätilisi huonosta käytöksestä. Moderaattorimme ovat nyt tarkastaneet tapauksesi ja todenneet sen kuuluvan kategoriaan %s, joka on Minecraftin yhteisön normien vastaista.", "gui.banned.description.reason_id": "Koodi: %s", "gui.banned.description.reason_id_message": "Koodi: %s – %s", "gui.banned.description.temporary": "%s <PERSON><PERSON><PERSON> et voi pelata verkossa tai liittyä Realmsiin.", "gui.banned.description.temporary.duration": "Tilisi on jäädytetty väliaikaisesti. Se aktivoidaan uudelleen %s kuluttua.", "gui.banned.description.unknownreason": "Olemme vastaanott<PERSON>et ilmoituksen käyttäjätilisi huonosta käytöksestä. Moderaattorimme ovat nyt tarkastaneet tapauksesi ja todenneet sen olevan Minecraftin yhteisön normien vastaista.", "gui.banned.name.description": "Nykyinen nimesi – \"%s\" – r<PERSON><PERSON>o Yhteisömme normeja. V<PERSON> pelata yksinpeliä, mutta pelataksesi netissä sinun on muutettava nimeäsi.\n\nLue lisää tai lähetä korjaushakemus seuraavasta linkistä: %s", "gui.banned.name.title": "<PERSON><PERSON> ei ole sallittu mon<PERSON>pel<PERSON>", "gui.banned.reason.defamation_impersonation_false_information": "<PERSON><PERSON>na henkilönä esiintyminen taikka tietojen jakaminen toisten hyväksikäyttämiseksi tai harhaanjohtamiseksi", "gui.banned.reason.drugs": "<PERSON><PERSON>ttaukset laittomiin huumausaineisiin", "gui.banned.reason.extreme_violence_or_gore": "Liiallisen väkivallan tai verenvuodatuksen esittäminen", "gui.banned.reason.false_reporting": "Liiallisten väärien tai virheellisten ilmoitusten luominen", "gui.banned.reason.fraud": "Sisällön vilpillinen hankkiminen tai käyttö", "gui.banned.reason.generic_violation": "<PERSON><PERSON><PERSON>sön normien rikkominen", "gui.banned.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON><PERSON><PERSON> ja ha<PERSON>n louk<PERSON> kielenkäyttö", "gui.banned.reason.hate_speech": "Vihapuhe tai syrjintä", "gui.banned.reason.hate_terrorism_notorious_figure": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>, terroristijärjestöihin tai pahamaineisiin henk<PERSON>", "gui.banned.reason.imminent_harm_to_person_or_property": "Tarkoitus aiheuttaa todellista vahinkoa henkilöille tai omaisuudelle", "gui.banned.reason.nudity_or_pornography": "Siveettömän tai pornografisen materiaalin esittäminen", "gui.banned.reason.sexually_inappropriate": "Seksuaaliset aiheet tai sisältö", "gui.banned.reason.spam_or_advertising": "Roskapostaus tai mainonta", "gui.banned.skin.description": "Nykyinen skinisi rikkoo Yhteisömme normeja. Voit pelata <PERSON>, tai valita uuden.\n\nLue lisää tai lähetä korjaushakemus seuraavasta linkistä: %s", "gui.banned.skin.title": "Skini ei sallittu", "gui.banned.title.permanent": "<PERSON><PERSON> estetty pysyvästi", "gui.banned.title.temporary": "<PERSON><PERSON> j<PERSON>", "gui.cancel": "Peruuta", "gui.chatReport.comments": "Ko<PERSON>ntit", "gui.chatReport.describe": "Yks<PERSON><PERSON><PERSON><PERSON> kertominen voi auttaa meitä parhaan mahdollisen valinnan tekemisessä.", "gui.chatReport.discard.content": "<PERSON><PERSON>, menetät tämän ilmoit<PERSON>sen ja siihen liittyvät kommentit.\n<PERSON><PERSON><PERSON><PERSON> varmasti poistua?", "gui.chatReport.discard.discard": "<PERSON><PERSON>u ja hylkää ilmoitus", "gui.chatReport.discard.draft": "<PERSON><PERSON><PERSON>", "gui.chatReport.discard.return": "Jatka muokkaamista", "gui.chatReport.discard.title": "Hylk<PERSON>ä ilmoitus ja kommentit?", "gui.chatReport.draft.content": "Haluaisitko jatkaa olemassa olevan il<PERSON> muokaamista vai luoda kokonaan uuden?", "gui.chatReport.draft.discard": "Hylkää", "gui.chatReport.draft.edit": "Jatka muokkaamista", "gui.chatReport.draft.quittotitle.content": "Haluaisitko jatkaa sen muokkaamista vai hylätä sen?", "gui.chatReport.draft.quittotitle.title": "<PERSON><PERSON><PERSON> on keskustelun il<PERSON> luonn<PERSON>, jonka menetät poistuessasi", "gui.chatReport.draft.title": "Muokkaa keskustelun ilmoituksen luonnosta?", "gui.chatReport.more_comments": "<PERSON><PERSON><PERSON> tapah<PERSON>ia:", "gui.chatReport.observed_what": "<PERSON>ksi haluat il<PERSON>ittaa tästä?", "gui.chatReport.read_info": "Lue lisää ilmoittamisesta", "gui.chatReport.report_sent_msg": "Olemme vastaanott<PERSON>et ilmoituksesi onnistuneesti. Kiitos!\n\nTiimimme tarkastaa sen mahdollisimman pian.", "gui.chatReport.select_chat": "<PERSON><PERSON><PERSON> viestit, joista haluat il<PERSON>aa", "gui.chatReport.select_reason": "Valitse ilmoituksen syy", "gui.chatReport.selected_chat": "%s viesti(ä) valittu ilmoitukseen", "gui.chatReport.send": "Lähetä ilmoitus", "gui.chatReport.send.comments_too_long": "Lyhennä kommenttia", "gui.chatReport.send.no_reason": "Valitse ilmoituksen syy", "gui.chatReport.send.no_reported_messages": "Valitse vähintään yksi viesti, josta haluat il<PERSON>", "gui.chatReport.send.too_many_messages": "Yrität sisällyttää ilmoitukseen liian monta viestiä", "gui.chatReport.title": "<PERSON><PERSON><PERSON>", "gui.chatSelection.context": "Tätä valintaa ympäröivät viestit lisätään mukaan asiayhteyden täydentämiseksi", "gui.chatSelection.fold": "%s viesti(ä) piilotettu", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s liittyi keskusteluun", "gui.chatSelection.message.narrate": "%s sanoi: %s, %s", "gui.chatSelection.selected": "%s/%s viesti(ä) valittu", "gui.chatSelection.title": "<PERSON><PERSON><PERSON> viestit, joista haluat il<PERSON>aa", "gui.continue": "Jatka", "gui.copy_link_to_clipboard": "<PERSON><PERSON><PERSON> leikepöydälle", "gui.days": "%s päivää", "gui.done": "Val<PERSON>", "gui.down": "Alas", "gui.entity_tooltip.type": "Tyyppi: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Hylättiin %s kansiota", "gui.fileDropFailure.title": "Tiedostojen lisääminen epäonnistui", "gui.hours": "%s tuntia", "gui.loadingMinecraft": "Ladataan Minecraftia", "gui.minutes": "%s minuuttia", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s-nappi", "gui.narrate.editBox": "%s muokkaa ruutua: %s", "gui.narrate.slider": "%s-liu'utin", "gui.narrate.tab": "Välilehti: %s", "gui.no": "<PERSON>i", "gui.none": "<PERSON><PERSON> mi<PERSON>n", "gui.ok": "Ok", "gui.open_report_dir": "<PERSON><PERSON>", "gui.proceed": "Jatka", "gui.recipebook.moreRecipes": "<PERSON><PERSON><PERSON><PERSON> hiiren oikeaa painiketta nähdäksesi lisää", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Hae...", "gui.recipebook.toggleRecipes.all": "Näytetään kaikki", "gui.recipebook.toggleRecipes.blastable": "Näytetään ma<PERSON> sulatettavat", "gui.recipebook.toggleRecipes.craftable": "Näytetään työstettävät", "gui.recipebook.toggleRecipes.smeltable": "Näytetään su<PERSON>", "gui.recipebook.toggleRecipes.smokable": "Näytetään sa<PERSON>", "gui.report_to_server": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.blocking_hint": "Hallinnoi Microsoft-tilillä", "gui.socialInteractions.empty_blocked": "Ei estetty<PERSON>ä pelaajia keskustelussa", "gui.socialInteractions.empty_hidden": "Ei piilotettuja pelaajia keskustelussa", "gui.socialInteractions.hidden_in_chat": "Pelaajan %s viestit piilotetaan", "gui.socialInteractions.hide": "Piilota keskustelussa", "gui.socialInteractions.narration.hide": "Piilota viestit pelaajalta %s", "gui.socialInteractions.narration.report": "Ilmoita pelaajasta %s", "gui.socialInteractions.narration.show": "Näytä viestit pelaajalta %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "<PERSON><PERSON><PERSON><PERSON>n pela<PERSON>a ei löytynyt tällä ni<PERSON>ä", "gui.socialInteractions.search_hint": "Hae...", "gui.socialInteractions.server_label.multiple": "%s – %s pelaajaa", "gui.socialInteractions.server_label.single": "%s – %s pelaaja", "gui.socialInteractions.show": "Näytä keskustelussa", "gui.socialInteractions.shown_in_chat": "Pelaajan %s viestit näytetään", "gui.socialInteractions.status_blocked": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_blocked_offline": "Estetty <PERSON> <PERSON>i pai<PERSON>la", "gui.socialInteractions.status_hidden": "Piilotettu", "gui.socialInteractions.status_hidden_offline": "Piilotettu – Ei paikalla", "gui.socialInteractions.status_offline": "<PERSON>i paikalla", "gui.socialInteractions.tab_all": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "Piilotettu", "gui.socialInteractions.title": "Sosiaaliset kanssakäymiset", "gui.socialInteractions.tooltip.hide": "Piilota viestit", "gui.socialInteractions.tooltip.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report.disabled": "Ilmoituspalvelu ei ole k<PERSON>ytettävissä", "gui.socialInteractions.tooltip.report.no_messages": "Ei ilmoitettaviksi kelpaavia viestejä pelaajalta %s", "gui.socialInteractions.tooltip.report.not_reportable": "Tästä pelaajasta ei voi ilmoittaa, koska hänen keskusteluviestejään ei voida todentaa tällä palvelimella", "gui.socialInteractions.tooltip.show": "Näytä viestit", "gui.stats": "Tilastot", "gui.toMenu": "<PERSON><PERSON><PERSON> p<PERSON>", "gui.toRealms": "Takaisin Realms-listaan", "gui.toTitle": "<PERSON><PERSON><PERSON> päävalikkoon", "gui.toWorld": "<PERSON><PERSON><PERSON>", "gui.togglable_slot": "Klikkaa poistaaksesi paikan k<PERSON>ö<PERSON>ä", "gui.up": "<PERSON><PERSON><PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "<PERSON><PERSON><PERSON> (%s s)", "gui.waitingForResponse.title": "Odotetaan palvelinta", "gui.yes": "K<PERSON><PERSON>ä", "hanging_sign.edit": "Muokkaa roikkuvan kyltin te<PERSON>", "instrument.minecraft.admire_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.call_goat_horn": "Kutsu", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Etsintä", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "Kai<PERSON><PERSON>", "inventory.binSlot": "<PERSON><PERSON><PERSON> esine", "inventory.hotbarInfo": "<PERSON><PERSON>na p<PERSON> %1$s+%2$s", "inventory.hotbarSaved": "Pikapalkki tallennettu (palauta pain<PERSON>lla %1$s+%2$s)", "item.canBreak": "Voi rikkoa:", "item.canPlace": "<PERSON><PERSON><PERSON> si<PERSON>:", "item.canUse.unknown": "Tuntematon", "item.color": "Väri: %s", "item.components": "%s komponentti(a)", "item.disabled": "Käytöstä poistettu esine", "item.durability": "Kestävyys: %s / %s", "item.dyed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "Akaasiavene", "item.minecraft.acacia_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON> a<PERSON>i", "item.minecraft.amethyst_shard": "Ametistisirpale", "item.minecraft.angler_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.angler_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.apple": "Omena", "item.minecraft.archer_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.archer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.armadillo_scute": "Vyötiäisen suomu", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON>", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_shard": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.arms_up_pottery_sherd": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "Aksolotli ämpärissä", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON>", "item.minecraft.baked_potato": "<PERSON><PERSON><PERSON><PERSON> peruna", "item.minecraft.bamboo_chest_raft": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>a", "item.minecraft.bamboo_raft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bat_spawn_egg": "<PERSON><PERSON>", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON>", "item.minecraft.beef": "<PERSON><PERSON> p<PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "Koivuvene", "item.minecraft.birch_chest_boat": "Koivuvene arkulla", "item.minecraft.black_bundle": "<PERSON><PERSON> pussi", "item.minecraft.black_dye": "<PERSON><PERSON> vä<PERSON>", "item.minecraft.black_harness": "Mustat valjaat", "item.minecraft.blade_pottery_shard": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.blade_pottery_sherd": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.blaze_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.blaze_spawn_egg": "<PERSON><PERSON> roihu", "item.minecraft.blue_bundle": "<PERSON><PERSON> pussi", "item.minecraft.blue_dye": "<PERSON><PERSON> väriaine", "item.minecraft.blue_egg": "<PERSON><PERSON> muna", "item.minecraft.blue_harness": "<PERSON><PERSON><PERSON> v<PERSON>", "item.minecraft.bogged_spawn_egg": "<PERSON><PERSON>", "item.minecraft.bolt_armor_trim_smithing_template": "Taontamalli", "item.minecraft.bolt_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.bone": "<PERSON><PERSON>", "item.minecraft.bone_meal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.book": "<PERSON><PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Hammaskororeunainen viirikuvio", "item.minecraft.bow": "<PERSON><PERSON><PERSON>", "item.minecraft.bowl": "<PERSON><PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON><PERSON>", "item.minecraft.breeze_rod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "Luo viima", "item.minecraft.brewer_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.brewer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.brick": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON> pussi", "item.minecraft.brown_dye": "Ruskea väriaine", "item.minecraft.brown_egg": "Ruskea muna", "item.minecraft.brown_harness": "Ruskeat valjaat", "item.minecraft.brush": "<PERSON><PERSON><PERSON>", "item.minecraft.bucket": "Ämpäri", "item.minecraft.bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty": "Tyhjä", "item.minecraft.bundle.empty.description": "Voi kantaa sekalaisen pinon esineitä", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON><PERSON> r<PERSON>", "item.minecraft.burn_pottery_sherd": "<PERSON><PERSON> r<PERSON>", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON> kameli", "item.minecraft.carrot": "Porkkana", "item.minecraft.carrot_on_a_stick": "Porkkana kepin päässä", "item.minecraft.cat_spawn_egg": "<PERSON><PERSON> kissa", "item.minecraft.cauldron": "<PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "<PERSON><PERSON>", "item.minecraft.chainmail_boots": "Rengashaarniskan saappaat", "item.minecraft.chainmail_chestplate": "Rengashaarniskan rintapanssari", "item.minecraft.chainmail_helmet": "Rengashaarniskan kypärä", "item.minecraft.chainmail_leggings": "Rengashaarniskan housut", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "Kirsikkapuuvene", "item.minecraft.cherry_chest_boat": "Kirsikkapuuvene arkulla", "item.minecraft.chest_minecart": "<PERSON><PERSON><PERSON> ka<PERSON>", "item.minecraft.chicken": "<PERSON><PERSON> kana", "item.minecraft.chicken_spawn_egg": "<PERSON>o kana", "item.minecraft.chorus_fruit": "Chorus-<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.clay_ball": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.clock": "<PERSON><PERSON>", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Taontamalli", "item.minecraft.coast_armor_trim_smithing_template.new": "Rann<PERSON>n haarniskakoristeet", "item.minecraft.cocoa_beans": "Kaakaopavut", "item.minecraft.cod": "<PERSON><PERSON>", "item.minecraft.cod_bucket": "Turska ämpärissä", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON>", "item.minecraft.command_block_minecart": "Komentokuutiokaivosvaunu", "item.minecraft.compass": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "<PERSON><PERSON><PERSON>", "item.minecraft.cooked_chicken": "<PERSON><PERSON><PERSON><PERSON> kana", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_mutton": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_porkchop": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON><PERSON> lohi", "item.minecraft.cookie": "<PERSON><PERSON><PERSON>", "item.minecraft.copper_ingot": "Ku<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "<PERSON><PERSON>", "item.minecraft.creaking_spawn_egg": "<PERSON><PERSON>", "item.minecraft.creeper_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Creeper-viirikuvio", "item.minecraft.creeper_spawn_egg": "<PERSON><PERSON> creeper", "item.minecraft.crossbow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.crossbow.projectile": "Ammus:", "item.minecraft.crossbow.projectile.multiple": "Ammus: %s x %s", "item.minecraft.crossbow.projectile.single": "Ammus: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON><PERSON> pu<PERSON>", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON> v<PERSON>", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.dark_oak_boat": "<PERSON><PERSON> tammivene", "item.minecraft.dark_oak_chest_boat": "<PERSON>mma tammivene a<PERSON>a", "item.minecraft.debug_stick": "Virheenkorjaustik<PERSON>", "item.minecraft.debug_stick.empty": "Kohteella %s ei ole ominaisuuksia", "item.minecraft.debug_stick.select": "valittu \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" tilaan %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "Timanttisaappaat", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_helmet": "Timanttikypärä", "item.minecraft.diamond_hoe": "Timant<PERSON><PERSON>ok<PERSON>", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON>n he<PERSON> pan<PERSON>ri", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5": "Musiikkilevyn palanen", "item.minecraft.disc_fragment_5.desc": "Musiikkilevy – 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON>", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON> aasi", "item.minecraft.dragon_breath": "Lohikäärmeen <PERSON>", "item.minecraft.dried_kelp": "Kuivattu rakkolevä", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON>", "item.minecraft.dune_armor_trim_smithing_template": "Taontamalli", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.echo_shard": "Kaikusirpal<PERSON>", "item.minecraft.egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON>", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "<PERSON><PERSON>g<PERSON>", "item.minecraft.enchanted_book": "<PERSON><PERSON><PERSON> kirja", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON> k<PERSON>", "item.minecraft.end_crystal": "End-k<PERSON><PERSON><PERSON>", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON> ender-lohikäärme", "item.minecraft.ender_eye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON>", "item.minecraft.endermite_spawn_egg": "<PERSON><PERSON> endermite", "item.minecraft.evoker_spawn_egg": "<PERSON><PERSON>", "item.minecraft.experience_bottle": "<PERSON><PERSON><PERSON> pullo", "item.minecraft.explorer_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.explorer_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template": "Taontamalli", "item.minecraft.eye_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>kor<PERSON>", "item.minecraft.feather": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fermented_spider_eye": "K<PERSON>ynyt hämähäkinsilmä", "item.minecraft.field_masoned_banner_pattern": "Tiilikuvioinen viirikuvio", "item.minecraft.filled_map": "<PERSON><PERSON><PERSON>", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_rocket.flight": "Lentoaika:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Ilotulituspanos", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "<PERSON><PERSON>", "item.minecraft.firework_star.brown": "R<PERSON><PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON>", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Häiv<PERSON>ys", "item.minecraft.firework_star.flicker": "<PERSON><PERSON>", "item.minecraft.firework_star.gray": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.green": "Vihreä", "item.minecraft.firework_star.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.lime": "Limetinvihreä", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.orange": "Orans<PERSON>", "item.minecraft.firework_star.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.purple": "<PERSON><PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Tuntematon kuvio", "item.minecraft.firework_star.shape.burst": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.creeper": "Creeper-kuvioitu", "item.minecraft.firework_star.shape.large_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "Tähtikuvio", "item.minecraft.firework_star.trail": "<PERSON><PERSON>", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "<PERSON><PERSON><PERSON>", "item.minecraft.fishing_rod": "Onkivapa", "item.minecraft.flint": "<PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "Tulukset", "item.minecraft.flow_armor_trim_smithing_template": "Taontamalli", "item.minecraft.flow_armor_trim_smithing_template.new": "Virtauksen haar<PERSON>kakoristeet", "item.minecraft.flow_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flow_banner_pattern.desc": "<PERSON>irt<PERSON>", "item.minecraft.flow_banner_pattern.new": "Virtausviiri<PERSON><PERSON>", "item.minecraft.flow_pottery_sherd": "<PERSON>irt<PERSON><PERSON><PERSON> ru<PERSON>", "item.minecraft.flower_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.flower_pot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON> kettu", "item.minecraft.friend_pottery_shard": "Ystävä<PERSON> ruuku<PERSON>e", "item.minecraft.friend_pottery_sherd": "Ystävä<PERSON> ruuku<PERSON>e", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON>", "item.minecraft.furnace_minecart": "Moottoroitu ka<PERSON>u", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON> ghast", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "<PERSON><PERSON><PERSON><PERSON> melonin si<PERSON>u", "item.minecraft.globe_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.new": "Karttakuutioviirikuvio", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_ink_sac": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "<PERSON><PERSON>", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.goat_horn": "<PERSON><PERSON><PERSON> sarvi", "item.minecraft.goat_spawn_egg": "<PERSON><PERSON> vuohi", "item.minecraft.gold_ingot": "Kultaharkko", "item.minecraft.gold_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_boots": "Kultasaappaat", "item.minecraft.golden_carrot": "Kultaporkkana", "item.minecraft.golden_chestplate": "Kultarintapanssari", "item.minecraft.golden_helmet": "Kultakypärä", "item.minecraft.golden_hoe": "Kultakuokka", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON> hevosen panssari", "item.minecraft.golden_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_shovel": "Kultalapio", "item.minecraft.golden_sword": "Ku<PERSON><PERSON><PERSON>", "item.minecraft.gray_bundle": "<PERSON><PERSON><PERSON> pussi", "item.minecraft.gray_dye": "<PERSON><PERSON><PERSON> v<PERSON>", "item.minecraft.gray_harness": "<PERSON><PERSON><PERSON> valjaat", "item.minecraft.green_bundle": "Vihreä pussi", "item.minecraft.green_dye": "Vihreä väriaine", "item.minecraft.green_harness": "Vihreät valjaat", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON>", "item.minecraft.gunpowder": "Ruuti", "item.minecraft.guster_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON> ghast", "item.minecraft.harness": "Valjaat", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON>", "item.minecraft.heart_pottery_shard": "Sydämen ruuku<PERSON>e", "item.minecraft.heart_pottery_sherd": "Sydämen ruuku<PERSON>e", "item.minecraft.heartbreak_pottery_shard": "<PERSON><PERSON><PERSON><PERSON>n sydämen ruukuns<PERSON>pale", "item.minecraft.heartbreak_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>n sydämen ruukuns<PERSON>pale", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON> hoglin", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.honeycomb": "Hu<PERSON>jak<PERSON><PERSON>", "item.minecraft.hopper_minecart": "Suppilokaivosvaunu", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON>", "item.minecraft.host_armor_trim_smithing_template": "Taontamalli", "item.minecraft.host_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.howl_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.husk_spawn_egg": "<PERSON><PERSON>", "item.minecraft.ink_sac": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_boots": "Rautasaappaat", "item.minecraft.iron_chestplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "<PERSON>o r<PERSON>ago<PERSON>", "item.minecraft.iron_helmet": "Rautakypärä", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "<PERSON>utainen hevosen panssari", "item.minecraft.iron_ingot": "Rautaharkko", "item.minecraft.iron_leggings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_shovel": "Rautalapio", "item.minecraft.iron_sword": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Viidakkopuuvene", "item.minecraft.jungle_chest_boat": "Viidakkopuuvene arkulla", "item.minecraft.knowledge_book": "Tietokirja", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Laavaämpäri", "item.minecraft.lead": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather": "Nahka", "item.minecraft.leather_boots": "Nahkasaappaat", "item.minecraft.leather_chestplate": "Nahkatakki", "item.minecraft.leather_helmet": "Nahkalak<PERSON>", "item.minecraft.leather_horse_armor": "Nahkainen he<PERSON>en pan<PERSON>ri", "item.minecraft.leather_leggings": "Nahkahou<PERSON><PERSON>", "item.minecraft.light_blue_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON> pussi", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON> väriaine", "item.minecraft.light_blue_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "item.minecraft.light_gray_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pussi", "item.minecraft.light_gray_dye": "Vaaleanharmaa vä<PERSON>", "item.minecraft.light_gray_harness": "Vaaleanharmaat valjaat", "item.minecraft.lime_bundle": "Limetinvihreä pussi", "item.minecraft.lime_dye": "Limetinvihreä väriaine", "item.minecraft.lime_harness": "Limetinvihreät valjaat", "item.minecraft.lingering_potion": "Viipyvä taika<PERSON>oma", "item.minecraft.lingering_potion.effect.awkward": "Kiusallinen viipyvä taika<PERSON>oma", "item.minecraft.lingering_potion.effect.empty": "Valmistuskelvoton viipyvä taikajuoma", "item.minecraft.lingering_potion.effect.fire_resistance": "Viipyvä tulenkestävyyden ta<PERSON>juoma", "item.minecraft.lingering_potion.effect.harming": "Viipyvä vahingoittava ta<PERSON>", "item.minecraft.lingering_potion.effect.healing": "Viipyvä parantava taika<PERSON>oma", "item.minecraft.lingering_potion.effect.infested": "Viipyvä sa<PERSON>umi<PERSON> ta<PERSON>", "item.minecraft.lingering_potion.effect.invisibility": "Viipyvä näkymättömyyden ta<PERSON>oma", "item.minecraft.lingering_potion.effect.leaping": "Viipyvä loik<PERSON>amisen ta<PERSON>", "item.minecraft.lingering_potion.effect.levitation": "Viipyvä levitaation taikajuoma", "item.minecraft.lingering_potion.effect.luck": "Viipyvä onnen ta<PERSON>", "item.minecraft.lingering_potion.effect.mundane": "Tavanomainen viipyvä taikajuoma", "item.minecraft.lingering_potion.effect.night_vision": "Viipyvä hämäränäön ta<PERSON>", "item.minecraft.lingering_potion.effect.oozing": "Viipyvä limaisuuden ta<PERSON>", "item.minecraft.lingering_potion.effect.poison": "Viipyvä myrkyllinen ta<PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "Viipyvä elpymisen ta<PERSON>", "item.minecraft.lingering_potion.effect.slow_falling": "Viipyvä hitaan putoamisen taikajuoma", "item.minecraft.lingering_potion.effect.slowness": "Viipyvä hitauden taika<PERSON>oma", "item.minecraft.lingering_potion.effect.strength": "Viipyvä vahvuuden ta<PERSON>", "item.minecraft.lingering_potion.effect.swiftness": "Viipyvä nopeuden taika<PERSON>oma", "item.minecraft.lingering_potion.effect.thick": "Sakea viipyvä taikajuoma", "item.minecraft.lingering_potion.effect.turtle_master": "Viipyvä kilpikonnamestarin taika<PERSON>oma", "item.minecraft.lingering_potion.effect.water": "Viipyvä vesipullo", "item.minecraft.lingering_potion.effect.water_breathing": "Viipyvä vedenhengityksen ta<PERSON>", "item.minecraft.lingering_potion.effect.weakness": "Viipyvä heik<PERSON>uden ta<PERSON>", "item.minecraft.lingering_potion.effect.weaving": "Viipyvä kutomisen ta<PERSON>", "item.minecraft.lingering_potion.effect.wind_charged": "Viipyvä tuulilatauksen ta<PERSON>", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON>", "item.minecraft.lodestone_compass": "Magneettikivikompassi", "item.minecraft.mace": "<PERSON><PERSON><PERSON>", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON><PERSON> pussi", "item.minecraft.magenta_dye": "Purppura väriaine", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON><PERSON><PERSON> val<PERSON>", "item.minecraft.magma_cream": "Magmavoide", "item.minecraft.magma_cube_spawn_egg": "Luo magmakuutio", "item.minecraft.mangrove_boat": "Mangrovepuuvene", "item.minecraft.mangrove_chest_boat": "Mangrovepuuvene arkulla", "item.minecraft.map": "Tyhjä kartta", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON> si<PERSON>u", "item.minecraft.milk_bucket": "Mai<PERSON>ämp<PERSON><PERSON>", "item.minecraft.minecart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.miner_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.mojang_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.desc": "Mojang-symboli", "item.minecraft.mojang_banner_pattern.new": "Mojang-viirikuvio", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON> mooshroom", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON>", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON> muuli", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11": "Musiik<PERSON>levy", "item.minecraft.music_disc_11.desc": "C418 –  11", "item.minecraft.music_disc_13": "Musiik<PERSON>levy", "item.minecraft.music_disc_13.desc": "C418 –  13", "item.minecraft.music_disc_5": "Musiik<PERSON>levy", "item.minecraft.music_disc_5.desc": "<PERSON> – 5", "item.minecraft.music_disc_blocks": "Musiik<PERSON>levy", "item.minecraft.music_disc_blocks.desc": "C418 –  blocks", "item.minecraft.music_disc_cat": "Musiik<PERSON>levy", "item.minecraft.music_disc_cat.desc": "C418 –  cat", "item.minecraft.music_disc_chirp": "Musiik<PERSON>levy", "item.minecraft.music_disc_chirp.desc": "C418 –  chirp", "item.minecraft.music_disc_creator": "Musiik<PERSON>levy", "item.minecraft.music_disc_creator.desc": "<PERSON> – <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Musiik<PERSON>levy", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> – Creator (Music Box)", "item.minecraft.music_disc_far": "Musiik<PERSON>levy", "item.minecraft.music_disc_far.desc": "C418 –  far", "item.minecraft.music_disc_lava_chicken": "Musiik<PERSON>levy", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions – Lava Chicken", "item.minecraft.music_disc_mall": "Musiik<PERSON>levy", "item.minecraft.music_disc_mall.desc": "C418 –  mall", "item.minecraft.music_disc_mellohi": "Musiik<PERSON>levy", "item.minecraft.music_disc_mellohi.desc": "C418 –  mellohi", "item.minecraft.music_disc_otherside": "Musiik<PERSON>levy", "item.minecraft.music_disc_otherside.desc": "<PERSON> – <PERSON>ide", "item.minecraft.music_disc_pigstep": "Musiik<PERSON>levy", "item.minecraft.music_disc_pigstep.desc": "<PERSON> Pigstep", "item.minecraft.music_disc_precipice": "Musiik<PERSON>levy", "item.minecraft.music_disc_precipice.desc": "<PERSON> – Precipice", "item.minecraft.music_disc_relic": "Musiik<PERSON>levy", "item.minecraft.music_disc_relic.desc": "<PERSON> <PERSON><PERSON>", "item.minecraft.music_disc_stal": "Musiik<PERSON>levy", "item.minecraft.music_disc_stal.desc": "C418 –  stal", "item.minecraft.music_disc_strad": "Musiik<PERSON>levy", "item.minecraft.music_disc_strad.desc": "C418 –  strad", "item.minecraft.music_disc_tears": "Musiik<PERSON>levy", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Musiik<PERSON>levy", "item.minecraft.music_disc_wait.desc": "C418 –  wait", "item.minecraft.music_disc_ward": "Musiik<PERSON>levy", "item.minecraft.music_disc_ward.desc": "C418 –  ward", "item.minecraft.mutton": "<PERSON><PERSON>", "item.minecraft.name_tag": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.nautilus_shell": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuo<PERSON>", "item.minecraft.nether_brick": "Nether-tiili", "item.minecraft.nether_star": "Nether-t<PERSON><PERSON>i", "item.minecraft.nether_wart": "Nether-pahka", "item.minecraft.netherite_axe": "<PERSON><PERSON><PERSON>tti<PERSON>rve<PERSON>", "item.minecraft.netherite_boots": "Netheriittisaappaat", "item.minecraft.netherite_chestplate": "Netheriittirintapanssari", "item.minecraft.netherite_helmet": "Netheriittikypärä", "item.minecraft.netherite_hoe": "Netheriittikuokka", "item.minecraft.netherite_ingot": "Netheriittiharkko", "item.minecraft.netherite_leggings": "Net<PERSON><PERSON>ttihou<PERSON><PERSON>", "item.minecraft.netherite_pickaxe": "Netheriittihakku", "item.minecraft.netherite_scrap": "<PERSON><PERSON><PERSON>ttirom<PERSON>", "item.minecraft.netherite_shovel": "Netheriittilapio", "item.minecraft.netherite_sword": "<PERSON><PERSON><PERSON>tti<PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template": "Taontamalli", "item.minecraft.netherite_upgrade_smithing_template.new": "Netheriittipäivitys", "item.minecraft.oak_boat": "Tammivene", "item.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON>", "item.minecraft.ominous_bottle": "<PERSON><PERSON><PERSON><PERSON> pullo", "item.minecraft.ominous_trial_key": "Pa<PERSON><PERSON>inen koetuksen avain", "item.minecraft.orange_bundle": "<PERSON><PERSON><PERSON> pussi", "item.minecraft.orange_dye": "Oranssi väriaine", "item.minecraft.orange_harness": "Oranssit val<PERSON>at", "item.minecraft.painting": "Ma<PERSON><PERSON>", "item.minecraft.pale_oak_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON>ven<PERSON>", "item.minecraft.pale_oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.panda_spawn_egg": "<PERSON><PERSON> panda", "item.minecraft.paper": "<PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON>", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON> kalvo", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON> aave", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON> sika", "item.minecraft.piglin_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Kärsäviirikuvio", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON>lin", "item.minecraft.pillager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.pink_bundle": "Vaalean<PERSON>nai<PERSON> pussi", "item.minecraft.pink_dye": "Vaaleanpunainen väriaine", "item.minecraft.pink_harness": "Vaaleanpunaiset valjaat", "item.minecraft.pitcher_plant": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pitcher_pod": "<PERSON><PERSON><PERSON><PERSON> palko", "item.minecraft.plenty_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> ru<PERSON>", "item.minecraft.plenty_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> ru<PERSON>", "item.minecraft.poisonous_potato": "<PERSON><PERSON><PERSON><PERSON> peruna", "item.minecraft.polar_bear_spawn_egg": "<PERSON><PERSON>", "item.minecraft.popped_chorus_fruit": "Paistettu Chorus-hedelmä", "item.minecraft.porkchop": "<PERSON><PERSON>", "item.minecraft.potato": "Peruna", "item.minecraft.potion": "Taikajuoma", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.empty": "Valmistuskelvoton <PERSON>", "item.minecraft.potion.effect.fire_resistance": "Tulenkestävyyden ta<PERSON>ju<PERSON>", "item.minecraft.potion.effect.harming": "Vahingoittava ta<PERSON>", "item.minecraft.potion.effect.healing": "<PERSON><PERSON><PERSON> ta<PERSON>", "item.minecraft.potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.invisibility": "Näkymättömyyden ta<PERSON>", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "item.minecraft.potion.effect.levitation": "Levitaation taikajuoma", "item.minecraft.potion.effect.luck": "<PERSON><PERSON>", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.night_vision": "Hämärän<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "item.minecraft.potion.effect.slow_falling": "<PERSON><PERSON> putoamisen ta<PERSON>", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.turtle_master": "<PERSON><PERSON><PERSON>konname<PERSON><PERSON>", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.wind_charged": "Viipyvä tuulilatauksen ta<PERSON>", "item.minecraft.pottery_shard_archer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_arms_up": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.pottery_shard_prize": "<PERSON><PERSON><PERSON>", "item.minecraft.pottery_shard_skull": "<PERSON><PERSON>", "item.minecraft.powder_snow_bucket": "Puuterilumiämpäri", "item.minecraft.prismarine_crystals": "Prismariinikristallit", "item.minecraft.prismarine_shard": "Prismariinisirpale", "item.minecraft.prize_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.pufferfish": "Pallokala", "item.minecraft.pufferfish_bucket": "Pallokala ämpärissä", "item.minecraft.pufferfish_spawn_egg": "<PERSON><PERSON>", "item.minecraft.pumpkin_pie": "Kurpitsapiirakka", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_bundle": "<PERSON><PERSON> pussi", "item.minecraft.purple_dye": "<PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON><PERSON> val<PERSON>", "item.minecraft.quartz": "Nether-kvartsi", "item.minecraft.rabbit": "<PERSON><PERSON>", "item.minecraft.rabbit_foot": "Jäniksenkäpälä", "item.minecraft.rabbit_hide": "Jäniksen nahka", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON>", "item.minecraft.rabbit_stew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "Taontamalli", "item.minecraft.raiser_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON> r<PERSON>", "item.minecraft.raw_copper": "<PERSON><PERSON> k<PERSON>", "item.minecraft.raw_gold": "<PERSON><PERSON> k<PERSON>a", "item.minecraft.raw_iron": "<PERSON><PERSON> r<PERSON>a", "item.minecraft.recovery_compass": "Paluukompassi", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON> pussi", "item.minecraft.red_dye": "Punainen väriaine", "item.minecraft.red_harness": "Punaiset val<PERSON>at", "item.minecraft.redstone": "Redstone-jauhe", "item.minecraft.resin_brick": "Pihkatiili", "item.minecraft.resin_clump": "Pihkakimpale", "item.minecraft.rib_armor_trim_smithing_template": "Taontamalli", "item.minecraft.rib_armor_trim_smithing_template.new": "Kylkiluiden haarniskakoristeet", "item.minecraft.rotten_flesh": "Mädäntynyt liha", "item.minecraft.saddle": "Sa<PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON> lohi", "item.minecraft.salmon_bucket": "Lohi ämpärissä", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON> lohi", "item.minecraft.scrape_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON> ruuku<PERSON>", "item.minecraft.scute": "<PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Taontamalli", "item.minecraft.sentry_armor_trim_smithing_template.new": "Vartion haarniskakoristeet", "item.minecraft.shaper_armor_trim_smithing_template": "Taontamalli", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.sheaf_pottery_shard": "Lyhteen ruukunsirpale", "item.minecraft.sheaf_pottery_sherd": "Lyhteen ruukunsirpale", "item.minecraft.shears": "Keritsimet", "item.minecraft.sheep_spawn_egg": "<PERSON>o lammas", "item.minecraft.shelter_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.shelter_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.shield": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON> kilpi", "item.minecraft.shield.blue": "<PERSON><PERSON> kilpi", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON> kilpi", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON> kil<PERSON>", "item.minecraft.shield.gray": "<PERSON><PERSON><PERSON> kilpi", "item.minecraft.shield.green": "Vihreä kilpi", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> kilpi", "item.minecraft.shield.light_gray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kilpi", "item.minecraft.shield.lime": "Limetinvihreä kilpi", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON><PERSON> kilpi", "item.minecraft.shield.orange": "Oranssi kilpi", "item.minecraft.shield.pink": "Vaaleanpunainen kilpi", "item.minecraft.shield.purple": "<PERSON><PERSON> kil<PERSON>", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON> kilpi", "item.minecraft.shield.white": "Val<PERSON><PERSON> kilpi", "item.minecraft.shield.yellow": "<PERSON><PERSON><PERSON> kilpi", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON> kuori", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON>", "item.minecraft.sign": "<PERSON><PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Taontamalli", "item.minecraft.silence_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> haar<PERSON>", "item.minecraft.silverfish_spawn_egg": "<PERSON><PERSON>", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON><PERSON>", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON>", "item.minecraft.skull_banner_pattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.desc": "Pääkallo", "item.minecraft.skull_banner_pattern.new": "Pääkalloviirikuvio", "item.minecraft.skull_pottery_shard": "<PERSON><PERSON>", "item.minecraft.skull_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.slime_ball": "<PERSON><PERSON><PERSON>", "item.minecraft.slime_spawn_egg": "<PERSON><PERSON> lima", "item.minecraft.smithing_template": "Taontamalli", "item.minecraft.smithing_template.applies_to": "<PERSON><PERSON><PERSON>:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Lisää harkko tai kristalli", "item.minecraft.smithing_template.armor_trim.applies_to": "Haarniska", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Lisää haarniskan osa", "item.minecraft.smithing_template.armor_trim.ingredients": "<PERSON><PERSON>ot ja kristallit", "item.minecraft.smithing_template.ingredients": "Ainesosat:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Lisää netheriittiharkko", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Lisää timanttihaarniskan osa, ase tai työkalu", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netheriittiharkko", "item.minecraft.smithing_template.upgrade": "Päivitys: ", "item.minecraft.sniffer_spawn_egg": "<PERSON><PERSON> nu<PERSON>ku", "item.minecraft.snort_pottery_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruuku<PERSON>pale", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruuku<PERSON>pale", "item.minecraft.snout_armor_trim_smithing_template": "Taontamalli", "item.minecraft.snout_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.snow_golem_spawn_egg": "Luo lumigolem", "item.minecraft.snowball": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spectral_arrow": "Spektraalinuoli", "item.minecraft.spider_eye": "Hämähäkinsilmä", "item.minecraft.spider_spawn_egg": "<PERSON><PERSON>", "item.minecraft.spire_armor_trim_smithing_template": "Taontamalli", "item.minecraft.spire_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.empty": "Valmistuskelvoton r<PERSON> ta<PERSON>", "item.minecraft.splash_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON>va tulenkestävyyden ta<PERSON>juoma", "item.minecraft.splash_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON><PERSON> vahi<PERSON><PERSON><PERSON> ta<PERSON>", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON><PERSON> parantava ta<PERSON>", "item.minecraft.splash_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> ta<PERSON>", "item.minecraft.splash_potion.effect.invisibility": "Rois<PERSON>htava näkymättömyyden ta<PERSON>oma", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON> ta<PERSON>", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON><PERSON> levi<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON><PERSON> onnen ta<PERSON>", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON><PERSON><PERSON> r<PERSON> ta<PERSON>", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON> hämärä<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON><PERSON> lima<PERSON><PERSON>n ta<PERSON>", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON><PERSON> my<PERSON><PERSON>", "item.minecraft.splash_potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON> el<PERSON> ta<PERSON>", "item.minecraft.splash_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON><PERSON> hitaan putoamisen taika<PERSON>", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON> hitauden ta<PERSON>", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON><PERSON> vah<PERSON><PERSON>n ta<PERSON>", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON><PERSON> nopeuden ta<PERSON>", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON> r<PERSON> ta<PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON><PERSON> kilpikonnamestarin ta<PERSON>", "item.minecraft.splash_potion.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON><PERSON> vedenhen<PERSON><PERSON> ta<PERSON>", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON><PERSON> heik<PERSON>n ta<PERSON>", "item.minecraft.splash_potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON><PERSON> kutomisen ta<PERSON>", "item.minecraft.splash_potion.effect.wind_charged": "<PERSON><PERSON><PERSON><PERSON><PERSON> tu<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON>", "item.minecraft.spruce_chest_boat": "<PERSON><PERSON><PERSON>", "item.minecraft.spyglass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON>", "item.minecraft.stick": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stone_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_sword": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.stray_spawn_egg": "<PERSON><PERSON>", "item.minecraft.strider_spawn_egg": "<PERSON><PERSON>", "item.minecraft.string": "Lanka", "item.minecraft.sugar": "<PERSON><PERSON><PERSON>", "item.minecraft.suspicious_stew": "Epäilyttävä muhennos", "item.minecraft.sweet_berries": "<PERSON><PERSON> mar<PERSON>t", "item.minecraft.tadpole_bucket": "Nuijapää ämpärissä", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON>ui<PERSON>", "item.minecraft.tide_armor_trim_smithing_template": "Taontamalli", "item.minecraft.tide_armor_trim_smithing_template.new": "Vuorovetten haarniskakoristeet", "item.minecraft.tipped_arrow": "Kastettu nuoli", "item.minecraft.tipped_arrow.effect.awkward": "Kastettu nuoli", "item.minecraft.tipped_arrow.effect.empty": "Valmistuskelvoton kastettu nuoli", "item.minecraft.tipped_arrow.effect.fire_resistance": "Tulenkestävyyden nuoli", "item.minecraft.tipped_arrow.effect.harming": "Vahingoittava nuoli", "item.minecraft.tipped_arrow.effect.healing": "Parantava nuoli", "item.minecraft.tipped_arrow.effect.infested": "Saastunut nuoli", "item.minecraft.tipped_arrow.effect.invisibility": "Näkymättömyyden nuoli", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON><PERSON><PERSON><PERSON> nuoli", "item.minecraft.tipped_arrow.effect.levitation": "Levitaation nuoli", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON> nuoli", "item.minecraft.tipped_arrow.effect.mundane": "Kastettu nuoli", "item.minecraft.tipped_arrow.effect.night_vision": "Hämärän<PERSON><PERSON><PERSON> nuoli", "item.minecraft.tipped_arrow.effect.oozing": "<PERSON>inen nuoli", "item.minecraft.tipped_arrow.effect.poison": "Myrkkynuoli", "item.minecraft.tipped_arrow.effect.regeneration": "Elpymisen nuoli", "item.minecraft.tipped_arrow.effect.slow_falling": "Hitaan putoamisen nuoli", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON> nuoli", "item.minecraft.tipped_arrow.effect.strength": "Vahvuuden nuoli", "item.minecraft.tipped_arrow.effect.swiftness": "<PERSON>uden nuoli", "item.minecraft.tipped_arrow.effect.thick": "Kastettu nuoli", "item.minecraft.tipped_arrow.effect.turtle_master": "Kilpikonnamestarin nuoli", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "<PERSON>edenheng<PERSON>ks<PERSON> nuoli", "item.minecraft.tipped_arrow.effect.weakness": "Heikkouden nuoli", "item.minecraft.tipped_arrow.effect.weaving": "Ku<PERSON><PERSON><PERSON> nuoli", "item.minecraft.tipped_arrow.effect.wind_charged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nuoli", "item.minecraft.tnt_minecart": "TNT-kaivosvaunu", "item.minecraft.torchflower_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.totem_of_undying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON> k<PERSON> laama", "item.minecraft.trial_key": "<PERSON><PERSON><PERSON><PERSON> avain", "item.minecraft.trident": "Atrain", "item.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> kala", "item.minecraft.tropical_fish_bucket": "Trooppinen kala ämpärissä", "item.minecraft.tropical_fish_spawn_egg": "<PERSON><PERSON> <PERSON><PERSON>n kala", "item.minecraft.turtle_helmet": "Kilpikonnan kuori", "item.minecraft.turtle_scute": "<PERSON><PERSON><PERSON><PERSON><PERSON> suomu", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON> k<PERSON>", "item.minecraft.vex_armor_trim_smithing_template": "Taontamalli", "item.minecraft.vex_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON>", "item.minecraft.vex_spawn_egg": "<PERSON>o piina", "item.minecraft.villager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.vindicator_spawn_egg": "<PERSON><PERSON>", "item.minecraft.wandering_trader_spawn_egg": "<PERSON><PERSON>", "item.minecraft.ward_armor_trim_smithing_template": "Taontamalli", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.warden_spawn_egg": "<PERSON><PERSON> warden", "item.minecraft.warped_fungus_on_a_stick": "Kummasieni kepin päässä", "item.minecraft.water_bucket": "Vesiämpäri", "item.minecraft.wayfinder_armor_trim_smithing_template": "Taontamalli", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Tiennäyttäjän haarniskakoristeet", "item.minecraft.wheat": "Vehnä", "item.minecraft.wheat_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.white_bundle": "<PERSON><PERSON><PERSON> pussi", "item.minecraft.white_dye": "Valkoinen väriaine", "item.minecraft.white_harness": "Valkoiset valjaat", "item.minecraft.wild_armor_trim_smithing_template": "Taontamalli", "item.minecraft.wild_armor_trim_smithing_template.new": "Viidakon haarniskakoristeet", "item.minecraft.wind_charge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.witch_spawn_egg": "<PERSON><PERSON> noita", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON> wither-l<PERSON><PERSON><PERSON>", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON> wither", "item.minecraft.wolf_armor": "<PERSON><PERSON>", "item.minecraft.wolf_spawn_egg": "<PERSON><PERSON> susi", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.writable_book": "<PERSON><PERSON><PERSON> ja sulk<PERSON>", "item.minecraft.written_book": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kirja", "item.minecraft.yellow_bundle": "<PERSON><PERSON><PERSON> pussi", "item.minecraft.yellow_dye": "Keltainen väriaine", "item.minecraft.yellow_harness": "Keltaiset valjaat", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON>", "item.minecraft.zombie_horse_spawn_egg": "<PERSON><PERSON>", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON> z<PERSON>i", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON>", "item.minecraft.zombified_piglin_spawn_egg": "<PERSON><PERSON>", "item.modifiers.any": "Kun käytössä:", "item.modifiers.armor": "Kun yllä:", "item.modifiers.body": "Kun yllä:", "item.modifiers.chest": "Kun yllä:", "item.modifiers.feet": "Kun jaloissa:", "item.modifiers.hand": "Kun kädessä:", "item.modifiers.head": "Kun päässä:", "item.modifiers.legs": "Kun jaloissa:", "item.modifiers.mainhand": "Kun pääkädessä:", "item.modifiers.offhand": "<PERSON>n to<PERSON><PERSON> k<PERSON>:", "item.modifiers.saddle": "Satuloituna:", "item.nbt_tags": "NBT: %s tunniste(tta)", "item.op_block_warning.line1": "Varoitus:", "item.op_block_warning.line2": "Tämän esineen käyttö saattaa johtaa komennon suoritta<PERSON>en", "item.op_block_warning.line3": "<PERSON><PERSON><PERSON> k<PERSON>, el<PERSON>ä tarkkaa sisältöä!", "item.unbreakable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.coloredBlocks": "Värj<PERSON><PERSON> k<PERSON>", "itemGroup.combat": "Taistelu", "itemGroup.consumables": "<PERSON><PERSON>", "itemGroup.crafting": "Työstäminen", "itemGroup.foodAndDrink": "<PERSON><PERSON><PERSON> ja juoma", "itemGroup.functional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ku<PERSON>", "itemGroup.hotbar": "Tallennetut pikapalkit", "itemGroup.ingredients": "Ainesosat", "itemGroup.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.natural": "Luonnolliset ku<PERSON>ot", "itemGroup.op": "Operaattoriesineet", "itemGroup.redstone": "Redstone-kuutiot", "itemGroup.search": "Etsi esineitä", "itemGroup.spawnEggs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.tools": "Työkalut", "item_modifier.unknown": "Tuntematon esinemuuntaja: %s", "jigsaw_block.final_state": "<PERSON><PERSON><PERSON><PERSON>:", "jigsaw_block.generate": "<PERSON><PERSON>", "jigsaw_block.joint.aligned": "Ke<PERSON><PERSON>tty", "jigsaw_block.joint.rollable": "Käärittävä", "jigsaw_block.joint_label": "Liitäntätyyppi:", "jigsaw_block.keep_jigsaws": "Pidä palapelit", "jigsaw_block.levels": "Tasot: %s", "jigsaw_block.name": "<PERSON><PERSON>:", "jigsaw_block.placement_priority": "Asetteluprioriteetti:", "jigsaw_block.placement_priority.tooltip": "Kun tämä palapelikuutio yhdistyy palaseen, tä<PERSON><PERSON> on järjestys, jossa tuo palanen käsitellään la<PERSON>emman rakennelman yhteyksiä varten.\n\nPalaset käsitellään laskevassa tärkeysjärjestyksessä, jossa lisäysjärjestys ratkoo tasatulokset.", "jigsaw_block.pool": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "jigsaw_block.selection_priority": "Valintaprioriteetti:", "jigsaw_block.selection_priority.tooltip": "Kun van<PERSON>pipalasta käsitellään yhteyksiä varten, tä<PERSON><PERSON> on järjestys, jossa tämä palapelikuutio yrittää yhdistyä kohdepalaseensa.\n\nPalapelit käsitellään laskevassa tärkeysjärjestyksessä, jossa satunnaisjärjestys ratkoo tasatulokset.", "jigsaw_block.target": "<PERSON><PERSON><PERSON> nimi:", "jukebox_song.minecraft.11": "C418 – 11", "jukebox_song.minecraft.13": "C418 – 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 – blocks", "jukebox_song.minecraft.cat": "C418 – cat", "jukebox_song.minecraft.chirp": "C418 – chirp", "jukebox_song.minecraft.creator": "<PERSON> – <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> – Creator (Soittorasia)", "jukebox_song.minecraft.far": "C418 – far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions – Lava Chicken", "jukebox_song.minecraft.mall": "C418 – mall", "jukebox_song.minecraft.mellohi": "C418 – mellohi", "jukebox_song.minecraft.otherside": "<PERSON> – <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> – Precipice", "jukebox_song.minecraft.relic": "<PERSON> <PERSON><PERSON>", "jukebox_song.minecraft.stal": "C418 – stal", "jukebox_song.minecraft.strad": "C418 – strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 – wait", "jukebox_song.minecraft.ward": "C418 – ward", "key.advancements": "Edistysaskeleet", "key.attack": "Hyökkää/tuhoa", "key.back": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.creative": "Luovuustila", "key.categories.gameplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.misc": "Sekalaiset", "key.categories.movement": "Liikkuminen", "key.categories.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.ui": "<PERSON><PERSON> k<PERSON>öliittymä", "key.chat": "<PERSON>a keskustelu", "key.command": "<PERSON><PERSON> komento", "key.drop": "<PERSON><PERSON><PERSON> valittu esine", "key.forward": "<PERSON><PERSON><PERSON><PERSON> eteenpäin", "key.fullscreen": "Vaihda koko<PERSON>ä<PERSON>tötilaan", "key.hotbar.1": "Pikanäppäin 1", "key.hotbar.2": "Pikanäppäin 2", "key.hotbar.3": "Pikanäppäin 3", "key.hotbar.4": "Pikanäppäin 4", "key.hotbar.5": "Pikanäppäin 5", "key.hotbar.6": "Pikanäppäin 6", "key.hotbar.7": "Pikanäppäin 7", "key.hotbar.8": "Pikanäppäin 8", "key.hotbar.9": "Pikanäppäin 9", "key.inventory": "<PERSON><PERSON>/sulje ta<PERSON>", "key.jump": "Hyppää", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "<PERSON><PERSON><PERSON>", "key.keyboard.end": "End", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Esc", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Home", "key.keyboard.insert": "Insert", "key.keyboard.keypad.0": "Num 0", "key.keyboard.keypad.1": "Num 1", "key.keyboard.keypad.2": "Num 2", "key.keyboard.keypad.3": "Num 3", "key.keyboard.keypad.4": "Num 4", "key.keyboard.keypad.5": "Num 5", "key.keyboard.keypad.6": "Num 6", "key.keyboard.keypad.7": "Num 7", "key.keyboard.keypad.8": "Num 8", "key.keyboard.keypad.9": "Num 9", "key.keyboard.keypad.add": "Num +", "key.keyboard.keypad.decimal": "Num desimaali", "key.keyboard.keypad.divide": "Num /", "key.keyboard.keypad.enter": "<PERSON><PERSON>", "key.keyboard.keypad.equal": "Num =", "key.keyboard.keypad.multiply": "Num *", "key.keyboard.keypad.subtract": "Num -", "key.keyboard.left": "<PERSON><PERSON><PERSON> nuoli", "key.keyboard.left.alt": "Vasen Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Vasen Ctrl", "key.keyboard.left.shift": "<PERSON><PERSON><PERSON>", "key.keyboard.left.win": "<PERSON><PERSON><PERSON>", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page Down", "key.keyboard.page.up": "Page Up", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Prt Scn", "key.keyboard.right": "<PERSON><PERSON><PERSON> nuoli", "key.keyboard.right.alt": "Oikea Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Oikea Ctrl", "key.keyboard.right.shift": "<PERSON><PERSON><PERSON>", "key.keyboard.right.win": "<PERSON><PERSON><PERSON>", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "<PERSON><PERSON>", "key.keyboard.up": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.world.1": "World 1", "key.keyboard.world.2": "World 2", "key.left": "<PERSON><PERSON><PERSON><PERSON> vasemmalle", "key.loadToolbarActivator": "La<PERSON><PERSON> pika<PERSON> aktivaattori", "key.mouse": "Nappi %1$s", "key.mouse.left": "Hiiren vasen", "key.mouse.middle": "Keskipainike", "key.mouse.right": "<PERSON><PERSON><PERSON>", "key.pickItem": "Valitse k<PERSON>o", "key.playerlist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.quickActions": "Pikatoiminnot", "key.right": "Kävele o<PERSON>alle", "key.saveToolbarActivator": "<PERSON><PERSON><PERSON> p<PERSON> aktivaatt<PERSON>", "key.screenshot": "<PERSON><PERSON>", "key.smoothCamera": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>aan", "key.sneak": "<PERSON><PERSON><PERSON>", "key.socialInteractions": "Sosiaaliset kanssakäymiset -ruutu", "key.spectatorOutlines": "<PERSON><PERSON><PERSON> (Katselijat)", "key.sprint": "<PERSON><PERSON><PERSON>", "key.swapOffhand": "<PERSON><PERSON><PERSON><PERSON> esine toiseen käteen", "key.togglePerspective": "<PERSON><PERSON><PERSON><PERSON>", "key.use": "Käytä es<PERSON>tä/sijoita kuutio", "known_server_link.announcements": "Ilmoitukset", "known_server_link.community": "Yhteisö", "known_server_link.community_guidelines": "Yhteisön säännöt", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Foorumit", "known_server_link.news": "<PERSON><PERSON><PERSON>", "known_server_link.report_bug": "<PERSON><PERSON><PERSON>", "known_server_link.status": "Tila", "known_server_link.support": "<PERSON><PERSON>", "known_server_link.website": "Verkkosivusto", "lanServer.otherPlayers": "<PERSON><PERSON> p<PERSON>", "lanServer.port": "Porttinumero", "lanServer.port.invalid": "Portti ei kelpaa.\nJätä muokkausruutu tyhjäksi tai syötä toinen luku väliltä 1024–65535.", "lanServer.port.invalid.new": "Portti ei kelpaa.\nJätä muokkausruutu tyhjäksi tai syötä toinen luku väliltä %s–%s.", "lanServer.port.unavailable": "Portti ei saatavilla.\nJätä muokkausruutu tyhjäksi tai syötä toinen luku väliltä 1024–65535.", "lanServer.port.unavailable.new": "Portti ei saatavilla.\nJätä muokkausruutu tyhjäksi tai syötä toinen luku väliltä %s–%s.", "lanServer.scanning": "Etsitään pelejä lähiverkostasi", "lanServer.start": "Jaa lähiverkkoon", "lanServer.title": "Lähiverkkomaailma", "language.code": "fin_FI", "language.name": "<PERSON><PERSON>", "language.region": "<PERSON><PERSON>", "lectern.take_book": "<PERSON><PERSON> k<PERSON>", "loading.progress": "%s%%", "mco.account.privacy.info": "Lue lisää Mojangis<PERSON> ja yksityisyydensuojalaeista", "mco.account.privacy.info.button": "Lue lisää yleisestä tietosuoja-asetuksesta (GDPR)", "mco.account.privacy.information": "Mojang soveltaa tiettyjä menetelmiä lasten ja heidän yksityisyytensä suojaamiseksi, mukaan lukien lasten yksityisyyden suojaa verkossa koske<PERSON> lain (COPPA) ja yleisen tietosuo<PERSON>-<PERSON><PERSON><PERSON><PERSON> (GDPR) noudattaminen.\n\nSaatat tarvita vanhempien suostumuksen ennen kuin voit käyttää Realms-tiliäsi.", "mco.account.privacyinfo": "Mojang soveltaa tiettyjä menetelmiä lasten ja heidän yksityisyytensä suojaamiseksi, mukaan lukien lasten yksityisyyden suojaa verkossa koske<PERSON> lain (COPPA) ja yleisen tietosuo<PERSON>-<PERSON><PERSON><PERSON><PERSON> (GDPR) noudattaminen.\n\nSaatat tarvita vanhempien suostumuksen ennen kuin voit käyttää Realms-tiliäsi.\n\n<PERSON><PERSON><PERSON><PERSON> sinulla on vanhempi Minecraft-tili (kirjaudut sisään käyttäjänimelläsi), sinun tulee yhdistää tilisi Mojang-tiliin päästäksesi Realmsiin.", "mco.account.update": "Päivitä tili", "mco.activity.noactivity": "Ei toimintaa %s päivään", "mco.activity.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.button.download": "Lataa u<PERSON>in", "mco.backup.button.reset": "<PERSON><PERSON><PERSON>", "mco.backup.button.restore": "<PERSON><PERSON><PERSON>", "mco.backup.button.upload": "<PERSON><PERSON><PERSON> ma<PERSON>", "mco.backup.changes.tooltip": "Muutokset", "mco.backup.entry": "Varmuuskopio (%s)", "mco.backup.entry.description": "<PERSON><PERSON><PERSON>", "mco.backup.entry.enabledPack": "Käytössä olevat paketit", "mco.backup.entry.gameDifficulty": "<PERSON><PERSON> vai<PERSON>o", "mco.backup.entry.gameMode": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Pelipalvelimen versio", "mco.backup.entry.name": "<PERSON><PERSON>", "mco.backup.entry.seed": "Lähtöarvo", "mco.backup.entry.templateName": "<PERSON><PERSON> nimi", "mco.backup.entry.undefined": "Määrittelemätön muutos", "mco.backup.entry.uploaded": "Ladattu", "mco.backup.entry.worldType": "<PERSON><PERSON><PERSON> tyy<PERSON>i", "mco.backup.generate.world": "<PERSON><PERSON>", "mco.backup.info.title": "Muutokset edellisestä varmuuskopiosta", "mco.backup.narration": "Varmuuskopio kohteesta %s", "mco.backup.nobackups": "Tällä Realmilla ei ole vielä varmuuskopiota.", "mco.backup.restoring": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.unknown": "Tuntematon", "mco.brokenworld.download": "Lataa", "mco.brokenworld.downloaded": "Ladattu", "mco.brokenworld.message.line1": "<PERSON><PERSON><PERSON> tai valitse toinen ma<PERSON>ma.", "mco.brokenworld.message.line2": "Voit myös ladata ma<PERSON>man koneellesi yksinpelimaailmaksi.", "mco.brokenworld.minigame.title": "Tätä minipeliä ei enää tueta", "mco.brokenworld.nonowner.error": "<PERSON><PERSON><PERSON> kunnes <PERSON> o<PERSON> on nollannut ma<PERSON>a", "mco.brokenworld.nonowner.title": "<PERSON><PERSON><PERSON> on vanhentunut", "mco.brokenworld.play": "P<PERSON><PERSON>", "mco.brokenworld.reset": "<PERSON><PERSON><PERSON>", "mco.brokenworld.title": "Nykyistä ma<PERSON>maasi ei tueta enää", "mco.client.incompatible.msg.line1": "Asiakas<PERSON><PERSON>lmasi on yhteensopimaton Realmsin kanssa.", "mco.client.incompatible.msg.line2": "Käytä uusinta Minecraftin versiota.", "mco.client.incompatible.msg.line3": "Realms ei ole yhteensopiva kehittäjäversioiden kanssa.", "mco.client.incompatible.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on yhteensopimaton!", "mco.client.outdated.stable.version": "<PERSON><PERSON><PERSON> versio (%s) ei ole yhteensopiva Realmsin kanssa.\n\nOle hyvä ja käytä Minecraftin uusinta versiota.", "mco.client.unsupported.snapshot.version": "P<PERSON>si versio (%s) ei ole yhteensopiva Realmsin kanssa.\n\nRealms ei ole saatavilla tällä kehittäjäversiolla.", "mco.compatibility.downgrade": "<PERSON><PERSON><PERSON> a<PERSON> versioon", "mco.compatibility.downgrade.description": "Tätä maailmaa pelattiin viimeksi versiossa %s; käytät nyt versiota %s. <PERSON><PERSON><PERSON> pelaaminen aiemmalla versiolla voi aiheuttaa vaurioita – emme voi luvata että maailma latautuu tai toimii.\n\nVarmuuskopio maailmastasi tallenne<PERSON>an \"Maailmojen varmuuskop<PERSON>\" -osioon. <PERSON><PERSON><PERSON> jos on tarve.", "mco.compatibility.incompatible.popup.title": "Yhteensopimaton versio", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON><PERSON><PERSON>, joh<PERSON> y<PERSON> lii<PERSON>, ei ole yhteensopiva käyttämäsi version kanssa.", "mco.compatibility.incompatible.series.popup.message": "Tätä maailmaa on viimeksi pelattu versiossa %s; olet tällä hetkellä versiossa %s.\n\nNämä versiot eivät ole yhteensopivia keskenään. Uusi maailma vaaditaan tämän version pelaamiseksi.", "mco.compatibility.unverifiable.message": "<PERSON><PERSON><PERSON><PERSON>, jolla t<PERSON> maailmaa vii<PERSON><PERSON><PERSON>, ei voitu tark<PERSON>. <PERSON><PERSON> vers<PERSON><PERSON>, var<PERSON><PERSON><PERSON><PERSON> luo<PERSON>an automaattisesti ja tallennetaan kohtaan \"Maailman varmuuskopiot\".", "mco.compatibility.unverifiable.title": "Yhteensopivuus ei vahvistettavissa", "mco.compatibility.upgrade": "Päivitä", "mco.compatibility.upgrade.description": "Tätä maailmaa pelattiin viimeksi versiossa %s; olet versiossa %s.\n\nVarmuuskopio ma<PERSON>mastasi tallennetaan \"World backups\" -kansioon.\n\nPalauta maailmasi tarvittaessa.", "mco.compatibility.upgrade.friend.description": "Tätä maailmaa pelattiin viimeksi versiossa %s; olet versiossa %s.\n\nVarmuuskopio maailmastasi tallennetaan \"World backups\" -kansioon.\n\nRealmin omistaja voi palauttaa maailman tarvittaessa.", "mco.compatibility.upgrade.title": "<PERSON><PERSON><PERSON><PERSON> todella päivitt<PERSON>ä ma<PERSON>i?", "mco.configure.current.minigame": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "Pelaajasyöte väliaikaisesti poissa käytöstä", "mco.configure.world.backup": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.activity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.close": "Sulje Realm väliaikaisesti", "mco.configure.world.buttons.delete": "Poista", "mco.configure.world.buttons.done": "Val<PERSON>", "mco.configure.world.buttons.edit": "Asetukset", "mco.configure.world.buttons.invite": "Ku<PERSON> pelaaja", "mco.configure.world.buttons.moreoptions": "Lisäasetukset", "mco.configure.world.buttons.newworld": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.open": "Avaa Realm uudelleen", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.players": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.region_preference": "Valitse alue...", "mco.configure.world.buttons.resetworld": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.save": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.settings": "Asetukset", "mco.configure.world.buttons.subscription": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.switchminigame": "Vaihda minipeli", "mco.configure.world.close.question.line1": "Voit sulkea Realmisi väliaikaisesti estä<PERSON>, kun teet muutoks<PERSON>. Ava<PERSON> se uude<PERSON>, kun olet valmis.\n\nTämä ei peruuta Realms-tilaustasi.", "mco.configure.world.close.question.line2": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> jat<PERSON>?", "mco.configure.world.close.question.title": "<PERSON><PERSON><PERSON>tseeko sinun tehdä muutoksia ilman häiriötä?", "mco.configure.world.closing": "Suljetaan realmia väliaikaisesti...", "mco.configure.world.commandBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.delete.button": "Poista Realm", "mco.configure.world.delete.question.line1": "Realmisi poistetaan p<PERSON>västi", "mco.configure.world.delete.question.line2": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> jat<PERSON>?", "mco.configure.world.description": "<PERSON><PERSON> k<PERSON>", "mco.configure.world.edit.slot.name": "<PERSON><PERSON><PERSON> nimi", "mco.configure.world.edit.subscreen.adventuremap": "<PERSON><PERSON><PERSON><PERSON> aset<PERSON>set ovat poissa k<PERSON>, koska nykyinen ma<PERSON> on seikkailumaailma", "mco.configure.world.edit.subscreen.experience": "<PERSON><PERSON><PERSON><PERSON> aset<PERSON> ovat poissa k<PERSON>ä, koska nykyinen ma<PERSON> on kokemusmaailma", "mco.configure.world.edit.subscreen.inspiration": "Jo<PERSON><PERSON><PERSON> asetukset ovat poissa käytöstä niin kauan kunnes nykyinen maailmasi on inspiraationa", "mco.configure.world.forceGameMode": "Pakota pelitila", "mco.configure.world.invite.narration": "<PERSON><PERSON><PERSON> on %s uutta kutsua", "mco.configure.world.invite.profile.name": "<PERSON><PERSON>", "mco.configure.world.invited": "Kutsu<PERSON>t p<PERSON>t", "mco.configure.world.invited.number": "Kutsuttu (%s)", "mco.configure.world.invites.normal.tooltip": "Tavallinen käyttäjä", "mco.configure.world.invites.ops.tooltip": "Operaattori", "mco.configure.world.invites.remove.tooltip": "Poista", "mco.configure.world.leave.question.line1": "Jos poistut tästä Realmista, et enää näe sitä ellei sinua kutsuta uudelleen", "mco.configure.world.leave.question.line2": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> jat<PERSON>?", "mco.configure.world.loading": "Ladataan Realmia", "mco.configure.world.location": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.minigame": "Nykyinen: %s", "mco.configure.world.name": "<PERSON><PERSON> nimi", "mco.configure.world.opening": "Avataan <PERSON>...", "mco.configure.world.players.error": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>n nime<PERSON> ei ole o<PERSON>a", "mco.configure.world.players.inviting": "<PERSON><PERSON><PERSON><PERSON> pela<PERSON>a...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.region_preference.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> valinta", "mco.configure.world.reset.question.line1": "<PERSON><PERSON><PERSON><PERSON> luodaan uudelleen ja nykyinen ma<PERSON>ma poistetaan lopu<PERSON>i", "mco.configure.world.reset.question.line2": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> jat<PERSON>?", "mco.configure.world.resourcepack.question": "Realm edellyttää mukautettua resurssipakettia\n\nHaluatko ladata sen pelataksesi?", "mco.configure.world.resourcepack.question.line1": "Tämä Realm edellyttää mukautettua resurssipakettia", "mco.configure.world.resourcepack.question.line2": "<PERSON><PERSON>tko ladata sen ja pelata?", "mco.configure.world.restore.download.question.line1": "Maailma ladataan ja lisätään sinun yksinpelimaailmoihisi.", "mco.configure.world.restore.download.question.line2": "<PERSON><PERSON><PERSON><PERSON> jat<PERSON>?", "mco.configure.world.restore.question.line1": "Maailmasi palautetaan päivämäärään '%s' (%s)", "mco.configure.world.restore.question.line2": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> jat<PERSON>?", "mco.configure.world.settings.expired": "Et voi muokata vanhentuneen Realmin asetuksia", "mco.configure.world.settings.title": "Asetukset", "mco.configure.world.slot": "Maailma %s", "mco.configure.world.slot.empty": "Tyhjä", "mco.configure.world.slot.switch.question.line1": "<PERSON><PERSON> vai<PERSON> to<PERSON>en ma<PERSON>an", "mco.configure.world.slot.switch.question.line2": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> jat<PERSON>?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot.tooltip.active": "Liity", "mco.configure.world.slot.tooltip.minigame": "Vaihda <PERSON>peliin", "mco.configure.world.spawnAnimals": "<PERSON><PERSON>", "mco.configure.world.spawnMonsters": "<PERSON><PERSON>r<PERSON>", "mco.configure.world.spawnNPCs": "<PERSON>o N<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawnProtection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.spawn_toggle.message": "Tämän asetuksen poistaminen käytöstä POISTAA KAIKKI tämän tyyppiset oliot", "mco.configure.world.spawn_toggle.message.npc": "Tä<PERSON>än asetuksen poistaminen käytöstä POISTAA KAIKKI tämän tyyppiset olemassa olevat oliot, kuten kyläläiset", "mco.configure.world.spawn_toggle.title": "Varoitus!", "mco.configure.world.status": "Tila", "mco.configure.world.subscription.day": "päivä", "mco.configure.world.subscription.days": "päivää", "mco.configure.world.subscription.expired": "Umpeutunut", "mco.configure.world.subscription.extend": "Pidennä tilausta", "mco.configure.world.subscription.less_than_a_day": "Alle päivä", "mco.configure.world.subscription.month": "ku<PERSON><PERSON>i", "mco.configure.world.subscription.months": "ku<PERSON><PERSON>a", "mco.configure.world.subscription.recurring.daysleft": "Uudistetaan automaattisesti", "mco.configure.world.subscription.recurring.info": "Realms-tilaukseesi teh<PERSON>t muuto<PERSON>, kuten ajan lis<PERSON>minen tai toistuvan laskutuksen päättäminen, tulevat voimaan vasta seuraavana laskutuspäivänä.", "mco.configure.world.subscription.remaining.days": "%1$s päivä(ä)", "mco.configure.world.subscription.remaining.months": "%1$s kuukautta", "mco.configure.world.subscription.remaining.months.days": "%1$s kuukautta, %2$s päivää", "mco.configure.world.subscription.start": "Aloituspäivä", "mco.configure.world.subscription.tab": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.unknown": "Tuntematon", "mco.configure.world.switch.slot": "<PERSON><PERSON>", "mco.configure.world.switch.slot.subtitle": "<PERSON><PERSON><PERSON> on tyhjä, valitse miten tehdä maailmasi", "mco.configure.world.title": "Määritä Realm:", "mco.configure.world.uninvite.player": "<PERSON><PERSON><PERSON> varma että haluat peruuttaa käyttäjän '%s' kutsun?", "mco.configure.world.uninvite.question": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>ti peru<PERSON>aa kutsun", "mco.configure.worlds.title": "<PERSON><PERSON><PERSON>", "mco.connect.authorizing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "mco.connect.connecting": "Yhdistetään Realmiin...", "mco.connect.failed": "Realmiin yhdistäminen epäonnistui", "mco.connect.region": "Palvelimen alue: %s", "mco.connect.success": "Val<PERSON>", "mco.create.world": "<PERSON><PERSON>", "mco.create.world.error": "Sinun tulee antaa nimi!", "mco.create.world.failed": "Ma<PERSON>man luominen epäonnistui!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON>...", "mco.create.world.skip": "<PERSON><PERSON>", "mco.create.world.subtitle": "<PERSON><PERSON>ht<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>, min<PERSON>ä maailman laitat uuteen Realmiisi", "mco.create.world.wait": "<PERSON><PERSON><PERSON>...", "mco.download.cancelled": "Lataus peru<PERSON>ttu", "mco.download.confirmation.line1": "<PERSON><PERSON><PERSON>, jota olet la<PERSON>, on suurempi kuin %s", "mco.download.confirmation.line2": "Et voi ladata tätä maailmaa uudelleen Realmiisi", "mco.download.confirmation.oversized": "<PERSON><PERSON><PERSON>, jota olet <PERSON>, on suurempi kuin %s\n\nEt voi ladata tätä maailmaa uudelleen Realmiisi", "mco.download.done": "<PERSON><PERSON><PERSON> valmis", "mco.download.downloading": "<PERSON><PERSON><PERSON>", "mco.download.extracting": "Puretaan", "mco.download.failed": "<PERSON><PERSON><PERSON> e<PERSON>", "mco.download.percent": "%s %%", "mco.download.preparing": "Valmistellaan latausta", "mco.download.resourcePack.fail": "Resurssipaketin lataaminen epäonnistui!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Ladata<PERSON> uusinta ma<PERSON>a", "mco.error.invalid.session.message": "Yritä käynnistää Minecraft uudelleen", "mco.error.invalid.session.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> istunto", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON><PERSON>", "mco.errorMessage.6002": "Käyttöehtoja ei ole hyväksytty", "mco.errorMessage.6003": "<PERSON><PERSON><PERSON><PERSON><PERSON>u", "mco.errorMessage.6004": "<PERSON><PERSON><PERSON><PERSON><PERSON>u", "mco.errorMessage.6005": "<PERSON><PERSON><PERSON> lukittu", "mco.errorMessage.6006": "<PERSON><PERSON><PERSON> on vanhentunut", "mco.errorMessage.6007": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on liian monessa Realmissa", "mco.errorMessage.6008": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "mco.errorMessage.6009": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON><PERSON> virhe, yrit<PERSON> uudelleen my<PERSON>.", "mco.errorMessage.generic": "Tapaht<PERSON> virhe: ", "mco.errorMessage.initialize.failed": "<PERSON><PERSON> alust<PERSON> ep<PERSON>", "mco.errorMessage.noDetails": "Virhetietoja ei annettu", "mco.errorMessage.realmsService": "Tapahtui virhe (%s):", "mco.errorMessage.realmsService.configurationError": "<PERSON><PERSON><PERSON> as<PERSON><PERSON> muokka<PERSON> tapahtui odottamaton virhe", "mco.errorMessage.realmsService.connectivity": "Realmiin ei voitu muodostaa yhteyttä: %s", "mco.errorMessage.realmsService.realmsError": "Realmsit (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Yhteensopivan version tarkistaminen epäonnistui: %s", "mco.errorMessage.retry": "Yritä operaatiota uudelleen", "mco.errorMessage.serviceBusy": "Realms on kiireinen tällä hetkellä.\nOle hyvä ja yritä liittyä uudelleen muutaman minuutin kuluttua.", "mco.gui.button": "<PERSON><PERSON><PERSON>", "mco.gui.ok": "Ok", "mco.info": "Tietoa!", "mco.invited.player.narration": "Pelaaja %s kutsuttu", "mco.invites.button.accept": "Hyväksy", "mco.invites.button.reject": "Hylkää", "mco.invites.nopending": "Ei avoimia kutsuja!", "mco.invites.pending": "Uusia kutsuja!", "mco.invites.title": "Avoimet kutsut", "mco.minigame.world.changeButton": "Valitse toinen minipeli", "mco.minigame.world.info.line1": "Tämä korvaa maailmasi tilapäisesti minipelillä!", "mco.minigame.world.info.line2": "<PERSON><PERSON> my<PERSON> palata alkuperäiseen maailmaasi menettämättä mitään.", "mco.minigame.world.noSelection": "<PERSON><PERSON> valinta", "mco.minigame.world.restore": "Lopetetaan minipeliä...", "mco.minigame.world.restore.question.line1": "Minipeli päättyy ja Realmisi palautetaan.", "mco.minigame.world.restore.question.line2": "<PERSON><PERSON><PERSON><PERSON> var<PERSON> jat<PERSON>?", "mco.minigame.world.selected": "Valittu minipeli:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "mco.minigame.world.startButton": "<PERSON><PERSON><PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Aloitetaan minipeli...", "mco.minigame.world.stopButton": "Lopeta minipeli", "mco.minigame.world.switch.new": "Valitse toinen minipeli?", "mco.minigame.world.switch.title": "Vaihda minipeliä", "mco.minigame.world.title": "Vaihda Realm minipeliin", "mco.news": "Realms-uutiset", "mco.notification.dismiss": "Hylkää", "mco.notification.transferSubscription.buttonText": "Siirrä nyt", "mco.notification.transferSubscription.message": "Java Realms -tilaukset siirtyvät Microsoft-kauppaan. Älä anna tilauksesi päättyä!\nSiirrä tilauksesi nyt ja saat 30 päivän Realmsin ilmaiseksi.\nMene Profiiliisi osoitteessa minecraft.net siirtääksesi tilauksesi.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON>", "mco.notification.visitUrl.message.default": "<PERSON><PERSON> alla oleva <PERSON>", "mco.onlinePlayers": "<PERSON><PERSON><PERSON> olevat pela<PERSON>t", "mco.play.button.realm.closed": "Realm on suljettu", "mco.question": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.experience": "Kokemukset", "mco.reset.world.generate": "<PERSON><PERSON><PERSON>", "mco.reset.world.inspiration": "Inspiraatio", "mco.reset.world.resetting.screen.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>...", "mco.reset.world.seed": "Lähtöarvo (Valinnainen)", "mco.reset.world.template": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.title": "<PERSON><PERSON><PERSON>", "mco.reset.world.upload": "<PERSON><PERSON><PERSON> ma<PERSON>", "mco.reset.world.warning": "Tämä kor<PERSON>a nykyisen Realmisi maailman", "mco.selectServer.buy": "Osta Realm!", "mco.selectServer.close": "Sulje", "mco.selectServer.closed": "Käytöstä poistettu Realm", "mco.selectServer.closeserver": "<PERSON><PERSON>", "mco.selectServer.configure": "Määritä Realm", "mco.selectServer.configureRealm": "Määritä Realm", "mco.selectServer.create": "Luo Realm", "mco.selectServer.create.subtitle": "Valitse minkä maailman laitat uuteen Realmiisi", "mco.selectServer.expired": "Erääntynyt Realm", "mco.selectServer.expiredList": "Tilauksesi on umpeutunut", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredTrial": "Kokeiluaikasi on loppunut", "mco.selectServer.expires.day": "Vanhenee päivässä", "mco.selectServer.expires.days": "Vanhenee %s päivässä", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON> pian", "mco.selectServer.leave": "<PERSON><PERSON><PERSON>", "mco.selectServer.loading": "Ladataan Realms-luetteloa", "mco.selectServer.mapOnlySupportedForVersion": "Tätä karttaa ei tueta versiossa %s", "mco.selectServer.minigame": "Minipeli:", "mco.selectServer.minigameName": "Minipeli: %s", "mco.selectServer.minigameNotSupportedInVersion": "Tätä minipeliä ei voi pelata versiossa %s", "mco.selectServer.noRealms": "Sinulla ei näytä olevan Realmia. Lisää Realm pelataksesi yhdessä kavereidesi kanssa.", "mco.selectServer.note": "Huom:", "mco.selectServer.open": "Avoin Realm", "mco.selectServer.openserver": "Avaa Realm", "mco.selectServer.play": "P<PERSON><PERSON>", "mco.selectServer.popup": "Realms on turvallinen ja yksinkertainen tapa nauttia Minecraft-maailmasta verkossa yhdessä jopa kymmenen kaverin kanssa. Se tukee useita minipelejä ja monia muokattuja maailmoja! Vain Realmin omistajan tarvitsee maksaa.", "mco.selectServer.purchase": "Lisää Realm", "mco.selectServer.trial": "<PERSON>i koke<PERSON>!", "mco.selectServer.uninitialized": "Klik<PERSON>a al<PERSON> uuden <PERSON>!", "mco.snapshot.createSnapshotPopup.text": "<PERSON><PERSON> l<PERSON>a ilmaista kehittäjäversion Realmia, joka yhdistetään maksulliseen Realms-tilaukseesi. Tämä kehittäjäversion Realm on käytettävissä niin kauan kuin sinulla on aktiivinen tilaus. Tämä ei vaikuta maksulliseen Realmiisi.", "mco.snapshot.createSnapshotPopup.title": "Luo kehittäjäversion Realm?", "mco.snapshot.creating": "<PERSON><PERSON><PERSON> kehittäjäversion Realmia...", "mco.snapshot.description": "Yhdistetty tilau<PERSON>een \"%s\"", "mco.snapshot.friendsRealm.downgrade": "Sinun tulee olla versiossa %s liittyäksesi tähän Realmiin", "mco.snapshot.friendsRealm.upgrade": "Pelaajan %s on päivitettävä Realminsa ennen kuin voit pelata siinä tällä versiolla", "mco.snapshot.paired": "Tämä kehittäjäversion Realm on yhdistetty tilaukseen \"%s\"", "mco.snapshot.parent.tooltip": "Käytä Minecraftin viimeisintä julkaisua pelataksesi tässä Realmissa", "mco.snapshot.start": "<PERSON><PERSON>a il<PERSON>inen kehittäjäversion Realm", "mco.snapshot.subscription.info": "Tämä on kehittäjäversion Realm, joka on yhdistetty Realmin \"%s\" tilaukseen. Se on käytettävissä kun sen yhteydessä oleva Realm on aktiivinen.", "mco.snapshot.tooltip": "Käytä kehittäjäversion Realmsia päästäksesi kurkistamaan Minecraftin tuleviin versioihin, jotka saattavat sisältää uusia ominaisuuksia ja muita muutoksia.\n\nLöydät tavalliset Realmisi pelin kokoversiosta.", "mco.snapshotRealmsPopup.message": "Realms on saatavilla kehittäjäversioissa alkaen versiosta 23w41a. Jokaisen Realms-tilauksen mukana tulee ilmainen kehittäjäversion Realm, joka on erillinen tavallisesta Java-version Realmistasi!", "mco.snapshotRealmsPopup.title": "Realms on nyt saatavilla kehittäjäversioissa", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON> l<PERSON>", "mco.template.button.publisher": "Julkaisija", "mco.template.button.select": "Valitse", "mco.template.button.trailer": "<PERSON><PERSON>", "mco.template.default.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.info.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.name": "<PERSON><PERSON>", "mco.template.select.failure": "<PERSON><PERSON> voineet hakea tämän kate<PERSON>ian sisä<PERSON>sluetteloa.\nTarkista internet-yhteytesi tai yritä myöhemmin uudelleen.", "mco.template.select.narrate.authors": "Tekijät: %s", "mco.template.select.narrate.version": "versio %s", "mco.template.select.none": "<PERSON><PERSON>, näyttää siltä, että tämä sisältökategoria on tällä hetkellä tyhjä. <PERSON><PERSON> my<PERSON>hemmin uudelleen uuden sisällön vuoksi, tai jos olet luoja, %s.", "mco.template.select.none.linkTitle": "harkitse jonkin lähettämistä itse", "mco.template.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.title.minigame": "Minipelit", "mco.template.trailer.tooltip": "Kartan traileri", "mco.terms.buttons.agree": "Hyväksy", "mco.terms.buttons.disagree": "Älä hyv<PERSON>ksy", "mco.terms.sentence.1": "Hyväksyn Minecraft Realmsin", "mco.terms.sentence.2": "Käyttöehdot", "mco.terms.title": "<PERSON><PERSON>", "mco.time.daysAgo": "%1$s päivä(ä) sitten", "mco.time.hoursAgo": "%1$s tunti(a) sitten", "mco.time.minutesAgo": "%1$s minuutti(a) sitten", "mco.time.now": "juuri nyt", "mco.time.secondsAgo": "%1$s sekunti(a) sitten", "mco.trial.message.line1": "Haluatko o<PERSON>in?", "mco.trial.message.line2": "Klikkaa tästä saadaksesi lisätietoja!", "mco.upload.button.name": "Lataa", "mco.upload.cancelled": "Lataus peru<PERSON>ttu", "mco.upload.close.failure": "<PERSON>ias<PERSON> ei voitu sulk<PERSON>, y<PERSON><PERSON><PERSON> u<PERSON>en", "mco.upload.done": "<PERSON><PERSON><PERSON> valmis", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Lataus epäonnistui! (%s)", "mco.upload.failed.too_big.description": "<PERSON><PERSON><PERSON> on liian suuri. <PERSON><PERSON><PERSON> sallittu koko on %s.", "mco.upload.failed.too_big.title": "<PERSON><PERSON><PERSON> on liian suuri", "mco.upload.hardcore": "<PERSON><PERSON>amamaailmoja ei voi ladata!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON><PERSON><PERSON>", "mco.upload.select.world.none": "Yksinpelimaailmoja ei löytynyt!", "mco.upload.select.world.subtitle": "Valitse yksinpelimaailma lad<PERSON>i", "mco.upload.select.world.title": "<PERSON><PERSON><PERSON> ma<PERSON>", "mco.upload.size.failure.line1": "'%s' on liian iso!", "mco.upload.size.failure.line2": "<PERSON> koko on %s. <PERSON><PERSON><PERSON> sa<PERSON>u koko on %s.", "mco.upload.uploading": "Ladataan '%s'", "mco.upload.verifying": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.version": "Versio: %s", "mco.warning": "Varoitus!", "mco.worldSlot.minigame": "Mini<PERSON><PERSON>", "menu.custom_options": "Mukautetut asetukset...", "menu.custom_options.title": "Mukautetut asetukset", "menu.custom_options.tooltip": "Huom: Mukautetut asetukset ovat kolmannen osapuolen palvelimien ja/tai sisältöjen tarjoamia.\nKäytä varoen!", "menu.custom_screen_info.button_narration": "T<PERSON>mä on mukautettu ruutu. Lue lisää.", "menu.custom_screen_info.contents": "Tämän ruudun sisältöä hallinnoivat kolmannen osapuolen palvelimet ja kartat, joita Mojang Studios tai Microsoft ei omista, käytä tai valvo.\n\nKäytä varoen! Ole aina varovainen klikatessasi linkkejä, <PERSON><PERSON>ä<PERSON><PERSON> koskaan anna henkilö<PERSON>hta<PERSON> tietojasi, kuten käyttäjätunnuksia ja salasanoja.\n\nJos tämä ruutu estää sinua pelaamasta, voit myös poistua nykyiseltä palvelimelta käyttämällä alla olevaa nappia.", "menu.custom_screen_info.disconnect": "Mukautettu ruutu hylätty", "menu.custom_screen_info.title": "<PERSON><PERSON><PERSON><PERSON> muka<PERSON> ruuduista", "menu.custom_screen_info.tooltip": "Tämä on mukautettu ruutu. Klikkaa tästä saadaksesi lisätietoa.", "menu.disconnect": "<PERSON><PERSON><PERSON> y<PERSON>eys", "menu.feedback": "Palaute...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.modded": " (Modattu)", "menu.multiplayer": "<PERSON><PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "Asetukset...", "menu.paused": "<PERSON><PERSON>", "menu.playdemo": "<PERSON><PERSON><PERSON> k<PERSON>", "menu.playerReporting": "<PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Valmistellaan synnyinaluetta: %s%%", "menu.quick_actions": "Pikatoiminnot...", "menu.quick_actions.title": "Pikatoiminnot", "menu.quit": "<PERSON><PERSON><PERSON>", "menu.reportBugs": "<PERSON><PERSON><PERSON>", "menu.resetdemo": "Tyhje<PERSON><PERSON> k<PERSON>", "menu.returnToGame": "<PERSON><PERSON><PERSON> peliin", "menu.returnToMenu": "Tallenna ja poistu päävalikkoon", "menu.savingChunks": "Tallennetaan lo<PERSON>", "menu.savingLevel": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "menu.sendFeedback": "<PERSON>", "menu.server_links": "Palvelinlinkit...", "menu.server_links.title": "Palvelinlinkit", "menu.shareToLan": "Jaa lähiverkkoon", "menu.singleplayer": "Yks<PERSON><PERSON><PERSON>", "menu.working": "Työstetään...", "merchant.deprecated": "Kyläläiset täydentävät varastonsa jopa 2x päivässä.", "merchant.level.1": "Noviisi", "merchant.level.2": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.3": "<PERSON><PERSON><PERSON><PERSON>", "merchant.level.4": "E<PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON><PERSON>", "merchant.title": "%s – %s", "merchant.trades": "Vaihtokaupat", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Hyppää pois pain<PERSON> %1$s", "multiplayer.applyingPack": "Asetetaan resurssipaket<PERSON>", "multiplayer.confirm_command.parse_errors": "Yrität suorittaa tuntematonta tai virheellistä komentoa.\n<PERSON><PERSON><PERSON> varma?\nKomento: %s", "multiplayer.confirm_command.permissions_required": "Yrität suorittaa komentoa, joka vaatii korkeampia o<PERSON>.\nTämä saattaa vaikuttaa peliin negatiivisesti.\n<PERSON><PERSON><PERSON> var<PERSON>?\nKomento: %s", "multiplayer.confirm_command.title": "Vahvista komennon suorittaminen", "multiplayer.disconnect.authservers_down": "Todennuspalvelimet ovat al<PERSON>a. Ole hyvä ja yritä myö<PERSON> uude<PERSON>, pahoittel<PERSON>!", "multiplayer.disconnect.bad_chat_index": "Vastaanotettu puuttuva tai uudelleenjärjestelty keskusteluviesti palvelimelta", "multiplayer.disconnect.banned": "Sinulla on porttikielto tälle palvelimelle", "multiplayer.disconnect.banned.expiration": "\nPorttikieltosi poistetaan %s", "multiplayer.disconnect.banned.reason": "Sinulla on porttikielto tälle palvelimelle.\nSyy: %s", "multiplayer.disconnect.banned_ip.expiration": "\nPorttikieltosi poistetaan %s", "multiplayer.disconnect.banned_ip.reason": "IP-osoitteellasi on porttikielto tälle palvelimelle.\nSyy: %s", "multiplayer.disconnect.chat_validation_failed": "Keskustelu<PERSON><PERSON> v<PERSON>n epäonnistui", "multiplayer.disconnect.duplicate_login": "Kir<PERSON><PERSON><PERSON> si<PERSON>än to<PERSON> si<PERSON>", "multiplayer.disconnect.expired_public_key": "Vanhentunut profiilin julkinen avain. Tarkista, että järjestelmäsi aika on synkronoitu, ja kokeile käynnistää peli uudelleen.", "multiplayer.disconnect.flying": "Lentäminen ei ole käytössä tällä palvelimella", "multiplayer.disconnect.generic": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "multiplayer.disconnect.idling": "<PERSON>t ollut toimetto<PERSON> liian kauan!", "multiplayer.disconnect.illegal_characters": "Kiellettyjä merkkejä keskustelussa", "multiplayer.disconnect.incompatible": "Yhteensopimaton asiakasohjelma! Ole hyvä ja käytä %s", "multiplayer.disconnect.invalid_entity_attacked": "Yritit hyökätä virheellisen olion kimppuun", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> vir<PERSON><PERSON> paketin", "multiplayer.disconnect.invalid_player_data": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "multiplayer.disconnect.invalid_player_movement": "<PERSON><PERSON><PERSON><PERSON><PERSON> pelaajan liikkumispaketti vastaanotettu", "multiplayer.disconnect.invalid_public_key_signature": "Virheellinen profiilin julkisen avaimen allekirjoitus.\nYritä käynnistää pelisi uudelleen.", "multiplayer.disconnect.invalid_public_key_signature.new": "Virheellinen profiilin julkisen avaimen allekirjoitus.\nYritä käynnistää pelisi uudelleen.", "multiplayer.disconnect.invalid_vehicle_movement": "Virheellinen ajoneuvon liikkumispaketti saatu", "multiplayer.disconnect.ip_banned": "IP-osoitteellesi on porttikielto tälle palvelimelle", "multiplayer.disconnect.kicked": "Potkaistu operaattorin toimesta", "multiplayer.disconnect.missing_tags": "Puutteellinen tunnistejoukko vast<PERSON>ttiin palvelimelta.\nOle hyvä ja ota yhteyttä palvelimen ylläpitäjään.", "multiplayer.disconnect.name_taken": "<PERSON><PERSON><PERSON><PERSON> nimi on jo käyt<PERSON>ä", "multiplayer.disconnect.not_whitelisted": "Et ole tämän palvelimen valkoisella listalla!", "multiplayer.disconnect.out_of_order_chat": "Virheellinen keskustelupaketti vastaanotettu. Muuttuiko jä<PERSON>stelmäsi aika?", "multiplayer.disconnect.outdated_client": "Yhteensopimaton asiakasohjelma! Ole hyvä ja käytä %s", "multiplayer.disconnect.outdated_server": "Yhteensopimaton asiakasohjelma! Ole hyvä ja käytä %s", "multiplayer.disconnect.server_full": "<PERSON><PERSON><PERSON> on t<PERSON><PERSON><PERSON>!", "multiplayer.disconnect.server_shutdown": "Palvelin suljettu", "multiplayer.disconnect.slow_login": "Kirjautumisessa kesti liian kauan", "multiplayer.disconnect.too_many_pending_chats": "Liikaa vahvistamattomia keskusteluviestejä", "multiplayer.disconnect.transfers_disabled": "Palvelin ei hyväksy siirtoja", "multiplayer.disconnect.unexpected_query_response": "Odottamatonta mukautettua dataa asiakasohjelmalta", "multiplayer.disconnect.unsigned_chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>, jonka all<PERSON><PERSON><PERSON><PERSON><PERSON> puuttuu tai on virheellinen.", "multiplayer.disconnect.unverified_username": "Käyttäjätunnuksen todentaminen epäonnistui!", "multiplayer.downloadingStats": "Palau<PERSON><PERSON><PERSON> tilastotietoja...", "multiplayer.downloadingTerrain": "La<PERSON>an maastoa...", "multiplayer.lan.server_found": "Uusi palvelin löydetty: %s", "multiplayer.message_not_delivered": "Keskusteluviestiä ei voida lähettää, tarkista palvelimen lokit: %s", "multiplayer.player.joined": "%s liittyi peliin", "multiplayer.player.joined.renamed": "%s (a<PERSON><PERSON>in tunnettu nimellä %s) liittyi peliin", "multiplayer.player.left": "%s poistui pelistä", "multiplayer.player.list.hp": "%sep", "multiplayer.player.list.narration": "Paikalla olevat pelaajat: %s", "multiplayer.requiredTexturePrompt.disconnect": "Pa<PERSON>lin vaatii mukautetun resurssipaketin", "multiplayer.requiredTexturePrompt.line1": "Tämä palvelin vaatii mukautetun resurssipaketin käyttöä.", "multiplayer.requiredTexturePrompt.line2": "Tämän mukautetun resurssipaketin hylkääminen katkaisee yhteyden palvelimeen.", "multiplayer.socialInteractions.not_available": "Sosiaaliset kanssakäymiset ovat saatavilla vain moninpelimaailmoissa", "multiplayer.status.and_more": "... ja %s lisää ...", "multiplayer.status.cancelled": "Peruutettu", "multiplayer.status.cannot_connect": "Palvelimeen ei saatu yhteyttä", "multiplayer.status.cannot_resolve": "Palvelimen osoitetta ei voitu ratkaista", "multiplayer.status.finished": "Val<PERSON>", "multiplayer.status.incompatible": "Yhteensopimaton versio!", "multiplayer.status.motd.narration": "Päivän viesti: %s", "multiplayer.status.no_connection": "(ei yhteyttä)", "multiplayer.status.old": "<PERSON><PERSON>", "multiplayer.status.online": "Verkossa", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Viive %s millisekuntia", "multiplayer.status.pinging": "Testataan yhteyttä...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s/%s pela<PERSON>a paikalla", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "Tilapyyntö on käsitelty", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Vastaanotettiin p<PERSON>ä<PERSON>ätö<PERSON> tila", "multiplayer.status.version.narration": "Palvelimen versio: %s", "multiplayer.stopSleeping": "Nouse sängystä", "multiplayer.texturePrompt.failure.line1": "Palvelimen resurssipakettia ei voitu käyttää", "multiplayer.texturePrompt.failure.line2": "Mukautettuja resursseja vaativat toiminnot eivät välttämättä toimi odotetusti", "multiplayer.texturePrompt.line1": "Tämä palvelin suosittelee muokatun resurssipaketin käyttöä.", "multiplayer.texturePrompt.line2": "Haluaisitko ladata ja asentaa sen automaagisesti?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nViesti palvelimelta:\n%s", "multiplayer.title": "<PERSON><PERSON><PERSON>", "multiplayer.unsecureserver.toast": "Tällä palvelimella lähetetyt viestit saattavat olla muokattuja, eivätkä välttämättä vastaa täysin alkuperäisiä viestejä", "multiplayer.unsecureserver.toast.title": "Keskusteluviestejä ei voida todentaa", "multiplayerWarning.check": "Älä näytä tätä uudelleen", "multiplayerWarning.header": "Huomio: <PERSON><PERSON><PERSON> o<PERSON>n mon<PERSON>peli", "multiplayerWarning.message": "Huomio: <PERSON>in<PERSON>i on kolmansien osapuolten palvelimien tarjoamia, jotka eivät ole Mojang Studiosin tai Microsoftin omistamia, operoimia tai valvomia. Moninpelin aikana saatat altistua valvomattomille keskusteluviesteille tai muulle muiden käyttäjien tekemälle sisä<PERSON>lle, joka ei välttämättä sovi kaikenikäisille.", "music.game.a_familiar_room": "<PERSON> – A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> – An Ordinary Day", "music.game.ancestry": "<PERSON> Ancestry", "music.game.below_and_above": "<PERSON> – Below and Above", "music.game.broken_clocks": "<PERSON> – Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 <PERSON> <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> – Comforting Memories", "music.game.creative.aria_math": "C418 – Aria Math", "music.game.creative.biome_fest": "C418 – Biome Fest", "music.game.creative.blind_spots": "C418 – Blind Spots", "music.game.creative.dreiton": "C418 <PERSON> <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 <PERSON> <PERSON><PERSON>", "music.game.creative.taswell": "C418 – Taswell", "music.game.crescent_dunes": "<PERSON> – Crescent Dunes", "music.game.danny": "C418 – <PERSON>", "music.game.deeper": "<PERSON> – <PERSON>er", "music.game.dry_hands": "C418 – Dry Hands", "music.game.echo_in_the_wind": "<PERSON> – Echo in the Wind", "music.game.eld_unknown": "<PERSON> – <PERSON><PERSON>", "music.game.end.alpha": "C418 – Alpha", "music.game.end.boss": "C418 – <PERSON>", "music.game.end.the_end": "C418 – The End", "music.game.endless": "<PERSON> – End<PERSON>", "music.game.featherfall": "<PERSON> – Featherfall", "music.game.fireflies": "<PERSON> – Fireflies", "music.game.floating_dream": "<PERSON><PERSON> – Floating Dream", "music.game.haggstrom": "C418 <PERSON>  <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> – Infinite Amethyst", "music.game.key": "C418 –  Key", "music.game.komorebi": "<PERSON><PERSON> komorebi", "music.game.left_to_bloom": "<PERSON> – Left to Bloom", "music.game.lilypad": "<PERSON> – Lily<PERSON>", "music.game.living_mice": "C418 – <PERSON> Mice", "music.game.mice_on_venus": "C418 <PERSON> <PERSON><PERSON> On Venus", "music.game.minecraft": "C418 – Mine<PERSON>", "music.game.nether.ballad_of_the_cats": "C418 – Ballad of the Cats", "music.game.nether.concrete_halls": "C418 – Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 – <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> – <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> – So Below", "music.game.nether.warmth": "C418 – Warmth", "music.game.one_more_day": "<PERSON> – One More Day", "music.game.os_piano": "<PERSON> – O's Piano", "music.game.oxygene": "C418 – Oxygene", "music.game.pokopoko": "<PERSON><PERSON> poko<PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> Puzzlebox", "music.game.stand_tall": "<PERSON> – Stand Tall", "music.game.subwoofer_lullaby": "C418 – Subwoofer Lullaby", "music.game.swamp.aerie": "<PERSON> <PERSON><PERSON>", "music.game.swamp.firebugs": "<PERSON> – Firebugs", "music.game.swamp.labyrinthine": "<PERSON> – Labryinthine", "music.game.sweden": "C418 – Sweden", "music.game.watcher": "<PERSON> – Watcher", "music.game.water.axolotl": "C418 – Axolotl", "music.game.water.dragon_fish": "C418 – <PERSON> Fish", "music.game.water.shuniji": "C418 – <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON> – <PERSON>", "music.game.wet_hands": "C418 – <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON> yakusoku", "music.menu.beginning_2": "C418 – Beginning 2", "music.menu.floating_trees": "C418 – Floating Trees", "music.menu.moog_city_2": "C418 – Moog City 2", "music.menu.mutation": "C418 – Mutation", "narration.button": "Painike: %s", "narration.button.usage.focused": "Paina Enteriä aktivoidaksesi", "narration.button.usage.hovered": "<PERSON><PERSON><PERSON><PERSON> hiiren vasenta painike<PERSON> aktivoidaks<PERSON>", "narration.checkbox": "Valintaruutu: %s", "narration.checkbox.usage.focused": "Paina Enteriä vai<PERSON>", "narration.checkbox.usage.hovered": "<PERSON><PERSON><PERSON><PERSON> hiiren vasenta <PERSON> v<PERSON>", "narration.component_list.usage": "<PERSON><PERSON> si<PERSON><PERSON><PERSON><PERSON> se<PERSON> k<PERSON>", "narration.cycle_button.usage.focused": "Paina Enteriä vaihtaaksesi %s", "narration.cycle_button.usage.hovered": "<PERSON><PERSON><PERSON><PERSON> hiiren vasenta pain<PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON> asetukseen %s", "narration.edit_box": "Muokkaa laatikkoa %s", "narration.item": "Esine: %s", "narration.recipe": "Resepti esineelle %s", "narration.recipe.usage": "<PERSON><PERSON><PERSON><PERSON> hiiren vasenta pain<PERSON> vali<PERSON>", "narration.recipe.usage.more": "<PERSON><PERSON><PERSON><PERSON> hiiren oikeaa painiketta nähdäksesi lisää reseptejä", "narration.selection.usage": "Paina nuolinäppäintä ylös tai alas siirty<PERSON><PERSON><PERSON> se<PERSON>an kohtaan", "narration.slider.usage.focused": "Paina nuolinäppäintä vasemmalle tai oikealle muuttaaksesi arvoa", "narration.slider.usage.hovered": "Vedä liukusäädintä muuttaaksesi arvoa", "narration.suggestion": "Valittu ehdotus %s / %s: %s", "narration.suggestion.tooltip": "Valittu ehdotus %s / %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "<PERSON><PERSON> si<PERSON><PERSON><PERSON><PERSON>", "narration.suggestion.usage.cycle.hidable": "Paina Tabia siirtyäks<PERSON> se<PERSON>an ehdotukseen tai Esciä poistuaksesi ehdotuksista", "narration.suggestion.usage.fill.fixed": "Paina Tabia k<PERSON>tää<PERSON><PERSON>", "narration.suggestion.usage.fill.hidable": "Paina Tabia käyttääksesi ehdotusta tai E<PERSON>ciä poistuaksesi ehdotuksista", "narration.tab_navigation.usage": "Paina Ctrl ja Ta<PERSON> vaihta<PERSON><PERSON> välilehteä", "narrator.button.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.locked": "Lukittu", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON>", "narrator.button.language": "<PERSON><PERSON>", "narrator.controls.bound": "Toiminnolla %s on näppäin %s", "narrator.controls.reset": "Nollaa toiminnon %s näppäin", "narrator.controls.unbound": "Toiminnolla %s ei ole näppäintä", "narrator.joining": "Liitytään", "narrator.loading": "Ladataan: %s", "narrator.loading.done": "Val<PERSON>", "narrator.position.list": "Valittu rivi %s / %s", "narrator.position.object_list": "Valittu rivin kohde %s / %s", "narrator.position.screen": "Valikon kohde %s / %s", "narrator.position.tab": "Valittu välilehti %s / %s", "narrator.ready_to_play": "<PERSON><PERSON>", "narrator.screen.title": "Päävalikko", "narrator.screen.usage": "Käytä hiiren kohdistinta tai Tab-näppäintä valitaksesi kohteen", "narrator.select": "Valittu: %s", "narrator.select.world": "Valittu %s, viimeksi pelattu; %s, %s, %s versio: %s", "narrator.select.world_info": "Valittu %s, viimeksi pelattu: %s, %s", "narrator.toast.disabled": "Kertoja poissa k<PERSON>ö<PERSON>ä", "narrator.toast.enabled": "Kertoja käytössä", "optimizeWorld.confirm.description": "Tämä yrittää optimoida maailmasi varmistaen, että kaikki tiedot tallennetaan uusimpaan pelimuotoon. Tämä voi kestää hyvin pitkään riippuen maailmasta. Kun se on tehty, maailmasi voi toimia nopeam<PERSON>, mutta ei enään ole yhteensopiva vanhempien versioiden kanssa. <PERSON><PERSON><PERSON><PERSON> varmasti jatkaa?", "optimizeWorld.confirm.proceed": "<PERSON><PERSON> varmuuskop<PERSON> ja optimoi", "optimizeWorld.confirm.title": "Optimoi ma<PERSON>ma", "optimizeWorld.info.converted": "Päivitetyt lohkot: %s", "optimizeWorld.info.skipped": "Ohitetut lohkot: %s", "optimizeWorld.info.total": "Lohkoja yhteensä: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Lasketaan lohkoja...", "optimizeWorld.stage.failed": "Epäonnistui! :(", "optimizeWorld.stage.finished": "Viimeistellään...", "optimizeWorld.stage.finished.chunks": "Viimeistellään lohkojen päivitystä...", "optimizeWorld.stage.finished.entities": "Viimeistellään olioiden päivitystä...", "optimizeWorld.stage.finished.poi": "Viimeistellään kiinnostavien kohteiden päivitystä...", "optimizeWorld.stage.upgrading": "Päivitetään kaikki lohkot...", "optimizeWorld.stage.upgrading.chunks": "Päivitetään kaikki lohkot...", "optimizeWorld.stage.upgrading.entities": "Päivitetään kaikki o<PERSON>...", "optimizeWorld.stage.upgrading.poi": "Päivitetään kaikki kiinnostavat kohteet...", "optimizeWorld.title": "Optimoidaan maail<PERSON>a '%s'", "options.accessibility": "Esteettömyysasetukset...", "options.accessibility.high_contrast": "<PERSON><PERSON>", "options.accessibility.high_contrast.error.tooltip": "<PERSON><PERSON> k<PERSON> resurssipaketti ei ole käytettävissä", "options.accessibility.high_contrast.tooltip": "Tehostaa k<PERSON>yttöliittymäelementtien kontrastia", "options.accessibility.high_contrast_block_outline": "Korkeakontrastiset kuutioiden ääriviivat", "options.accessibility.high_contrast_block_outline.tooltip": "Tehostaa tähtäimen kohteena olevan kuution ääriviivojen kontrastia.", "options.accessibility.link": "Esteettömyysopas", "options.accessibility.menu_background_blurriness": "Valikon taustan sumennus", "options.accessibility.menu_background_blurriness.tooltip": "<PERSON><PERSON><PERSON> valikon taustan sumeutta", "options.accessibility.narrator_hotkey": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "<PERSON><PERSON><PERSON> kert<PERSON>n asettamisen päälle tai pois näppäinkomennolla 'Komento+B'", "options.accessibility.narrator_hotkey.tooltip": "<PERSON><PERSON><PERSON> kert<PERSON>n asettamisen päälle tai pois näppäinkomennolla 'Ctrl+B'", "options.accessibility.panorama_speed": "Panoraaman vierity<PERSON>us", "options.accessibility.text_background": "<PERSON><PERSON><PERSON>", "options.accessibility.text_background.chat": "Keskustelu", "options.accessibility.text_background.everywhere": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.accessibility.text_background_opacity": "Taustan näkyvyys", "options.accessibility.title": "Esteettömyysasetukset...", "options.allowServerListing": "<PERSON><PERSON>", "options.allowServerListing.tooltip": "Palvelimet voivat luetella paikalla olevat pelaajat osana julkista tilaan<PERSON>.\nTämän asetuksen ollessa pois päältä, nimesi ei näy kyseisissä listoissa.", "options.ao": "<PERSON>eh<PERSON><PERSON> valaistus", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "<PERSON><PERSON>", "options.ao.off": "<PERSON><PERSON>", "options.attack.crosshair": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.attack.hotbar": "Pikapalkki", "options.attackIndicator": "Hyökkäysilmaisin", "options.audioDevice": "<PERSON><PERSON>", "options.audioDevice.default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.autoJump": "Automaattinen hyppy", "options.autoSuggestCommands": "Komentoehdotukset", "options.autosaveIndicator": "Automaattitallennuksen ilmaisin", "options.biomeBlendRadius": "Biomie<PERSON>", "options.biomeBlendRadius.1": "<PERSON><PERSON> (Nopein)", "options.biomeBlendRadius.11": "11x11 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.13": "13x13 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.15": "15x15 (<PERSON><PERSON><PERSON><PERSON>)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (<PERSON><PERSON>)", "options.biomeBlendRadius.7": "7x7 (Ko<PERSON><PERSON>)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON><PERSON>)", "options.chat": "Keskusteluasetukset...", "options.chat.color": "<PERSON><PERSON><PERSON>", "options.chat.delay": "Keskusteluviive: %s sekunti(a)", "options.chat.delay_none": "Keskusteluviive: <PERSON><PERSON> viivettä", "options.chat.height.focused": "Keski<PERSON><PERSON> kor<PERSON>", "options.chat.height.unfocused": "<PERSON><PERSON><PERSON> sul<PERSON>", "options.chat.line_spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.links": "Linkit", "options.chat.links.prompt": "Kysy linkkien avaamisesta", "options.chat.opacity": "<PERSON><PERSON><PERSON>", "options.chat.scale": "<PERSON><PERSON><PERSON> koko", "options.chat.title": "Keskusteluasetukset...", "options.chat.visibility": "Keskustelu", "options.chat.visibility.full": "Näkyy", "options.chat.visibility.hidden": "<PERSON><PERSON> n<PERSON>y", "options.chat.visibility.system": "<PERSON><PERSON> komennot", "options.chat.width": "<PERSON><PERSON><PERSON>", "options.chunks": "%s lohkoa", "options.clouds.fancy": "<PERSON><PERSON>", "options.clouds.fast": "<PERSON>a", "options.controls": "Näppäimet...", "options.credits_and_attribution": "Tekijät ja attribuutio...", "options.damageTiltStrength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.damageTiltStrength.tooltip": "Vahingosta aiheutuvan kameran heilahtamisen määrä.", "options.darkMojangStudiosBackgroundColor": "Mustavalkoinen logo", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON><PERSON><PERSON> lata<PERSON> ta<PERSON><PERSON>.", "options.darknessEffectScale": "<PERSON><PERSON><PERSON><PERSON>", "options.darknessEffectScale.tooltip": "Hallitsee <PERSON>a tai sculk-kirkujalta saadun pimeysvaikutuksen sykkimistä.", "options.difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy": "Helppo", "options.difficulty.easy.info": "Vihamielisiä o<PERSON> s<PERSON>, mutta ne tekevät vähemmän vahinkoa. <PERSON><PERSON><PERSON> tulee nälk<PERSON>, ja otat siitä vahinkoa 5 sydämeen saakka.", "options.difficulty.hard": "Vaikea", "options.difficulty.hard.info": "Vihamielisiä o<PERSON>, ja ne tekevät enemmän vahi<PERSON>. <PERSON><PERSON><PERSON> tulee nälk<PERSON>, ja voit kuolla siihen.", "options.difficulty.hardcore": "<PERSON><PERSON><PERSON>", "options.difficulty.normal": "<PERSON><PERSON>", "options.difficulty.normal.info": "Vihamielisiä o<PERSON> s<PERSON>, ja ne tekevät vahinkoa normaalisti. <PERSON><PERSON><PERSON> tulee nälk<PERSON>, ja otat siitä vahinkoa puoleen sydämeen saakka.", "options.difficulty.online": "Palvelimen vaikeustaso", "options.difficulty.peaceful": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.peaceful.info": "Vihamielisiä olentoja ei synny lainkaan ja neutraaleja olentojakin vain vähän. <PERSON>ulle ei tule nälkä, ja elämäpisteet palautuvat ajan my<PERSON>.", "options.directionalAudio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.directionalAudio.off.tooltip": "Klassinen stereoääni", "options.directionalAudio.on.tooltip": "Käyttää HRTF-pohjaista tilaääntä 3D-äänen simuloinnin parantamiseksi. Vaatii HRTF-yhteensopivan äänilaitteiston ja toimii parhaiten kuulok<PERSON>illa.", "options.discrete_mouse_scroll": "<PERSON><PERSON><PERSON> r<PERSON>", "options.entityDistanceScaling": "<PERSON><PERSON><PERSON>ä<PERSON>", "options.entityShadows": "<PERSON><PERSON><PERSON>", "options.font": "Fonttiasetukset...", "options.font.title": "Fonttiasetukset", "options.forceUnicodeFont": "Pakota Unicode-fontti", "options.fov": "Näkökenttä", "options.fov.max": "Quake-mestari", "options.fov.min": "<PERSON><PERSON><PERSON><PERSON>", "options.fovEffectScale": "Näkökenttätehosteet", "options.fovEffectScale.tooltip": "Hallitsee näkökentän muuttumista pelin vaikutusten mukaisesti.", "options.framerate": "%s fps", "options.framerateLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.framerateLimit.max": "<PERSON><PERSON>", "options.fullscreen": "Ko<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.current": "<PERSON><PERSON><PERSON><PERSON>", "options.fullscreen.entry": "%sx%s@%s (%s-bittinen)", "options.fullscreen.resolution": "Kokon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> resoluutio", "options.fullscreen.unavailable": "As<PERSON> ei ole sa<PERSON>", "options.gamma": "<PERSON><PERSON><PERSON>", "options.gamma.default": "<PERSON><PERSON>", "options.gamma.max": "<PERSON><PERSON>", "options.gamma.min": "Tunnelmallinen", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON><PERSON><PERSON> nopeus", "options.glintSpeed.tooltip": "Hallitsee lumottujen esineiden visuaalisen hehkun nopeutta.", "options.glintStrength": "<PERSON>h<PERSON><PERSON> voimakkuus", "options.glintStrength.tooltip": "Hallitsee lumottujen esineiden visuaalisen hehkun läpinäkyvyyttä.", "options.graphics": "Grafiikat", "options.graphics.fabulous": "Upea!", "options.graphics.fabulous.tooltip": "%s grafiiikka käyttää näytön varjostimia sään, pilvien ja partikkelien piirtämiseen, jotka ovat läpinäkyvien kuutioiden ja veden takana. Tämä saattaa vaikuttaa merkittävästi kannettavien laitteiden ja 4K-näyttöjen suorituskykyyn.", "options.graphics.fancy": "<PERSON><PERSON>", "options.graphics.fancy.tooltip": "Hienot grafiikat tasoittavat suorituskyvyn ja laadun suurimmalle osalle laitteista.\nSää, pilvet ja partikkelit eivät saata näkyä läpinäkyvien kuutioiden ja veden läpi.", "options.graphics.fast": "<PERSON>a", "options.graphics.fast.tooltip": "Nopeat grafiikat vähentävät näkyvän sateen ja lumen määrää.\nLäpinäkyvyystehosteet eivät ole käytössä useimmissa kuutioissa, kuten lehdiss<PERSON>.", "options.graphics.warning.accept": "Jatka ilman tukea", "options.graphics.warning.cancel": "<PERSON>ie minut takaisin", "options.graphics.warning.message": "Grafiikkalaitteesi havaittiin olevan tukematon %s-grafiikkavaihtoehdolle.\n\nVoit jättää tämän huomiotta ja jatkaa, mutta tukea ei anneta laitteellesi jos aiot käyttää %s-vaihtoehtoa.", "options.graphics.warning.renderer": "Renderöijä havaittu: [%s]", "options.graphics.warning.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> tukematon", "options.graphics.warning.vendor": "Renderoija havaittu: [%s]", "options.graphics.warning.version": "OpenGL-versio havaittu: [%s]", "options.guiScale": "Valikon koko", "options.guiScale.auto": "Automaattinen", "options.hidden": "Piilotettu", "options.hideLightningFlashes": "Piilota salaman välähdykset", "options.hideLightningFlashes.tooltip": "Estää taivaan välähtelyn salaman iskiessä. Salamat itse näkyvät edelleen.", "options.hideMatchedNames": "<PERSON><PERSON><PERSON> nimet", "options.hideMatchedNames.tooltip": "Kolmannen osapuolen palvelimet saattavat lähettää keskusteluviestejä standardista poikkeavissa muodoissa.\nTämän asetuksen ollessa päällä, piilotetut pelaajat sovitetaan keskusteluun osallistuneiden nimien perusteella.", "options.hideSplashTexts": "<PERSON><PERSON><PERSON> h<PERSON>ks<PERSON>", "options.hideSplashTexts.tooltip": "Piilottaa keltaisen huomiotekstin päävalikosta.", "options.inactivityFpsLimit": "Pienennä kuvat<PERSON>a", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Raj<PERSON><PERSON>a ku<PERSON>n 30:een, kun peli ei saa mitään pelaajan s<PERSON>tteitä yli minuuttiin. Rajoittaa sen edelleen 10:een 9 minuutin kuluttua.", "options.inactivityFpsLimit.minimized": "Minimoitu", "options.inactivityFpsLimit.minimized.tooltip": "<PERSON><PERSON><PERSON><PERSON> kuvat<PERSON>a kun ikkuna on pienennetty.", "options.invertMouse": "Käänteinen hiiri", "options.japaneseGlyphVariants": "Japanilaiset merkkivariantit", "options.japaneseGlyphVariants.tooltip": "Käyttää japanilaisia variantteja CJK-kirjoitusmerkeistä oletusfontissa", "options.key.hold": "<PERSON><PERSON><PERSON>", "options.key.toggle": "<PERSON><PERSON>", "options.language": "Kieli...", "options.language.title": "<PERSON><PERSON>", "options.languageAccuracyWarning": "(Käännökset eivät välttämättä ole 100%% tarkkoja)", "options.languageWarning": "Käännökset eivät välttämättä ole 100%% tarkkoja", "options.mainHand": "K<PERSON><PERSON><PERSON><PERSON>", "options.mainHand.left": "<PERSON><PERSON><PERSON>", "options.mainHand.right": "<PERSON><PERSON><PERSON>", "options.mipmapLevels": "Mipmap-tasot", "options.modelPart.cape": "Viitta", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON><PERSON>", "options.modelPart.left_pants_leg": "<PERSON><PERSON><PERSON>", "options.modelPart.left_sleeve": "<PERSON><PERSON><PERSON> <PERSON>ha", "options.modelPart.right_pants_leg": "<PERSON><PERSON><PERSON>", "options.modelPart.right_sleeve": "<PERSON><PERSON><PERSON> hiha", "options.mouseWheelSensitivity": "<PERSON><PERSON><PERSON>", "options.mouse_settings": "Hiiriasetukset...", "options.mouse_settings.title": "Hiiriasetukset", "options.multiplayer.title": "Moninpeliasetukset...", "options.multiplier": "%sx", "options.music_frequency": "<PERSON><PERSON><PERSON><PERSON> es<PERSON><PERSON>", "options.music_frequency.constant": "<PERSON><PERSON><PERSON><PERSON>", "options.music_frequency.default": "<PERSON><PERSON>", "options.music_frequency.frequent": "Tiheä", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON>, kuinka usein musiikkia soi pelima<PERSON>a.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "<PERSON><PERSON><PERSON><PERSON> kaiken", "options.narrator.chat": "Selostaa keskustelun", "options.narrator.notavailable": "Ei sa<PERSON>villa", "options.narrator.off": "<PERSON><PERSON>", "options.narrator.system": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>lmän", "options.notifications.display_time": "Ilmoitusten kesto", "options.notifications.display_time.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuinka pitk<PERSON>n kaikki il<PERSON> nä<PERSON>v<PERSON> n<PERSON>ytöllä.", "options.off": "<PERSON><PERSON>", "options.off.composed": "%s: <PERSON><PERSON>", "options.on": "Päällä", "options.on.composed": "%s: <PERSON><PERSON><PERSON><PERSON><PERSON>", "options.online": "Verkossa...", "options.online.title": "Verkkopelaamisen asetukset", "options.onlyShowSecureChat": "Vain suojattu keskustelu", "options.onlyShowSecureChat.tooltip": "Näytä vain sellaiset muiden pelaajien viestit, jotka ovat varmasti pelaajien itse lähettämiä ja joita ei ole muokattu.", "options.operatorItemsTab": "Operaattoriesineet", "options.particles": "Partikkelit", "options.particles.all": "<PERSON><PERSON><PERSON>", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "<PERSON><PERSON><PERSON><PERSON>", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Lohkotus", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON><PERSON> kasa<PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "<PERSON><PERSON><PERSON> toimet lohkossa tulevat kokoamaan lohkon välittömästi uudelleen. Tämä sisältää kuutioiden asettamisen ja tuh<PERSON>.", "options.prioritizeChunkUpdates.nearby": "<PERSON><PERSON><PERSON><PERSON> kasa<PERSON>", "options.prioritizeChunkUpdates.nearby.tooltip": "Läheiset lohkot kootaan aina välittömästi. Tämä saattaa vaikuttaa pelin suorituskykyyn kuutioita as<PERSON><PERSON>a tai tuhott<PERSON>.", "options.prioritizeChunkUpdates.none": "Säikeistetty", "options.prioritizeChunkUpdates.none.tooltip": "Läheiset lohkot kootaan rinnakkaisina ketjuina. Tämä saattaa aiheuttaa lyhytaikaisia visuaalisia aukkoja kuutioiden tuhoutuessa.", "options.rawMouseInput": "Raakasyöte", "options.realmsNotifications": "Realms-uutiset ja -kutsut", "options.realmsNotifications.tooltip": "Hakee Realms-uutiset ja -kutsut päävalikosta ja näyttää niiden vastaavat kuvakkeet Realms-painikkessa.", "options.reducedDebugInfo": "Vähennetyt virheenkorjaustiedot", "options.renderClouds": "<PERSON><PERSON><PERSON>", "options.renderCloudsDistance": "Pilvien etäisyys", "options.renderDistance": "Näköetäisyys", "options.resourcepack": "Resurssipaketit...", "options.rotateWithMinecart": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>u<PERSON>n mukana", "options.rotateWithMinecart.tooltip": "Kääntyykö pelaajan kuvakulma kaivosvaunun mukana. Saatavilla vain ma<PERSON>issa, j<PERSON><PERSON> as<PERSON> \"Kaivosvaunuparannukset\" on otettu käyttöön.", "options.screenEffectScale": "Vääristymätehosteet", "options.screenEffectScale.tooltip": "Pahoinvoinnin ja <PERSON>her-portaalin näytönvääristystehosteiden voimakkuus.\nAlemmilla arvoilla pahoinvointitehoste korvataan vihreällä peitteellä.", "options.sensitivity": "Herkkyys", "options.sensitivity.max": "HYPERNOPEUS!!!", "options.sensitivity.min": "*huoh*", "options.showNowPlayingToast": "Näytä musiikin ponnahdusilmoitus", "options.showNowPlayingToast.tooltip": "Näyttää ponnahdusilmoituksen kappaleen alkaessa soida. Sama ilmoitus näkyy jatkuvasti pelin taukoru<PERSON>ussa kappaleen soidessa.", "options.showSubtitles": "Näytä tekstitykset", "options.simulationDistance": "Mallinnusetäisyys", "options.skinCustomisation": "Skinin muokkaus...", "options.skinCustomisation.title": "<PERSON><PERSON> muokkaus", "options.sounds": "<PERSON><PERSON><PERSON><PERSON> ja <PERSON>...", "options.sounds.title": "Musi<PERSON><PERSON>- ja <PERSON>", "options.telemetry": "Telemetriatiedot...", "options.telemetry.button": "<PERSON><PERSON><PERSON><PERSON><PERSON> ker<PERSON>", "options.telemetry.button.tooltip": "\"%s\" sisältää vain pakolliset tiedot.\n\"%s\" sisältää pakollisten tietojen lisäksi myö<PERSON> valinnaiset tiedot.", "options.telemetry.disabled": "Telemetria on poissa käytöstä.", "options.telemetry.state.all": "<PERSON><PERSON><PERSON>", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.telemetry.state.none": "<PERSON><PERSON> mit<PERSON>n", "options.title": "Asetukset", "options.touchscreen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.video": "Videoasetukset...", "options.videoTitle": "Videoasetukset", "options.viewBobbing": "<PERSON><PERSON>", "options.visible": "Näkyvillä", "options.vsync": "VSync", "outOfMemory.message": "Minecraftilta loppui muisti.\n\nTämä saattaa johtua virheestä pelissä tai siitä, ettei Java Virtual Machine saanut käyttöönsä tarpeeksi muistia.\n\nMaailmojen korruptoitumisen estämiseksi sinut poistettiin pelistä. Yritimme vapauttaa muistia päästäksesi takaisin päävalik<PERSON>on ja pela<PERSON>an, mutta tämä ei välttämättä toiminut.\n\nOle hyvä ja käynnistä peli uudelleen mikäli vielä näet tämän viestin.", "outOfMemory.title": "Mu<PERSON>i loppu!", "pack.available.title": "Saatavilla", "pack.copyFailure": "<PERSON><PERSON>en kopiointi epäonnistui", "pack.dropConfirm": "Haluatko lisätä seuraavat paketit Minecraftiin?", "pack.dropInfo": "<PERSON><PERSON><PERSON> ja pudota tied<PERSON>ot tähän ikkunaan lisätäks<PERSON> paketteja", "pack.dropRejected.message": "<PERSON><PERSON><PERSON>t kohteet eivät olleet kelvollisia paketteja, eikä niitä kopioitu:\n %s", "pack.dropRejected.title": "<PERSON><PERSON><PERSON><PERSON>, jotka eivät ole paketteja", "pack.folderInfo": "(<PERSON><PERSON> p<PERSON>dos<PERSON>t tänne)", "pack.incompatible": "Yhteensopimaton", "pack.incompatible.confirm.new": "<PERSON><PERSON><PERSON><PERSON> paketti tehtiin uudemmalle Minecraft-versiolle eikä välttämättä toimi enää oikein.", "pack.incompatible.confirm.old": "<PERSON><PERSON><PERSON><PERSON> paketti tehtiin vanhemmalle Minecraft-versiolle eikä välttämättä toimi enää oikein.", "pack.incompatible.confirm.title": "<PERSON><PERSON><PERSON><PERSON> varmasti ladata tämän paketin?", "pack.incompatible.new": "(Tehty Minecraftin uudemmalle versiolle)", "pack.incompatible.old": "(<PERSON><PERSON><PERSON> Minecraftin vanhem<PERSON> versiolle)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON>", "pack.selected.title": "Valittuna", "pack.source.builtin": "sisäänrakennettu", "pack.source.feature": "ominaisuus", "pack.source.local": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.server": "palvelin", "pack.source.world": "<PERSON><PERSON><PERSON>", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON> pommitettu onnistuneesti", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "Kukkakimppu", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON><PERSON>", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Lavasteiden vaihto", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Maa", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "Viimeinen vihollinen", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Tai<PERSON>li<PERSON><PERSON>", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Löytö", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Nöyryys", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "<PERSON><PERSON><PERSON> k<PERSON> pepperonilla", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "<PERSON><PERSON>", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Sitruunapöllö", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Käytävä", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON><PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "Allas", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Preeriaratsastaja", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Merinäköala", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "<PERSON>llo ja ruusut", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Näyttämö on valmis", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Auringonkukat", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "Auringonlasku", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "A<PERSON>t", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "<PERSON>i pakettia", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "Tyhjyys", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Vaeltaja", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "E<PERSON>ä<PERSON><PERSON>", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON>", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON>", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "<PERSON><PERSON><PERSON><PERSON> teos", "parsing.bool.expected": "Odotettiin booleania", "parsing.bool.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>, odotettiin 'true' tai 'false' mutta sinulla on '%s'", "parsing.double.expected": "<PERSON><PERSON><PERSON><PERSON> des<PERSON>", "parsing.double.invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> desimaali ’%s’", "parsing.expected": "Odotettiin ’%s’", "parsing.float.expected": "<PERSON><PERSON><PERSON><PERSON>", "parsing.float.invalid": "Virheellinen murtoarvo ’%s’", "parsing.int.expected": "Odotettiin numeroa", "parsing.int.invalid": "Virheellinen numero '%s'", "parsing.long.expected": "Odotettiin pitk<PERSON> koko<PERSON>ua", "parsing.long.invalid": "Virheellinen pitkä kokonaisluku '%s'", "parsing.quote.escape": "Virheellinen poistumisjärjestys '\\%s' lainausmerkeissä", "parsing.quote.expected.end": "Avonainen lainattu merk<PERSON>", "parsing.quote.expected.start": "Odotettiin lainausta merk<PERSON> al<PERSON>n", "particle.invalidOptions": "Ei voida jäsentää partikkeliasetuksia: %s", "particle.notFound": "Tuntematon partikkeli: %s", "permissions.requires.entity": "<PERSON><PERSON><PERSON> tarvi<PERSON>an tämän komennon suoritta<PERSON>en", "permissions.requires.player": "<PERSON><PERSON><PERSON><PERSON> tarvitaan tämän komennon suoritta<PERSON>en", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Käytettäessä:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Tuntematon predikaatti: %s", "quickplay.error.invalid_identifier": "<PERSON><PERSON> l<PERSON>y ma<PERSON><PERSON>a annetulla tun<PERSON>", "quickplay.error.realm_connect": "Realmiin ei voitu muodostaa yhteyttä", "quickplay.error.realm_permission": "<PERSON>ulla ei ole oikeuksia liittyä tähän Realmiin", "quickplay.error.title": "Pikapeliin yhdistäminen epäonnistui", "realms.configuration.region.australia_east": "<PERSON><PERSON><PERSON>-Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasilia", "realms.configuration.region.central_india": "Intia", "realms.configuration.region.central_us": "Iowa, Yhdysvallat", "realms.configuration.region.east_asia": "Hongkong", "realms.configuration.region.east_us": "Virginia, Yhdysvallat", "realms.configuration.region.east_us_2": "Pohjois-Carolina, Yhdysvallat", "realms.configuration.region.france_central": "Ranska", "realms.configuration.region.japan_east": "Itä-Japani", "realms.configuration.region.japan_west": "Länsi-Japani", "realms.configuration.region.korea_central": "Etelä-Korea", "realms.configuration.region.north_central_us": "Illinois, Yhdysvallat", "realms.configuration.region.north_europe": "Irlanti", "realms.configuration.region.south_central_us": "Teksas, Yhdysvallat", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.uae_north": "Yhdistyneet arabiemiirikunnat", "realms.configuration.region.uk_south": "Etelä-Englanti", "realms.configuration.region.west_central_us": "Utah, Yhdysvallat", "realms.configuration.region.west_europe": "Alankomaat", "realms.configuration.region.west_us": "Kalifornia, Yhdysvallat", "realms.configuration.region.west_us_2": "Washington, Yhdysvallat", "realms.configuration.region_preference.automatic_owner": "Automaattinen (Realmin omistajan viive)", "realms.configuration.region_preference.automatic_player": "Automaattinen (ensimmäiseksi liittynyt)", "realms.missing.snapshot.error.text": "Realms ei ole yhteensopiva kehittäjäversioiden kanssa", "recipe.notFound": "Tuntematon resepti: %s", "recipe.toast.description": "Tarkista reseptikirjasi", "recipe.toast.title": "Uusia reseptejä avattu!", "record.nowPlaying": "Nyt soi: %s", "recover_world.bug_tracker": "<PERSON><PERSON><PERSON>", "recover_world.button": "<PERSON><PERSON><PERSON> pala<PERSON>", "recover_world.done.failed": "Edellisestä versiosta palauttaminen epäonnistui.", "recover_world.done.success": "Palauttaminen onnistui!", "recover_world.done.title": "Palauttaminen valmis", "recover_world.issue.missing_file": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recover_world.issue.none": "<PERSON><PERSON> on<PERSON>", "recover_world.message": "<PERSON><PERSON><PERSON><PERSON> on<PERSON>, kun yritettiin lukea ma<PERSON>mankan<PERSON> \"%s\".\nOn ehkä mahdollista palauttaa maailma aiemmasta tilasta, tai voit ilmoittaa virheestä bugien seurantaan.", "recover_world.no_fallback": "<PERSON><PERSON><PERSON>, josta p<PERSON>, ei ole sa<PERSON>", "recover_world.restore": "<PERSON><PERSON><PERSON> pala<PERSON>", "recover_world.restoring": "Yritetään palauttaa ma<PERSON>a...", "recover_world.state_entry": "Versio ajalta %s: ", "recover_world.state_entry.unknown": "tunt<PERSON><PERSON>", "recover_world.title": "<PERSON><PERSON><PERSON> e<PERSON>", "recover_world.warning": "Ma<PERSON><PERSON> yhteenvedon lataaminen epäonnistui", "resourcePack.broken_assets": "VIRHEELLISIÄ RESURSSITIEDOSTOINDEKSEJÄ HAVAITTU", "resourcePack.high_contrast.name": "<PERSON><PERSON>", "resourcePack.load_fail": "Resurssien lataaminen epäonnistui", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "<PERSON><PERSON><PERSON> resurs<PERSON><PERSON>ketissa havaittu", "resourcePack.server.name": "Maailmakohtaiset resurssit", "resourcePack.title": "Valitse resurssipaketit", "resourcePack.vanilla.description": "Minecraftin alkuperäinen ulkoasu ja tunnelma", "resourcePack.vanilla.name": "<PERSON><PERSON>", "resourcepack.downloading": "Ladataan resurssipaket<PERSON>", "resourcepack.progress": "Ladataan tiedostoa (%s MB)...", "resourcepack.requesting": "Pyyntöä tehdään...", "screenshot.failure": "Kuvankaappausta ei voitu tallentaa: %s", "screenshot.success": "Kuvankaappaus tallennettu nimellä %s", "selectServer.add": "<PERSON>s<PERSON><PERSON> palvelin", "selectServer.defaultName": "Minecraft-palvelin", "selectServer.delete": "Poista", "selectServer.deleteButton": "Poista", "selectServer.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän palvelimen?", "selectServer.deleteWarning": "'%s' poiste<PERSON>an lopullisesti! (Pitkä aika!)", "selectServer.direct": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.hiddenAddress": "(Piilotettu)", "selectServer.refresh": "Päivitä", "selectServer.select": "<PERSON><PERSON>", "selectWorld.access_failure": "Ma<PERSON>maan pä<PERSON><PERSON> ep<PERSON>on<PERSON>", "selectWorld.allowCommands": "<PERSON><PERSON>", "selectWorld.allowCommands.info": "<PERSON><PERSON><PERSON> kuten /gamemode, /experience", "selectWorld.allowCommands.new": "<PERSON><PERSON> komennot", "selectWorld.backupEraseCache": "Pyyhi välimuisti", "selectWorld.backupJoinConfirmButton": "<PERSON><PERSON><PERSON> ja lataa", "selectWorld.backupJoinSkipButton": "Tiedän mitä olen tekemässä!", "selectWorld.backupQuestion.customized": "Mukautettuja maailmoja ei enää tueta", "selectWorld.backupQuestion.downgrade": "<PERSON><PERSON><PERSON> palauttamista a<PERSON>mpaan versioon ei tueta", "selectWorld.backupQuestion.experimental": "Kokeellisia asetuksia käyttäviä maailmoja ei tueta", "selectWorld.backupQuestion.snapshot": "<PERSON><PERSON><PERSON><PERSON> varmasti ladata tämän ma<PERSON>man?", "selectWorld.backupWarning.customized": "Valitettavasti emme tue mukautettuja maailmoja tässä Minecraftin versiossa. Voimme silti ladata tämän maailman ja pitää kaiken samana, mutta uusi maasto ei tule enää olemaan mukautettua. Pahoittelemme hankaluutta!", "selectWorld.backupWarning.downgrade": "Tätä maailmaa pelattiin viimeksi versiossa %s; käytät nyt versiota %s. <PERSON><PERSON><PERSON> palauttaminen aiempaan versioon voi aiheuttaa vaurioita tiedostoon – emme voi luvata, että maailma latautuu tai toimii. <PERSON><PERSON> haluat silti jatkaa, on suositeltavaa, että luot ensin varmuuskopion maailmasta.", "selectWorld.backupWarning.experimental": "Tämä maailma käyttää kokeellisia asetuksia, jotka voivat lakata toimimasta milloin tahansa. Emme voi taata niiden latautuvan tai toimivan.", "selectWorld.backupWarning.snapshot": "Tätä maailmaa on viimeksi pelattu versiossa %s; olet tällä hetkellä versiossa %s. Luothan varmuuskopion siltä varalta että maailmasi korruptoituu.", "selectWorld.bonusItems": "Bonusarkku", "selectWorld.cheats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.commands": "<PERSON><PERSON><PERSON>", "selectWorld.conversion": "<PERSON><PERSON><PERSON> muuntaa!", "selectWorld.conversion.tooltip": "<PERSON><PERSON><PERSON><PERSON> maailma tulee avata van<PERSON> vers<PERSON> (kuten 1.6.4), jotta sen mu<PERSON><PERSON>n olisi turvallista", "selectWorld.create": "<PERSON><PERSON> uusi ma<PERSON>", "selectWorld.customizeType": "<PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Datapaketit", "selectWorld.data_read": "<PERSON><PERSON><PERSON> ma<PERSON>man tie<PERSON>...", "selectWorld.delete": "Poista", "selectWorld.deleteButton": "Poista", "selectWorld.deleteQuestion": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän ma<PERSON>?", "selectWorld.deleteWarning": "'%s' poiste<PERSON>an lopullisesti! (Pitkä aika!)", "selectWorld.delete_failure": "<PERSON><PERSON><PERSON> poistaminen ep<PERSON><PERSON>ui", "selectWorld.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backup": "<PERSON><PERSON>", "selectWorld.edit.backupCreated": "Varmuuskopioitu: %s", "selectWorld.edit.backupFailed": "Varmuuskopionti epäonnistui", "selectWorld.edit.backupFolder": "<PERSON><PERSON>", "selectWorld.edit.backupSize": "koko: %s MB", "selectWorld.edit.export_worldgen_settings": "<PERSON><PERSON>", "selectWorld.edit.export_worldgen_settings.failure": "Vienti epäonnistui", "selectWorld.edit.export_worldgen_settings.success": "Vietiin", "selectWorld.edit.openFolder": "<PERSON><PERSON>", "selectWorld.edit.optimize": "Optimoi ma<PERSON>ma", "selectWorld.edit.resetIcon": "<PERSON><PERSON><PERSON> k<PERSON>", "selectWorld.edit.save": "<PERSON><PERSON><PERSON>", "selectWorld.edit.title": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>a", "selectWorld.enterName": "<PERSON><PERSON><PERSON> nimi", "selectWorld.enterSeed": "Lähtöarvo maailman luomista varten", "selectWorld.experimental": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.experimental.details": "Yksityiskohdat", "selectWorld.experimental.details.entry": "Vaaditut kokeelliset ominaisuudet: %s", "selectWorld.experimental.details.title": "Kokeellisten ominaisuuksien vaatimukset", "selectWorld.experimental.message": "Ole varovainen!\nTämä kokoonpano vaatii ominaisuuksia, jotka ovat vasta kehitteillä. Maailmas<PERSON> saattaa kaatua, hajota tai lakata toimimasta tulevissa versioissa.", "selectWorld.experimental.title": "Kokeellisten ominaisuuksien varoitus", "selectWorld.experiments": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "selectWorld.experiments.info": "Kokeelliset ominaisuudet ovat mahdollisia uusia ominaisuuksia. <PERSON>, sillä jotain saattaa hajota. Kokeellisia ominaisuuksia ei voi poistaa käytöstä maailman luomisen jälk<PERSON>.", "selectWorld.futureworld.error.text": "Jo<PERSON> meni vikaan ladattaessa maailmaa tulevasta versiosta. Tämä oli alunperinkin riskialtis operaatio; pahoittelut ettei se onnistunut.", "selectWorld.futureworld.error.title": "Ta<PERSON><PERSON>ui virhe!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "Se<PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "<PERSON><PERSON> kuin sel<PERSON><PERSON><PERSON><PERSON><PERSON>, mutta kuutioita ei voi asettaa tai poistaa.", "selectWorld.gameMode.adventure.line1": "<PERSON>a kuin sel<PERSON><PERSON><PERSON><PERSON><PERSON>, mutta kuuti<PERSON>a ei", "selectWorld.gameMode.adventure.line2": "voi lisätä tai poistaa", "selectWorld.gameMode.creative": "<PERSON><PERSON>", "selectWorld.gameMode.creative.info": "<PERSON><PERSON>, rake<PERSON> ja tutki ilman rajoituk<PERSON>. <PERSON><PERSON>, sin<PERSON><PERSON> on loputtomasti materiaaleja ja hirviöt eivät voi vahingoittaa sinua.", "selectWorld.gameMode.creative.line1": "Loputtomasti resursseja, lenn<PERSON> vapaasti ja", "selectWorld.gameMode.creative.line2": "tuhoa kuutioita välittömästi", "selectWorld.gameMode.hardcore": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.hardcore.info": "Sel<PERSON>ytymispelitila lukittuna vaikeustasolle 'vaikea'. Et voi syntyä uudelleen kuoltuasi.", "selectWorld.gameMode.hardcore.line1": "<PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON>, luki<PERSON><PERSON> v<PERSON><PERSON><PERSON>", "selectWorld.gameMode.hardcore.line2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ja sinulla on vain yksi elämä", "selectWorld.gameMode.spectator": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON>, mutta et koskea.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON>, mutta et koskea", "selectWorld.gameMode.survival": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jossa voit rake<PERSON>, ker<PERSON>illä ja työstää sekä taistella hirviöitä vastaan.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON><PERSON> resu<PERSON>, luo <PERSON>, ker<PERSON><PERSON>", "selectWorld.gameMode.survival.line2": "ta<PERSON><PERSON>, terveyttä ja nälkää", "selectWorld.gameRules": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "<PERSON><PERSON>", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON><PERSON> tuodes<PERSON> as<PERSON>", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON><PERSON> (.json)", "selectWorld.incompatible.description": "Tätä maailmaa ei voi avata tässä versiossa.\nSitä pelattiin viimeksi versiossa %s.", "selectWorld.incompatible.info": "Yhteensopimaton versio: %s", "selectWorld.incompatible.title": "Yhteensopimaton versio", "selectWorld.incompatible.tooltip": "<PERSON><PERSON>ä ma<PERSON>a ei voi avata, koska se on luotu yhteensopimattomalla versiolla.", "selectWorld.incompatible_series": "<PERSON><PERSON><PERSON> yht<PERSON>sopimat<PERSON>alla versiolla", "selectWorld.load_folder_access": "<PERSON><PERSON><PERSON><PERSON>, johon peli<PERSON> ovat <PERSON>, ei voida lukea tai avata!", "selectWorld.loading_list": "<PERSON><PERSON><PERSON>", "selectWorld.locked": "Lukittu toisen käynnissä olevan Minecraft-instanssin takia", "selectWorld.mapFeatures": "<PERSON><PERSON> rake<PERSON><PERSON><PERSON>", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, jne.", "selectWorld.mapType": "<PERSON><PERSON><PERSON> tyy<PERSON>i", "selectWorld.mapType.normal": "<PERSON><PERSON>", "selectWorld.moreWorldOptions": "Maailman lisäasetukset...", "selectWorld.newWorld": "<PERSON><PERSON><PERSON>", "selectWorld.recreate": "<PERSON><PERSON>", "selectWorld.recreate.customized.text": "Mukautettuja maailmoja ei enään tueta tässä Minecraft versiossa. Voimme yrittää uudelleenluoda sen samalla lähtöarvolla ja ominaisuuk<PERSON>lla, mutta kaikki ma<PERSON>onmu<PERSON>ukset menetetään. Pahoittelemme hankaluutta!", "selectWorld.recreate.customized.title": "Mukautettuja maailmoja ei enää tueta", "selectWorld.recreate.error.text": "<PERSON><PERSON> meni vikaan, kun y<PERSON><PERSON>in u<PERSON> ma<PERSON>.", "selectWorld.recreate.error.title": "Ta<PERSON><PERSON>ui virhe!", "selectWorld.resource_load": "Valmistellaan resursseja...", "selectWorld.resultFolder": "Tallennuskohde:", "selectWorld.search": "etsi <PERSON>", "selectWorld.seedInfo": "Jätä tyhjäksi käyttääksesi satunnaista lähtöarvoa", "selectWorld.select": "<PERSON><PERSON>a valittua ma<PERSON>a", "selectWorld.targetFolder": "Tallennuskansio: %s", "selectWorld.title": "<PERSON><PERSON><PERSON>", "selectWorld.tooltip.fromNewerVersion1": "<PERSON><PERSON><PERSON> tallennettiin uudemma<PERSON> versios<PERSON>,", "selectWorld.tooltip.fromNewerVersion2": "tä<PERSON><PERSON>n ma<PERSON>man lataaminen saattaa aiheuttaa ongelmia!", "selectWorld.tooltip.snapshot1": "Muista varmuuskopioida tämä maailma", "selectWorld.tooltip.snapshot2": "ennen kuin lataat sen tässä kehittäjäversiossa.", "selectWorld.unable_to_load": "Maailmoja ei voida ladata", "selectWorld.version": "Versio:", "selectWorld.versionJoinButton": "<PERSON><PERSON><PERSON>", "selectWorld.versionQuestion": "<PERSON><PERSON><PERSON><PERSON> varmasti ladata tämän ma<PERSON>man?", "selectWorld.versionUnknown": "tunt<PERSON><PERSON>", "selectWorld.versionWarning": "Tätä maailma pelattiin viimeksi versiossa '%s' ja sen lataaminen tässä versiossa saattaa aiheuttaa sen korruptoitumisen!", "selectWorld.warning.deprecated.question": "Jotkut käytetyt ominaisuudet ovat van<PERSON>eita ja lakkaavat toimimasta tulevaisuudessa. <PERSON><PERSON>t<PERSON> varmasti jatkaa?", "selectWorld.warning.deprecated.title": "Varoitus! Nämä asetukset käyttävät vanhentuneita ominaisuuksia", "selectWorld.warning.experimental.question": "Nämä asetukset ovat kokeellisia ja voivat jonain päivänä lakata toimimasta. <PERSON><PERSON><PERSON><PERSON> varmasti jat<PERSON>a?", "selectWorld.warning.experimental.title": "Varoitus! Nämä asetukset käyttävät kokeellisia ominaisuuksia", "selectWorld.warning.lowDiskSpace.description": "Laitteellasi ei ole enää paljoa tilaa.\n<PERSON><PERSON><PERSON> lo<PERSON> kesken pelin saattaa johtaa maailman vahingoittumiseen.", "selectWorld.warning.lowDiskSpace.title": "Varoitus! <PERSON><PERSON>a v<PERSON>his<PERSON>ä!", "selectWorld.world": "<PERSON><PERSON><PERSON>", "sign.edit": "<PERSON>ok<PERSON><PERSON> kyl<PERSON> te<PERSON>", "sleep.not_possible": "<PERSON><PERSON><PERSON><PERSON><PERSON> määrä lepoa ei voi ohittaa tätä yötä", "sleep.players_sleeping": "%s/%s pelaajista nukkumassa", "sleep.skipping_night": "<PERSON><PERSON><PERSON><PERSON> yön yli", "slot.only_single_allowed": "<PERSON><PERSON> y<PERSON><PERSON><PERSON>t paikat salli<PERSON>, sin<PERSON>a on '%s'", "slot.unknown": "Tuntematon lokero '%s'", "snbt.parser.empty_key": "Avain ei voi olla tyhjä", "snbt.parser.expected_binary_numeral": "<PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_decimal_numeral": "<PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_float_type": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "snbt.parser.expected_hex_escape": "<PERSON><PERSON><PERSON><PERSON>, jonka pituus on %s", "snbt.parser.expected_hex_numeral": "<PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_integer_type": "<PERSON><PERSON><PERSON><PERSON>", "snbt.parser.expected_non_negative_number": "Odotettiin epänegatiivista lukua", "snbt.parser.expected_number_or_boolean": "Odotettiin lukua tai totuusarvoa", "snbt.parser.expected_string_uuid": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON> kelpaa UUID:ksi", "snbt.parser.expected_unquoted_string": "Odotettiin kelvollista merkkijonoa ilman lainausmerkkejä", "snbt.parser.infinity_not_allowed": "Ei-äärellisiä lukuja ei sallita", "snbt.parser.invalid_array_element_type": "<PERSON><PERSON><PERSON><PERSON>en taulukon alkion tyyppi", "snbt.parser.invalid_character_name": "Virheellinen Unicode-merkin nimi", "snbt.parser.invalid_codepoint": "Virheellinen Unicode-merkin arvo: %s", "snbt.parser.invalid_string_contents": "Virheellinen merkkijonon si<PERSON>ältö", "snbt.parser.invalid_unquoted_start": "Merkkijonot ilman lainausmerkkejä eivät voi alkaa numeroilla 0-9 taikka merkeillä + tai -", "snbt.parser.leading_zero_not_allowed": "Desimaaliluvut eivät voi alkaa numerolla 0", "snbt.parser.no_such_operation": "Operaatiota ei ole: %s", "snbt.parser.number_parse_failure": "Luvun jäsentäminen epäonnistui: %s", "snbt.parser.undescore_not_allowed": "Alaviivamerkit eivät ole sallittuja luvun alussa tai lopussa", "soundCategory.ambient": "Tunnelma/Ympäristö", "soundCategory.block": "<PERSON><PERSON><PERSON>", "soundCategory.hostile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "soundCategory.master": "<PERSON><PERSON><PERSON>", "soundCategory.music": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.neutral": "Ystävälliset olennot", "soundCategory.player": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.record": "Levysoitin/Sointukuutiot", "soundCategory.ui": "Käyttöliittymä", "soundCategory.voice": "<PERSON><PERSON><PERSON>/<PERSON>e", "soundCategory.weather": "Sää", "spectatorMenu.close": "<PERSON><PERSON> vali<PERSON>", "spectatorMenu.next_page": "<PERSON><PERSON><PERSON> sivu", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON> sivu", "spectatorMenu.root.prompt": "Paina näppäintä valitaksesi komennon, ja uudestaan käyttääksesi sitä.", "spectatorMenu.team_teleport": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>en luo<PERSON>e", "spectatorMenu.team_teleport.prompt": "Valitse ryhmä jonka luokse teleportata", "spectatorMenu.teleport": "Teleporttaa pelaajan luokse", "spectatorMenu.teleport.prompt": "<PERSON><PERSON><PERSON> p<PERSON>, jonka luokse teleportata", "stat.generalButton": "<PERSON><PERSON><PERSON>", "stat.itemsButton": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Eläimiä ruokittu", "stat.minecraft.aviate_one_cm": "<PERSON><PERSON><PERSON> le<PERSON> matka", "stat.minecraft.bell_ring": "Soittokelloja soitettu", "stat.minecraft.boat_one_cm": "Veneellä kuljettu matka", "stat.minecraft.clean_armor": "Haarniskan o<PERSON>", "stat.minecraft.clean_banner": "Viirejä pu<PERSON>u", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON><PERSON> matka", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON><PERSON> estetty vahinko", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> (kärsitty)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON><PERSON> (kestetty)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON><PERSON> saatu", "stat.minecraft.deaths": "Kuolemien määrä", "stat.minecraft.drop": "<PERSON><PERSON><PERSON> pu<PERSON>", "stat.minecraft.eat_cake_slice": "Kakkupaloja syöty", "stat.minecraft.enchant_item": "Esineitä lumottu", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON><PERSON> matka", "stat.minecraft.fill_cauldron": "<PERSON><PERSON>", "stat.minecraft.fish_caught": "<PERSON><PERSON><PERSON>", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.happy_ghast_one_cm": "<PERSON><PERSON><PERSON> g<PERSON> k<PERSON>ka", "stat.minecraft.horse_one_cm": "<PERSON><PERSON><PERSON>ka", "stat.minecraft.inspect_dispenser": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarka<PERSON>u", "stat.minecraft.inspect_dropper": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "stat.minecraft.inspect_hopper": "Supp<PERSON><PERSON> tarka<PERSON>u", "stat.minecraft.interact_with_anvil": "Vuorovaikutukset alasimen kanssa", "stat.minecraft.interact_with_beacon": "Merkkivaloa k<PERSON>", "stat.minecraft.interact_with_blast_furnace": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON><PERSON>ustelin<PERSON><PERSON> k<PERSON>", "stat.minecraft.interact_with_campfire": "Nuotiota k<PERSON>tty", "stat.minecraft.interact_with_cartography_table": "Karttapöytää k<PERSON>", "stat.minecraft.interact_with_crafting_table": "Työpöytä<PERSON> k<PERSON>", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_grindstone": "Vuorovaikutukset tahkon kanssa", "stat.minecraft.interact_with_lectern": "<PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_loom": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_smithing_table": "Takomispöytiä k<PERSON>", "stat.minecraft.interact_with_smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.jump": "Hyppyjä", "stat.minecraft.leave_game": "Peleist<PERSON> poistuttu", "stat.minecraft.minecart_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON>", "stat.minecraft.open_barrel": "Tynnyreitä avattu", "stat.minecraft.open_chest": "Ark<PERSON><PERSON> a<PERSON>", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.open_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "stat.minecraft.pig_one_cm": "<PERSON><PERSON><PERSON> matka", "stat.minecraft.play_noteblock": "Sointukuutiota soitettu", "stat.minecraft.play_record": "Musiikkilevyjä soitettu", "stat.minecraft.play_time": "P<PERSON>ttu aika", "stat.minecraft.player_kills": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.pot_flower": "Kasveja istutettu ruuk<PERSON>in", "stat.minecraft.raid_trigger": "Hyökkäyksiä aiheutettu", "stat.minecraft.raid_win": "Hyökkäyksiä voitettu", "stat.minecraft.sleep_in_bed": "Sängyssä nukuttu", "stat.minecraft.sneak_time": "<PERSON>iv<PERSON><PERSON> aika", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.strider_one_cm": "Rämpijäll<PERSON> kuljettu matka", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON> mat<PERSON>", "stat.minecraft.talked_to_villager": "Kylä<PERSON>ä<PERSON><PERSON>", "stat.minecraft.target_hit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>u", "stat.minecraft.time_since_death": "Aikaa viime kuolem<PERSON>a", "stat.minecraft.time_since_rest": "Aikaa viime nuk<PERSON>misesta", "stat.minecraft.total_world_time": "<PERSON><PERSON><PERSON>", "stat.minecraft.traded_with_villager": "Vaihdettu kyläläisten kanssa", "stat.minecraft.trigger_trapped_chest": "Ansoitettuja arkkuja aktivoitu", "stat.minecraft.tune_noteblock": "Sointukuutiota viritetty", "stat.minecraft.use_cauldron": "Pa<PERSON>ta otettu vettä", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON><PERSON><PERSON> matka veden päällä", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON><PERSON><PERSON> matka veden alla", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "<PERSON>rt<PERSON> r<PERSON>", "stat_type.minecraft.crafted": "Valmistettu", "stat_type.minecraft.dropped": "Pudotettu", "stat_type.minecraft.killed": "Tapettu %s %s", "stat_type.minecraft.killed.none": "Et ole tappanut olentoa %s kertaakaan", "stat_type.minecraft.killed_by": "%s tappoi sinut %s kertaa", "stat_type.minecraft.killed_by.none": "%s ei ole tappanut sinua kertaakaan", "stat_type.minecraft.mined": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.picked_up": "<PERSON><PERSON><PERSON><PERSON>", "stat_type.minecraft.used": "<PERSON><PERSON><PERSON><PERSON>", "stats.none": "-", "structure_block.button.detect_size": "TUNNISTA", "structure_block.button.load": "LATAA", "structure_block.button.save": "TALLENNA", "structure_block.custom_data": "Mukautettu datatunnisteen nimi", "structure_block.detect_size": "<PERSON><PERSON><PERSON> rakenne<PERSON> koko ja sijainti:", "structure_block.hover.corner": "Kulma: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Lataa: %s", "structure_block.hover.save": "Tallenna: %s", "structure_block.include_entities": "Sisällytä kohteet:", "structure_block.integrity": "Rakennelman eheys ja siemen<PERSON>u", "structure_block.integrity.integrity": "Rakennelman yhtenäisyys", "structure_block.integrity.seed": "Rakennelman lähtöarvo", "structure_block.invalid_structure_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> rakennelman nimi '%s'", "structure_block.load_not_found": "Rakennelma '%s' ei ole saatavilla ", "structure_block.load_prepare": "Rakennelman '%s' sijainti valmisteltu", "structure_block.load_success": "Rakennelma ladattu kohteesta '%s'", "structure_block.mode.corner": "<PERSON><PERSON>", "structure_block.mode.data": "Data", "structure_block.mode.load": "Lataa", "structure_block.mode.save": "<PERSON><PERSON><PERSON>", "structure_block.mode_info.corner": "<PERSON><PERSON><PERSON><PERSON> <PERSON> as<PERSON><PERSON><PERSON> ja k<PERSON><PERSON>", "structure_block.mode_info.data": "Datatila <PERSON> pelin log<PERSON>", "structure_block.mode_info.load": "Lataustila – lataa tiedostosta", "structure_block.mode_info.save": "<PERSON><PERSON><PERSON><PERSON><PERSON> – k<PERSON><PERSON><PERSON>", "structure_block.position": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>", "structure_block.position.x": "<PERSON><PERSON><PERSON><PERSON><PERSON> sijainti x", "structure_block.position.y": "<PERSON><PERSON><PERSON><PERSON><PERSON> sijainti y", "structure_block.position.z": "su<PERSON><PERSON>linen sijainti z", "structure_block.save_failure": "Rakennelman '%s' tallentaminen epäonnistui", "structure_block.save_success": "Rakennelma tallennettu nimellä '%s'", "structure_block.show_air": "Näytä näkymättömät kuutiot:", "structure_block.show_boundingbox": "Näytä alueen kehys:", "structure_block.size": "Rakennelman koko", "structure_block.size.x": "rakenne<PERSON> koko x", "structure_block.size.y": "rakenne<PERSON> koko y", "structure_block.size.z": "rakenne<PERSON> koko z", "structure_block.size_failure": "Rakennelman kokoa ei voida tunnistaa. Lisää kulmat toisiaan vastaavilla nimillä", "structure_block.size_success": "Rakennelman '%s' koko havaittu onnistuneesti", "structure_block.strict": "<PERSON><PERSON><PERSON> sijoitus:", "structure_block.structure_name": "Rakennelman nimi", "subtitles.ambient.cave": "<PERSON>ave<PERSON><PERSON> ääni", "subtitles.ambient.sound": "<PERSON>ave<PERSON><PERSON> ääni", "subtitles.block.amethyst_block.chime": "Ametisti kilisee", "subtitles.block.amethyst_block.resonate": "Ametisti resonoi", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON> t<PERSON>n", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON>", "subtitles.block.barrel.close": "<PERSON><PERSON><PERSON>", "subtitles.block.barrel.open": "<PERSON><PERSON><PERSON> ava<PERSON>uu", "subtitles.block.beacon.activate": "Merkkivalo aktivoituu", "subtitles.block.beacon.ambient": "Merkkivalo hum<PERSON>e", "subtitles.block.beacon.deactivate": "Merkki<PERSON><PERSON> sa<PERSON>", "subtitles.block.beacon.power_select": "Merkkivalon teho valittiin", "subtitles.block.beehive.drip": "<PERSON><PERSON><PERSON><PERSON> valuu", "subtitles.block.beehive.enter": "<PERSON><PERSON><PERSON><PERSON> menee pesään", "subtitles.block.beehive.exit": "<PERSON><PERSON><PERSON><PERSON> poistuu pesästä", "subtitles.block.beehive.shear": "Keritsimet raaputtavat", "subtitles.block.beehive.work": "Mehiläiset työskentelevät", "subtitles.block.bell.resonate": "<PERSON><PERSON><PERSON><PERSON> kajahtaa", "subtitles.block.bell.use": "Soittokello soi", "subtitles.block.big_dripleaf.tilt_down": "T<PERSON><PERSON>leht<PERSON> kallistuu alas", "subtitles.block.big_dripleaf.tilt_up": "Tippuleht<PERSON> kallis<PERSON>u y<PERSON>", "subtitles.block.blastfurnace.fire_crackle": "<PERSON><PERSON><PERSON>", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON><PERSON> virtaa<PERSON>", "subtitles.block.bubble_column.upwards_inside": "Ku<PERSON>lat työntävät", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.whirlpool_inside": "Kuplat vetävät", "subtitles.block.button.click": "<PERSON><PERSON><PERSON>", "subtitles.block.cake.add_candle": "Kakku litistyy", "subtitles.block.campfire.crackle": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.block.candle.crackle": "Kynttilä rätisee", "subtitles.block.candle.extinguish": "Kynttilä sammuu", "subtitles.block.chest.close": "<PERSON><PERSON> su<PERSON>", "subtitles.block.chest.locked": "Arkku lukittu", "subtitles.block.chest.open": "Ark<PERSON> avautuu", "subtitles.block.chorus_flower.death": "Chorus-k<PERSON><PERSON> k<PERSON>u", "subtitles.block.chorus_flower.grow": "Chorus-<PERSON><PERSON><PERSON> ka<PERSON>a", "subtitles.block.comparator.click": "<PERSON>mp<PERSON>att<PERSON>a", "subtitles.block.composter.empty": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.fill": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.composter.ready": "Komposti kompostoi", "subtitles.block.conduit.activate": "Kanavoija aktivoituu", "subtitles.block.conduit.ambient": "Kanavoija sykkii", "subtitles.block.conduit.attack.target": "Kanavoija hyökkää", "subtitles.block.conduit.deactivate": "Kanavoija sammuu", "subtitles.block.copper_bulb.turn_off": "Kupar<PERSON>mp<PERSON> sammuu", "subtitles.block.copper_bulb.turn_on": "Kuparilamppu syttyy", "subtitles.block.copper_trapdoor.close": "<PERSON><PERSON><PERSON> su<PERSON>", "subtitles.block.copper_trapdoor.open": "Lu<PERSON>ku avautuu", "subtitles.block.crafter.craft": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "subtitles.block.crafter.fail": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.creaking_heart.hurt": "<PERSON><PERSON>televa sydän vai<PERSON>oi", "subtitles.block.creaking_heart.idle": "<PERSON>ave<PERSON><PERSON> ääni", "subtitles.block.creaking_heart.spawn": "<PERSON><PERSON><PERSON>eva sydän <PERSON>", "subtitles.block.deadbush.idle": "<PERSON><PERSON>ia <PERSON>", "subtitles.block.decorated_pot.insert": "Koristeltu ruukku tä<PERSON>y", "subtitles.block.decorated_pot.insert_fail": "<PERSON><PERSON><PERSON><PERSON> ruukku heiluu", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>to<PERSON>uu", "subtitles.block.dispenser.dispense": "<PERSON><PERSON><PERSON><PERSON> jakoi", "subtitles.block.dispenser.fail": "<PERSON><PERSON><PERSON><PERSON> antoi tyhj<PERSON>", "subtitles.block.door.toggle": "<PERSON><PERSON>", "subtitles.block.dried_ghast.ambient": "Kuivumisen ääniä", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON><PERSON><PERSON> ghast nest<PERSON>y", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON><PERSON><PERSON> ghast kastuu", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON><PERSON> ghast tuntee o<PERSON>a parem<PERSON>i", "subtitles.block.dry_grass.ambient": "Tu<PERSON>sia <PERSON>", "subtitles.block.enchantment_table.use": "Lumouspöytää k<PERSON>", "subtitles.block.end_portal.spawn": "End-portaali avautuu", "subtitles.block.end_portal_frame.fill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kiinn<PERSON>y", "subtitles.block.eyeblossom.close": "<PERSON>lm<PERSON><PERSON><PERSON> sulk<PERSON>", "subtitles.block.eyeblossom.idle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.eyeblossom.open": "Silmä<PERSON><PERSON> avautuu", "subtitles.block.fence_gate.toggle": "<PERSON><PERSON>", "subtitles.block.fire.ambient": "<PERSON><PERSON>", "subtitles.block.fire.extinguish": "<PERSON><PERSON>", "subtitles.block.firefly_bush.idle": "Tulikärpäset surisevat", "subtitles.block.frogspawn.hatch": "Nuijapää kuoriutuu", "subtitles.block.furnace.fire_crackle": "Uuni rätisee", "subtitles.block.generic.break": "<PERSON><PERSON><PERSON> hajoaa", "subtitles.block.generic.fall": "<PERSON><PERSON> put<PERSON>a ku<PERSON> p<PERSON>lle", "subtitles.block.generic.footsteps": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.hit": "Kuutiota hajo<PERSON>", "subtitles.block.generic.place": "<PERSON><PERSON><PERSON>", "subtitles.block.grindstone.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.growing_plant.crop": "<PERSON><PERSON><PERSON>", "subtitles.block.hanging_sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> he<PERSON>", "subtitles.block.honey_block.slide": "Hunajakuutiota pitkin liu'utaan", "subtitles.block.iron_trapdoor.close": "<PERSON><PERSON><PERSON> su<PERSON>", "subtitles.block.iron_trapdoor.open": "Lu<PERSON>ku avautuu", "subtitles.block.lava.ambient": "<PERSON><PERSON> kuplii", "subtitles.block.lava.extinguish": "<PERSON><PERSON>", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON>a", "subtitles.block.note_block.note": "Sointukuutio soi", "subtitles.block.pale_hanging_moss.idle": "<PERSON>ave<PERSON><PERSON> ääni", "subtitles.block.piston.move": "Mäntä liikkuu", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON><PERSON> valuu", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "<PERSON><PERSON><PERSON> valuu pataan", "subtitles.block.pointed_dripstone.drip_water": "<PERSON>ett<PERSON> valuu", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON>ett<PERSON> valuu pataan", "subtitles.block.pointed_dripstone.land": "Stalaktiitti rysähtää maahan", "subtitles.block.portal.ambient": "Portaali humisee", "subtitles.block.portal.travel": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.block.portal.trigger": "Portaalin ääni voimist<PERSON>u", "subtitles.block.pressure_plate.click": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.pumpkin.carve": "Keritsimet kaivertavat", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> si<PERSON>a", "subtitles.block.respawn_anchor.ambient": "Uudelleensyntymisank<PERSON><PERSON> hum<PERSON>e", "subtitles.block.respawn_anchor.charge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on viritetty", "subtitles.block.respawn_anchor.deplete": "Uudelleensyntymisankkuri kuluu loppuun", "subtitles.block.respawn_anchor.set_spawn": "Uude<PERSON><PERSON>ynty<PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON>tymis<PERSON>", "subtitles.block.sand.idle": "Hiekkaisia ääniä", "subtitles.block.sand.wind": "Tu<PERSON>sia <PERSON>", "subtitles.block.sculk.charge": "Sculk kuplii", "subtitles.block.sculk.spread": "Sculk leviää", "subtitles.block.sculk_catalyst.bloom": "Sculk-<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.block.sculk_sensor.clicking": "Sculk-<PERSON><PERSON> nak<PERSON>a", "subtitles.block.sculk_sensor.clicking_stop": "Sculk-sensori lakkaa naksu<PERSON>ta", "subtitles.block.sculk_shrieker.shriek": "Sculk-kirkuja kirkuu", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> a<PERSON>u", "subtitles.block.sign.waxed_interact_fail": "<PERSON><PERSON><PERSON> he<PERSON>", "subtitles.block.smithing_table.use": "Takomispöytää k<PERSON>", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON><PERSON> savu<PERSON>", "subtitles.block.sniffer_egg.crack": "<PERSON><PERSON><PERSON><PERSON><PERSON> muna halkeilee", "subtitles.block.sniffer_egg.hatch": "<PERSON>u<PERSON>kun muna kuoriutuu", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON><PERSON> munii", "subtitles.block.sponge.absorb": "Pesusieni imee", "subtitles.block.sweet_berry_bush.pick_berries": "<PERSON><PERSON><PERSON>", "subtitles.block.trapdoor.close": "<PERSON><PERSON><PERSON> su<PERSON>", "subtitles.block.trapdoor.open": "Lu<PERSON>ku avautuu", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON><PERSON> na<PERSON>a", "subtitles.block.trial_spawner.about_to_spawn_item": "Pa<PERSON>enteinen esine valmist<PERSON>uu", "subtitles.block.trial_spawner.ambient": "Koetuksen luojakuutio rätisee", "subtitles.block.trial_spawner.ambient_charged": "Pahaenteinen koetuksen luojakuutio rätisee", "subtitles.block.trial_spawner.ambient_ominous": "Pahaenteinen koetuksen luojakuutio rätisee", "subtitles.block.trial_spawner.charge_activate": "<PERSON><PERSON> ni<PERSON>e k<PERSON>tu<PERSON> l<PERSON>", "subtitles.block.trial_spawner.close_shutter": "<PERSON><PERSON><PERSON><PERSON> luo<PERSON>uti<PERSON> su<PERSON>", "subtitles.block.trial_spawner.detect_player": "<PERSON><PERSON><PERSON><PERSON> luojak<PERSON>utio latautuu", "subtitles.block.trial_spawner.eject_item": "Koetuksen luojakuutio sylkee esineitä", "subtitles.block.trial_spawner.ominous_activate": "<PERSON><PERSON> ni<PERSON>e k<PERSON>tu<PERSON> l<PERSON>", "subtitles.block.trial_spawner.open_shutter": "<PERSON><PERSON><PERSON><PERSON> luojakuutio avautuu", "subtitles.block.trial_spawner.spawn_item": "<PERSON><PERSON><PERSON><PERSON> esine putoaa", "subtitles.block.trial_spawner.spawn_item_begin": "<PERSON><PERSON><PERSON><PERSON> esine il<PERSON>y", "subtitles.block.trial_spawner.spawn_mob": "<PERSON><PERSON><PERSON><PERSON> luojakuutio luo olennon", "subtitles.block.tripwire.attach": "Ansalanka kiinnitt<PERSON>y", "subtitles.block.tripwire.click": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON> irt<PERSON>a", "subtitles.block.vault.activate": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.close_shutter": "<PERSON><PERSON><PERSON>", "subtitles.block.vault.deactivate": "<PERSON><PERSON><PERSON> sa<PERSON>u", "subtitles.block.vault.eject_item": "<PERSON><PERSON><PERSON> s<PERSON> es<PERSON>en", "subtitles.block.vault.insert_item": "<PERSON><PERSON> lukitus poistuu", "subtitles.block.vault.insert_item_fail": "<PERSON><PERSON> a<PERSON>aminen epäonnistuu", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON> a<PERSON>uu", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON><PERSON> h<PERSON> pela<PERSON>n", "subtitles.block.water.ambient": "Vesi virtaa", "subtitles.block.wet_sponge.dries": "Pesusieni kuivuu", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.insert_enchanted": "<PERSON><PERSON><PERSON> k<PERSON>ja <PERSON>", "subtitles.chiseled_bookshelf.take": "<PERSON><PERSON><PERSON>", "subtitles.chiseled_bookshelf.take_enchanted": "<PERSON><PERSON><PERSON> k<PERSON>ja <PERSON>", "subtitles.enchant.thorns.hit": "Piikikkyys vahingoittaa", "subtitles.entity.allay.ambient_with_item": "Apuri etsii", "subtitles.entity.allay.ambient_without_item": "Apuri haluaa esineitä", "subtitles.entity.allay.death": "Apuri kuolee", "subtitles.entity.allay.hurt": "A<PERSON><PERSON> vahingo<PERSON>uu", "subtitles.entity.allay.item_given": "Apuri kiittää", "subtitles.entity.allay.item_taken": "Apuri antaa esineen", "subtitles.entity.allay.item_thrown": "Apuri heittää", "subtitles.entity.armadillo.ambient": "Vyötiäinen ähkii", "subtitles.entity.armadillo.brush": "Vyötiä<PERSON><PERSON>r<PERSON>", "subtitles.entity.armadillo.death": "Vyötiäinen kuolee", "subtitles.entity.armadillo.eat": "Vyötiäinen syö", "subtitles.entity.armadillo.hurt": "Vyötiäinen vahingoittuu", "subtitles.entity.armadillo.hurt_reduced": "Vyötiäinen suojautuu", "subtitles.entity.armadillo.land": "Vyötiäinen tömähtää maahan", "subtitles.entity.armadillo.peek": "Vyötiäinen kurkistaa", "subtitles.entity.armadillo.roll": "Vyötiäinen k<PERSON>ytyy kerälle", "subtitles.entity.armadillo.scute_drop": "Vyötiäinen pudottaa suomun", "subtitles.entity.armadillo.unroll_finish": "Vyö<PERSON>äinen suoristautuu", "subtitles.entity.armadillo.unroll_start": "Vyötiäinen kurkistaa", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON> tippui", "subtitles.entity.arrow.hit": "<PERSON><PERSON><PERSON>", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> p<PERSON>an", "subtitles.entity.arrow.shoot": "<PERSON><PERSON><PERSON> am<PERSON>", "subtitles.entity.axolotl.attack": "Aks<PERSON>tli h<PERSON>ökkää", "subtitles.entity.axolotl.death": "<PERSON>ks<PERSON><PERSON><PERSON> kuo<PERSON>", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.axolotl.idle_air": "Aksolotli visertää", "subtitles.entity.axolotl.idle_water": "Aksolotli visertää", "subtitles.entity.axolotl.splash": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>", "subtitles.entity.axolotl.swim": "Aksolotli ui", "subtitles.entity.bat.ambient": "Le<PERSON>k<PERSON> v<PERSON>", "subtitles.entity.bat.death": "Lepakko kuolee", "subtitles.entity.bat.hurt": "Lepak<PERSON> v<PERSON>u", "subtitles.entity.bat.takeoff": "Lepakko l<PERSON>ht<PERSON> lentoon", "subtitles.entity.bee.ambient": "<PERSON><PERSON><PERSON><PERSON> pö<PERSON>", "subtitles.entity.bee.death": "Me<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.bee.hurt": "<PERSON><PERSON><PERSON><PERSON> vahingo<PERSON>u", "subtitles.entity.bee.loop": "<PERSON><PERSON><PERSON><PERSON> pö<PERSON>", "subtitles.entity.bee.loop_aggressive": "Mehiläinen pörrää vihaisesti", "subtitles.entity.bee.pollinate": "Me<PERSON><PERSON>inen pörrää iloisesti", "subtitles.entity.bee.sting": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.blaze.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.blaze.death": "<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.blaze.hurt": "<PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON> ampuu", "subtitles.entity.boat.paddle_land": "Soutua", "subtitles.entity.boat.paddle_water": "Soutua", "subtitles.entity.bogged.ambient": "Rämeinen kalisee", "subtitles.entity.bogged.death": "Rämeinen kuolee", "subtitles.entity.bogged.hurt": "Rämeinen vahingoittuu", "subtitles.entity.breeze.charge": "Viima lataa", "subtitles.entity.breeze.death": "Viima kuolee", "subtitles.entity.breeze.deflect": "<PERSON><PERSON><PERSON> to<PERSON>", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON> vahi<PERSON>u", "subtitles.entity.breeze.idle_air": "Viima ujeltaa", "subtitles.entity.breeze.idle_ground": "<PERSON><PERSON><PERSON> pu<PERSON>", "subtitles.entity.breeze.inhale": "Viima henkäisee", "subtitles.entity.breeze.jump": "<PERSON><PERSON><PERSON> lo<PERSON>a", "subtitles.entity.breeze.land": "<PERSON><PERSON><PERSON>", "subtitles.entity.breeze.shoot": "Viima ampuu", "subtitles.entity.breeze.slide": "Viima liukuu", "subtitles.entity.breeze.whirl": "<PERSON>ii<PERSON> vinkuu", "subtitles.entity.breeze.wind_burst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON> p<PERSON>u", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON> kuo<PERSON>", "subtitles.entity.camel.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.camel.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON> nousee y<PERSON>", "subtitles.entity.camel.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.camel.step_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cat.ambient": "<PERSON><PERSON> ma<PERSON>u", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.cat.death": "<PERSON><PERSON> kuolee", "subtitles.entity.cat.eat": "Kissa <PERSON>", "subtitles.entity.cat.hiss": "<PERSON><PERSON>", "subtitles.entity.cat.hurt": "<PERSON><PERSON> v<PERSON>u", "subtitles.entity.cat.purr": "<PERSON><PERSON> keh<PERSON>", "subtitles.entity.chicken.ambient": "<PERSON>na k<PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON> kuo<PERSON>", "subtitles.entity.chicken.egg": "<PERSON>na munii", "subtitles.entity.chicken.hurt": "<PERSON><PERSON> v<PERSON>u", "subtitles.entity.cod.death": "Turska kuolee", "subtitles.entity.cod.flop": "<PERSON>rska sätkii", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.cow.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.cow.death": "<PERSON><PERSON><PERSON> kuo<PERSON>", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.cow.milk": "Lehmää lypsetään", "subtitles.entity.creaking.activate": "<PERSON><PERSON><PERSON><PERSON> hava<PERSON>u", "subtitles.entity.creaking.ambient": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "subtitles.entity.creaking.attack": "Narahtelija hyökkää", "subtitles.entity.creaking.deactivate": "<PERSON>rahtel<PERSON> r<PERSON>u", "subtitles.entity.creaking.death": "<PERSON><PERSON><PERSON><PERSON> murtuu", "subtitles.entity.creaking.freeze": "Narahtelija p<PERSON>ähtyy", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.creaking.sway": "Narahtelijaa lyödään", "subtitles.entity.creaking.twitch": "Narahtelija nykii", "subtitles.entity.creaking.unfreeze": "Narahtelija liikkuu", "subtitles.entity.creeper.death": "Creeper kuolee", "subtitles.entity.creeper.hurt": "Creeper vahingoittuu", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> si<PERSON>", "subtitles.entity.dolphin.ambient": "Delfiini visertää", "subtitles.entity.dolphin.ambient_water": "Delfiini viheltää", "subtitles.entity.dolphin.attack": "Delfiini hyökkää", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.dolphin.eat": "Delfiini syö", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.dolphin.jump": "Delfiini hyppää", "subtitles.entity.dolphin.play": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.splash": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.swim": "Delfiini ui", "subtitles.entity.donkey.ambient": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.donkey.angry": "<PERSON><PERSON> hi<PERSON>u", "subtitles.entity.donkey.chest": "<PERSON><PERSON><PERSON> la<PERSON> a<PERSON>", "subtitles.entity.donkey.death": "<PERSON><PERSON> kuo<PERSON>", "subtitles.entity.donkey.eat": "Aasi <PERSON>", "subtitles.entity.donkey.hurt": "<PERSON><PERSON> v<PERSON>u", "subtitles.entity.donkey.jump": "<PERSON><PERSON> h<PERSON>", "subtitles.entity.drowned.ambient": "Huk<PERSON><PERSON> k<PERSON>", "subtitles.entity.drowned.ambient_water": "Huk<PERSON><PERSON> k<PERSON>", "subtitles.entity.drowned.death": "Hukkunut kuolee", "subtitles.entity.drowned.hurt": "Huk<PERSON>nut vahingo<PERSON>u", "subtitles.entity.drowned.shoot": "Hukkunut heittää atraimen", "subtitles.entity.drowned.step": "Hukkunut astelee", "subtitles.entity.drowned.swim": "Huk<PERSON>nut ui", "subtitles.entity.egg.throw": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> valittaa", "subtitles.entity.elder_guardian.ambient_land": "V<PERSON><PERSON>van<PERSON> räpyttelee", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.elder_guardian.death": "V<PERSON><PERSON><PERSON><PERSON> kuolee", "subtitles.entity.elder_guardian.flop": "Vartijavanhin läiskii", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> vahi<PERSON>uu", "subtitles.entity.ender_dragon.ambient": "Lohikää<PERSON><PERSON> kar<PERSON>", "subtitles.entity.ender_dragon.death": "Lohikäärme kuolee", "subtitles.entity.ender_dragon.flap": "Lohikäärme läpyttelee", "subtitles.entity.ender_dragon.growl": "Lohikää<PERSON><PERSON> kar<PERSON>", "subtitles.entity.ender_dragon.hurt": "Lohikäärme vahingo<PERSON>u", "subtitles.entity.ender_dragon.shoot": "Lohikäärme syöksee", "subtitles.entity.ender_eye.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sinkoutuu", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON>-<PERSON><PERSON>", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> vwoop<PERSON><PERSON>", "subtitles.entity.enderman.death": "<PERSON><PERSON> kuolee", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> v<PERSON>u", "subtitles.entity.enderman.scream": "<PERSON><PERSON> kirkuu", "subtitles.entity.enderman.stare": "<PERSON><PERSON> itkee", "subtitles.entity.enderman.teleport": "<PERSON>erman teleporttaa", "subtitles.entity.endermite.ambient": "Endermite vilistää", "subtitles.entity.endermite.death": "Endermite kuolee", "subtitles.entity.endermite.hurt": "Endermite vahingoittuu", "subtitles.entity.evoker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.evoker.cast_spell": "<PERSON><PERSON><PERSON><PERSON> lo<PERSON>", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON><PERSON> hur<PERSON>a", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.evoker.prepare_attack": "<PERSON><PERSON><PERSON> valmist<PERSON>uu hyökkäämään", "subtitles.entity.evoker.prepare_summon": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.prepare_wololo": "<PERSON><PERSON><PERSON> valmistaa <PERSON>ostami<PERSON>", "subtitles.entity.evoker_fangs.attack": "Ham<PERSON><PERSON>", "subtitles.entity.experience_orb.pickup": "Kokemuspisteitä saatu", "subtitles.entity.firework_rocket.blast": "Ilotulite räjähtää", "subtitles.entity.firework_rocket.launch": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.firework_rocket.twinkle": "Ilot<PERSON><PERSON> tui<PERSON>i", "subtitles.entity.fish.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fishing_bobber.retrieve": "<PERSON><PERSON> vedet<PERSON>", "subtitles.entity.fishing_bobber.splash": "Ongenkoukku loiskah<PERSON>a", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON> he<PERSON>", "subtitles.entity.fox.aggro": "<PERSON><PERSON>u suuttuu", "subtitles.entity.fox.ambient": "<PERSON><PERSON><PERSON> vinga<PERSON>a", "subtitles.entity.fox.bite": "Kettu puree", "subtitles.entity.fox.death": "Kettu kuolee", "subtitles.entity.fox.eat": "Kettu s<PERSON>ö", "subtitles.entity.fox.hurt": "<PERSON><PERSON>u vahingo<PERSON>uu", "subtitles.entity.fox.screech": "<PERSON><PERSON><PERSON> ulvoo", "subtitles.entity.fox.sleep": "<PERSON><PERSON>u kuo<PERSON>a", "subtitles.entity.fox.sniff": "<PERSON><PERSON>u nuuhkii", "subtitles.entity.fox.spit": "Kettu sylkee", "subtitles.entity.fox.teleport": "Kettu teleporttaa", "subtitles.entity.frog.ambient": "Sammakko k<PERSON>", "subtitles.entity.frog.death": "Sammakko kuolee", "subtitles.entity.frog.eat": "Sammakko s<PERSON>ö", "subtitles.entity.frog.hurt": "Sammakko v<PERSON>u", "subtitles.entity.frog.lay_spawn": "Sammak<PERSON> kutee", "subtitles.entity.frog.long_jump": "Sammakko hyppä<PERSON>", "subtitles.entity.generic.big_fall": "<PERSON><PERSON>", "subtitles.entity.generic.burn": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.drink": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.eat": "Syödään", "subtitles.entity.generic.explode": "Räjähtää", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON> sammuu", "subtitles.entity.generic.hurt": "<PERSON><PERSON> v<PERSON>u", "subtitles.entity.generic.small_fall": "<PERSON><PERSON>", "subtitles.entity.generic.splash": "Polskintaa", "subtitles.entity.generic.swim": "Uimista", "subtitles.entity.generic.wind_burst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghast.ambient": "G<PERSON>t itkee", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> ampuu", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.ghastling.death": "Ghastling kuolee", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> il<PERSON><PERSON>y", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.break": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "subtitles.entity.glow_item_frame.place": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.remove_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.glow_squid.ambient": "Hehkukalmar<PERSON> ui", "subtitles.entity.glow_squid.death": "Heh<PERSON><PERSON><PERSON><PERSON> kuolee", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vahi<PERSON>uu", "subtitles.entity.glow_squid.squirt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ampuu mustetta", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.goat.eat": "Vuohi s<PERSON>ö", "subtitles.entity.goat.horn_break": "<PERSON><PERSON><PERSON> sarvi hajoaa", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> vahi<PERSON>u", "subtitles.entity.goat.long_jump": "<PERSON><PERSON><PERSON> lo<PERSON>a", "subtitles.entity.goat.milk": "<PERSON><PERSON><PERSON> lypsetään", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON> puskee", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON>i", "subtitles.entity.goat.step": "<PERSON><PERSON><PERSON> astelee", "subtitles.entity.guardian.ambient": "<PERSON>artija vali<PERSON>a", "subtitles.entity.guardian.ambient_land": "Vartija <PERSON>", "subtitles.entity.guardian.attack": "Vartija h<PERSON>", "subtitles.entity.guardian.death": "Vartija kuolee", "subtitles.entity.guardian.flop": "Vartija läiskii", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.happy_ghast.ambient": "<PERSON><PERSON><PERSON> ghast hyr<PERSON>e", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON> ghast kuolee", "subtitles.entity.happy_ghast.equip": "<PERSON><PERSON><PERSON> g<PERSON> v<PERSON>", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON><PERSON> ghast on valmis", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON> ghast p<PERSON>y", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON> g<PERSON>t vahi<PERSON>u", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> g<PERSON> v<PERSON> r<PERSON>", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> murisee", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON> murisee vihaisesti", "subtitles.entity.hoglin.attack": "<PERSON><PERSON>n hyökkä<PERSON>", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> muuntuu zogliniksi", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.hoglin.retreat": "Hoglin vetäytyy", "subtitles.entity.hoglin.step": "<PERSON><PERSON><PERSON> astelee", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.armor": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.breathe": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> kuo<PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.gallop": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.jump": "<PERSON><PERSON><PERSON> h<PERSON>", "subtitles.entity.horse.saddle": "<PERSON><PERSON><PERSON>", "subtitles.entity.husk.ambient": "Holk<PERSON> vaikeroi", "subtitles.entity.husk.converted_to_zombie": "<PERSON><PERSON><PERSON> muuttuu z<PERSON>", "subtitles.entity.husk.death": "Holkki kuolee", "subtitles.entity.husk.hurt": "<PERSON><PERSON><PERSON> vahi<PERSON>u", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON>e", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON> j<PERSON>", "subtitles.entity.illusioner.prepare_blindness": "Taikuri valmistelee näkymättömyyttä", "subtitles.entity.illusioner.prepare_mirror": "<PERSON><PERSON><PERSON> val<PERSON><PERSON><PERSON> p<PERSON>", "subtitles.entity.iron_golem.attack": "Rautagolem hyökkää", "subtitles.entity.iron_golem.damage": "Rautagolem hajoaa", "subtitles.entity.iron_golem.death": "Rautagolem kuolee", "subtitles.entity.iron_golem.hurt": "Rautagolem vahingoittuu", "subtitles.entity.iron_golem.repair": "Rautagol<PERSON> k<PERSON>u", "subtitles.entity.item.break": "<PERSON><PERSON><PERSON> hajoaa", "subtitles.entity.item.pickup": "<PERSON>sine poimittiin", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.break": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON>", "subtitles.entity.leash_knot.break": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "subtitles.entity.leash_knot.place": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.lightning_bolt.impact": "Salama iskee", "subtitles.entity.lightning_bolt.thunder": "Ukkonen jyrisee", "subtitles.entity.llama.ambient": "Laama määkii", "subtitles.entity.llama.angry": "Laama määkii vihaisesti", "subtitles.entity.llama.chest": "<PERSON><PERSON><PERSON> la<PERSON> a<PERSON>", "subtitles.entity.llama.death": "Laama kuolee", "subtitles.entity.llama.eat": "Laama syö", "subtitles.entity.llama.hurt": "<PERSON><PERSON> vahi<PERSON>uu", "subtitles.entity.llama.spit": "Laama sylkee", "subtitles.entity.llama.step": "Laama astelee", "subtitles.entity.llama.swag": "Laama on koristeltu", "subtitles.entity.magma_cube.death": "Magmakuutio kuolee", "subtitles.entity.magma_cube.hurt": "Magmakuutio vahingoittuu", "subtitles.entity.magma_cube.squish": "Magmakuutio <PERSON>htaa", "subtitles.entity.minecart.inside": "Kaivosvaunu rämisee", "subtitles.entity.minecart.inside_underwater": "Kaivosvaunu rämisee veden alla", "subtitles.entity.minecart.riding": "Kaivosvaunu liikkuu", "subtitles.entity.mooshroom.convert": "Mooshroom muuntautuu", "subtitles.entity.mooshroom.eat": "Mooshroom syö", "subtitles.entity.mooshroom.milk": "Mooshroomia lypsetään", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroomia lypsetään epäilyttävästi", "subtitles.entity.mule.ambient": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.mule.angry": "<PERSON><PERSON> hi<PERSON>", "subtitles.entity.mule.chest": "<PERSON><PERSON><PERSON> la<PERSON> a<PERSON>", "subtitles.entity.mule.death": "<PERSON><PERSON> kuo<PERSON>", "subtitles.entity.mule.eat": "<PERSON><PERSON>", "subtitles.entity.mule.hurt": "<PERSON><PERSON> v<PERSON>u", "subtitles.entity.mule.jump": "<PERSON><PERSON> h<PERSON>", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON> hajoaa", "subtitles.entity.painting.place": "<PERSON><PERSON><PERSON>", "subtitles.entity.panda.aggressive_ambient": "Panda pu<PERSON>a", "subtitles.entity.panda.ambient": "Panda läähättää", "subtitles.entity.panda.bite": "Panda puree", "subtitles.entity.panda.cant_breed": "Panda määkii", "subtitles.entity.panda.death": "Panda kuolee", "subtitles.entity.panda.eat": "Panda syö", "subtitles.entity.panda.hurt": "Panda vahingo<PERSON>uu", "subtitles.entity.panda.pre_sneeze": "Pandan nen<PERSON> kutittaa", "subtitles.entity.panda.sneeze": "Panda aivastaa", "subtitles.entity.panda.step": "Panda tallustaa", "subtitles.entity.panda.worried_ambient": "<PERSON>da vinkuu", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON><PERSON> pu<PERSON>u", "subtitles.entity.parrot.death": "Papukaija kuolee", "subtitles.entity.parrot.eats": "Papukaija syö", "subtitles.entity.parrot.fly": "Papukaija räpyttelee", "subtitles.entity.parrot.hurts": "Pa<PERSON><PERSON><PERSON> vahi<PERSON>u", "subtitles.entity.parrot.imitate.blaze": "Papukaija hengittää", "subtitles.entity.parrot.imitate.bogged": "Pa<PERSON><PERSON><PERSON> kalisee", "subtitles.entity.parrot.imitate.breeze": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.creaking": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>e", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.drowned": "Papukaija k<PERSON>", "subtitles.entity.parrot.imitate.elder_guardian": "Papukaija voihkii", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON><PERSON> kar<PERSON>", "subtitles.entity.parrot.imitate.endermite": "Papukaija kipittää", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON><PERSON> mum<PERSON>e", "subtitles.entity.parrot.imitate.ghast": "Papukaija it<PERSON>e", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON><PERSON> valittaa", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON><PERSON> murisee", "subtitles.entity.parrot.imitate.husk": "Papukaija vaikeroi", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON><PERSON> mum<PERSON>e", "subtitles.entity.parrot.imitate.magma_cube": "<PERSON><PERSON><PERSON><PERSON>a", "subtitles.entity.parrot.imitate.phantom": "Papukaija ujeltaa", "subtitles.entity.parrot.imitate.piglin": "Papukaija röhkii", "subtitles.entity.parrot.imitate.piglin_brute": "Papukaija röhkii", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON><PERSON> supisee", "subtitles.entity.parrot.imitate.ravager": "Papukaija ä<PERSON>i", "subtitles.entity.parrot.imitate.shulker": "Papukaija väijyy", "subtitles.entity.parrot.imitate.silverfish": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.skeleton": "Pa<PERSON><PERSON><PERSON> kalisee", "subtitles.entity.parrot.imitate.slime": "<PERSON><PERSON><PERSON><PERSON>a", "subtitles.entity.parrot.imitate.spider": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.stray": "Pa<PERSON><PERSON><PERSON> kalisee", "subtitles.entity.parrot.imitate.vex": "Papukaija härnää", "subtitles.entity.parrot.imitate.vindicator": "<PERSON><PERSON><PERSON><PERSON> mum<PERSON>e", "subtitles.entity.parrot.imitate.warden": "Papu<PERSON><PERSON> vinkuu", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON><PERSON> kika<PERSON>a", "subtitles.entity.parrot.imitate.wither": "<PERSON><PERSON><PERSON><PERSON>u", "subtitles.entity.parrot.imitate.wither_skeleton": "Pa<PERSON><PERSON><PERSON> kalisee", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON><PERSON> murisee", "subtitles.entity.parrot.imitate.zombie": "Papukaija vaikeroi", "subtitles.entity.parrot.imitate.zombie_villager": "Papukaija vaikeroi", "subtitles.entity.phantom.ambient": "<PERSON><PERSON>", "subtitles.entity.phantom.bite": "Aave puree", "subtitles.entity.phantom.death": "<PERSON><PERSON> kuolee", "subtitles.entity.phantom.flap": "<PERSON><PERSON> iskee si<PERSON>", "subtitles.entity.phantom.hurt": "<PERSON><PERSON> v<PERSON>u", "subtitles.entity.phantom.swoop": "<PERSON><PERSON>", "subtitles.entity.pig.ambient": "Sika röhkii", "subtitles.entity.pig.death": "Sika kuolee", "subtitles.entity.pig.hurt": "<PERSON>ka vahi<PERSON>uu", "subtitles.entity.pig.saddle": "<PERSON><PERSON>", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON>", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> r<PERSON>", "subtitles.entity.piglin.angry": "<PERSON>lin röhkii vihaisesti", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> juhlii", "subtitles.entity.piglin.converted_to_zombified": "<PERSON><PERSON> muuntuu z<PERSON>i", "subtitles.entity.piglin.death": "<PERSON><PERSON> kuolee", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> vahi<PERSON>u", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> röhkii ka<PERSON>i", "subtitles.entity.piglin.retreat": "Piglin vetäytyy", "subtitles.entity.piglin.step": "<PERSON><PERSON> astelee", "subtitles.entity.piglin_brute.ambient": "<PERSON>lin-<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>i", "subtitles.entity.piglin_brute.angry": "Piglin-r<PERSON><PERSON><PERSON><PERSON> röhkii vih<PERSON>esti", "subtitles.entity.piglin_brute.converted_to_zombified": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> muuntuu zombipigliniksi", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> kuolee", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vahi<PERSON>uu", "subtitles.entity.piglin_brute.step": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> astelee", "subtitles.entity.pillager.ambient": "Ryöstäj<PERSON> mum<PERSON>e", "subtitles.entity.pillager.celebrate": "Ryöstäj<PERSON> hurraa", "subtitles.entity.pillager.death": "Ryöstäjä kuolee", "subtitles.entity.pillager.hurt": "Ryöstäjä vahingo<PERSON>u", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON><PERSON> hyökkäys", "subtitles.entity.player.attack.knockback": "Tönäisyhyökkäys", "subtitles.entity.player.attack.strong": "Vahva hyökkäys", "subtitles.entity.player.attack.sweep": "Kiitäv<PERSON> isku", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.burp": "Röyh", "subtitles.entity.player.death": "Pelaaja kuolee", "subtitles.entity.player.freeze_hurt": "Pelaaja j<PERSON>ät<PERSON>y", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON> vahi<PERSON>u", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON> huk<PERSON>u", "subtitles.entity.player.hurt_on_fire": "<PERSON><PERSON><PERSON> palaa", "subtitles.entity.player.levelup": "Pelaaja he<PERSON>ähtää", "subtitles.entity.player.teleport": "Pelaaja teleporttaa", "subtitles.entity.polar_bear.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>a", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON> pentu hyrisee", "subtitles.entity.polar_bear.death": "Jääkar<PERSON> kuolee", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> vahi<PERSON>u", "subtitles.entity.polar_bear.warning": "<PERSON><PERSON><PERSON><PERSON><PERSON> kar<PERSON>", "subtitles.entity.potion.splash": "<PERSON><PERSON><PERSON>", "subtitles.entity.potion.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.blow_out": "Pallokala t<PERSON>", "subtitles.entity.puffer_fish.blow_up": "Pallokala paisuu", "subtitles.entity.puffer_fish.death": "Pallokala kuolee", "subtitles.entity.puffer_fish.flop": "Pallokala sätkii", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON>kala vahingo<PERSON>uu", "subtitles.entity.puffer_fish.sting": "Pallokala pistää", "subtitles.entity.rabbit.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> kuo<PERSON>", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.rabbit.jump": "<PERSON><PERSON><PERSON>", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "subtitles.entity.ravager.attack": "Raunioittaja puree", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON> hur<PERSON>a", "subtitles.entity.ravager.death": "Raunioit<PERSON><PERSON> kuolee", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.step": "Raunioittaja löntystää", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.salmon.death": "<PERSON><PERSON> kuolee", "subtitles.entity.salmon.flop": "<PERSON><PERSON>", "subtitles.entity.salmon.hurt": "<PERSON><PERSON> vahi<PERSON>uu", "subtitles.entity.sheep.ambient": "<PERSON><PERSON>", "subtitles.entity.sheep.death": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON> v<PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> vaanii", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> a<PERSON>u", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> am<PERSON>", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON> teleport<PERSON>a", "subtitles.entity.shulker_bullet.hit": "Shulkerin ammus räjähtää", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON>erin ammus hajoaa", "subtitles.entity.silverfish.ambient": "Soker<PERSON>uk<PERSON>", "subtitles.entity.silverfish.death": "Sokeritoukka kuolee", "subtitles.entity.silverfish.hurt": "Sokeritoukka vahingo<PERSON>uu", "subtitles.entity.skeleton.ambient": "<PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.entity.skeleton.converted_to_stray": "<PERSON><PERSON><PERSON> muuntuu v<PERSON>", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON> kuolee", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> v<PERSON><PERSON>", "subtitles.entity.skeleton.shoot": "<PERSON><PERSON><PERSON> am<PERSON>", "subtitles.entity.skeleton_horse.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton_horse.death": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kuo<PERSON>", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "subtitles.entity.skeleton_horse.jump_water": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hy<PERSON>", "subtitles.entity.skeleton_horse.swim": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ui", "subtitles.entity.slime.attack": "Lima hyökkää", "subtitles.entity.slime.death": "Lima kuolee", "subtitles.entity.slime.hurt": "Lima vahingoittuu", "subtitles.entity.slime.squish": "Lima litsahtaa", "subtitles.entity.sniffer.death": "Nuuhku kuolee", "subtitles.entity.sniffer.digging": "<PERSON><PERSON><PERSON><PERSON> kaivaa", "subtitles.entity.sniffer.digging_stop": "<PERSON>uuh<PERSON> nousee ylös", "subtitles.entity.sniffer.drop_seed": "<PERSON>u<PERSON>ku pudottaa siemenen", "subtitles.entity.sniffer.eat": "Nuuhku s<PERSON>ö", "subtitles.entity.sniffer.egg_crack": "<PERSON><PERSON><PERSON><PERSON><PERSON> muna halkeilee", "subtitles.entity.sniffer.egg_hatch": "<PERSON>u<PERSON>kun muna kuoriutuu", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>", "subtitles.entity.sniffer.hurt": "<PERSON>u<PERSON><PERSON> vahingoittuu", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON><PERSON> narisee", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>e", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON><PERSON> etsii", "subtitles.entity.sniffer.sniffing": "Nuuhku nuuhkii", "subtitles.entity.sniffer.step": "Nuuhku löntystää", "subtitles.entity.snow_golem.death": "Lumigolem kuolee", "subtitles.entity.snow_golem.hurt": "Lumigolem vahingoittuu", "subtitles.entity.snowball.throw": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.spider.ambient": "Hämähä<PERSON><PERSON>", "subtitles.entity.spider.death": "Hämähäkki kuolee", "subtitles.entity.spider.hurt": "Hämähäkki vahingo<PERSON>u", "subtitles.entity.squid.ambient": "Kalmari ui", "subtitles.entity.squid.death": "Kalmari kuolee", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.squid.squirt": "<PERSON><PERSON><PERSON> ampuu mustetta", "subtitles.entity.stray.ambient": "<PERSON>ael<PERSON><PERSON> kalisee", "subtitles.entity.stray.death": "Vaeltaja kuolee", "subtitles.entity.stray.hurt": "<PERSON>ael<PERSON><PERSON> v<PERSON>u", "subtitles.entity.strider.death": "Rämpijä kuolee", "subtitles.entity.strider.eat": "Rämpijä s<PERSON>ö", "subtitles.entity.strider.happy": "Rämpijä <PERSON>", "subtitles.entity.strider.hurt": "Rämpijä vahingo<PERSON>u", "subtitles.entity.strider.idle": "Rämpijä visertää", "subtitles.entity.strider.retreat": "Rämpijä <PERSON>", "subtitles.entity.tadpole.death": "Nuijapää kuolee", "subtitles.entity.tadpole.flop": "Nuijapää sätkii", "subtitles.entity.tadpole.grow_up": "Nuijapää kasvaa", "subtitles.entity.tadpole.hurt": "Nuijapää vahingoittuu", "subtitles.entity.tnt.primed": "TNT sihisee", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON>n kala kuolee", "subtitles.entity.tropical_fish.flop": "Trooppinen kala m<PERSON>", "subtitles.entity.tropical_fish.hurt": "<PERSON><PERSON><PERSON> kala vahi<PERSON>uu", "subtitles.entity.turtle.ambient_land": "Kilpikonna visertää", "subtitles.entity.turtle.death": "<PERSON>lpikonna kuolee", "subtitles.entity.turtle.death_baby": "Kilpikonnan poikanen kuolee", "subtitles.entity.turtle.egg_break": "Kilpikonnan muna särkyy", "subtitles.entity.turtle.egg_crack": "<PERSON><PERSON><PERSON>konnan muna halkeilee", "subtitles.entity.turtle.egg_hatch": "Kilpikonnan muna kuoriutuu", "subtitles.entity.turtle.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.turtle.hurt_baby": "Kilpikonnan poikanen vahi<PERSON>uu", "subtitles.entity.turtle.lay_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON> munii munan", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.turtle.shamble_baby": "<PERSON><PERSON><PERSON><PERSON><PERSON> poikanen <PERSON>", "subtitles.entity.turtle.swim": "Kilpikonna ui", "subtitles.entity.vex.ambient": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.vex.charge": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.vex.death": "<PERSON><PERSON> k<PERSON>", "subtitles.entity.vex.hurt": "<PERSON><PERSON>", "subtitles.entity.villager.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mumisee", "subtitles.entity.villager.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hurraa", "subtitles.entity.villager.death": "Kyläläinen kuolee", "subtitles.entity.villager.hurt": "K<PERSON><PERSON><PERSON><PERSON><PERSON> vahingoittuu", "subtitles.entity.villager.no": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on eri mieltä", "subtitles.entity.villager.trade": "Kyläläinen tekee vaihtokaupan", "subtitles.entity.villager.work_armorer": "Haarniskaseppä työskentelee", "subtitles.entity.villager.work_butcher": "Teurastaja työskentelee", "subtitles.entity.villager.work_cartographer": "Kartoittaja työskentelee", "subtitles.entity.villager.work_cleric": "Pappi työskentelee", "subtitles.entity.villager.work_farmer": "Maanviljelijä työskentelee", "subtitles.entity.villager.work_fisherman": "Kalastaja työskentelee", "subtitles.entity.villager.work_fletcher": "Jousiseppä työskentelee", "subtitles.entity.villager.work_leatherworker": "Suutari työskentelee", "subtitles.entity.villager.work_librarian": "Kirjastonhoitaja työskentelee", "subtitles.entity.villager.work_mason": "Mu<PERSON>ri työskente<PERSON>", "subtitles.entity.villager.work_shepherd": "Lammaspaimen työskentelee", "subtitles.entity.villager.work_toolsmith": "Työkaluseppä työskentelee", "subtitles.entity.villager.work_weaponsmith": "Aseseppä työskentelee", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on samaa mieltä", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON> hur<PERSON>a", "subtitles.entity.vindicator.death": "Puhdistaja kuolee", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mumisee", "subtitles.entity.wandering_trader.death": "Kulkukauppias kuolee", "subtitles.entity.wandering_trader.disappeared": "<PERSON><PERSON><PERSON>up<PERSON><PERSON> katoaa", "subtitles.entity.wandering_trader.drink_milk": "Ku<PERSON>ukauppias juo maitoa", "subtitles.entity.wandering_trader.drink_potion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> juo ta<PERSON>", "subtitles.entity.wandering_trader.hurt": "Ku<PERSON>ukauppias vahingo<PERSON>u", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on eri mieltä", "subtitles.entity.wandering_trader.reappeared": "Ku<PERSON>ukaup<PERSON>s ilmestyy", "subtitles.entity.wandering_trader.trade": "Kulkukauppias tekee vai<PERSON>an", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on samaa mieltä", "subtitles.entity.warden.agitated": "Warden m<PERSON><PERSON> vih<PERSON>i", "subtitles.entity.warden.ambient": "Warden vinkuu", "subtitles.entity.warden.angry": "Warden raivoaa", "subtitles.entity.warden.attack_impact": "Warden iskee", "subtitles.entity.warden.death": "Warden kuolee", "subtitles.entity.warden.dig": "<PERSON> ka<PERSON>", "subtitles.entity.warden.emerge": "Warden nousee", "subtitles.entity.warden.heartbeat": "<PERSON><PERSON> s<PERSON> l<PERSON>", "subtitles.entity.warden.hurt": "Warden v<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.listening": "<PERSON> k<PERSON><PERSON><PERSON>", "subtitles.entity.warden.listening_angry": "<PERSON> ku<PERSON><PERSON><PERSON> v<PERSON>i", "subtitles.entity.warden.nearby_close": "<PERSON> l<PERSON>", "subtitles.entity.warden.nearby_closer": "<PERSON> et<PERSON><PERSON>", "subtitles.entity.warden.nearby_closest": "Warden on lähellä", "subtitles.entity.warden.roar": "<PERSON>", "subtitles.entity.warden.sniff": "<PERSON> nu<PERSON><PERSON>i", "subtitles.entity.warden.sonic_boom": "Warden jymisee", "subtitles.entity.warden.sonic_charge": "<PERSON> lataa", "subtitles.entity.warden.step": "Warden astelee", "subtitles.entity.warden.tendril_clicks": "<PERSON>", "subtitles.entity.wind_charge.throw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wind_charge.wind_burst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.witch.ambient": "<PERSON><PERSON> k<PERSON>a", "subtitles.entity.witch.celebrate": "<PERSON><PERSON> hur<PERSON>a", "subtitles.entity.witch.death": "<PERSON><PERSON> kuo<PERSON>", "subtitles.entity.witch.drink": "<PERSON><PERSON> juo", "subtitles.entity.witch.hurt": "<PERSON><PERSON> v<PERSON>u", "subtitles.entity.witch.throw": "<PERSON><PERSON>", "subtitles.entity.wither.ambient": "<PERSON><PERSON>", "subtitles.entity.wither.death": "<PERSON><PERSON> kuolee", "subtitles.entity.wither.hurt": "<PERSON>er v<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON> h<PERSON>kk<PERSON>", "subtitles.entity.wither.spawn": "Wither on vapautettu", "subtitles.entity.wither_skeleton.ambient": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> kuolee", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> v<PERSON>u", "subtitles.entity.wolf.ambient": "Susi läähättää", "subtitles.entity.wolf.bark": "<PERSON><PERSON> ha<PERSON>u", "subtitles.entity.wolf.death": "<PERSON><PERSON> kuolee", "subtitles.entity.wolf.growl": "<PERSON><PERSON> muri<PERSON>", "subtitles.entity.wolf.hurt": "<PERSON><PERSON> vahi<PERSON>u", "subtitles.entity.wolf.pant": "Susi läähättää", "subtitles.entity.wolf.shake": "<PERSON><PERSON> ravi<PERSON>", "subtitles.entity.wolf.whine": "<PERSON><PERSON> vinkuu", "subtitles.entity.zoglin.ambient": "<PERSON><PERSON><PERSON> murisee", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> murisee vihaisesti", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON> hyökkää", "subtitles.entity.zoglin.death": "Zoglin kuolee", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> vahingoitt<PERSON>u", "subtitles.entity.zoglin.step": "<PERSON><PERSON><PERSON> astelee", "subtitles.entity.zombie.ambient": "<PERSON><PERSON><PERSON> vai<PERSON>oi", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON>", "subtitles.entity.zombie.break_wooden_door": "<PERSON><PERSON> hajoaa", "subtitles.entity.zombie.converted_to_drowned": "<PERSON><PERSON>i muuntuu huk<PERSON>i", "subtitles.entity.zombie.death": "Zombi kuolee", "subtitles.entity.zombie.destroy_egg": "Kilpikonnan muna tallottiin", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> vahi<PERSON>u", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON> tartuttaa", "subtitles.entity.zombie_horse.ambient": "Zombihevonen hirnuu", "subtitles.entity.zombie_horse.death": "Zombihevonen kuolee", "subtitles.entity.zombie_horse.hurt": "Zombihevonen vahingoittuu", "subtitles.entity.zombie_villager.ambient": "Zombikyläläinen vaikeroi", "subtitles.entity.zombie_villager.converted": "Zombik<PERSON><PERSON><PERSON><PERSON><PERSON> huutaa", "subtitles.entity.zombie_villager.cure": "Zombikyläläinen niiskuttelee", "subtitles.entity.zombie_villager.death": "Zombikyläläinen kuolee", "subtitles.entity.zombie_villager.hurt": "Zombikylä<PERSON>äinen vahingoittuu", "subtitles.entity.zombified_piglin.ambient": "Zombipiglin röhkii", "subtitles.entity.zombified_piglin.angry": "Zombipiglin röhkii vih<PERSON>esti", "subtitles.entity.zombified_piglin.death": "Zombipiglin kuolee", "subtitles.entity.zombified_piglin.hurt": "Zombipiglin vahingoittuu", "subtitles.event.mob_effect.bad_omen": "Enne saa otteen", "subtitles.event.mob_effect.raid_omen": "Hyökkäys uhkaa lähistöllä", "subtitles.event.mob_effect.trial_omen": "Pa<PERSON><PERSON>inen koetus uhkaa lähistöllä", "subtitles.event.raid.horn": "Paha<PERSON>ist<PERSON> torven soittoa", "subtitles.item.armor.equip": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_chain": "Rengashaarniska helisee pu<PERSON>a", "subtitles.item.armor.equip_diamond": "Timanttihaarniska kilahtelee pu<PERSON>a", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON> rasahtaa", "subtitles.item.armor.equip_gold": "Kultahaarniska helisee pu<PERSON>a", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kalisee pu<PERSON>a", "subtitles.item.armor.equip_leather": "Nahkahaar<PERSON><PERSON> rapisee pu<PERSON><PERSON>a", "subtitles.item.armor.equip_netherite": "<PERSON>heriittihaarniska kalisee pu<PERSON>a", "subtitles.item.armor.equip_turtle": "Kilpikonnan kuori tö<PERSON>ähtää", "subtitles.item.armor.equip_wolf": "<PERSON>den haarniska puetaan", "subtitles.item.armor.unequip_wolf": "Suden haarniska riisutaan", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON>", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON>", "subtitles.item.axe.wax_off": "<PERSON><PERSON><PERSON> poistettu", "subtitles.item.bone_meal.use": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.book.page_turn": "<PERSON>vu rapisee", "subtitles.item.book.put": "<PERSON><PERSON><PERSON>", "subtitles.item.bottle.empty": "<PERSON><PERSON><PERSON> t<PERSON>y", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>a", "subtitles.item.brush.brushing.gravel.complete": "<PERSON><PERSON> har<PERSON> valmis", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.sand.complete": "<PERSON><PERSON><PERSON> har<PERSON> valmis", "subtitles.item.bucket.empty": "Ämpäri tyhjentyy", "subtitles.item.bucket.fill": "Ämpäri <PERSON>", "subtitles.item.bucket.fill_axolotl": "<PERSON><PERSON><PERSON><PERSON><PERSON>n", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON>", "subtitles.item.bucket.fill_tadpole": "Nuijapää napattiin", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON><PERSON> t<PERSON>y", "subtitles.item.bundle.insert": "<PERSON>sine laitettiin pussiin", "subtitles.item.bundle.insert_fail": "<PERSON><PERSON><PERSON> t<PERSON>", "subtitles.item.bundle.remove_one": "<PERSON><PERSON><PERSON> otettiin pussista", "subtitles.item.chorus_fruit.teleport": "Pelaaja teleporttaa", "subtitles.item.crop.plant": "<PERSON><PERSON><PERSON>", "subtitles.item.crossbow.charge": "<PERSON><PERSON><PERSON><PERSON><PERSON> lata<PERSON>uu", "subtitles.item.crossbow.hit": "<PERSON><PERSON><PERSON>", "subtitles.item.crossbow.load": "<PERSON><PERSON><PERSON><PERSON><PERSON> lata<PERSON>uu", "subtitles.item.crossbow.shoot": "Varsijousi ampuu", "subtitles.item.dye.use": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "subtitles.item.elytra.flying": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "<PERSON><PERSON><PERSON>", "subtitles.item.flintandsteel.use": "<PERSON><PERSON><PERSON>", "subtitles.item.glow_ink_sac.use": "Hehkumustepussi läiskähtää", "subtitles.item.goat_horn.play": "<PERSON><PERSON><PERSON> sarvi soi", "subtitles.item.hoe.till": "Kuokka kyntää", "subtitles.item.honey_bottle.drink": "Nieleskelyä", "subtitles.item.honeycomb.wax_on": "<PERSON><PERSON><PERSON>u", "subtitles.item.horse_armor.unequip": "<PERSON><PERSON><PERSON> haarniska riisu<PERSON>", "subtitles.item.ink_sac.use": "Mustepussi läiskähtää", "subtitles.item.lead.break": "<PERSON><PERSON><PERSON><PERSON><PERSON> kat<PERSON>", "subtitles.item.lead.tied": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>u", "subtitles.item.lead.untied": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "subtitles.item.llama_carpet.unequip": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.item.lodestone_compass.lock": "Magneettikompassi lukittuu magneettikiveen", "subtitles.item.mace.smash_air": "<PERSON><PERSON><PERSON>", "subtitles.item.mace.smash_ground": "<PERSON><PERSON><PERSON>", "subtitles.item.nether_wart.plant": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> istu<PERSON><PERSON>", "subtitles.item.ominous_bottle.dispose": "<PERSON><PERSON><PERSON> hajoaa", "subtitles.item.saddle.unequip": "<PERSON><PERSON><PERSON> r<PERSON>", "subtitles.item.shears.shear": "Kerits<PERSON>t napsahtavat", "subtitles.item.shears.snip": "Keritsimet leik<PERSON>avat", "subtitles.item.shield.block": "<PERSON><PERSON><PERSON>", "subtitles.item.shovel.flatten": "<PERSON><PERSON>", "subtitles.item.spyglass.stop_using": "Kaukoputki vetäytyy", "subtitles.item.spyglass.use": "Kaukoputki tarkentaa", "subtitles.item.totem.use": "Toteemi aktivoituu", "subtitles.item.trident.hit": "Atrain iskee", "subtitles.item.trident.hit_ground": "Atrain värähtelee", "subtitles.item.trident.return": "Atrain palaa", "subtitles.item.trident.riptide": "Atrain syöksähtää", "subtitles.item.trident.throw": "<PERSON><PERSON> ka<PERSON>", "subtitles.item.trident.thunder": "Atrain iskee salamalla", "subtitles.item.wolf_armor.break": "<PERSON>den haarniska hajoaa", "subtitles.item.wolf_armor.crack": "Suden haarniska halkeilee", "subtitles.item.wolf_armor.damage": "Suden haarniska vahingo<PERSON>uu", "subtitles.item.wolf_armor.repair": "Suden haarniska korja<PERSON>", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON> pakenee", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ui.hud.bubble_pop": "Hengitysmittari las<PERSON>e", "subtitles.ui.loom.take_result": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON>eik<PERSON><PERSON>", "subtitles.weather.rain": "Sataa", "symlink_warning.message": "Maailmojen lataaminen symbolisia linkkejä sisältävistä kansioista voi olla vaarallista, jos ei tied<PERSON> ta<PERSON>, mitä on tekemässä. Lisätietoja osoitteesta %s.", "symlink_warning.message.pack": "Pakettien lataaminen symbolisia linkkejä sisältävistä kansioista voi olla vaarallista, jos ei tiedä ta<PERSON>, mitä on tekemässä. Lisätietoja osoitteesta %s.", "symlink_warning.message.world": "Maailmojen lataaminen symbolisia linkkejä sisältävistä kansioista voi olla vaarallista, jos ei tied<PERSON> ta<PERSON>, mitä on tekemässä. Lisätietoja osoitteesta %s.", "symlink_warning.more_info": "Lisätiet<PERSON>", "symlink_warning.title": "Maailman kansio si<PERSON>ältää symbolisia linkkejä", "symlink_warning.title.pack": "Lisätyt paketit sisältävät symbolisia linkkejä", "symlink_warning.title.world": "Maailman kansio si<PERSON>ältää symbolisia linkkejä", "team.collision.always": "<PERSON><PERSON>", "team.collision.never": "<PERSON><PERSON> k<PERSON>an", "team.collision.pushOtherTeams": "Työnnä muita joukku<PERSON>a", "team.collision.pushOwnTeam": "Työnnä omaa jouk<PERSON>", "team.notFound": "Tuntematon joukkue '%s'", "team.visibility.always": "<PERSON><PERSON>", "team.visibility.hideForOtherTeams": "Piilota toisilta joukkueilta", "team.visibility.hideForOwnTeam": "<PERSON><PERSON><PERSON> o<PERSON>ta j<PERSON>", "team.visibility.never": "<PERSON><PERSON> k<PERSON>an", "telemetry.event.advancement_made.description": "Edystysaskeleen ansaitsemisen taustalla olevan asiayhteyden hahmottaminen voi auttaa meitä ymmärtämään ja kehittämään pelin etenemistä paremmin.", "telemetry.event.advancement_made.title": "Ansaitut edistysaskeleet", "telemetry.event.game_load_times.description": "<PERSON>ämä tapahtuma voi auttaa meitä selvitt<PERSON>mä<PERSON>n, miss<PERSON> k<PERSON><PERSON>ist<PERSON>sen suorituskykyä on parannettava mittaamalla käynnistysvaiheiden suoritusaikoja.", "telemetry.event.game_load_times.title": "<PERSON><PERSON>", "telemetry.event.optional": "%s (Valinnainen)", "telemetry.event.optional.disabled": "%s (Vapaaehtoinen) – Poissa k<PERSON>östä", "telemetry.event.performance_metrics.description": "Minecraftin yleisen suorituskykyprofiilin tunteminen auttaa meitä virittämään ja optimoimaan peliä monille erilaisille konetyypeille ja käyttöjärjestelmille. \nP<PERSON> versio on mukana, jotta voimme vertailla Minecraftin uusien versioiden suorituskykyprofiilia.", "telemetry.event.performance_metrics.title": "Suorituskykymittarit", "telemetry.event.required": "%s (<PERSON><PERSON><PERSON>)", "telemetry.event.world_load_times.description": "On tärkeää ymmärtää, kuinka kauan kestää liitty<PERSON>, ja kuinka paljon tuo kesto muuttuu ajan my<PERSON>. Kun esimerkiksi lisäämme uusia ominaisuuksia tai teemme suuria teknisiä muutoksia, haluamme tietää miten se vaikuttaa latausaiko<PERSON>in.", "telemetry.event.world_load_times.title": "<PERSON><PERSON><PERSON>", "telemetry.event.world_loaded.description": "<PERSON><PERSON> tied<PERSON>, miten pelaajat pelaavat Minecraftia (kuten pelitilat, modatut asiakasohjelmat ja palvelimet sekä peliversiot), voimme kohdentaa pelipäivitykset pelaajien eniten tarvitsemiin ominaisuuksiin.\nMaailma ladattu -tapahtuma yhdistetään Maailma purettu -tapahtumaan peli-istunnon keston laskemiseksi.", "telemetry.event.world_loaded.title": "<PERSON><PERSON><PERSON> lad<PERSON>u", "telemetry.event.world_unloaded.description": "<PERSON><PERSON><PERSON><PERSON> tapahtuma on yhdistetty Ma<PERSON>ma ladattu -tapahtumaan peli-istunnon keston laskemiseksi.\n<PERSON><PERSON> (sekunteina ja pykälinä) lasketaan maailman istunnon päättymisestä (päävalikkoon poistum<PERSON>, palvelimelta poistuminen).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON><PERSON>u", "telemetry.property.advancement_game_time.title": "Peliaika (pykälinä)", "telemetry.property.advancement_id.title": "Edistysaskeleen ID", "telemetry.property.client_id.title": "Asiakasohjelman ID", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.dedicated_memory_kb.title": "Vara<PERSON><PERSON> muisti (kB)", "telemetry.property.event_timestamp_utc.title": "Tapaht<PERSON> aikaleima (UTC)", "telemetry.property.frame_rate_samples.title": "Kuvataajuusnäytteet (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.game_version.title": "<PERSON><PERSON> versio", "telemetry.property.launcher_name.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mi", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap-aika (millisekunteina)", "telemetry.property.load_time_loading_overlay_ms.title": "<PERSON><PERSON> (millisekunteina)", "telemetry.property.load_time_pre_window_ms.title": "Aika ennen ikkunan avautumista (millisekunteina)", "telemetry.property.load_time_total_time_ms.title": "Ko<PERSON><PERSON>latausaika (millisekunteina)", "telemetry.property.minecraft_session_id.title": "Minecraft-istunnon ID", "telemetry.property.new_world.title": "<PERSON><PERSON><PERSON>", "telemetry.property.number_of_samples.title": "Näytteiden määrä", "telemetry.property.operating_system.title": "Käyttöjärjestelmä", "telemetry.property.opt_in.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "<PERSON><PERSON><PERSON>", "telemetry.property.realms_map_content.title": "Realms-kartan si<PERSON> (minipelin nimi)", "telemetry.property.render_distance.title": "Näköetäisyys", "telemetry.property.render_time_samples.title": "Piirtoaikanäytteet", "telemetry.property.seconds_since_load.title": "Lataamisesta kulu<PERSON> aika (sekunteina)", "telemetry.property.server_modded.title": "<PERSON><PERSON><PERSON><PERSON> palvelin", "telemetry.property.server_type.title": "Palvelimen tyyppi", "telemetry.property.ticks_since_load.title": "Lataamisesta kulu<PERSON> a<PERSON> (pykälinä)", "telemetry.property.used_memory_samples.title": "<PERSON><PERSON><PERSON><PERSON>", "telemetry.property.user_id.title": "Käyttäjän ID", "telemetry.property.world_load_time_ms.title": "<PERSON><PERSON><PERSON> (millisekunteina)", "telemetry.property.world_session_id.title": "<PERSON><PERSON><PERSON> is<PERSON>non <PERSON>", "telemetry_info.button.give_feedback": "<PERSON>", "telemetry_info.button.privacy_statement": "Tietosuojaseloste", "telemetry_info.button.show_data": "Näytä <PERSON>", "telemetry_info.opt_in.description": "Hyväksyn vapaaehtoisten telemetriatietojen lähettämisen", "telemetry_info.property_title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tiedot", "telemetry_info.screen.description": "Näiden tietojen kerääminen auttaa meitä parantamaan Minecraftia ohjaamalla meitä pelaajillemme merkityksellisiin suuntiin.\nVoit myös lähettää lisäpalautetta, joka auttaa meitä Minecraftin kehittämisessä.", "telemetry_info.screen.title": "Telemetriatietojen kerääminen", "test.error.block_property_mismatch": "Odotettiin ominaisuuden %s olevan %s, mutta sinulla on %s", "test.error.block_property_missing": "Kuution ominaisuudet puuttuvat, ominaisuuden %s odote<PERSON><PERSON> olevan %s", "test.error.entity_property": "Kohde %s epäonnistui testissä: %s", "test.error.entity_property_details": "Kohde %s epäonnistui testissä: %s, odotettiin: %s, saatiin: %s", "test.error.expected_block": "Odotettiin ku<PERSON>ota %s, mutta sinulla on %s", "test.error.expected_block_tag": "Odotettiin ku<PERSON>ota tunnisteella #%s, mutta sinulla on %s", "test.error.expected_container_contents": "Säiliössä tulisi olla: %s", "test.error.expected_container_contents_single": "Säiliössä tulisi olla yksi yksittäinen: %s", "test.error.expected_empty_container": "<PERSON><PERSON><PERSON><PERSON><PERSON> tulisi olla tyhjä", "test.error.expected_entity": "Odotettiin %s", "test.error.expected_entity_around": "Odotettiin %s olemassa olevaksi sijainnissa %s, %s, %s", "test.error.expected_entity_count": "Odotettiin %s tyypin %s kohdetta, löydettiin %s", "test.error.expected_entity_data": "<PERSON><PERSON><PERSON><PERSON> koh<PERSON> tie<PERSON>n o<PERSON>: %s, mutta sinulla on %s", "test.error.expected_entity_data_predicate": "Kohteen %s tietojen yhteensopimattomuus", "test.error.expected_entity_effect": "Odotettiin koh<PERSON>lle %s vaikutusta %s %s", "test.error.expected_entity_having": "<PERSON><PERSON><PERSON>ttelossa tulisi olla %s", "test.error.expected_entity_holding": "Kohteella tulisi olla kädessään %s", "test.error.expected_entity_in_test": "Odotettiin koh<PERSON> %s olemassaoloa testissä", "test.error.expected_entity_not_touching": "Ei odotettu kohteen %s koskettavan sijaintia %s, %s, %s (suhteellinen: %s, %s, %s)", "test.error.expected_entity_touching": "Odotettiin kohteen %s koskettavan sijaintia%s, %s, %s (suhteellinen: %s, %s, %s)", "test.error.expected_item": "Odotettiin esineen tyyppiä %s", "test.error.expected_items_count": "Odotettiin %s tyypin %s esinettä, löydettiin %s", "test.error.fail": "Epäonnistumisen ehdot t<PERSON>yttyivät", "test.error.invalid_block_type": "Odottamaton kuutiotyyppi löydetty: %s", "test.error.missing_block_entity": "<PERSON><PERSON><PERSON><PERSON><PERSON> ku<PERSON>", "test.error.position": "%s sijainnissa %s, %s, %s (suhteellinen: %s, %s, %s) pykälällä %s", "test.error.sequence.condition_already_triggered": "Ehto on jo käynnistetty %s", "test.error.sequence.condition_not_triggered": "Ehto ei käynn<PERSON>", "test.error.sequence.invalid_tick": "Onnistui virheellisellä pykälällä: odotettiin %s", "test.error.sequence.not_completed": "Testi aikakatkaistiin ennen sek<PERSON> suorittamista", "test.error.set_biome": "Testibiomin asettaminen ep<PERSON>ui", "test.error.spawn_failure": "Kohteen %s luominen epäonnistui", "test.error.state_not_equal": "Virheellinen tila. Odotettiin %s, mutta sinulla on %s", "test.error.structure.failure": "Testirakennelman sijoittaminen kohteelle %s epäonnistui", "test.error.tick": "%s pykälällä %s", "test.error.ticking_without_structure": "Pykälätesti ennen rakennelman asettamista", "test.error.timeout.no_result": "Ei onnistunut tai epäonnistunut %s pykälän aikana", "test.error.timeout.no_sequences_finished": "Ei suoritettuja sekvenssejä %s pykälän aikana", "test.error.too_many_entities": "Odotettiin vain yksi %s olemassa olevaksi sijainnissa %s, %s, %s mutta löydettiin %s", "test.error.unexpected_block": "Ei odotettu kuution olevan %s", "test.error.unexpected_entity": "Ei odotettu kohteen %s olemassaoloa", "test.error.unexpected_item": "Ei odotettu esineen tyyppiä %s", "test.error.unknown": "Tuntematon sisäinen virhe: %s", "test.error.value_not_equal": "Odotettiin kohteen %s olevan %s, mutta sinulla on %s", "test.error.wrong_block_entity": "Väärä kuutiokohteen tyyppi: %s", "test_block.error.missing": "Testirakennelmasta puuttuu %s kuutio", "test_block.error.too_many": "Liian monta %s ku<PERSON>ota", "test_block.invalid_timeout": "Virheellinen <PERSON> (%s) – positiivinen määrä pykäliä vaaditaan", "test_block.message": "Viesti:", "test_block.mode.accept": "Hyväksy", "test_block.mode.fail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test_block.mode.log": "<PERSON>", "test_block.mode.start": "Aloita", "test_block.mode_info.accept": "Hyväksymistila ​​– Hyväks<PERSON> testin (osan) onnistuminen", "test_block.mode_info.fail": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Epäonnist<PERSON> testi", "test_block.mode_info.log": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> v<PERSON>i", "test_block.mode_info.start": "Aloitustila – <PERSON><PERSON> al<PERSON>", "test_instance.action.reset": "Nollaa ja lataa", "test_instance.action.run": "Lataa ja suorita", "test_instance.action.save": "<PERSON><PERSON><PERSON> r<PERSON>", "test_instance.description.batch": "Erä: %s", "test_instance.description.failed": "Epäonnistui: %s", "test_instance.description.function": "Funktio: %s", "test_instance.description.invalid_id": "<PERSON><PERSON><PERSON><PERSON><PERSON> testitunnus", "test_instance.description.no_test": "Testiä ei ole", "test_instance.description.structure": "Rakennelma: %s", "test_instance.description.type": "Tyyppi: %s", "test_instance.type.block_based": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> testi", "test_instance.type.function": "Sisäänrakennettu funktiotesti", "test_instance_block.entities": "Ko<PERSON><PERSON>t:", "test_instance_block.error.no_test": "Ei voida suorittaa testitapausta sijainnissa %s, %s, %s, koska sillä on määrittelemätön testi", "test_instance_block.error.no_test_structure": "Ei voida suorittaa testitapausta sijainnissa %s, %s, %s, koska sillä ei ole testirakennelmaa", "test_instance_block.error.unable_to_save": "Testirakennelmamallia ei voida tallentaa testitapaukselle sijainnissa %s, %s, %s", "test_instance_block.invalid": "[v<PERSON><PERSON><PERSON><PERSON>]", "test_instance_block.reset_success": "Nollaus on<PERSON>ui testille: %s", "test_instance_block.rotation": "Kierto:", "test_instance_block.size": "Testirakennelman koko", "test_instance_block.starting": "Aloitetaan testiä %s", "test_instance_block.test_id": "Testitapauksen ID", "title.32bit.deprecation": "32-bittinen järjestelmä havaittu: tämä voi estää sinua pelaamasta tulevaisuudessa, kun 64-bittist<PERSON> järjestelmää vaaditaan!", "title.32bit.deprecation.realms": "Minecraft vaatii pian 64-bit<PERSON><PERSON> jä<PERSON>lmän, mikä estää sinua pelaamasta tai käyttämästä Realmsia tällä laitteella. Sinun on peruutettava kaikki Realms-tilaukset manuaalisesti.", "title.32bit.deprecation.realms.check": "Älä näytä tätä uudelleen", "title.32bit.deprecation.realms.header": "32-bit<PERSON>n järjestelmä havaittu", "title.credits": "Tekijänoikeudet omistaa Mojang AB. Älä levitä!", "title.multiplayer.disabled": "<PERSON><PERSON><PERSON><PERSON> on poissa k<PERSON>ytöstä. Ole hyvä ja tarkista Microsoft-tilisi asetukset.", "title.multiplayer.disabled.banned.name": "Sinun on vaihdettava nimesi ennen kuin voit pelata verkossa", "title.multiplayer.disabled.banned.permanent": "Tilisi on pysyvästi suljettu verkkopelistä", "title.multiplayer.disabled.banned.temporary": "Tilisi on väliaikaisesti suljettu verkkopelistä", "title.multiplayer.lan": "Moninpeli (Lähiverkko)", "title.multiplayer.other": "Moninpeli (kolmannen osapuolen palvelin)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Yks<PERSON><PERSON><PERSON>", "translation.test.args": "%s %s", "translation.test.complex": "Etuliite, %s%2$s taas %s ja %1$s viimeiseksi %s ja myös %1$s uudelleen!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "hei %", "translation.test.invalid2": "hei %s", "translation.test.none": "Hei, ma<PERSON><PERSON>!", "translation.test.world": "<PERSON><PERSON><PERSON>", "trim_material.minecraft.amethyst": "Ametistimateriaali", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.gold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.iron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.lapis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.netherite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.quartz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.redstone": "Redstone-materiaali", "trim_material.minecraft.resin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.bolt": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.coast": "Rann<PERSON>n haarniskakoristeet", "trim_pattern.minecraft.dune": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.eye": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>kor<PERSON>", "trim_pattern.minecraft.flow": "Virtauksen haar<PERSON>kakoristeet", "trim_pattern.minecraft.host": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.raiser": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.rib": "Kylkiluiden haarniskakoristeet", "trim_pattern.minecraft.sentry": "Vartion haarniskakoristeet", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.silence": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> haar<PERSON>", "trim_pattern.minecraft.snout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.spire": "<PERSON><PERSON>", "trim_pattern.minecraft.tide": "Vuorovetten haarniskakoristeet", "trim_pattern.minecraft.vex": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.ward": "<PERSON><PERSON>", "trim_pattern.minecraft.wayfinder": "Tiennäyttäjän haarniskakoristeet", "trim_pattern.minecraft.wild": "Viidakon haarniskakoristeet", "tutorial.bundleInsert.description": "<PERSON><PERSON><PERSON><PERSON> hiiren oikeaa painiketta lisätäksesi esineitä", "tutorial.bundleInsert.title": "<PERSON><PERSON><PERSON><PERSON> pussia", "tutorial.craft_planks.description": "Reseptikirja voi auttaa", "tutorial.craft_planks.title": "Valmista puisia lankkuja", "tutorial.find_tree.description": "Lyö sitä kerätäksesi puuta", "tutorial.find_tree.title": "Etsi puu", "tutorial.look.description": "Käytä hiirtä kääntymiseen", "tutorial.look.title": "<PERSON><PERSON>", "tutorial.move.description": "Hyppää painamalla %s", "tutorial.move.title": "Liiku näppäimillä %s, %s,%s ja %s", "tutorial.open_inventory.description": "Paina %s", "tutorial.open_inventory.title": "<PERSON><PERSON>", "tutorial.punch_tree.description": "Pidä %s painettuna", "tutorial.punch_tree.title": "<PERSON><PERSON>a puu", "tutorial.socialInteractions.description": "Paina %s avat<PERSON>esi", "tutorial.socialInteractions.title": "Sosiaaliset kanssakäymiset", "upgrade.minecraft.netherite_upgrade": "Netheriittipäivitys"}