{"accessibility.onboarding.accessibility.button": "naw'la<PERSON><PERSON><PERSON>...", "accessibility.onboarding.screen.narrator": "DelwI' Dachu'meH ‹enter› leQ yI'uy", "accessibility.onboarding.screen.title": "minecraft DaQuj. nuqneH?\n\nDelwI' Dachu' DaneH'a'? naw'laHghach DuHmey DaSuch DaneH'a'?", "addServer.add": "pItlh", "addServer.enterIp": "turwI' ngu'meH mI'", "addServer.enterName": "turwI' pong", "addServer.resourcePack": "turwI' jo vey", "addServer.resourcePack.disabled": "Qotlh", "addServer.resourcePack.enabled": "QotlhHa'", "addServer.resourcePack.prompt": "ghel", "addServer.title": "turwI' De' choH", "advMode.command": "SeHlaw ra'meH ghItlh", "advMode.mode": "lo'", "advMode.mode.auto": "vang'qa'", "advMode.mode.autoexec.bat": "reH chu'lu'", "advMode.mode.conditional": "vay' wuv", "advMode.mode.redstone": "Hong", "advMode.mode.redstoneTriggered": "redstone poQ", "advMode.mode.sequence": "mIr", "advMode.mode.unconditional": "tlhab", "advMode.notAllowed": "turwI' vu'wI' SoHnIS 'ej chenmoHmeH lo' Dalo'nIS", "advMode.notEnabled": "ra'wI' buq'I<PERSON>ey chaw'be' turwI'vam", "advMode.previousOutput": "g<PERSON>'<PERSON> vorgh", "advMode.setCommand": "buq'<PERSON>rmeH SeHlaw ra'meH ghItlh cher", "advMode.setCommand.success": "ra'meH ghItlh cherta': %s", "advMode.trackOutput": "g<PERSON>'<PERSON> juv", "advMode.triggering": "chu'ta<PERSON>", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "chav So'lu'be'bogh: %s", "advancements.adventure.adventuring_time.description": "Hoch 'umber tItu' ", "advancements.adventure.adventuring_time.title": "bItlharghchu'", "advancements.adventure.arbalistic.description": "vagh yagh tlhIn DaHoHmeH wa' jo' HurwI' bach yIlo'", "advancements.adventure.arbalistic.title": "vI' vIvI'", "advancements.adventure.avoid_vibration.description": "sculk noch HubwI' joq bISumDI', bISo'eghmeH yIjoD", "advancements.adventure.avoid_vibration.title": "ghoSmeH pegh wa'vatlh", "advancements.adventure.blowback.description": "SuS tlhapragh DaHoHmeH SuS bach'e' bachpu'bogh SuS tlhapragh 'ej way'lu'pu'bogh yIlo'", "advancements.adventure.blowback.title": "SuSwI' SuSlu'", "advancements.adventure.brush_armadillo.description": "woSwa' ghISDen DaSuqmeH yachwI' yIlo'", "advancements.adventure.brush_armadillo.title": "'IH DIr! nIH! yIr!", "advancements.adventure.bullseye.description": "wejmaH buq'Ir 'oHtaHvIS chuqlIj'e', DoS botlh yIqIp", "advancements.adventure.bullseye.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>'", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "mIllogh bal DachenmoHmeH loS mIllogh bal re'nop tIlo'", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "chenqa'moHtaHvIS yep", "advancements.adventure.crafters_crafting_crafters.description": "chenmoHwI' chenmoHtaHvIS chenmoHwI' 'oHDaq yISum", "advancements.adventure.crafters_crafting_crafters.title": "chenmoHwI' chenmoHlI' chenmoHwI'", "advancements.adventure.fall_from_world_height.description": "qo' 'aqroSvo' qo' ravDaq yIpum 'ej yItaH.", "advancements.adventure.fall_from_world_height.title": "DIS qoj je", "advancements.adventure.heart_transplanter.description": "cha' 'oq wov <PERSON><PERSON>'a' buq'<PERSON><PERSON><PERSON> joj<PERSON>aq HaSwI' tIq yIlanchu'", "advancements.adventure.heart_transplanter.title": "tIq QaywI'", "advancements.adventure.hero_of_the_village.description": "vengHomvaD yot yIjeychu'", "advancements.adventure.hero_of_the_village.title": "vengHom Sub", "advancements.adventure.honey_block_slide.description": "pe'vIlHa' pummeH DayqIr buq'<PERSON>r<PERSON><PERSON> yISup", "advancements.adventure.honey_block_slide.title": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "advancements.adventure.kill_a_mob.description": "tlhapragh naS yIHoH", "advancements.adventure.kill_a_mob.title": "tlhapragh wamwI'", "advancements.adventure.kill_all_mobs.description": "Hoch tlhapragh naH Segh tIHoH", "advancements.adventure.kill_all_mobs.title": "tlha<PERSON><PERSON> wamlu'pu'", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "yagh yIHoH SumtaHvIS sculk lIngwI'", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "<PERSON><PERSON>", "advancements.adventure.lighten_up.description": "Sorpuq wovmoHwI' DawovmoHmeH yItey 'obmaQ Dalo'taHvIS", "advancements.adventure.lighten_up.title": "yIwovchoH", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "vengHom ngan mup pe'bIl 'e' y<PERSON><PERSON>, 'ej qul yIchenmoHQo'", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "botjan yIchu'!", "advancements.adventure.minecraft_trials_edition.description": "qaD pa' yI'el", "advancements.adventure.minecraft_trials_edition.title": "minecraft: qaD(mey) Segh", "advancements.adventure.ol_betsy.description": "bIbachmeH jo' HurwI' yIchu'", "advancements.adventure.ol_betsy.title": "cha yIghuS!", "advancements.adventure.overoverkill.description": "ngIq ghanjaq tonSaw' yIlo' 'ej vaghmaH tIq yIQIH", "advancements.adventure.overoverkill.title": "Ho<PERSON>chu'qu'", "advancements.adventure.play_jukebox_in_meadows.description": "HuD'a' puH nIl pIlmoH bom jengva' chu'wI' 'e' yIqaSmoH", "advancements.adventure.play_jukebox_in_meadows.title": "QoQ wab", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "paq yorgh nanlu'bogh HoS patlh DalaDmeH juvwI' yIlo'", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "paq HoS", "advancements.adventure.revaulting.description": "maQmIgh qengHoD buq'<PERSON>r <PERSON>'moHmeH maQmIgh qaD ngaQHa'moHwI' yIlo'", "advancements.adventure.revaulting.title": "qengHoD qeng HoD", "advancements.adventure.root.description": "Hoq, leng, may' je", "advancements.adventure.root.title": "Hoq", "advancements.adventure.salvage_sherd.description": "mIllogh bal re'nop DaSuqmeH buq'Ir nub y<PERSON>yach", "advancements.adventure.salvage_sherd.title": "pIgh vuv", "advancements.adventure.shoot_arrow.description": "v<PERSON><PERSON><PERSON><PERSON>Hom yIbaH", "advancements.adventure.shoot_arrow.title": "yIQeq", "advancements.adventure.sleep_in_bed.description": "bogh<PERSON>'<PERSON><PERSON>, QongDaq yIlo'", "advancements.adventure.sleep_in_bed.title": "SuchtuvlIj DatIvjaj", "advancements.adventure.sniper_duel.description": "vaghmaH rav ngoghvo' nobmeD yIHoH", "advancements.adventure.sniper_duel.title": "Hay' baHwI'", "advancements.adventure.spyglass_at_dragon.description": "ender lung'a' DaleghmeH Hov tut yIlo'", "advancements.adventure.spyglass_at_dragon.title": "muD Duj 'oH'a'?", "advancements.adventure.spyglass_at_ghast.description": "ghast DaleghmeH Hov tut yIlo'", "advancements.adventure.spyglass_at_ghast.title": "qoch'uq ba'Suq 'oH'a'?", "advancements.adventure.spyglass_at_parrot.description": "vIlInHoD DaleghmeH Hov tut yIlo'", "advancements.adventure.spyglass_at_parrot.title": "bo'Degh 'oH'a'?", "advancements.adventure.summon_iron_golem.description": "veng HubmeH 'uSqan velqa' nuv yIrIt", "advancements.adventure.summon_iron_golem.title": "QaH DI<PERSON>b<PERSON>h", "advancements.adventure.throw_trident.description": "vay' chonnaQ yIchuH.\nyIqIm: wa' nuH <PERSON><PERSON><PERSON>, b<PERSON>oghqu'.", "advancements.adventure.throw_trident.title": "chuHchugh, chuHchuq", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON>, jubmoHmeH HewHom yIlo'", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON> toj", "advancements.adventure.trade.description": "vengHom ngan Doch yIje'", "advancements.adventure.trade.title": "tlhongqu'!", "advancements.adventure.trade_at_world_height.description": "qo' 'a<PERSON><PERSON><PERSON><PERSON> vengHom ngan Doch yIje'", "advancements.adventure.trade_at_world_height.title": "<PERSON><PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "mItlhmeH ghantoHmeyvam yIlo': chalqach, ghIch, joQ, 'av, tam, nuQ, vI'Ir, chIjwI'", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "HoytaHvIS mItlh", "advancements.adventure.trim_with_any_armor_pattern.description": "mItlhmeH raSDaq HaSreH may' Sut yIchenmoH", "advancements.adventure.trim_with_any_armor_pattern.title": "tu'qom chu' chen<PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.description": "cha' ram toQmey tIHoHmeH ghIDbogh naQjejHom yIlo'", "advancements.adventure.two_birds_one_arrow.title": "cha' b<PERSON><PERSON><PERSON><PERSON>, wa<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.under_lock_and_key.description": "qengHoD buq'Ir <PERSON>'moHmeH qaD ngaQHa'moHwI' yIlo'", "advancements.adventure.under_lock_and_key.title": "ngaQmo<PERSON><PERSON>'chu'", "advancements.adventure.use_lodestone.description": "peQnaghvaD SInan yIlo'", "advancements.adventure.use_lodestone.title": "hats tawmey, ju<PERSON><PERSON> h<PERSON>ev", "advancements.adventure.very_very_frightening.description": "vengHom ghotDaq pe'bIl yIbach", "advancements.adventure.very_very_frightening.title": "raw, 'aw, paw', 'aw', Qaw', choHlaw'", "advancements.adventure.voluntary_exile.description": "yot HoD yIHoH.\nDaH vengHomvo' DaghoSnISlaw'...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '<PERSON>' DawIv", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "chal chuch Say'qISDaq yIyIt... 'ach yI'oqQo'", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "tIS; cheS rur", "advancements.adventure.who_needs_rockets.description": "bISalmeH 'ej <PERSON><PERSON> bu<PERSON><PERSON><PERSON>r DalIDmeH SuS bach yIlo'", "advancements.adventure.who_needs_rockets.title": "lop jorwI' poQ 'Iv?", "advancements.adventure.whos_the_pillager_now.description": "weHwI' DaHoHmeH, jo' HurwI' yIlo'", "advancements.adventure.whos_the_pillager_now.title": "DaH weHwI' 'Iv?", "advancements.empty": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.dragon_breath.description": "lung'a' tlhuH DaboSmeH 'al'on bal yIlo'", "advancements.end.dragon_breath.title": "chertIS DaSopnIS", "advancements.end.dragon_egg.description": "lung'a' QIm yI'uch", "advancements.end.dragon_egg.title": "puq poH veb", "advancements.end.elytra.description": "telDu'qoq yISam", "advancements.end.elytra.title": "veH 'oH chal'e' neH", "advancements.end.enter_end_gateway.description": "'ambayvo' y<PERSON><PERSON>gh", "advancements.end.enter_end_gateway.title": "ghoch <PERSON>'", "advancements.end.find_end_city.description": "yI'el, nuq DaSIQvIp?", "advancements.end.find_end_city.title": "<PERSON>u<PERSON> be<PERSON><PERSON><PERSON> veng", "advancements.end.kill_dragon.description": "Qapla'", "advancements.end.kill_dragon.title": "end yItlhabmoH", "advancements.end.levitate.description": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> bu<PERSON>'<PERSON>r y<PERSON>'al", "advancements.end.levitate.title": "na<PERSON><PERSON> <PERSON><PERSON><PERSON>", "advancements.end.respawn_dragon.description": "ender lung'a' y<PERSON><PERSON><PERSON><PERSON>'moH", "advancements.end.respawn_dragon.title": "the end... qaSqa'...", "advancements.end.root.description": "joq bI'reS?", "advancements.end.root.title": "end", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "'awtIr buq'Ir<PERSON><PERSON> chab'a' HIj yIrwI'", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "qoS bom", "advancements.husbandry.allay_deliver_item_to_player.description": "SoHDaq Doch HIj yIrwI'", "advancements.husbandry.allay_deliver_item_to_player.title": "layDaq jaHDI' yIrwI' jup<PERSON>'<PERSON>j lon<PERSON>'", "advancements.husbandry.axolotl_in_a_bucket.description": "HaySInDaq 'evta' yIjon", "advancements.husbandry.axolotl_in_a_bucket.title": "Ha'DIbaH wamwI' 'IHqu'", "advancements.husbandry.balanced_diet.description": "Hoch Doch SoplaHbogh vay' tISop; vabDot pIvHa'moH<PERSON><PERSON>hm<PERSON>", "advancements.husbandry.balanced_diet.title": "Soj nab ngaD", "advancements.husbandry.breed_all_animals.description": "Hoch Ha'DIbaH Segh tISep", "advancements.husbandry.breed_all_animals.title": "chang'eng law'", "advancements.husbandry.breed_an_animal.description": "cha' Ha'DIbaH tISep", "advancements.husbandry.breed_an_animal.title": "vIlInHoDmey valqe'mey je", "advancements.husbandry.complete_catalogue.description": "Hoch vIghro' Seghmey tItlhay'moH!", "advancements.husbandry.complete_catalogue.title": "mem naQ", "advancements.husbandry.feed_snifflet.description": "larghwI'Hom yIje'", "advancements.husbandry.feed_snifflet.title": "larghchoH", "advancements.husbandry.fishy_business.description": "ghotI' yIjon", "advancements.husbandry.fishy_business.title": "'Ich; ghot<PERSON>' rur", "advancements.husbandry.froglights.description": "buqmeylIjDaq Hoch mabeb wovmoHwI' Segh yIqeng", "advancements.husbandry.froglights.title": "wa' Dol nIvDaq matay'DI' maQap!", "advancements.husbandry.kill_axolotl_target.description": "<PERSON>v b<PERSON><PERSON><PERSON><PERSON>' pet<PERSON><PERSON>j <PERSON> 'evta' je", "advancements.husbandry.kill_axolotl_target.title": "rachlaH jup!", "advancements.husbandry.leash_all_frog_variants.description": "Hoch mabeb Segh DajonmeH rIjmeH tlhegh tIlo'", "advancements.husbandry.leash_all_frog_variants.title": "ghewmey tISuq", "advancements.husbandry.make_a_sign_glow.description": "QIn 'echlet ghItlh yIwewmoH", "advancements.husbandry.make_a_sign_glow.title": "ghay tamghay!", "advancements.husbandry.netherite_hoe.description": "rutneD DubmeH netherite ngogh yIlo' 'ej ghIq yIn wIvmeylIj yIchovqa'", "advancements.husbandry.netherite_hoe.title": "vawqu'", "advancements.husbandry.obtain_sniffer_egg.description": "larghwI' QIm yISuq", "advancements.husbandry.obtain_sniffer_egg.title": "<PERSON><PERSON> pIw", "advancements.husbandry.place_dried_ghast_in_water.description": "bIQ<PERSON><PERSON> ghast QaD b<PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>lan", "advancements.husbandry.place_dried_ghast_in_water.title": "bIjatlh 'e' yImev. yItlhutlh!", "advancements.husbandry.plant_any_sniffer_seed.description": "larghwI' raS'IS yIpoch", "advancements.husbandry.plant_any_sniffer_seed.title": "pa'logh poch", "advancements.husbandry.plant_seed.description": "raS'IS yIpoch 'ej nenDaj yIbej", "advancements.husbandry.plant_seed.title": "tIr tIr", "advancements.husbandry.remove_wolf_armor.description": "ngavyaw' may' Sut DateqmeH cha''etlh pe'wI''a' yIlo'", "advancements.husbandry.remove_wolf_armor.title": "yIpe'!", "advancements.husbandry.repair_wolf_armor.description": "ngavyaw' may' Sut QIHlu'pu'bogh DatI'meH woSwa' ghISDen tIlo'", "advancements.husbandry.repair_wolf_armor.title": "chu'law'", "advancements.husbandry.ride_a_boat_with_a_goat.description": "gha'cher g<PERSON><PERSON><PERSON><PERSON> bIQ Duj yI'el 'ej yIchergh", "advancements.husbandry.ride_a_boat_with_a_goat.title": "gha'cher yIchergh!", "advancements.husbandry.root.description": "jup law' Soj law' je ghaj qo'", "advancements.husbandry.root.title": "Satlh", "advancements.husbandry.safely_harvest_honey.description": "lachvoHvo' DayqIr yIboSmeH raQ qul bal je yIlo', 'ej maqSung yInuQQo'", "advancements.husbandry.safely_harvest_honey.title": "yIHoj; bI'oj 'ach yIQojQo'; yIroj vaj Soj nInoj; ghIq loj Doj", "advancements.husbandry.silk_touch_nest.description": "wej maqSung ngaSbogh lachvoH but'e' yIlup; pe'vIlHa' boS tlheH yIlo'", "advancements.husbandry.silk_touch_nest.title": "lachvoH yIvIHmoH", "advancements.husbandry.tactical_fishing.description": "ghotI' yIjon... 'ach ghotI' jonmeH naQ yIlo'Qo'!", "advancements.husbandry.tactical_fishing.title": "bat<PERSON>h ghot<PERSON>' wam", "advancements.husbandry.tadpole_in_a_bucket.description": "HaySInDaq yemghaw yIjon", "advancements.husbandry.tadpole_in_a_bucket.title": "ghew yam", "advancements.husbandry.tame_an_animal.description": "Ha'DIbaH yItlhay'moH", "advancements.husbandry.tame_an_animal.title": "reH yIr'ach maH", "advancements.husbandry.wax_off.description": "Sorpuq buq'Irvo' DayqIr SIr'o' yIteq!", "advancements.husbandry.wax_off.title": "req yIteq", "advancements.husbandry.wax_on.description": "Sorp<PERSON><PERSON> bu<PERSON>'<PERSON>rDaq <PERSON>qIr SIr'o' yIlan!", "advancements.husbandry.wax_on.title": "req y<PERSON>lan", "advancements.husbandry.whole_pack.description": "Hoch ngavyaw' Segh tItlhay'moH", "advancements.husbandry.whole_pack.title": "ghom naQ", "advancements.nether.all_effects.description": "Hoch wanI' DaSIQ 'e' tIquqmoH", "advancements.nether.all_effects.title": "chay' na<PERSON>ev wIpawpu'?", "advancements.nether.all_potions.description": "nItebHa' Hoch 'IDnar taS wanI' tISIQ", "advancements.nether.all_potions.title": "taS vaQqu'", "advancements.nether.brew_potion.description": "'IDnar taS yIvut", "advancements.nether.brew_potion.title": "bewSom qach Sum", "advancements.nether.charge_respawn_anchor.description": "bogh<PERSON>'meH jan y<PERSON><PERSON>u<PERSON><PERSON>u'", "advancements.nether.charge_respawn_anchor.title": "bogh bog<PERSON><PERSON><PERSON> QujwI'", "advancements.nether.create_beacon.description": "rI'meH wovmoHwI' yIchenmoH 'ej yIlan", "advancements.nether.create_beacon.title": "rI'meH qul yIchu'", "advancements.nether.create_full_beacon.description": "rI'meH wovmoHwI' yIHujqu'", "advancements.nether.create_full_beacon.title": "wov wovmoHwI'wI'", "advancements.nether.distract_piglin.description": "piglin DayutmeH qol'om yIlo'", "advancements.nether.distract_piglin.title": "boch", "advancements.nether.explore_nether.description": "Hoch nether 'umber yISuch", "advancements.nether.explore_nether.title": "Hoch ghoch ghoch", "advancements.nether.fast_travel.description": "netherDaq yIleng; ghorDaq SochSaD buq'Ir yIlID", "advancements.nether.fast_travel.title": "pIvchem", "advancements.nether.find_bastion.description": "waw' pIgh yI'el", "advancements.nether.find_bastion.title": "meQtaHbogh qachDaq Suv qoH neH", "advancements.nether.find_fortress.description": "nether j<PERSON>'<PERSON><PERSON> y<PERSON>yot", "advancements.nether.find_fortress.title": "ghIjbogh jem'IH", "advancements.nether.get_wither_skull.description": "wither nobmeD DughrI' yISuq", "advancements.nether.get_wither_skull.title": "ghIj<PERSON><PERSON>'bogh nobmeD", "advancements.nether.loot_bastion.description": "waw' pIghDaq qengHoD yInIH", "advancements.nether.loot_bastion.title": "waw' pIgh", "advancements.nether.netherite_armor.description": "netherite may' Sut vey naQ yISuq", "advancements.nether.netherite_armor.title": "chovelmeH DI tIlo'", "advancements.nether.obtain_ancient_debris.description": "DI tIQ yISuq", "advancements.nether.obtain_ancient_debris.title": "bIngDaq So'lu'", "advancements.nether.obtain_blaze_rod.description": "qul tlhapraghvo' qul naQ yInge'", "advancements.nether.obtain_blaze_rod.title": "qul 'el", "advancements.nether.obtain_crying_obsidian.description": "SaQbogh 'al'on nagh yISuq", "advancements.nether.obtain_crying_obsidian.title": "'anyan 'o<PERSON><PERSON><PERSON> pe' 'Iv?", "advancements.nether.return_to_sender.description": "ghastDaq qul moQ yIbach 'ej yIHoH", "advancements.nether.return_to_sender.title": "ngeHwI'Daq yInobHa'", "advancements.nether.ride_strider.description": "yItwI' DalIghtaHvIS 'atlhqam SuD naQ yIlo'", "advancements.nether.ride_strider.title": "'<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> b<PERSON><PERSON>", "advancements.nether.ride_strider_in_overworld_lava.description": "Dung qo'Daq vaHbo' ngengDaq yItwI' yIlIgh 'ej leng tIqqu'qu'qu' yIDIgh", "advancements.nether.ride_strider_in_overworld_lava.title": "juH rur", "advancements.nether.root.description": "qImroq tuj Sut yIqem", "advancements.nether.root.title": "nether", "advancements.nether.summon_wither.description": "wither yIrIt", "advancements.nether.summon_wither.title": "puH wItlhermoH", "advancements.nether.uneasy_alliance.description": "nethervo' ghast yItoD. pe'vIl ghorDaq yIqem... 'ej ghIq yIHoH", "advancements.nether.uneasy_alliance.title": "boq jotHa'", "advancements.nether.use_lodestone.description": "peQnaghvaD SInan yIlo'", "advancements.nether.use_lodestone.title": "hats tawmey, ju<PERSON><PERSON> h<PERSON>ev", "advancements.progress": "%2$s loch %1$s", "advancements.sad_label": "}}:(", "advancements.story.cure_zombie_villager.description": "yInbogh vengHom ghot lom yIpujmoH, ghIq yIvor", "advancements.story.cure_zombie_villager.title": "yInbogh lom Qel", "advancements.story.deflect_arrow.description": "bach DabotmeH yoD yIlo'", "advancements.story.deflect_arrow.title": "Qo'", "advancements.story.enchant_item.description": "reSmeH raSDaq DochvaD tlheH yIreS", "advancements.story.enchant_item.title": "reSwI'", "advancements.story.enter_the_end.description": "end lojmIt yI'el", "advancements.story.enter_the_end.title": "the end?", "advancements.story.enter_the_nether.description": "nether lojmIt yIchenmoH 'ej yIchu' 'ej yI'el", "advancements.story.enter_the_nether.title": "maghIrnIS", "advancements.story.follow_ender_eye.description": "ender mIn yItlha'", "advancements.story.follow_ender_eye.title": "mIn pIn", "advancements.story.form_obsidian.description": "'al'on nagh yISuq", "advancements.story.form_obsidian.title": "chuch <PERSON> qaD", "advancements.story.iron_tools.description": "tlhIlwI' SommI'lIj yIDub", "advancements.story.iron_tools.title": "'uSqan tlhIlwI' jan 'oH, qar'a'", "advancements.story.lava_bucket.description": "HaySInDaq vaHbo' yIboS", "advancements.story.lava_bucket.title": "tujqu'", "advancements.story.mine_diamond.description": "chanmon y<PERSON>q", "advancements.story.mine_diamond.title": "chanmon!", "advancements.story.mine_stone.description": "nagh DatlhIlmeH tlhIlwI' SommI'lIj chu' yIlo'", "advancements.story.mine_stone.title": "nagh bov", "advancements.story.obtain_armor.description": "bIHub'eghmeH 'uSqan may' Sut yItuQmoH", "advancements.story.obtain_armor.title": "SIryoD yISuq", "advancements.story.root.description": "Quj tIq lut je", "advancements.story.root.title": "minecraft", "advancements.story.shiny_gear.description": "yIn choq chanmon may' Sut", "advancements.story.shiny_gear.title": "chovelmeH chanmon tIlo'", "advancements.story.smelt_iron.description": "nagh 'uSqan yIwatlhmoH", "advancements.story.smelt_iron.title": "SommI' let y<PERSON><PERSON>q", "advancements.story.upgrade_tools.description": "tlhIlwI' SommI' nIv yIchenmoH", "advancements.story.upgrade_tools.title": "tlhIlwI' SommI yIDub", "advancements.toast.challenge": "qaD ta'!", "advancements.toast.goal": "ngoQ ta'!", "advancements.toast.task": "chav chav!", "argument.anchor.invalid": "Dol rIjmoHmeH Daq waS 'oH %s'e'", "argument.angle.incomplete": "waS (wa' tajvaj pIH)", "argument.angle.invalid": "tajvaj waS", "argument.block.id.invalid": "bu<PERSON><PERSON><PERSON><PERSON>'lu'bogh 'oH «%s»'e'", "argument.block.property.duplicate": "%% jatloDpu'jatlu'%", "argument.block.property.invalid": "%3$s DI'onmeH \"%2$s\" lajbe' %1$s buq'Ir", "argument.block.property.novalue": "%% ghotvam <PERSON>qa<PERSON>", "argument.block.property.unclosed": "bu<PERSON>'<PERSON><PERSON> DI'onmeH SoQmoHbogh ] pIH", "argument.block.property.unknown": "«%2$s» DI'on ghajbe' «%1$s» buq'Ir", "argument.block.tag.disallowed": "naDev per chaw'be'lu'; buq'<PERSON><PERSON>' neH", "argument.color.invalid": "rItlh Sovbe'lu'bogh: %s", "argument.component.invalid": "QIn pat 'ay' waS: %s", "argument.criteria.invalid": "rIchwIn Sovbe'lu'bogh 'oH «%s»'e'", "argument.dimension.invalid": "'u' Sovbe'lu'bogh: %s", "argument.double.big": "yIHmey lo'laHbe'ch %%ugh, vaj yIQub.", "argument.double.low": "Double must not be less than %s, found %s", "argument.entity.invalid": "uuid pong joq waS", "argument.entity.notfound.entity": "pagh Dol tu'lu'", "argument.entity.notfound.player": "pagh QujwI' tu'lu'", "argument.entity.options.advancements.description": "chav<PERSON><PERSON> ghajbogh QujwI''e'", "argument.entity.options.distance.description": "<PERSON><PERSON> chu<PERSON> 'ar", "argument.entity.options.distance.negative": "DoplaHbe' chuq", "argument.entity.options.dx.description": "x x + dx je jo<PERSON><PERSON><PERSON>", "argument.entity.options.dy.description": "y y + dy je jo<PERSON><PERSON><PERSON>", "argument.entity.options.dz.description": "z z + dz je jo<PERSON><PERSON><PERSON>", "argument.entity.options.gamemode.description": "QujwI''e' lo'bogh Quj lo'", "argument.entity.options.inapplicable": "Option '%s' isn't applicable here", "argument.entity.options.level.description": "Hen pat<PERSON>h", "argument.entity.options.level.negative": "Dopbe'nIS patlh", "argument.entity.options.limit.description": "cheghbogh Dol 'aqroS", "argument.entity.options.limit.toosmall": "wa' juSnIS veH", "argument.entity.options.mode.invalid": "<PERSON>u<PERSON><PERSON> waS 'oH «%s»'e'", "argument.entity.options.name.description": "Dol pong", "argument.entity.options.nbt.description": "NBT ghajbogh Dol", "argument.entity.options.predicate.description": "Custom predicate", "argument.entity.options.scores.description": "pe''eghbogh Dol", "argument.entity.options.sort.description": "<PERSON><PERSON>", "argument.entity.options.sort.irreversible": "patlhmoHmeH mIw waS 'oH «%s»'e'", "argument.entity.options.tag.description": "per ghajbo<PERSON>l", "argument.entity.options.team.description": "ghom muv<PERSON><PERSON>bogh <PERSON>", "argument.entity.options.type.description": "<PERSON><PERSON>", "argument.entity.options.type.invalid": "Dol Segh waS 'oH «%s»'e'", "argument.entity.options.unknown": "DuH Sovbe'lu'bogh 'oH «%s»'e'", "argument.entity.options.unterminated": "DuHmey natlIS pIH", "argument.entity.options.valueless": "Expected value for option '%s'", "argument.entity.options.x.description": "«x» Daq", "argument.entity.options.x_rotation.description": "x rober bu<PERSON><PERSON>'D<PERSON>' jIr 'ar <PERSON>", "argument.entity.options.y.description": "«y» Daq", "argument.entity.options.y_rotation.description": "y rober bu<PERSON><PERSON>'DI' jIr 'ar <PERSON>l", "argument.entity.options.z.description": "«z» Daq", "argument.entity.selector.allEntities": "Hoch Dol", "argument.entity.selector.allPlayers": "Hoch QujwI'pu'", "argument.entity.selector.missing": "Dach wIvwI' Segh", "argument.entity.selector.nearestEntity": "<PERSON><PERSON>'", "argument.entity.selector.nearestPlayer": "QujwI' Sumqu'", "argument.entity.selector.not_allowed": "wIvwI' chaw'be'lu'", "argument.entity.selector.randomPlayer": "Qujw<PERSON><PERSON> 'al", "argument.entity.selector.self": "Dol<PERSON>m", "argument.entity.selector.unknown": "wIvwI' Segh Sovbe'lu'bogh 'oH «%s»'e'", "argument.entity.toomany": "wa' Dol neH chaw'lu', 'ach latlh chaw' wIvwI'vam", "argument.enum.invalid": "mI' muj 'oH %s'e'", "argument.float.big": "Float must not be more than %s, found %s", "argument.float.low": "Float must not be less than %s, found %s", "argument.gamemode.invalid": "<PERSON>uj lo' Sovbe'lu'bogh: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "ngu'meH mI' waS", "argument.id.unknown": "ngu'meH mI' Sovbe'lu'bogh: %s", "argument.integer.big": "Integer must not be more than %s, found %s", "argument.integer.low": "Integer must not be less than %s, found %s", "argument.item.id.invalid": "<PERSON>h Sovbe'lu'bogh 'oH «%s»'e'", "argument.item.tag.disallowed": "naDev per chaw'be'lu'; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "argument.literal.incorrect": "Expected literal %s", "argument.long.big": "Long must not be more than %s, found %s", "argument.long.low": "Long must not be less than %s, found %s", "argument.message.too_long": "tlhoy tIq QIn (%1$s tIq law' %2$s tIq puS, %2$s 'oH 'aqroS'e') ", "argument.nbt.array.invalid": "DaH Segh waS 'oH %s'e'", "argument.nbt.array.mixed": "Can't insert %s into %s", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "ngaQHa'moHwI' pIH", "argument.nbt.expected.value": "Dotlh pIH", "argument.nbt.list.mixed": "Can't insert %s into list of %s", "argument.nbt.trailing": "ratlhbogh De' pIHbe'lu'bogh", "argument.player.entities": "QujwI' neH choHlaH ra'meH ghItlhvam, 'ach Dol yugh wIvwI'vam", "argument.player.toomany": "wa' QujwI' neH chaw'lu', 'ach latlh chaw' wIvwI'vam", "argument.player.unknown": "QujwI'vetlh tu'lu'be'", "argument.pos.missing.double": "Quv pIHpu'", "argument.pos.missing.int": "b<PERSON><PERSON><PERSON><PERSON><PERSON> pIH", "argument.pos.mixed": "qo' Quv Quv Sum je merghmoHlaHtaHbe' (^ lo'nIS pagh lo'be'nIS Hoch)", "argument.pos.outofbounds": "ve<PERSON> la<PERSON><PERSON>'b<PERSON>h <PERSON> tu'lu'", "argument.pos.outofworld": "qo'vam <PERSON><PERSON>'", "argument.pos.unloaded": "lI'be'l<PERSON>' <PERSON><PERSON><PERSON><PERSON><PERSON>", "argument.pos2d.incomplete": "Incomplete (expected 2 coordinates)", "argument.pos3d.incomplete": "waS (wej Quv pIH)", "argument.range.empty": "mI' mI' chuq joq pIH", "argument.range.ints": "vI' ghajbe'nISbogh mI' neH chaw'", "argument.range.swapped": "rav tIn law' 'aqroS tIn puS 'e' chaw'be'", "argument.resource.invalid_type": "Element '%s' has wrong type '%s' (expected '%s')", "argument.resource.not_found": "Can't find element '%s' of type '%s'", "argument.resource_or_id.failed_to_parse": "qach pong poj 'e' luj: %s", "argument.resource_or_id.invalid": "waS ngu'meH mI' per ghap", "argument.resource_or_id.no_such_element": "‹%2$s› qawHaqDaq ‹%1$s› 'ay' tu'laHbe'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "Tag '%s' has wrong type '%s' (expected '%s')", "argument.resource_tag.not_found": "Can't find tag '%s' of type '%s'", "argument.rotation.incomplete": "waS (cha' Quv pIH)", "argument.scoreHolder.empty": "pagh rarbogh mIvwa' ngaSwI' tu'lu'ta'", "argument.scoreboardDisplaySlot.invalid": "Ha<PERSON><PERSON><PERSON> waS 'oH «%s»'e'", "argument.style.invalid": "Ho'DoS waS: %s", "argument.time.invalid_tick_count": "DopHa'nIS mIwHom mI'", "argument.time.invalid_unit": "nIqHom Dol waS", "argument.time.tick_count_too_low": "The tick count must not be less than %s, found %s", "argument.uuid.invalid": "UUID waS", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "buq'Ir per Sov<PERSON>'lu'bogh 'oH «%s»'e'", "arguments.function.tag.unknown": "mIw per Sovbe'lu'bogh 'oH «%s»'e'", "arguments.function.unknown": "mIw Sovbe'lu'bogh 'oH «%s»'e'", "arguments.item.component.expected": "Doch 'ay' pIH", "arguments.item.component.malformed": "‹%s› 'ay' chenHa'moH<PERSON>'bogh: ‹%s›", "arguments.item.component.repeated": "cha'logh <PERSON>h 'ay' ‹%s› tu'lu', 'ach wa' mI' neH perlaH", "arguments.item.component.unknown": "<PERSON><PERSON> 'ay' Sov<PERSON>'lu'bogh ‹%s›", "arguments.item.malformed": "<PERSON><PERSON> chenHa'moH<PERSON>'bogh: ‹%s›", "arguments.item.overstacked": "%s can only stack up to %s", "arguments.item.predicate.malformed": "Malformed '%s' predicate: '%s'", "arguments.item.predicate.unknown": "Unknown item predicate '%s'", "arguments.item.tag.unknown": "Doch pere Sovbe'lu'bogh 'oH «%s»'e'", "arguments.nbtpath.node.invalid": "nbt He 'ay' waS", "arguments.nbtpath.nothing_found": "pagh 'ay' nel %s", "arguments.nbtpath.too_deep": "Resulting NBT too deeply nested", "arguments.nbtpath.too_large": "Resulting NBT too large", "arguments.objective.notFound": "mIvwa'mey ngoQ Sovbe'lu'bogh 'oH «%s»'e'", "arguments.objective.readonly": "%s mIvwa' ngoQ laDlaH neH vay'", "arguments.operation.div0": "pagh<PERSON>h boq<PERSON><PERSON>'e<PERSON><PERSON><PERSON><PERSON>' vay'", "arguments.operation.invalid": "mIw waS", "arguments.swizzle.invalid": "Quv waS; mergh x, y, z je 'e' pIH", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON>' <PERSON><PERSON>", "attribute.name.armor_toughness": "may' Sut QanmeH HoS", "attribute.name.attack_damage": "HIvmeH QIH", "attribute.name.attack_knockback": "yuvmeH HoS", "attribute.name.attack_speed": "HIvmeH Do", "attribute.name.block_break_speed": "b<PERSON><PERSON><PERSON><PERSON><PERSON> ghorme<PERSON>", "attribute.name.block_interaction_range": "bu<PERSON><PERSON><PERSON>r SIchmeH chuq", "attribute.name.burning_time": "meQmeH poH", "attribute.name.camera_distance": "mIllogh qonwI' chuq", "attribute.name.entity_interaction_range": "Dol SIchmeH chuq", "attribute.name.explosion_knockback_resistance": "jorwI' Surma' 'ommeH Ho<PERSON>", "attribute.name.fall_damage_multiplier": "pummeH QIH boq'eghbogh mI'", "attribute.name.flying_speed": "puvmeH Do", "attribute.name.follow_range": "yagh tlha'meH chuq", "attribute.name.generic.armor": "<PERSON>' <PERSON><PERSON>", "attribute.name.generic.armor_toughness": "may' <PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "HIv QIH", "attribute.name.generic.attack_knockback": "QachmeH yuv", "attribute.name.generic.attack_speed": "HIvmeH Do", "attribute.name.generic.block_interaction_range": "bu<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON> chuq", "attribute.name.generic.burning_time": "meQmeH poH", "attribute.name.generic.entity_interaction_range": "Dol rarmeH chuq", "attribute.name.generic.explosion_knockback_resistance": "jorlu'DI' yuvlu' 'e' 'ommeH patlh", "attribute.name.generic.fall_damage_multiplier": "pumwI' QIH choHbogh mI''e'", "attribute.name.generic.flying_speed": "puvmeH Do", "attribute.name.generic.follow_range": "yagh tlha'meH chuq", "attribute.name.generic.gravity": "tlham", "attribute.name.generic.jump_strength": "SupmeH HoS", "attribute.name.generic.knockback_resistance": "yuvlu' 'e' 'om", "attribute.name.generic.luck": "Do'", "attribute.name.generic.max_absorption": "bummeH 'aqroS", "attribute.name.generic.max_health": "'aqroS 'on'aS", "attribute.name.generic.movement_efficiency": "vIHmeH Do", "attribute.name.generic.movement_speed": "Do", "attribute.name.generic.oxygen_bonus": "latlh yInSIp", "attribute.name.generic.safe_fall_distance": "pummeH chuq jochbe'", "attribute.name.generic.scale": "juvmeH mI'", "attribute.name.generic.step_height": "SalmeH chuq", "attribute.name.generic.water_movement_efficiency": "bIQDaq vIHmeH Do", "attribute.name.gravity": "tlham", "attribute.name.horse.jump_strength": "Sargh SupmeH HoS", "attribute.name.jump_strength": "SupmeH HoS", "attribute.name.knockback_resistance": "yuvwI' 'ommeH HoS", "attribute.name.luck": "Do'", "attribute.name.max_absorption": "'aqroS 'on'aS chach", "attribute.name.max_health": "'aqroS 'on'aS", "attribute.name.mining_efficiency": "tlhIlmeH Do", "attribute.name.movement_efficiency": "vIHmeH Do", "attribute.name.movement_speed": "Do", "attribute.name.oxygen_bonus": "latlh yInSIp", "attribute.name.player.block_break_speed": "b<PERSON><PERSON><PERSON><PERSON><PERSON> ghorme<PERSON>", "attribute.name.player.block_interaction_range": "bu<PERSON><PERSON><PERSON>r SIchmeH chuq", "attribute.name.player.entity_interaction_range": "Dol SIchmeH chuq", "attribute.name.player.mining_efficiency": "tlhIlmeH Do", "attribute.name.player.sneaking_speed": "joDmeH Do", "attribute.name.player.submerged_mining_speed": "bIQDaq tlhIlmeH Do", "attribute.name.player.sweeping_damage_ratio": "QachwI' QIH SubmaH", "attribute.name.safe_fall_distance": "pummeH chuq jochbe'", "attribute.name.scale": "juvmeH SubmaH", "attribute.name.sneaking_speed": "joDmeH Do", "attribute.name.spawn_reinforcements": "yInbogh lom ngaq", "attribute.name.step_height": "SalmeH chuq", "attribute.name.submerged_mining_speed": "bIQDaq tlhIlmeH Do", "attribute.name.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.tempt_range": "Mob Tempt Range", "attribute.name.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "yInbogh lom ngaq", "biome.minecraft.badlands": "<PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "bam<PERSON>' ngem yI<PERSON>", "biome.minecraft.basalt_deltas": "Hotnagh puH", "biome.minecraft.beach": "bIQ'a' HeH", "biome.minecraft.birch_forest": "ber<PERSON>ch Sor ngem", "biome.minecraft.cherry_grove": "<PERSON><PERSON><PERSON>em", "biome.minecraft.cold_ocean": "bIQ'a' bIr", "biome.minecraft.crimson_forest": "nether Sor <PERSON><PERSON> ngem", "biome.minecraft.dark_forest": "<PERSON><PERSON>", "biome.minecraft.deep_cold_ocean": "bIQ'a' bIr jaQ", "biome.minecraft.deep_dark": "QIb jaQ", "biome.minecraft.deep_frozen_ocean": "bIQ'a' taD jaQ", "biome.minecraft.deep_lukewarm_ocean": "bIQ'a' buj jaQ", "biome.minecraft.deep_ocean": "bIQ'a' jaQ", "biome.minecraft.desert": "Deb", "biome.minecraft.dripstone_caves": "De'lor DIS", "biome.minecraft.end_barrens": "<PERSON> 'ambay qoj", "biome.minecraft.end_highlands": "end 'ambay HuD", "biome.minecraft.end_midlands": "end 'ambay <PERSON>", "biome.minecraft.eroded_badlands": "<PERSON><PERSON> pat<PERSON>h ngawlu'pu'bogh", "biome.minecraft.flower_forest": "'In<PERSON><PERSON> ngem", "biome.minecraft.forest": "ngem", "biome.minecraft.frozen_ocean": "bIQ'a' taD", "biome.minecraft.frozen_peaks": "HuD'a' taD", "biome.minecraft.frozen_river": "bIQtIq taD", "biome.minecraft.grove": "<PERSON><PERSON>'a' ngem", "biome.minecraft.ice_spikes": "chuch w<PERSON>lmey", "biome.minecraft.jagged_peaks": "HuD'a' job", "biome.minecraft.jungle": "ngem yIQ", "biome.minecraft.lukewarm_ocean": "bI<PERSON>'a' buj", "biome.minecraft.lush_caves": "DIS nIl", "biome.minecraft.mangrove_swamp": "mangghov Sor puH yIQ", "biome.minecraft.meadow": "HuD'a' puH nIl", "biome.minecraft.mushroom_fields": "'at<PERSON><PERSON><PERSON>m puH", "biome.minecraft.nether_wastes": "ghe'tor <PERSON>", "biome.minecraft.ocean": "bIQ'a'", "biome.minecraft.old_growth_birch_forest": "ber<PERSON><PERSON> Sor qan ngem", "biome.minecraft.old_growth_pine_taiga": "qIrqoD qan ngem bIr", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h qIrqoD qan ngem bIr", "biome.minecraft.pale_garden": "<PERSON><PERSON><PERSON><PERSON> wov", "biome.minecraft.plains": "puH beQ", "biome.minecraft.river": "bIQtIq", "biome.minecraft.savanna": "QaDbogh puH nIl", "biome.minecraft.savanna_plateau": "QaDbogh puH nIl 'ej jen<PERSON>gh", "biome.minecraft.small_end_islands": "end 'ambayHommey", "biome.minecraft.snowy_beach": "chal chuch bIQ'a' HeH", "biome.minecraft.snowy_plains": "chal chuch puH beQ", "biome.minecraft.snowy_slopes": "chal chuch HuD'a'", "biome.minecraft.snowy_taiga": "ngem ta<PERSON>", "biome.minecraft.soul_sand_valley": "qa' <PERSON>'ol ngech", "biome.minecraft.sparse_jungle": "r<PERSON><PERSON><PERSON><PERSON> ngem yIQ", "biome.minecraft.stony_peaks": "nagh HuD'a'", "biome.minecraft.stony_shore": "bIQ'a' nagh HeH", "biome.minecraft.sunflower_plains": "<PERSON><PERSON><PERSON><PERSON>Hom puH beQ", "biome.minecraft.swamp": "puH yIQ", "biome.minecraft.taiga": "ngem bIr", "biome.minecraft.the_end": "end", "biome.minecraft.the_void": "'a<PERSON><PERSON><PERSON>", "biome.minecraft.warm_ocean": "bI<PERSON>'a' ghun", "biome.minecraft.warped_forest": "nether Sor SuD ngem", "biome.minecraft.windswept_forest": "SuS ngem", "biome.minecraft.windswept_gravelly_hills": "SuS yetmoS HuD", "biome.minecraft.windswept_hills": "SuS HuD", "biome.minecraft.windswept_savanna": "SuS QaDbogh puH nIl", "biome.minecraft.wooded_badlands": "<PERSON><PERSON> pat<PERSON>h ngem", "block.minecraft.acacia_button": "'a<PERSON><PERSON>'Sa <PERSON> le<PERSON>", "block.minecraft.acacia_door": "'a<PERSON><PERSON>'<PERSON> lo<PERSON>", "block.minecraft.acacia_fence": "'aq<PERSON>'Sa <PERSON>r tlho<PERSON>'Hom", "block.minecraft.acacia_fence_gate": "'a<PERSON><PERSON>'Sa <PERSON>r tlho<PERSON>'Hom lojmIt", "block.minecraft.acacia_hanging_sign": "'a<PERSON><PERSON>'<PERSON> Sor QIn 'echlet tlhep", "block.minecraft.acacia_leaves": "'aqey'<PERSON> pormey", "block.minecraft.acacia_log": "'aqey'Sa <PERSON>'a'", "block.minecraft.acacia_planks": "'aq<PERSON>'<PERSON>", "block.minecraft.acacia_pressure_plate": "'aq<PERSON>'Sa <PERSON>r <PERSON> 'echlet", "block.minecraft.acacia_sapling": "'a<PERSON><PERSON>'<PERSON>r<PERSON>", "block.minecraft.acacia_sign": "'a<PERSON><PERSON>'<PERSON>r QIn 'echlet", "block.minecraft.acacia_slab": "'a<PERSON><PERSON>'<PERSON> bu<PERSON>'<PERSON>r b<PERSON>", "block.minecraft.acacia_stairs": "'aq<PERSON>'<PERSON>r letlh", "block.minecraft.acacia_trapdoor": "'a<PERSON><PERSON>'<PERSON> lojmIt", "block.minecraft.acacia_wall_hanging_sign": "'aq<PERSON>'Sa Sor tlhoy' QIn 'echlet tlhep", "block.minecraft.acacia_wall_sign": "'aq<PERSON>'Sa Sor tlhoy' QIn 'echlet", "block.minecraft.acacia_wood": "'a<PERSON><PERSON>'<PERSON>", "block.minecraft.activator_rail": "chu'wI' tIHmey", "block.minecraft.air": "rewve'", "block.minecraft.allium": "'<PERSON><PERSON> '<PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "choSom nguv buq'<PERSON>r", "block.minecraft.amethyst_cluster": "choSom nguv qutmey", "block.minecraft.ancient_debris": "DI tIQ", "block.minecraft.andesite": "chan nagh", "block.minecraft.andesite_slab": "chan nagh bu<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.andesite_stairs": "chan nagh letlh", "block.minecraft.andesite_wall": "chan nagh tlhoy'", "block.minecraft.anvil": "'achme'", "block.minecraft.attached_melon_stem": "qaq na<PERSON> r<PERSON>bogh", "block.minecraft.attached_pumpkin_stem": "'aq<PERSON>' rar<PERSON>'bogh", "block.minecraft.azalea": "'aSe'lIya SorHom", "block.minecraft.azalea_leaves": "DIS lav pormey", "block.minecraft.azure_bluet": "'aSur bIlu''et 'InSong", "block.minecraft.bamboo": "bambu'", "block.minecraft.bamboo_block": "b<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_button": "bam<PERSON>' le<PERSON>", "block.minecraft.bamboo_door": "bambu' lojmIt", "block.minecraft.bamboo_fence": "bam<PERSON>' t<PERSON><PERSON><PERSON>'<PERSON>m", "block.minecraft.bamboo_fence_gate": "bam<PERSON>' t<PERSON><PERSON><PERSON>'Hom lojmIt", "block.minecraft.bamboo_hanging_sign": "bam<PERSON>' <PERSON><PERSON><PERSON> 'echlet tlhep", "block.minecraft.bamboo_mosaic": "bambu' HaSre<PERSON>", "block.minecraft.bamboo_mosaic_slab": "b<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> bu<PERSON>'<PERSON>r b<PERSON>", "block.minecraft.bamboo_mosaic_stairs": "bam<PERSON>' HaSre<PERSON> letlh", "block.minecraft.bamboo_planks": "bam<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_pressure_plate": "bam<PERSON>' <PERSON><PERSON> 'echlet", "block.minecraft.bamboo_sapling": "b<PERSON><PERSON><PERSON> <PERSON><PERSON>b", "block.minecraft.bamboo_sign": "bam<PERSON><PERSON> <PERSON><PERSON><PERSON> 'e<PERSON>let", "block.minecraft.bamboo_slab": "b<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.bamboo_stairs": "bambu' letlh", "block.minecraft.bamboo_trapdoor": "bambu' vonmeH lojmIt", "block.minecraft.bamboo_wall_hanging_sign": "bam<PERSON>' tlhoy' QIn 'echlet tlhep", "block.minecraft.bamboo_wall_sign": "bam<PERSON>' tlho<PERSON>' QIn 'echlet", "block.minecraft.banner.base.black": "<PERSON><PERSON><PERSON><PERSON> qIj", "block.minecraft.banner.base.blue": "SuDbogh weSje<PERSON> '<PERSON>j <PERSON>", "block.minecraft.banner.base.brown": "Doq<PERSON>gh weS<PERSON><PERSON> 'ej wovbe'bogh", "block.minecraft.banner.base.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h we<PERSON><PERSON><PERSON> 'e<PERSON> wov<PERSON>gh", "block.minecraft.banner.base.gray": "qIjbogh weSje<PERSON> 'ej wovbogh", "block.minecraft.banner.base.green": "<PERSON><PERSON><PERSON><PERSON>'", "block.minecraft.banner.base.light_blue": "SuDbogh weSjech 'ej wovbogh", "block.minecraft.banner.base.light_gray": "qIjbogh weS<PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.base.lime": "<PERSON><PERSON><PERSON><PERSON>'bogh we<PERSON><PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.base.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h we<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> w<PERSON><PERSON>gh", "block.minecraft.banner.base.orange": "Doqbogh weSje<PERSON> 'ej beqpuj rurbogh", "block.minecraft.banner.base.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh we<PERSON><PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.base.purple": "ghIrep Hurgh rurbogh weSjech'e'", "block.minecraft.banner.base.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h joqw<PERSON>' naQ", "block.minecraft.banner.base.white": "weSjech chIS", "block.minecraft.banner.base.yellow": "SuDbogh weS<PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.border.black": "<PERSON><PERSON> qIj", "block.minecraft.banner.border.blue": "SuDbogh HeH <PERSON><PERSON><PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON><PERSON>ej wovbe'bogh", "block.minecraft.banner.border.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h <PERSON> <PERSON><PERSON><PERSON> w<PERSON><PERSON>", "block.minecraft.banner.border.gray": "qIjbogh He<PERSON> <PERSON><PERSON>j wovbogh", "block.minecraft.banner.border.green": "HeH SuDqu'", "block.minecraft.banner.border.light_blue": "SuDbogh HeH 'ej wovbogh", "block.minecraft.banner.border.light_gray": "qIj<PERSON>gh <PERSON><PERSON> <PERSON>ej wovqu'bogh", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh <PERSON><PERSON> <PERSON>ej wovqu'bogh", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.orange": "<PERSON>q<PERSON><PERSON> He<PERSON> <PERSON><PERSON><PERSON> beq<PERSON>j rurbogh", "block.minecraft.banner.border.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h <PERSON><PERSON> <PERSON><PERSON>j wovqu'bogh", "block.minecraft.banner.border.purple": "ghIrep Hurgh rurbogh HeH'e'", "block.minecraft.banner.border.red": "<PERSON><PERSON>", "block.minecraft.banner.border.white": "HeH chIS", "block.minecraft.banner.border.yellow": "SuDbogh He<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.bricks.black": "ngogh reD qIj", "block.minecraft.banner.bricks.blue": "SuDbogh ngogh re<PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.brown": "<PERSON><PERSON><PERSON><PERSON> ngogh re<PERSON> 'ej wovbe'bogh", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh ngogh re<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.bricks.gray": "qIjbogh ngogh re<PERSON> 'ej wovbogh", "block.minecraft.banner.bricks.green": "ngogh reD SuDqu'", "block.minecraft.banner.bricks.light_blue": "SuDbogh ngogh re<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.bricks.light_gray": "qIjbogh ngogh reD 'ej wovqu'bogh", "block.minecraft.banner.bricks.lime": "<PERSON><PERSON><PERSON><PERSON>'bogh ngogh reD 'ej wovqu'bogh", "block.minecraft.banner.bricks.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h ngogh re<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.bricks.orange": "Doqbogh ngogh re<PERSON> 'ej beqpuj rurbogh", "block.minecraft.banner.bricks.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh ngogh re<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.bricks.purple": "ghIrep Hurgh rurbogh ngogh reD'e'", "block.minecraft.banner.bricks.red": "ng<PERSON>h <PERSON>'", "block.minecraft.banner.bricks.white": "ngogh reD chIS", "block.minecraft.banner.bricks.yellow": "SuDbogh ngogh re<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.circle.black": "botlh gho qIj", "block.minecraft.banner.circle.blue": "SuDbogh botlh g<PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.brown": "<PERSON><PERSON><PERSON><PERSON> botlh gho 'ej wovbe'bogh", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh botlh gho 'ej wovbogh", "block.minecraft.banner.circle.gray": "qIjbogh botlh gho 'ej wovbogh", "block.minecraft.banner.circle.green": "botlh gho SuDqu'", "block.minecraft.banner.circle.light_blue": "SuDbogh botlh gho 'ej wovbogh", "block.minecraft.banner.circle.light_gray": "qIj<PERSON><PERSON> botlh gho 'ej wovqu'bogh", "block.minecraft.banner.circle.lime": "<PERSON><PERSON><PERSON><PERSON>'bogh botlh gho 'ej wovqu'bogh", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh botlh gho 'ej wov<PERSON>gh", "block.minecraft.banner.circle.orange": "Do<PERSON><PERSON><PERSON> botlh gho 'ej beq<PERSON>j rurbogh", "block.minecraft.banner.circle.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh botlh gho 'ej wovqu'bogh", "block.minecraft.banner.circle.purple": "ghIrep Hurgh rurbogh botlh gho'e'", "block.minecraft.banner.circle.red": "botlh gho Doqqu'", "block.minecraft.banner.circle.white": "botlh gho chIS", "block.minecraft.banner.circle.yellow": "SuD<PERSON>gh botlh gho 'ej wovqu'bogh", "block.minecraft.banner.creeper.black": "creeper <PERSON><PERSON> qIj", "block.minecraft.banner.creeper.blue": "SuDbogh creeper <PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.brown": "<PERSON><PERSON><PERSON><PERSON> creeper <PERSON><PERSON> 'ej wovbe'bogh", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh creeper <PERSON><PERSON> 'e<PERSON> w<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.gray": "qIjbogh creeper <PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.creeper.green": "creeper <PERSON><PERSON>'", "block.minecraft.banner.creeper.light_blue": "SuDbogh creeper <PERSON><PERSON> 'e<PERSON> wov<PERSON>gh", "block.minecraft.banner.creeper.light_gray": "qIjbogh creeper <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.creeper.lime": "<PERSON><PERSON><PERSON><PERSON>'bogh creeper <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh creeper <PERSON><PERSON> <PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON><PERSON><PERSON> creeper <PERSON><PERSON> <PERSON><PERSON><PERSON> beq<PERSON>j rurbogh", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh creeper <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.creeper.purple": "ghIrep Hurgh rurbogh creeper Degh'e'", "block.minecraft.banner.creeper.red": "creeper <PERSON><PERSON>'", "block.minecraft.banner.creeper.white": "creeper Degh chIS", "block.minecraft.banner.creeper.yellow": "SuDbogh creeper <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.cross.black": "bo'ghey me'cheD qIj", "block.minecraft.banner.cross.blue": "Su<PERSON><PERSON><PERSON> bo'ghey me'cheD 'ej <PERSON>", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON><PERSON> bo'ghey me'cheD 'ej wovbe'bogh", "block.minecraft.banner.cross.cyan": "SuD<PERSON><PERSON>'bogh bo'ghey me'cheD 'ej wovbogh", "block.minecraft.banner.cross.gray": "qIjbogh bo'ghey me'cheD 'ej wovbogh", "block.minecraft.banner.cross.green": "bo'ghey me'cheD SuDqu'", "block.minecraft.banner.cross.light_blue": "Su<PERSON><PERSON><PERSON> bo'ghey me'cheD 'ej wovbogh", "block.minecraft.banner.cross.light_gray": "qIjbogh bo'ghey me'cheD 'ej wovqu'bogh", "block.minecraft.banner.cross.lime": "SuDq<PERSON>'bogh bo'ghey me'cheD 'ej wovqu'bogh", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON><PERSON>'bogh bo'ghey me'cheD 'ej wovbogh", "block.minecraft.banner.cross.orange": "<PERSON><PERSON><PERSON><PERSON> bo'ghey me'cheD 'ej beqpuj rurbogh", "block.minecraft.banner.cross.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh bo'ghey me'cheD 'ej wovqu'bogh", "block.minecraft.banner.cross.purple": "ghIrep Hurgh rurbogh bo'ghey me'cheD'e'", "block.minecraft.banner.cross.red": "bo'ghey me'cheD Do<PERSON>qu'", "block.minecraft.banner.cross.white": "bo'ghey me'cheD chIS", "block.minecraft.banner.cross.yellow": "<PERSON><PERSON><PERSON><PERSON> bo'ghey me'cheD 'ej wovqu'bogh", "block.minecraft.banner.curly_border.black": "qIjbogh HeH tIy", "block.minecraft.banner.curly_border.blue": "SuDbogh HeH tIy '<PERSON>j <PERSON>", "block.minecraft.banner.curly_border.brown": "Doqbogh HeH tIy 'ej wovbe'bogh", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h <PERSON><PERSON> tIy 'ej wovbogh", "block.minecraft.banner.curly_border.gray": "qIjbogh HeH tIy 'ej wovbogh", "block.minecraft.banner.curly_border.green": "Su<PERSON><PERSON><PERSON><PERSON>b<PERSON>h He<PERSON> tIy", "block.minecraft.banner.curly_border.light_blue": "SuDbogh HeH tIy 'ej wovbogh", "block.minecraft.banner.curly_border.light_gray": "qIjbogh HeH tIy 'ej wovqu'bogh", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON>'bogh <PERSON><PERSON> tIy 'ej wovqu'bogh", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h <PERSON><PERSON> tIy 'ej wov<PERSON>gh", "block.minecraft.banner.curly_border.orange": "Doqbogh HeH tIy 'ej beqpuj rurbogh", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh <PERSON><PERSON> tIy 'ej wovqu'bogh", "block.minecraft.banner.curly_border.purple": "ghIrep Hurgh rurbogh HeH'e' tIy", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h <PERSON> tIy", "block.minecraft.banner.curly_border.white": "chISbogh HeH tIy", "block.minecraft.banner.curly_border.yellow": "SuDbogh HeH tIy 'ej wovqu'bogh", "block.minecraft.banner.diagonal_left.black": "yor poS bo'ghey qIj", "block.minecraft.banner.diagonal_left.blue": "SuDbogh yor poS bo'ghey 'e<PERSON>", "block.minecraft.banner.diagonal_left.brown": "Do<PERSON><PERSON><PERSON> yor poS bo'ghey 'ej wovbe'bogh", "block.minecraft.banner.diagonal_left.cyan": "Su<PERSON><PERSON><PERSON>'bogh yor poS bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_left.gray": "qIjbogh yor poS bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_left.green": "yor poS bo'ghey SuDqu'", "block.minecraft.banner.diagonal_left.light_blue": "SuDbogh yor poS bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_left.light_gray": "qIjbogh yor poS bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_left.lime": "SuD<PERSON><PERSON>'bogh yor poS bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_left.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh yor poS bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_left.orange": "Doqbogh yor poS bo'ghey 'ej beqpuj rurbogh", "block.minecraft.banner.diagonal_left.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh yor poS bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_left.purple": "ghIrep Hurgh rurbogh yor poS bo'ghey'e'", "block.minecraft.banner.diagonal_left.red": "yor poS bo'ghey Doqqu'", "block.minecraft.banner.diagonal_left.white": "yor poS bo'ghey chIS", "block.minecraft.banner.diagonal_left.yellow": "SuDbogh yor poS bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_right.black": "yor nIH bo'ghey qIj", "block.minecraft.banner.diagonal_right.blue": "SuDbogh yor nIH bo'ghey 'e<PERSON>", "block.minecraft.banner.diagonal_right.brown": "Doq<PERSON>gh yor nIH bo'ghey 'ej wovbe'bogh", "block.minecraft.banner.diagonal_right.cyan": "Su<PERSON><PERSON><PERSON>'bogh yor nIH bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_right.gray": "qIjbogh yor nIH bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_right.green": "yor nIH bo'ghey SuDqu'", "block.minecraft.banner.diagonal_right.light_blue": "SuDbogh yor nIH bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_right.light_gray": "qIjbogh yor nIH bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_right.lime": "SuD<PERSON><PERSON>'bogh yor nIH bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_right.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh yor nIH bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_right.orange": "Doqbogh yor nIH bo'ghey 'ej beqpuj rurbogh", "block.minecraft.banner.diagonal_right.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh yor nIH bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_right.purple": "ghIrep Hurgh rurbogh yor nIH bo'ghey'e'", "block.minecraft.banner.diagonal_right.red": "yor nIH bo'ghey Doqqu'", "block.minecraft.banner.diagonal_right.white": "yor nIH bo'ghey chIS", "block.minecraft.banner.diagonal_right.yellow": "SuDbogh yor nIH bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_up_left.black": "pIrmuS poS bo'ghey qIj", "block.minecraft.banner.diagonal_up_left.blue": "SuDbogh pIrmuS poS bo'ghey 'ej <PERSON>", "block.minecraft.banner.diagonal_up_left.brown": "Doqbogh pIrmuS poS bo'ghey 'ej wovbe'bogh", "block.minecraft.banner.diagonal_up_left.cyan": "SuDqu'bogh pIrmuS poS bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_up_left.gray": "qIjbogh pIrmuS poS bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_up_left.green": "pIrmuS poS bo'ghey SuDqu'", "block.minecraft.banner.diagonal_up_left.light_blue": "SuDbogh pIrmuS poS bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_up_left.light_gray": "qIjbogh pIrmuS poS bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_up_left.lime": "SuDq<PERSON>'bogh pIrmuS poS bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmuS poS bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_up_left.orange": "Doqbogh pIrmuS poS bo'ghey 'ej beqpuj rurbogh", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmuS poS bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_up_left.purple": "ghIrep Hurgh rurbogh pIrmuS poS bo'ghey'e'", "block.minecraft.banner.diagonal_up_left.red": "pIrmuS poS bo'ghey Doqqu'", "block.minecraft.banner.diagonal_up_left.white": "pIrmuS poS bo'ghey chIS", "block.minecraft.banner.diagonal_up_left.yellow": "SuDbogh pIrmuS poS bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_up_right.black": "pIrmuS nIH bo'ghey qIj", "block.minecraft.banner.diagonal_up_right.blue": "SuDbogh pIrmuS nIH bo'ghey 'ej <PERSON>", "block.minecraft.banner.diagonal_up_right.brown": "Doqbogh pIrmuS nIH bo'ghey 'ej wovbe'bogh", "block.minecraft.banner.diagonal_up_right.cyan": "SuDqu'bogh pIrmuS nIH bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_up_right.gray": "qIjbogh pIrmuS nIH bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_up_right.green": "pIrmuS nIH bo'ghey SuDqu'", "block.minecraft.banner.diagonal_up_right.light_blue": "SuDbogh pIrmuS nIH bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_up_right.light_gray": "qIjbogh pIrmuS nIH bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_up_right.lime": "SuDq<PERSON>'bogh pIrmuS nIH bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmuS nIH bo'ghey 'ej wovbogh", "block.minecraft.banner.diagonal_up_right.orange": "Doqbogh pIrmuS nIH bo'ghey 'ej beqpuj rurbogh", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh pIrmuS nIH bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.diagonal_up_right.purple": "ghIrep Hurgh rurbogh pIrmuS nIH bo'ghey'e'", "block.minecraft.banner.diagonal_up_right.red": "pIrmuS nIH bo'ghey Doqqu'", "block.minecraft.banner.diagonal_up_right.white": "pIrmuS nIH bo'ghey chIS", "block.minecraft.banner.diagonal_up_right.yellow": "SuDbogh pIrmuS nIH bo'ghey 'ej wovqu'bogh", "block.minecraft.banner.flow.black": "<PERSON><PERSON><PERSON><PERSON>t qIj", "block.minecraft.banner.flow.blue": "SuDbogh Dem'ot '<PERSON><PERSON>", "block.minecraft.banner.flow.brown": "Doq<PERSON><PERSON> Dem'ot 'ej wovbe'bogh", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>o<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.flow.gray": "qIjbogh Dem'ot 'ej wovbogh", "block.minecraft.banner.flow.green": "Dem'ot SuDqu'", "block.minecraft.banner.flow.light_blue": "SuDbogh Dem'ot 'ej wovbogh", "block.minecraft.banner.flow.light_gray": "qIjbogh Dem'ot 'ej wovqu'bogh", "block.minecraft.banner.flow.lime": "<PERSON><PERSON><PERSON><PERSON>'bogh <PERSON>'ot 'ej wovqu'bogh", "block.minecraft.banner.flow.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>o<PERSON> 'ej w<PERSON><PERSON>gh", "block.minecraft.banner.flow.orange": "Doqbogh Dem'ot 'ej wovbogh", "block.minecraft.banner.flow.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh <PERSON>'ot 'ej wovqu'bogh", "block.minecraft.banner.flow.purple": "ghIrep Hurgh rurbogh Dem'ot", "block.minecraft.banner.flow.red": "De<PERSON>'ot <PERSON>'", "block.minecraft.banner.flow.white": "Dem'ot ch<PERSON>", "block.minecraft.banner.flow.yellow": "SuDbogh Dem'ot 'ej wovqu'bogh", "block.minecraft.banner.flower.black": "'<PERSON><PERSON><PERSON> qIj", "block.minecraft.banner.flower.blue": "SuDbogh '<PERSON><PERSON><PERSON> 'e<PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> 'ej wovbe'bogh", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON> '<PERSON><PERSON><PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.flower.gray": "qIjbogh '<PERSON><PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.flower.green": "'<PERSON><PERSON><PERSON>qu’", "block.minecraft.banner.flower.light_blue": "SuDbogh '<PERSON><PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.flower.light_gray": "qIj<PERSON><PERSON> '<PERSON><PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.flower.lime": "<PERSON><PERSON><PERSON><PERSON>'bog<PERSON> '<PERSON><PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.flower.orange": "Doqbo<PERSON> '<PERSON><PERSON><PERSON> 'ej beq<PERSON>j rurbogh", "block.minecraft.banner.flower.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.flower.purple": "ghIrep Hurgh rurbogh 'InSong <PERSON>gh'e'", "block.minecraft.banner.flower.red": "'<PERSON><PERSON><PERSON>'", "block.minecraft.banner.flower.white": "'InSong <PERSON> chIS", "block.minecraft.banner.flower.yellow": "SuD<PERSON><PERSON> '<PERSON><PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.globe.black": "yuQ qIj", "block.minecraft.banner.globe.blue": "SuDbogh yu<PERSON> '<PERSON><PERSON>", "block.minecraft.banner.globe.brown": "Doq<PERSON><PERSON> yu<PERSON> 'ej wov<PERSON>'bogh", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh yu<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.globe.gray": "qIjbogh yu<PERSON> 'ej wovbogh", "block.minecraft.banner.globe.green": "yuQ <PERSON>'", "block.minecraft.banner.globe.light_blue": "SuDbogh yu<PERSON> 'ej wovbogh", "block.minecraft.banner.globe.light_gray": "qIjbogh yuQ 'ej wovqu'bogh", "block.minecraft.banner.globe.lime": "Su<PERSON><PERSON><PERSON>'bogh yuQ 'ej wovqu'bogh", "block.minecraft.banner.globe.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh yu<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.globe.orange": "Doqbogh yu<PERSON> 'ej beqpuj rurbogh", "block.minecraft.banner.globe.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh yuQ 'ej wovqu'bogh", "block.minecraft.banner.globe.purple": "ghIrep Hurgh rurbogh yuQ'e'", "block.minecraft.banner.globe.red": "qo' Doqqu'", "block.minecraft.banner.globe.white": "yuQ ch<PERSON>", "block.minecraft.banner.globe.yellow": "SuDbogh yuQ 'ej wovqu'bogh", "block.minecraft.banner.gradient.black": "qIjbogh yor cho<PERSON>b", "block.minecraft.banner.gradient.blue": "SuDbogh yor cho<PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON><PERSON> yor cho<PERSON> <PERSON><PERSON> 'ej wov<PERSON>'bogh", "block.minecraft.banner.gradient.cyan": "Su<PERSON><PERSON><PERSON><PERSON>bogh yor cho<PERSON> <PERSON><PERSON> 'e<PERSON> wov<PERSON>gh", "block.minecraft.banner.gradient.gray": "qIjbogh yor cho<PERSON> <PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.gradient.green": "Su<PERSON><PERSON><PERSON><PERSON>bogh yor cho<PERSON>b", "block.minecraft.banner.gradient.light_blue": "SuDbogh yor choH <PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.gradient.light_gray": "qIjbogh yor cho<PERSON> <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.gradient.lime": "Su<PERSON><PERSON><PERSON>'bogh yor cho<PERSON> <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh yor cho<PERSON> <PERSON><PERSON> <PERSON>e<PERSON> wov<PERSON>gh", "block.minecraft.banner.gradient.orange": "Doqbogh yor cho<PERSON> <PERSON><PERSON> <PERSON>ej beq<PERSON>j rurbogh", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh yor cho<PERSON> <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.gradient.purple": "ghIrep Hurgh rurbogh yor choH'e' Hab", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh yor cho<PERSON>b", "block.minecraft.banner.gradient.white": "chISbogh yor choH Hab", "block.minecraft.banner.gradient.yellow": "SuDbogh yor cho<PERSON> <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.gradient_up.black": "qIjbogh pIrmuS choH Hab", "block.minecraft.banner.gradient_up.blue": "SuDbogh pIrmuS choH Hab '<PERSON>j <PERSON>", "block.minecraft.banner.gradient_up.brown": "Doqbogh pIrmuS choH Ha<PERSON> 'ej wovbe'bogh", "block.minecraft.banner.gradient_up.cyan": "SuDqu'bogh pIrmuS choH Hab 'ej wovbogh", "block.minecraft.banner.gradient_up.gray": "qIjbogh pIrmuS choH Ha<PERSON> 'ej wovbogh", "block.minecraft.banner.gradient_up.green": "SuDqu'bogh pIrmuS choH Hab", "block.minecraft.banner.gradient_up.light_blue": "SuDbogh pIrmuS choH Hab 'ej wovbogh", "block.minecraft.banner.gradient_up.light_gray": "qIjbogh pIrmuS choH <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.gradient_up.lime": "SuDq<PERSON>'bogh pIrmuS choH Hab 'ej wovqu'bogh", "block.minecraft.banner.gradient_up.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmuS choH Ha<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.gradient_up.orange": "Doqbogh pIrmuS choH Hab 'ej beqpuj rurbogh", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmuS choH Ha<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.gradient_up.purple": "ghIrep Hurgh rurbogh pIrmuS choH'e' Hab", "block.minecraft.banner.gradient_up.red": "Do<PERSON><PERSON><PERSON><PERSON>bogh pIrmuS choH Hab", "block.minecraft.banner.gradient_up.white": "chISbogh pIrmuS choH Hab", "block.minecraft.banner.gradient_up.yellow": "SuDbogh pIrmuS choH Hab 'ej wovqu'bogh", "block.minecraft.banner.guster.black": "cheqwI' qIj", "block.minecraft.banner.guster.blue": "SuDbogh cheqwI' 'e<PERSON>", "block.minecraft.banner.guster.brown": "Doqbogh cheqwI' 'ej wovbe'bogh", "block.minecraft.banner.guster.cyan": "SuDq<PERSON>'bogh cheqwI' 'ej wovbogh", "block.minecraft.banner.guster.gray": "qIjbogh cheqwI' 'ej wovbogh", "block.minecraft.banner.guster.green": "cheqwI' SuDqu'", "block.minecraft.banner.guster.light_blue": "SuDbogh cheqwI' 'ej wovbogh", "block.minecraft.banner.guster.light_gray": "qIjbogh cheqwI' 'ej wovqu'bogh", "block.minecraft.banner.guster.lime": "SuDqu'bogh cheqwI' 'ej wovqu'bogh", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON><PERSON>'bogh cheqwI' 'ej wovbogh", "block.minecraft.banner.guster.orange": "Doqbogh cheqwI' 'ej wovbogh", "block.minecraft.banner.guster.pink": "Doqq<PERSON>'bogh cheqwI' 'ej wovqu'bogh", "block.minecraft.banner.guster.purple": "ghIrep Hurgh rurbogh cheqwI'", "block.minecraft.banner.guster.red": "cheqwI' Doqqu'", "block.minecraft.banner.guster.white": "cheqwI' chIS", "block.minecraft.banner.guster.yellow": "SuDbogh cheqwI' 'ej wovqu'bogh", "block.minecraft.banner.half_horizontal.black": "yor bID qIj", "block.minecraft.banner.half_horizontal.blue": "SuDbogh yor b<PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON><PERSON><PERSON> yor b<PERSON> 'ej wovbe'bogh", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh yor b<PERSON> 'ej wovbogh", "block.minecraft.banner.half_horizontal.gray": "qIjbogh yor bID 'ej wovbogh", "block.minecraft.banner.half_horizontal.green": "yor bID SuDqu'", "block.minecraft.banner.half_horizontal.light_blue": "SuDbogh yor b<PERSON> 'ej wovbogh", "block.minecraft.banner.half_horizontal.light_gray": "qIj<PERSON><PERSON> yor b<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.half_horizontal.lime": "Su<PERSON><PERSON><PERSON>'bogh yor bID 'ej wovqu'bogh", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h yor b<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.half_horizontal.orange": "<PERSON><PERSON><PERSON><PERSON> yor b<PERSON> 'ej beqpuj rurbogh", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh yor b<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.half_horizontal.purple": "ghIrep Hurgh rurbogh yor bID'e'", "block.minecraft.banner.half_horizontal.red": "yor b<PERSON>qqu'", "block.minecraft.banner.half_horizontal.white": "yor bID chIS", "block.minecraft.banner.half_horizontal.yellow": "SuD<PERSON><PERSON> yor b<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.half_horizontal_bottom.black": "pIrmuS bID qIj", "block.minecraft.banner.half_horizontal_bottom.blue": "SuDbogh pIrmuS bID 'ej <PERSON>", "block.minecraft.banner.half_horizontal_bottom.brown": "Doqbogh pIrmuS bID 'ej wovbe'bogh", "block.minecraft.banner.half_horizontal_bottom.cyan": "SuDqu'bogh pIrmuS bID 'ej wovbogh", "block.minecraft.banner.half_horizontal_bottom.gray": "qIjbogh pIrmuS bID 'ej wovbogh", "block.minecraft.banner.half_horizontal_bottom.green": "pIrmuS bID SuDqu'", "block.minecraft.banner.half_horizontal_bottom.light_blue": "SuDbogh pIrmuS bID 'ej wovbogh", "block.minecraft.banner.half_horizontal_bottom.light_gray": "qIjbogh pIrmuS bID 'ej wovqu'bogh", "block.minecraft.banner.half_horizontal_bottom.lime": "SuDq<PERSON>'bogh pIrmuS bID 'ej wovqu'bogh", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h pIrmuS bID 'ej wovbogh", "block.minecraft.banner.half_horizontal_bottom.orange": "Doqbogh pIrmuS bID 'ej beqpuj rurbogh", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmuS bID 'ej wovqu'bogh", "block.minecraft.banner.half_horizontal_bottom.purple": "ghIrep Hurgh rurbogh pIrmuS bID'e'", "block.minecraft.banner.half_horizontal_bottom.red": "pIrmuS bID Doqqu'", "block.minecraft.banner.half_horizontal_bottom.white": "pIrmuS bID chIS", "block.minecraft.banner.half_horizontal_bottom.yellow": "SuDbogh pIrmuS bID 'ej wovqu'bogh", "block.minecraft.banner.half_vertical.black": "poS bID qIj", "block.minecraft.banner.half_vertical.blue": "SuDbogh poS bID 'ej <PERSON>", "block.minecraft.banner.half_vertical.brown": "Doqbogh poS bID 'ej wovbe'bogh", "block.minecraft.banner.half_vertical.cyan": "SuDq<PERSON>'bogh poS bID 'ej wovbogh", "block.minecraft.banner.half_vertical.gray": "qIjbogh poS bID 'ej wovbogh", "block.minecraft.banner.half_vertical.green": "poS bID SuDqu'", "block.minecraft.banner.half_vertical.light_blue": "SuDbogh poS bID 'ej wovbogh", "block.minecraft.banner.half_vertical.light_gray": "qIjbogh poS bID 'ej wovqu'bogh", "block.minecraft.banner.half_vertical.lime": "SuD<PERSON><PERSON>'bogh poS bID 'ej wovqu'bogh", "block.minecraft.banner.half_vertical.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh poS bID 'ej wovbogh", "block.minecraft.banner.half_vertical.orange": "Doqbogh poS bID 'ej beqpuj rurbogh", "block.minecraft.banner.half_vertical.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh poS bID 'ej wovqu'bogh", "block.minecraft.banner.half_vertical.purple": "ghIrep Hurgh rurbogh poS bID'e'", "block.minecraft.banner.half_vertical.red": "poS bID Doqqu'", "block.minecraft.banner.half_vertical.white": "poS bID chIS", "block.minecraft.banner.half_vertical.yellow": "SuDbogh poS bID 'ej wovqu'bogh", "block.minecraft.banner.half_vertical_right.black": "nIH bID qIj", "block.minecraft.banner.half_vertical_right.blue": "SuDbogh nIH bID 'ej <PERSON>", "block.minecraft.banner.half_vertical_right.brown": "Doqbogh nIH bID 'ej wovbe'bogh", "block.minecraft.banner.half_vertical_right.cyan": "SuDq<PERSON>'bogh nIH bID 'ej wovbogh", "block.minecraft.banner.half_vertical_right.gray": "qIjbogh nIH bID 'ej wovbogh", "block.minecraft.banner.half_vertical_right.green": "nIH bID SuDqu'", "block.minecraft.banner.half_vertical_right.light_blue": "SuDbogh nIH bID 'ej wovbogh", "block.minecraft.banner.half_vertical_right.light_gray": "qIjbogh nIH bID 'ej wovqu'bogh", "block.minecraft.banner.half_vertical_right.lime": "SuD<PERSON><PERSON>'bogh nIH bID 'ej wovqu'bogh", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh nIH bID 'ej wovbogh", "block.minecraft.banner.half_vertical_right.orange": "Doqbogh nIH bID 'ej beqpuj rurbogh", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh nIH bID 'ej wovqu'bogh", "block.minecraft.banner.half_vertical_right.purple": "ghIrep Hurgh rurbogh nIH bID'e'", "block.minecraft.banner.half_vertical_right.red": "nIH bID Doqqu'", "block.minecraft.banner.half_vertical_right.white": "nIH bID chIS", "block.minecraft.banner.half_vertical_right.yellow": "SuDbogh nIH bID 'ej wovqu'bogh", "block.minecraft.banner.mojang.black": "vIqraq qIj", "block.minecraft.banner.mojang.blue": "SuDbogh vIqraq 'ej <PERSON>", "block.minecraft.banner.mojang.brown": "Doqbogh vIqraq 'ej wov<PERSON>'bogh", "block.minecraft.banner.mojang.cyan": "Su<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>qra<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.mojang.gray": "qIjbogh vIqraq 'ej wovbogh", "block.minecraft.banner.mojang.green": "vIqraq SuDqu'", "block.minecraft.banner.mojang.light_blue": "SuDbogh vIqraq 'ej wovbogh", "block.minecraft.banner.mojang.light_gray": "qIjbogh vIqra<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.mojang.lime": "Su<PERSON><PERSON><PERSON><PERSON>bogh v<PERSON>q<PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON> <PERSON>e<PERSON> w<PERSON><PERSON>gh", "block.minecraft.banner.mojang.orange": "Doqbogh vIqraq 'ej beqpuj rurbogh", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh v<PERSON><PERSON><PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.mojang.purple": "ghIrep Hurgh rurbogh vIqraq'e'", "block.minecraft.banner.mojang.red": "<PERSON><PERSON>", "block.minecraft.banner.mojang.white": "vIqraq chIS", "block.minecraft.banner.mojang.yellow": "SuDbogh vIqraq 'ej wovq<PERSON>'bogh", "block.minecraft.banner.piglin.black": "ghIch qIj", "block.minecraft.banner.piglin.blue": "SuDbogh ghIch 'ej <PERSON>", "block.minecraft.banner.piglin.brown": "Doqbogh ghIch 'ej wovbe'bogh", "block.minecraft.banner.piglin.cyan": "Su<PERSON><PERSON><PERSON><PERSON>b<PERSON>h gh<PERSON><PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.piglin.gray": "qIjbogh ghIch 'ej wovbogh", "block.minecraft.banner.piglin.green": "ghIch SuDqu'", "block.minecraft.banner.piglin.light_blue": "SuDbogh ghIch 'ej wovbogh", "block.minecraft.banner.piglin.light_gray": "qIjbogh ghIch 'ej wovqu'bogh", "block.minecraft.banner.piglin.lime": "Su<PERSON><PERSON><PERSON>'bogh gh<PERSON>ch 'ej wovqu'bogh", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h gh<PERSON><PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.piglin.orange": "Doqbogh ghIch 'ej beqpuj rurbogh", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh gh<PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.piglin.purple": "ghIrep Hurgh rurbogh ghIch'e'", "block.minecraft.banner.piglin.red": "ghIch Doqqu'", "block.minecraft.banner.piglin.white": "ghIch chIS", "block.minecraft.banner.piglin.yellow": "SuDbogh ghIch 'ej wovqu'bogh", "block.minecraft.banner.rhombus.black": "botlh meyrI' qIj", "block.minecraft.banner.rhombus.blue": "SuDbogh botlh meyrI' 'e<PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON><PERSON><PERSON> botlh meyrI' 'ej wovbe'bogh", "block.minecraft.banner.rhombus.cyan": "SuD<PERSON><PERSON>'bogh botlh meyrI' 'ej wovbogh", "block.minecraft.banner.rhombus.gray": "qIjbogh botlh meyrI' 'ej wovbogh", "block.minecraft.banner.rhombus.green": "botlh meyrI' SuDqu'", "block.minecraft.banner.rhombus.light_blue": "SuDbogh botlh meyrI' 'ej wovbogh", "block.minecraft.banner.rhombus.light_gray": "qIj<PERSON>gh botlh meyrI' 'ej wovqu'bogh", "block.minecraft.banner.rhombus.lime": "SuDqu'bogh botlh meyrI' 'ej wovqu'bogh", "block.minecraft.banner.rhombus.magenta": "<PERSON><PERSON><PERSON><PERSON>'bogh botlh meyrI' 'ej wovbogh", "block.minecraft.banner.rhombus.orange": "Do<PERSON><PERSON><PERSON> botlh meyrI' 'ej beqpuj rurbogh", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh botlh meyrI' 'ej wovqu'bogh", "block.minecraft.banner.rhombus.purple": "ghIrep Hurgh rurbogh botlh meyrI''e'", "block.minecraft.banner.rhombus.red": "botlh Hov <PERSON>qqu'", "block.minecraft.banner.rhombus.white": "botlh meyrI' chIS", "block.minecraft.banner.rhombus.yellow": "SuD<PERSON>gh botlh meyrI' 'ej wovqu'bogh", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> qIj", "block.minecraft.banner.skull.blue": "SuDbogh DughrI' De<PERSON> '<PERSON><PERSON>", "block.minecraft.banner.skull.brown": "Doqbogh DughrI' De<PERSON> 'ej wovbe'bogh", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>' <PERSON><PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.skull.gray": "qIjbogh DughrI' De<PERSON> 'ej wovbogh", "block.minecraft.banner.skull.green": "Dughr<PERSON>' <PERSON><PERSON>'", "block.minecraft.banner.skull.light_blue": "SuDbogh DughrI' De<PERSON> 'ej wovbogh", "block.minecraft.banner.skull.light_gray": "qIjbogh DughrI' De<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.skull.lime": "<PERSON><PERSON><PERSON><PERSON>'bogh <PERSON><PERSON>' <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.skull.orange": "Doqbogh DughrI' Degh 'ej beqpuj rurbogh", "block.minecraft.banner.skull.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h <PERSON><PERSON>' <PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.skull.purple": "ghIrep Hurgh rurbogh DughrI' Degh'e'", "block.minecraft.banner.skull.red": "Dughr<PERSON>' <PERSON><PERSON>'", "block.minecraft.banner.skull.white": "DughrI' Degh chIS", "block.minecraft.banner.skull.yellow": "SuDbogh DughrI' De<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.small_stripes.black": "qIjbogh weS<PERSON>ch way", "block.minecraft.banner.small_stripes.blue": "SuDbogh weS<PERSON>ch way 'ej <PERSON>", "block.minecraft.banner.small_stripes.brown": "<PERSON><PERSON><PERSON><PERSON> we<PERSON><PERSON><PERSON> way 'ej wovbe'bogh", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON><PERSON>'bogh we<PERSON><PERSON>ch way 'ej wovbogh", "block.minecraft.banner.small_stripes.gray": "qIjbogh weS<PERSON>ch way 'ej wovbogh", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh we<PERSON><PERSON><PERSON> way", "block.minecraft.banner.small_stripes.light_blue": "SuDbogh weS<PERSON>ch way 'ej wovbogh", "block.minecraft.banner.small_stripes.light_gray": "qIj<PERSON>gh weS<PERSON>ch way 'ej wovqu'bogh", "block.minecraft.banner.small_stripes.lime": "Su<PERSON><PERSON><PERSON>'bogh we<PERSON><PERSON>ch way 'ej wovqu'bogh", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh we<PERSON><PERSON><PERSON> way 'ej wov<PERSON>gh", "block.minecraft.banner.small_stripes.orange": "Doqbogh weS<PERSON>ch way 'ej beqpuj rurbogh", "block.minecraft.banner.small_stripes.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh we<PERSON><PERSON>ch way 'ej wovqu'bogh", "block.minecraft.banner.small_stripes.purple": "ghIrep Hurgh rurbogh weSjech'e' way", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h we<PERSON><PERSON><PERSON> way", "block.minecraft.banner.small_stripes.white": "chISbogh weSjech way", "block.minecraft.banner.small_stripes.yellow": "SuD<PERSON>gh weS<PERSON>ch way 'ej wovqu'bogh", "block.minecraft.banner.square_bottom_left.black": "pIrmuS poS po'oH qIj", "block.minecraft.banner.square_bottom_left.blue": "SuDbogh pIrmuS poS po'oH 'ej <PERSON>", "block.minecraft.banner.square_bottom_left.brown": "Doqbogh pIrmuS poS po'oH 'ej wovbe'bogh", "block.minecraft.banner.square_bottom_left.cyan": "SuDqu'bogh pIrmuS poS po'oH 'ej wovbogh", "block.minecraft.banner.square_bottom_left.gray": "qIjbogh pIrmuS poS po'oH 'ej wovbogh", "block.minecraft.banner.square_bottom_left.green": "pIrmuS poS po'oH SuDqu'", "block.minecraft.banner.square_bottom_left.light_blue": "SuDbogh pIrmuS poS po'oH 'ej wovbogh", "block.minecraft.banner.square_bottom_left.light_gray": "qIjbogh pIrmuS poS po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_bottom_left.lime": "SuDqu'bogh pIrmuS poS po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_bottom_left.magenta": "Doqq<PERSON>'bogh pIrmuS poS po'oH 'ej wovbogh", "block.minecraft.banner.square_bottom_left.orange": "Doqbogh pIrmuS poS po'oH 'ej beqpuj rurbogh", "block.minecraft.banner.square_bottom_left.pink": "Doqq<PERSON>'bogh pIrmuS poS po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_bottom_left.purple": "ghIrep Hurgh rurbogh pIrmuS poS po'oH'e'", "block.minecraft.banner.square_bottom_left.red": "pIrmuS poS po'oH Doqqu'", "block.minecraft.banner.square_bottom_left.white": "pIrmuS poS po'oH chIS", "block.minecraft.banner.square_bottom_left.yellow": "SuDbogh pIrmuS poS po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_bottom_right.black": "pIrmuS nIH po'oH qIj", "block.minecraft.banner.square_bottom_right.blue": "SuDbogh pIrmuS nIH po'oH 'ej <PERSON>", "block.minecraft.banner.square_bottom_right.brown": "Doqbogh pIrmuS nIH po'oH 'ej wovbe'bogh", "block.minecraft.banner.square_bottom_right.cyan": "SuDqu'bogh pIrmuS nIH po'oH 'ej wovbogh", "block.minecraft.banner.square_bottom_right.gray": "qIjbogh pIrmuS nIH po'oH 'ej wovbogh", "block.minecraft.banner.square_bottom_right.green": "pIrmuS nIH po'oH SuDqu'", "block.minecraft.banner.square_bottom_right.light_blue": "SuDbogh pIrmuS nIH po'oH 'ej wovbogh", "block.minecraft.banner.square_bottom_right.light_gray": "qIjbogh pIrmuS nIH po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_bottom_right.lime": "SuDqu'bogh pIrmuS nIH po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_bottom_right.magenta": "Doqq<PERSON>'bogh pIrmuS nIH po'oH 'ej wovbogh", "block.minecraft.banner.square_bottom_right.orange": "Doqbogh pIrmuS nIH po'oH 'ej beqpuj rurbogh", "block.minecraft.banner.square_bottom_right.pink": "Doq<PERSON><PERSON>'bogh pIrmuS nIH po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_bottom_right.purple": "ghIrep Hurgh rurbogh pIrmuS nIH po'oH'e'", "block.minecraft.banner.square_bottom_right.red": "pIrmuS nIH po'oH Doqqu'", "block.minecraft.banner.square_bottom_right.white": "pIrmuS nIH po'oH chIS", "block.minecraft.banner.square_bottom_right.yellow": "SuDbogh pIrmuS nIH po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_top_left.black": "yor poS po'oH qIj", "block.minecraft.banner.square_top_left.blue": "SuDbogh yor poS po'o<PERSON> 'e<PERSON>", "block.minecraft.banner.square_top_left.brown": "Doqbogh yor poS po'oH 'ej wovbe'bogh", "block.minecraft.banner.square_top_left.cyan": "SuDq<PERSON>'bogh yor poS po'oH 'ej wovbogh", "block.minecraft.banner.square_top_left.gray": "qIjbogh yor poS po'oH 'ej wovbogh", "block.minecraft.banner.square_top_left.green": "yor poS po'oH SuDqu'", "block.minecraft.banner.square_top_left.light_blue": "SuDbogh yor poS po'oH 'ej wovbogh", "block.minecraft.banner.square_top_left.light_gray": "qIjbogh yor poS po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_top_left.lime": "SuDq<PERSON>'bogh yor poS po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_top_left.magenta": "<PERSON><PERSON><PERSON><PERSON>'bogh yor poS po'oH 'ej wovbogh", "block.minecraft.banner.square_top_left.orange": "Doqbogh yor poS po'oH 'ej beqpuj rurbogh", "block.minecraft.banner.square_top_left.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh yor poS po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_top_left.purple": "ghIrep Hurgh rurbogh yor poS po'oH'e'", "block.minecraft.banner.square_top_left.red": "yor poS po'oH Doqqu'", "block.minecraft.banner.square_top_left.white": "yor poS po'oH chIS", "block.minecraft.banner.square_top_left.yellow": "SuDbogh yor poS po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_top_right.black": "yor nIH po'oH qIj", "block.minecraft.banner.square_top_right.blue": "SuDbogh yor nIH po'oH 'e<PERSON>", "block.minecraft.banner.square_top_right.brown": "Doqbogh yor nIH po'oH 'ej wovbe'bogh", "block.minecraft.banner.square_top_right.cyan": "SuD<PERSON><PERSON>'bogh yor nIH po'oH 'ej wovbogh", "block.minecraft.banner.square_top_right.gray": "qIjbogh yor nIH po'oH 'ej wovbogh", "block.minecraft.banner.square_top_right.green": "yor nIH po'oH SuDqu'", "block.minecraft.banner.square_top_right.light_blue": "SuDbogh yor nIH po'oH 'ej wovbogh", "block.minecraft.banner.square_top_right.light_gray": "qIjbogh yor nIH po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_top_right.lime": "SuDq<PERSON>'bogh yor nIH po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_top_right.magenta": "<PERSON><PERSON><PERSON><PERSON>'bogh yor nIH po'oH 'ej wovbogh", "block.minecraft.banner.square_top_right.orange": "Doqbogh yor nIH po'oH 'ej beqpuj rurbogh", "block.minecraft.banner.square_top_right.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh yor nIH po'oH 'ej wovqu'bogh", "block.minecraft.banner.square_top_right.purple": "ghIrep Hurgh rurbogh yor nIH po'oH'e'", "block.minecraft.banner.square_top_right.red": "yor nIH po'oH Doqqu'", "block.minecraft.banner.square_top_right.white": "yor nIH po'oH chIS", "block.minecraft.banner.square_top_right.yellow": "SuDbogh yor nIH po'oH 'ej wovqu'bogh", "block.minecraft.banner.straight_cross.black": "me'cheD qIj", "block.minecraft.banner.straight_cross.blue": "SuDbogh me'cheD 'ej <PERSON>", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON><PERSON><PERSON> me'cheD 'ej wovbe'bogh", "block.minecraft.banner.straight_cross.cyan": "Su<PERSON><PERSON><PERSON>'bogh me'cheD 'ej wovbogh", "block.minecraft.banner.straight_cross.gray": "qIjbogh me'cheD 'ej wovbogh", "block.minecraft.banner.straight_cross.green": "me'cheD SuDqu'", "block.minecraft.banner.straight_cross.light_blue": "SuD<PERSON>gh me'cheD 'ej wovbogh", "block.minecraft.banner.straight_cross.light_gray": "qIjbogh me'cheD 'ej wovqu'bogh", "block.minecraft.banner.straight_cross.lime": "SuDq<PERSON>'bogh me'cheD 'ej wovqu'bogh", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON><PERSON>'bogh me'che<PERSON> 'ej wovbogh", "block.minecraft.banner.straight_cross.orange": "Doq<PERSON><PERSON> me'cheD 'ej beqpuj rurbogh", "block.minecraft.banner.straight_cross.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh me'cheD 'ej wovqu'bogh", "block.minecraft.banner.straight_cross.purple": "ghIrep Hurgh rurbogh me'cheD'e'", "block.minecraft.banner.straight_cross.red": "me'cheD <PERSON>'", "block.minecraft.banner.straight_cross.white": "me'cheD chIS", "block.minecraft.banner.straight_cross.yellow": "SuD<PERSON>gh me'cheD 'ej wovqu'bogh", "block.minecraft.banner.stripe_bottom.black": "pIrmuS 'ejvoH qIj", "block.minecraft.banner.stripe_bottom.blue": "SuDbogh pIrmuS 'ejvoH 'ej <PERSON>", "block.minecraft.banner.stripe_bottom.brown": "Doqbogh pIrmuS 'ejvoH 'ej wovbe'bogh", "block.minecraft.banner.stripe_bottom.cyan": "SuDq<PERSON>'bogh pIrmuS 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_bottom.gray": "qIjbogh pIrmuS 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_bottom.green": "pIrmuS 'ejvoH SuDqu'", "block.minecraft.banner.stripe_bottom.light_blue": "SuDbogh pIrmuS 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_bottom.light_gray": "qIjbogh pIrmuS 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_bottom.lime": "SuD<PERSON><PERSON>'bogh pIrmuS 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmuS 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_bottom.orange": "Doqbogh pIrmuS 'ejvoH 'ej beqpuj rurbogh", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmuS 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_bottom.purple": "ghIrep Hurgh rurbogh pIrmuS 'ejvoH'e'", "block.minecraft.banner.stripe_bottom.red": "pIrmuS 'ejvoH Doqqu'", "block.minecraft.banner.stripe_bottom.white": "pIrmuS 'ejvoH chIS", "block.minecraft.banner.stripe_bottom.yellow": "SuDbogh pIrmuS 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_center.black": "bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong qIj", "block.minecraft.banner.stripe_center.blue": "SuDbogh botlh '<PERSON><PERSON><PERSON><PERSON><PERSON> 'e<PERSON>", "block.minecraft.banner.stripe_center.brown": "<PERSON><PERSON><PERSON><PERSON> bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej wovbe'bogh", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej wov<PERSON>gh", "block.minecraft.banner.stripe_center.gray": "qIjbogh bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej wovbogh", "block.minecraft.banner.stripe_center.green": "botlh 'e<PERSON><PERSON><PERSON> chong SuDqu'", "block.minecraft.banner.stripe_center.light_blue": "SuDbogh bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej wovbogh", "block.minecraft.banner.stripe_center.light_gray": "qIj<PERSON><PERSON> bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej wovqu'bogh", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON><PERSON><PERSON>'bogh bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej wovqu'bogh", "block.minecraft.banner.stripe_center.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej wov<PERSON>gh", "block.minecraft.banner.stripe_center.orange": "Doq<PERSON><PERSON> bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej beq<PERSON>j rurbogh", "block.minecraft.banner.stripe_center.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej wovqu'bogh", "block.minecraft.banner.stripe_center.purple": "ghIrep Hurgh rurbogh botlh 'ejvo<PERSON>'e' chong", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h bot<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> chong", "block.minecraft.banner.stripe_center.white": "chISbogh bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong", "block.minecraft.banner.stripe_center.yellow": "Su<PERSON><PERSON><PERSON> bot<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> chong 'ej wovqu'bogh", "block.minecraft.banner.stripe_downleft.black": "poS bo'ghey 'ejvoH qIj", "block.minecraft.banner.stripe_downleft.blue": "SuDbogh poS bo'ghey 'ejvoH 'ej <PERSON>", "block.minecraft.banner.stripe_downleft.brown": "Doqbogh poS bo'ghey 'ej<PERSON><PERSON> 'ej wovbe'bogh", "block.minecraft.banner.stripe_downleft.cyan": "Su<PERSON><PERSON><PERSON>'bogh poS bo'ghey 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_downleft.gray": "qIjbogh poS bo'ghey 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_downleft.green": "poS bo'ghey 'ejvoH SuDqu'", "block.minecraft.banner.stripe_downleft.light_blue": "SuDbogh poS bo'ghey 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_downleft.light_gray": "qIjbogh poS bo'ghey 'ejvo<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.stripe_downleft.lime": "SuD<PERSON><PERSON>'bogh poS bo'ghey 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_downleft.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh poS bo'ghey 'ejvo<PERSON> 'ej wovbogh", "block.minecraft.banner.stripe_downleft.orange": "Doqbogh poS bo'ghey 'ejvoH 'ej beqpuj rurbogh", "block.minecraft.banner.stripe_downleft.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh poS bo'ghey 'ejvo<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.stripe_downleft.purple": "ghIrep Hurgh rurbogh poS bo'ghey 'ejvoH'e'", "block.minecraft.banner.stripe_downleft.red": "poS bo'ghey 'ejvoH Doqqu'", "block.minecraft.banner.stripe_downleft.white": "poS bo'ghey 'ejvoH chIS", "block.minecraft.banner.stripe_downleft.yellow": "SuDbogh poS bo'ghey 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_downright.black": "nIH bo'ghey 'ejvoH qIj", "block.minecraft.banner.stripe_downright.blue": "SuDbogh nIH bo'ghey 'ejvoH 'ej <PERSON>", "block.minecraft.banner.stripe_downright.brown": "Doqbogh nIH bo'ghey 'ejvo<PERSON> 'ej wovbe'bogh", "block.minecraft.banner.stripe_downright.cyan": "Su<PERSON><PERSON><PERSON>'bogh nIH bo'ghey 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_downright.gray": "qIjbogh nIH bo'ghey 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_downright.green": "nIH bo'ghey 'ejvoH SuDqu'", "block.minecraft.banner.stripe_downright.light_blue": "SuDbogh nIH bo'ghey 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_downright.light_gray": "qIjbogh nIH bo'ghey 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_downright.lime": "SuD<PERSON><PERSON>'bogh nIH bo'ghey 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_downright.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh nIH bo'ghey 'ejvo<PERSON> 'ej wovbogh", "block.minecraft.banner.stripe_downright.orange": "Doqbogh nIH bo'ghey 'ejvoH 'ej beqpuj rurbogh", "block.minecraft.banner.stripe_downright.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh nIH bo'ghey 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_downright.purple": "ghIrep Hurgh rurbogh nIH bo'ghey 'ejvoH'e'", "block.minecraft.banner.stripe_downright.red": "nIH bo'ghey 'ejvoH Doqqu'", "block.minecraft.banner.stripe_downright.white": "nIH bo'ghey 'ejvoH chIS", "block.minecraft.banner.stripe_downright.yellow": "SuDbogh nIH bo'ghey 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_left.black": "poS 'ejvoH qIj", "block.minecraft.banner.stripe_left.blue": "SuDbogh poS 'ejvoH 'ej <PERSON>", "block.minecraft.banner.stripe_left.brown": "Doqbogh poS 'ejvoH 'ej wovbe'bogh", "block.minecraft.banner.stripe_left.cyan": "SuD<PERSON><PERSON>'bogh poS 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_left.gray": "qIjbogh poS 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_left.green": "poS 'ejvoH SuDqu'", "block.minecraft.banner.stripe_left.light_blue": "SuDbogh poS 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_left.light_gray": "qIjbogh poS 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_left.lime": "SuD<PERSON><PERSON>'bogh poS 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh po<PERSON> 'ejvo<PERSON> 'ej wovbogh", "block.minecraft.banner.stripe_left.orange": "Doqbogh poS 'ejvoH 'ej beqpuj rurbogh", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh poS 'ejvo<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.stripe_left.purple": "ghIrep Hurgh rurbogh poS 'ejvoH'e'", "block.minecraft.banner.stripe_left.red": "poS 'ejvoH Doqqu'", "block.minecraft.banner.stripe_left.white": "poS 'ejvoH chIS", "block.minecraft.banner.stripe_left.yellow": "SuDbogh poS 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_middle.black": "qIjbogh botlh 'ejvoH SaS", "block.minecraft.banner.stripe_middle.blue": "SuDbogh botlh 'ejvoH SaS 'ej <PERSON>gh", "block.minecraft.banner.stripe_middle.brown": "Do<PERSON><PERSON><PERSON> botlh 'ejvoH SaS 'ej wovbe'bogh", "block.minecraft.banner.stripe_middle.cyan": "Su<PERSON><PERSON><PERSON><PERSON>b<PERSON>h bot<PERSON>h 'ejvoH SaS 'ej wovbogh", "block.minecraft.banner.stripe_middle.gray": "qIjbogh botlh 'ejvoH SaS 'ej wovbogh", "block.minecraft.banner.stripe_middle.green": "Su<PERSON><PERSON><PERSON><PERSON>b<PERSON>h botlh 'ejvoH SaS", "block.minecraft.banner.stripe_middle.light_blue": "SuDbogh botlh 'ejvoH SaS 'ej wovbogh", "block.minecraft.banner.stripe_middle.light_gray": "qIjbogh botlh 'ejvoH SaS 'ej wovqu'bogh", "block.minecraft.banner.stripe_middle.lime": "Su<PERSON><PERSON><PERSON>'bogh bot<PERSON>h 'ejvoH SaS 'ej wovqu'bogh", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h bot<PERSON><PERSON> 'ejvoH SaS 'ej wovbogh", "block.minecraft.banner.stripe_middle.orange": "Doqbogh botlh 'ejvoH SaS 'ej beqpuj rurbogh", "block.minecraft.banner.stripe_middle.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh bot<PERSON><PERSON> 'ejvoH SaS 'ej wovqu'bogh", "block.minecraft.banner.stripe_middle.purple": "ghIrep Hurgh rurbogh botlh 'ejvoH'e' SaS", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h bot<PERSON>h 'ejvoH SaS", "block.minecraft.banner.stripe_middle.white": "chISbogh botlh 'ejvoH SaS", "block.minecraft.banner.stripe_middle.yellow": "SuDbogh botlh 'ejvoH SaS 'ej wovqu'bogh", "block.minecraft.banner.stripe_right.black": "nIH 'ejvoH qIj", "block.minecraft.banner.stripe_right.blue": "SuDbogh nIH 'ejvoH 'ej <PERSON>", "block.minecraft.banner.stripe_right.brown": "Doqbogh nIH 'ejvoH 'ej wovbe'bogh", "block.minecraft.banner.stripe_right.cyan": "SuD<PERSON><PERSON>'bogh nIH 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_right.gray": "qIjbogh nIH 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_right.green": "nIH 'ejvoH SuDqu'", "block.minecraft.banner.stripe_right.light_blue": "SuDbogh nIH 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_right.light_gray": "qIjbogh nIH 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_right.lime": "SuD<PERSON><PERSON>'bogh nIH 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh nIH 'ejvoH 'ej wovbogh", "block.minecraft.banner.stripe_right.orange": "Doqbogh nIH 'ejvoH 'ej beqpuj rurbogh", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh nIH 'ejvo<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.stripe_right.purple": "ghIrep Hurgh rurbogh nIH 'ejvoH'e'", "block.minecraft.banner.stripe_right.red": "nIH 'ejvoH Doqqu'", "block.minecraft.banner.stripe_right.white": "nIH 'ejvoH chIS", "block.minecraft.banner.stripe_right.yellow": "SuDbogh nIH 'ejvoH 'ej wovqu'bogh", "block.minecraft.banner.stripe_top.black": "yo<PERSON> '<PERSON>j<PERSON>H qIj", "block.minecraft.banner.stripe_top.blue": "SuDbogh yor 'e<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.brown": "<PERSON><PERSON><PERSON><PERSON> yor 'e<PERSON><PERSON><PERSON> 'ej wovbe'bogh", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON><PERSON>'bogh yor 'ej<PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.stripe_top.gray": "qIjbogh yor 'ej<PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.stripe_top.green": "SuDq<PERSON>' <PERSON><PERSON> 'ejvoH", "block.minecraft.banner.stripe_top.light_blue": "SuDbogh yor 'ej<PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.stripe_top.light_gray": "qIj<PERSON><PERSON> yor 'ej<PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.stripe_top.lime": "Su<PERSON><PERSON><PERSON>'bogh yor 'ej<PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h yor 'e<PERSON><PERSON><PERSON> <PERSON>ej wov<PERSON>gh", "block.minecraft.banner.stripe_top.orange": "Doqbogh yor 'e<PERSON><PERSON><PERSON> 'ej beqpuj rurbogh", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh yor 'ej<PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.stripe_top.purple": "ghIrep Hurgh rurbogh yor 'ejvoH'e'", "block.minecraft.banner.stripe_top.red": "yor '<PERSON><PERSON><PERSON><PERSON> Doqqu'", "block.minecraft.banner.stripe_top.white": "yor 'ejvoH chIS", "block.minecraft.banner.stripe_top.yellow": "SuD<PERSON>gh yor 'ej<PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.banner.triangle_bottom.black": "pIrmuS ra'Duch qIj", "block.minecraft.banner.triangle_bottom.blue": "SuDbogh pIrmuS ra'Duch 'ej <PERSON>", "block.minecraft.banner.triangle_bottom.brown": "Doqbogh pIrmuS ra'Du<PERSON> 'ej wovbe'bogh", "block.minecraft.banner.triangle_bottom.cyan": "SuDq<PERSON>'bogh pIrmuS ra'Duch 'ej wovbogh", "block.minecraft.banner.triangle_bottom.gray": "qIjbogh pIrmuS ra'Duch 'ej wovbogh", "block.minecraft.banner.triangle_bottom.green": "pIrmuS ra'Duch SuDqu'", "block.minecraft.banner.triangle_bottom.light_blue": "SuDbogh pIrmuS ra'Duch 'ej wovbogh", "block.minecraft.banner.triangle_bottom.light_gray": "qIjbogh pIrmuS ra'Duch 'ej wovqu'bogh", "block.minecraft.banner.triangle_bottom.lime": "SuD<PERSON><PERSON>'bogh pIrmuS ra'Duch 'ej wovqu'bogh", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h pIrmuS ra'Du<PERSON> 'ej wovbogh", "block.minecraft.banner.triangle_bottom.orange": "Doqbogh pIrmuS ra'Duch 'ej beqpuj rurbogh", "block.minecraft.banner.triangle_bottom.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmuS ra'Duch 'ej wovqu'bogh", "block.minecraft.banner.triangle_bottom.purple": "ghIrep Hurgh rurbogh pIrmuS ra'Duch'e'", "block.minecraft.banner.triangle_bottom.red": "pIrmuS ra'<PERSON><PERSON>'", "block.minecraft.banner.triangle_bottom.white": "pIrmuS ra'Duch chIS", "block.minecraft.banner.triangle_bottom.yellow": "SuDbogh pIrmuS ra'Duch 'ej wovqu'bogh", "block.minecraft.banner.triangle_top.black": "yor r<PERSON><PERSON><PERSON><PERSON> qIj", "block.minecraft.banner.triangle_top.blue": "SuDbogh yor ra'<PERSON><PERSON> '<PERSON><PERSON>", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON><PERSON><PERSON> yor ra'<PERSON><PERSON> 'ej wovbe'bogh", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON><PERSON><PERSON>'bogh yor ra'<PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.triangle_top.gray": "qIjbogh yor ra'<PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.triangle_top.green": "yor ra'<PERSON>ch <PERSON>'", "block.minecraft.banner.triangle_top.light_blue": "SuDbogh yor ra'<PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.triangle_top.light_gray": "qIjbogh yor ra'Du<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.triangle_top.lime": "Su<PERSON><PERSON><PERSON>'bogh yor ra'Du<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.triangle_top.magenta": "<PERSON><PERSON><PERSON><PERSON>'bogh yor ra'<PERSON><PERSON> 'ej wovbogh", "block.minecraft.banner.triangle_top.orange": "Doqbo<PERSON> yor ra'<PERSON><PERSON> 'ej beqpuj rurbogh", "block.minecraft.banner.triangle_top.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh yor ra'Du<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.triangle_top.purple": "ghIrep Hurgh rurbogh yor ra'Duch'e'", "block.minecraft.banner.triangle_top.red": "yor ra'<PERSON><PERSON>'", "block.minecraft.banner.triangle_top.white": "yor ra'<PERSON>ch chIS", "block.minecraft.banner.triangle_top.yellow": "SuD<PERSON>gh yor ra'Du<PERSON> 'ej wovqu'bogh", "block.minecraft.banner.triangles_bottom.black": "pIrmuS Ho<PERSON>Du' qIj", "block.minecraft.banner.triangles_bottom.blue": "SuDbogh pIrmuS Ho<PERSON>Du' '<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.brown": "Doqbogh pIrmuS Ho'<PERSON>' 'ej wovbe'bogh", "block.minecraft.banner.triangles_bottom.cyan": "SuD<PERSON><PERSON>'bogh pIrmuS <PERSON> 'ej wovbogh", "block.minecraft.banner.triangles_bottom.gray": "qIjbogh pIrmuS <PERSON>' 'ej wovbogh", "block.minecraft.banner.triangles_bottom.green": "pIrmuS Ho<PERSON>Du' SuDqu'", "block.minecraft.banner.triangles_bottom.light_blue": "SuDbogh pIrmuS Ho'Du' 'ej wovbogh", "block.minecraft.banner.triangles_bottom.light_gray": "qIjbogh pIrmuS <PERSON>' 'ej wovqu'bogh", "block.minecraft.banner.triangles_bottom.lime": "SuD<PERSON><PERSON>'bogh pIrmuS <PERSON> 'ej wovqu'bogh", "block.minecraft.banner.triangles_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh pIrmu<PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.triangles_bottom.orange": "Doqbogh pIrmuS Ho<PERSON>Du' 'ej beqpuj rurbogh", "block.minecraft.banner.triangles_bottom.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh pIrmu<PERSON> <PERSON> 'ej wovqu'bogh", "block.minecraft.banner.triangles_bottom.purple": "ghIrep Hurgh rurbogh pIrmuS Ho'Du''e'", "block.minecraft.banner.triangles_bottom.red": "pIrmuS Ho<PERSON> Doqqu'", "block.minecraft.banner.triangles_bottom.white": "pIrmuS Ho'Du' chIS", "block.minecraft.banner.triangles_bottom.yellow": "SuDbogh pIrmuS <PERSON>' 'ej wovqu'bogh", "block.minecraft.banner.triangles_top.black": "yor <PERSON> qIj", "block.minecraft.banner.triangles_top.blue": "SuDbogh yor <PERSON> '<PERSON><PERSON>", "block.minecraft.banner.triangles_top.brown": "<PERSON><PERSON><PERSON><PERSON> yor <PERSON>'<PERSON>' 'ej wovbe'bogh", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON><PERSON><PERSON>'bogh yor <PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.triangles_top.gray": "qIjbogh yor <PERSON><PERSON>' 'ej wovbogh", "block.minecraft.banner.triangles_top.green": "yor <PERSON> SuDqu'", "block.minecraft.banner.triangles_top.light_blue": "SuDbogh yor <PERSON>' 'ej wovbogh", "block.minecraft.banner.triangles_top.light_gray": "qIj<PERSON><PERSON> yor <PERSON>' 'ej wovqu'bogh", "block.minecraft.banner.triangles_top.lime": "SuD<PERSON><PERSON>'bogh yor <PERSON> 'ej wovqu'bogh", "block.minecraft.banner.triangles_top.magenta": "<PERSON><PERSON><PERSON><PERSON>'bogh yor <PERSON> 'ej wov<PERSON>gh", "block.minecraft.banner.triangles_top.orange": "<PERSON><PERSON><PERSON><PERSON> yor <PERSON>' 'ej beqpuj rurbogh", "block.minecraft.banner.triangles_top.pink": "<PERSON><PERSON><PERSON><PERSON>'bogh yor <PERSON> 'ej wovqu'bogh", "block.minecraft.banner.triangles_top.purple": "ghIrep Hurgh rurbogh yor Ho'Du''e'", "block.minecraft.banner.triangles_top.red": "yor <PERSON>'", "block.minecraft.banner.triangles_top.white": "yor <PERSON>", "block.minecraft.banner.triangles_top.yellow": "Su<PERSON><PERSON><PERSON> yor <PERSON>' 'ej wovqu'bogh", "block.minecraft.barrel": "qegh", "block.minecraft.barrier": "botw<PERSON>' bu<PERSON>'<PERSON>r", "block.minecraft.basalt": "Hotnagh", "block.minecraft.beacon": "rI'meH wovmoHwI'", "block.minecraft.beacon.primary": "HoS'a'", "block.minecraft.beacon.secondary": "HoS cha'DIch", "block.minecraft.bed.no_sleep": "bIQongmeH qaSnIStaH ram qoj jevnIStaH muD", "block.minecraft.bed.not_safe": "bIQonglaHbe'; SoH Sum tl<PERSON>gh", "block.minecraft.bed.obstructed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wa<PERSON>'", "block.minecraft.bed.occupied": "buy' QongDaqvam", "block.minecraft.bed.too_far_away": "bIQonglaHbe'; tlhoy Hop QongDaq", "block.minecraft.bedrock": "ghor<PERSON>'bogh nagh", "block.minecraft.bee_nest": "lachvoH but", "block.minecraft.beehive": "lachvoH", "block.minecraft.beetroots": "'<PERSON><PERSON><PERSON><PERSON><PERSON>'", "block.minecraft.bell": "'In", "block.minecraft.big_dripleaf": "por tom tIn", "block.minecraft.big_dripleaf_stem": "por tom <PERSON>", "block.minecraft.birch_button": "berIch Sor leQ", "block.minecraft.birch_door": "berIch Sor lojmIt", "block.minecraft.birch_fence": "ber<PERSON><PERSON> So<PERSON>", "block.minecraft.birch_fence_gate": "berIch Sor <PERSON> lojmIt", "block.minecraft.birch_hanging_sign": "berIch Sor QIn 'echlet tlhep", "block.minecraft.birch_leaves": "ber<PERSON><PERSON>ey", "block.minecraft.birch_log": "ber<PERSON><PERSON>'a'", "block.minecraft.birch_planks": "ber<PERSON><PERSON> Sor '<PERSON>", "block.minecraft.birch_pressure_plate": "ber<PERSON>ch Sor Surmen 'echlet", "block.minecraft.birch_sapling": "berIch SorHom", "block.minecraft.birch_sign": "berIch Sor QIn 'echlet", "block.minecraft.birch_slab": "ber<PERSON>ch Sor bu<PERSON>'<PERSON>r bID", "block.minecraft.birch_stairs": "ber<PERSON>ch Sor letlh", "block.minecraft.birch_trapdoor": "ber<PERSON>ch Sor vonmeH lojmIt", "block.minecraft.birch_wall_hanging_sign": "berIch Sor tlhoy' QIn 'echlet tlhep", "block.minecraft.birch_wall_sign": "berIch Sor tlhoy' QIn 'echlet", "block.minecraft.birch_wood": "ber<PERSON><PERSON> So<PERSON>", "block.minecraft.black_banner": "joqwI' qIj", "block.minecraft.black_bed": "<PERSON>ong<PERSON><PERSON> qIj", "block.minecraft.black_candle": "weQ qIj", "block.minecraft.black_candle_cake": "weQ qIj ghajbogh chab'a''e'", "block.minecraft.black_carpet": "tlhIm qIj", "block.minecraft.black_concrete": "tungyen qIj", "block.minecraft.black_concrete_powder": "tungyen Say'qIS qIj", "block.minecraft.black_glazed_terracotta": "qIjbogh HaSreH nagh ghun", "block.minecraft.black_shulker_box": "shulker ngaSwI' qIj", "block.minecraft.black_stained_glass": "'al'on qIj", "block.minecraft.black_stained_glass_pane": "'al'on 'echlet qIj", "block.minecraft.black_terracotta": "qIjbogh nagh ghun", "block.minecraft.black_wool": "veD qIj", "block.minecraft.blackstone": "nagh qIj", "block.minecraft.blackstone_slab": "nagh qIj bu<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.blackstone_stairs": "nagh qIj letlh", "block.minecraft.blackstone_wall": "nagh qIj tlhoy'", "block.minecraft.blast_furnace": "vIncha' tujqu'", "block.minecraft.blue_banner": "SuDbogh joqwI' 'e<PERSON>", "block.minecraft.blue_bed": "SuD<PERSON><PERSON>ong<PERSON><PERSON><PERSON>", "block.minecraft.blue_candle": "SuDbogh weQ 'ej <PERSON>", "block.minecraft.blue_candle_cake": "SuDbogh weQ 'ej <PERSON> ghajbogh chab'a''e'", "block.minecraft.blue_carpet": "SuDbogh tlhIm '<PERSON>j <PERSON>", "block.minecraft.blue_concrete": "SuDbogh tungyen '<PERSON><PERSON>", "block.minecraft.blue_concrete_powder": "SuDbogh tungyen Say'qIS 'ej <PERSON>", "block.minecraft.blue_glazed_terracotta": "SuDbogh HaSreH nagh ghun 'ej <PERSON>", "block.minecraft.blue_ice": "chuch <PERSON>", "block.minecraft.blue_orchid": "bIlu 'orqID 'InSong", "block.minecraft.blue_shulker_box": "SuDbogh shulker ngaSwI' 'e<PERSON>", "block.minecraft.blue_stained_glass": "'al'on SuD", "block.minecraft.blue_stained_glass_pane": "'al'on 'echlet SuD", "block.minecraft.blue_terracotta": "SuDbogh nagh ghun 'ej <PERSON>", "block.minecraft.blue_wool": "SuDbogh veD '<PERSON><PERSON>", "block.minecraft.bone_block": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>r", "block.minecraft.bookshelf": "paq bey'", "block.minecraft.brain_coral": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>o<PERSON><PERSON>'", "block.minecraft.brain_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brain_coral_fan": "QoghIj 'o<PERSON><PERSON>' m<PERSON>jDang", "block.minecraft.brain_coral_wall_fan": "QoghIj 'oqe' tl<PERSON>y' mIjDang", "block.minecraft.brewing_stand": "taS vutmeH qal'aq", "block.minecraft.brick_slab": "<PERSON><PERSON><PERSON><PERSON> bu<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.brick_stairs": "ngogh letlh", "block.minecraft.brick_wall": "ngog<PERSON>ey tlhoy'", "block.minecraft.bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "Doqbogh joqwI' 'ej wovbe'bogh", "block.minecraft.brown_bed": "<PERSON><PERSON><PERSON><PERSON>ej wov<PERSON>'bogh", "block.minecraft.brown_candle": "Doqbogh weQ 'ej wovbe'bogh", "block.minecraft.brown_candle_cake": "Doqbogh weQ 'ej wovbe'bogh ghajbogh chab'a''e'", "block.minecraft.brown_carpet": "Doqbogh tlhIm 'ej wovbe'bogh", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON><PERSON> tungyen 'ej wovbe'bogh", "block.minecraft.brown_concrete_powder": "Doqbogh tungyen Say'qIS 'ej wovbe'bogh", "block.minecraft.brown_glazed_terracotta": "Doqbogh HaSreH nagh ghun 'ej wovbe'bogh", "block.minecraft.brown_mushroom": "yav 'atlhqam wovbe'", "block.minecraft.brown_mushroom_block": "yav 'at<PERSON><PERSON><PERSON>m wov<PERSON>' bu<PERSON>'<PERSON>r", "block.minecraft.brown_shulker_box": "Do<PERSON><PERSON><PERSON> shulker ngaSwI' 'ej wovbe'bogh", "block.minecraft.brown_stained_glass": "'al'on <PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "'al'on 'e<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_terracotta": "Doqbogh nagh ghun 'ej wov<PERSON>'bogh", "block.minecraft.brown_wool": "Doqbo<PERSON> veD 'ej wovbe'bogh", "block.minecraft.bubble_column": "ba'<PERSON>q tut", "block.minecraft.bubble_coral": "ba'<PERSON><PERSON> 'oqe'", "block.minecraft.bubble_coral_block": "ba'<PERSON><PERSON> 'oq<PERSON>' bu<PERSON>'<PERSON>r", "block.minecraft.bubble_coral_fan": "ba'<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral_wall_fan": "ba'<PERSON><PERSON> 'oq<PERSON>' t<PERSON><PERSON><PERSON>' mIjDang", "block.minecraft.budding_amethyst": "Sachbogh choSom nguv", "block.minecraft.bush": "lav", "block.minecraft.cactus": "qaqtaS San'emDer", "block.minecraft.cactus_flower": "qaqtaS 'InSong", "block.minecraft.cake": "chab'a'", "block.minecraft.calcite": "'a<PERSON><PERSON> nagh", "block.minecraft.calibrated_sculk_sensor": "sculk noch pup", "block.minecraft.campfire": "raQ qul", "block.minecraft.candle": "weQ", "block.minecraft.candle_cake": "we<PERSON> ghaj<PERSON>gh chab'a'", "block.minecraft.carrots": "qe'rot 'o<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "pu'jIn raS", "block.minecraft.carved_pumpkin": "'aquta' naH pe'lu'ta'bogh", "block.minecraft.cauldron": "bargh'a'", "block.minecraft.cave_air": "DIS rewve'", "block.minecraft.cave_vines": "DIS tlhegh tI", "block.minecraft.cave_vines_plant": "DIS tlhegh tI San'emDer", "block.minecraft.chain": "mIr", "block.minecraft.chain_command_block": "mIr ra'wI' buq'Ir", "block.minecraft.cherry_button": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_door": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_fence_gate": "<PERSON><PERSON><PERSON>'Ho<PERSON> lojmIt", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON> 'e<PERSON><PERSON> tlhep", "block.minecraft.cherry_leaves": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_log": "<PERSON><PERSON><PERSON>'a'", "block.minecraft.cherry_planks": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_sapling": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_slab": "<PERSON><PERSON><PERSON>u<PERSON><PERSON> b<PERSON>", "block.minecraft.cherry_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.cherry_trapdoor": "<PERSON><PERSON><PERSON> lojmIt", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON> tlho<PERSON>' QIn 'echlet tlhep", "block.minecraft.cherry_wall_sign": "<PERSON><PERSON><PERSON>ho<PERSON>' QIn 'echlet", "block.minecraft.cherry_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.chest": "DerlIq", "block.minecraft.chipped_anvil": "'achme' QIHlu'pu'bogh", "block.minecraft.chiseled_bookshelf": "paq yorgh nanlu'bogh", "block.minecraft.chiseled_copper": "<PERSON><PERSON><PERSON><PERSON> nan<PERSON>bogh", "block.minecraft.chiseled_deepslate": "nagh ja<PERSON> nan<PERSON>bogh", "block.minecraft.chiseled_nether_bricks": "nether ngog<PERSON>ey nan<PERSON>'bogh", "block.minecraft.chiseled_polished_blackstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ogh nagh qIj nan<PERSON>'bogh", "block.minecraft.chiseled_quartz_block": "cho<PERSON><PERSON> bu<PERSON>'<PERSON>r nan<PERSON>bogh", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON>ol nagh Doq nan<PERSON>'bogh", "block.minecraft.chiseled_resin_bricks": "reb<PERSON><PERSON> ng<PERSON><PERSON><PERSON> na<PERSON>bogh", "block.minecraft.chiseled_sandstone": "Do'ol nagh nan<PERSON>'bogh", "block.minecraft.chiseled_stone_bricks": "nagh ngog<PERSON>ey nan<PERSON>'bogh", "block.minecraft.chiseled_tuff": "tIpqan nagh nan<PERSON>'bogh", "block.minecraft.chiseled_tuff_bricks": "tIpqan nagh ngoghmey nan<PERSON>'bogh", "block.minecraft.chorus_flower": "bomw<PERSON>' ghom 'In<PERSON>ong", "block.minecraft.chorus_plant": "bomwI' ghom San'emDer", "block.minecraft.clay": "DabqI'", "block.minecraft.closed_eyeblossom": "mIn 'InSong SoQ", "block.minecraft.coal_block": "ghav nIn buq'Ir", "block.minecraft.coal_ore": "nagh ghav nIn", "block.minecraft.coarse_dirt": "lam ghegh", "block.minecraft.cobbled_deepslate": "tlherbogh nagh jaQ", "block.minecraft.cobbled_deepslate_slab": "tlherbogh nagh ja<PERSON> bu<PERSON><PERSON><PERSON><PERSON> bID", "block.minecraft.cobbled_deepslate_stairs": "tlherbogh nagh jaQ letlh", "block.minecraft.cobbled_deepslate_wall": "tlherbogh nagh jaQ tlhoy'", "block.minecraft.cobblestone": "nagh tlher", "block.minecraft.cobblestone_slab": "nagh tlher bu<PERSON>'<PERSON><PERSON> b<PERSON>", "block.minecraft.cobblestone_stairs": "nagh tlher letlh", "block.minecraft.cobblestone_wall": "nagh tlher tlhoy'", "block.minecraft.cobweb": "SIrgh SIr'o'", "block.minecraft.cocoa": "yuch", "block.minecraft.command_block": "ra'wI' bu<PERSON>'Ir", "block.minecraft.comparator": "redstone DaywI'", "block.minecraft.composter": "nonmoHwI' 'Ib", "block.minecraft.conduit": "'och", "block.minecraft.copper_block": "<PERSON><PERSON><PERSON><PERSON> bu<PERSON>'<PERSON>r", "block.minecraft.copper_bulb": "Sorpuq wovmoHwI'", "block.minecraft.copper_door": "Sorpuq lojmIt", "block.minecraft.copper_grate": "So<PERSON><PERSON>q neng'el", "block.minecraft.copper_ore": "nagh Sorpuq", "block.minecraft.copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> lojmIt", "block.minecraft.cornflower": "qorInvIlaw''er 'InSong", "block.minecraft.cracked_deepslate_bricks": "nagh ja<PERSON> ng<PERSON><PERSON><PERSON>h", "block.minecraft.cracked_deepslate_tiles": "nagh ja<PERSON> ma<PERSON>h", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON> ngogh <PERSON>bogh", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON> nagh qIj ng<PERSON><PERSON><PERSON>bogh", "block.minecraft.cracked_stone_bricks": "nagh ng<PERSON><PERSON><PERSON>b<PERSON>h", "block.minecraft.crafter": "chenmoHwI'", "block.minecraft.crafting_table": "vummeH raS", "block.minecraft.creaking_heart": "HaSwI' tIq", "block.minecraft.creeper_head": "creeper nach", "block.minecraft.creeper_wall_head": "creeper tlhoy' nach", "block.minecraft.crimson_button": "nether Sor <PERSON> leQ", "block.minecraft.crimson_door": "nether <PERSON><PERSON><PERSON> lo<PERSON>", "block.minecraft.crimson_fence": "nether Sor <PERSON> t<PERSON>ho<PERSON>'<PERSON>", "block.minecraft.crimson_fence_gate": "nether Sor <PERSON> t<PERSON>'<PERSON> lojmIt", "block.minecraft.crimson_fungus": "'<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_hanging_sign": "nether Sor <PERSON>q QI<PERSON> 'echlet tlhep", "block.minecraft.crimson_hyphae": "nether <PERSON><PERSON>y", "block.minecraft.crimson_nylium": "nether magh Doq", "block.minecraft.crimson_planks": "nether So<PERSON>", "block.minecraft.crimson_pressure_plate": "nether So<PERSON>", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "nether Sor <PERSON> 'e<PERSON>let", "block.minecraft.crimson_slab": "nether So<PERSON> <PERSON><PERSON> bu<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.crimson_stairs": "nether <PERSON><PERSON><PERSON>h", "block.minecraft.crimson_stem": "nether <PERSON><PERSON>", "block.minecraft.crimson_trapdoor": "nether <PERSON><PERSON><PERSON> lojmIt", "block.minecraft.crimson_wall_hanging_sign": "nether Sor <PERSON><PERSON> tlhoy' QIn 'echlet tlhep", "block.minecraft.crimson_wall_sign": "nether Sor <PERSON><PERSON> tlho<PERSON>' QIn 'echlet", "block.minecraft.crying_obsidian": "SaQbogh 'al'on nagh", "block.minecraft.cut_copper": "Sorpuq pe'lu'bogh", "block.minecraft.cut_copper_slab": "<PERSON><PERSON><PERSON>q pe'lu'b<PERSON>h bu<PERSON><PERSON><PERSON><PERSON> bID", "block.minecraft.cut_copper_stairs": "Sorpuq pe'lu'bogh letlh", "block.minecraft.cut_red_sandstone": "Do'ol nagh Doq pe'lu'bogh", "block.minecraft.cut_red_sandstone_slab": "<PERSON>'ol nagh Doq pe'lu'bogh bu<PERSON><PERSON><PERSON><PERSON> bID", "block.minecraft.cut_sandstone": "Do'ol nagh pe'lu'bogh", "block.minecraft.cut_sandstone_slab": "Do'ol nagh pe'lu'bogh bu<PERSON>'<PERSON>r bID", "block.minecraft.cyan_banner": "SuDq<PERSON>'bogh joqwI' 'ej wovbogh", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh we<PERSON> 'ej wov<PERSON>gh", "block.minecraft.cyan_candle_cake": "Su<PERSON><PERSON><PERSON>'bogh weQ 'ej wovbogh ghajbogh chab'a''e'", "block.minecraft.cyan_carpet": "Su<PERSON><PERSON><PERSON><PERSON>bogh tlh<PERSON><PERSON> 'ej wov<PERSON>gh", "block.minecraft.cyan_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh tungyen 'ej wov<PERSON>gh", "block.minecraft.cyan_concrete_powder": "Su<PERSON><PERSON><PERSON><PERSON>b<PERSON>h tungyen Say'qIS 'ej wovbogh", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>re<PERSON> nagh ghun 'ej wovbogh", "block.minecraft.cyan_shulker_box": "SuD<PERSON><PERSON>'bogh shulker ngaSwI' 'ej wovbogh", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON><PERSON>'bog<PERSON> 'al'on 'ej wovbogh", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>'bogh 'al'on 'echlet 'ej wovbogh", "block.minecraft.cyan_terracotta": "Su<PERSON><PERSON><PERSON><PERSON>bogh nagh ghun 'ej wov<PERSON>gh", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h ve<PERSON> 'ej wov<PERSON>gh", "block.minecraft.damaged_anvil": "'achme' QIHqu'lu'pu'bogh", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON>", "block.minecraft.dark_oak_button": "'<PERSON><PERSON> le<PERSON>", "block.minecraft.dark_oak_door": "<PERSON><PERSON> lojm<PERSON>", "block.minecraft.dark_oak_fence": "'oq <PERSON><PERSON>h Sor tlhoy'Hom", "block.minecraft.dark_oak_fence_gate": "'oq <PERSON><PERSON>h Sor tlhoy'Ho<PERSON> lojmIt", "block.minecraft.dark_oak_hanging_sign": "'oq <PERSON><PERSON>h Sor QIn 'echlet tlhep", "block.minecraft.dark_oak_leaves": "'oq <PERSON> p<PERSON>ey", "block.minecraft.dark_oak_log": "'oq <PERSON><PERSON><PERSON>'a'", "block.minecraft.dark_oak_planks": "'oq <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_pressure_plate": "'<PERSON>q <PERSON><PERSON> Sor Sur<PERSON> 'echlet", "block.minecraft.dark_oak_sapling": "'<PERSON><PERSON> <PERSON> SorHom", "block.minecraft.dark_oak_sign": "'oq <PERSON><PERSON>h Sor QIn 'echlet", "block.minecraft.dark_oak_slab": "'<PERSON><PERSON> <PERSON><PERSON><PERSON>r bu<PERSON>'<PERSON><PERSON> b<PERSON>", "block.minecraft.dark_oak_stairs": "'o<PERSON> <PERSON><PERSON>h <PERSON>r letlh", "block.minecraft.dark_oak_trapdoor": "'<PERSON><PERSON> <PERSON><PERSON><PERSON> lojmIt", "block.minecraft.dark_oak_wall_hanging_sign": "'oq <PERSON><PERSON>h Sor tlhoy' QIn 'echlet tlhep", "block.minecraft.dark_oak_wall_sign": "'oq <PERSON><PERSON>h Sor tlhoy' QIn 'echlet", "block.minecraft.dark_oak_wood": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.dark_prismarine": "prism<PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "prism<PERSON><PERSON> b<PERSON> b<PERSON>", "block.minecraft.dark_prismarine_stairs": "prismari<PERSON>h", "block.minecraft.daylight_detector": "pem noch", "block.minecraft.dead_brain_coral": "<PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>oqe'", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>r", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>' mIj<PERSON><PERSON>", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oq<PERSON>' t<PERSON><PERSON><PERSON>' mIjDang", "block.minecraft.dead_bubble_coral": "<PERSON><PERSON><PERSON>'bogh ba'<PERSON><PERSON> 'oqe'", "block.minecraft.dead_bubble_coral_block": "<PERSON><PERSON><PERSON><PERSON>bogh ba'<PERSON><PERSON> 'oq<PERSON>' bu<PERSON>'Ir", "block.minecraft.dead_bubble_coral_fan": "<PERSON><PERSON><PERSON><PERSON>bogh b<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>' m<PERSON>j<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>bogh ba'<PERSON><PERSON> 'oqe' t<PERSON><PERSON><PERSON>' mIjDang", "block.minecraft.dead_bush": "Heghbogh lav", "block.minecraft.dead_fire_coral": "<PERSON>gh<PERSON>'bogh qul 'oqe'", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON><PERSON><PERSON>bogh qul 'oq<PERSON>' bu<PERSON>'<PERSON>r", "block.minecraft.dead_fire_coral_fan": "<PERSON><PERSON><PERSON><PERSON>bogh qul 'oq<PERSON>' m<PERSON>j<PERSON>ang", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON>bogh qul 'oqe' tlhoy' mIjDang", "block.minecraft.dead_horn_coral": "Hegh<PERSON>'bogh pu' 'oqe'", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON><PERSON>'bogh pu' 'oqe' bu<PERSON>'Ir", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON><PERSON>'bogh pu' 'oqe' m<PERSON>j<PERSON>ang", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON>'bogh pu' 'oqe' tl<PERSON><PERSON>' mIjDang", "block.minecraft.dead_tube_coral": "Hegh<PERSON>'bogh 'och 'oqe'", "block.minecraft.dead_tube_coral_block": "<PERSON><PERSON><PERSON>'bogh 'och 'oqe' bu<PERSON>'Ir", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON><PERSON>'bogh 'och 'oqe' mIjDang", "block.minecraft.dead_tube_coral_wall_fan": "<PERSON><PERSON><PERSON>'bogh 'och 'oqe' tl<PERSON>y' mIjDang", "block.minecraft.decorated_pot": "m<PERSON><PERSON><PERSON> bal", "block.minecraft.deepslate": "nagh jaQ", "block.minecraft.deepslate_brick_slab": "nagh ja<PERSON> ng<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.deepslate_brick_stairs": "nagh ja<PERSON> ng<PERSON><PERSON><PERSON> let<PERSON>h", "block.minecraft.deepslate_brick_wall": "nagh ja<PERSON> ng<PERSON><PERSON>ey tlhoy'", "block.minecraft.deepslate_bricks": "nagh ja<PERSON> ng<PERSON>", "block.minecraft.deepslate_coal_ore": "nagh jaQ ghav nIn", "block.minecraft.deepslate_copper_ore": "nagh ja<PERSON>", "block.minecraft.deepslate_diamond_ore": "nagh ja<PERSON> chanmon", "block.minecraft.deepslate_emerald_ore": "nagh ja<PERSON> patmor", "block.minecraft.deepslate_gold_ore": "nagh jaQ qol'om", "block.minecraft.deepslate_iron_ore": "nagh ja<PERSON> 'uSqan tlhIl", "block.minecraft.deepslate_lapis_ore": "nagh ja<PERSON>", "block.minecraft.deepslate_redstone_ore": "nagh jaQ redstone", "block.minecraft.deepslate_tile_slab": "nagh ja<PERSON> ma<PERSON> b<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.deepslate_tile_stairs": "nagh ja<PERSON> majyang <PERSON>h", "block.minecraft.deepslate_tile_wall": "nagh jaQ majyang tlhoy'", "block.minecraft.deepslate_tiles": "nagh ja<PERSON> majyang", "block.minecraft.detector_rail": "noch tIHmey", "block.minecraft.diamond_block": "chan<PERSON> bu<PERSON>'<PERSON>r", "block.minecraft.diamond_ore": "nagh chanmon", "block.minecraft.diorite": "qopagh", "block.minecraft.diorite_slab": "qop<PERSON> bu<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.diorite_stairs": "qopagh letlh", "block.minecraft.diorite_wall": "qopagh tlhoy'", "block.minecraft.dirt": "lam", "block.minecraft.dirt_path": "lam taw", "block.minecraft.dispenser": "ba<PERSON><PERSON>", "block.minecraft.dragon_egg": "lung'a' QIm", "block.minecraft.dragon_head": "lung'a' nach", "block.minecraft.dragon_wall_head": "lung'a' tlhoy' nach", "block.minecraft.dried_ghast": "ghast <PERSON>a<PERSON>", "block.minecraft.dried_kelp_block": "nI'qur <PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>r", "block.minecraft.dripstone_block": "<PERSON><PERSON><PERSON><PERSON> bu<PERSON>'<PERSON>r", "block.minecraft.dropper": "chaghwI'", "block.minecraft.emerald_block": "patmor ngogh", "block.minecraft.emerald_ore": "nagh patmor", "block.minecraft.enchanting_table": "reSmeH raS", "block.minecraft.end_gateway": "end DIn", "block.minecraft.end_portal": "end lojmIt", "block.minecraft.end_portal_frame": "end lojmIt qal'aq", "block.minecraft.end_rod": "end naQ", "block.minecraft.end_stone": "end nagh", "block.minecraft.end_stone_brick_slab": "end nagh ng<PERSON><PERSON><PERSON> bu<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.end_stone_brick_stairs": "end nagh ng<PERSON><PERSON>ey letlh", "block.minecraft.end_stone_brick_wall": "end nagh ngoghmey tlhoy'", "block.minecraft.end_stone_bricks": "<PERSON> nagh ngoghmey", "block.minecraft.ender_chest": "ender <PERSON>", "block.minecraft.exposed_chiseled_copper": "raghchoHbogh Sorpuq nan<PERSON>'bogh", "block.minecraft.exposed_copper": "raghchoHbogh Sorpuq", "block.minecraft.exposed_copper_bulb": "raghchoHbogh Sorpuq wovmoHwI'", "block.minecraft.exposed_copper_door": "raghchoHbogh Sorpuq lojmIt", "block.minecraft.exposed_copper_grate": "raghchoHbogh Sorpuq neng'el", "block.minecraft.exposed_copper_trapdoor": "raghchoHbogh Sorpuq vonmeH lojmIt", "block.minecraft.exposed_cut_copper": "raghchoHbogh Sorpuq pe'lu'pu'bogh", "block.minecraft.exposed_cut_copper_slab": "raghchoHbogh Sorpuq pe'lu'bogh bu<PERSON>'<PERSON>r bID", "block.minecraft.exposed_cut_copper_stairs": "raghchoHbogh Sorpuq pe'lu'bogh letlh", "block.minecraft.farmland": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.fern": "DIS'oS", "block.minecraft.fire": "qul", "block.minecraft.fire_coral": "qul 'oqe'", "block.minecraft.fire_coral_block": "qul 'oqe' ngogh", "block.minecraft.fire_coral_fan": "qul 'o<PERSON><PERSON>' <PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.fire_coral_wall_fan": "qul 'oqe' t<PERSON><PERSON><PERSON>' mIjDang", "block.minecraft.firefly_bush": "wewbogh ghew lav", "block.minecraft.fletching_table": "naQjejHom raS", "block.minecraft.flower_pot": "'In<PERSON><PERSON> bal", "block.minecraft.flowering_azalea": "'InSong 'aSe'lIya lav", "block.minecraft.flowering_azalea_leaves": "'InSong 'aSe'l<PERSON>ya pormey", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "chuch ru'", "block.minecraft.furnace": "vIncha'", "block.minecraft.gilded_blackstone": "qol'om nagh qIj", "block.minecraft.glass": "'al'on", "block.minecraft.glass_pane": "'al'on 'echlet", "block.minecraft.glow_lichen": "wew<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.glowstone": "wovbogh nagh", "block.minecraft.gold_block": "qol'om buq'Ir", "block.minecraft.gold_ore": "nagh qol'om", "block.minecraft.granite": "lIvrI'", "block.minecraft.granite_slab": "lIvr<PERSON>' bu<PERSON>'<PERSON>r bID", "block.minecraft.granite_stairs": "lIvrI' letlh", "block.minecraft.granite_wall": "lIvrI' tlhoy'", "block.minecraft.grass": "magh", "block.minecraft.grass_block": "magh bu<PERSON>'<PERSON>r", "block.minecraft.gravel": "yetmoS", "block.minecraft.gray_banner": "qIjbogh joqwI' 'ej wovbogh", "block.minecraft.gray_bed": "qIjbogh <PERSON>ej wovbogh", "block.minecraft.gray_candle": "qIjbogh weQ 'ej wovbogh", "block.minecraft.gray_candle_cake": "qIjbogh weQ 'ej wovbogh ghajbogh chab'a''e'", "block.minecraft.gray_carpet": "qIjbogh tlhIm 'ej wovbogh", "block.minecraft.gray_concrete": "qIjbogh tungyen 'ej wovbogh", "block.minecraft.gray_concrete_powder": "qIjbogh tungyen Say'qIS 'ej wovbogh", "block.minecraft.gray_glazed_terracotta": "qIjbogh HaSreH nagh ghun 'ej wovbogh", "block.minecraft.gray_shulker_box": "qIjbogh shulker ngaSwI' 'ej wovbogh", "block.minecraft.gray_stained_glass": "'al'on qIj 'ej wov", "block.minecraft.gray_stained_glass_pane": "'al'on 'echlet qIj 'ej wov", "block.minecraft.gray_terracotta": "qIjbogh nagh ghun 'ej wovbogh", "block.minecraft.gray_wool": "qIjbogh veD 'ej wovbogh", "block.minecraft.green_banner": "joqwI' SuDqu'", "block.minecraft.green_bed": "QongDaq SuDqu'", "block.minecraft.green_candle": "weQ SuDqu'", "block.minecraft.green_candle_cake": "weQ SuDqu' g<PERSON><PERSON><PERSON><PERSON> chab'a''e'", "block.minecraft.green_carpet": "tlhIm SuDqu'", "block.minecraft.green_concrete": "tungyen SuDqu'", "block.minecraft.green_concrete_powder": "tungyen Say'qIS SuDqu'", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nagh ghun", "block.minecraft.green_shulker_box": "shulker ngaSwI' SuDqu'", "block.minecraft.green_stained_glass": "'al'on SuDqu'", "block.minecraft.green_stained_glass_pane": "'al'on 'echlet SuDqu'", "block.minecraft.green_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh nagh ghun", "block.minecraft.green_wool": "veD <PERSON>'", "block.minecraft.grindstone": "jejmoHmeH nagh", "block.minecraft.hanging_roots": "'<PERSON><PERSON><PERSON><PERSON> tlhep", "block.minecraft.hay_block": "magh g<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.heavy_core": "SuqSIv 'ugh", "block.minecraft.heavy_weighted_pressure_plate": "Surmen 'echlet 'ugh", "block.minecraft.honey_block": "<PERSON><PERSON><PERSON><PERSON> bu<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.honeycomb_block": "<PERSON>q<PERSON>r S<PERSON><PERSON>'<PERSON>' bu<PERSON><PERSON><PERSON>r", "block.minecraft.hopper": "tomter", "block.minecraft.horn_coral": "pu' 'oqe'", "block.minecraft.horn_coral_block": "pu' 'oqe' buq'Ir", "block.minecraft.horn_coral_fan": "pu' 'oqe' m<PERSON>j<PERSON><PERSON>", "block.minecraft.horn_coral_wall_fan": "pu' 'oqe' tlhoy' mIjDang", "block.minecraft.ice": "chuch", "block.minecraft.infested_chiseled_stone_bricks": "nagh ngog<PERSON>ey nan<PERSON>'bogh 'ej ngejlu'bogh", "block.minecraft.infested_cobblestone": "nagh tlher ngej<PERSON>'bogh", "block.minecraft.infested_cracked_stone_bricks": "nagh ng<PERSON><PERSON><PERSON>'bogh 'ej ngejlu'bogh", "block.minecraft.infested_deepslate": "nagh ja<PERSON> ng<PERSON>bogh", "block.minecraft.infested_mossy_stone_bricks": "'ayIvlaS nagh ngoghmey ngejlu'bogh", "block.minecraft.infested_stone": "nagh ngej<PERSON>'bogh", "block.minecraft.infested_stone_bricks": "nagh ngog<PERSON>ey ng<PERSON>bogh", "block.minecraft.iron_bars": "'u<PERSON><PERSON><PERSON> na<PERSON>mey", "block.minecraft.iron_block": "'<PERSON><PERSON><PERSON><PERSON> bu<PERSON>'<PERSON>r", "block.minecraft.iron_door": "'uSqan lojmIt", "block.minecraft.iron_ore": "na<PERSON> '<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.iron_trapdoor": "'u<PERSON><PERSON><PERSON> lojmIt", "block.minecraft.jack_o_lantern": "'aquta' naH wovmoHwI'", "block.minecraft.jigsaw": "<PERSON><PERSON><PERSON><PERSON><PERSON> bu<PERSON>'<PERSON>r", "block.minecraft.jukebox": "bom jengva' chu'wI'", "block.minecraft.jungle_button": "ngem yIQ Sor leQ", "block.minecraft.jungle_door": "ngem yIQ Sor lojmIt", "block.minecraft.jungle_fence": "ngem yIQ <PERSON> t<PERSON>ho<PERSON>'<PERSON>", "block.minecraft.jungle_fence_gate": "ngem yIQ Sor tlho<PERSON>'Ho<PERSON> lojmIt", "block.minecraft.jungle_hanging_sign": "ngem yIQ Sor QIn 'echlet tlhep", "block.minecraft.jungle_leaves": "ngem yIQ <PERSON>ey", "block.minecraft.jungle_log": "ngem yIQ <PERSON>'a'", "block.minecraft.jungle_planks": "ngem yIQ <PERSON>", "block.minecraft.jungle_pressure_plate": "ngem yIQ Sor Sur<PERSON> 'e<PERSON>let", "block.minecraft.jungle_sapling": "ngem yIQ SorHom", "block.minecraft.jungle_sign": "ngem yIQ Sor QIn 'echlet", "block.minecraft.jungle_slab": "ngem yIQ <PERSON>r bu<PERSON>'<PERSON>r bID", "block.minecraft.jungle_stairs": "ngem yIQ <PERSON> letlh", "block.minecraft.jungle_trapdoor": "ngem yIQ So<PERSON> lojmIt", "block.minecraft.jungle_wall_hanging_sign": "ngem yIQ Sor tlhoy' QIn 'echlet tlhep", "block.minecraft.jungle_wall_sign": "ngem yIQ Sor tlhoy' QIn 'echlet", "block.minecraft.jungle_wood": "ngem yIQ <PERSON>", "block.minecraft.kelp": "nI'qur", "block.minecraft.kelp_plant": "nI'qur San'emDer", "block.minecraft.ladder": "letlh chong", "block.minecraft.lantern": "wovmoHwI'", "block.minecraft.lapis_block": "<PERSON><PERSON><PERSON> buq'<PERSON>", "block.minecraft.lapis_ore": "nagh Doltop", "block.minecraft.large_amethyst_bud": "choSom nguv ghub'a'", "block.minecraft.large_fern": "DIS'oS tIn", "block.minecraft.lava": "vaHbo'", "block.minecraft.lava_cauldron": "vaHbo' bargh'a'", "block.minecraft.leaf_litter": "por DI", "block.minecraft.lectern": "paq raS", "block.minecraft.lever": "chu'meH naQ", "block.minecraft.light": "wovmoHwI'", "block.minecraft.light_blue_banner": "SuDbogh joqwI' 'ej wovbogh", "block.minecraft.light_blue_bed": "SuDbo<PERSON> 'ej w<PERSON>gh", "block.minecraft.light_blue_candle": "SuDbogh weQ 'ej wovbogh", "block.minecraft.light_blue_candle_cake": "SuDbogh weQ 'ej wovbogh ghajbogh chab'a''e'", "block.minecraft.light_blue_carpet": "SuDbogh tlhIm 'ej wovbogh", "block.minecraft.light_blue_concrete": "SuDbogh tungyen 'ej wovbogh", "block.minecraft.light_blue_concrete_powder": "SuDbogh tungyen Say'qIS 'ej wovbogh", "block.minecraft.light_blue_glazed_terracotta": "SuDbogh HaSreH nagh ghun 'ej wovbogh", "block.minecraft.light_blue_shulker_box": "SuDbogh shulker ngaSwI' 'ej wovbogh", "block.minecraft.light_blue_stained_glass": "'al'on SuD 'ej wov", "block.minecraft.light_blue_stained_glass_pane": "'al'on 'echlet SuD 'ej wov", "block.minecraft.light_blue_terracotta": "SuDbogh nagh ghun 'ej wovbogh", "block.minecraft.light_blue_wool": "SuDbogh veD 'ej wovbogh", "block.minecraft.light_gray_banner": "qIjbogh joqwI' 'ej wovqu'bogh", "block.minecraft.light_gray_bed": "q<PERSON><PERSON><PERSON><PERSON>ej wovqu'bogh", "block.minecraft.light_gray_candle": "qIjbogh weQ 'ej wovqu'bogh", "block.minecraft.light_gray_candle_cake": "qIjbogh weQ 'ej wovqu'bogh ghajbogh chab'a''e'", "block.minecraft.light_gray_carpet": "qIjbogh tlhIm 'ej wovqu'bogh", "block.minecraft.light_gray_concrete": "qIj<PERSON><PERSON> tungyen 'ej wovqu'bogh", "block.minecraft.light_gray_concrete_powder": "qIjbogh tungyen Say'qIS 'ej wovqu'bogh", "block.minecraft.light_gray_glazed_terracotta": "qIjbogh HaSreH nagh ghun 'ej wovqu'bogh", "block.minecraft.light_gray_shulker_box": "qIjbogh shulker ngaSwI' 'ej wovqu'bogh", "block.minecraft.light_gray_stained_glass": "'al'on qIj 'ej wovqu'", "block.minecraft.light_gray_stained_glass_pane": "'al'on 'echlet qIj 'ej wovqu'", "block.minecraft.light_gray_terracotta": "qIjbogh nagh ghun 'ej wovqu'bogh", "block.minecraft.light_gray_wool": "qIjbogh veD 'ej wovqu'bogh", "block.minecraft.light_weighted_pressure_plate": "Surmen 'echlet tIS", "block.minecraft.lightning_rod": "pe'bIl naQ", "block.minecraft.lilac": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.lily_of_the_valley": "ngech lI'lIy", "block.minecraft.lily_pad": "'echlet por'a'", "block.minecraft.lime_banner": "SuDqu'bogh joqwI' 'ej wovqu'bogh", "block.minecraft.lime_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>ej wovqu'bogh", "block.minecraft.lime_candle": "Su<PERSON><PERSON><PERSON>'bogh weQ 'ej wovqu'bogh", "block.minecraft.lime_candle_cake": "SuDq<PERSON>'bogh weQ 'ej wovqu'bogh ghajbogh chab'a''e'", "block.minecraft.lime_carpet": "Su<PERSON><PERSON><PERSON>'bogh tlhIm 'ej wovqu'bogh", "block.minecraft.lime_concrete": "<PERSON><PERSON><PERSON><PERSON>'bogh tungyen 'ej wovqu'bogh", "block.minecraft.lime_concrete_powder": "Su<PERSON><PERSON><PERSON>'bogh tungyen Say'qIS 'ej wovqu'bogh", "block.minecraft.lime_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h Ha<PERSON>re<PERSON> nagh ghun 'ej wovqu'bogh", "block.minecraft.lime_shulker_box": "SuDqu'bogh shulker ngaSwI' 'ej wovqu'bogh", "block.minecraft.lime_stained_glass": "Su<PERSON><PERSON><PERSON>'bogh 'al'on 'ej wovqu'bogh", "block.minecraft.lime_stained_glass_pane": "SuD<PERSON><PERSON>'bogh 'al'on 'echlet 'ej wovqu'bogh", "block.minecraft.lime_terracotta": "<PERSON><PERSON><PERSON><PERSON>'bogh nagh ghun 'ej wovqu'bogh", "block.minecraft.lime_wool": "Su<PERSON><PERSON><PERSON>'bogh veD 'ej wovqu'bogh", "block.minecraft.lodestone": "peQnagh", "block.minecraft.loom": "nIqmeH qal'aq", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON><PERSON>'bogh joqwI' 'ej wov<PERSON>gh", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh we<PERSON> 'ej wov<PERSON>gh", "block.minecraft.magenta_candle_cake": "<PERSON><PERSON><PERSON><PERSON>'bogh weQ 'ej wovbogh ghajbogh chab'a''e'", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh tlh<PERSON><PERSON> 'ej wov<PERSON>gh", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h tungyen 'ej w<PERSON><PERSON>gh", "block.minecraft.magenta_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h tungyen Say'qIS 'ej wovbogh", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nagh ghun 'ej wovbogh", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON><PERSON>'bogh shulker ngaSwI' 'ej wovbogh", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON>bog<PERSON> 'al'on 'ej wov<PERSON>gh", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON>'bog<PERSON> 'al'on 'ech<PERSON> 'ej wovbogh", "block.minecraft.magenta_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh nagh ghun 'ej wov<PERSON>gh", "block.minecraft.magenta_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h ve<PERSON> 'ej wov<PERSON>gh", "block.minecraft.magma_block": "vaH<PERSON>' <PERSON> bu<PERSON>'Ir", "block.minecraft.mangrove_button": "man<PERSON><PERSON>r le<PERSON>", "block.minecraft.mangrove_door": "mangghov Sor lojmIt", "block.minecraft.mangrove_fence": "man<PERSON><PERSON> Sor tlho<PERSON>'Hom", "block.minecraft.mangrove_fence_gate": "man<PERSON>hov Sor tlhoy'Hom lojmIt", "block.minecraft.mangrove_hanging_sign": "man<PERSON><PERSON> Sor QIn 'echlet tlhep", "block.minecraft.mangrove_leaves": "man<PERSON><PERSON> p<PERSON>ey", "block.minecraft.mangrove_log": "<PERSON><PERSON><PERSON>'a'", "block.minecraft.mangrove_planks": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_pressure_plate": "mangghov Sor Surmen 'echlet", "block.minecraft.mangrove_propagule": "mangghov Sor raS'IS'a'", "block.minecraft.mangrove_roots": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_sign": "<PERSON><PERSON><PERSON> Sor QIn 'echlet", "block.minecraft.mangrove_slab": "man<PERSON><PERSON> <PERSON> bu<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.mangrove_stairs": "<PERSON><PERSON><PERSON> letlh", "block.minecraft.mangrove_trapdoor": "mangghov Sor jonmeH lojmIt", "block.minecraft.mangrove_wall_hanging_sign": "mangg<PERSON> Sor tlhoy' QIn 'echlet tlhep", "block.minecraft.mangrove_wall_sign": "mangg<PERSON> Sor tlhoy' QIn 'echlet", "block.minecraft.mangrove_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.medium_amethyst_bud": "choSom nguv ghub", "block.minecraft.melon": "qaq naH", "block.minecraft.melon_stem": "qaq na<PERSON>", "block.minecraft.moss_block": "'ayIvlaS buq'Ir", "block.minecraft.moss_carpet": "'ayIvlaS tlhIm", "block.minecraft.mossy_cobblestone": "'ayIvlaS nagh tlher", "block.minecraft.mossy_cobblestone_slab": "'ayIvlaS nagh tlher buq'<PERSON>r bID", "block.minecraft.mossy_cobblestone_stairs": "'ayIvlaS nagh tlher letlh", "block.minecraft.mossy_cobblestone_wall": "'ayIvlaS nagh tlher tlhoy'", "block.minecraft.mossy_stone_brick_slab": "'ayIvlaS nagh ngoghmey bu<PERSON>'<PERSON><PERSON> bID", "block.minecraft.mossy_stone_brick_stairs": "'ayIvlaS nagh ngoghmey letlh", "block.minecraft.mossy_stone_brick_wall": "'ayIvlaS nagh ngoghmey tlhoy'", "block.minecraft.mossy_stone_bricks": "'ayIvlaS nagh ngoghmey", "block.minecraft.moving_piston": "vIHtaHbogh ghunglItlh", "block.minecraft.mud": "DabqI' yIQ", "block.minecraft.mud_brick_slab": "DabqI' y<PERSON><PERSON> ng<PERSON> bu<PERSON><PERSON> b<PERSON>", "block.minecraft.mud_brick_stairs": "DabqI' y<PERSON><PERSON> ng<PERSON>h", "block.minecraft.mud_brick_wall": "DabqI' y<PERSON><PERSON> ng<PERSON> tlhoy'", "block.minecraft.mud_bricks": "DabqI' y<PERSON><PERSON>", "block.minecraft.muddy_mangrove_roots": "<PERSON><PERSON><PERSON><PERSON><PERSON> lam", "block.minecraft.mushroom_stem": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mycelium": "'<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "nether ng<PERSON>h <PERSON>", "block.minecraft.nether_brick_slab": "nether ngogh bu<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.nether_brick_stairs": "nether ngogh letlh", "block.minecraft.nether_brick_wall": "nether ngogh tlhoy'", "block.minecraft.nether_bricks": "nether ngog<PERSON>ey", "block.minecraft.nether_gold_ore": "nether nagh qol'om", "block.minecraft.nether_portal": "nether lojmIt", "block.minecraft.nether_quartz_ore": "nether nagh choSom", "block.minecraft.nether_sprouts": "nether ghub<PERSON>y", "block.minecraft.nether_wart": "nether 'e<PERSON><PERSON><PERSON> 'at<PERSON>m", "block.minecraft.nether_wart_block": "nether 'e<PERSON><PERSON><PERSON> bu<PERSON>'<PERSON>r", "block.minecraft.netherite_block": "netherite buq'Ir", "block.minecraft.netherrack": "netherrack", "block.minecraft.note_block": "'awt<PERSON>r buq'Ir", "block.minecraft.oak_button": "'oq Sor leQ", "block.minecraft.oak_door": "'oq <PERSON>r lojm<PERSON>t", "block.minecraft.oak_fence": "'oq Sor tlhoy'Hom", "block.minecraft.oak_fence_gate": "'oq Sor tlhoy'<PERSON><PERSON> lojmIt", "block.minecraft.oak_hanging_sign": "'oq Sor QIn 'echlet tlhep", "block.minecraft.oak_leaves": "'oq <PERSON>r pormey", "block.minecraft.oak_log": "'oq <PERSON><PERSON>'a'", "block.minecraft.oak_planks": "'oq <PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "'oq <PERSON>r <PERSON> 'echlet", "block.minecraft.oak_sapling": "'oq SorHom", "block.minecraft.oak_sign": "'oq Sor QIn 'echlet", "block.minecraft.oak_slab": "'oq <PERSON>r bu<PERSON>'<PERSON>r b<PERSON>", "block.minecraft.oak_stairs": "'oq <PERSON>r letlh", "block.minecraft.oak_trapdoor": "'oq <PERSON><PERSON> lojmIt", "block.minecraft.oak_wall_hanging_sign": "'oq Sor tlhoy' QIn 'echlet tlhep", "block.minecraft.oak_wall_sign": "'oq Sor tlhoy' QIn 'echlet", "block.minecraft.oak_wood": "'oq <PERSON><PERSON>", "block.minecraft.observer": "bejwI'", "block.minecraft.obsidian": "'al'on nagh", "block.minecraft.ochre_froglight": "mabeb wovmoHwI' SuD", "block.minecraft.ominous_banner": "maQmIgh joqwI'", "block.minecraft.open_eyeblossom": "mIn 'InSong poS", "block.minecraft.orange_banner": "Doqbogh joqwI' 'ej beqpuj rurbogh", "block.minecraft.orange_bed": "<PERSON><PERSON><PERSON><PERSON>ej beq<PERSON>j rurbogh", "block.minecraft.orange_candle": "Doqbogh weQ 'ej beqpuj rurbogh", "block.minecraft.orange_candle_cake": "Doqbogh weQ 'ej beqpuj rurbogh ghajbogh chab'a''e'", "block.minecraft.orange_carpet": "Doqbogh tlhI<PERSON> 'ej beqpuj rurbogh", "block.minecraft.orange_concrete": "Doqbogh tungyen 'ej beqpuj rurbogh", "block.minecraft.orange_concrete_powder": "Doqbogh tungyen Say'qIS 'ej beqpuj rurbogh", "block.minecraft.orange_glazed_terracotta": "Doqbogh HaSreH nagh ghun 'ej beqpuj rurbogh", "block.minecraft.orange_shulker_box": "Doqbogh shulker ngaSwI' 'ej beqpuj rurbogh", "block.minecraft.orange_stained_glass": "<PERSON><PERSON><PERSON><PERSON> 'al'on 'ej beqpuj rurbogh", "block.minecraft.orange_stained_glass_pane": "<PERSON>q<PERSON><PERSON> 'al'on 'echlet 'ej beqpuj rurbogh", "block.minecraft.orange_terracotta": "Doqbogh nagh ghun 'ej beqpuj rurbogh", "block.minecraft.orange_tulip": "Doqbogh tu'lI<PERSON> 'ej beqpuj rurbogh", "block.minecraft.orange_wool": "Doqbogh veD 'ej beqpuj rurbogh", "block.minecraft.oxeye_daisy": "'aq<PERSON>ay DeySIy 'InSong", "block.minecraft.oxidized_chiseled_copper": "<PERSON><PERSON><PERSON><PERSON>bogh Sorpuq nan<PERSON>'bogh", "block.minecraft.oxidized_copper": "<PERSON><PERSON><PERSON><PERSON>b<PERSON>h <PERSON>", "block.minecraft.oxidized_copper_bulb": "ragh<PERSON>'bogh Sorpuq wovmoHwI'", "block.minecraft.oxidized_copper_door": "<PERSON><PERSON><PERSON><PERSON>b<PERSON>h Sorpuq lojmIt", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON><PERSON><PERSON>bogh <PERSON>rpuq neng'el", "block.minecraft.oxidized_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON>b<PERSON>h <PERSON> lojmIt", "block.minecraft.oxidized_cut_copper": "<PERSON><PERSON><PERSON>'bogh Sorpuq pe'lu'bogh", "block.minecraft.oxidized_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON>bogh <PERSON>rpuq pe'lu'bogh bu<PERSON>'<PERSON>r bID", "block.minecraft.oxidized_cut_copper_stairs": "<PERSON>h<PERSON>'bogh Sorpuq pe'lu'bogh letlh", "block.minecraft.packed_ice": "chuch H<PERSON>", "block.minecraft.packed_mud": "HISbogh DabqI' yIQ", "block.minecraft.pale_hanging_moss": "wovbogh 'ayIvlaS tlhep", "block.minecraft.pale_moss_block": "'ayIvlaS wov buq'Ir", "block.minecraft.pale_moss_carpet": "'ayIvlaS wov tlhIm", "block.minecraft.pale_oak_button": "'oq wov Sor leQ", "block.minecraft.pale_oak_door": "'oq wov Sor lojmIt", "block.minecraft.pale_oak_fence": "'oq wov <PERSON><PERSON>", "block.minecraft.pale_oak_fence_gate": "'oq wov <PERSON><PERSON> lojmIt", "block.minecraft.pale_oak_hanging_sign": "'oq wov Sor QIn 'echlet tlhep", "block.minecraft.pale_oak_leaves": "'oq wov Sor pormey", "block.minecraft.pale_oak_log": "'oq wov Sor <PERSON>'a'", "block.minecraft.pale_oak_planks": "'oq wov <PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pale_oak_pressure_plate": "'oq wov Sor <PERSON> 'echlet", "block.minecraft.pale_oak_sapling": "'oq wov SorHom", "block.minecraft.pale_oak_sign": "'oq wov Sor QIn 'echlet", "block.minecraft.pale_oak_slab": "'oq wov Sor buq'Ir bID", "block.minecraft.pale_oak_stairs": "'oq wov Sor letlh", "block.minecraft.pale_oak_trapdoor": "'oq wov <PERSON><PERSON> lojmIt", "block.minecraft.pale_oak_wall_hanging_sign": "'oq wov Sor tlhoy' QIn 'echlet tlhep", "block.minecraft.pale_oak_wall_sign": "'oq wov Sor tlhoy' QIn 'echlet", "block.minecraft.pale_oak_wood": "'oq wov So<PERSON>p", "block.minecraft.pearlescent_froglight": "mabeb wovmoHwI' Doq", "block.minecraft.peony": "pIy'an<PERSON><PERSON> '<PERSON>", "block.minecraft.petrified_oak_slab": "'oq <PERSON>r buq'Ir b<PERSON> 'an", "block.minecraft.piglin_head": "piglin nach", "block.minecraft.piglin_wall_head": "piglin tlhoy' nach", "block.minecraft.pink_banner": "Doq<PERSON><PERSON>'bogh joqwI' 'ej wovqu'bogh", "block.minecraft.pink_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ej wovqu'bogh", "block.minecraft.pink_candle": "<PERSON><PERSON><PERSON><PERSON>'bogh weQ 'ej wovqu'bogh", "block.minecraft.pink_candle_cake": "<PERSON><PERSON><PERSON><PERSON>'bogh weQ 'ej wovqu'bogh ghajbogh chab'a''e'", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON><PERSON>'bogh tlh<PERSON><PERSON> 'ej wovqu'bogh", "block.minecraft.pink_concrete": "<PERSON><PERSON><PERSON><PERSON>'bogh tungyen 'ej wovqu'bogh", "block.minecraft.pink_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh tungyen Say'qIS 'ej wovqu'bogh", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h Ha<PERSON>re<PERSON> nagh ghun 'ej wovqu'bogh", "block.minecraft.pink_petals": "<PERSON><PERSON><PERSON><PERSON>'bog<PERSON> '<PERSON><PERSON>'me<PERSON> 'ej wovqu'bogh", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON><PERSON>'bogh shulker ngaSwI' 'ej wovqu'bogh", "block.minecraft.pink_stained_glass": "'al'on Doqqu' 'ej wov", "block.minecraft.pink_stained_glass_pane": "'al'on 'echlet <PERSON>' 'ej wov", "block.minecraft.pink_terracotta": "<PERSON><PERSON><PERSON><PERSON>'bogh nagh ghun 'ej wovqu'bogh", "block.minecraft.pink_tulip": "<PERSON><PERSON><PERSON><PERSON>'bogh tu'lIp 'ej wovqu'bogh", "block.minecraft.pink_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh veD 'ej wovqu'bogh", "block.minecraft.piston": "ghunglItlh", "block.minecraft.piston_head": "ghunglItlh nach", "block.minecraft.pitcher_crop": "bal <PERSON>'<PERSON><PERSON><PERSON> wIj<PERSON>'bogh", "block.minecraft.pitcher_plant": "bal San'emDer", "block.minecraft.player_head": "QujwI' nach", "block.minecraft.player_head.named": "%s nach", "block.minecraft.player_wall_head": "QujwI' tlhoy' nach", "block.minecraft.podzol": "ngem b<PERSON>r lam", "block.minecraft.pointed_dripstone": "<PERSON><PERSON><PERSON><PERSON> jej", "block.minecraft.polished_andesite": "chan nagh <PERSON>b", "block.minecraft.polished_andesite_slab": "chan nagh <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.polished_andesite_stairs": "chan nagh <PERSON>b letlh", "block.minecraft.polished_basalt": "<PERSON><PERSON><PERSON>b", "block.minecraft.polished_blackstone": "<PERSON><PERSON><PERSON><PERSON> nagh qIj", "block.minecraft.polished_blackstone_brick_slab": "<PERSON><PERSON><PERSON><PERSON> nagh qIj ng<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.polished_blackstone_brick_stairs": "<PERSON><PERSON><PERSON><PERSON> nagh qIj ng<PERSON><PERSON><PERSON> let<PERSON>h", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON><PERSON><PERSON> nagh qIj ngoghmey tlhoy'", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON> nagh qIj ng<PERSON>hmey", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON><PERSON><PERSON> nagh qIj leQ", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON><PERSON><PERSON><PERSON> nagh qIj jembogh 'echlet", "block.minecraft.polished_blackstone_slab": "<PERSON><PERSON><PERSON><PERSON> nagh qIj b<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.polished_blackstone_stairs": "<PERSON><PERSON><PERSON><PERSON> nagh qIj letlh", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON><PERSON><PERSON> nagh qIj tlhoy'", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON><PERSON> nagh jaQ", "block.minecraft.polished_deepslate_slab": "<PERSON><PERSON><PERSON><PERSON> nagh ja<PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.polished_deepslate_stairs": "<PERSON><PERSON><PERSON><PERSON> nagh jaQ letlh", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON><PERSON><PERSON> nagh jaQ tlhoy'", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.polished_diorite_slab": "q<PERSON><PERSON> bu<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.polished_diorite_stairs": "qop<PERSON> Hab letlh", "block.minecraft.polished_granite": "lIvr<PERSON>' Hab", "block.minecraft.polished_granite_slab": "lIvr<PERSON><PERSON> <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.polished_granite_stairs": "lIvr<PERSON><PERSON> <PERSON><PERSON> letlh", "block.minecraft.polished_tuff": "tIpqan nagh Habqu'", "block.minecraft.polished_tuff_slab": "tIpqan nagh bu<PERSON>'<PERSON>r bID Habqu'", "block.minecraft.polished_tuff_stairs": "tIpqan nagh letlh Habqu'", "block.minecraft.polished_tuff_wall": "tIpqan nagh tlhoy' Habqu'", "block.minecraft.poppy": "DIghna'", "block.minecraft.potatoes": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "'aq<PERSON>'Sa <PERSON>rHom ngaSbogh 'In<PERSON><PERSON> bal", "block.minecraft.potted_allium": "'any<PERSON> 'InSong ngaSbogh 'InSong bal", "block.minecraft.potted_azalea_bush": "'aSe'lIya SorHom ngaSbogh 'InSong bal", "block.minecraft.potted_azure_bluet": "'aSur bIlu''et 'InSong ngaSbogh 'InSong bal", "block.minecraft.potted_bamboo": "bam<PERSON>' nga<PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> bal", "block.minecraft.potted_birch_sapling": "ber<PERSON>ch SorHom ngaSbogh 'InSong bal", "block.minecraft.potted_blue_orchid": "bIlu 'orqID 'InSong ngaSbogh 'InSong bal", "block.minecraft.potted_brown_mushroom": "yav 'at<PERSON><PERSON><PERSON>m wovbe' ngaS<PERSON><PERSON> 'InSong bal", "block.minecraft.potted_cactus": "qaqtaS San'emDer ngaSbogh 'InSong bal", "block.minecraft.potted_cherry_sapling": "<PERSON><PERSON><PERSON> ngaSbogh 'InSong bal", "block.minecraft.potted_closed_eyeblossom": "mIn 'InSong SoQ ngaSbogh 'InSong bal", "block.minecraft.potted_cornflower": "qorInvIlaw''er 'InSong ngaSbogh 'InSong bal", "block.minecraft.potted_crimson_fungus": "'at<PERSON><PERSON><PERSON>m <PERSON> ngaSbogh 'In<PERSON><PERSON> bal", "block.minecraft.potted_crimson_roots": "'o<PERSON><PERSON><PERSON> nga<PERSON>gh '<PERSON><PERSON><PERSON> bal", "block.minecraft.potted_dandelion": "<PERSON><PERSON><PERSON><PERSON>'an 'InSong ngaSbogh 'InSong bal", "block.minecraft.potted_dark_oak_sapling": "'oq <PERSON> SorHom ngaSbogh 'In<PERSON>ong bal", "block.minecraft.potted_dead_bush": "Heghbogh lav ngaSbogh 'InSong bal", "block.minecraft.potted_fern": "DIS'oS ngaSbogh 'InSong bal", "block.minecraft.potted_flowering_azalea_bush": "'InSong 'aSe'lIya SorHom ngaSbogh 'InSong bal", "block.minecraft.potted_jungle_sapling": "ngem yIQ SorHom ngaSbogh 'InSong bal", "block.minecraft.potted_lily_of_the_valley": "ngech lI'lIy ngaSbogh 'InSong bal", "block.minecraft.potted_mangrove_propagule": "mangghov Sor raS'IS'a' ngaSbogh 'InSong bal", "block.minecraft.potted_oak_sapling": "'oq SorHom ngaSbogh 'In<PERSON>ong bal", "block.minecraft.potted_open_eyeblossom": "mIn 'InSong poS ngaSbogh 'InSong bal", "block.minecraft.potted_orange_tulip": "Doqbogh tu'lIp 'ej beqpuj rurbogh ngaSbogh 'InSong bal", "block.minecraft.potted_oxeye_daisy": "'aqSay DeySIy 'InSong ngaSbogh 'InSong bal", "block.minecraft.potted_pale_oak_sapling": "'oq wov SorHom ngaSbogh 'InSong bal", "block.minecraft.potted_pink_tulip": "<PERSON><PERSON><PERSON><PERSON>'bogh tu'lIp 'ej wovqu'bogh ngaSbo<PERSON> 'In<PERSON><PERSON> bal", "block.minecraft.potted_poppy": "<PERSON><PERSON><PERSON>' nga<PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> bal", "block.minecraft.potted_red_mushroom": "yav atlhqam yegh ngaSbogh 'InSong bal", "block.minecraft.potted_red_tulip": "tu'l<PERSON>p <PERSON> ng<PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> bal", "block.minecraft.potted_spruce_sapling": "qIrqoD SorHom ngaSbogh 'InSong bal", "block.minecraft.potted_torchflower": "<PERSON><PERSON> 'In<PERSON>ong ngaSbogh 'In<PERSON>ong bal", "block.minecraft.potted_warped_fungus": "'atlhqam SuD ngaSbogh 'InSong bal", "block.minecraft.potted_warped_roots": "'oQqar SuD ngaSbogh 'InSong bal", "block.minecraft.potted_white_tulip": "tu'lIp ch<PERSON> ngaSbogh 'InSong bal", "block.minecraft.potted_wither_rose": "wither ro'<PERSON>' nga<PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> bal", "block.minecraft.powder_snow": "chal chuch <PERSON>'q<PERSON>", "block.minecraft.powder_snow_cauldron": "chal chuch Say'qIS bargh'a'", "block.minecraft.powered_rail": "HoSbogh tIHmey", "block.minecraft.prismarine": "prismarine", "block.minecraft.prismarine_brick_slab": "prismari<PERSON> ng<PERSON><PERSON> b<PERSON><PERSON> b<PERSON>", "block.minecraft.prismarine_brick_stairs": "prismarine ng<PERSON>h", "block.minecraft.prismarine_bricks": "prismari<PERSON>", "block.minecraft.prismarine_slab": "prismari<PERSON> b<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.prismarine_stairs": "prismarine letlh", "block.minecraft.prismarine_wall": "prismarine tlhoy'", "block.minecraft.pumpkin": "'aquta' naH", "block.minecraft.pumpkin_stem": "'aquta'", "block.minecraft.purple_banner": "ghIrep Hurgh rurbogh joqwI''e'", "block.minecraft.purple_bed": "ghIrep Hurgh rurbogh QongDaq'e'", "block.minecraft.purple_candle": "ghIrep Hurgh rurbogh weQ'e'", "block.minecraft.purple_candle_cake": "ghIrep Hurgh rurbogh weQ ghajbogh chab'a''e'", "block.minecraft.purple_carpet": "ghIrep Hurgh rurbogh tlhIm'e'", "block.minecraft.purple_concrete": "ghIrep Hurgh rurbogh tungyen'e'", "block.minecraft.purple_concrete_powder": "ghIrep Hurgh rurbogh tungyen Say'qIS'e'", "block.minecraft.purple_glazed_terracotta": "ghIrep Hurgh rurbogh HaSreH nagh ghun'e'", "block.minecraft.purple_shulker_box": "ghIrep Hurgh rurbogh shulker ngaSwI''e'", "block.minecraft.purple_stained_glass": "ghIrep Hurgh rurbogh 'al'on'e'", "block.minecraft.purple_stained_glass_pane": "ghIrep Hurgh rurbogh 'al'on 'echlet'e'", "block.minecraft.purple_terracotta": "ghIrep rurbogh nagh ghun'e'", "block.minecraft.purple_wool": "ghIrep Hurgh rurbogh veD'e'", "block.minecraft.purpur_block": "purpur bu<PERSON>'<PERSON>r", "block.minecraft.purpur_pillar": "purpur tut", "block.minecraft.purpur_slab": "purpur bu<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.purpur_stairs": "purpur letlh", "block.minecraft.quartz_block": "cho<PERSON><PERSON> bu<PERSON>'<PERSON>r", "block.minecraft.quartz_bricks": "cho<PERSON><PERSON> ng<PERSON><PERSON>ey", "block.minecraft.quartz_pillar": "choSom tut", "block.minecraft.quartz_slab": "choSom bu<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.quartz_stairs": "choSom letlh", "block.minecraft.rail": "tIHmey", "block.minecraft.raw_copper_block": "<PERSON><PERSON><PERSON>q tlhol buq'Ir", "block.minecraft.raw_gold_block": "qol'om tlhol buq'Ir", "block.minecraft.raw_iron_block": "'u<PERSON><PERSON>n tlhol buq'Ir", "block.minecraft.red_banner": "joqwI' Doqqu'", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_candle": "weQ <PERSON>'", "block.minecraft.red_candle_cake": "we<PERSON>' g<PERSON><PERSON><PERSON><PERSON> chab'a'", "block.minecraft.red_carpet": "tlhIm <PERSON>'", "block.minecraft.red_concrete": "tungyen Doqqu'", "block.minecraft.red_concrete_powder": "tungyen Say'qIS Doqqu'", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nagh ghun", "block.minecraft.red_mushroom": "yav atlhqam yegh", "block.minecraft.red_mushroom_block": "yav atlhqam yegh bu<PERSON>'<PERSON>r", "block.minecraft.red_nether_brick_slab": "nether ngogh wov bu<PERSON>'<PERSON><PERSON> bID", "block.minecraft.red_nether_brick_stairs": "nether ngogh wov letlh", "block.minecraft.red_nether_brick_wall": "nether ngogh wov tlhoy'", "block.minecraft.red_nether_bricks": "nether ngog<PERSON>ey wov", "block.minecraft.red_sand": "Do'<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone": "Do'o<PERSON> nagh Doq", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON><PERSON><PERSON> nagh <PERSON><PERSON> bu<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.red_sandstone_stairs": "Do<PERSON>ol nagh Doq letlh", "block.minecraft.red_sandstone_wall": "Do'ol nagh Doq tlhoy'", "block.minecraft.red_shulker_box": "shulker ngaSwI' Doqqu'", "block.minecraft.red_stained_glass": "'al'on Doqqu'", "block.minecraft.red_stained_glass_pane": "'al'on 'echlet <PERSON>'", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ogh nagh ghun", "block.minecraft.red_tulip": "tu'lIp <PERSON>'", "block.minecraft.red_wool": "ve<PERSON>'", "block.minecraft.redstone_block": "redstone buq'Ir", "block.minecraft.redstone_lamp": "redstone wovmoHwI'", "block.minecraft.redstone_ore": "nagh redstone", "block.minecraft.redstone_torch": "redstone Sech", "block.minecraft.redstone_wall_torch": "redstone tlhoy' Sech", "block.minecraft.redstone_wire": "redstone SIrgh", "block.minecraft.reinforced_deepslate": "HoSbogh nagh jaQ", "block.minecraft.repeater": "redstone HoSmoHwI'", "block.minecraft.repeating_command_block": "ra'qa'bogh ra'wI' buq'Ir", "block.minecraft.resin_block": "re<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>r", "block.minecraft.resin_brick_slab": "reb<PERSON><PERSON> ng<PERSON>h bu<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.resin_brick_stairs": "reb<PERSON><PERSON> ngogh letlh", "block.minecraft.resin_brick_wall": "rebmugh ngogh tlhoy'", "block.minecraft.resin_bricks": "reb<PERSON><PERSON> ng<PERSON><PERSON>", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON>'", "block.minecraft.respawn_anchor": "bog<PERSON><PERSON><PERSON><PERSON><PERSON> jan", "block.minecraft.rooted_dirt": "'<PERSON><PERSON><PERSON><PERSON> lam", "block.minecraft.rose_bush": "ro'<PERSON>' lav", "block.minecraft.sand": "Do'ol", "block.minecraft.sandstone": "Do'ol nagh", "block.minecraft.sandstone_slab": "Do'ol nagh bu<PERSON>'<PERSON>r bID", "block.minecraft.sandstone_stairs": "Do'ol nagh letlh", "block.minecraft.sandstone_wall": "Do'ol nagh tlhoy'", "block.minecraft.scaffolding": "chenmoHmeH qal'aq", "block.minecraft.sculk": "sculk", "block.minecraft.sculk_catalyst": "sculk lIngwI'", "block.minecraft.sculk_sensor": "sculk noch", "block.minecraft.sculk_shrieker": "sculk jachwI'", "block.minecraft.sculk_vein": "sculk SIr'o'", "block.minecraft.sea_lantern": "bIQ'a' wovmoHwI'", "block.minecraft.sea_pickle": "bI<PERSON>'a' <PERSON><PERSON>", "block.minecraft.seagrass": "bIQ'a' magh", "block.minecraft.set_spawn": "bog<PERSON><PERSON>'<PERSON><PERSON> cher<PERSON>'", "block.minecraft.short_dry_grass": "runbogh magh QaD", "block.minecraft.short_grass": "magh run", "block.minecraft.shroomlight": "'atlhqam wovmoHwI'", "block.minecraft.shulker_box": "shulker ngaSwI'", "block.minecraft.skeleton_skull": "nobmeD DughrI'", "block.minecraft.skeleton_wall_skull": "nobmeD tlhoy' DughrI'", "block.minecraft.slime_block": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>", "block.minecraft.small_amethyst_bud": "choSom nguv ghubHom", "block.minecraft.small_dripleaf": "por tom mach", "block.minecraft.smithing_table": "mitlhmeH raS", "block.minecraft.smoker": "tlhIch vIncha'", "block.minecraft.smooth_basalt": "<PERSON><PERSON><PERSON>'", "block.minecraft.smooth_quartz": "cho<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_quartz_slab": "choSom <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.smooth_quartz_stairs": "choSom Hab letlh", "block.minecraft.smooth_red_sandstone": "<PERSON>bbogh Do'o<PERSON> nagh <PERSON>q", "block.minecraft.smooth_red_sandstone_slab": "Habbogh Do'ol nagh Doq bID ngogh", "block.minecraft.smooth_red_sandstone_stairs": "<PERSON><PERSON><PERSON><PERSON> Do'o<PERSON> nagh Do<PERSON> letlh", "block.minecraft.smooth_sandstone": "Do'ol nagh Hab", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON>ol nagh <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.smooth_sandstone_stairs": "Do'ol nagh Hab letlh", "block.minecraft.smooth_stone": "nagh Hab", "block.minecraft.smooth_stone_slab": "nagh <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.sniffer_egg": "larghwI' QIm", "block.minecraft.snow": "chal chuch", "block.minecraft.snow_block": "chal chuch buq'Ir", "block.minecraft.soul_campfire": "qa' raQ qul", "block.minecraft.soul_fire": "qa' qul", "block.minecraft.soul_lantern": "qa' wovmoHwI'", "block.minecraft.soul_sand": "qa' Do'ol", "block.minecraft.soul_soil": "qa' lam", "block.minecraft.soul_torch": "qa' <PERSON>ch", "block.minecraft.soul_wall_torch": "qa' t<PERSON><PERSON><PERSON>' <PERSON>ch", "block.minecraft.spawn.not_valid": "<PERSON><PERSON><PERSON> bogh<PERSON>'meH jan <PERSON>'pu' bogh ghap tu'lu'be', pagh wa<PERSON>lu'", "block.minecraft.spawner": "t<PERSON><PERSON><PERSON><PERSON> boghmoHwI'", "block.minecraft.spawner.desc1": "boghmoHbogh QIm lo'lu'DI':", "block.minecraft.spawner.desc2": "yagh Segh choH", "block.minecraft.sponge": "va'chum", "block.minecraft.spore_blossom": "SanmIr 'InSong", "block.minecraft.spruce_button": "qIrqoD leQ", "block.minecraft.spruce_door": "qIrqoD lojmIt", "block.minecraft.spruce_fence": "qIrqo<PERSON>", "block.minecraft.spruce_fence_gate": "qIrqoD <PERSON>h lojmIt", "block.minecraft.spruce_hanging_sign": "qIrqoD QIn 'echlet tlhep", "block.minecraft.spruce_leaves": "qIrqoD pormey", "block.minecraft.spruce_log": "qIrqoD <PERSON>'a'", "block.minecraft.spruce_planks": "qIrqo<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "qIrqoD Surmen 'echlet", "block.minecraft.spruce_sapling": "qIrqoD SorHom", "block.minecraft.spruce_sign": "qIrqoD QIn 'echlet", "block.minecraft.spruce_slab": "qIrqoD bu<PERSON>'<PERSON>r bID", "block.minecraft.spruce_stairs": "qIrqoD letlh", "block.minecraft.spruce_trapdoor": "qIrqoD <PERSON> lojmIt", "block.minecraft.spruce_wall_hanging_sign": "qIrqoD tlhoy' QIn 'echlet tlhep", "block.minecraft.spruce_wall_sign": "qIrqoD tlhoy' QIn 'echlet", "block.minecraft.spruce_wood": "qIrqoD So<PERSON>", "block.minecraft.sticky_piston": "ghunglItl<PERSON>", "block.minecraft.stone": "nagh", "block.minecraft.stone_brick_slab": "nagh ng<PERSON><PERSON><PERSON> bu<PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.stone_brick_stairs": "nagh ngogh letlh", "block.minecraft.stone_brick_wall": "nagh ngoghmey tlhoy'", "block.minecraft.stone_bricks": "nagh ng<PERSON><PERSON>ey", "block.minecraft.stone_button": "nagh leQ", "block.minecraft.stone_pressure_plate": "nagh Surmen 'echlet", "block.minecraft.stone_slab": "nagh bu<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.stone_stairs": "nagh letlh", "block.minecraft.stonecutter": "nagh pe'wI'", "block.minecraft.stripped_acacia_log": "'aq<PERSON>'Sa <PERSON>'a' <PERSON>en", "block.minecraft.stripped_acacia_wood": "'a<PERSON><PERSON>'<PERSON>", "block.minecraft.stripped_bamboo_block": "b<PERSON><PERSON><PERSON> <PERSON><PERSON> bu<PERSON>'<PERSON>r", "block.minecraft.stripped_birch_log": "ber<PERSON><PERSON>", "block.minecraft.stripped_birch_wood": "ber<PERSON><PERSON>", "block.minecraft.stripped_cherry_log": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_crimson_hyphae": "nether <PERSON><PERSON>", "block.minecraft.stripped_crimson_stem": "nether <PERSON><PERSON><PERSON>", "block.minecraft.stripped_dark_oak_log": "'<PERSON><PERSON> <PERSON><PERSON><PERSON>'a' <PERSON>en", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.stripped_jungle_log": "ngem yIQ <PERSON>", "block.minecraft.stripped_jungle_wood": "ngem yIQ <PERSON>", "block.minecraft.stripped_mangrove_log": "<PERSON><PERSON><PERSON>' <PERSON>", "block.minecraft.stripped_mangrove_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_oak_log": "'oq <PERSON><PERSON>'a' <PERSON>en", "block.minecraft.stripped_oak_wood": "'oq <PERSON><PERSON>", "block.minecraft.stripped_pale_oak_log": "'oq wov <PERSON><PERSON>'a' <PERSON>en", "block.minecraft.stripped_pale_oak_wood": "'oq wov <PERSON><PERSON><PERSON>", "block.minecraft.stripped_spruce_log": "qIrqo<PERSON>", "block.minecraft.stripped_spruce_wood": "qIrqo<PERSON>", "block.minecraft.stripped_warped_hyphae": "nether Sor SuD <PERSON>'me<PERSON>en", "block.minecraft.stripped_warped_stem": "nether Sor <PERSON>", "block.minecraft.structure_block": "qach bu<PERSON>'<PERSON>r", "block.minecraft.structure_void": "q<PERSON> 'a<PERSON><PERSON><PERSON>", "block.minecraft.sugar_cane": "SIyech", "block.minecraft.sunflower": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.suspicious_gravel": "yetmoS nub", "block.minecraft.suspicious_sand": "Do'ol nub", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tall_dry_grass": "wochbogh magh QaD", "block.minecraft.tall_grass": "magh woch", "block.minecraft.tall_seagrass": "bIQ'a' magh woch", "block.minecraft.target": "DoS", "block.minecraft.terracotta": "nagh ghun", "block.minecraft.test_block": "qa<PERSON> b<PERSON><PERSON><PERSON><PERSON>r", "block.minecraft.test_instance_block": "Test Instance Block", "block.minecraft.tinted_glass": "'al'on nguv", "block.minecraft.tnt": "jorwI'", "block.minecraft.tnt.disabled": "jor jorwI' net chu'Ha'", "block.minecraft.torch": "Se<PERSON>", "block.minecraft.torchflower": "<PERSON><PERSON> <PERSON>", "block.minecraft.torchflower_crop": "Sech 'InSong San'emDer", "block.minecraft.trapped_chest": "vonwI' DerlIq", "block.minecraft.trial_spawner": "qaD boghmoHwI'", "block.minecraft.tripwire": "jemmeH SIrgh", "block.minecraft.tripwire_hook": "tangmoHmeH SIrgh SIrIl", "block.minecraft.tube_coral": "'och 'oqe'", "block.minecraft.tube_coral_block": "'och 'oqe' buq'Ir", "block.minecraft.tube_coral_fan": "'och 'oqe' mIj<PERSON>ang", "block.minecraft.tube_coral_wall_fan": "'och 'oqe' tlhoy' mIjDang", "block.minecraft.tuff": "tipqan nagh", "block.minecraft.tuff_brick_slab": "tIpqan nagh ngogh bu<PERSON><PERSON><PERSON> bID", "block.minecraft.tuff_brick_stairs": "tIpqan nagh ngogh letlh", "block.minecraft.tuff_brick_wall": "tIpqan nagh ngogh tlhoy'", "block.minecraft.tuff_bricks": "tIpqan nagh ngoghmey", "block.minecraft.tuff_slab": "tIpqan nagh bu<PERSON><PERSON><PERSON> bID", "block.minecraft.tuff_stairs": "tIpqan nagh letlh", "block.minecraft.tuff_wall": "tIpqan nagh tlhoy'", "block.minecraft.turtle_egg": "la'SIv QIm", "block.minecraft.twisting_vines": "tlhegh tI ver", "block.minecraft.twisting_vines_plant": "tlhegh tI ver San'emDer", "block.minecraft.vault": "qengHoD bu<PERSON>'<PERSON>r", "block.minecraft.verdant_froglight": "mabeb wovmoHwI' SuDqu'", "block.minecraft.vine": "tlhegh tI", "block.minecraft.void_air": "'ach<PERSON><PERSON> rewve'", "block.minecraft.wall_torch": "tl<PERSON><PERSON>' <PERSON>ch", "block.minecraft.warped_button": "nether Sor SuD leQ", "block.minecraft.warped_door": "nether Sor SuD lojmIt", "block.minecraft.warped_fence": "nether Sor SuD tlhoy'Hom", "block.minecraft.warped_fence_gate": "nether Sor SuD tlhoy'Hom lojmIt", "block.minecraft.warped_fungus": "'<PERSON><PERSON><PERSON><PERSON><PERSON> SuD", "block.minecraft.warped_hanging_sign": "nether Sor SuD QIn 'echlet tlhep", "block.minecraft.warped_hyphae": "nether Sor SuD <PERSON>'mey", "block.minecraft.warped_nylium": "nether magh SuD", "block.minecraft.warped_planks": "nether Sor <PERSON>", "block.minecraft.warped_pressure_plate": "nether Sor SuD Surmen 'echlet", "block.minecraft.warped_roots": "'<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_sign": "nether Sor SuD QIn 'echlet", "block.minecraft.warped_slab": "nether Sor Su<PERSON> bu<PERSON>'<PERSON>r bID", "block.minecraft.warped_stairs": "nether Sor Su<PERSON> letlh", "block.minecraft.warped_stem": "nether Sor <PERSON>", "block.minecraft.warped_trapdoor": "nether Sor <PERSON> lojmIt", "block.minecraft.warped_wall_hanging_sign": "nether Sor SuD tlhoy' QIn 'echlet tlhep", "block.minecraft.warped_wall_sign": "nether Sor SuD tlhoy' QIn 'echlet", "block.minecraft.warped_wart_block": "<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> SuD bu<PERSON>'<PERSON>r", "block.minecraft.water": "bIQ", "block.minecraft.water_cauldron": "bIQ bargh'a'", "block.minecraft.waxed_chiseled_copper": "So<PERSON><PERSON><PERSON>'e' nan<PERSON>'bogh 'ej velbogh req", "block.minecraft.waxed_copper_block": "<PERSON><PERSON><PERSON><PERSON> buq'<PERSON>r'<PERSON>' ve<PERSON><PERSON><PERSON> req", "block.minecraft.waxed_copper_bulb": "Sorpuq wovmoHwI''e' velbogh req", "block.minecraft.waxed_copper_door": "Sorpuq lojmIt'e' velbogh req", "block.minecraft.waxed_copper_grate": "Sorpuq neng'el'e' velbogh req", "block.minecraft.waxed_copper_trapdoor": "<PERSON><PERSON><PERSON><PERSON> von<PERSON> lojmIt'e' velbogh req", "block.minecraft.waxed_cut_copper": "Sorpuq'e' pe'lu'pu'bogh 'ej velbogh req", "block.minecraft.waxed_cut_copper_slab": "Sorpuq pe'lu'pu'bogh buq'<PERSON>r b<PERSON>'e' vel<PERSON>gh req", "block.minecraft.waxed_cut_copper_stairs": "Sorpuq pe'lu'pu'bogh letlh'e' velbogh req", "block.minecraft.waxed_exposed_chiseled_copper": "raghchoHbogh Sorpuq'e' nanlu'pu'bogh 'ej velbogh req", "block.minecraft.waxed_exposed_copper": "raghchoHbogh Sorpuq'e' velbogh req", "block.minecraft.waxed_exposed_copper_bulb": "raghchoHbogh Sorpuq wovmoHwI''e' velbogh req", "block.minecraft.waxed_exposed_copper_door": "raghchoHbogh Sorpuq lojmIt'e' velbogh req", "block.minecraft.waxed_exposed_copper_grate": "req rag<PERSON>cho<PERSON>bogh Sorpuq neng'el", "block.minecraft.waxed_exposed_copper_trapdoor": "req raghchoH<PERSON><PERSON> Sorpuq vonmeH lojmIt", "block.minecraft.waxed_exposed_cut_copper": "req ragh<PERSON><PERSON>gh Sorpuq pe'lu'bogh", "block.minecraft.waxed_exposed_cut_copper_slab": "req <PERSON><PERSON><PERSON><PERSON> Sorpuq pe'lu'bogh bu<PERSON>'<PERSON>r bID", "block.minecraft.waxed_exposed_cut_copper_stairs": "req raghcho<PERSON><PERSON>gh Sorpuq pe'lu'pu'bogh letlh", "block.minecraft.waxed_oxidized_chiseled_copper": "req ragh<PERSON>'bogh Sorpuq nanlu'bogh", "block.minecraft.waxed_oxidized_copper": "req rag<PERSON>bogh <PERSON>", "block.minecraft.waxed_oxidized_copper_bulb": "req raghchu'bogh Sorpuq wovmoHwI'", "block.minecraft.waxed_oxidized_copper_door": "req ragh<PERSON><PERSON>bogh Sorpuq lojmIt", "block.minecraft.waxed_oxidized_copper_grate": "req ragh<PERSON>'bogh Sorpuq neng'el", "block.minecraft.waxed_oxidized_copper_trapdoor": "req rag<PERSON><PERSON><PERSON>b<PERSON>h <PERSON><PERSON><PERSON><PERSON> lojmIt", "block.minecraft.waxed_oxidized_cut_copper": "req ragh<PERSON>'bogh Sorpuq pe'lu'pu'bogh", "block.minecraft.waxed_oxidized_cut_copper_slab": "req ragh<PERSON>'bogh Sorpuq pe'lu'pu'bogh bu<PERSON>'<PERSON>r b<PERSON>", "block.minecraft.waxed_oxidized_cut_copper_stairs": "req raghchu'bogh Sorpuq pe'lu'pu'bogh letlh", "block.minecraft.waxed_weathered_chiseled_copper": "req rag<PERSON><PERSON><PERSON> Sorpuq nan<PERSON>bogh", "block.minecraft.waxed_weathered_copper": "req <PERSON><PERSON><PERSON> Sorpuq", "block.minecraft.waxed_weathered_copper_bulb": "req rag<PERSON>H<PERSON>gh Sorpuq wovmoHwI'", "block.minecraft.waxed_weathered_copper_door": "req raghta<PERSON><PERSON><PERSON> Sorpuq lojmIt", "block.minecraft.waxed_weathered_copper_grate": "req rag<PERSON><PERSON>gh Sorpuq neng'el", "block.minecraft.waxed_weathered_copper_trapdoor": "req rag<PERSON><PERSON><PERSON><PERSON> Sorpuq vonmeH lojmIt", "block.minecraft.waxed_weathered_cut_copper": "req rag<PERSON><PERSON>gh Sorpuq pe'lu'pu'bogh", "block.minecraft.waxed_weathered_cut_copper_slab": "req <PERSON><PERSON><PERSON>gh Sorpuq pe'lu'pu'bogh bu<PERSON>'<PERSON>r bID", "block.minecraft.waxed_weathered_cut_copper_stairs": "req raghta<PERSON><PERSON>gh Sorpuq pe'lu'pu'bogh letlh", "block.minecraft.weathered_chiseled_copper": "raghtaHbogh Sorpuq nan<PERSON>'bogh", "block.minecraft.weathered_copper": "raghtaHbogh Sorpuq", "block.minecraft.weathered_copper_bulb": "raghtaHbogh Sorpuq wovmoHwI'", "block.minecraft.weathered_copper_door": "raghtaHbogh Sorpuq lojmIt", "block.minecraft.weathered_copper_grate": "raghtaHbogh Sorpuq neng'el", "block.minecraft.weathered_copper_trapdoor": "raghtaHbogh Sorpuq vonmeH lojmIt", "block.minecraft.weathered_cut_copper": "raghtaHbogh Sorpuq pe'lu'pu'bogh", "block.minecraft.weathered_cut_copper_slab": "raghtaHbogh Sorpuq pe'lu'pu'bogh bu<PERSON>'<PERSON>r bID", "block.minecraft.weathered_cut_copper_stairs": "raghtaHbogh Sorpuq pe'lu'pu'bogh letlh", "block.minecraft.weeping_vines": "tlhegh tI Saqbogh", "block.minecraft.weeping_vines_plant": "tlhegh tI Saqbogh San'emDer", "block.minecraft.wet_sponge": "va'chum yIQ", "block.minecraft.wheat": "wevpev San'emDer", "block.minecraft.white_banner": "joqwI' chIS", "block.minecraft.white_bed": "QongDaq chIS", "block.minecraft.white_candle": "weQ chIS", "block.minecraft.white_candle_cake": "weQ ch<PERSON> ghajbogh chab'a''e'", "block.minecraft.white_carpet": "tlhIm chIS", "block.minecraft.white_concrete": "tungyen chIS", "block.minecraft.white_concrete_powder": "tungyen Say'qIS chIS", "block.minecraft.white_glazed_terracotta": "chISbogh HaSreH nagh ghun", "block.minecraft.white_shulker_box": "shulker ngaSwI' chIS", "block.minecraft.white_stained_glass": "'al'on chIS", "block.minecraft.white_stained_glass_pane": "'al'on 'echlet chIS", "block.minecraft.white_terracotta": "chISbogh nagh ghun", "block.minecraft.white_tulip": "tu'lIp chIS", "block.minecraft.white_wool": "veD chIS", "block.minecraft.wildflowers": "'InSongmey tlhab", "block.minecraft.wither_rose": "wither ro'Sa'", "block.minecraft.wither_skeleton_skull": "wither nobmeD DughrI'", "block.minecraft.wither_skeleton_wall_skull": "wither nobmeD tlhoy' DughrI'", "block.minecraft.yellow_banner": "SuDbogh joqwI' 'ej wovqu'bogh", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON><PERSON>ej wovq<PERSON>'bogh", "block.minecraft.yellow_candle": "SuDbogh weQ 'ej wovqu'bogh", "block.minecraft.yellow_candle_cake": "SuDbogh weQ 'ej wovqu'bogh ghajbogh chab'a''e'", "block.minecraft.yellow_carpet": "SuDbogh tlhIm 'ej wovqu'bogh", "block.minecraft.yellow_concrete": "SuD<PERSON>gh tungyen 'ej wovqu'bogh", "block.minecraft.yellow_concrete_powder": "SuDbogh tungyen Say'qIS 'ej wovqu'bogh", "block.minecraft.yellow_glazed_terracotta": "SuDbogh HaSreH nagh ghun 'ej wovqu'bogh", "block.minecraft.yellow_shulker_box": "SuDbogh shulker ngaSwI' 'ej wovqu'bogh", "block.minecraft.yellow_stained_glass": "'al'on SuD 'ej wovqu'", "block.minecraft.yellow_stained_glass_pane": "'al'on 'echlet SuD 'ej wovqu'", "block.minecraft.yellow_terracotta": "SuDbogh nagh ghun 'ej wovqu'bogh", "block.minecraft.yellow_wool": "SuDbogh veD 'ej wovqu'bogh", "block.minecraft.zombie_head": "yInbogh lom nach", "block.minecraft.zombie_wall_head": "yInbogh lom tlhoy' nach", "book.byAuthor": "sum %1$s", "book.edit.title": "paq DI'meH <PERSON>", "book.editTitle": "paq pong qon:", "book.finalizeButton": "qI' 'ej <PERSON>", "book.finalizeWarning": "yIqIm! paq <PERSON>o<PERSON>'chugh, <PERSON><PERSON><PERSON><PERSON>'laHbe'.", "book.generation.0": "mung", "book.generation.1": "wa'DIch velqa'", "book.generation.2": "velqa' velqa'", "book.generation.3": "vIrghlu'", "book.invalid.tag": "* paq per waS *", "book.pageIndicator": "[Page] %1$s vo' %2$s", "book.page_button.next": "tenwal veb", "book.page_button.previous": "tenwal vorgh", "book.sign.title": "paq tlhI'meH <PERSON>", "book.sign.titlebox": "per", "book.signButton": "qI'", "book.view.title": "paq leghmeH HaSta", "build.tooHigh": "%s 'oH chenmoHmeH patlh 'aqroS", "chat.cannotSend": "QInvetlh ngeHlaHbe'", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "jolmeH yIwIv", "chat.copy": "QaymeH taDaq qon", "chat.copy.click": "DachenmoHqa'meH yIwIv", "chat.deleted_marker": "QInvam <PERSON>'pu' turwI'", "chat.disabled.chain_broken": "<PERSON><PERSON> disabled due to broken chain. Please try reconnecting.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON> disabled due to expired profile public key. Please try reconnecting.", "chat.disabled.invalid_command_signature": "chenmoH yay'law' choraQ.", "chat.disabled.invalid_signature": "<PERSON><PERSON> had an invalid signature. Please try reconnecting.", "chat.disabled.launcher": "Chat disabled by launcher option. Cannot send message.", "chat.disabled.missingProfileKey": "<PERSON><PERSON> disabled due to missing profile public key. Please try reconnecting.", "chat.disabled.options": "Chat disabled in client options.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> received out-of-order. Did your system time change?", "chat.disabled.profile": "mach pong wInuDlaHbe'chugh. %% Bor: ghe''or'", "chat.disabled.profile.moreInfo": "Chat is not allowed by account settings. Cannot send or view messages.", "chat.editBox": "QIn pat", "chat.filtered": "QIb turwI'.", "chat.filtered_full": "The server has hidden your message for some players.", "chat.link.confirm": "'Internet Daqvam <PERSON>w' DaneHbej'a'?", "chat.link.confirmTrusted": "nelmeSvam DawIv pagh ghItlh QaymeH talIjvaD Daqon?", "chat.link.open": "'Internet jaH", "chat.link.warning": "vay' <PERSON><PERSON>qqu'be'chugh, 'Internet rarwI'mey <PERSON> tInaw'Qo'!", "chat.queue": "[+%s pending tlhegh]", "chat.square_brackets": "[%s]", "chat.tag.error": "Server sent invalid message.", "chat.tag.modified": "QIn choHpu' turwI'. QIn wa'DIch:", "chat.tag.not_secure": "Unverified message. Cannot be reported.", "chat.tag.system": "Server message. Cannot be reported.", "chat.tag.system_single_player": "turwI' QIn.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%2$s qaD naQmoH %1$s", "chat.type.advancement.goal": "%2$s ngoQ ta' %1$s", "chat.type.advancement.task": "%2$s chav chav %1$s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "mangghomvam'e' tIQum", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%2$s jatlh %1$s", "chat.validation_error": "Chat validation error", "chat_screen.message": "QIn DangeHbogh: %s", "chat_screen.title": "jawmeH jIH", "chat_screen.usage": "DangeHmeH QIn yIghItlh 'ej enter leQ yI'uy", "chunk.toast.checkLog": "latlh De' DaleghmeH QonoS yIlaD", "chunk.toast.loadFailure": "%sDaq puH 'ay' lI' 'e' luj", "chunk.toast.lowDiskSpace": "puS De' jengva' qawHaq chIm!", "chunk.toast.lowDiskSpace.description": "chaq qo' ngeqlaHbe'", "chunk.toast.saveFailure": "%sDaq puH 'ay' ngeq 'e' luj", "clear.failed.multiple": "No items were found on %s players", "clear.failed.single": "No items were found on player %s", "color.minecraft.black": "qIj", "color.minecraft.blue": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "color.minecraft.brown": "<PERSON><PERSON> 'ej wovbe'", "color.minecraft.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> 'ej wov", "color.minecraft.gray": "qIj 'ej wov", "color.minecraft.green": "SuDqu'", "color.minecraft.light_blue": "Su<PERSON> 'ej wov", "color.minecraft.light_gray": "qIj 'ej wovqu'", "color.minecraft.lime": "SuDqu' 'ej wovqu'", "color.minecraft.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> 'ej wov", "color.minecraft.orange": "<PERSON><PERSON> <PERSON><PERSON><PERSON> be<PERSON><PERSON>j rur", "color.minecraft.pink": "<PERSON><PERSON><PERSON><PERSON>' 'ej wovqu'", "color.minecraft.purple": "ghIrep Hurgh rur", "color.minecraft.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.white": "chIS", "color.minecraft.yellow": "<PERSON><PERSON> 'ej wovqu'", "command.context.here": "<--[HERE]", "command.context.parse_error": "%2$sDaq %1$s tu'lu': %3$s", "command.exception": "Could not parse command: %s", "command.expected.separator": "Expected whitespace to end one argument, but found trailing data", "command.failed": "ra'meH ghItlh turtaHvIS qaS Qagh jum", "command.forkLimit": "Maximum number of contexts (%s) reached", "command.unknown.argument": "ghunHa'lu' ra'meH ghItlh", "command.unknown.command": "<PERSON><PERSON><PERSON>'l<PERSON>'bogh <PERSON>' pagh incomplete ra', legh below Qagh", "commands.advancement.criterionNotFound": "%2$s poQbe' %1$s chav", "commands.advancement.grant.criterion.to.many.failure": "%3$s QujwI'vaD %1$s poQ %2$s 'e' qaSmoHlaHbe', wej lughaj<PERSON>'", "commands.advancement.grant.criterion.to.many.success": "%3$s QujwI'vaD %1$s poQ %2$s 'e' qaSmoHta'", "commands.advancement.grant.criterion.to.one.failure": "%3$svaD %1$s poQ %2$s 'e' qaSmoHlaHbe', wej ghajmo'", "commands.advancement.grant.criterion.to.one.success": "%3$svaD %1$s poQ %2$s 'e' qaSmoHta'", "commands.advancement.grant.many.to.many.failure": "%2$s QujwI'vaD %1$s chav noblaHbe', wej ghajmo'", "commands.advancement.grant.many.to.many.success": "%2$s QujwI'vaD %1$s chav nobpu'", "commands.advancement.grant.many.to.one.failure": "%2$svaD %1$s chav noblaHbe', wej ghajmo'", "commands.advancement.grant.many.to.one.success": "%2$svaD %1$s chav nobpu'", "commands.advancement.grant.one.to.many.failure": "%2$s QujwI'vaD %1$s chav noblaHbe', wej l<PERSON>aj<PERSON>'", "commands.advancement.grant.one.to.many.success": "%2$s QujwI'vaD %1$s chav nobpu'", "commands.advancement.grant.one.to.one.failure": "%2$svaD %1$s chav noblaHbe', wej ghajmo'", "commands.advancement.grant.one.to.one.success": "%2$svaD %1$s chav nobpu'", "commands.advancement.revoke.criterion.to.many.failure": "%3$s QujwI'vaD %1$s poQbe' %2$s 'e' qaSmoHlaHbe', wej lughaj<PERSON>'", "commands.advancement.revoke.criterion.to.many.success": "%3$s QujwI'vaD %1$s poQbe' %2$s 'e' qaSmoHta'", "commands.advancement.revoke.criterion.to.one.failure": "%3$svaD %1$s poQbe' %2$s 'e' qaSmoHlaHbe', wej ghajmo'", "commands.advancement.revoke.criterion.to.one.success": "%3$svaD %1$s poQbe' %2$s 'e' qaSmoHta'", "commands.advancement.revoke.many.to.many.failure": "%2$s QujwI'vo' %1$s chav nge'la<PERSON><PERSON>', wej <PERSON><PERSON><PERSON><PERSON>'", "commands.advancement.revoke.many.to.many.success": "%2$s QujwI'vo' %1$s chav nge'pu'", "commands.advancement.revoke.many.to.one.failure": "%2$svo' %1$s chav nge'la<PERSON><PERSON>', wej <PERSON><PERSON><PERSON>'", "commands.advancement.revoke.many.to.one.success": "%2$svo' %1$s chav nge'pu'", "commands.advancement.revoke.one.to.many.failure": "%2$s QujwI'vo' %1$s chav nge'la<PERSON><PERSON>', wej <PERSON><PERSON><PERSON><PERSON>'", "commands.advancement.revoke.one.to.many.success": "%2$s QujwI'vo' %1$s chav nge'pu'", "commands.advancement.revoke.one.to.one.failure": "%2$svo' %1$s chav nge'la<PERSON><PERSON>', wej <PERSON><PERSON><PERSON>'", "commands.advancement.revoke.one.to.one.success": "%2$svo' %1$s chav nge'pu'", "commands.attribute.base_value.get.success": "Waw' nIv attribute %s Dol %s ghaH %s", "commands.attribute.base_value.reset.success": "Base value for attribute %s for entity %s reset to default %s", "commands.attribute.base_value.set.success": "Waw' lo'laHghach attribute %s Dol %s HijmeH %s", "commands.attribute.failed.entity": "ra'meH ghItlhvaD Dol waS 'oH %s'e'", "commands.attribute.failed.modifier_already_present": "Modifier %s chonayta' SaH attribute %s Dol %s", "commands.attribute.failed.no_attribute": "Dol %s ghIq pagh attribute %s", "commands.attribute.failed.no_modifier": "Attribute %s Dol %s ghIq pagh modifier %s", "commands.attribute.modifier.add.success": "Boq modifier %s to attribute %s Dol %s", "commands.attribute.modifier.remove.success": "Modifier teq %s pa'vo' attribute %s Dol %s", "commands.attribute.modifier.value.get.success": "Niv modifier %s on attribute %s Dol %s ghaH %s", "commands.attribute.value.get.success": "Niv attribute %s Dol %s ghaH %s", "commands.ban.failed": "pagh choHta'. wejHa' QujwI' tuchlu'", "commands.ban.success": "%s ghImpu': %s", "commands.banip.failed": "pagh choH. wejHa' De'wI' ngu'meH mI'vetlh tuch", "commands.banip.info": "This ban affects %s player(s): %s", "commands.banip.invalid": "De'wI' ngu'meH mI' waS pagh QujwI' Sovbe'lu'bogh", "commands.banip.success": "De'wI' mI' %s tuchlu': %s", "commands.banlist.entry": "%s tuchta' %s: %s", "commands.banlist.entry.unknown": "(Sovbe'lu')", "commands.banlist.list": "There are %s ban(s):", "commands.banlist.none": "pagh QujwI tuchlu'", "commands.bossbar.create.failed": "A bossbar already exists with the ID '%s'", "commands.bossbar.create.success": "pIn tlhlInbogh pe'eghmeH yamtaw chenmoHpu'", "commands.bossbar.get.max": "%2$s 'oH %1$s pIn mIvwa' mIr tlhIn 'aqroS'e'", "commands.bossbar.get.players.none": "lInbogh QujwI' ghajbe' %s pIn mIvwa' mIr tlhIn", "commands.bossbar.get.players.some": "Custom bossbar %s has %s player(s) currently online: %s", "commands.bossbar.get.value": "%2$s 'oH %1$s pIn mIvwa' mIr tlhIn mI''e'", "commands.bossbar.get.visible.hidden": "DaH pIn tlhlInbogh pe'eghmeH yamtaw So'lu'", "commands.bossbar.get.visible.visible": "DaH %s pIn mIvwa' mIr tlhIn lo'laH vay'", "commands.bossbar.list.bars.none": "pagh pIn tlhlInbogh pe'eghmeH yamtaw tu'lu'", "commands.bossbar.list.bars.some": "There are %s custom bossbar(s) active: %s", "commands.bossbar.remove.success": "pIn tlhlInbogh pe'eghmeH yamtaw teqpu'", "commands.bossbar.set.color.success": "jaS nguvchoH %s pIn mIvwa' mIr tlhIn", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON>'. vaj qeyl<PERSON> 'oH mIwvam'e'", "commands.bossbar.set.max.success": "%2$s moj %1$s pIn mIvwa' mIr tlhIn 'aqroS", "commands.bossbar.set.max.unchanged": "Nothing changed. That's already the max of this bossbar", "commands.bossbar.set.name.success": "%s pIn mIvwa' mIr tlhIn pongqa'ta'", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON>'. 'ej mIwvam'e' wIlo'bogh neH lutu'lu'mo'", "commands.bossbar.set.players.success.none": "DaH QujwI' ghajbe' %s pIn tlhlInbogh pe'eghmeH yamtaw", "commands.bossbar.set.players.success.some": "Custom bossbar %s now has %s player(s): %s", "commands.bossbar.set.players.unchanged": "<PERSON>gh <PERSON>'. vaj qo'vaD lo'laHbe'chugh vay'", "commands.bossbar.set.style.success": "%s pIn mIvwa' mIr tlhIn tu'qom choHta'", "commands.bossbar.set.style.unchanged": "Nothing changed. That's already the style of this bossbar", "commands.bossbar.set.value.success": "%2$s moj %1$s pIn mIvwa' mIr tlhIn mI'", "commands.bossbar.set.value.unchanged": "Nothing changed. That's already the value of this bossbar", "commands.bossbar.set.visibility.unchanged.hidden": "Nothing changed. The bossbar is already hidden", "commands.bossbar.set.visibility.unchanged.visible": "Nothing changed. The bossbar is already visible", "commands.bossbar.set.visible.success.hidden": "DaH %s pIn mIvwa' mIr tlhIn So'lu'", "commands.bossbar.set.visible.success.visible": "DaH %s pIn mIvwa' mIr tlhIn lo'laH vay'", "commands.bossbar.unknown": "pIn mIvwa'mey mIr'e' ponglu'bogh «%s» tu'lu'be'", "commands.clear.success.multiple": "%2$s QujwI'vo' %1$s Doch nge'pu'", "commands.clear.success.single": "QujwI' %2$svo' %1$s Doch nge'pu'", "commands.clear.test.multiple": "Found %s matching item(s) on %s players", "commands.clear.test.single": "Found %s matching item(s) on player %s", "commands.clone.failed": "DaH 'e'", "commands.clone.overlap": "The source and destination areas cannot overlap", "commands.clone.success": "Successfully cloned %s block(s)", "commands.clone.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.damage.invulnerable": "Target is invulnerable to the given damage type", "commands.damage.success": "Applied %s damage to %s", "commands.data.block.get": "%s on block %s, %s, %s after scale factor of %s is %s", "commands.data.block.invalid": "bu<PERSON>'<PERSON><PERSON><PERSON>' Do<PERSON> buq'Ir", "commands.data.block.modified": "%s, %s, %s buq'<PERSON><PERSON>'", "commands.data.block.query": "bu<PERSON><PERSON><PERSON><PERSON> ghaj %s, %s, %s: %s", "commands.data.entity.get": "%s on %s after scale factor of %s is %s", "commands.data.entity.invalid": "QujwI' De' choHlaHbe'", "commands.data.entity.modified": "%s <PERSON><PERSON> choHta'", "commands.data.entity.query": "%2$s yugh %1$s Dol De'", "commands.data.get.invalid": "%s SuqlaHbe'; mI' per neH chaw'", "commands.data.get.multiple": "wa' nbt mI' laj mIwvam", "commands.data.get.unknown": "%s SuqlaHbe', per tu'lu'be'", "commands.data.merge.failed": "ChoH pagh. chonayta' lo'laHwI'na' per bang", "commands.data.modify.expected_list": "tetlh pIH, 'ach %s Hev", "commands.data.modify.expected_object": "Doch pIH, 'ach %s Hev", "commands.data.modify.expected_value": "Expected value, got: %s", "commands.data.modify.invalid_index": "tetlh patlh waS", "commands.data.modify.invalid_substring": "Invalid substring indices: %s to %s", "commands.data.storage.get": "%s qaStaHvIS storage %s qaSpu'Di' ghISDen factor %s ghaH %s", "commands.data.storage.modified": "Modified storage %s", "commands.data.storage.query": "Storage %s chaq tlha' 'a ghIH: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "vey pong waS 'oH ‹%s›'e'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "Pack '%s' is not enabled!", "commands.datapack.disable.failed.feature": "vey ‹%s› chu'Ha'laHbe', joqwI' chu'lu'bogh 'ay' 'oHmo'!", "commands.datapack.enable.failed": "Pack '%s' is already enabled!", "commands.datapack.enable.failed.no_flags": "Pack '%s' cannot be enabled, since required flags are not enabled in this world: %s!", "commands.datapack.list.available.none": "DaH pagh <PERSON>' vey <PERSON>'la<PERSON>", "commands.datapack.list.available.success": "There are %s data pack(s) available: %s", "commands.datapack.list.enabled.none": "pagh De' vey chu'lu'", "commands.datapack.list.enabled.success": "There are %s data pack(s) enabled: %s", "commands.datapack.modify.disable": "%s De' <PERSON>y QotlhlI'", "commands.datapack.modify.enable": "%s De' vey chu'lI'", "commands.datapack.unknown": "<PERSON>' <PERSON>y Sov<PERSON>'lu'bogh 'oH «%s»'e'", "commands.debug.alreadyRunning": "wejHa' juv<PERSON><PERSON> Quj mIw juvwI'", "commands.debug.function.noRecursion": "mIw qoDvo' ghochlaHbe'", "commands.debug.function.noReturnRun": "Tracing can't be used with return run", "commands.debug.function.success.multiple": "Traced %s command(s) from %s functions to output file %s", "commands.debug.function.success.single": "Traced %s command(s) from function '%s' to output file %s", "commands.debug.function.traceFailed": "mIw ghoch 'e' luj", "commands.debug.notRunning": "juvchoHbe' Quj mIw juvwI'", "commands.debug.started": "<PERSON>uj mIw juvcho<PERSON>", "commands.debug.stopped": "qaSDI' %s lup %s Quj mIw je Quj mIw juvbe'choH (qaStaHvIS wa' lup qaS %s Quj mIw)", "commands.defaultgamemode.success": "DaH %s 'oH Quj lo' motlh'e'", "commands.deop.failed": "Pagh <PERSON>apbe'. ru'Ha'choHbe' HoSwI''e'", "commands.deop.success": "%s numHa'pu'; turwI' vu'wI' mojHa' ghaH", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "choHta'be' Qatlhghach. %s'e' 'oHtaH.", "commands.difficulty.query": "%s 'o<PERSON>", "commands.difficulty.success": "%s moj <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commands.drop.no_held_items": "Doch 'u<PERSON>laH<PERSON><PERSON>l", "commands.drop.no_loot_table": "qengHoD wa'chaw' Hutlh %s Dol", "commands.drop.no_loot_table.block": "qengHoD wa'chaw' Hutlh %s buq'Ir", "commands.drop.success.multiple": "%s Hoch chaghpu'", "commands.drop.success.multiple_with_table": "qengHoD wa'chaw %2$svo' %1$s Doch teqpu'", "commands.drop.success.single": "%s %s chaghpu'", "commands.drop.success.single_with_table": "%3$sqengHoD wa'chawvo' %1$s %2$s teqpu'", "commands.effect.clear.everything.failed": "Target has no effects to remove", "commands.effect.clear.everything.success.multiple": "%s yaghvo' Hoch wanI' teqpu'", "commands.effect.clear.everything.success.single": "%svo' Hoch wanI' nge'ta'", "commands.effect.clear.specific.failed": "Target doesn't have the requested effect", "commands.effect.clear.specific.success.multiple": "%2$s yaghvo' %1$s wanI' teqpu'", "commands.effect.clear.specific.success.single": "%2$svo' %1$s wanI' nge'ta'", "commands.effect.give.failed": "Unable to apply this effect (target is either immune to effects, or has something stronger)", "commands.effect.give.success.multiple": "%2$s yaghvaD %1$s wanI' nobpu'", "commands.effect.give.success.single": "%2$svaD %1$s wanI' nobta'", "commands.enchant.failed": "Nothing changed. Targets either have no item in their hands or the enchantment could not be applied", "commands.enchant.failed.entity": "%s is not a valid entity for this command", "commands.enchant.failed.incompatible": "%s cannot support that enchantment", "commands.enchant.failed.itemless": "Doch 'uchbe' %s", "commands.enchant.failed.level": "%s is higher than the maximum level of %s supported by that enchantment", "commands.enchant.success.multiple": "%2$s DolvaD %1$s tlheH chelpu'", "commands.enchant.success.single": "%2$s DochvaD %1$s tlheH chelta'", "commands.execute.blocks.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.execute.conditional.fail": "qaD lujta'", "commands.execute.conditional.fail_count": "qaD lujta', mIvwa': %s", "commands.execute.conditional.pass": "qa<PERSON>'", "commands.execute.conditional.pass_count": "qaD <PERSON>', mIvwa': %s", "commands.execute.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.experience.add.levels.success.multiple": "%2$s QujwI'vaD %1$s ta' patlh nobta'", "commands.experience.add.levels.success.single": "%2$svaD %1$s ta' patlh nobta'", "commands.experience.add.points.success.multiple": "%2$s QujwI'vaD %1$s ta' mIvwa' nobta'", "commands.experience.add.points.success.single": "%2$svaD %1$s ta' mIvwa' nobta'", "commands.experience.query.levels": "%2$s 'oH %1$s ta' patlh'e'", "commands.experience.query.points": "%2$s ta' mIvwa' ghaj %1$s", "commands.experience.set.levels.success.multiple": "%2$s QujwI'vaD %1$s 'oHchoH ta' patlh''e' 'e' qaSmoHta'", "commands.experience.set.levels.success.single": "%2$svaD %1$s 'oHchoH ta' patlh''e' 'e' qaSmoHta'", "commands.experience.set.points.invalid": "QujwI' patlh 'aqroS mIvwa' juSlaHbe' HenmoHmeH mIvwa'", "commands.experience.set.points.success.multiple": "%2$s QujwI'vaD %1$s 'oHchoH ta' mIvwa' mI''e' 'e' qaSmoHta'", "commands.experience.set.points.success.single": "%2$svaD %1$s 'oHchoH ta' mIvwa' mI''e' 'e' qaSmoHta'", "commands.fill.failed": "pagh buq'Ir teblu'pu'", "commands.fill.success": "Successfully filled %s block(s)", "commands.fill.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.fillbiome.success": "Biomes set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.success.count": "%s biome entry/entries set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.toobig": "Too many blocks in the specified volume (maximum %s, specified %s)", "commands.forceload.added.failure": "pe'vIl lI'meH ghItlh pagh puH 'ay'", "commands.forceload.added.multiple": "%2$sDaq pe'vIl lI'meH %1$s puH 'ay' ghitlhpu'; chuq luchen %3$s %4$s je", "commands.forceload.added.none": "%sDaq pagh puH 'ay' pe'vIl lI'lu'bogh tu'lu'", "commands.forceload.added.single": "pe'vIl lI'meH ghItlh %s %sDaq puH 'ay'", "commands.forceload.list.multiple": "%2$sDaq %3$sDaq %1$s puH 'ay' pe'vIl lI'lu'bogh tu'lu'", "commands.forceload.list.single": "%sDaq puH 'ay' pe'vIl lI'lu'bogh tu'lu': %s 'oH Daq'e'", "commands.forceload.query.failure": "%1$sDaq %2$sDaq pe'vIl lI'meH puH 'ay' perlu'be'", "commands.forceload.query.success": "%1$sDaq %2$sDaq pe'vIl lI'meH puH 'ay' perlu'", "commands.forceload.removed.all": "%sDaq Hoch puH 'ay' pe'vIl lI'lu'bogh perHa'ta'", "commands.forceload.removed.failure": "pe'vIl lI'bogh mIwvo' pagh puH 'ay' nge'ta'", "commands.forceload.removed.multiple": "%2$sDaq pe'vIl lI'meH %1$s puH 'ay' ghitlhHa'pu'; chuq luchen %3$s %4$s je", "commands.forceload.removed.single": "%2$sDaq pe'vIl lI'meH %1$s puH 'ay' perHa'ta'", "commands.forceload.toobig": "Daq perlu' puH 'ay' 'Iq tu'lu' (%s 'oH 'aqroS, %s perlu')", "commands.function.error.argument_not_compound": "Invalid argument type: %s, expected Compound", "commands.function.error.missing_argument": "Missing argument %2$s to function %1$s", "commands.function.error.missing_arguments": "Missing arguments to function %s", "commands.function.error.parse": "While instantiating macro %s: Command '%s' caused error: %s", "commands.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.function.result": "Function %s returned %s", "commands.function.scheduled.multiple": "Running functions %s", "commands.function.scheduled.no_functions": "Can't find any functions for name %s", "commands.function.scheduled.single": "Running function %s", "commands.function.success.multiple": "Executed %s command(s) from %s functions", "commands.function.success.multiple.result": "Executed %s functions", "commands.function.success.single": "Executed %s command(s) from function '%s'", "commands.function.success.single.result": "Function '%2$s' returned %1$s", "commands.gamemode.success.other": "cher %s<PERSON><PERSON> %s", "commands.gamemode.success.self": "cher ghaj <PERSON><PERSON><PERSON> %s", "commands.gamerule.query": "DaH %2$s 'oH %1$s Quj chut Dotlh'e'", "commands.gamerule.set": "DaH %2$s 'oH %1$s Quj chut Dotlh'e'", "commands.give.failed.toomanyitems": "%2$s tu'lu'DI', %1$s neH noblaH", "commands.give.success.multiple": "%3$s QujwI'vaD %$1s %2$s nobpu'", "commands.give.success.single": "%3$svaD %$1s %2$s nobpu'", "commands.help.failed": "ra'meH ghItlh Sovbe'lu'bogh ghap chaw' yapbe'", "commands.item.block.set.success": "%s, %s, %sDaq %s lanta'", "commands.item.entity.set.success.multiple": "Replaced a slot on %s entities with %s", "commands.item.entity.set.success.single": "Replaced a slot on %s with %s", "commands.item.source.no_such_slot": "%s <PERSON><PERSON>", "commands.item.source.not_a_container": "ngaSwI' 'oHbe' %s, %s, %s <PERSON>'", "commands.item.target.no_changed.known_item": "%2$s DaqvaD %1$s Doch lajpu' pagh DoS", "commands.item.target.no_changes": "%s DaqvaD <PERSON> la<PERSON>agh DoS", "commands.item.target.no_such_slot": "%s <PERSON><PERSON>", "commands.item.target.not_a_container": "ngaSwI' 'oHbe' %s, %s, %s DoS Daq'e'", "commands.jfr.dump.failed": "jfr <PERSON>' <PERSON> 'e' luj: %s", "commands.jfr.start.failed": "juvcho<PERSON> 'e' luj jfr", "commands.jfr.started": "juvchoH jfr", "commands.jfr.stopped": "juvbe'choH jfr 'ej %sDaq <PERSON>' lablu'", "commands.kick.owner.failed": "qaStaHvIS SIr'o' Sum Quj turwI' ghajwI' vujlaHbe'", "commands.kick.singleplayer.failed": "lInbe'bogh wa' QujwI' Quj QujtaHvIS vujlaHbe'", "commands.kick.success": "%s vujpu': %s", "commands.kill.success.multiple": "%s Dol HoH", "commands.kill.success.single": "%s HoH", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Tu'lu' %s max %s chu'wI' online: %s", "commands.locate.biome.not_found": "\"%s\" Segh 'umber nejqu'pu', 'ach tu'lu'be'.", "commands.locate.biome.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.poi.not_found": "<PERSON>gh «%s» Daq <PERSON>j tu'lu'be'", "commands.locate.poi.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.structure.invalid": "Segh \"%s\" ghajbogh qach tu'lu'be'", "commands.locate.structure.not_found": "na<PERSON><PERSON> \"%s\" ghajbogh qach tu'lu'be'", "commands.locate.structure.success": "%2$sDaq %1$s Sum law' Hoch Sum puS (chuq luchen %3$s buq'Ir)", "commands.message.display.incoming": "Dutlhup %s: «%s»", "commands.message.display.outgoing": "%svaD Dathlup: «%s»", "commands.op.failed": "Nothing changed. The player already is an operator", "commands.op.success": "%s numpu'; turwI' vu'wI' moj ghaH", "commands.pardon.failed": "Nothing changed. The player isn't banned", "commands.pardon.success": "%s ghImHa'pu'", "commands.pardonip.failed": "pagh choH. tuchlu'be' De'wI' ngu'meH mI'vetlh", "commands.pardonip.invalid": "De'wI' ngu'meH mI' waS", "commands.pardonip.success": "De'wI' ngu'meH mI' %s tuchHa'pu'", "commands.particle.failed": "pay'an leghlaH pagh", "commands.particle.success": "%s pay'an 'aghtaH", "commands.perf.alreadyRunning": "wejHa' juvchoH laH juvwI'", "commands.perf.notRunning": "juvchoHbe' laH juvwI'", "commands.perf.reportFailed": "QaghHa'wI' ja'meH ghItlh chenmoH 'e' lujta'", "commands.perf.reportSaved": "<PERSON>agh ja'ghach chenmo<PERSON>lu' %sDaq", "commands.perf.started": "qaStaHvIS wa'maH lup laH juv (DaghangmeH /perf stop yIlo')", "commands.perf.stopped": "Stopped performance profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.place.feature.failed": "Daq noy lan 'e' QapHa'", "commands.place.feature.invalid": "Segh \"%s\" ghajbogh Daq noy tu'lu'be'", "commands.place.feature.success": " %2$s, %3$s, %4$sDaq \"%1$s\" lanlu'", "commands.place.jigsaw.failed": "Failed to generate jigsaw", "commands.place.jigsaw.invalid": "There is no template pool with type \"%s\"", "commands.place.jigsaw.success": "Generated jigsaw at %s, %s, %s", "commands.place.structure.failed": "Failed to place structure", "commands.place.structure.invalid": "There is no structure with type \"%s\"", "commands.place.structure.success": "Generated structure \"%s\" at %s, %s, %s", "commands.place.template.failed": "Failed to place template", "commands.place.template.invalid": "There is no template with id \"%s\"", "commands.place.template.success": "Loaded template \"%s\" at %s, %s, %s", "commands.playsound.failed": "The sound is too far away to be heard", "commands.playsound.success.multiple": "%2$s QujwI'vaD %1$s wab muchpu'", "commands.playsound.success.single": "%2$svaD %1$s wab muchpu'", "commands.publish.alreadyPublished": "wejHa' %s<PERSON><PERSON> QujwI' law'", "commands.publish.failed": "<PERSON>u<PERSON> <PERSON> cher<PERSON>'", "commands.publish.started": "He %s<PERSON>aq <PERSON>'", "commands.publish.success": "DaH %s<PERSON>aq <PERSON>u<PERSON>laH QujwI' law'", "commands.random.error.range_too_large": "The range of the random value must be at most 2147483646", "commands.random.error.range_too_small": "The range of the random value must be at least 2", "commands.random.reset.all.success": "Reset %s random sequence(s)", "commands.random.reset.success": "Reset random sequence %s", "commands.random.roll": "%s rolled %s (from %s to %s)", "commands.random.sample.success": "Randomized value: %s", "commands.recipe.give.failed": "pagh mIw chu' ghojpu'", "commands.recipe.give.success.multiple": "%2$sQujwI'vaD %s mIw ngaQHa'moHpu'", "commands.recipe.give.success.single": "%2$svaD %s mIw ngaQHa'moHpu'", "commands.recipe.take.failed": "pagh mIw lIjlaH", "commands.recipe.take.success.multiple": "%2$s QujwI'vo' %s mIw tlhappu'", "commands.recipe.take.success.single": "%2$svo' %s mIw tlhappu'", "commands.reload.failure": "lI'qa' 'e' luj; <PERSON>' ngo' pol", "commands.reload.success": "lI'qa'taH!", "commands.ride.already_riding": "%s is already riding %s", "commands.ride.dismount.success": "%s stopped riding %s", "commands.ride.mount.failure.cant_ride_players": "QujwI' lItlaHbe' vay'", "commands.ride.mount.failure.generic": "%s couldn't start riding %s", "commands.ride.mount.failure.loop": "Can't mount entity on itself or any of its passengers", "commands.ride.mount.failure.wrong_dimension": "Can't mount entity in different dimension", "commands.ride.mount.success": "%s started riding %s", "commands.ride.not_riding": "%s is not riding any vehicle", "commands.rotate.success": "%s jIrmoH", "commands.save.alreadyOff": "wej qonmeH DuH chu'Ha'lu'", "commands.save.alreadyOn": "wech qonmeH DuH chu'lu'", "commands.save.disabled": "DaH qon De'wI' 'e' Qotlh", "commands.save.enabled": "DaH ngeq De'wI' 'e' chu'lu'", "commands.save.failed": "Quj ngeqlaHbe' (qawHaq yap lo'laH'a' Quj?)", "commands.save.saving": "Quj ngeqlI' (chaq nI' mIwvam!)", "commands.save.success": "Quj ngeqta'", "commands.schedule.cleared.failure": "Pagh schedules id %s", "commands.schedule.cleared.success": "Removed %s schedule(s) with id %s", "commands.schedule.created.function": "Scheduled function '%s' in %s tick(s) at gametime %s", "commands.schedule.created.tag": "qaSDI' %2$s Quj mIw 'ej %3$s 'oHDI' Quj poH'e' %1$s per lo' 'e' cherta'", "commands.schedule.macro": "Can't schedule a macro", "commands.schedule.same_tick": "mIwHomvammeH nablaHbe'", "commands.scoreboard.objectives.add.duplicate": "wej pong<PERSON><PERSON>h ghaj la<PERSON>h <PERSON>", "commands.scoreboard.objectives.add.success": "ngoQ chu' %s chenmo<PERSON><PERSON>'", "commands.scoreboard.objectives.display.alreadyEmpty": "pagh choHta'. wejHa' chIm bey'vetlh", "commands.scoreboard.objectives.display.alreadySet": "pagh choHta'. wejHa' ngoQvetlh cha' bey'vetlh.", "commands.scoreboard.objectives.display.cleared": "%s bey'vo' ngoQ nge'ta'", "commands.scoreboard.objectives.display.set": "%2$s ngoQ cha' %1$s bey' 'e' cherta'", "commands.scoreboard.objectives.list.empty": "ngoQ tu'lu'be'", "commands.scoreboard.objectives.list.success": "There are %s objective(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Enabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayname": "Bey' pong choH %s to %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Cleared default number format of objective %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Changed default number format of objective %s", "commands.scoreboard.objectives.modify.rendertype": "'Im <PERSON> objective choH %s", "commands.scoreboard.objectives.remove.success": "ngoQ %s <PERSON><PERSON>'lu'", "commands.scoreboard.players.add.success.multiple": "%3$sDolvaD %2$sDaq %1$s chelta'", "commands.scoreboard.players.add.success.single": "%3$svaD %2$sDaq %1$s chelta' (DaH %4$s 'oH)", "commands.scoreboard.players.display.name.clear.success.multiple": "Cleared display name for %s entities in %s", "commands.scoreboard.players.display.name.clear.success.single": "Cleared display name for %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Changed display name to %s for %s entities in %s", "commands.scoreboard.players.display.name.set.success.single": "Changed display name to %s for %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Cleared number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Cleared number format for %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Changed number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Changed number format for %s in %s", "commands.scoreboard.players.enable.failed": "pagh choHta'. wejHa' chu'Ha'lu' chu'wI'vetlh", "commands.scoreboard.players.enable.invalid": "chu'meH ngoQvaD neH enable Dalo'laH", "commands.scoreboard.players.enable.success.multiple": "%2$s DolvaD %1$s chu'wI' chu'lu'", "commands.scoreboard.players.enable.success.single": "%2$svaD %1$s chu'wI' chu'lu'", "commands.scoreboard.players.get.null": "Can't get value of %s for %s; none is set", "commands.scoreboard.players.get.success": "%2$s %3$s ghaj %1$s", "commands.scoreboard.players.list.empty": "pagh <PERSON>l ghochlu'bogh tu'lu'", "commands.scoreboard.players.list.entity.empty": "'aghmeH pagh mIvwa' mI' ghaj %s", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s has %s score(s):", "commands.scoreboard.players.list.success": "There are %s tracked entity/entities: %s", "commands.scoreboard.players.operation.success.multiple": "%2$s DolvaD %1$s choHta'", "commands.scoreboard.players.operation.success.single": "Set %s for %s to %s", "commands.scoreboard.players.remove.success.multiple": "%3$s DolvaD %2$svo' %1$s nge'ta'", "commands.scoreboard.players.remove.success.single": "%3$svaD %2$svo' %1$s nge'ta' (DaH %4$s 'oH)", "commands.scoreboard.players.reset.all.multiple": "%s DolvaD Hoch mIvwa' mI' motlh cheghmoHta'", "commands.scoreboard.players.reset.all.single": "%svaD Hoch mIvwa' mI' motlh cheghmoHta'", "commands.scoreboard.players.reset.specific.multiple": "%2$s DolvaD %1$s Dotlh motlh cheghmoHta'", "commands.scoreboard.players.reset.specific.single": "%2$svaD %1$s Dotlh motlh cheghmoHta'", "commands.scoreboard.players.set.success.multiple": "%3$s 'oH %2$s DolmeH %1$s'e' 'e' cherta'", "commands.scoreboard.players.set.success.single": "%3$s 'oH %2$smeH %1$s'e' 'e' cherta'", "commands.seed.success": "qo' mI': %s", "commands.setblock.failed": "buq'<PERSON><PERSON>'", "commands.setblock.success": "%s, %s, %s buq'Ir cho<PERSON><PERSON>'", "commands.setidletimeout.success": "The player idle timeout is now %s minute(s)", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "Can only set the world spawn for overworld", "commands.setworldspawn.success": "qo' spawn lang HIjmeH (%s, %s, %s)", "commands.spawnpoint.success.multiple": "HijmeH spawn Siq %s, %s, %s pa' %s meH %s", "commands.spawnpoint.success.single": "HijmeH spawn Siq %s, %s, %s pa' [%s] meH %s chu'wI'", "commands.spectate.not_spectator": "bejwI' ghaHbe' %s", "commands.spectate.self": "bej'e<PERSON><PERSON><PERSON><PERSON>'", "commands.spectate.success.started": "DaH %s Dabej", "commands.spectate.success.stopped": "Dol <PERSON>jbe'choH", "commands.spreadplayers.failed.entities": "Could not spread %s entity/entities around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.failed.invalid.height": "'aqroS patlh waS 'oH %s'e'; 'aqroS patlh jen law' patlh rav %s jen puS 'e' pIH", "commands.spreadplayers.failed.teams": "Could not spread %s team(s) around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.success.entities": "Spread %s entity/entities around %s, %s with an average distance of %s block(s) apart", "commands.spreadplayers.success.teams": "Spread %s team(s) around %s, %s with an average distance of %s block(s) apart", "commands.stop.stopping": "mev tebwI'", "commands.stopsound.success.source.any": "Hoch %s wab chu'<PERSON>'moHlu'", "commands.stopsound.success.source.sound": "%2$s %1$s tlhen 'e' mevta'", "commands.stopsound.success.sourceless.any": "Hoch wab chu'Ha'moHlu'", "commands.stopsound.success.sourceless.sound": "%s wab chu'<PERSON>'mo<PERSON><PERSON>'", "commands.summon.failed": "Dol rItlaHbe'", "commands.summon.failed.uuid": "rapbogh ngu'meH mI'meymo' Dol rItlaHbe'", "commands.summon.invalidPosition": "rItmeH Daq waS", "commands.summon.success": "%s chu' rItpu'", "commands.tag.add.failed": "wejH<PERSON>' per ghaj Do<PERSON> pagh per 'Iq ghaj <PERSON>", "commands.tag.add.success.multiple": "%2$s DolDaq %1$s per chelta'", "commands.tag.add.success.single": "%2$sDaq %1$s per chelta'", "commands.tag.list.multiple.empty": "per ghajbe' %s Dol", "commands.tag.list.multiple.success": "%2$s per ghaj %1$s Dol: %3$s", "commands.tag.list.single.empty": "per ghajbe' %s", "commands.tag.list.single.success": "%2$s per ghaj %1$s: %3$s", "commands.tag.remove.failed": "pervam ghaj<PERSON>' <PERSON>", "commands.tag.remove.success.multiple": "%2$s Dolvo' %1$s per teqta'", "commands.tag.remove.success.single": "%2$svo' %1$s per teqta'", "commands.team.add.duplicate": "wej<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>h ghaj latlh ghom", "commands.team.add.success": "%s ghom chenmoHta'", "commands.team.empty.success": "Removed %s member(s) from team %s", "commands.team.empty.unchanged": "pagh choHta'. wejHa' ch<PERSON>m g<PERSON>h", "commands.team.join.success.multiple": "%2$s ghomDaq %1$s QujwI' chelta'", "commands.team.join.success.single": "%2$s ghomDaq %1$s chelta'", "commands.team.leave.success.multiple": "vay' ghomvo' nge'ta' %s QujwI'", "commands.team.leave.success.single": "vay' ghomvo' nge'ta' %s", "commands.team.list.members.empty": "pagh vInDa' ghaj %s ghom", "commands.team.list.members.success": "Team %s has %s member(s): %s", "commands.team.list.teams.empty": "ghommey tu'lu'be'", "commands.team.list.teams.success": "There are %s team(s): %s", "commands.team.option.collisionRule.success": "DaH %s ghomvaD %s 'oH paw'meH chut'e'", "commands.team.option.collisionRule.unchanged": "pagh choHta'. wejHa' mI'vetlh 'oH paw'meH chut'e'", "commands.team.option.color.success": "%s ghom nguvmoHta'; %s rur", "commands.team.option.color.unchanged": "pagh choHta'. wejHa' rItlhvet<PERSON>h ghaj ghomvetlh", "commands.team.option.deathMessageVisibility.success": "DaH %s ghomvaD «%s»'e' 'oH Hegh QIn leghlaHghach", "commands.team.option.deathMessageVisibility.unchanged": "choH pagh. wejHa' rap Hegh QIn leghlaHghach", "commands.team.option.friendlyfire.alreadyDisabled": "pagh choHta'. wejHa' QIHchuqlaH", "commands.team.option.friendlyfire.alreadyEnabled": "pagh choHta'. wejHa' QIHchuqlaHbe'", "commands.team.option.friendlyfire.disabled": "%s ghom, latlh je QIHchuqlaH", "commands.team.option.friendlyfire.enabled": "%s ghom, latlh je QIHchuqlaHbe'", "commands.team.option.name.success": "Team pong updated %s", "commands.team.option.name.unchanged": "pagh choHta'. wejH<PERSON>' pong<PERSON><PERSON>h ghaj g<PERSON>vetlh", "commands.team.option.nametagVisibility.success": "DaH %s ghomvaD %s 'oH pong per leghlaHghach'e'", "commands.team.option.nametagVisibility.unchanged": "pagh choHta'. wejHa' mI'vetlh 'oH pong per leghlaHghach'e'", "commands.team.option.prefix.success": "DaH %s 'oH ghom moHaq'e'", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "pagh choHta'. wejHa' vInDa' So'lu'bogh leghlaHbe' g<PERSON><PERSON>lh", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "pagh choHta'. wejHa' vInDa' So'lu'bogh leghlaH ghomvetlh", "commands.team.option.seeFriendlyInvisibles.disabled": "DaH vInDa' So'lu'bogh leghlaHbe' %s ghom", "commands.team.option.seeFriendlyInvisibles.enabled": "DaH vInDa' So'lu'bogh leghlaH %s ghom", "commands.team.option.suffix.success": "DaH %s 'oH ghom mojaq'e'", "commands.team.remove.success": "%s ghom Qaw'ta'", "commands.teammsg.failed.noteam": "ghomlIj DarI'meH, ghom DaghajnIS", "commands.teleport.invalidPosition": "jolmeH Daq waS", "commands.teleport.success.entity.multiple": "%2$sDaq jolpu' %1$s Dol", "commands.teleport.success.entity.single": "%2$sDaq %1$s jolpu'", "commands.teleport.success.location.multiple": "%2$s, %3$s, %4$sDaq jolpu' %1$s Dol", "commands.teleport.success.location.single": "%2$s vI' %3$s vI' %4$sDaq %1$s jolpu'", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s vI' %s vI' %s", "commands.test.coordinates.copy": "Click to copy to clipboard", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "vatlhvI': vaghmaH: %s tup loch wa'SaD. HutmaH vagh: %s tup loch wa'SaD. HutmaH Hut: %s tup loch wa'SaD, chovnatlh: %s", "commands.tick.query.rate.running": "mIwHom Do ngoQ: %s loch lup.\nmIwHom poH ghIl: %s loch lup 'e' loch wa'SaD (ngoQ: %s loch lup loch wa'SaD)", "commands.tick.query.rate.sprinting": "mIwHom Do ngoQ: %s loch lup (buSHa', nompuq neH)\nmIwHom poH ghIl: %s loch lup 'e' loch wa'SaD", "commands.tick.rate.success": "Set the target tick rate to %s per second", "commands.tick.sprint.report": "Sprint completed with %s ticks per second, or %s ms per tick", "commands.tick.sprint.stop.fail": "No tick sprint in progress", "commands.tick.sprint.stop.success": "Interrupted the current tick sprint", "commands.tick.status.frozen": "<PERSON><PERSON>", "commands.tick.status.lagging": "<PERSON><PERSON><PERSON><PERSON>, 'ach mIwHom Do ngoQ ta'laHbe'", "commands.tick.status.running": "The game is running normally", "commands.tick.status.sprinting": "The game is sprinting", "commands.tick.step.fail": "Unable to step the game - the game must be frozen first", "commands.tick.step.stop.fail": "No tick step in progress", "commands.tick.step.stop.success": "Interrupted the current tick step", "commands.tick.step.success": "Stepping %s tick(s)", "commands.time.query": "%s 'oH poH mI''e'", "commands.time.set": "DaH %s 'oH poH mI''e'", "commands.title.cleared.multiple": "%s QujwI'vaD per Qaw'ta'", "commands.title.cleared.single": "%svaD per Qaw'ta'", "commands.title.reset.multiple": "%s QujwI'vaD per DuHmey Qaw'ta'", "commands.title.reset.single": "%svaD per DuHmey Qaw'ta'", "commands.title.show.actionbar.multiple": "%s QujwI'vaD may' per chu' cha'taH", "commands.title.show.actionbar.single": "%svaD may' per chu' cha'taH", "commands.title.show.subtitle.multiple": "%s QujwI'vaD perHom chu' cha'taH", "commands.title.show.subtitle.single": "%svaD perHom chu' cha'taH", "commands.title.show.title.multiple": "%s QujwI'vaD per chu' cha'taH", "commands.title.show.title.single": "%svaD per chu' cha'taH", "commands.title.times.multiple": "%s QujwI'vaD per cha'meH poH choHta'", "commands.title.times.single": "%svaD per cha'meH poH choHta'", "commands.transfer.error.no_players": "Must specify at least one player to transfer", "commands.transfer.success.multiple": "Transferring %s players to %s:%s", "commands.transfer.success.single": "Transferring %s to %s:%s", "commands.trigger.add.success": "%s chu'ta' (mI'Daq %s chelta')", "commands.trigger.failed.invalid": "trigger Segh ghajnIS ngoQ <PERSON>bogh", "commands.trigger.failed.unprimed": "wej ng<PERSON><PERSON><PERSON><PERSON>", "commands.trigger.set.success": "%s chu'ta' (%s mojmoHta' mI')", "commands.trigger.simple.success": "%s chu'ta'", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "jul 'anglu'", "commands.weather.set.rain": "SISchoHmoHlu' muD", "commands.weather.set.thunder": "muD jevmoHtaHlu'", "commands.whitelist.add.failed": "wejH<PERSON>' <PERSON>ujw<PERSON>'vam'e' yugh naD tetlh", "commands.whitelist.add.success": "naD tetlhDaq %s chelta'", "commands.whitelist.alreadyOff": "wej<PERSON><PERSON>' <PERSON><PERSON> tetlh chu'Ha'lu'", "commands.whitelist.alreadyOn": "wej<PERSON><PERSON><PERSON> <PERSON><PERSON> tetlh chu'lu'", "commands.whitelist.disabled": "DaH naD tetlh chu'Ha'lu'", "commands.whitelist.enabled": "DaH naD tetlh chu'lu'", "commands.whitelist.list": "There are %s whitelisted player(s): %s", "commands.whitelist.none": "pagh QujwI' yugh naD tetlh", "commands.whitelist.reloaded": "naD tetlh lI'qa'ta'", "commands.whitelist.remove.failed": "Qujw<PERSON>'vam'e' yughbe' naD tetlh", "commands.whitelist.remove.success": "naD tetlhDaq %s polHa'ta'", "commands.worldborder.center.failed": "pagh choHta'. wejHa' qo' veH botlh 'oH", "commands.worldborder.center.success": "%s vI' %svaD qo' HeH botlh mojmoHpu'", "commands.worldborder.damage.amount.failed": "pagh choHta'. wejHa' mI'vetlh lo' qo' veH QIH", "commands.worldborder.damage.amount.success": "Qo' border Qih HijmeH %s per bot Hoch cha'Dich", "commands.worldborder.damage.buffer.failed": "pagh choHta'. wejHa' chuqvetlh lo' qo' veH QIH", "commands.worldborder.damage.buffer.success": "Set the world border damage buffer to %s block(s)", "commands.worldborder.get": "The world border is currently %s block(s) wide", "commands.worldborder.set.failed.big": "%s buq'Ir ju<PERSON><PERSON><PERSON>' qo' veH chuq", "commands.worldborder.set.failed.far": "%s buq'<PERSON><PERSON><PERSON> ju<PERSON><PERSON>' qo' veH chuq", "commands.worldborder.set.failed.nochange": "pagh choHta'. wejHa' chuqvetlh 'aD qo' veH", "commands.worldborder.set.failed.small": "qo' veH chuq juSlaHbe' wa' buq'Ir", "commands.worldborder.set.grow": "qaStaHvIS %2$s lup %1$s buq'Ir SIchmeH Sach qo' veH 'e' qaSmoHtaH", "commands.worldborder.set.immediate": "Set the world border to %s block(s) wide", "commands.worldborder.set.shrink": "Shrinking the world border to %s block(s) wide over %s second(s)", "commands.worldborder.warning.distance.failed": "pagh choHta'. wejH<PERSON>' chuqvet<PERSON>h lo' qo' veH ghuH", "commands.worldborder.warning.distance.success": "Set the world border warning distance to %s block(s)", "commands.worldborder.warning.time.failed": "pagh choHta'. wejHa' poHvetlh lo' qo' veH ghuH", "commands.worldborder.warning.time.success": "Set the world border warning time to %s second(s)", "compliance.playtime.greaterThan24Hours": "poH DaQujtaHvIS law' cha'maH loS rep puS", "compliance.playtime.hours": "qaStaHvIS %s rep Da<PERSON><PERSON>j", "compliance.playtime.message": "<PERSON><PERSON><PERSON><PERSON>, yIn motlh nISlaH Quj", "connect.aborted": "qIllu'", "connect.authorizing": "bIrar 'e' chaw'taH...", "connect.connecting": "turw<PERSON>'Daq rartaH...", "connect.encrypting": "So'lI'...", "connect.failed": "De'wI' HopDaq rarlaHbe'", "connect.failed.transfer": "Connection failed while transferring to the server", "connect.joining": "qo' muvlI'", "connect.negotiating": "SutlhlI'...", "connect.reconfiging": "Reconfiguring...", "connect.reconfiguring": "mutlhqa'lI'...", "connect.transferring": "turwI' chu'<PERSON><PERSON>'...", "container.barrel": "qegh", "container.beacon": "rI'meH wovmoHwI'", "container.beehive.bees": "maqSung: %2$s loch %1$s", "container.beehive.honey": "DayqIr: %2$s loch %1$s", "container.blast_furnace": "vIncha' tujqu'", "container.brewing": "taS vutmeH qal'aq", "container.cartography_table": "pu'jIn raS", "container.chest": "DerlIq", "container.chestDouble": "DerlIq tIn", "container.crafter": "chenmoHwI'", "container.crafting": "chenmoH", "container.creative": "Doch wIv", "container.dispenser": "peSwI'", "container.dropper": "chaghwI'", "container.enchant": "laH chel", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Doltop", "container.enchant.lapis.one": "wa' <PERSON><PERSON><PERSON>", "container.enchant.level.many": "%s tlhe<PERSON>", "container.enchant.level.one": "wa' tlheH patlh", "container.enchant.level.requirement": "patlh %s DachavnIS", "container.enderchest": "ender <PERSON>", "container.furnace": "vIncha'", "container.grindstone_title": "tI' qoj reSHa'", "container.hopper": "tomter", "container.inventory": "buq<PERSON><PERSON>", "container.isLocked": "%s ngaQ!", "container.lectern": "paq raS", "container.loom": "nIqmeH qal'aq", "container.repair": "tI' 'ej pong", "container.repair.cost": "tlheH qav'ap: %1$s", "container.repair.expensive": "tlhoy wagh!", "container.shulkerBox": "shulker ngaSwI'", "container.shulkerBox.itemCount": "%2$s %1$s", "container.shulkerBox.more": "%s latlh je...", "container.shulkerBox.unknownContents": "Sovbe'lu'", "container.smoker": "tlhIch vIncha'", "container.spectatorCantOpen": "DapoSmoHlaHbe'. wej qengHoDDaj lIng.", "container.stonecutter": "nagh pe'wI'", "container.upgrade": "luch rach", "container.upgrade.error_tooltip": "Item can't be upgraded this way", "container.upgrade.missing_template_tooltip": "mItlhmeH ghantoH yIlan", "controls.keybinds": "le<PERSON><PERSON><PERSON>...", "controls.keybinds.duplicateKeybinds": "This key is also used for:\n%s", "controls.keybinds.title": "<PERSON><PERSON><PERSON><PERSON>", "controls.reset": "<PERSON><PERSON><PERSON> motlh chegh", "controls.resetAll": "le<PERSON>mey mot<PERSON>'moH", "controls.title": "SeHw<PERSON>'mey", "createWorld.customize.buffet.biome": "'umber yIwIv", "createWorld.customize.buffet.title": "DuH<PERSON>y law' qo'", "createWorld.customize.flat.height": "puH patlh 'aqroS", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "bIng - %s", "createWorld.customize.flat.layer.top": "yor - %s", "createWorld.customize.flat.removeLayer": "patlh teq", "createWorld.customize.flat.tile": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.title": "qo' beQqu' choH", "createWorld.customize.presets": "<PERSON><PERSON><PERSON><PERSON> cherlu'pu'bogh", "createWorld.customize.presets.list": "<PERSON><PERSON><PERSON><PERSON>, naDev 'op DuHmey DIcherlu'ta'bogh tu'lu'!", "createWorld.customize.presets.select": "DuH cherlu'pu'bogh lo'", "createWorld.customize.presets.share": "vay'vaD qo' beQqu' lIngmeH mIwlIj'e' DalIn DaneH'a'? bIngDaq tu'lu'bogh ngaSwI''e' yIlo'!", "createWorld.customize.presets.title": "DuH cherlu'pu'bogh yIwIv", "createWorld.preparing": "qo' chenmoH 'e' qeq...", "createWorld.tab.game.title": "<PERSON><PERSON><PERSON>", "createWorld.tab.more.title": "latlh", "createWorld.tab.world.title": "qo'", "credits_and_attribution.button.attribution": "ghaqwI'pu'", "credits_and_attribution.button.credits": "chenmoHwI'pu'", "credits_and_attribution.button.licenses": "nIqHom chaw'mey", "credits_and_attribution.screen.title": "Quj chenmoHwI' ghaqwI' je", "dataPack.bundle.description": "Doj tlhum chu'", "dataPack.bundle.name": "<PERSON><PERSON> t<PERSON><PERSON>", "dataPack.locator_bar.description": "QujtaHvIS latlh QujwI', lurghchaj cha'", "dataPack.locator_bar.name": "SammeH yamtaw", "dataPack.minecart_improvements.description": "Improved movement for Minecarts", "dataPack.minecart_improvements.name": "tlhIlHal Duj luDublu'", "dataPack.redstone_experiments.description": "Experimental Redstone changes", "dataPack.redstone_experiments.name": "Redstone Experiments", "dataPack.title": "De' vey tIwIv", "dataPack.trade_rebalance.description": "vengHom ghot malja' chu'", "dataPack.trade_rebalance.name": "vengHom ghot malja' l<PERSON><PERSON>'", "dataPack.update_1_20.description": "New features and content for Minecraft 1.20", "dataPack.update_1_20.name": "qa'meH mI' 1.20", "dataPack.update_1_21.description": "New features and content for Minecraft 1.21", "dataPack.update_1_21.name": "qa'meH mI' 1.21", "dataPack.validation.back": "JaH chap", "dataPack.validation.failed": "De' vey woq 'e' luj!", "dataPack.validation.reset": "motlh chegh", "dataPack.validation.working": "De' vey wIvlu'bogh 'ollI'...", "dataPack.vanilla.description": "minecraft De' motlh", "dataPack.vanilla.name": "motlh", "dataPack.winter_drop.description": "qImroq bIr qa'meH mI' DI'onmey chu", "dataPack.winter_drop.name": "qImroq bIr qa'meH mI'", "datapackFailure.safeMode": "QanwI'", "datapackFailure.safeMode.failed.description": "This world contains invalid or corrupted save data.", "datapackFailure.safeMode.failed.title": "Failed to load world in Safe Mode.", "datapackFailure.title": "qo' lI'lu' 'e' bot <PERSON>' ve<PERSON><PERSON>m <PERSON>agh'e'.\n<PERSON>' vey motlh Dalo'taHvIS neH qo' DalI' 'e' DanIDlaH, pagh DatI'meH per HaSta DacheghlaH.", "death.attack.anvil": "%1$s tap pumbogh 'achme'", "death.attack.anvil.player": "SuvchuqtaHvIS %1$s %2$s je, %1$s HoH pumbogh 'achme'", "death.attack.arrow": "%1$sDaq baH %2$s", "death.attack.arrow.item": "%1$sDaq baH %2$s %3$s lo'taHvIS", "death.attack.badRespawnPoint.link": "<PERSON><PERSON><PERSON> nab", "death.attack.badRespawnPoint.message": "%1$s HoH %2$s", "death.attack.cactus": "%1$s HoH qaqtaS vIlHom", "death.attack.cactus.player": "yIt %1$s cactus vaj chalvaD HeS %2$s nargh 'e' lunID", "death.attack.cramming": "%1$s taplu'", "death.attack.cramming.player": "%1$s tap %2$s", "death.attack.dragonBreath": "lung'a' tlhuHDaq %1$s ronglu'", "death.attack.dragonBreath.player": "lung'a' tlhuHDaq %1$s rong %2$s", "death.attack.drown": "bIQDaQ vIQ %1$s", "death.attack.drown.player": "%2$svo' narghtaHvIS, bIQDaq vIQ %1$s", "death.attack.dryout": "tlhoy 'oj<PERSON>' <PERSON>gh<PERSON>' %1$s", "death.attack.dryout.player": "%2$svo' nargh 'e' nIDtaHvIS Heghpu' %1$s tlhoy 'ojmo'", "death.attack.even_more_magic": "%1$s HoHmeH latlh 'IDnar lo'lu'pu'", "death.attack.explosion": "jor %1$s", "death.attack.explosion.player": "%1$s jormoH %2$s", "death.attack.explosion.player.item": "%1$s jormoHmeH %3$s lo' %2$s", "death.attack.fall": "yav qIpqu' %1$s", "death.attack.fall.player": "%2$svo' Haw'taHvIS %1$s, tlhoy pe'vIl yav mup %1$s", "death.attack.fallingBlock": "%1$s tap pumtaHbogh buq'Ir", "death.attack.fallingBlock.player": "%2$s SuvvIS %1$s, %1$s HoH pumbogh buq'Ir", "death.attack.fallingStalactite": "%1$s betpu' pumbogh De'lor", "death.attack.fallingStalactite.player": "%2$s SuvtaHvIS %1$s'e' betpu' pumbogh De'lor", "death.attack.fireball": "%1$s fireballed %2$s pong", "death.attack.fireball.item": "%1$s fireballed %2$s %3$s lo' pong", "death.attack.fireworks": "%1$s HoH lop jorwI'", "death.attack.fireworks.item": "%1$s loj bang muHIvtaHbogh firework qul vo' %3$s pong %2$s", "death.attack.fireworks.player": "%2$s SuvvIS %1$s, %1$s HoH lop jorwI'", "death.attack.flyIntoWall": "%1$s puv tlhoy'", "death.attack.flyIntoWall.player": "%2$s Haw'taHvIS, tlhoy' ngeQ %1$s", "death.attack.freeze": "taDchu' %1$s", "death.attack.freeze.player": "%1$s taDmoHchu' %2$s", "death.attack.generic": "Hegh %1$s", "death.attack.generic.player": "%2$smo' Hegh %1$s", "death.attack.genericKill": "%1$s HoHlu'", "death.attack.genericKill.player": "%2$s SuvtaHvIS, Hegh %1$s", "death.attack.hotFloor": "vaHbo' 'oH rav 'e' tu' %1$s", "death.attack.hotFloor.player": "%2$smo' Qob mIch 'el %1$s", "death.attack.inFire": "'el %1$s DungDaj", "death.attack.inFire.player": "yIt %1$s vaj qul chalvaD HeS %2$s ghob", "death.attack.inWall": "reDDaq thluHlaHbe'mo' Hegh %1$s", "death.attack.inWall.player": "tlhoy'Daq vIQ %1$s %2$s SuvtaHvIS ghaH", "death.attack.indirectMagic": "%1$s HoHmeH 'IDnar lo'pu' %2$s", "death.attack.indirectMagic.item": "%1$s HoHmeH %3$s lo'pu' %2$s", "death.attack.lava": "vaHbo'<PERSON><PERSON>e' nID %1$s", "death.attack.lava.player": "Qal vaHbo' %2$s nargh nID %1$s", "death.attack.lightningBolt": "%1$s mup pe'bIl", "death.attack.lightningBolt.player": "%1$s mup pe'bIl %2$s SuvtaHvIS", "death.attack.mace_smash": "%1$s was smashed by %2$s", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "%1$s HoH 'IDnar", "death.attack.magic.player": "%1$s muHlu'nIS pong magic chalvaD HeS nargh 'e' nID %2$s", "death.attack.message_too_long": "tlhoy tIqmo' QIn naQ, ngeHlaHbe'. tlhIj! QIn runmoHlu' ngeH: %s", "death.attack.mob": "%1$s HoH %2$s", "death.attack.mob.item": "slain %1$s pong %2$s %3$s yIlo'", "death.attack.onFire": "meQchu' %1$s", "death.attack.onFire.item": "%1$s was burned to a crisp while fighting %2$s wielding %3$s", "death.attack.onFire.player": "meQ %1$s crisp chalvaD HeS %2$s ghob", "death.attack.outOfWorld": "qo'vo' pum %1$s", "death.attack.outOfWorld.player": "qo''e' <PERSON><PERSON><PERSON> %2$s Dab neHbe' %1$s", "death.attack.outsideBorder": "qo' veH mej %1$s", "death.attack.outsideBorder.player": "%2$s SuvtaHvIS, qo' veH mej %1$s", "death.attack.player": "%1$s HoH %2$s", "death.attack.player.item": "%1$s HoH %2$s %3$s lo'taHvIS", "death.attack.sonic_boom": "%1$s Sang bey Ho<PERSON>qu'", "death.attack.sonic_boom.item": "%3$s 'uchtaHbogh %2$svo' nargh 'e' nIDtaHvIS %1$s, Sang bey <PERSON>'", "death.attack.sonic_boom.player": "%2$svo' nargh 'e' nIDtaHvIS %1$s, Sang bey Ho<PERSON>qu'", "death.attack.stalagmite": "%1$s betpu' rav <PERSON>r", "death.attack.stalagmite.player": "%2$s SuvtaHvIS %1$s'e' betpu' De'lor", "death.attack.starve": "ghungq<PERSON>'mo' <PERSON>gh %1$s", "death.attack.starve.player": "ghungqu'mo' <PERSON>gh %1$s %2$s SuvtaHvIS ghaH", "death.attack.sting": "Heghpu'meH %1$s DuQlu'pu'", "death.attack.sting.item": "%1$s was stung to death by %2$s using %3$s", "death.attack.sting.player": "Heghpu'meH %1$s DuQpu' %2$s", "death.attack.sweetBerryBush": "%1$s <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "death.attack.sweetBerryBush.player": "%1$s DuQ<PERSON>' <PERSON><PERSON><PERSON><PERSON> ghaH tlha'taHvIS %2$s", "death.attack.thorns": "%1$s muHlu'nIS 'oy' 'e' lunID %2$s", "death.attack.thorns.item": "%2$s HIvtaHvIS %1$s, %1$s HoH %3$s", "death.attack.thrown": "pummeled %1$s pong %2$s", "death.attack.thrown.item": "%1$s pummeled pong %2$s waH %3$s", "death.attack.trident": "%1$s bet %2$s", "death.attack.trident.item": "%1$s betmeH %3$s chuH %2$s", "death.attack.wither": "raghchu' %1$s", "death.attack.wither.player": "%2$s SuvtaHvIS, raghchu' %1$s", "death.attack.witherSkull": "%1$sDaq DughrI' baH %2$s", "death.attack.witherSkull.item": "%1$s was shot by a skull from %2$s using %3$s", "death.fell.accident.generic": "Daq<PERSON>' jen pum %1$s", "death.fell.accident.ladder": "letlh chongvo' pum %1$s", "death.fell.accident.other_climbable": "%1$s wa'vatlh toS pum", "death.fell.accident.scaffolding": "chenmoHmeH qal'aqvo' pum %1$s'e'", "death.fell.accident.twisting_vines": "tlhegh tIvo' ver pum %1$s", "death.fell.accident.vines": "tlhegh tIvo' pum %1$s", "death.fell.accident.weeping_vines": "tlhegh tIvo' Saqbogh pum %1$s", "death.fell.assist": "%1$s pummoH %2$s", "death.fell.assist.item": "%1$s pummoH %2$s; %3$s lo'", "death.fell.finish": "%1$s pumta' Daq Hop 'ej gha<PERSON>ta' HoHta' Sum %2$s", "death.fell.finish.item": "%1$s pumta' Daq Hop 'ej gha<PERSON>ta' HoHta' Sum %2$s 'uch %3$s", "death.fell.killer": "%1$s pummoHlu'", "deathScreen.quit.confirm": "bIbup DaneHbej'a'?", "deathScreen.respawn": "bogh<PERSON>'", "deathScreen.score": "mI' Dape''eghbogh", "deathScreen.score.value": "mIvwa'mey: %s", "deathScreen.spectate": "qo' bej", "deathScreen.title": "bIHegh!", "deathScreen.title.hardcore": "rIn Quj!", "deathScreen.titleScreen": "per <PERSON><PERSON>", "debug.advanced_tooltips.help": "F3 + H = 'Itlhbogh ghItlh Sa<PERSON>lu'bogh", "debug.advanced_tooltips.off": "'Itlhbogh ghItlh Sa<PERSON>lu'bogh: So'lu'", "debug.advanced_tooltips.on": "'Itlhbogh ghItlh Sa<PERSON>lu'bogh: 'anglu'", "debug.chunk_boundaries.help": "F3 + G = puH 'ay' HeHmey cha'", "debug.chunk_boundaries.off": "puH 'ay' HeH: So'lu'", "debug.chunk_boundaries.on": "puH 'ay' HeH: cha'lu'", "debug.clear_chat.help": "F3 + D = QIn pat chImmoH", "debug.copy_location.help": "F3 + C = chenqa'moHtaHvIS /tp ra'meH ghItlh mojmo<PERSON> Daq, Quj Qapbe'choHmoHmeH F3 + C yI'uytaH", "debug.copy_location.message": "<PERSON><PERSON> ch<PERSON>'pu'", "debug.crash.message": "DaH F3 + C 'uylu'taH. 'uyHa'lu'be'chugh Qapbe'choH Quj.", "debug.crash.warning": "qaSDI' %s lup <PERSON>ap<PERSON>'choH Qujvam...", "debug.creative_spectator.error": "<PERSON>uj lo' DachoHlaHbe'; <PERSON><PERSON><PERSON>'be'lu'", "debug.creative_spectator.help": "F3 + N = Quj lo' vorgh bejwI' lo' je tamchuqmoH", "debug.dump_dynamic_textures": "Saved dynamic textures to %s", "debug.dump_dynamic_textures.help": "F3 + S = Dump dynamic textures", "debug.gamemodes.error": "Unable Quj mode switcher, pagh choHwI' wIlIng poS", "debug.gamemodes.help": "F3 + F4 = Quj lo' choHwI' poSmoH", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "ghIq %s", "debug.help.help": "F3 + Q = tetlhvam 'ang", "debug.help.message": "nItlh 'e<PERSON><PERSON> DuHmey:", "debug.inspect.client.block": "De'wI'vam buq'Ir <PERSON>' chen<PERSON><PERSON><PERSON>'pu'", "debug.inspect.client.entity": "De'wI'vam <PERSON>l <PERSON>' chenmoHqa'pu'", "debug.inspect.help": "F3 + I = <PERSON><PERSON> <PERSON> <PERSON>u<PERSON>'<PERSON><PERSON> ghap chen<PERSON>'", "debug.inspect.server.block": "turw<PERSON>' bu<PERSON>'<PERSON>r <PERSON>' chenmo<PERSON>'pu'", "debug.inspect.server.entity": "turwI' Dol De' chenmoHqa'pu'", "debug.pause.help": "F3 + Esc = yev 'ach yevmeH HIDjolev lo'be' (yevlaHchugh)", "debug.pause_focus.help": "F3 + P = latlh nIqHom lo'lu'DI', <PERSON>u<PERSON> yev<PERSON><PERSON>", "debug.pause_focus.off": "latlh nIqHom lo'lu'DI', <PERSON>uj yevmoH: Qotlhlu'", "debug.pause_focus.on": "latlh nIqHom lo'lu'DI', Quj yevmo<PERSON>: chu'lu'", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = juv(be')choH", "debug.profiling.start": "qaStaHvIS %s lup juv. DaghangmeH, F3 + L yIlo'", "debug.profiling.stop": "juvbe'choH. %sDaq gher'ID pol", "debug.reload_chunks.help": "F3 + A = puH 'ay'mey lI'qa'", "debug.reload_chunks.message": "Hoch puH 'ay' lI'qa'", "debug.reload_resourcepacks.help": "F3 + T = mIllogh vey lI'qa'", "debug.reload_resourcepacks.message": "mIllogh vey lI'qa'", "debug.show_hitboxes.help": "F3 + B = DoS tu'qommey cha'", "debug.show_hitboxes.off": "DoS tu'qom: So'lu'", "debug.show_hitboxes.on": "DoS tu'qom: cha'lu'", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "qaStaHvIS vagh Quj jaj taH 'aghmeH Qujvam. Qapla'!", "demo.day.2": "jaj cha'", "demo.day.3": "jaj wej", "demo.day.4": "jaj <PERSON>", "demo.day.5": "jaj<PERSON><PERSON><PERSON> 'oH <PERSON>'<PERSON>'!", "demo.day.6": "rIn jaj vaghDIch. mIllogh DachenmoHmeH %s yIlo'.", "demo.day.warning": "tlhoS rIn poHlIj!", "demo.demoExpired": "rIntaH 'aghmeH Quj poH!", "demo.help.buy": "DaH yIje'!", "demo.help.fullWrapped": "qaStaHvIS vagh Quj jaj taH 'aghmeH Qujvam (wa' rep loSmaH tup je chen DI'ruj poH). DuboQlaH chav ta! Qapla'!", "demo.help.inventory": "'aplo'lIj poSmoHmeH %1$s yI'uy", "demo.help.jump": "SupmeH %1$s yI'uy", "demo.help.later": "Quj yItaH!", "demo.help.movement": "bIvIHmeH %1$s, %2$s, %3$s, %4$s, 'eQway' je tIlo'", "demo.help.movementMouse": "Daq <PERSON>uDmeH SeHwI' yIlo'", "demo.help.movementShort": "vIHmoH %1$s, %2$s, %3$s, %4$s", "demo.help.title": "'aghmeH Minecraft", "demo.remainingTime": "ratlhbogh poH: %s", "demo.reminder": "rIn 'aghmeH poH. qo'vam <PERSON>moHmeH, qo' chu' DachenmoHmeH pagh, Quj yIje'!", "difficulty.lock.question": "qo' QItl<PERSON><PERSON>ach <PERSON>pol 'e' neH? choHqa'laHbe' 'ej reH %1$s 'OH'e'.", "difficulty.lock.title": "qo' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pol", "disconnect.endOfStream": "rIn ghoqchuy", "disconnect.exceeded_packet_rate": "De' veyH<PERSON> '<PERSON><PERSON>' <PERSON><PERSON><PERSON>'", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Dotlh tlhoblu' 'e' buSHa'", "disconnect.loginFailedInfo": "ngaQHa'moH 'e' luj: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "chu'<PERSON>'lu' QujwI' law'. microsoft mab DuHmey tInuD", "disconnect.loginFailedInfo.invalidSession": "Quj waS (<PERSON>uj Quj taghwI' je <PERSON>' 'e' yInID)", "disconnect.loginFailedInfo.serversUnavailable": "'olwI' turwI' rI'laHbe' vay'. yInIDqa'.", "disconnect.loginFailedInfo.userBanned": "You are banned from playing online", "disconnect.lost": "rarHa'", "disconnect.packetError": "SI<PERSON>'<PERSON>' m<PERSON><PERSON>", "disconnect.spam": "Dapmo' rItHa'", "disconnect.timeout": "rarmeH poH natlhlu'", "disconnect.transfer": "latlh turwI'<PERSON><PERSON>'", "disconnect.unknownHost": "turw<PERSON>' Sovbe'lu'bogh", "download.pack.failed": "%2$s loch %1$s vey lI' 'e' luj", "download.pack.progress.bytes": "Ser: %s ('aqroS Sovbe'lu')", "download.pack.progress.percent": "Ser: %s vatlhvI'", "download.pack.title": "jo vey lI'lI' %2$s loch %1$s", "editGamerule.default": "DuH motlh: %s", "editGamerule.title": "<PERSON><PERSON><PERSON> chut <PERSON>", "effect.duration.infinite": "'e<PERSON>yaH", "effect.minecraft.absorption": "latlh tIq", "effect.minecraft.bad_omen": "maQmIgh", "effect.minecraft.blindness": "<PERSON>hbe'", "effect.minecraft.conduit_power": "'och Ho<PERSON>", "effect.minecraft.darkness": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "SolbIS Do", "effect.minecraft.fire_resistance": "qul 'om", "effect.minecraft.glowing": "wewtaH", "effect.minecraft.haste": "moD", "effect.minecraft.health_boost": "latlh 'on'aS", "effect.minecraft.hero_of_the_village": "vengHom Sub", "effect.minecraft.hunger": "ghung", "effect.minecraft.infested": "ngej<PERSON>'", "effect.minecraft.instant_damage": "Do QIH", "effect.minecraft.instant_health": "Do <PERSON>", "effect.minecraft.invisibility": "So'lu'", "effect.minecraft.jump_boost": "pe'vIl Sup", "effect.minecraft.levitation": "'al", "effect.minecraft.luck": "Do'", "effect.minecraft.mining_fatigue": "QItqu' tlhIl", "effect.minecraft.nausea": "ver", "effect.minecraft.night_vision": "ram <PERSON><PERSON>", "effect.minecraft.oozing": "char", "effect.minecraft.poison": "tar", "effect.minecraft.raid_omen": "yot maQmIgh", "effect.minecraft.regeneration": "rachtaH", "effect.minecraft.resistance": "'om", "effect.minecraft.saturation": "ghungHa'", "effect.minecraft.slow_falling": "peD", "effect.minecraft.slowness": "QIt vIH", "effect.minecraft.speed": "Do", "effect.minecraft.strength": "HoS", "effect.minecraft.trial_omen": "qaD maQmIgh", "effect.minecraft.unluck": "Do'Ha'", "effect.minecraft.water_breathing": "bIQ tlhIH", "effect.minecraft.weakness": "puj", "effect.minecraft.weaving": "nIq", "effect.minecraft.wind_charged": "<PERSON><PERSON>", "effect.minecraft.wither": "ragh", "effect.none": "pagh wanI'", "enchantment.level.1": "wa'", "enchantment.level.10": "wa'maH", "enchantment.level.2": "cha'", "enchantment.level.3": "wej", "enchantment.level.4": "loS", "enchantment.level.5": "vagh", "enchantment.level.6": "jav", "enchantment.level.7": "Soch", "enchantment.level.8": "chorgh", "enchantment.level.9": "Hu<PERSON>", "enchantment.minecraft.aqua_affinity": "bIQ maS", "enchantment.minecraft.bane_of_arthropods": "ghew jagh", "enchantment.minecraft.binding_curse": "'Igh<PERSON>' ngam", "enchantment.minecraft.blast_protection": "jorwI' QanwI'", "enchantment.minecraft.breach": "'aw", "enchantment.minecraft.channeling": "pe'bIl rIt", "enchantment.minecraft.density": "jeD", "enchantment.minecraft.depth_strider": "bIQ yItwI'", "enchantment.minecraft.efficiency": "vaQ", "enchantment.minecraft.feather_falling": "pumbogh boHey", "enchantment.minecraft.fire_aspect": "qul nob", "enchantment.minecraft.fire_protection": "qul QanwI'", "enchantment.minecraft.flame": "qul", "enchantment.minecraft.fortune": "Do'", "enchantment.minecraft.frost_walker": "chuch yItwI'", "enchantment.minecraft.impaling": "Du<PERSON>", "enchantment.minecraft.infinity": "'e<PERSON>yaH", "enchantment.minecraft.knockback": "yuv", "enchantment.minecraft.looting": "la<PERSON><PERSON>", "enchantment.minecraft.loyalty": "matlh", "enchantment.minecraft.luck_of_the_sea": "bIQ'a' Do'", "enchantment.minecraft.lure": "ngel", "enchantment.minecraft.mending": "tI'", "enchantment.minecraft.multishot": "bach law'", "enchantment.minecraft.piercing": "ghID", "enchantment.minecraft.power": "HoS", "enchantment.minecraft.projectile_protection": "peng QanwI'", "enchantment.minecraft.protection": "QanwI'", "enchantment.minecraft.punch": "qIp", "enchantment.minecraft.quick_charge": "nom Hur", "enchantment.minecraft.respiration": "tlhuH", "enchantment.minecraft.riptide": "pep'en Ho<PERSON>", "enchantment.minecraft.sharpness": "jejqu'", "enchantment.minecraft.silk_touch": "pe'vIlHa' boS", "enchantment.minecraft.smite": "<PERSON>gh<PERSON><PERSON>'wI' jagh", "enchantment.minecraft.soul_speed": "qa' Do", "enchantment.minecraft.sweeping": "pe'vIl bI'chu'", "enchantment.minecraft.sweeping_edge": "pe'vIl bI'chu'", "enchantment.minecraft.swift_sneak": "nom jaH joDwI'", "enchantment.minecraft.thorns": "Du<PERSON>w<PERSON>'mey", "enchantment.minecraft.unbreaking": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ch", "enchantment.minecraft.vanishing_curse": "'Igh<PERSON>' ngab", "enchantment.minecraft.wind_burst": "SuS jorwI'", "entity.minecraft.acacia_boat": "'a<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "entity.minecraft.acacia_chest_boat": "DerlIq ngaSbo<PERSON> 'a<PERSON><PERSON><PERSON>r b<PERSON><PERSON>", "entity.minecraft.allay": "yIrwI'", "entity.minecraft.area_effect_cloud": "yoS wanI' 'eng", "entity.minecraft.armadillo": "woSwa'", "entity.minecraft.armor_stand": "may' Sut qal'aq", "entity.minecraft.arrow": "naQjejHom", "entity.minecraft.axolotl": "'evta'", "entity.minecraft.bamboo_chest_raft": "DerlIq ngaSbogh bambu' bIQ <PERSON>", "entity.minecraft.bamboo_raft": "b<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "entity.minecraft.bat": "valqe'", "entity.minecraft.bee": "maqSung", "entity.minecraft.birch_boat": "ber<PERSON><PERSON> Sor b<PERSON><PERSON>", "entity.minecraft.birch_chest_boat": "DerlIq ngaSbogh berIch Sor bIQ Duj", "entity.minecraft.blaze": "qul yagh", "entity.minecraft.block_display": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.boat": "b<PERSON><PERSON>", "entity.minecraft.bogged": "nobmeD yIQ", "entity.minecraft.breeze": "SuS tlhapragh", "entity.minecraft.breeze_wind_charge": "Su<PERSON> bach", "entity.minecraft.camel": "qa'mel", "entity.minecraft.cat": "vIghro'", "entity.minecraft.cave_spider": "DIS voDchuch", "entity.minecraft.cherry_boat": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "entity.minecraft.cherry_chest_boat": "DerlIq ngaSbogh tapqej Sor bIQ Duj", "entity.minecraft.chest_boat": "DerlIq ngaSbogh bIQ Duj", "entity.minecraft.chest_minecart": "DerlIq ghajbogh tlhIlHal Duj'e'", "entity.minecraft.chicken": "'<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cod": "qaD ghotI'", "entity.minecraft.command_block_minecart": "ra'wI' bu<PERSON>'<PERSON><PERSON> tlhIlHal Duj", "entity.minecraft.cow": "tangqa'", "entity.minecraft.creaking": "HaS", "entity.minecraft.creaking_transient": "HaS", "entity.minecraft.creeper": "creeper", "entity.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "entity.minecraft.dark_oak_chest_boat": "DerlIq ngaSbo<PERSON> <PERSON><PERSON><PERSON> Sor bIQ <PERSON>j", "entity.minecraft.dolphin": "SolbIS", "entity.minecraft.donkey": "Sargh jachbogh", "entity.minecraft.dragon_fireball": "lung'a' qul moQ", "entity.minecraft.drowned": "yInbogh lom yIQ", "entity.minecraft.egg": "QIm vo'lu'bogh", "entity.minecraft.elder_guardian": "'avwI' qup", "entity.minecraft.end_crystal": "end qut", "entity.minecraft.ender_dragon": "ender lung'a'", "entity.minecraft.ender_pearl": "ender moQ vo'lu'bogh", "entity.minecraft.enderman": "enderman", "entity.minecraft.endermite": "endermite", "entity.minecraft.evoker": "reSwI'", "entity.minecraft.evoker_fangs": "reSwI' <PERSON>'mey", "entity.minecraft.experience_bottle": "reSmeH bal vo'lu'pu'bogh", "entity.minecraft.experience_orb": "XP moQ", "entity.minecraft.eye_of_ender": "ender mIn", "entity.minecraft.falling_block": "pum<PERSON><PERSON><PERSON><PERSON> bu<PERSON><PERSON><PERSON>r", "entity.minecraft.falling_block_type": "pumtaHbogh %s", "entity.minecraft.fireball": "qul moQ", "entity.minecraft.firework_rocket": "lop jorwI'", "entity.minecraft.fishing_bobber": "ghotI' wammeH 'aywI'", "entity.minecraft.fox": "qeSHoS", "entity.minecraft.frog": "mabeb", "entity.minecraft.furnace_minecart": "vIncha' ghajbogh tlhIlHal Duj", "entity.minecraft.ghast": "ghast", "entity.minecraft.giant": "tInbogh 'ej wochbogh loD", "entity.minecraft.glow_item_frame": "wewbogh <PERSON>h bey'", "entity.minecraft.glow_squid": "wewbogh cheyIS", "entity.minecraft.goat": "gha'cher", "entity.minecraft.guardian": "'avwI'", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.hoglin": "hoglin", "entity.minecraft.hopper_minecart": "to<PERSON><PERSON> tlhIlHal Duj", "entity.minecraft.horse": "Sargh", "entity.minecraft.husk": "yub", "entity.minecraft.illusioner": "tojwI' 'IDnar pIn'a'", "entity.minecraft.interaction": "Interaction", "entity.minecraft.iron_golem": "'u<PERSON>qan velqa' nuv", "entity.minecraft.item": "<PERSON><PERSON>", "entity.minecraft.item_display": "<PERSON><PERSON>", "entity.minecraft.item_frame": "<PERSON>h bey'", "entity.minecraft.jungle_boat": "ngem yIQ Sor bIQ <PERSON>j", "entity.minecraft.jungle_chest_boat": "DerlIq ngaSbogh ngem yIQ Sor bIQ Duj", "entity.minecraft.killer_bunny": "HoHbogh cheSoy", "entity.minecraft.leash_knot": "rIjmeH tlhegh meS", "entity.minecraft.lightning_bolt": "pe'bIl tIH", "entity.minecraft.lingering_potion": "ratlhbogh 'IDnar taS", "entity.minecraft.llama": "'ImtIy", "entity.minecraft.llama_spit": "'ImtIy tlhepQe'", "entity.minecraft.magma_cube": "vaHbo' HuH", "entity.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "entity.minecraft.mangrove_chest_boat": "DerlIq ngaS<PERSON><PERSON> mangghov Sor bIQ Duj", "entity.minecraft.marker": "ghItlhwI'", "entity.minecraft.minecart": "tlhIlHal Duj", "entity.minecraft.mooshroom": "mooshroom", "entity.minecraft.mule": "Sargh nungmaH", "entity.minecraft.oak_boat": "'oq <PERSON><PERSON> b<PERSON><PERSON>", "entity.minecraft.oak_chest_boat": "DerlIq ngaSbogh 'oq Sor bIQ <PERSON>j", "entity.minecraft.ocelot": "vIghro''a'", "entity.minecraft.ominous_item_spawner": "maQmIgh <PERSON>h boghmoHwI'", "entity.minecraft.painting": "nagh beQ", "entity.minecraft.pale_oak_boat": "'oq wov <PERSON><PERSON> b<PERSON><PERSON>j", "entity.minecraft.pale_oak_chest_boat": "DerlIq ngaSbogh 'oq wov Sor bIQ Duj", "entity.minecraft.panda": "panDa'", "entity.minecraft.parrot": "vIlInHoD", "entity.minecraft.phantom": "ram toQ", "entity.minecraft.pig": "<PERSON><PERSON>'", "entity.minecraft.piglin": "piglin", "entity.minecraft.piglin_brute": "piglin mang", "entity.minecraft.pillager": "weHwI'", "entity.minecraft.player": "QujwI'", "entity.minecraft.polar_bear": "chuch mIl'oD", "entity.minecraft.potion": "'<PERSON>nar taS", "entity.minecraft.pufferfish": "lunbogh ghotI'", "entity.minecraft.rabbit": "cheS", "entity.minecraft.ravager": "puywI'", "entity.minecraft.salmon": "Se'mon", "entity.minecraft.sheep": "DI'raq", "entity.minecraft.shulker": "s<PERSON><PERSON>", "entity.minecraft.shulker_bullet": "shul<PERSON> bach", "entity.minecraft.silverfish": "g<PERSON><PERSON>' boch", "entity.minecraft.skeleton": "Hom loD", "entity.minecraft.skeleton_horse": "nobmeD Sargh", "entity.minecraft.slime": "HuH", "entity.minecraft.small_fireball": "qul moQ mach", "entity.minecraft.sniffer": "larghwI'", "entity.minecraft.snow_golem": "chal chuch velqa' nuv", "entity.minecraft.snowball": "chal chuch moQ", "entity.minecraft.spawner_minecart": "tl<PERSON><PERSON><PERSON> boghmoHwI' ghajbogh tlhIlHal Duj", "entity.minecraft.spectral_arrow": "wewmoHbogh naQjejHom", "entity.minecraft.spider": "voDchuch", "entity.minecraft.splash_potion": "bob<PERSON><PERSON> '<PERSON><PERSON> taS", "entity.minecraft.spruce_boat": "qIrqoD bIQ Duj", "entity.minecraft.spruce_chest_boat": "DerlIq ngaSbogh qIrqoD bIQ Duj", "entity.minecraft.squid": "cheyIS", "entity.minecraft.stray": "nobmeD taD", "entity.minecraft.strider": "yItwI'", "entity.minecraft.tadpole": "ye<PERSON><PERSON><PERSON>", "entity.minecraft.text_display": "ghItlh <PERSON>", "entity.minecraft.tnt": "jorw<PERSON>' chu'lu'pu'bogh", "entity.minecraft.tnt_minecart": "jorwI' ghajbogh tlhIlHal Duj", "entity.minecraft.trader_llama": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.trident": "chonnaQ", "entity.minecraft.tropical_fish": "bIQ ghun ghotI'", "entity.minecraft.tropical_fish.predefined.0": "'anemonIy ghotI'", "entity.minecraft.tropical_fish.predefined.1": "b<PERSON>laq teng ghotI'", "entity.minecraft.tropical_fish.predefined.10": "mor'IS 'ayDol ghotI'", "entity.minecraft.tropical_fish.predefined.11": "Su'wan ghew ghotI' way", "entity.minecraft.tropical_fish.predefined.12": "vIlInHoD ghotI'", "entity.minecraft.tropical_fish.predefined.13": "be' ta' qa' ghotI'", "entity.minecraft.tropical_fish.predefined.14": "SIqlID Doq ghotI'", "entity.minecraft.tropical_fish.predefined.15": "wuS <PERSON><PERSON> bIlenIy ghotI''e'", "entity.minecraft.tropical_fish.predefined.16": "rISwI' Doq ghotI'", "entity.minecraft.tropical_fish.predefined.17": "SIrgh mIjDang", "entity.minecraft.tropical_fish.predefined.18": "tomat na<PERSON> tlhawun ghot<PERSON>'", "entity.minecraft.tropical_fish.predefined.19": "chu'wI' ghotI'", "entity.minecraft.tropical_fish.predefined.2": "bIlu teng ghotI'", "entity.minecraft.tropical_fish.predefined.20": "yelotel parotvIS ghotI'", "entity.minecraft.tropical_fish.predefined.21": "yelo tang ghotI'", "entity.minecraft.tropical_fish.predefined.3": "Su'wan ghew ghotI'", "entity.minecraft.tropical_fish.predefined.4": "SIqlID ghotI'", "entity.minecraft.tropical_fish.predefined.5": "tl<PERSON>un ghot<PERSON>'", "entity.minecraft.tropical_fish.predefined.6": "qa'tan qenDIy be'ta ghotI'", "entity.minecraft.tropical_fish.predefined.7": "DatIybeq ghotI'", "entity.minecraft.tropical_fish.predefined.8": "ta' chopwI' Doq ghotI'", "entity.minecraft.tropical_fish.predefined.9": "gha'cher ghot<PERSON>'", "entity.minecraft.tropical_fish.type.betty": "betIy ghotI'", "entity.minecraft.tropical_fish.type.blockfish": "ngogh ghotI'", "entity.minecraft.tropical_fish.type.brinely": "bIrInlIy ghotI'", "entity.minecraft.tropical_fish.type.clayfish": "DabqI' ghotI'", "entity.minecraft.tropical_fish.type.dasher": "qetwI'", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "SibI'", "entity.minecraft.tropical_fish.type.kob": "qob g<PERSON>'", "entity.minecraft.tropical_fish.type.snooper": "DaqwI'", "entity.minecraft.tropical_fish.type.spotty": "SepatIy ghotI'", "entity.minecraft.tropical_fish.type.stripey": "SatraypIy ghotI'", "entity.minecraft.tropical_fish.type.sunstreak": "Sunstreak", "entity.minecraft.turtle": "la'SIv", "entity.minecraft.vex": "nuQwI'", "entity.minecraft.villager": "vengHom ghot", "entity.minecraft.villager.armorer": "may' Sut chenmoHwI'", "entity.minecraft.villager.butcher": "Ha'DIbaH HoHwI'", "entity.minecraft.villager.cartographer": "pu'jIn chenmoHwI'", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "wIjwI'", "entity.minecraft.villager.fisherman": "ghotI' wamwI'", "entity.minecraft.villager.fletcher": "naQjejHom chenmoHwI'", "entity.minecraft.villager.leatherworker": "qo'rIn DIr chenmoHwI'", "entity.minecraft.villager.librarian": "paq nojwI'", "entity.minecraft.villager.mason": "nagh chenmoHwI'", "entity.minecraft.villager.nitwit": "vumlaHbe'bogh vengHom ghot", "entity.minecraft.villager.none": "vengHom ghot", "entity.minecraft.villager.shepherd": "bolmaq DevwI'", "entity.minecraft.villager.toolsmith": "SommI' chenmoHwI'", "entity.minecraft.villager.weaponsmith": "nuH chenmoHwI'", "entity.minecraft.vindicator": "'obmaQ ghajbogh weHwI'", "entity.minecraft.wandering_trader": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.warden": "HubwI'", "entity.minecraft.wind_charge": "Su<PERSON> bach", "entity.minecraft.witch": "'IDnar pIn'a'", "entity.minecraft.wither": "wither", "entity.minecraft.wither_skeleton": "wither nobmeD", "entity.minecraft.wither_skull": "wither <PERSON>r<PERSON>'", "entity.minecraft.wolf": "ngavyaw'", "entity.minecraft.zoglin": "<PERSON>oglin", "entity.minecraft.zombie": "yInbogh lom", "entity.minecraft.zombie_horse": "yInbogh lom Sargh", "entity.minecraft.zombie_villager": "yInbogh vengHom ghot lom", "entity.minecraft.zombified_piglin": "yInbogh piglin lom", "entity.not_summonable": "Can't summon entity of type %s", "event.minecraft.raid": "yot", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "yot - jeylu'ta'", "event.minecraft.raid.raiders_remaining": "ratlh %s weHwI'", "event.minecraft.raid.victory": "yay", "event.minecraft.raid.victory.full": "yot - yay", "filled_map.buried_treasure": "qengHoD molu'pu'bogh pu'jIn", "filled_map.explorer_jungle": "ngem yIQ lengwI' pu'jIn", "filled_map.explorer_swamp": "puH yIQ lengwI' pu'jIn", "filled_map.id": "Id #%s", "filled_map.level": "(patlh %s/%s)", "filled_map.locked": "ngaQ", "filled_map.mansion": "ngem lengwI' pu'jIn", "filled_map.monument": "bIQ'a' lengwI' pu'jIn", "filled_map.scale": "1:%s men", "filled_map.trial_chambers": "qaD pa' pu'jIn", "filled_map.unknown": "pu'jIn Sov<PERSON>'lu'bogh", "filled_map.village_desert": "Deb vengHom pu'jIn", "filled_map.village_plains": "puH beQ vengHom pu'jIn", "filled_map.village_savanna": "QaDbogh puH nIl", "filled_map.village_snowy": "chal chuch vengHom pu'jIn", "filled_map.village_taiga": "ngem bIr vengHom pu'jIn", "flat_world_preset.minecraft.bottomless_pit": "b<PERSON>'<PERSON><PERSON>emjIq", "flat_world_preset.minecraft.classic_flat": "motlh<PERSON><PERSON> qo' beQqu'", "flat_world_preset.minecraft.desert": "Deb", "flat_world_preset.minecraft.overworld": "ghor", "flat_world_preset.minecraft.redstone_ready": "redstone ghajbeH", "flat_world_preset.minecraft.snowy_kingdom": "chal chuch <PERSON>", "flat_world_preset.minecraft.the_void": "'a<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "'och ghawwI' luvnup", "flat_world_preset.minecraft.water_world": "bIQ qo'", "flat_world_preset.unknown": "Sovbe'lu'", "gameMode.adventure": "Hoq lo'", "gameMode.changed": "%s moj <PERSON>u<PERSON>", "gameMode.creative": "chenmoHmeH Quj", "gameMode.hardcore": "Qatlhqu'!", "gameMode.spectator": "bejwI' lo'", "gameMode.survival": "taHmeH lo'", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "chav ve<PERSON><PERSON> 'agh", "gamerule.blockExplosionDropDecay": "In block interaction explosions, some blocks won't drop their loot", "gamerule.blockExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by block interactions are lost in the explosion.", "gamerule.category.chat": "QIn pat", "gamerule.category.drops": "bu<PERSON><PERSON><PERSON><PERSON> yagh je jo", "gamerule.category.misc": "Sar", "gamerule.category.mobs": "yagh", "gamerule.category.player": "QujwI'", "gamerule.category.spawning": "bogh", "gamerule.category.updates": "qo' De' chu'", "gamerule.commandBlockOutput": "ra'wI' buq'Ir ghItlh 'agh", "gamerule.commandModificationBlockLimit": "Command modification block limit", "gamerule.commandModificationBlockLimit.description": "Number of blocks that can be changed at once by one command, such as fill or clone.", "gamerule.disableElytraMovementCheck": "telDu'qoq Do 'ol 'e' chu'Ha'", "gamerule.disablePlayerMovementCheck": "QujwI' Do 'olQo'", "gamerule.disableRaids": "yotmey chu'Ha'", "gamerule.doDaylightCycle": "tlhetlh jaj 'e' chaw'", "gamerule.doEntityDrops": "Dol luch chagh", "gamerule.doEntityDrops.description": "Controls drops from minecarts (including inventories), item frames, boats, etc.", "gamerule.doFireTick": "tlhetlh qul", "gamerule.doImmediateRespawn": "SIbI' botlhqa'", "gamerule.doInsomnia": "ram to<PERSON> boghmo<PERSON>", "gamerule.doLimitedCrafting": "mutlhmeH mIw poQ", "gamerule.doLimitedCrafting.description": "chu'lu'pu'chugh, mIw ngaQHa' neH lo'laH QujwI'.", "gamerule.doMobLoot": "yagh jo chenmo<PERSON>", "gamerule.doMobLoot.description": "yagh jo <PERSON><PERSON>, <PERSON>nmoHme<PERSON> mo<PERSON><PERSON>y yugh.", "gamerule.doMobSpawning": "yagh boghmoH", "gamerule.doMobSpawning.description": "chaq chut chev pab 'op <PERSON>l.", "gamerule.doPatrolSpawning": "weHwI' ngIvmeH ghommey boghmoH", "gamerule.doTileDrops": "bu<PERSON><PERSON><PERSON><PERSON> chagh", "gamerule.doTileDrops.description": "b<PERSON><PERSON><PERSON><PERSON><PERSON> jo <PERSON>, HenmoHmeH mo<PERSON><PERSON>y yugh.", "gamerule.doTraderSpawning": "bogh yo<PERSON><PERSON><PERSON>", "gamerule.doVinesSpread": "Sach tlhegh tI", "gamerule.doVinesSpread.description": "Controls whether or not the Vines block spreads randomly to adjacent blocks. Does not affect other types of vine blocks such as Weeping Vines, Twisting Vines, etc.", "gamerule.doWardenSpawning": "bogh HubwI'", "gamerule.doWeatherCycle": "MuD update", "gamerule.drowningDamage": "bIQDaq vIQlaH", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON>'D<PERSON>' ngab ender moQ vo'lu'pu'bogh", "gamerule.enderPearlsVanishOnDeath.description": "<PERSON><PERSON><PERSON>' QujwI', ngab ender mo<PERSON>'e' vo'pu'bogh QujwI'vetlh pagh taH", "gamerule.entitiesWithPassengersCanUsePortals": "'u' lojmIt lo'laH raQpo' qengbogh Dol'e'", "gamerule.entitiesWithPassengersCanUsePortals.description": "nether lojmIt, end lojmIt, end DIn je 'el raQpo' qeng<PERSON>gh <PERSON> 'e' chaw'.", "gamerule.fallDamage": "pumchugh rIQchoH", "gamerule.fireDamage": "rIQmoHlaH qul", "gamerule.forgiveDeadPlayers": "<PERSON>gh<PERSON>'bogh QujwI'pu' qeHHa'", "gamerule.forgiveDeadPlayers.description": "SumtaHvIS HeghDI' QujwI' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>e<PERSON>e<PERSON><PERSON>gh yagh 'e' mev.", "gamerule.freezeDamage": "QIH bIr", "gamerule.globalSoundEvents": "Dat wab wanI'", "gamerule.globalSoundEvents.description": "qaSDI' wanI' le' (bogh pIn; poSchoH end lojmIt; latlh je) Dat wab <PERSON>'", "gamerule.keepInventory": "<PERSON><PERSON><PERSON>'DI' QujwI', buq<PERSON><PERSON> pol", "gamerule.lavaSourceConversion": "Hal moj vaHbo'", "gamerule.lavaSourceConversion.description": "ghItbogh vaHbo' retlhDaq cha' vaHbo' Hal tu'lu'DI', <PERSON> moj ghItbogh vaHbo'.", "gamerule.locatorBar": "QujwI' SammeH yamtaw chu'", "gamerule.locatorBar.description": "chu'lu'<PERSON><PERSON><PERSON><PERSON>, jIHDaq QujwI' lurgh ghaSbogh yamtaw cha'lu'.", "gamerule.logAdminCommands": "ra'DI' loHwI' ghaS", "gamerule.maxCommandChainLength": "ra'wI' buq'Ir mIr tIqmeH 'aqroS", "gamerule.maxCommandChainLength.description": "ra'wI' buq'Ir mI<PERSON>ey mIwmey je choH.", "gamerule.maxCommandForkCount": "Command context limit", "gamerule.maxCommandForkCount.description": "Maximum number of contexts that can be used by commands like 'execute as'.", "gamerule.maxEntityCramming": "Dol qochmoHmeH 'aqroS", "gamerule.minecartMaxSpeed": "tlhIlHal Duj 'aqroS Do", "gamerule.minecartMaxSpeed.description": "Maximum default speed of a moving Minecart on land.", "gamerule.mobExplosionDropDecay": "jor<PERSON>' yagh, <PERSON><PERSON> chaghbe' 'op buq'Ir", "gamerule.mobExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by mobs are lost in the explosion.", "gamerule.mobGriefing": "Destructive mob actions chaw'", "gamerule.naturalRegeneration": "rachtaH", "gamerule.playersNetherPortalCreativeDelay": "Player's Nether portal delay in creative mode", "gamerule.playersNetherPortalCreativeDelay.description": "Time (in ticks) that a creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersNetherPortalDefaultDelay": "Player's Nether portal delay in non-creative mode", "gamerule.playersNetherPortalDefaultDelay.description": "Time (in ticks) that a non-creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersSleepingPercentage": "vatlhvI' Qong", "gamerule.playersSleepingPercentage.description": "QujwI' QongnISbogh vatlhvI' ramvam juSmeH", "gamerule.projectilesCanBreakBlocks": "Projectiles can break blocks", "gamerule.projectilesCanBreakBlocks.description": "Controls whether impact projectiles will destroy blocks that are destructible by them.", "gamerule.randomTickSpeed": "'arl<PERSON>h qaS mIwHom Haw", "gamerule.reducedDebugInfo": "Qagh tI'meH De' vuS", "gamerule.reducedDebugInfo.description": "Qagh tI'meH HaSta De' vuS.", "gamerule.sendCommandFeedback": "ghunwI'vaD ra'wI' QInHom ghItlh", "gamerule.showDeathMessages": "<PERSON>gh QIn 'agh", "gamerule.snowAccumulationHeight": "chal chuch <PERSON>j chuq chong", "gamerule.snowAccumulationHeight.description": "chal chuch pat<PERSON>h 'aqroS", "gamerule.spawnChunkRadius": "Spawn chunk radius", "gamerule.spawnChunkRadius.description": "Amount of chunks that stay loaded around the overworld spawn position.", "gamerule.spawnRadius": "bog<PERSON><PERSON><PERSON><PERSON><PERSON> chuq", "gamerule.spawnRadius.description": "Controls the size of the area around the spawn point that players can spawn in.", "gamerule.spectatorsGenerateChunks": "puH chu' lIng bejwI' 'e' chaw'", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "jor<PERSON>' jorw<PERSON>', <PERSON><PERSON> chaghbe' 'op buq'Ir", "gamerule.tntExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by TNT are lost in the explosion.", "gamerule.universalAnger": "Hoch QujwI'mo' QeH", "gamerule.universalAnger.description": "Hoch QujwI' Sum HIv Hub'eghbogh yagh QeH, QeHmoHbogh QujwI' neHHa'. forgiveDeadPlayers chu'<PERSON>'lu'chugh Qapq<PERSON>' <PERSON>.", "gamerule.waterSourceConversion": "<PERSON> moj bIQ", "gamerule.waterSourceConversion.description": "ghItbogh bIQ retlhDaq cha' bIQ Hal tu'lu'DI', <PERSON> moj ghItbogh bIQ.", "generator.custom": "tIgh", "generator.customized": "ngo'bogh qo' tlhIn", "generator.minecraft.amplified": "tInmoH", "generator.minecraft.amplified.info": "ghuH: DutIvmeH neH. De'wI' HoS poQ.", "generator.minecraft.debug_all_block_states": "Qagh tI'meH lo'", "generator.minecraft.flat": "beQqu'", "generator.minecraft.large_biomes": "Sepmey tIn", "generator.minecraft.normal": "motlh", "generator.minecraft.single_biome_surface": "wa' 'umber", "generator.single_biome_caves": "DISmey", "generator.single_biome_floating_islands": "'albogh 'ambay", "gui.abuseReport.attestation": "qar De' Dape<PERSON><PERSON><PERSON> 'e' Da<PERSON><PERSON><PERSON>' 'e' <PERSON><PERSON>' b<PERSON><PERSON>'chugh", "gui.abuseReport.comments": "QInHom", "gui.abuseReport.describe": "Sharing details will help us make a well-informed decision.", "gui.abuseReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.abuseReport.discard.discard": "Leave and Discard Report", "gui.abuseReport.discard.draft": "ru'wI' ngeq", "gui.abuseReport.discard.return": "Continue Editing", "gui.abuseReport.discard.title": "Discard report and comments?", "gui.abuseReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.abuseReport.draft.discard": "Discard", "gui.abuseReport.draft.edit": "Continue Editing", "gui.abuseReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.abuseReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.abuseReport.draft.title": "Edit draft chat report?", "gui.abuseReport.error.title": "Problem sending your report", "gui.abuseReport.message": "Where did you observe the bad behavior?\nThis will help us in researching your case.", "gui.abuseReport.more_comments": "Please describe what happened:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "You are reporting \"%s\".", "gui.abuseReport.name.title": "Report Inappropriate Player Name", "gui.abuseReport.observed_what": "Why are you reporting this?", "gui.abuseReport.read_info": "ja'meH ghItlh ghoj", "gui.abuseReport.reason.alcohol_tobacco_drugs": "'enteD HIq joq", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Someone is encouraging others to partake in illegal drug related activities or encouraging underage drinking.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Child sexual exploitation or abuse", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Someone is talking about or otherwise promoting indecent behavior involving children.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Defamation", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Someone is damaging your or someone else's reputation, for example sharing false information with the aim to exploit or mislead others.", "gui.abuseReport.reason.description": "DelmeH ghItlh:", "gui.abuseReport.reason.false_reporting": "ja'meH ghItlh ngeb ngeHlu'", "gui.abuseReport.reason.generic": "vIja' vIneH", "gui.abuseReport.reason.generic.description": "munuQ / vay' vIparbogh DIgh.", "gui.abuseReport.reason.harassment_or_bullying": "'Irgh", "gui.abuseReport.reason.harassment_or_bullying.description": "Someone is shaming, attacking, or bullying you or someone else. This includes when someone is repeatedly trying to contact you or someone else without consent or posting private personal information about you or someone else without consent (\"doxing\").", "gui.abuseReport.reason.hate_speech": "muSmeH jatlh", "gui.abuseReport.reason.hate_speech.description": "Someone is attacking you or another player based on characteristics of their identity, like religion, race, or sexuality.", "gui.abuseReport.reason.imminent_harm": "Threat of harm to others", "gui.abuseReport.reason.imminent_harm.description": "Someone is threatening to harm you or someone else in real life.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Non-consensual intimate imagery", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Someone is talking about, sharing, or otherwise promoting private and intimate images.", "gui.abuseReport.reason.self_harm_or_suicide": "Self-harm or suicide", "gui.abuseReport.reason.self_harm_or_suicide.description": "Someone is threatening to harm themselves in real life or talking about harming themselves in real life.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "vIrveq", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Someone is talking about, promoting, or threatening to commit acts of terrorism or violent extremism for political, religious, ideological, or other reasons.", "gui.abuseReport.reason.title": "Select Report Category", "gui.abuseReport.report_sent_msg": "ja'meH ghItlhlIj wIHevta'. pItlho'!\n\nDuHDI' 'oH chov ghommaj.", "gui.abuseReport.select_reason": "ja'meH ghItlh buv yIwIv", "gui.abuseReport.send": "ja'meH ghItlh lab", "gui.abuseReport.send.comment_too_long": "QInHom yIwIlmoH", "gui.abuseReport.send.error_message": "An error was returned while sending your report:\n'%s'", "gui.abuseReport.send.generic_error": "Encountered an unexpected error while sending your report.", "gui.abuseReport.send.http_error": "An unexpected HTTP error occurred while sending your report.", "gui.abuseReport.send.json_error": "Encountered malformed payload while sending your report.", "gui.abuseReport.send.no_reason": "ja'meH ghItlh buv yIwIv", "gui.abuseReport.send.not_attested": "bIja'laHmeH bIng ghItlh yIlaD 'ej meyrI'Hom yIwIv", "gui.abuseReport.send.service_unavailable": "Unable to reach the Abuse Reporting service. Please make sure you are connected to the internet and try again.", "gui.abuseReport.sending.title": "ja'meH ghItlhlIj ngeHlu'lI'...", "gui.abuseReport.sent.title": "ja'meH ghItlh ngeHlu'pu'", "gui.abuseReport.skin.title": "QujwI' tu'qom ja'", "gui.abuseReport.title": "QujwI' ja'", "gui.abuseReport.type.chat": "QIn", "gui.abuseReport.type.name": "Qujw<PERSON>' pong", "gui.abuseReport.type.skin": "QujwI' tu'qom", "gui.acknowledge": "jIyaj", "gui.advancements": "chav<PERSON>y", "gui.all": "Hoch", "gui.back": "chegh", "gui.banned.description": "%s\n\n%s\n\nLearn more at the following link: %s", "gui.banned.description.permanent": "chocheghlaHbe' 'e' vIDellaw' 'e' vISov.", "gui.banned.description.reason": "We recently received a report for bad behavior by your account. Our moderators have now reviewed your case and identified it as %s, which goes against the Minecraft Community Standards.", "gui.banned.description.reason_id": "mI': %s", "gui.banned.description.reason_id_message": "mI': %s - %s", "gui.banned.description.temporary": "%s Until then, you can't play online or join Realms.", "gui.banned.description.temporary.duration": "Your account is temporarily suspended and will be reactivated in %s.", "gui.banned.description.unknownreason": "may'<PERSON>q pawtaH ghaH tu'lu'pu'bogh batlh wa'DIch ghaH 'Iw. QumwI'Daq ghoSchoHmoHlaH.", "gui.banned.name.description": "Your current name - \"%s\" - violates our Community Standards. You can play singleplayer, but will need to change your name to play online.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.name.title": "Quj<PERSON>' QujwI' law' ponglIj tuchlu'", "gui.banned.reason.defamation_impersonation_false_information": "Impersonation or sharing information to exploit or mislead others", "gui.banned.reason.drugs": "References to illegal drugs", "gui.banned.reason.extreme_violence_or_gore": "Depictions of real-life excessive violence or gore", "gui.banned.reason.false_reporting": "tlhoy ja'meH ghItlh qarHa' ngeHlu'", "gui.banned.reason.fraud": "Fraudulent acquisition or use of content", "gui.banned.reason.generic_violation": "Violating Community Standards", "gui.banned.reason.harassment_or_bullying": "pIQbogh ghongmeH mu'mey joch", "gui.banned.reason.hate_speech": "Hate speech or discrimination", "gui.banned.reason.hate_terrorism_notorious_figure": "References to hate groups, terrorist organizations, or notorious figures", "gui.banned.reason.imminent_harm_to_person_or_property": "Intent to cause real-life harm to persons or property", "gui.banned.reason.nudity_or_pornography": "Displaying lewd or pornographic material", "gui.banned.reason.sexually_inappropriate": "Topics or content of a sexual nature", "gui.banned.reason.spam_or_advertising": "Spam or advertising", "gui.banned.skin.description": "nugh qangtlhInmaj wem DIrlIj. DIr motlh yIlo', pagh DIr chu' yIwIv.", "gui.banned.skin.title": " tu'qom chaw'be'lu'", "gui.banned.title.permanent": "tangqa' targhHom nuv", "gui.banned.title.temporary": "Do'Ha'", "gui.cancel": "qIl", "gui.chatReport.comments": "QInHom", "gui.chatReport.describe": "Sharing details will help us make a well-informed decision.", "gui.chatReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.chatReport.discard.discard": "mej 'ej ja'meH ghItlh polHa'", "gui.chatReport.discard.draft": "ru'wI' ngeq", "gui.chatReport.discard.return": "choHtaH", "gui.chatReport.discard.title": "Discard report and comments?", "gui.chatReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.chatReport.draft.discard": "polHa'", "gui.chatReport.draft.edit": "choHtaH", "gui.chatReport.draft.quittotitle.content": "bIchoHtaH DaneH'a'?", "gui.chatReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.chatReport.draft.title": "Edit draft chat report?", "gui.chatReport.more_comments": "wan<PERSON>' y<PERSON><PERSON>:", "gui.chatReport.observed_what": "qatlh juja'?", "gui.chatReport.read_info": "ja'meH ghItlh ghoj", "gui.chatReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.chatReport.select_chat": "Select Chat Messages to Report", "gui.chatReport.select_reason": "ja'meH ghItlh buv yIwIv", "gui.chatReport.selected_chat": "%s Chat Message(s) Selected to Report", "gui.chatReport.send": "ja'meH ghItlh ngeH", "gui.chatReport.send.comments_too_long": "Please shorten the comment", "gui.chatReport.send.no_reason": "ja'meH ghItlh buv yIwIv", "gui.chatReport.send.no_reported_messages": "QIn yIwIv", "gui.chatReport.send.too_many_messages": "Trying to include too many messages in the report", "gui.chatReport.title": "Report Player Chat", "gui.chatSelection.context": "Messages surrounding this selection will be included to provide additional context", "gui.chatSelection.fold": "%s QIn So'lu'", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "QIn pat muv %s", "gui.chatSelection.message.narrate": "%s said: %s at %s", "gui.chatSelection.selected": "%s/%s message(s) selected", "gui.chatSelection.title": "Select Chat Messages to Report", "gui.continue": "ruch", "gui.copy_link_to_clipboard": "QaymeH taDaq nelmeS qon", "gui.days": "%s jaj", "gui.done": "pItlh", "gui.down": "bIng", "gui.entity_tooltip.type": "Segh: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "%s tamey laj<PERSON>o'", "gui.fileDropFailure.title": "tamey chel 'e' luj", "gui.hours": "%s rep", "gui.loadingMinecraft": "minecraft lI'lI'", "gui.minutes": "%s tup", "gui.multiLineEditBox.character_limit": "%2$s loch %1$s", "gui.narrate.button": "%s leQ", "gui.narrate.editBox": "%s choHmeH 'aplo': %s", "gui.narrate.slider": "%s tlhamwI'", "gui.narrate.tab": "%s tab", "gui.no": "ghobe'", "gui.none": "pagh", "gui.ok": "luq", "gui.open_report_dir": "ja'meH tetlh naw'", "gui.proceed": "ruch", "gui.recipebook.moreRecipes": "latlh leghmeH nIH leQ 'uy", "gui.recipebook.page": "%2$s loch %1$s", "gui.recipebook.search_hint": "nej...", "gui.recipebook.toggleRecipes.all": "Hoch cha'taH", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON>ey vIjormoHlaHbogh cha'", "gui.recipebook.toggleRecipes.craftable": "Dochmey vIchenmoHlaHbogh cha'", "gui.recipebook.toggleRecipes.smeltable": "Doc<PERSON>ey vItetlaHbogh cha'", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON><PERSON><PERSON>' vutlaHbogh tlhIch vIncha' cha'", "gui.report_to_server": "turwI' ja'", "gui.socialInteractions.blocking_hint": "SeHmeH microsoft mab lo'", "gui.socialInteractions.empty_blocked": "QIn pat<PERSON>aq pagh QujwI' tuchlu'bogh tu'lu'", "gui.socialInteractions.empty_hidden": "QIn pat<PERSON>aq pagh QujwI' So'lu'bogh tu'lu'", "gui.socialInteractions.hidden_in_chat": "%svo' QIn So'", "gui.socialInteractions.hide": "QIn patDaq So'", "gui.socialInteractions.narration.hide": "Hide messages from %s", "gui.socialInteractions.narration.report": "Report player %s", "gui.socialInteractions.narration.show": "Show messages from %s", "gui.socialInteractions.report": "ja'meH ghItlh", "gui.socialInteractions.search_empty": "pongvetlh pongbogh QujwI'e' SamlaHbe'", "gui.socialInteractions.search_hint": "nej...", "gui.socialInteractions.server_label.multiple": "%s - %s QujwI'", "gui.socialInteractions.server_label.single": "%s - QujwI' %s", "gui.socialInteractions.show": "QIn <PERSON>g", "gui.socialInteractions.shown_in_chat": "%svo' QIn 'ang", "gui.socialInteractions.status_blocked": "botlu'", "gui.socialInteractions.status_blocked_offline": "botlu' - lInbe'", "gui.socialInteractions.status_hidden": "So'lu'", "gui.socialInteractions.status_hidden_offline": "So'lu' - lInbe'", "gui.socialInteractions.status_offline": "lInbe'", "gui.socialInteractions.tab_all": "Hoch", "gui.socialInteractions.tab_blocked": "botlu'", "gui.socialInteractions.tab_hidden": "So'lu'", "gui.socialInteractions.title": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.hide": "Humbaba", "gui.socialInteractions.tooltip.report": "QujwI'vaD ja'", "gui.socialInteractions.tooltip.report.disabled": "The reporting service is unavailable", "gui.socialInteractions.tooltip.report.no_messages": "No reportable messages from player %s", "gui.socialInteractions.tooltip.report.not_reportable": "This player can't be reported, because their chat messages can't be verified on this server", "gui.socialInteractions.tooltip.show": "Humbaba", "gui.stats": "ta", "gui.toMenu": "turw<PERSON>' tetlh chegh", "gui.toRealms": "realms tetlh chegh", "gui.toTitle": "per <PERSON><PERSON><PERSON> chegh", "gui.toWorld": "qo' tetlh chegh", "gui.togglable_slot": "<PERSON><PERSON>H yI'uy", "gui.up": "Dung", "gui.waitingForResponse.button.inactive": "chegh (%s lup)", "gui.waitingForResponse.title": "turwI' loStaH", "gui.yes": "HIja'", "hanging_sign.edit": "QIn 'echlet tlhep ghItlh", "instrument.minecraft.admire_goat_horn": "Ho'", "instrument.minecraft.call_goat_horn": "rIt", "instrument.minecraft.dream_goat_horn": "naj", "instrument.minecraft.feel_goat_horn": "jem", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "nej", "instrument.minecraft.sing_goat_horn": "bom", "instrument.minecraft.yearn_goat_horn": "neH", "inventory.binSlot": "<PERSON><PERSON>", "inventory.hotbarInfo": "qogh DapolmeH %1$s %2$s je yIlo'", "inventory.hotbarSaved": "qogh pollu'pu' (<PERSON><PERSON>'meH %1$s %2$s je yIlo')", "item.canBreak": "laH ghor:", "item.canPlace": "laH lan:", "item.canUse.unknown": "Sovbe'lu'", "item.color": "nguvpu': %s", "item.components": "%s 'ay'", "item.disabled": "<PERSON><PERSON>'", "item.durability": "naQghach %s / %s", "item.dyed": "nguv", "item.minecraft.acacia_boat": "'a<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.acacia_chest_boat": "<PERSON><PERSON><PERSON><PERSON> 'a<PERSON><PERSON>'<PERSON>r bIQ <PERSON>'e'", "item.minecraft.allay_spawn_egg": "yIrwI' boghmoHbogh QIm'e'", "item.minecraft.amethyst_shard": "choSom nguv qut", "item.minecraft.angler_pottery_shard": "ghot<PERSON>' wamwI' m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.angler_pottery_sherd": "g<PERSON><PERSON>' m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.apple": "'epIl naH", "item.minecraft.archer_pottery_shard": "Hurw<PERSON>' m<PERSON><PERSON><PERSON> bal 'ay'", "item.minecraft.archer_pottery_sherd": "Hurw<PERSON>' m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.armadillo_scute": "woSwa' ghISDen", "item.minecraft.armadillo_spawn_egg": "woSwa' boghm<PERSON>Hbogh QIm'e'", "item.minecraft.armor_stand": "may' Sut qal'aq", "item.minecraft.arms_up_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> bal 'ay'", "item.minecraft.arms_up_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.arrow": "naQjejHom", "item.minecraft.axolotl_bucket": "'evta' <PERSON><PERSON>", "item.minecraft.axolotl_spawn_egg": "'ev<PERSON>' b<PERSON>hmoHbogh QIm", "item.minecraft.baked_potato": "pat<PERSON> '<PERSON><PERSON><PERSON><PERSON> me<PERSON>'pu'bogh", "item.minecraft.bamboo_chest_raft": "Derl<PERSON>q ghaj<PERSON>gh bambu' bIQ Duj'e'", "item.minecraft.bamboo_raft": "b<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "item.minecraft.bat_spawn_egg": "valq<PERSON>' b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.bee_spawn_egg": "maq<PERSON><PERSON> boghmoHbogh QIm'e'", "item.minecraft.beef": "tang<PERSON>' ghab tlhol", "item.minecraft.beetroot": "'<PERSON><PERSON><PERSON><PERSON>'", "item.minecraft.beetroot_seeds": "'<PERSON><PERSON><PERSON><PERSON> ra<PERSON>'IS", "item.minecraft.beetroot_soup": "'<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "ber<PERSON><PERSON> Sor b<PERSON><PERSON>", "item.minecraft.birch_chest_boat": "DerlIq ghaj<PERSON>gh berIch Sor bIQ Duj'e'", "item.minecraft.black_bundle": "<PERSON><PERSON> buq qIj", "item.minecraft.black_dye": "rItlh qIj", "item.minecraft.black_harness": "'aHra' qIj", "item.minecraft.blade_pottery_shard": "'etlh m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.blade_pottery_sherd": "'etlh m<PERSON><PERSON>gh bal re'nop", "item.minecraft.blaze_powder": "qul yagh Say'qIS", "item.minecraft.blaze_rod": "qul naQ", "item.minecraft.blaze_spawn_egg": "qul tl<PERSON><PERSON><PERSON> boghmoHbogh QIm'e'", "item.minecraft.blue_bundle": "SuDbogh Doj b<PERSON><PERSON> <PERSON><PERSON><PERSON>", "item.minecraft.blue_dye": "SuDbogh rItlh '<PERSON><PERSON>", "item.minecraft.blue_egg": "QIm SuD", "item.minecraft.blue_harness": "SuDbogh 'aHra' '<PERSON><PERSON>", "item.minecraft.bogged_spawn_egg": "nobmeD yIQ boghmoHbogh QIm'e'", "item.minecraft.bolt_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.bolt_armor_trim_smithing_template.new": "Hut'In may' <PERSON>t Ha<PERSON>re<PERSON>", "item.minecraft.bone": "Hom", "item.minecraft.bone_meal": "Hom Say'qIS", "item.minecraft.book": "paq", "item.minecraft.bordure_indented_banner_pattern": "HeH tIy joqwI' HaSreH", "item.minecraft.bow": "HurwI'", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "tIr ngogh", "item.minecraft.breeze_rod": "SuS naQ", "item.minecraft.breeze_spawn_egg": "SuS tlhapragh boghmoHbogh QIm'e'", "item.minecraft.brewer_pottery_shard": "bewSom m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.brewer_pottery_sherd": "bewSom m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.brewing_stand": "taS vutmeH qal'aq", "item.minecraft.brick": "ngogh", "item.minecraft.brown_bundle": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> buq 'ej wov<PERSON>'bogh", "item.minecraft.brown_dye": "Doqbogh r<PERSON><PERSON><PERSON> 'ej wovbe'bogh", "item.minecraft.brown_egg": "QIm <PERSON>", "item.minecraft.brown_harness": "<PERSON><PERSON><PERSON><PERSON> 'a<PERSON><PERSON>' 'ej wovbe'bogh", "item.minecraft.brush": "yachwI'", "item.minecraft.bucket": "HaySIn", "item.minecraft.bundle": "<PERSON><PERSON> t<PERSON><PERSON>", "item.minecraft.bundle.empty": "chIm", "item.minecraft.bundle.empty.description": "wa' <PERSON><PERSON> mergh ngaSlaH", "item.minecraft.bundle.full": "buy'", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "meQ m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.burn_pottery_sherd": "meQ m<PERSON><PERSON>gh bal re'nop", "item.minecraft.camel_spawn_egg": "qa'mel bog<PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.carrot": "qe'rot 'o<PERSON><PERSON>r", "item.minecraft.carrot_on_a_stick": "qe'rot 'o<PERSON>qar naQ", "item.minecraft.cat_spawn_egg": "vIghro' boghmoHbogh QIm'e'", "item.minecraft.cauldron": "bargh'a'", "item.minecraft.cave_spider_spawn_egg": "DIS voDchuch boghmoHbogh QIm'e'", "item.minecraft.chainmail_boots": "mIr <PERSON>", "item.minecraft.chainmail_chestplate": "mIr wep", "item.minecraft.chainmail_helmet": "mIr mIv", "item.minecraft.chainmail_leggings": "mIr yopwa<PERSON>", "item.minecraft.charcoal": "n<PERSON>l'atrogh", "item.minecraft.cherry_boat": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.cherry_chest_boat": "<PERSON>l<PERSON>q ghaj<PERSON><PERSON> tap<PERSON>ej Sor bIQ Duj'e'", "item.minecraft.chest_minecart": "DerlIq ghajbogh tlhIlHal Duj'e'", "item.minecraft.chicken": "'u<PERSON><PERSON><PERSON> tlhol", "item.minecraft.chicken_spawn_egg": "'u<PERSON><PERSON><PERSON> boghmoHbogh QIm'e'", "item.minecraft.chorus_fruit": "bomw<PERSON>' ghom naH", "item.minecraft.clay_ball": "DabqI' 'ay'", "item.minecraft.clock": "tlhaq", "item.minecraft.coal": "ghav nIn", "item.minecraft.coast_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.coast_armor_trim_smithing_template.new": "<PERSON><PERSON> <PERSON>' <PERSON><PERSON>", "item.minecraft.cocoa_beans": "yuch qurgh", "item.minecraft.cod": "qaD ghotI' tlhol", "item.minecraft.cod_bucket": "qaD ghotI' HaySIn", "item.minecraft.cod_spawn_egg": "qaD ghot<PERSON>' boghmoHbogh QIm'e'", "item.minecraft.command_block_minecart": "ra'wI' bu<PERSON>'Ir tlh<PERSON>l<PERSON>al <PERSON>j", "item.minecraft.compass": "SInan", "item.minecraft.cooked_beef": "tang<PERSON>' ghab meQ", "item.minecraft.cooked_chicken": "'uSgheb meQ", "item.minecraft.cooked_cod": "qaD ghotI' meQ", "item.minecraft.cooked_mutton": "DI'raq ghab meQ", "item.minecraft.cooked_porkchop": "targh ghab meQ", "item.minecraft.cooked_rabbit": "cheS meQ", "item.minecraft.cooked_salmon": "Se'mon meQ", "item.minecraft.cookie": "<PERSON><PERSON><PERSON><PERSON> chabHom", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON><PERSON> ngogh", "item.minecraft.cow_spawn_egg": "tang<PERSON>' b<PERSON><PERSON><PERSON><PERSON><PERSON>gh QIm'e'", "item.minecraft.creaking_spawn_egg": "HaSwI' boghmoHbogh QIm'e'", "item.minecraft.creeper_banner_pattern": "joqwI' HaSreH", "item.minecraft.creeper_banner_pattern.desc": "creeper <PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.new": "creeper De<PERSON> joqwI' HaSreH", "item.minecraft.creeper_spawn_egg": "creeper bog<PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.crossbow": "jo' <PERSON>rw<PERSON>'", "item.minecraft.crossbow.projectile": "nIch:", "item.minecraft.crossbow.projectile.multiple": "nIch: %2$s %1$s", "item.minecraft.crossbow.projectile.single": "nIch: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> buq 'ej wov<PERSON>gh", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h r<PERSON><PERSON><PERSON> '<PERSON>j wov<PERSON>gh", "item.minecraft.cyan_harness": "<PERSON><PERSON><PERSON><PERSON>'b<PERSON><PERSON> 'a<PERSON><PERSON>' 'ej wov<PERSON>gh", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON> m<PERSON><PERSON><PERSON> bal 'ay'", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON> m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.dark_oak_boat": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.dark_oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON>r bIQ Duj'e'", "item.minecraft.debug_stick": "tI'meH naQ", "item.minecraft.debug_stick.empty": "DI'onmey Hutlh %s", "item.minecraft.debug_stick.select": "\"%s\" (%s) wIvlu'pu'", "item.minecraft.debug_stick.update": "%s moj \"%s\"", "item.minecraft.diamond": "chanmon", "item.minecraft.diamond_axe": "chan<PERSON> 'obma<PERSON>", "item.minecraft.diamond_boots": "<PERSON>an<PERSON>", "item.minecraft.diamond_chestplate": "chanmon wep", "item.minecraft.diamond_helmet": "chanmon mIv", "item.minecraft.diamond_hoe": "chanmon rutneD", "item.minecraft.diamond_horse_armor": "chan<PERSON> may'<PERSON>t", "item.minecraft.diamond_leggings": "chanmon yopwa<PERSON>", "item.minecraft.diamond_pickaxe": "chanmon tlhIlwI' jan", "item.minecraft.diamond_shovel": "chan<PERSON> g<PERSON>r", "item.minecraft.diamond_sword": "chanmon yan", "item.minecraft.disc_fragment_5": "QoQ ngop 'ay'", "item.minecraft.disc_fragment_5.desc": "QoQ ngop - 5", "item.minecraft.dolphin_spawn_egg": "SolbIS boghmoHbogh QIm'e'", "item.minecraft.donkey_spawn_egg": "jachbogh Sargh boghmoHbogh QIm'e'", "item.minecraft.dragon_breath": "lung'a' tlhuH", "item.minecraft.dried_kelp": "nI'qur QaD", "item.minecraft.drowned_spawn_egg": "yInbogh lom yIQ boghmoHbogh QIm'e'", "item.minecraft.dune_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> may' <PERSON>t Ha<PERSON>reH", "item.minecraft.echo_shard": "ya'rIS 'ay'", "item.minecraft.egg": "QIm", "item.minecraft.elder_guardian_spawn_egg": "'avwI' qup boghm<PERSON><PERSON><PERSON>gh QIm'e'", "item.minecraft.elytra": "telDu'qoq", "item.minecraft.emerald": "patmor", "item.minecraft.enchanted_book": "tlheH paq", "item.minecraft.enchanted_golden_apple": "tlheH qol'om ep'Il naH", "item.minecraft.end_crystal": "end qut", "item.minecraft.ender_dragon_spawn_egg": "ender lung'a' b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.ender_eye": "ender mIn", "item.minecraft.ender_pearl": "ender moQ", "item.minecraft.enderman_spawn_egg": "enderman b<PERSON><PERSON>oH<PERSON>gh QIm'e'", "item.minecraft.endermite_spawn_egg": "endermite boghmoHbogh QIm'e'", "item.minecraft.evoker_spawn_egg": "reSwI' boghmoHbogh QIm'e'", "item.minecraft.experience_bottle": "reSmeH bal", "item.minecraft.explorer_pottery_shard": "tlharghwI' m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.explorer_pottery_sherd": "tlharghw<PERSON>' m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.eye_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.eye_armor_trim_smithing_template.new": "mIn may' Sut HaSreH", "item.minecraft.feather": "bo", "item.minecraft.fermented_spider_eye": "ghew naS mIn roghmoHlu'", "item.minecraft.field_masoned_banner_pattern": "ngogh reD joqwI' HaSreH", "item.minecraft.filled_map": "pu'jIn", "item.minecraft.fire_charge": "qul moQ", "item.minecraft.firework_rocket": "lop jorwI'", "item.minecraft.firework_rocket.flight": "leng poH:", "item.minecraft.firework_rocket.multiple_stars": "%2$s %1$s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "lop jorwI' Hov", "item.minecraft.firework_star.black": "qIj", "item.minecraft.firework_star.blue": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "item.minecraft.firework_star.brown": "<PERSON><PERSON> 'ej wovbe'", "item.minecraft.firework_star.custom_color": "tlhIn", "item.minecraft.firework_star.cyan": "Su<PERSON> 'ej wov", "item.minecraft.firework_star.fade_to": "moj", "item.minecraft.firework_star.flicker": "ghon", "item.minecraft.firework_star.gray": "qIj 'ej wov", "item.minecraft.firework_star.green": "SuDqu'", "item.minecraft.firework_star.light_blue": "Su<PERSON> 'ej wov", "item.minecraft.firework_star.light_gray": "qIj 'ej wovqu'", "item.minecraft.firework_star.lime": "SuDqu' 'ej wovqu'", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON> 'ej wov", "item.minecraft.firework_star.orange": "<PERSON><PERSON> <PERSON><PERSON><PERSON> be<PERSON><PERSON>j rur", "item.minecraft.firework_star.pink": "<PERSON><PERSON><PERSON><PERSON>' 'ej wovqu'", "item.minecraft.firework_star.purple": "ghIrep Hurgh rur", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "tu'qom Sovbe'lu'bogh", "item.minecraft.firework_star.shape.burst": "jorqu'", "item.minecraft.firework_star.shape.creeper": "creeper rur", "item.minecraft.firework_star.shape.large_ball": "moQ tIn", "item.minecraft.firework_star.shape.small_ball": "moQHom", "item.minecraft.firework_star.shape.star": "DujtlhuQ", "item.minecraft.firework_star.trail": "ratlh", "item.minecraft.firework_star.white": "chIS", "item.minecraft.firework_star.yellow": "<PERSON><PERSON> 'ej wovqu'", "item.minecraft.fishing_rod": "ghotI' jonmeH naQ", "item.minecraft.flint": "qIjbo<PERSON> 'ej letbogh nagh", "item.minecraft.flint_and_steel": "qul chenmoHwI'", "item.minecraft.flow_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.flow_armor_trim_smithing_template.new": "Dem'ot may' <PERSON><PERSON>re<PERSON>", "item.minecraft.flow_banner_pattern": "joqwI' HaSreH", "item.minecraft.flow_banner_pattern.desc": "Dem'ot", "item.minecraft.flow_banner_pattern.new": "Dem'ot joqwI' HaSreH", "item.minecraft.flow_pottery_sherd": "De<PERSON>'ot m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.flower_banner_pattern": "joqwI' HaSreH", "item.minecraft.flower_banner_pattern.desc": "'<PERSON><PERSON><PERSON>", "item.minecraft.flower_banner_pattern.new": "'InSong <PERSON>gh joqwI' HaSreH", "item.minecraft.flower_pot": "'In<PERSON><PERSON> bal", "item.minecraft.fox_spawn_egg": "qeSHoS boghmoHbogh QIm'e'", "item.minecraft.friend_pottery_shard": "jup m<PERSON><PERSON><PERSON> bal 'ay'", "item.minecraft.friend_pottery_sherd": "jup m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.frog_spawn_egg": "ma<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.furnace_minecart": "vIncha' ghajbogh tlhIlHal Duj", "item.minecraft.ghast_spawn_egg": "ghast b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.ghast_tear": "ghast mIn 'onroS", "item.minecraft.glass_bottle": "'al'on bal", "item.minecraft.glistering_melon_slice": "ghonbogh qaq naH baylaD", "item.minecraft.globe_banner_pattern": "joqwI' HaSreH", "item.minecraft.globe_banner_pattern.desc": "qo'", "item.minecraft.globe_banner_pattern.new": "yuQ joqwI' HaSreH", "item.minecraft.glow_berries": "wewbogh naH", "item.minecraft.glow_ink_sac": "wewbogh rItlh 'enDeq", "item.minecraft.glow_item_frame": "wewbogh <PERSON>h bey'", "item.minecraft.glow_squid_spawn_egg": "wewbogh cheyIS boghmoHbogh QIm", "item.minecraft.glowstone_dust": "wewbogh nagh Say'qIS", "item.minecraft.goat_horn": "gha'cher pu'", "item.minecraft.goat_spawn_egg": "<PERSON><PERSON>'<PERSON>er b<PERSON><PERSON>oHbogh QIm", "item.minecraft.gold_ingot": "qol'om ngogh", "item.minecraft.gold_nugget": "qol'om <PERSON>'", "item.minecraft.golden_apple": "qol'om 'epIl naH", "item.minecraft.golden_axe": "qol'om 'obmaQ", "item.minecraft.golden_boots": "qol'om <PERSON>", "item.minecraft.golden_carrot": "qol'om qe'rot 'o<PERSON>qar", "item.minecraft.golden_chestplate": "qol'om wep", "item.minecraft.golden_helmet": "qol'om mIv", "item.minecraft.golden_hoe": "qol'om rutneD", "item.minecraft.golden_horse_armor": "qol'om <PERSON>h may' Sut", "item.minecraft.golden_leggings": "qol'om yopwaH", "item.minecraft.golden_pickaxe": "qol'om tlhIlwI' jan", "item.minecraft.golden_shovel": "qol'om ghevjur", "item.minecraft.golden_sword": "qol'om yan", "item.minecraft.gray_bundle": "qIjbogh <PERSON><PERSON> buq 'ej wovbogh", "item.minecraft.gray_dye": "qIjbo<PERSON> 'ej wovbogh rItlh", "item.minecraft.gray_harness": "qIjbogh 'aHra' 'ej wovbogh", "item.minecraft.green_bundle": "<PERSON><PERSON> buq <PERSON>'", "item.minecraft.green_dye": "rItlh <PERSON>qu'", "item.minecraft.green_harness": "'aHra' SuDqu'", "item.minecraft.guardian_spawn_egg": "'avwI' boghmoHbogh QIm'e'", "item.minecraft.gunpowder": "ngat", "item.minecraft.guster_banner_pattern": "joqwI' HaSreH", "item.minecraft.guster_banner_pattern.desc": "cheqwI'", "item.minecraft.guster_banner_pattern.new": "cheqwI' joqwI' HaSreH", "item.minecraft.guster_pottery_sherd": "cheqwI' m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.happy_ghast_spawn_egg": "ghast <PERSON><PERSON> boghm<PERSON>H<PERSON>gh QIm'e'", "item.minecraft.harness": "'aHra'", "item.minecraft.heart_of_the_sea": "bIQ'a' tIq", "item.minecraft.heart_pottery_shard": "tIq m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.heart_pottery_sherd": "tIq m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.heartbreak_pottery_shard": "ghor tIq m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.heartbreak_pottery_sherd": "ghor tIq m<PERSON><PERSON>gh bal re'nop", "item.minecraft.hoglin_spawn_egg": "hoglin b<PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.honey_bottle": "<PERSON><PERSON><PERSON><PERSON> bal", "item.minecraft.honeycomb": "DayqIr SIr'o'", "item.minecraft.hopper_minecart": "to<PERSON><PERSON> tlhIlHal Duj", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.host_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.host_armor_trim_smithing_template.new": "ma'wI' may' <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "bey m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.howl_pottery_sherd": "bey m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.husk_spawn_egg": "yub b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.ink_sac": "qIjbogh rItlh", "item.minecraft.iron_axe": "'uSqan 'ob<PERSON><PERSON>", "item.minecraft.iron_boots": "'uSqan DaS", "item.minecraft.iron_chestplate": "'u<PERSON><PERSON><PERSON> wep", "item.minecraft.iron_golem_spawn_egg": "'uSqan velqa' nuv boghmoHbogh QIm'e'", "item.minecraft.iron_helmet": "'uSqan mIv", "item.minecraft.iron_hoe": "'uSqan rutneD", "item.minecraft.iron_horse_armor": "'<PERSON><PERSON><PERSON><PERSON> may'Sut", "item.minecraft.iron_ingot": "'<PERSON><PERSON><PERSON><PERSON> ngogh", "item.minecraft.iron_leggings": "'<PERSON><PERSON><PERSON><PERSON> yopwaH", "item.minecraft.iron_nugget": "'<PERSON><PERSON><PERSON><PERSON>'", "item.minecraft.iron_pickaxe": "'uSqan tlhIlwI' jan", "item.minecraft.iron_shovel": "'<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_sword": "'<PERSON><PERSON><PERSON><PERSON> yan", "item.minecraft.item_frame": "<PERSON>h bey'", "item.minecraft.jungle_boat": "ngem yIQ Sor bIQ <PERSON>j", "item.minecraft.jungle_chest_boat": "DerlIq ghajbogh ngem yIQ Sor bIQ Duj'e'", "item.minecraft.knowledge_book": "Sov paq", "item.minecraft.lapis_lazuli": "Doltop", "item.minecraft.lava_bucket": "vaHbo' HaySIn", "item.minecraft.lead": "rIjmeH tlhegh", "item.minecraft.leather": "qo'rIn DIr", "item.minecraft.leather_boots": "qo'rIn DIr DaS", "item.minecraft.leather_chestplate": "qo'rIn DIr wep", "item.minecraft.leather_helmet": "qo'rIn DIr mIv", "item.minecraft.leather_horse_armor": "qo'rIn D<PERSON>r <PERSON> may'<PERSON>t", "item.minecraft.leather_leggings": "qo'rIn DIr yopwaH", "item.minecraft.light_blue_bundle": "SuDbogh Doj buq 'ej wov<PERSON>gh", "item.minecraft.light_blue_dye": "SuDbogh 'ej wovbogh rItlh", "item.minecraft.light_blue_harness": "SuDbogh 'aHra' 'ej wov<PERSON>gh", "item.minecraft.light_gray_bundle": "qIjbogh <PERSON><PERSON> buq 'ej wovqu'bogh", "item.minecraft.light_gray_dye": "qIj<PERSON><PERSON> 'ej wovqu'bogh rItlh", "item.minecraft.light_gray_harness": "qIjbo<PERSON> 'a<PERSON><PERSON>' 'ej wovqu'bogh", "item.minecraft.lime_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh <PERSON><PERSON> buq 'ej wovqu'bogh", "item.minecraft.lime_dye": "Su<PERSON><PERSON><PERSON> 'ej wovbe'bogh rItlh", "item.minecraft.lime_harness": "SuDq<PERSON>'bogh '<PERSON><PERSON><PERSON>' 'ej wovqu'bogh", "item.minecraft.lingering_potion": "ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.awkward": "ratlhbogh 'IDnar taS mItHa'", "item.minecraft.lingering_potion.effect.empty": "ratlhbogh '<PERSON>nar taS'e' chenmoHlaHbe'bogh vay'", "item.minecraft.lingering_potion.effect.fire_resistance": "qul 'ommeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.harming": "QIH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.healing": "Do Hergh ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.infested": "ngejmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.invisibility": "So'meH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.leaping": "pe'vIl SupmoHmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.levitation": "'almoHmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.luck": "Do'moHmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.mundane": "ratlhbogh 'IDnar taS motlh", "item.minecraft.lingering_potion.effect.night_vision": "ram <PERSON>hl<PERSON><PERSON><PERSON>ach ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.oozing": "charmoHmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.poison": "tar ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.regeneration": "rachmoHtaHmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.slow_falling": "peDmoHmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.slowness": "QIt vIHmoHmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.strength": "HoS ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.swiftness": "Do ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.thick": "ratlhbogh 'IDnar taS jeD", "item.minecraft.lingering_potion.effect.turtle_master": "la'SIv pIn'a' ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.water": "ratlhbogh bIQ bal", "item.minecraft.lingering_potion.effect.water_breathing": "nIwqen ratl<PERSON>gh 'IDnar taS", "item.minecraft.lingering_potion.effect.weakness": "pujmoHmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.weaving": "nIqmeH ratlhbogh 'IDnar taS", "item.minecraft.lingering_potion.effect.wind_charged": "SuS bach ratlhbogh 'IDnar taS", "item.minecraft.llama_spawn_egg": "'Im<PERSON><PERSON><PERSON> bog<PERSON><PERSON><PERSON>gh QIm'e'", "item.minecraft.lodestone_compass": "pe<PERSON><PERSON><PERSON>", "item.minecraft.mace": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> buq 'ej wov<PERSON>gh", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h r<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON>", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON><PERSON>'b<PERSON><PERSON> '<PERSON><PERSON><PERSON><PERSON> 'ej w<PERSON><PERSON>gh", "item.minecraft.magma_cream": "vaH<PERSON>' tlhagh", "item.minecraft.magma_cube_spawn_egg": "vaH<PERSON><PERSON> <PERSON>u<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.mangrove_boat": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.mangrove_chest_boat": "<PERSON>l<PERSON><PERSON> g<PERSON> mangghov Sor bIQ Duj'e'", "item.minecraft.map": "pu'jIn chIm", "item.minecraft.melon_seeds": "qaq naH raS'IS", "item.minecraft.melon_slice": "qaq naH baylaD", "item.minecraft.milk_bucket": "nIm HaySIn", "item.minecraft.minecart": "tlhIlHal Duj", "item.minecraft.miner_pottery_shard": "tlhIlwI' mIllogh bal 'ay'", "item.minecraft.miner_pottery_sherd": "tlhIlwI' m<PERSON><PERSON>gh bal re'nop", "item.minecraft.mojang_banner_pattern": "joqwI' HaSreH", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "vIqraq joqwI' HaSreH", "item.minecraft.mooshroom_spawn_egg": "moos<PERSON><PERSON> boghmoH<PERSON>gh QIm'e'", "item.minecraft.mourner_pottery_shard": "'ubwI' m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.mourner_pottery_sherd": "'ubwI' m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.mule_spawn_egg": "qu'vat<PERSON>h b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.mushroom_stew": "yav 'atlhqam tlhIq", "item.minecraft.music_disc_11": "bom jengva'", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "bom jengva'", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "bom jengva'", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "bom jengva'", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "bom jengva'", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "bom jengva'", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "bom jengva'", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "bom jengva'", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "bom jengva'", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "bom jengva'", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "bom jengva'", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "bom jengva'", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "bom jengva'", "item.minecraft.music_disc_otherside.desc": "<PERSON>", "item.minecraft.music_disc_pigstep": "bom jengva'", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - pigstep", "item.minecraft.music_disc_precipice": "bom jengva'", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "bom jengva'", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "bom jengva'", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "bom jengva'", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "bom jengva'", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "bom jengva'", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "bom jengva'", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "DI'raq ghab tlhol", "item.minecraft.name_tag": "pong per", "item.minecraft.nautilus_shell": "qatvoH nagh DIr", "item.minecraft.nether_brick": "nether ngogh", "item.minecraft.nether_star": "nether Hov", "item.minecraft.nether_wart": "nether 'e<PERSON><PERSON><PERSON> 'at<PERSON>m", "item.minecraft.netherite_axe": "netherite 'obmaQ", "item.minecraft.netherite_boots": "netherite Da<PERSON>", "item.minecraft.netherite_chestplate": "netherite wep", "item.minecraft.netherite_helmet": "netherite mIv", "item.minecraft.netherite_hoe": "netherite rutneD", "item.minecraft.netherite_ingot": "netherite ngogh", "item.minecraft.netherite_leggings": "netherite yopwaH", "item.minecraft.netherite_pickaxe": "netherite tlhIlwI' jan", "item.minecraft.netherite_scrap": "netherite baS 'ay'", "item.minecraft.netherite_shovel": "netherite ghevjur", "item.minecraft.netherite_sword": "netherite yan", "item.minecraft.netherite_upgrade_smithing_template": "mItlhmeH ghantoH", "item.minecraft.netherite_upgrade_smithing_template.new": "netherite DubwI'", "item.minecraft.oak_boat": "'oq <PERSON><PERSON> b<PERSON><PERSON>", "item.minecraft.oak_chest_boat": "<PERSON><PERSON><PERSON><PERSON> ghaj<PERSON><PERSON> 'oq <PERSON>r bIQ Duj'e'", "item.minecraft.ocelot_spawn_egg": "vIghro''a' boghmoHbogh QIm'e'", "item.minecraft.ominous_bottle": "maQm<PERSON>gh bal", "item.minecraft.ominous_trial_key": "maQmIgh qaD ngaQHa'moHwI'", "item.minecraft.orange_bundle": "Doqbo<PERSON> Doj buq 'ej beq<PERSON>j rurbogh", "item.minecraft.orange_dye": "Doqbogh rItl<PERSON> '<PERSON>j beqpuj rurbogh", "item.minecraft.orange_harness": "Doqbo<PERSON> 'aH<PERSON>' 'ej beq<PERSON>j rurbogh", "item.minecraft.painting": "nagh beQ", "item.minecraft.pale_oak_boat": "'oq wov <PERSON><PERSON> b<PERSON><PERSON>j", "item.minecraft.pale_oak_chest_boat": "DerlIq ngaSbogh 'oq wov Sor bIQ Duj", "item.minecraft.panda_spawn_egg": "panDa' b<PERSON>hm<PERSON>H<PERSON>gh QIm'e'", "item.minecraft.paper": "nav", "item.minecraft.parrot_spawn_egg": "vIlInHoD boghmoHbogh QIm'e'", "item.minecraft.phantom_membrane": "ram toQ yelmo'", "item.minecraft.phantom_spawn_egg": "ram to<PERSON> bog<PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.pig_spawn_egg": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.piglin_banner_pattern": "joqwI' HaSreH", "item.minecraft.piglin_banner_pattern.desc": "ghIch", "item.minecraft.piglin_banner_pattern.new": "ghIch joqwI' HaSreH", "item.minecraft.piglin_brute_spawn_egg": "piglin mang boghmoH<PERSON>gh QIm'e'", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON> boghmoH<PERSON>gh QIm'e'", "item.minecraft.pillager_spawn_egg": "weHwI' boghmoHbogh QIm'e'", "item.minecraft.pink_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h Doj buq 'ej wovqu'bogh", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON> <PERSON><PERSON>j wovbogh rItlh", "item.minecraft.pink_harness": "<PERSON><PERSON><PERSON><PERSON>'bog<PERSON> '<PERSON><PERSON><PERSON>' 'ej wovqu'bogh", "item.minecraft.pitcher_plant": "bal San'emDer", "item.minecraft.pitcher_pod": "bal San'emDer raS'IS", "item.minecraft.plenty_pottery_shard": "qengHoD mIllogh bal 'ay'", "item.minecraft.plenty_pottery_sherd": "qengHoD mIllogh bal re'nop", "item.minecraft.poisonous_potato": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.polar_bear_spawn_egg": "chuch mIl'o<PERSON> b<PERSON><PERSON><PERSON><PERSON>gh QIm'e'", "item.minecraft.popped_chorus_fruit": "bomw<PERSON>' ghom naH ghorlu'pu'bogh", "item.minecraft.porkchop": "targh ghab tlhol", "item.minecraft.potato": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion": "'<PERSON>nar taS", "item.minecraft.potion.effect.awkward": "'IDnar taS mItHa'", "item.minecraft.potion.effect.empty": "'IDnar taS'e' chenmoHlaHbe'bogh vay'", "item.minecraft.potion.effect.fire_resistance": "qul 'ommeH '<PERSON>nar taS", "item.minecraft.potion.effect.harming": "QIH 'IDnar taS", "item.minecraft.potion.effect.healing": "Do Hergh 'IDnar taS", "item.minecraft.potion.effect.infested": "ngejmeH 'IDnar taS", "item.minecraft.potion.effect.invisibility": "So'meH 'IDnar taS", "item.minecraft.potion.effect.leaping": "pe'vIl SupmoHmeH 'IDnar taS", "item.minecraft.potion.effect.levitation": "'almoHmeH 'IDnar taS", "item.minecraft.potion.effect.luck": "Do'moHmeH 'IDnar taS", "item.minecraft.potion.effect.mundane": "'IDnar taS motlh", "item.minecraft.potion.effect.night_vision": "ram leghlaHghach '<PERSON>nar taS", "item.minecraft.potion.effect.oozing": "charmoHmeH 'IDnar taS", "item.minecraft.potion.effect.poison": "tar 'IDnar taS", "item.minecraft.potion.effect.regeneration": "rachmoHtaHmeH 'IDnar taS", "item.minecraft.potion.effect.slow_falling": "peDmoHmeH 'IDnar taS", "item.minecraft.potion.effect.slowness": "QIt vIHmoHwI' 'IDnar taS", "item.minecraft.potion.effect.strength": "HoS 'IDnar taS", "item.minecraft.potion.effect.swiftness": "Do 'IDnar taS", "item.minecraft.potion.effect.thick": "'<PERSON><PERSON> taS jeD", "item.minecraft.potion.effect.turtle_master": "la'SIv pIn'a' 'IDnar taS", "item.minecraft.potion.effect.water": "bIQ bal", "item.minecraft.potion.effect.water_breathing": "nIwqen 'IDnar taS", "item.minecraft.potion.effect.weakness": "pujmoHmeH 'IDnar taS", "item.minecraft.potion.effect.weaving": "nIqmeH 'IDnar taS", "item.minecraft.potion.effect.wind_charged": "SuS bach 'IDnar taS", "item.minecraft.pottery_shard_archer": "Hurw<PERSON>' m<PERSON><PERSON><PERSON> bal 'ay'", "item.minecraft.pottery_shard_arms_up": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> bal 'ay'", "item.minecraft.pottery_shard_prize": "tev m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.pottery_shard_skull": "Dughr<PERSON>' m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.powder_snow_bucket": "chal chuch Say'qIS HaySIn", "item.minecraft.prismarine_crystals": "prismarine q<PERSON>y", "item.minecraft.prismarine_shard": "prismarine 'ay'", "item.minecraft.prize_pottery_shard": "tev m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.prize_pottery_sherd": "tev m<PERSON><PERSON>gh bal re'nop", "item.minecraft.pufferfish": "lunbogh ghotI'", "item.minecraft.pufferfish_bucket": "lunbogh ghotI' HaySIn", "item.minecraft.pufferfish_spawn_egg": "lunbogh ghotI' boghmoHbogh QIm'e'", "item.minecraft.pumpkin_pie": "'aquta' chab", "item.minecraft.pumpkin_seeds": "'aquta' raS'IS", "item.minecraft.purple_bundle": "ghIrep Hurgh rurbogh Doj buq", "item.minecraft.purple_dye": "ghIrep Hurgh rurbogh rItlh'e'", "item.minecraft.purple_harness": "ghIrep Hurgh rurbogh 'aHra'", "item.minecraft.quartz": "nether choSom", "item.minecraft.rabbit": "cheS tlhol", "item.minecraft.rabbit_foot": "cheS qam", "item.minecraft.rabbit_hide": "cheS DIr", "item.minecraft.rabbit_spawn_egg": "cheS boghmoHbogh QIm'e'", "item.minecraft.rabbit_stew": "cheS chatlh", "item.minecraft.raiser_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.raiser_armor_trim_smithing_template.new": "pepwI' may' <PERSON>t Ha<PERSON>re<PERSON>", "item.minecraft.ravager_spawn_egg": "Ha'DIbaH <PERSON>'bogh boghmoHbogh QIm'e'", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON>q tlhol", "item.minecraft.raw_gold": "qol'om tlhol", "item.minecraft.raw_iron": "'u<PERSON><PERSON><PERSON> tlhol", "item.minecraft.recovery_compass": "cheghwI' SInan", "item.minecraft.red_bundle": "<PERSON><PERSON> buq <PERSON>'", "item.minecraft.red_dye": "r<PERSON><PERSON><PERSON>'", "item.minecraft.red_harness": "'a<PERSON><PERSON>' Doqqu'", "item.minecraft.redstone": "redstone Say'qIS", "item.minecraft.resin_brick": "reb<PERSON><PERSON> ngogh", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON><PERSON>'", "item.minecraft.rib_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.rib_armor_trim_smithing_template.new": "joQ may' <PERSON><PERSON>", "item.minecraft.rotten_flesh": "Ha'DIbaH non", "item.minecraft.saddle": "ba'qIn", "item.minecraft.salmon": "Se'mon tlhol", "item.minecraft.salmon_bucket": "<PERSON><PERSON>mon <PERSON>", "item.minecraft.salmon_spawn_egg": "Se'mon b<PERSON><PERSON><PERSON>H<PERSON><PERSON> QIm'e'", "item.minecraft.scrape_pottery_sherd": "tey m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.scute": "ghISDen", "item.minecraft.sentry_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.sentry_armor_trim_smithing_template.new": "'avwI' may' <PERSON><PERSON> Ha<PERSON>re<PERSON>", "item.minecraft.shaper_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.shaper_armor_trim_smithing_template.new": "chenmoHwI' may' <PERSON>t Ha<PERSON>reH", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON> <PERSON> bal 'ay'", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON> <PERSON> bal re'nop", "item.minecraft.shears": "cha''etlh pe'wI''a'", "item.minecraft.sheep_spawn_egg": "D<PERSON>'raq boghmoH<PERSON>gh QIm'e'", "item.minecraft.shelter_pottery_shard": "lulIgh mIllogh bal 'ay'", "item.minecraft.shelter_pottery_sherd": "lul<PERSON>gh mIllogh bal re'nop", "item.minecraft.shield": "yoD", "item.minecraft.shield.black": "yoD qIj", "item.minecraft.shield.blue": "SuDbogh yo<PERSON> <PERSON><PERSON><PERSON>", "item.minecraft.shield.brown": "<PERSON><PERSON><PERSON><PERSON> yo<PERSON> 'ej wovbe'bogh", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h yo<PERSON> 'ej wov<PERSON>gh", "item.minecraft.shield.gray": "qIjbogh yoD 'ej wovbogh", "item.minecraft.shield.green": "yoD <PERSON>qu'", "item.minecraft.shield.light_blue": "SuDbogh yoD 'ej wovbogh", "item.minecraft.shield.light_gray": "qIj<PERSON><PERSON> yoD 'ej wovqu'bogh", "item.minecraft.shield.lime": "<PERSON><PERSON><PERSON><PERSON>'bogh yoD 'ej wovqu'bogh", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h yo<PERSON> <PERSON>ej w<PERSON><PERSON>gh", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON><PERSON> yo<PERSON> 'ej beq<PERSON>j rurbogh", "item.minecraft.shield.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>bogh yo<PERSON> 'ej wovqu'bogh", "item.minecraft.shield.purple": "ghIrep Hurgh rurbogh yoD'e'", "item.minecraft.shield.red": "<PERSON><PERSON>'", "item.minecraft.shield.white": "yoD chIS", "item.minecraft.shield.yellow": "Su<PERSON><PERSON><PERSON> yoD 'ej wovqu'bogh", "item.minecraft.shulker_shell": "ghachw<PERSON>' nagh <PERSON>r", "item.minecraft.shulker_spawn_egg": "s<PERSON><PERSON> b<PERSON><PERSON><PERSON>H<PERSON>gh QIm'e'", "item.minecraft.sign": "QIn 'echlet", "item.minecraft.silence_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.silence_armor_trim_smithing_template.new": "tam may' <PERSON><PERSON>re<PERSON>", "item.minecraft.silverfish_spawn_egg": "SIrl<PERSON>y ghew boghm<PERSON>H<PERSON>gh QIm'e'", "item.minecraft.skeleton_horse_spawn_egg": "nobmeD Sargh boghmoHbogh QIm'e'", "item.minecraft.skeleton_spawn_egg": "nobmeD boghmoHbogh QIm'e'", "item.minecraft.skull_banner_pattern": "joqwI' HaSreH", "item.minecraft.skull_banner_pattern.desc": "Dughr<PERSON><PERSON> <PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "DughrI' Degh joqwI' HasreH", "item.minecraft.skull_pottery_shard": "Dughr<PERSON>' m<PERSON><PERSON>gh bal 'ay'", "item.minecraft.skull_pottery_sherd": "Dughr<PERSON>' m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.slime_ball": "HuH moQ", "item.minecraft.slime_spawn_egg": "<PERSON><PERSON> boghmoHbogh QIm'e'", "item.minecraft.smithing_template": "mItlhmeH ghantoH", "item.minecraft.smithing_template.applies_to": "bop:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "ba<PERSON> ng<PERSON>, qut ghap y<PERSON>lan", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON>' <PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "may' <PERSON>t yIlan", "item.minecraft.smithing_template.armor_trim.ingredients": "ba<PERSON> ngogh qut je", "item.minecraft.smithing_template.ingredients": "qurme':", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "netherite ngogh y<PERSON>lan", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "chanmon luch", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "chan<PERSON> may' <PERSON>, nu<PERSON>, <PERSON>mm<PERSON>' ghap y<PERSON>lan", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "netherite ngogh", "item.minecraft.smithing_template.upgrade": "Dub:", "item.minecraft.sniffer_spawn_egg": "larghwI' boghmoHbogh QIm'e'", "item.minecraft.snort_pottery_shard": "<PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON> bal 'ay'", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON> bal re'nop", "item.minecraft.snout_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.snout_armor_trim_smithing_template.new": "ghIch may' Sut HaSreH", "item.minecraft.snow_golem_spawn_egg": "chal chuch velqa' nuv boghmoHbogh QIm'e'", "item.minecraft.snowball": "chal chuch moQ", "item.minecraft.spectral_arrow": "le' bach", "item.minecraft.spider_eye": "voDchuch mIn", "item.minecraft.spider_spawn_egg": "voD<PERSON><PERSON> boghmoHbogh QIm'e'", "item.minecraft.spire_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.spire_armor_trim_smithing_template.new": "chalqa<PERSON> may' <PERSON><PERSON> Ha<PERSON>re<PERSON>", "item.minecraft.splash_potion": "bob<PERSON><PERSON> '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.awkward": "bobDar 'IDnar taS mItHa'", "item.minecraft.splash_potion.effect.empty": "bobDar '<PERSON><PERSON> taS'e' chenmoHlaHbe'bogh vay'", "item.minecraft.splash_potion.effect.fire_resistance": "qul 'ommeH bobD<PERSON> '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.harming": "QIH bobDar '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.healing": "<PERSON> bob<PERSON><PERSON> '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.infested": "ngejmeH bobDar '<PERSON>nar taS", "item.minecraft.splash_potion.effect.invisibility": "So'meH bobDar '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.leaping": "pe'vIl SupmoHmeH bobDar 'IDnar taS", "item.minecraft.splash_potion.effect.levitation": "'almoHmeH bobDar '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.luck": "Do'moHmeH bobDar 'IDnar taS", "item.minecraft.splash_potion.effect.mundane": "'bobDar '<PERSON><PERSON> taS motlh", "item.minecraft.splash_potion.effect.night_vision": "ram <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bob<PERSON><PERSON> '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.oozing": "charmoHmeH bobDar '<PERSON>nar taS", "item.minecraft.splash_potion.effect.poison": "tar bob<PERSON><PERSON> '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.regeneration": "rachmoHtaHmeH bobDar 'IDnar taS", "item.minecraft.splash_potion.effect.slow_falling": "peDmoHmeH bobDar 'IDnar taS", "item.minecraft.splash_potion.effect.slowness": "QIt vIHmoHmeH bobDar 'IDnar taS", "item.minecraft.splash_potion.effect.strength": "HoS bobDar '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.swiftness": "Do bobDar '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.thick": "bob<PERSON><PERSON> '<PERSON><PERSON> taS jeD", "item.minecraft.splash_potion.effect.turtle_master": "la'SIv pIn'a' bobDar 'IDnar taS", "item.minecraft.splash_potion.effect.water": "bob<PERSON>ar bIQ bal", "item.minecraft.splash_potion.effect.water_breathing": "nIwqen bob<PERSON><PERSON> '<PERSON><PERSON> taS", "item.minecraft.splash_potion.effect.weakness": "pujmoHmeH bobDar 'IDnar taS", "item.minecraft.splash_potion.effect.weaving": "nIqmeH bobDar 'IDnar taS", "item.minecraft.splash_potion.effect.wind_charged": "SuS bach bob<PERSON><PERSON> '<PERSON><PERSON> taS", "item.minecraft.spruce_boat": "qIrqoD bIQ Duj", "item.minecraft.spruce_chest_boat": "DerlIq g<PERSON> qIrqoD bIQ Duj'e'", "item.minecraft.spyglass": "Hov tut", "item.minecraft.squid_spawn_egg": "chey<PERSON> boghmoHbogh QIm'e'", "item.minecraft.stick": "naQ", "item.minecraft.stone_axe": "nagh 'obmaQ", "item.minecraft.stone_hoe": "nagh rutneD", "item.minecraft.stone_pickaxe": "nagh tlhIlw<PERSON>' jan", "item.minecraft.stone_shovel": "nagh ghevjur", "item.minecraft.stone_sword": "nagh yan", "item.minecraft.stray_spawn_egg": "nobmeD taD boghmoHbogh QIm'e'", "item.minecraft.strider_spawn_egg": "yItwI' boghmoHbogh QIm'e'", "item.minecraft.string": "SIrgh", "item.minecraft.sugar": "Su'ghar", "item.minecraft.suspicious_stew": "tlhIq nub", "item.minecraft.sweet_berries": "<PERSON><PERSON>'", "item.minecraft.tadpole_bucket": "<PERSON><PERSON><PERSON><PERSON> HaySIn", "item.minecraft.tadpole_spawn_egg": "mabeb ghu b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.tide_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.tide_armor_trim_smithing_template.new": "vI'Ir may' <PERSON>t HaSreH", "item.minecraft.tipped_arrow": "'IDnar naQjejHom", "item.minecraft.tipped_arrow.effect.awkward": "'IDnar naQjejHom", "item.minecraft.tipped_arrow.effect.empty": "'IDnar naQjejHom'e' chenmoHlaHbe'bogh vay'", "item.minecraft.tipped_arrow.effect.fire_resistance": "qul 'ommeH naQjejHom", "item.minecraft.tipped_arrow.effect.harming": "QIH naQjejHom", "item.minecraft.tipped_arrow.effect.healing": "Do Hergh naQjejHom", "item.minecraft.tipped_arrow.effect.infested": "ngejmeH naQjejHom", "item.minecraft.tipped_arrow.effect.invisibility": "So'meH naQjejHom", "item.minecraft.tipped_arrow.effect.leaping": "pe'vIl SupmoHmeH naQjejHom", "item.minecraft.tipped_arrow.effect.levitation": "'almoHmeH naQjejHom", "item.minecraft.tipped_arrow.effect.luck": "Do'moHmeH naQjejHom", "item.minecraft.tipped_arrow.effect.mundane": "'IDnar naQjejHom", "item.minecraft.tipped_arrow.effect.night_vision": "ram <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> naQjejHom", "item.minecraft.tipped_arrow.effect.oozing": "charmoHmeH naQjejHom", "item.minecraft.tipped_arrow.effect.poison": "tar naQjejHom", "item.minecraft.tipped_arrow.effect.regeneration": "rachmoHtaHmeH naQjejHom", "item.minecraft.tipped_arrow.effect.slow_falling": "peDmoHmeH naQjejHom", "item.minecraft.tipped_arrow.effect.slowness": "QIt vIHmoHmeH naQjejHom", "item.minecraft.tipped_arrow.effect.strength": "HoS naQjejHom", "item.minecraft.tipped_arrow.effect.swiftness": "Do naQjejHom", "item.minecraft.tipped_arrow.effect.thick": "'IDnar naQjejHom", "item.minecraft.tipped_arrow.effect.turtle_master": "la'SIv pIn'a' naQjejHom", "item.minecraft.tipped_arrow.effect.water": "bobDar naQjejHom", "item.minecraft.tipped_arrow.effect.water_breathing": "nIwqen naQjejHom", "item.minecraft.tipped_arrow.effect.weakness": "pujmoHmeH naQjejHom", "item.minecraft.tipped_arrow.effect.weaving": "nIqmeH naQjejHom", "item.minecraft.tipped_arrow.effect.wind_charged": "SuS bach naQjejHom", "item.minecraft.tnt_minecart": "jorwI' tlhIlHal Duj", "item.minecraft.torchflower_seeds": "Sech 'InSong raS'IS", "item.minecraft.totem_of_undying": "jubmoHmeH HewHom", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> boghmoHbogh QIm'e'", "item.minecraft.trial_key": "qaD ngaQHa'moHwI'", "item.minecraft.trident": "chonnaQ", "item.minecraft.tropical_fish": "bIQ ghun ghotI'", "item.minecraft.tropical_fish_bucket": "bIQ ghun ghotI' HaySIn", "item.minecraft.tropical_fish_spawn_egg": "bIQ ghun ghotI' boghmoHbogh QIm'e'", "item.minecraft.turtle_helmet": "la'SIv nagh DIr", "item.minecraft.turtle_scute": "la'SIv ghISDen", "item.minecraft.turtle_spawn_egg": "la'SIv boghmoHbogh QIm'e'", "item.minecraft.vex_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.vex_armor_trim_smithing_template.new": "nuQ may' <PERSON>t HaSreH", "item.minecraft.vex_spawn_egg": "nuQwI' boghmoHbogh QIm'e'", "item.minecraft.villager_spawn_egg": "vengHom ghot boghmoHbogh QIm", "item.minecraft.vindicator_spawn_egg": "'obmaQ ghajbogh weHwI' boghmoHbogh QIm", "item.minecraft.wandering_trader_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.ward_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.ward_armor_trim_smithing_template.new": "'av may' <PERSON><PERSON>", "item.minecraft.warden_spawn_egg": "HubwI' boghmoHbogh QIm'e'", "item.minecraft.warped_fungus_on_a_stick": "'atlhqam SuD naQ", "item.minecraft.water_bucket": "bIQ <PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "chIjwI' may' <PERSON>t HaSreH", "item.minecraft.wheat": "wevpev", "item.minecraft.wheat_seeds": "wevpev raS'IS", "item.minecraft.white_bundle": "<PERSON><PERSON> buq ch<PERSON>", "item.minecraft.white_dye": "rItlh chIS", "item.minecraft.white_harness": "'aHra' chIS", "item.minecraft.wild_armor_trim_smithing_template": "mItlhmeH ghantoH", "item.minecraft.wild_armor_trim_smithing_template.new": "tlhab may' <PERSON><PERSON> HaSreH", "item.minecraft.wind_charge": "Su<PERSON> bach", "item.minecraft.witch_spawn_egg": "'IDnar pIn'a' boghmoHbogh QIm", "item.minecraft.wither_skeleton_spawn_egg": "wither nobme<PERSON> bog<PERSON>oH<PERSON>gh QIm'e'", "item.minecraft.wither_spawn_egg": "wither b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> QIm'e'", "item.minecraft.wolf_armor": "ngavyaw' may' Sut", "item.minecraft.wolf_spawn_egg": "ngavyaw' b<PERSON><PERSON><PERSON><PERSON><PERSON>gh QIm'e'", "item.minecraft.wooden_axe": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_hoe": "<PERSON>r <PERSON><PERSON> rutneD", "item.minecraft.wooden_pickaxe": "<PERSON>r <PERSON><PERSON> tlhIlw<PERSON>' jan", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "<PERSON>r <PERSON><PERSON> yan", "item.minecraft.writable_book": "paq ghItlhwI' bo je", "item.minecraft.written_book": "ghItlhta' paq", "item.minecraft.yellow_bundle": "SuDbogh <PERSON>j buq 'ej wovqu'bogh", "item.minecraft.yellow_dye": "SuDbogh rI<PERSON><PERSON> 'ej wovqu'bogh", "item.minecraft.yellow_harness": "SuD<PERSON><PERSON> 'aH<PERSON>' 'ej wovqu'bogh", "item.minecraft.zoglin_spawn_egg": "zog<PERSON> b<PERSON><PERSON>oH<PERSON>gh QIm'e'", "item.minecraft.zombie_horse_spawn_egg": "yInbogh lom Sargh boghmoHbogh QIm", "item.minecraft.zombie_spawn_egg": "yInbogh lom boghmoHbogh QIm'e'", "item.minecraft.zombie_villager_spawn_egg": "yInbogh vengHom ghot lom boghmoHbogh QIm'e'", "item.minecraft.zombified_piglin_spawn_egg": "yInbogh piglin lom boghmoHbogh QIm'e'", "item.modifiers.any": "lo'lu'DI':", "item.modifiers.armor": "tuQlu'DI':", "item.modifiers.body": "tuQlu'taHvIS:", "item.modifiers.chest": "roDaq tu'lu'DI':", "item.modifiers.feet": "qamDaq tu'lu'DI':", "item.modifiers.hand": "'uchlu'DI':", "item.modifiers.head": "nachDaq tu'lu'DI':", "item.modifiers.legs": "'uSDaq tu'lu'DI':", "item.modifiers.mainhand": "ghop wa'DIchDaq:", "item.modifiers.offhand": "ghop cha'DIchDaq tu'lu'DI':", "item.modifiers.saddle": "ba'qIn tuQtaHvIS:", "item.nbt_tags": "nbt: %s per(mey)", "item.op_block_warning.line1": "yIghuH:", "item.op_block_warning.line2": "<PERSON><PERSON><PERSON>m lo'lu'chugh, chaq '<PERSON>gh turlu'", "item.op_block_warning.line3": "Do not use unless you know the exact contents!", "item.unbreakable": "ghorlaHbe' vay'", "itemGroup.buildingBlocks": "chenmoHmeH buq'Ir", "itemGroup.coloredBlocks": "bu<PERSON><PERSON><PERSON><PERSON> nguv", "itemGroup.combat": "vIq", "itemGroup.consumables": "<PERSON><PERSON>", "itemGroup.crafting": "chenmoH", "itemGroup.foodAndDrink": "<PERSON><PERSON>", "itemGroup.functional": "bu<PERSON>'<PERSON>r lI'", "itemGroup.hotbar": "qog<PERSON><PERSON> pollu'bogh", "itemGroup.ingredients": "qurme'", "itemGroup.inventory": "taHmeH lo' buqmey", "itemGroup.natural": "bu<PERSON><PERSON><PERSON><PERSON> but", "itemGroup.op": "turwI' vu'wI' SommI'", "itemGroup.redstone": "redstone", "itemGroup.search": "<PERSON><PERSON> nej", "itemGroup.spawnEggs": "boghmoHbogh QIm", "itemGroup.tools": "SommI'", "item_modifier.unknown": "Doch choHwI' Sovbe'lu'bogh: %s", "jigsaw_block.final_state": "moj:", "jigsaw_block.generate": "lIng", "jigsaw_block.joint.aligned": "jIrmoHlaHbe' vay'", "jigsaw_block.joint.rollable": "jIrmoHlaH vay'", "jigsaw_block.joint_label": "wI<PERSON><PERSON> <PERSON><PERSON>", "jigsaw_block.keep_jigsaws": "Qay'mol pol", "jigsaw_block.levels": "Patlh: %s", "jigsaw_block.name": "pong:", "jigsaw_block.placement_priority": "lanmeH ngep:", "jigsaw_block.placement_priority.tooltip": "When this Jigsaw block connects to a piece, this is the order in which that piece is processed for connections in the wider structure.\n\nPieces will be processed in descending priority with insertion order breaking ties.", "jigsaw_block.pool": "ngoQ vey:", "jigsaw_block.selection_priority": "wIvvaD ngep:", "jigsaw_block.selection_priority.tooltip": "When the parent piece is being processed for connections, this is the order in which this Jigsaw block attempts to connect to its target piece.\n\nJigsaws will be processed in descending priority with random ordering breaking ties.", "jigsaw_block.target": "DoS pong:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "chav<PERSON>y", "key.attack": "HIv ghap <PERSON>'", "key.back": "Backwards yIt", "key.categories.creative": "chenmoHmeH lo'", "key.categories.gameplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.inventory": "buq<PERSON><PERSON>", "key.categories.misc": "latlh", "key.categories.movement": "vIHghach", "key.categories.multiplayer": "QujwI' law'", "key.categories.ui": "<PERSON><PERSON><PERSON>", "key.chat": "QIn pat poSmoH", "key.command": "mInDu'lIj ra'", "key.drop": "<PERSON><PERSON> chu<PERSON>", "key.forward": "Forwards yIt", "key.fullscreen": "jIH naQ cha' cha'<PERSON>' pagh", "key.hotbar.1": "qogh buq wa'", "key.hotbar.2": "qogh buq cha'", "key.hotbar.3": "qogh buq wej", "key.hotbar.4": "qogh buq loS", "key.hotbar.5": "qogh buq vagh", "key.hotbar.6": "qogh buq jav", "key.hotbar.7": "qogh buq <PERSON>", "key.hotbar.8": "qogh buq chorgh", "key.hotbar.9": "qogh buq <PERSON>", "key.inventory": "buqmey poSmoH/SoQmoH", "key.jump": "<PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "ghItlhHa'", "key.keyboard.caps.lock": "ngutlh tIn ratlhmoH", "key.keyboard.comma": ",", "key.keyboard.delete": "Qaw'", "key.keyboard.down": "bIng SIqwI'", "key.keyboard.end": "HochDIch", "key.keyboard.enter": "'el", "key.keyboard.equal": "=", "key.keyboard.escape": "nargh", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "wa'DIch", "key.keyboard.insert": "vev", "key.keyboard.keypad.0": "mI' tlhat pagh", "key.keyboard.keypad.1": "mI' tlhat wa'", "key.keyboard.keypad.2": "mI' tlhat cha'", "key.keyboard.keypad.3": "mI' tlhat wej", "key.keyboard.keypad.4": "mI' tlhat loS", "key.keyboard.keypad.5": "mI' tlhat vagh", "key.keyboard.keypad.6": "mI' tlhat jav", "key.keyboard.keypad.7": "mI' tlhat <PERSON>ch", "key.keyboard.keypad.8": "mI' tlhat chorgh", "key.keyboard.keypad.9": "mI' t<PERSON>hat <PERSON>", "key.keyboard.keypad.add": "mI' tlhat +", "key.keyboard.keypad.decimal": "mI' tlhat vI'", "key.keyboard.keypad.divide": "mI' tlhat /", "key.keyboard.keypad.enter": "mI' tlhat 'el", "key.keyboard.keypad.equal": "mI' tlhat =", "key.keyboard.keypad.multiply": "mI' tlhat *", "key.keyboard.keypad.subtract": "mI' tlhat -", "key.keyboard.left": "poS SIqwI'", "key.keyboard.left.alt": "poS SetqIn", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "poS SeH", "key.keyboard.left.shift": "poS ‹shift›", "key.keyboard.left.win": "poS windows", "key.keyboard.menu": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "mI' ratlhmoH", "key.keyboard.page.down": "tenwal veb", "key.keyboard.page.up": "tenwal vorgh", "key.keyboard.pause": "yev", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON><PERSON> qon", "key.keyboard.right": "nIH SIqwI'", "key.keyboard.right.alt": "nIH SetqIn", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "nIH SeH", "key.keyboard.right.shift": "nIH ‹shift›", "key.keyboard.right.win": "nIH windows", "key.keyboard.scroll.lock": "HotlhwI' ratlhmoH", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "'o<PERSON><PERSON><PERSON>", "key.keyboard.tab": "nechchu'", "key.keyboard.unknown": "Sovbe'lu'", "key.keyboard.up": "Dung SIqwI'", "key.keyboard.world.1": "qo' wa'", "key.keyboard.world.2": "qo' cha'", "key.left": "poS strafe", "key.loadToolbarActivator": "qogh chu'wI' lI'", "key.mouse": "chu'wI' %1$s", "key.mouse.left": "poS leQ", "key.mouse.middle": "rutlhHom", "key.mouse.right": "nIH leQ", "key.pickItem": "bu<PERSON><PERSON><PERSON><PERSON> wIv", "key.playerlist": "QujwI' tetlh", "key.quickActions": "<PERSON><PERSON>'me<PERSON> ngaj", "key.right": "nIH strafe", "key.saveToolbarActivator": "qogh chu'wI' ngeq", "key.screenshot": "screenshot ghaH", "key.smoothCamera": "much mIllogh choH", "key.sneak": "joD", "key.socialInteractions": "<PERSON><PERSON><PERSON><PERSON>", "key.spectatorOutlines": "QujwI' cha' (bejwI')", "key.sprint": "qet", "key.swapOffhand": "ghop<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "key.togglePerspective": "toggle Perspective", "key.use": "<PERSON>h lo'/buq'Ir lan", "known_server_link.announcements": "<PERSON><PERSON><PERSON>", "known_server_link.community": "yuvwan", "known_server_link.community_guidelines": "yuvwan chutmey", "known_server_link.feedback": "vuD", "known_server_link.forums": "rIchmeH 'Internet Daqmey", "known_server_link.news": "De' chu'", "known_server_link.report_bug": "turwI' Qagh ja'", "known_server_link.status": "Dotlh", "known_server_link.support": "ngaq", "known_server_link.website": "'Internet Daq", "lanServer.otherPlayers": "latlh QujwI'pu' <PERSON>", "lanServer.port": "rarmeH mI'", "lanServer.port.invalid": "Not a valid port.\nLeave the edit box empty or enter a number between 1024 and 65535.", "lanServer.port.invalid.new": "Not a valid port.\nLeave the edit box empty or enter a number between %s and %s.", "lanServer.port.unavailable": "Port not available.\nLeave the edit box empty or enter a different number between 1024 and 65535.", "lanServer.port.unavailable.new": "Port not available.\nLeave the edit box empty or enter a different number between %s and %s.", "lanServer.scanning": "na<PERSON>ev <PERSON><PERSON><PERSON><PERSON><PERSON>", "lanServer.start": "SIr'o' Sum qo' cher", "lanServer.title": "SIr'o' Sum qo'", "language.code": "tlh", "language.name": "tlhIngan <PERSON>l", "language.region": "tlhIngan wo'", "lectern.take_book": "paq tlhap", "loading.progress": "%s vatlhvI'", "mco.account.privacy.info": "mojang <PERSON>'meH chut je ghoj", "mco.account.privacy.info.button": "Read more about GDPR", "mco.account.privacy.information": "Mojang implements certain procedures to help protect children and their privacy including complying with the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to obtain parental consent before accessing your Realms account.", "mco.account.privacyinfo": "Puq privacy puqpu' Online Privacy Protection 'AY' ((coppa)) je Sa' De' Protection Regulation ((gdpr)) HeQ DaH nobvam 'ej QaD QaH mIw be implements mojang.\n\nparental ghIb pa' qo' account naw' Suq chaq bImejnIS.\n\nvaj older minecraft account (QonoS SoH in with username), account migrate mojang account in order to qo' naw' bImejnIS Daghaj.", "mco.account.update": "mab choH", "mco.activity.noactivity": "No activity for the past %s day(s)", "mco.activity.title": "QujwI' poH", "mco.backup.button.download": "retbogh lI'", "mco.backup.button.reset": "qo' lIngqa'", "mco.backup.button.restore": "SabHa'", "mco.backup.button.upload": "qo' lab", "mco.backup.changes.tooltip": "choHmey", "mco.backup.entry": "chach (%s)", "mco.backup.entry.description": "DelmeH ghItlh", "mco.backup.entry.enabledPack": "vey chu'lu'", "mco.backup.entry.gameDifficulty": "<PERSON>uj qa<PERSON>h", "mco.backup.entry.gameMode": "<PERSON>uj lo'", "mco.backup.entry.gameServerVersion": "<PERSON>uj turw<PERSON><PERSON> <PERSON><PERSON>", "mco.backup.entry.name": "pong", "mco.backup.entry.seed": "raS'IS", "mco.backup.entry.templateName": "qal'aq pong", "mco.backup.entry.undefined": "choH So'be'lu'", "mco.backup.entry.uploaded": "labta'", "mco.backup.entry.worldType": "qo' <PERSON>gh", "mco.backup.generate.world": "qo' lIng", "mco.backup.info.title": "choH'e' yughbe'bogh chach ret", "mco.backup.narration": "%s chach", "mco.backup.nobackups": "<PERSON><PERSON> pagh chach ghaj <PERSON>vam.", "mco.backup.restoring": "qo' SabHa'", "mco.backup.unknown": "Sovbe'lu'", "mco.brokenworld.download": "lI'", "mco.brokenworld.downloaded": "lI'ta'", "mco.brokenworld.message.line1": "Dubel<PERSON><PERSON><PERSON>gh reset pagh latlh qo' wIv.", "mco.brokenworld.message.line2": "Qo' download singleplayer la<PERSON> je wutlh SoH.", "mco.brokenworld.minigame.title": "QujHom leHbe'choHlu'", "mco.brokenworld.nonowner.error": "DubelmoHchugh loS qo' owner qo' reset", "mco.brokenworld.nonowner.title": "notlh qo'", "mco.brokenworld.play": "<PERSON><PERSON><PERSON>", "mco.brokenworld.reset": "lIngqa'", "mco.brokenworld.title": "<PERSON><PERSON><PERSON>' ngaq Qu'mey potlh qo'", "mco.client.incompatible.msg.line1": "realms lo'laHbe' De'wI'lIj.", "mco.client.incompatible.msg.line2": "minecraft Segh ret yIlo'.", "mco.client.incompatible.msg.line3": "ngongmeH Segh lo'laHbe' realms.", "mco.client.incompatible.title": "mItbe' De'wI'vam!", "mco.client.outdated.stable.version": "nelchuqbe' <PERSON><PERSON>'bogh (%s) realms je.\n\nminecraft Segh ret yIlo'.", "mco.client.unsupported.snapshot.version": "nelchuqbe' <PERSON><PERSON>'bogh (%s) realms je.\n\nwaHmeH SeghvamvaD realms naw'laHbe'.", "mco.compatibility.downgrade": "Downgrade", "mco.compatibility.downgrade.description": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work.\n\nA backup of your world will be saved under \"World Backups\". Please restore your world if needed.", "mco.compatibility.incompatible.popup.title": "nel<PERSON><PERSON> <PERSON><PERSON>", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> je.", "mco.compatibility.incompatible.series.popup.message": "qaSDI' qo'vam <PERSON>uj <PERSON>t, nIqHom mI' %s lo'lu'. DaH nIqHom mI' %s Dalo'.\n\nnelchuqbe' nIqHom mI'meyvam. Seghvam DaQujmeH qo' chu' DapoQ.", "mco.compatibility.unverifiable.message": "The version this world was last played in could not be verified. If the world gets upgraded or downgraded, a backup will be automatically created and saved under \"World Backups\".", "mco.compatibility.unverifiable.title": "Compatibility not verifiable", "mco.compatibility.upgrade": "Upgrade", "mco.compatibility.upgrade.description": "This world was last played in version %s; you are on version %s.\n\nA backup of your world will be saved under \"World Backups\".\n\nPlease restore your world if needed.", "mco.compatibility.upgrade.friend.description": "This world was last played in version %s; you are on version %s.\n\nA backup of the world will be saved under \"World Backups\".\n\nThe owner of the Realm can restore the world if needed.", "mco.compatibility.upgrade.title": "Do you really want to upgrade this world?", "mco.configure.current.minigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.activityfeed.disabled": "QIjwI' ta' tetlh Qotlhpu'lu'; ru'", "mco.configure.world.backup": "qo' chach", "mco.configure.world.buttons.activity": "QujwI' poH", "mco.configure.world.buttons.close": "realm SoQmoH", "mco.configure.world.buttons.delete": "Qaw'", "mco.configure.world.buttons.done": "pItlh", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "QujwI' rIt", "mco.configure.world.buttons.moreoptions": "la<PERSON><PERSON>", "mco.configure.world.buttons.newworld": "qo' chu'", "mco.configure.world.buttons.open": "realm poSmoH", "mco.configure.world.buttons.options": "qo' DuH", "mco.configure.world.buttons.players": "QujwI'pu'", "mco.configure.world.buttons.region_preference": "Sep wIv...", "mco.configure.world.buttons.resetworld": "qo' qan rap chenmoH", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "SeHlaw", "mco.configure.world.buttons.subscription": "roD DIlmeH mab", "mco.configure.world.buttons.switchminigame": "QujHom pIm Quj", "mco.configure.world.close.question.line1": "ghIq realmlIj naw'laHbe'lu'", "mco.configure.world.close.question.line2": "b<PERSON>ruch Dane<PERSON>bej'a'?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "realm SoQmoHlI'...", "mco.configure.world.commandBlocks": "Computing Device", "mco.configure.world.delete.button": "realm Qaw'", "mco.configure.world.delete.question.line1": "<PERSON><PERSON><PERSON><PERSON>; ru<PERSON><PERSON><PERSON> m<PERSON>w.", "mco.configure.world.delete.question.line2": "b<PERSON>ruch Dane<PERSON>bej'a'?", "mco.configure.world.description": "realm DelmeH ghItlh", "mco.configure.world.edit.slot.name": "qo' pong", "mco.configure.world.edit.subscreen.adventuremap": "'op DuHmey Qot<PERSON>hlu' Ho<PERSON> 'oHmo' qo'vam", "mco.configure.world.edit.subscreen.experience": "'op DuHmey Qotlhlu' wanI' 'oHmo' qo'vam", "mco.configure.world.edit.subscreen.inspiration": "pIlmoHmeH qo' Dalo'mo' 'op DuHmey Dalo'laHbe'", "mco.configure.world.forceGameMode": "<PERSON>u<PERSON> lo' raD", "mco.configure.world.invite.narration": "%s ruSve<PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON>", "mco.configure.world.invite.profile.name": "pong", "mco.configure.world.invited": "vItlhobqa'", "mco.configure.world.invited.number": "rItlu' (%s)", "mco.configure.world.invites.normal.tooltip": "Motlh user", "mco.configure.world.invites.ops.tooltip": "'orwI'", "mco.configure.world.invites.remove.tooltip": "Teq", "mco.configure.world.leave.question.line1": "realmvam Damejchugh realmvam DaleghmeH DarItnISqa'lu'", "mco.configure.world.leave.question.line2": "b<PERSON>ruch Dane<PERSON>bej'a'?", "mco.configure.world.loading": "realm lI'lI'", "mco.configure.world.location": "<PERSON><PERSON>", "mco.configure.world.minigame": "QujHomvam: %s", "mco.configure.world.name": "realm pong", "mco.configure.world.opening": "realm poSmoHtaH...", "mco.configure.world.players.error": "ghaHbe' pong<PERSON><PERSON>h ghaH QujwI'", "mco.configure.world.players.inviting": "Inviting player...", "mco.configure.world.players.title": "QujwI'pu'", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "qo'lIj lingqa' 'ej ngab qo'vam", "mco.configure.world.reset.question.line2": "b<PERSON>ruch Dane<PERSON>bej'a'?", "mco.configure.world.resourcepack.question": "realmvamDaq bIQujmeH jo vey le' DapoQ\n\n'oH Dal<PERSON> 'ej bIQuj DaneH'a'?", "mco.configure.world.resourcepack.question.line1": "realmvam DaQujmeH mIllogh vey tlhIn DaghajnIS", "mco.configure.world.resourcepack.question.line2": "Dal<PERSON>' 'ej <PERSON>a'?", "mco.configure.world.restore.download.question.line1": "qo' lI' 'ej wa' QujwI' qo'meyDaq chel", "mco.configure.world.restore.download.question.line2": "bIruch DaneH'a'?", "mco.configure.world.restore.question.line1": "%s (%s) poH chegh qo'lIj", "mco.configure.world.restore.question.line2": "b<PERSON>ruch Dane<PERSON>bej'a'?", "mco.configure.world.settings.expired": "realm notlh <PERSON><PERSON>'laHbe'", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.slot": "qo' %%mey", "mco.configure.world.slot.empty": "chIm", "mco.configure.world.slot.switch.question.line1": "latlh qo' lo'choH realmlIj", "mco.configure.world.slot.switch.question.line2": "b<PERSON>ruch Dane<PERSON>bej'a'?", "mco.configure.world.slot.tooltip": "qo' lo'", "mco.configure.world.slot.tooltip.active": "muv", "mco.configure.world.slot.tooltip.minigame": "realm qa' QujHom", "mco.configure.world.spawnAnimals": "Ha'DIbaHmey chenmoH", "mco.configure.world.spawnMonsters": "tlhapraghmey chenmoH", "mco.configure.world.spawnNPCs": "yoqpu''e' SeHbe' QujwI' chenmoH", "mco.configure.world.spawnProtection": "Qujw<PERSON><PERSON> <PERSON><PERSON> <PERSON>", "mco.configure.world.spawn_toggle.message": "Turning this option off will remove all existing entities of that type", "mco.configure.world.spawn_toggle.message.npc": "Turning this option off will remove all existing entities of that type, like Villagers", "mco.configure.world.spawn_toggle.title": "ghuH!", "mco.configure.world.status": "Dotlh", "mco.configure.world.subscription.day": "jaj", "mco.configure.world.subscription.days": "jajmey", "mco.configure.world.subscription.expired": "vanpu'lu'", "mco.configure.world.subscription.extend": "roD DIlmeH mab nI'qa'moH", "mco.configure.world.subscription.less_than_a_day": "jaj nI' law' poHvam nI' puS", "mco.configure.world.subscription.month": "jar", "mco.configure.world.subscription.months": "jarmey", "mco.configure.world.subscription.recurring.daysleft": "taHmoH De'wI' qaSDI'", "mco.configure.world.subscription.recurring.info": "Changes made to your Realms subscription such as stacking time or turning off recurring billing will not be reflected until your next bill date.", "mco.configure.world.subscription.remaining.days": "%1$s jaj", "mco.configure.world.subscription.remaining.months": "%1$s jar", "mco.configure.world.subscription.remaining.months.days": "%1$s jar, %2$s jaj", "mco.configure.world.subscription.start": "bI'reS poH", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "ratlhbogh poH", "mco.configure.world.subscription.title": "roD DIlmeH mablIj", "mco.configure.world.subscription.unknown": "Sovbe'lu'", "mco.configure.world.switch.slot": "qo' chenmoH", "mco.configure.world.switch.slot.subtitle": "chIm qo'vam, chay' qo'l<PERSON>j <PERSON>", "mco.configure.world.title": "realm choH:", "mco.configure.world.uninvite.player": "Are you sure that you want to uninvite '%s'?", "mco.configure.world.uninvite.question": "be 'e' uninvite DaneH'a'", "mco.configure.worlds.title": "qo'", "mco.connect.authorizing": "ngaQHa'moHlI'...", "mco.connect.connecting": "realmvaD lInlI'...", "mco.connect.failed": "lInchoHmoH realm De'wI'lIj je 'e' luj", "mco.connect.region": "Server region: %s", "mco.connect.success": "pItlh", "mco.create.world": "chenmoH", "mco.create.world.error": "pong'e' DapeSqu'nIS", "mco.create.world.failed": "qo' chenmo<PERSON> 'e' luj!", "mco.create.world.reset.title": "qo' lInglI'...", "mco.create.world.skip": "buSHa'", "mco.create.world.subtitle": "realm chu'vaD chelmeH qo' yIwIv; 'utbe'", "mco.create.world.wait": "qo' chenmoH...", "mco.download.cancelled": "lI' 'e' qIlta'", "mco.download.confirmation.line1": "qo' DalI'bogh tIn law' %s tIn puS", "mco.download.confirmation.line2": "realmlIjDaq qo'vam <PERSON>'la<PERSON>be'", "mco.download.confirmation.oversized": "qo' DalI'lI'bogh tIn law' %s tIn puS\n\nrealmlIjDaq qo'vam <PERSON>'laHbe'", "mco.download.done": "lI'ta'", "mco.download.downloading": "lI'lI'", "mco.download.extracting": "tlhaplI'", "mco.download.failed": "lI'ta'Ha'", "mco.download.percent": "%s %%", "mco.download.preparing": "lab 'e' qeqlI'", "mco.download.resourcePack.fail": "mIllogh vey lI' 'e' luj!", "mco.download.speed": "(%s/lup)", "mco.download.speed.narration": "%s/lup", "mco.download.title": "qo' ret lI'lI'", "mco.error.invalid.session.message": "minecraft Dataghqa' 'e' yInID", "mco.error.invalid.session.title": "<PERSON><PERSON><PERSON> wa<PERSON>", "mco.errorMessage.6001": "notlh De'wI'", "mco.errorMessage.6002": "lo'lu'meH mab <PERSON>q<PERSON>'pu'be'", "mco.errorMessage.6003": "VuS Sich download", "mco.errorMessage.6004": "labmeH 'aqroS SIchpu'", "mco.errorMessage.6005": "World locked", "mco.errorMessage.6006": "World is out of date", "mco.errorMessage.6007": "User in too many Realms", "mco.errorMessage.6008": "Invalid Realm name", "mco.errorMessage.6009": "Invalid Realm description", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON>, SIbI'Ha' penIDqa'.", "mco.errorMessage.generic": "An error occurred: ", "mco.errorMessage.initialize.failed": "realm tagh 'e' luj", "mco.errorMessage.noDetails": "No error details provided", "mco.errorMessage.realmsService": "An error occurred (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Could not connect to Realms: %s", "mco.errorMessage.realmsService.realmsError": "realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Could not check compatible version, got response: %s", "mco.errorMessage.retry": "Retry operation", "mco.errorMessage.serviceBusy": "DaH rarlaHbe' qo'mey. tugh qo'lIjDaq yIlInqa' 'e' yInID.", "mco.gui.button": "leQ", "mco.gui.ok": "luq", "mco.info": "De'!", "mco.invited.player.narration": "QujwI' %s rIt", "mco.invites.button.accept": "laj", "mco.invites.button.reject": "lajQo'", "mco.invites.nopending": "pagh ruSvep!", "mco.invites.pending": "ruSvep chu'!", "mco.invites.title": "ruSvep", "mco.minigame.world.changeButton": "QujHom yIwIv; latlh yIwIv", "mco.minigame.world.info.line1": "qo'lIj qa' QujHom; ru'", "mco.minigame.world.info.line2": "SIbI'Ha' qo'lIj wa'D<PERSON><PERSON>gh; pagh <PERSON>l", "mco.minigame.world.noSelection": "yIwIv", "mco.minigame.world.restore": "QujHom ghanglI'...", "mco.minigame.world.restore.question.line1": "QujHom ghanghlu' 'ej <PERSON>lIj chu'qa'lu'", "mco.minigame.world.restore.question.line2": "bItaH <PERSON>'a'", "mco.minigame.world.selected": "<PERSON>u<PERSON><PERSON><PERSON>'e' wIvlu'bogh:", "mco.minigame.world.slot.screen.title": "qo' choHlI'...", "mco.minigame.world.startButton": "leQ", "mco.minigame.world.starting.screen.title": "QujHom taghlu'lI'...", "mco.minigame.world.stopButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> ghang", "mco.minigame.world.switch.new": "QujHom wIv'a'; latlh wIv'a'", "mco.minigame.world.switch.title": "QujHom qa'", "mco.minigame.world.title": "realm qa' QujHom", "mco.news": "realms De' chu'", "mco.notification.dismiss": "rItHa'", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON>", "mco.notification.transferSubscription.message": "Java Realms subscriptions are moving to the Microsoft Store. Do not let your subscription expire!\nTransfer now and get 30 days of Realms for free.\nGo to Profile on minecraft.net to transfer your subscription.", "mco.notification.visitUrl.buttonText.default": "nelmeS wIv", "mco.notification.visitUrl.message.default": "bIng nelmeS yI'uy", "mco.onlinePlayers": "lIntaHbogh QujwI'", "mco.play.button.realm.closed": "SoQ realm", "mco.question": "ghel", "mco.reset.world.adventure": "HoqmeH", "mco.reset.world.experience": "wanI'mey", "mco.reset.world.generate": "qo' chu'", "mco.reset.world.inspiration": "pIlmoHmeH", "mco.reset.world.resetting.screen.title": "qo' lIngqa'lI'...", "mco.reset.world.seed": "qo' mI' ('utbe')", "mco.reset.world.template": "tlhInbogh qo'mey chu'", "mco.reset.world.title": "qo' lIngqa'", "mco.reset.world.upload": "qo' lab", "mco.reset.world.warning": "<PERSON>l<PERSON>j qo' qa' <PERSON>m", "mco.selectServer.buy": "realm je'!", "mco.selectServer.close": "SoQmoH", "mco.selectServer.closed": "realm SoQ", "mco.selectServer.closeserver": "qo' SoQ", "mco.selectServer.configure": "realm choH", "mco.selectServer.configureRealm": "realm choH", "mco.selectServer.create": "Realm chenmoH", "mco.selectServer.create.subtitle": "realml<PERSON><PERSON> ch<PERSON><PERSON><PERSON><PERSON> q<PERSON><PERSON> <PERSON><PERSON> yIwIv", "mco.selectServer.expired": "rIn realmvam", "mco.selectServer.expiredList": "qatlh expired jabwI' SoH", "mco.selectServer.expiredRenew": "latlh poH chel", "mco.selectServer.expiredSubscribe": "qI'", "mco.selectServer.expiredTrial": "waHmeH poHlIj natlhlu'", "mco.selectServer.expires.day": "qaStaHvIS jaj expires", "mco.selectServer.expires.days": "expires qaStaHvIS %s jaj", "mco.selectServer.expires.soon": "tugh expires", "mco.selectServer.leave": "<PERSON> mej", "mco.selectServer.loading": "Loading Realms List", "mco.selectServer.mapOnlySupportedForVersion": "wej <PERSON><PERSON> wItlha' %s", "mco.selectServer.minigame": "<PERSON><PERSON><PERSON>:", "mco.selectServer.minigameName": "QujHom: %s", "mco.selectServer.minigameNotSupportedInVersion": "%s <PERSON><PERSON>'ch<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'", "mco.selectServer.noRealms": "You don't seem to have a Realm. Add a Realm to play together with your friends.", "mco.selectServer.note": "ghItlh:", "mco.selectServer.open": "realm poSmoH", "mco.selectServer.openserver": "realm poSmoH", "mco.selectServer.play": "<PERSON><PERSON><PERSON>", "mco.selectServer.popup": "qo' QaD, nap mIw jabwI' minecraft qo' Sengvo' wa'maH jup tIv legh poH. Quj mo'mey labbe'chugh tIgh je ngaq 'oH! yIDIl nIS neH 'avwI' Dawuvmo'.", "mco.selectServer.purchase": "realm chel", "mco.selectServer.trial": "waHmeH poH yISuq!", "mco.selectServer.uninitialized": "Hot qo' chenmoH!", "mco.snapshot.createSnapshotPopup.text": "You are about to create a free Snapshot Realm that will be paired with your paid Realms subscription. This new Snapshot Realm will be accessible for as long as the paid subscription is active. Your paid Realm will not be affected.", "mco.snapshot.createSnapshotPopup.title": "Create Snapshot Realm?", "mco.snapshot.creating": "Creating Snapshot Realm...", "mco.snapshot.description": "Paired with \"%s\"", "mco.snapshot.friendsRealm.downgrade": "You need to be on version %s to join this Realm", "mco.snapshot.friendsRealm.upgrade": "%s needs to upgrade their Realm before you can play from this version", "mco.snapshot.paired": "This Snapshot Realm is paired with \"%s\"", "mco.snapshot.parent.tooltip": "Use the latest release of Minecraft to play on this Realm", "mco.snapshot.start": "Start free Snapshot Realm", "mco.snapshot.subscription.info": "This is a Snapshot Realm that is paired to the subscription of your Realm '%s'. It will stay active for as long as its paired Realm is.", "mco.snapshot.tooltip": "minecraft Segh veb DawaHmeH, «waHmeH Segh realms» yIlo'. chaq DI'on chu', latlh choH joq tu'lu'.\n\nQuj Segh naQ Dalo'DI', realmslIj motlh Datu'laH.", "mco.snapshotRealmsPopup.message": "Realms are now available in Snapshots starting with Snapshot 23w41a. Every Realms subscription comes with a free Snapshot Realm that is separate from your normal Java Realm!", "mco.snapshotRealmsPopup.title": "DaH waHmeH Segh lo'taHvIS realms QujlaH", "mco.snapshotRealmsPopup.urlText": "la<PERSON>h ghoj", "mco.template.button.publisher": "Se'vIr malja'", "mco.template.button.select": "wIv", "mco.template.button.trailer": "'aghmeH muchHom", "mco.template.default.name": "wIv Template (Optional)", "mco.template.info.tooltip": "Se'vIr malja' 'Internet Daq", "mco.template.name": "qal'aq", "mco.template.select.failure": "buvvam De' tetlh wItu'laHbe'.\nlInchu' 'Internet De'wI'lIj je 'e' yI'ol, qoj SIbI'Ha' yInIDqa'.", "mco.template.select.narrate.authors": "ghItlhwI'pu': %s", "mco.template.select.narrate.version": "Segh %s", "mco.template.select.none": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> chI<PERSON>' buvvam.\nSIbI'Ha' <PERSON>' chu' <PERSON><PERSON>'me<PERSON>, yIchegh. 'ach chenmoHwI' So<PERSON><PERSON>gh, %s.", "mco.template.select.none.linkTitle": "vay' chenge<PERSON> 'e' y<PERSON><PERSON>el", "mco.template.title": "tlhInbogh qo'mey chu'", "mco.template.title.minigame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.template.trailer.tooltip": "pu'jIn 'aghmeH muchHom", "mco.terms.buttons.agree": "Qochbe'", "mco.terms.buttons.disagree": "<PERSON><PERSON>", "mco.terms.sentence.1": "minecraft realms vIQochbe'", "mco.terms.sentence.2": "lo'lu'meH mab", "mco.terms.title": "realms lo'lu'meH mab", "mco.time.daysAgo": "%1$s Hu'", "mco.time.hoursAgo": "%1$s rep ret", "mco.time.minutesAgo": "%1$s tup ret", "mco.time.now": "DaH", "mco.time.secondsAgo": "%1$s lup ret", "mco.trial.message.line1": "realm qo' tlhIn <PERSON><PERSON>aj <PERSON>a'?", "mco.trial.message.line2": "naDev <PERSON> latlh tu'lu'!", "mco.upload.button.name": "lab", "mco.upload.cancelled": "lab 'e' qIl", "mco.upload.close.failure": "realmlIj SoQmoHlaHbe', SIbI'Ha' yInIDqa'", "mco.upload.done": "lab 'e' rInmoHpu'", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "lab 'e' luj! (%s)", "mco.upload.failed.too_big.description": "tlhoy tIn qo' wIvlu'bogh. %s 'oH 'aqroS'e'.", "mco.upload.failed.too_big.title": "tlhoy tIn qo'", "mco.upload.hardcore": "qo' Qatlhqu' DalablaHbe'!", "mco.upload.percent": "%s vatlhvI'", "mco.upload.preparing": "qo'lIj ghuHlI'", "mco.upload.select.world.none": "pagh wa' QujwI' qo' tu'lu'!", "mco.upload.select.world.subtitle": "DalabmeH wa' QujwI' qo' yIwIv", "mco.upload.select.world.title": "qo' lab", "mco.upload.size.failure.line1": "tlhoy tIn %s!", "mco.upload.size.failure.line2": "%s 'oH. %s 'oH 'aqroS'e'.", "mco.upload.uploading": "%s lablI'", "mco.upload.verifying": "qo'lIj 'ollI'", "mco.version": "Segh: %s", "mco.warning": "ghuH!", "mco.worldSlot.minigame": "QujHom", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "rarHa'", "menu.feedback": "vuD...", "menu.feedback.title": "vuD", "menu.game": "<PERSON><PERSON><PERSON>", "menu.modded": " (cho<PERSON>lu'pu')", "menu.multiplayer": "QujwI' law'", "menu.online": "minecraft realms", "menu.options": "DuHmey...", "menu.paused": "yev <PERSON><PERSON>j", "menu.playdemo": "'aghmeH qo' Quj", "menu.playerReporting": "QujwI'vaD ja'", "menu.preparingSpawn": "Spawn mIchHom qeq: vatlhvI' %s %%", "menu.quick_actions": "<PERSON>u'mey ngaj...", "menu.quick_actions.title": "<PERSON><PERSON>'me<PERSON> ngaj", "menu.quit": "ghang", "menu.reportBugs": "QaghvaD ja'", "menu.resetdemo": "'aghmeH qo' chu'qa'", "menu.returnToGame": "<PERSON><PERSON><PERSON> chegh", "menu.returnToMenu": "qon 'ej per <PERSON><PERSON><PERSON> chegh", "menu.savingChunks": "puH 'ay' choqlI'", "menu.savingLevel": "qo' ngeqlI'", "menu.sendFeedback": "QInHom ghItlh", "menu.server_links": "turw<PERSON>' nelme<PERSON>mey...", "menu.server_links.title": "turw<PERSON>' ne<PERSON><PERSON><PERSON><PERSON>", "menu.shareToLan": "SIr'o' Sum lo'", "menu.singleplayer": "wa' QujwI'", "menu.working": "vumtaH...", "merchant.deprecated": "qaSD<PERSON>' wa' jaj cha'logh <PERSON><PERSON><PERSON><PERSON> qa'laH vengHom ngan", "merchant.level.1": "vumwI' Henbe'", "merchant.level.2": "ghojwI'", "merchant.level.3": "vumw<PERSON>' Hen", "merchant.level.4": "pIn", "merchant.level.5": "pIn'a'", "merchant.title": "%s - %s", "merchant.trades": "<PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "lItHa'meH %1$s yIchu'", "multiplayer.applyingPack": "mIllogh vey lo'choHmoHlI'", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "Qapbe' 'olmeH turwI'mey. SIbI'Ha' rar yInID!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "De'wI'vam Hop rar 'e' Datuchlu'", "multiplayer.disconnect.banned.expiration": "qaSDI' %s <PERSON><PERSON><PERSON><PERSON>'be'choH", "multiplayer.disconnect.banned.reason": "De'wI'vam Hop rar 'e' Datuchlu'. %s 'oH meq.", "multiplayer.disconnect.banned_ip.expiration": "\nYour ban will be removed on %s", "multiplayer.disconnect.banned_ip.reason": "De'wI' ngu'meH mI'lIj tuch turwI'vam. meq:", "multiplayer.disconnect.chat_validation_failed": "Chat message validation failure", "multiplayer.disconnect.duplicate_login": "<PERSON><PERSON> la<PERSON>' DangaQHa'moHpu'", "multiplayer.disconnect.expired_public_key": "Expired profile public key. Check that your system time is synchronized, and try restarting your game.", "multiplayer.disconnect.flying": "puvghach chawbe' De'wI'", "multiplayer.disconnect.generic": "lInHa'pu'", "multiplayer.disconnect.idling": "Pagh vIbuSlaw' 'e' vI'angnIS je qaStaHvIS!", "multiplayer.disconnect.illegal_characters": "Hat vuDmey'e' qaStaHvIS jaw", "multiplayer.disconnect.incompatible": "meylaHbe' naw'bogh De'wI'! %s yIlo'", "multiplayer.disconnect.invalid_entity_attacked": "Dol waS HIv 'e' nID", "multiplayer.disconnect.invalid_packet": "De' veyHom waS ngeH turwI'", "multiplayer.disconnect.invalid_player_data": "QujwI' De' waS", "multiplayer.disconnect.invalid_player_movement": "QujwI' vIHmoHmeH De' veyHom waS Hevpu'", "multiplayer.disconnect.invalid_public_key_signature": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "Duj vIHmoHmeH De' veyHom waS Hevpu'", "multiplayer.disconnect.ip_banned": "De'wI' ngu'meH mI'lIj tuch turwI'vam", "multiplayer.disconnect.kicked": "lIvujpu' turwI' vu'wI'", "multiplayer.disconnect.missing_tags": "nIqHom 'ay' vey naQbe' ngeHpu' turwI'. turwI' vu'wI' yIrI'.", "multiplayer.disconnect.name_taken": "pong<PERSON><PERSON>h tlhaplu'", "multiplayer.disconnect.not_whitelisted": "<PERSON>ughbe turwI'vam naD tetlh!", "multiplayer.disconnect.out_of_order_chat": "Out-of-order chat packet received. Did your system time change?", "multiplayer.disconnect.outdated_client": "Incompatible client! Please use %s", "multiplayer.disconnect.outdated_server": "Incompatible client! Please use %s", "multiplayer.disconnect.server_full": "buy' turwI'!", "multiplayer.disconnect.server_shutdown": "SoQ turwI'", "multiplayer.disconnect.slow_login": "tlhoy nI' muvmeH mIw", "multiplayer.disconnect.too_many_pending_chats": "Too many unacknowledged chat messages", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "De'wI'vamvo' De' jum", "multiplayer.disconnect.unsigned_chat": "Received chat packet with missing or invalid signature.", "multiplayer.disconnect.unverified_username": "Userna<PERSON> 'ol: luj!", "multiplayer.downloadingStats": "De' HevtaH...", "multiplayer.downloadingTerrain": "puH lI'lI'...", "multiplayer.lan.server_found": "New server found: %s", "multiplayer.message_not_delivered": "QInvetlh ngeHlaHbe', De'wI' QonoS yInuD: %s", "multiplayer.player.joined": "Quj muv %s", "multiplayer.player.joined.renamed": "Quj muv %s (pong Deq 'oH %s'e')", "multiplayer.player.left": "Qujvo' mej %s", "multiplayer.player.list.hp": "%s yIn 'ay'", "multiplayer.player.list.narration": "Online players: %s", "multiplayer.requiredTexturePrompt.disconnect": "mIllogh vey tlhIn poQ turwI'vam", "multiplayer.requiredTexturePrompt.line1": "mIllogh vey tlhIn poQ turwI'vam", "multiplayer.requiredTexturePrompt.line2": "m<PERSON><PERSON>gh veyvam tlhIn <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>'moH turwI'vam.", "multiplayer.socialInteractions.not_available": "neq<PERSON><PERSON> peS QujwI' law' qo' neH ", "multiplayer.status.and_more": "... 'ej %s neH ...", "multiplayer.status.cancelled": "<PERSON>l", "multiplayer.status.cannot_connect": "lInlaHbe' turwI' De'wI'vam je", "multiplayer.status.cannot_resolve": "turwI' pong lo'laHbe'", "multiplayer.status.finished": "rIn", "multiplayer.status.incompatible": "Ghu'vam lughajlu'!", "multiplayer.status.motd.narration": "Message of the day: %s", "multiplayer.status.no_connection": "(lInlaHbe')", "multiplayer.status.old": "<PERSON><PERSON><PERSON>", "multiplayer.status.online": "lIn", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milliseconds", "multiplayer.status.pinging": "rI'taH...", "multiplayer.status.player_count": "%2$s loch %1$s", "multiplayer.status.player_count.narration": "%s out of %s players online", "multiplayer.status.quitting": "buplI'", "multiplayer.status.request_handled": "Dotlh tlhob 'e' DIghpu'", "multiplayer.status.unknown": "Sovbe'lu'", "multiplayer.status.unrequested": "<PERSON><PERSON>h tlhoblu'be' Hev<PERSON>'", "multiplayer.status.version.narration": "Server version: %s", "multiplayer.stopSleeping": "QongDaq mej", "multiplayer.texturePrompt.failure.line1": "turwI' mIllogh vey lo'laHbe'", "multiplayer.texturePrompt.failure.line2": "chaq Qapchu'be' Sup tlhIn poQbogh laH'e'", "multiplayer.texturePrompt.line1": "vam qo' 'oH tlhob SoH Daq ghaj DIch Hal cher.", "multiplayer.texturePrompt.line2": "Suq 'ej jom <PERSON>'wI'lIj DaneH'a'?", "multiplayer.texturePrompt.serverPrompt": "%s\n\nturwI'vo' QIn:\n%s", "multiplayer.title": "QujwI' law'", "multiplayer.unsecureserver.toast": "turwI'vam QIn choHlaH vay'", "multiplayer.unsecureserver.toast.title": "QIn 'ollaHbe'", "multiplayerWarning.check": "QInvam yI'angqa'Qo'", "multiplayerWarning.header": "ghuH: <PERSON>uj lIn ghom wejDIch", "multiplayerWarning.message": "yIHoj: lInbogh Qujmey peS turwI'mey tlhab. turwI'meyvam ghajbe' 'ej vu'be' 'ej jIHbe' mojang studios microsoft ghap. qaStaHvIS lInbogh Qujmey, mu'mey mIlloghmey joq jIHlu'pu'be'bogh <PERSON> 'e' qIt. leghmeH 'op QujwI'pu' mItHa' mu'meyvam mIlloghmeyvam joq 'e' qIt.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> On <PERSON>", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "leQ: %s", "narration.button.usage.focused": "Dachu'meH ‹enter› leQ yI'uy", "narration.button.usage.hovered": "poS leQ yIlo'meH yIchu'", "narration.checkbox": "'olwI': %s", "narration.checkbox.usage.focused": "<PERSON><PERSON>'me<PERSON> 'ej <PERSON>'<PERSON>'meH ‹enter› leQ yI'uy", "narration.checkbox.usage.hovered": "tammeH «Left click» chu'", "narration.component_list.usage": "'ay' veb DajaHmeH tab leQ yI'uy", "narration.cycle_button.usage.focused": "%s Da<PERSON>'meH ‹enter› leQ yI'uy", "narration.cycle_button.usage.hovered": "%s Dachu'meH 'eQway' poS leQ yI'uy", "narration.edit_box": "ngaSwI' choH: %s", "narration.item": "Doch: %s", "narration.recipe": "%s chenmoHmeH mIw'e'", "narration.recipe.usage": "poS leQ yIlo'meH yIwIv", "narration.recipe.usage.more": "nIH leQ yIlo'meH yIlegh law' HIlaDmey", "narration.selection.usage": "latlh 'ay' DajaHmeH up leQ down leQ joq yI'uy", "narration.slider.usage.focused": "mI' DachoHmeH left leQ right leQ joq yI'uy", "narration.slider.usage.hovered": "mI' DachoHmeH tlhamwI' yIlIS", "narration.suggestion": "ghu' wIvlu' %s %svo': %s", "narration.suggestion.tooltip": "ghu' wIvlu' %s %svo': %s (%s)", "narration.suggestion.usage.cycle.fixed": "Press Tab to cycle to the next suggestion", "narration.suggestion.usage.cycle.hidable": "Press Tab to cycle to the next suggestion, or Escape to leave suggestions", "narration.suggestion.usage.fill.fixed": "Press Tab to use suggestion", "narration.suggestion.usage.fill.hidable": "Press Tab to use suggestion, or Escape to leave suggestions", "narration.tab_navigation.usage": "Press Ctrl and Tab to switch between tabs", "narrator.button.accessibility": "naw'la<PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "qa<PERSON> <PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock.locked": "ngaQpu'", "narrator.button.difficulty_lock.unlocked": "ngaQHa'pu'", "narrator.button.language": "<PERSON>l", "narrator.controls.bound": "%s baghpu' %s", "narrator.controls.reset": "%s leQ chu'be<PERSON><PERSON>'moH", "narrator.controls.unbound": "baghbe' %s", "narrator.joining": "muvtaH", "narrator.loading": "laDtaH: %s", "narrator.loading.done": "pItlh", "narrator.position.list": "pe'vIl SaQmoHlaHchu'", "narrator.position.object_list": "wev Doch %sDIch wIv; %s tu'lu'", "narrator.position.screen": "HaSta Doch %sDIch 'oH; %s tu'lu'", "narrator.position.tab": "Selected tab %s out of %s", "narrator.ready_to_play": "Ready to play", "narrator.screen.title": "per <PERSON><PERSON>", "narrator.screen.usage": "'eQway' yI<PERSON>' qoj \"tab\" leQ yIlo'meH 'ay' wIv", "narrator.select": "wIvpu': %s", "narrator.select.world": "%s wIvlu'pu'. %s Quj. %s. %s. Segh mI': %s", "narrator.select.world_info": "Selected %s, last played: %s, %s", "narrator.toast.disabled": "DelwI' chu'Ha'lu'", "narrator.toast.enabled": "DelwI' chu'lu'", "optimizeWorld.confirm.description": "qo'lIj <PERSON>, <PERSON><PERSON> De' ngeq Quj nab ret 'e' 'ol mIwvam. 'op qo'meH, qaStaHvIS poH nI' taHlaH mIw. rInDI' mIw, nomqu' <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 'ach Quj <PERSON>gh ngo' DaQujlaHbe'. Daruch <PERSON>bej'a'?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "qo' nIvmoH", "optimizeWorld.info.converted": "puH 'ay' <PERSON><PERSON>'pu'bogh: %s", "optimizeWorld.info.skipped": "puH 'ay' <PERSON><PERSON>'be'pu'bogh: %s", "optimizeWorld.info.total": "Hoch puH 'ay': %s", "optimizeWorld.progress.counter": "%2$s loch %1$s", "optimizeWorld.progress.percentage": "%s vatlhvI'", "optimizeWorld.stage.counting": "puH 'aymey toghlI'...", "optimizeWorld.stage.failed": "lujpu'! }}:(", "optimizeWorld.stage.finished": "rInmoHlI'...", "optimizeWorld.stage.finished.chunks": "puH 'ay' Dub 'e' rInmoH...", "optimizeWorld.stage.finished.entities": "Dol Dub 'e' rInmoH...", "optimizeWorld.stage.finished.poi": "<PERSON><PERSON>' rInmoH...", "optimizeWorld.stage.upgrading": "Hoch puH 'ay' DublI'...", "optimizeWorld.stage.upgrading.chunks": "Hoch puH 'ay' DublI'...", "optimizeWorld.stage.upgrading.entities": "Hoch Dol DublI'...", "optimizeWorld.stage.upgrading.poi": "<PERSON><PERSON> Daq Daj <PERSON>'...", "optimizeWorld.title": "%s qo' nIvmoHlI'", "options.accessibility": "naw'meH <PERSON>y", "options.accessibility.high_contrast": "rItlh pImqu'", "options.accessibility.high_contrast.error.tooltip": "‹jaS nguv› jo vey naw'laHbe'", "options.accessibility.high_contrast.tooltip": "Enhances the contrast of UI elements.", "options.accessibility.high_contrast_block_outline": "ja<PERSON> w<PERSON><PERSON><PERSON> bu<PERSON><PERSON><PERSON><PERSON>", "options.accessibility.high_contrast_block_outline.tooltip": "ja<PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>h HeH wovmoH", "options.accessibility.link": "naw'<PERSON><PERSON><PERSON>ach DevmeH HaSta", "options.accessibility.menu_background_blurriness": "t<PERSON><PERSON><PERSON> 'em", "options.accessibility.menu_background_blurriness.tooltip": "tlhoD HID<PERSON> 'emmey 'e' choH", "options.accessibility.narrator_hotkey": "DelwI' leQ", "options.accessibility.narrator_hotkey.mac.tooltip": "«Cmd+B» 'uyDI' DelwI' chu' pagh chu'Ha' 'e' chaw'", "options.accessibility.narrator_hotkey.tooltip": "«Ctrl+B» 'uyDI' DelwI' chu' pagh chu'Ha' 'e' chaw'", "options.accessibility.panorama_speed": "<PERSON><PERSON><PERSON><PERSON> 'em Do", "options.accessibility.text_background": "mu'mey 'em", "options.accessibility.text_background.chat": "QIn pat", "options.accessibility.text_background.everywhere": "<PERSON>t", "options.accessibility.text_background_opacity": "mu'mey 'em Dem patlh", "options.accessibility.title": "naw'meH <PERSON>y", "options.allowServerListing": "ponglIj 'ang turwI' 'e' chaw'", "options.allowServerListing.tooltip": "lIntaHbogh QujwI' pongmey 'aghlaH turwI'mey. <PERSON><PERSON><PERSON><PERSON>, ponglIj yughbe' te<PERSON><PERSON><PERSON><PERSON><PERSON>h.", "options.ao": "wovmoHmeH pat", "options.ao.max": "'aqroS", "options.ao.min": "rav", "options.ao.off": "chu'Ha'", "options.attack.crosshair": "ch<PERSON><PERSON>", "options.attack.hotbar": "qogh", "options.attackIndicator": "HIv cha'", "options.audioDevice": "wab jan", "options.audioDevice.default": "motlh pat", "options.autoJump": "DuSupmoH De'wI'", "options.autoSuggestCommands": "ra'meH chup", "options.autosaveIndicator": "ngeqmeH Deryat ghaSwI'", "options.biomeBlendRadius": "DuD '<PERSON>bermey", "options.biomeBlendRadius.1": "chu'Ha' (nomqu')", "options.biomeBlendRadius.11": "wa'maH wa'logh boq'egh wa'maH wa'logh (Dojqu')", "options.biomeBlendRadius.13": "wa'maH wej<PERSON>h boq'egh wa'maH wej ('amuS chIw)", "options.biomeBlendRadius.15": "wa'maH vaghlogh boq'egh wa'maH vagh ('aqroS)", "options.biomeBlendRadius.3": "wej<PERSON>h bo<PERSON>'egh wej (nom)", "options.biomeBlendRadius.5": "vagh<PERSON>h boq'egh vagh (motlh)", "options.biomeBlendRadius.7": "<PERSON><PERSON><PERSON><PERSON> boq'egh <PERSON> (vItlh)", "options.biomeBlendRadius.9": "<PERSON><PERSON><PERSON><PERSON> bo<PERSON><PERSON>egh <PERSON> (vItlhqu')", "options.chat": "QIn pat <PERSON>...", "options.chat.color": "mu'mey nguv", "options.chat.delay": "%s lupvaD QIn pat mIm", "options.chat.delay_none": "QIn pat mImbe'", "options.chat.height.focused": "Focused Height", "options.chat.height.unfocused": "Unfocused 'ab", "options.chat.line_spacing": "mu't<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "options.chat.links": "[Web] [Links]", "options.chat.links.prompt": "[Prompt] <PERSON><PERSON> [Links]", "options.chat.opacity": "QIn pat mu'mey <PERSON>ch", "options.chat.scale": "QIn mu'mey machH<PERSON>'ghach", "options.chat.title": "QIn pat <PERSON>...", "options.chat.visibility": "QIn pat", "options.chat.visibility.full": "'anglu'", "options.chat.visibility.hidden": "So'lu'", "options.chat.visibility.system": "ra'meH mu'tlhegh<PERSON>y neH", "options.chat.width": "juch", "options.chunks": "%s puH 'ay'", "options.clouds.fancy": "Hoy", "options.clouds.fast": "nom", "options.controls": "SeHwI'mey...", "options.credits_and_attribution": "Quj chenmoHwI' ghaqwI' je...", "options.damageTiltStrength": "QIHlu'DI' tom", "options.damageTiltStrength.tooltip": "The amount of camera shake caused by being hurt.", "options.darkMojangStudiosBackgroundColor": "Degh chumbe'", "options.darkMojangStudiosBackgroundColor.tooltip": "lI'meH HaSta 'em qIjmoH", "options.darknessEffectScale": "<PERSON><PERSON><PERSON>ch", "options.darknessEffectScale.tooltip": "Controls how much the Darkness effect pulses when a Warden or Sculk Shrieker gives it to you.", "options.difficulty": "qaD patlh", "options.difficulty.easy": "ngeD", "options.difficulty.easy.info": "bogh yagh naH 'ach vItlhHa' QIH laHchaj. Soj mIr natlhlu'taH. mIrvetlh natlhlu'chu'DI', loj 'on'aS mIr bID.", "options.difficulty.hard": "Qatlh", "options.difficulty.hard.info": "bogh yagh naH 'ej vItlh QIH laHchaj. Soj mIr natlhlu'taH. mIrvetlh natlhlu'chu'DI', lojchu' 'on'aS mIr.", "options.difficulty.hardcore": "Qatlhq<PERSON>'", "options.difficulty.normal": "motlh", "options.difficulty.normal.info": "bogh yagh naH 'ej motlh QIH laHchaj. Soj mIr natlhlu'taH. mIrvetlh natlhlu'chu'DI', loj 'on'aS mIr 'ach tIq bID noplu'.", "options.difficulty.online": "turwI' qaD patlh", "options.difficulty.peaceful": "roj", "options.difficulty.peaceful.info": "bogh pagh yagh naH 'ej bogh 'op Hub'eghbogh yagh. Soj mIr natlhbe'lu', 'ej rach'egh 'on'aS.", "options.directionalAudio": "lurgh ghaS wab", "options.directionalAudio.off.tooltip": "wab motlh", "options.directionalAudio.on.tooltip": "Dechbogh wab rurqu'choHmeH, hrtf lurgh ghaSbogh wab lo' DuHvam'e'. hrtf lo'laHbogh jan poQ, 'ej <PERSON>n<PERSON>' lo'lu'DI' QaQqu' wanI'.", "options.discrete_mouse_scroll": "rutlhHom mIw chev", "options.entityDistanceScaling": "Dol chuq", "options.entityShadows": "<PERSON><PERSON>", "options.font": "ngutlh tu'qom <PERSON>...", "options.font.title": "ngutlh tu'qom <PERSON>law", "options.forceUnicodeFont": "Unicode ngutlh lo'moH", "options.fov": "leghlaHbogh chuq", "options.fov.max": "<PERSON><PERSON>qu'<PERSON><PERSON>", "options.fov.min": "motlh", "options.fovEffectScale": "HaSta HeH wanI'mey", "options.fovEffectScale.tooltip": "HaSta HeH luchoHlaH Do' wanI'mey 'e' SeH.", "options.framerate": "%s/wa' lup", "options.framerateLimit": "'aqroS mIllogh Do", "options.framerateLimit.max": "vuSbe'lu'", "options.fullscreen": "jIH naQ", "options.fullscreen.current": "rap", "options.fullscreen.entry": "%s juch %s 'ab %s chaDvay' (%s San'on)", "options.fullscreen.resolution": "jIH", "options.fullscreen.unavailable": "DuHvam naw'laHbe'", "options.gamma": "tamghay pat<PERSON>h", "options.gamma.default": "motlh", "options.gamma.max": "wovqu'", "options.gamma.min": "Hurg<PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "tamghay Do", "options.glintSpeed.tooltip": "tlheH DochDaq nom ghon tamghay 'e' SeH.", "options.glintStrength": "tamghay HoS", "options.glintStrength.tooltip": "tlheH DochDaq Dem tamghay 'e' SeH.", "options.graphics": "m<PERSON><PERSON><PERSON>", "options.graphics.fabulous": "maj!", "options.graphics.fabulous.tooltip": "bu<PERSON><PERSON><PERSON><PERSON><PERSON> bIQ je 'em<PERSON>aq muD, 'eng<PERSON><PERSON>, pay'anmey je DIjmeH, HaSta nIqHom lo' %s mIllogh DuH. jan q<PERSON><PERSON>'bogh loS SaD HaStay' j<PERSON><PERSON>, <PERSON>uj laH vuSqu'la<PERSON>.", "options.graphics.fancy": "'IHqu'", "options.graphics.fancy.tooltip": "HochHom De'wI'vaD laH 'IHqu'ghach je ma' mIllogh Hoy. chaq buq'Ir Dem bIQ joq 'emDaq So' muD <PERSON>, 'eng<PERSON><PERSON>, pay'an je.", "options.graphics.fast": "nom", "options.graphics.fast.tooltip": "chal bIQ chal chuch je DaleghlaHbogh nup nombogh mIllogh DuH. <PERSON><PERSON>' bu<PERSON>'<PERSON><PERSON><PERSON>; pormey yugh.", "options.graphics.warning.accept": "taH, ngaq HutlhtaHvIS", "options.graphics.warning.cancel": "HIcheghmoH", "options.graphics.warning.message": "%s mIllogh DuHmeH mIllogh janlIj qotlu'be' 'e' jem.\n\nghuHvam DabuSHa'laH 'ej bIruchlaH, 'ach %s mIllogh Dalo'chugh janlIj qottaHbe'.", "options.graphics.warning.renderer": "mIllogh nIqHom jemlu': [%s]", "options.graphics.warning.title": "m<PERSON><PERSON><PERSON> jan le<PERSON>'be'", "options.graphics.warning.vendor": "ngevwI' jempu': [%s]", "options.graphics.warning.version": "opengl Segh jem: [%s]", "options.guiScale": "<PERSON><PERSON><PERSON>", "options.guiScale.auto": "SeHnISbe'", "options.hidden": "So'lu'", "options.hideLightningFlashes": "bar pe'bIl 'e' So'", "options.hideLightningFlashes.tooltip": "chal barmoH pe'bIl 'e' bot. pe'bIl leghlaHtaH vay'.", "options.hideMatchedNames": "nelchuqbogh pong So'", "options.hideMatchedNames.tooltip": "tu'qom motlhbe' ghajbogh QIn ngeHlaH turwI'mey.\nchu'lu'chugh, QujwI' So'lu'bogh nelchuqmoHlu'meH ngeHwI' pong lo'lu'.", "options.hideSplashTexts": "bobDar ghItlh So'", "options.hideSplashTexts.tooltip": "H<PERSON><PERSON><PERSON>'a'<PERSON><PERSON> bobDar ghItlh SuD So'", "options.inactivityFpsLimit": "mIllogh nupmoH:", "options.inactivityFpsLimit.afk": "<PERSON><PERSON>", "options.inactivityFpsLimit.afk.tooltip": "qaSpu'DI' wa' tup vangbe'chugh QujwI', qaStaHvIS wa' lup 'aqroS wejmaH mIllogh cha'lu'. qaSpu'DI' latlh Hut tup vangbe'chugh QujwI', qaStaHvIS wa' lup 'aqroS wa'maH mIllogh cha'lu'.", "options.inactivityFpsLimit.minimized": "cha'be'lu'DI'", "options.inactivityFpsLimit.minimized.tooltip": "Quj cha'be'lu'DI' neH mIllogh Do vuS.", "options.invertMouse": "'eQway' yoymoH", "options.japaneseGlyphVariants": "nIpon <PERSON>l ng<PERSON><PERSON>h <PERSON>", "options.japaneseGlyphVariants.tooltip": "ngutlh tu'qom motlh j<PERSON><PERSON><PERSON>, n<PERSON><PERSON><PERSON>, cho<PERSON><PERSON>l je ngutlhmey nIpon Hol Sarmey lo'", "options.key.hold": "yuvtaH", "options.key.toggle": "Toggle", "options.language": "Hol...", "options.language.title": "<PERSON>l", "options.languageAccuracyWarning": "(chaq qarchu'be' 'op mu'mey mughlu'pu'bogh)", "options.languageWarning": "rut qarchu'be' 'op mu'mey mughlu'pu'bogh", "options.mainHand": "ghop'a'", "options.mainHand.left": "poS", "options.mainHand.right": "nIH", "options.mipmapLevels": "may 'It pay pu'jIn patlh", "options.modelPart.cape": "ngup", "options.modelPart.hat": "mIv", "options.modelPart.jacket": "wep", "options.modelPart.left_pants_leg": "poS yopwaH 'uS", "options.modelPart.left_sleeve": "poS tlhay", "options.modelPart.right_pants_leg": "nIH yopwaH 'uS", "options.modelPart.right_sleeve": "nIH tlhay", "options.mouseWheelSensitivity": "rutlhHom jemmeH patlh", "options.mouse_settings": "'eQway' <PERSON><PERSON><PERSON><PERSON>...", "options.mouse_settings.title": "'eQway' <PERSON><PERSON><PERSON><PERSON>", "options.multiplayer.title": "law' QujwI' Du<PERSON><PERSON>...", "options.multiplier": "%slogh boq'egh", "options.music_frequency": "QoQ SubmaH", "options.music_frequency.constant": "reH much", "options.music_frequency.default": "motlh", "options.music_frequency.frequent": "pIj much", "options.music_frequency.tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> wanI' SubmaH choH.", "options.narrator": "DelwI'", "options.narrator.all": "Hoch Del", "options.narrator.chat": "QIn Del", "options.narrator.notavailable": "naw'laHbe'", "options.narrator.off": "chu'Ha'", "options.narrator.system": "<PERSON>", "options.notifications.display_time": "vep poH", "options.notifications.display_time.tooltip": "jIHDaq Hoch vepmey leghmeH poH SeH.", "options.off": "chu'Ha'", "options.off.composed": "%s: chu'<PERSON>'lu'", "options.on": "chu'", "options.on.composed": "%s: chu'pu'", "options.online": "lIn...", "options.online.title": "lIn DuHmey", "options.onlyShowSecureChat": "QInmey QaD neH 'ang", "options.onlyShowSecureChat.tooltip": "Only display messages from other players that can be verified to have been sent by that player, and have not been modified.", "options.operatorItemsTab": "turwI' vu'wI' <PERSON>h tenwal", "options.particles": "pay'an", "options.particles.all": "Hoch", "options.particles.decreased": "'op", "options.particles.minimal": "patlh 'e<PERSON>qu'", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "qo' chenmoHwI'", "options.prioritizeChunkUpdates.byPlayer": "rut bot", "options.prioritizeChunkUpdates.byPlayer.tooltip": "puH 'ay'Daq <PERSON>b<PERSON>' puH 'ay' lugh<PERSON><PERSON>' 'op choHmey. buq'Ir lan<PERSON>' 'ej Qaw'lu' 'e' yugh choHmeyvam", "options.prioritizeChunkUpdates.nearby": "reH bot", "options.prioritizeChunkUpdates.nearby.tooltip": "reH SIbI' puH 'ay' Sum g<PERSON>'. bu<PERSON>'<PERSON><PERSON>n<PERSON>'DI' qoj <PERSON>aw'lu'DI', <PERSON>uj laH vuSlaH DuHvam.", "options.prioritizeChunkUpdates.none": "nIqlu'pu'", "options.prioritizeChunkUpdates.none.tooltip": "tlheghmey DonDaq puH 'ay' Sum g<PERSON>'. bu<PERSON><PERSON><PERSON><PERSON>'lu'DI', m<PERSON><PERSON><PERSON> qung ngaj narghmoHlaH DuHvam.", "options.rawMouseInput": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "options.realmsNotifications": "realms De' chu' ruSvep je", "options.realmsNotifications.tooltip": "per HaStaDaq realms De' chu' ruSvep je Suq 'ej realms leQDaq Deghmeychaj cha'.", "options.reducedDebugInfo": "Qagh tI'meH De' vuS", "options.renderClouds": "'eng<PERSON>y", "options.renderCloudsDistance": "'eng chuq", "options.renderDistance": "mIllogh chuq", "options.resourcepack": "mIllogh vey...", "options.rotateWithMinecart": "jIr jIrDI' tlhIlHal Duj", "options.rotateWithMinecart.tooltip": "tlhe'DI' tlhIl<PERSON><PERSON>, tlhe' QujwI' pagh tlhe'be'. qo'vaD chu'lu'chugh neH ‹tlhIlHal Duj luDublu'› waHmeH DuH DuHvam naw'laH.", "options.screenEffectScale": "joqmeH wanI'", "options.screenEffectScale.tooltip": "vertaHghach SIQlu' qoj nether lojmIt 'ellu' joq <PERSON> 'e' SeH.\ntlhoS chu'Ha'lu'DI', joq <PERSON> 'e' qa' patlh SuDqu'.", "options.sensitivity": "SeHwI' Do", "options.sensitivity.max": "pIvlob wa'maH!!!", "options.sensitivity.min": "*Hob*", "options.showNowPlayingToast": "QoQ vep 'ang", "options.showNowPlayingToast.tooltip": "bom muchchoHDI', vep cha'. bom muchtaHvIS, yevmeH HIDjolevDaq vepvetlh cha'taH.", "options.showSubtitles": "DelmeH per 'ang", "options.simulationDistance": "SImmeH chuq", "options.skinCustomisation": "QujwI' tu'qom chenmoH...", "options.skinCustomisation.title": "QujwI' tu'qom chenmo<PERSON>", "options.sounds": "QoQ wab je...", "options.sounds.title": "<PERSON><PERSON><PERSON> wab je <PERSON>", "options.telemetry": "noch Hop De'...", "options.telemetry.button": "De' boS", "options.telemetry.button.tooltip": "<PERSON>' poQ<PERSON>'bogh neH yugh «%s».\n<PERSON>' <PERSON>o<PERSON><PERSON>'lu'bogh, <PERSON>' po<PERSON><PERSON>'bogh je yugh «%s».", "options.telemetry.disabled": "noch Hop De' chu'Ha'lu'.", "options.telemetry.state.all": "Hoch", "options.telemetry.state.minimal": "rav", "options.telemetry.state.none": "pagh", "options.title": "<PERSON><PERSON><PERSON><PERSON>", "options.touchscreen": "nItlh jIH", "options.video": "m<PERSON><PERSON>gh <PERSON>...", "options.videoTitle": "m<PERSON>llogh <PERSON>", "options.viewBobbing": "mIllogh tav", "options.visible": "cha'lu'", "options.vsync": "<PERSON><PERSON> chong", "outOfMemory.message": "buy'chu' minecraft qawHaq.\n\nchaq Qagh <PERSON>uj, pagh chaq java De'wI' lIlwI'vaD qawHaq yap noblu'be'.\n\nqo' qalmoH 'e' botmeH, Qujvam qIllu'. per Ha<PERSON><PERSON> Quj je <PERSON>la<PERSON>me<PERSON>, qawHaq yap wItlhabmoH 'e' wInID, 'ach chaq Qappu'be' mIwvam.\n\nQInvam <PERSON>ch<PERSON>, Quj yItaghqa'.", "outOfMemory.title": "buy' qawHaq!", "pack.available.title": "naw'laH", "pack.copyFailure": "vey <PERSON><PERSON> <PERSON><PERSON>' luj", "pack.dropConfirm": "minecraftDaq veymeyvam Dachel DaneH'a'?", "pack.dropInfo": "vey DachelmeH, DaqvamDaq ta tItlhammoH", "pack.dropRejected.message": "The following entries were not valid packs and were not copied:\n %s", "pack.dropRejected.title": "Non-pack entries", "pack.folderInfo": "(naDev vey ta yIlan)", "pack.incompatible": "lo'laHbe'", "pack.incompatible.confirm.new": "Minecraft Segh chu'vaD Supvam vey chenmoHlu'pu' 'ej <PERSON><PERSON>'be'.", "pack.incompatible.confirm.old": "Minecraft Segh chu'vaD Supvam vey chenmoHlu'pu' 'ej <PERSON><PERSON>'be'.", "pack.incompatible.confirm.title": "mIllogh veyvam DalI' DaneHbej'a'?", "pack.incompatible.new": "(minecraft Segh chu'vaD chenmoHlu')", "pack.incompatible.old": "(minecraft Segh ngo'vaD chenmoHlu')", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "vey ta'a' poSmoH", "pack.selected.title": "Wiv", "pack.source.builtin": "<PERSON>haj <PERSON><PERSON>j motlh", "pack.source.feature": "DI'on", "pack.source.local": "<PERSON><PERSON><PERSON>", "pack.source.server": "turwI'", "pack.source.world": "qo'", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "<PERSON><PERSON>", "parsing.bool.expected": "vIt mI' pIHlu'", "parsing.bool.invalid": "vIt mI' waS, ‹teH› ‹teHHa'› ghap pIH 'ach ‹%s› tu'", "parsing.double.expected": "javmaH loS San'on mI' pIH", "parsing.double.invalid": "double waS 'oH %s'e'", "parsing.expected": "‹%s› pIH", "parsing.float.expected": "vIHbogh vI' mI' pIH", "parsing.float.invalid": "vIHbogh vI' mI' waS 'oH ‹%s›'e'", "parsing.int.expected": "Dol mI' pIH", "parsing.int.invalid": "mI' waS 'oH %s'e'", "parsing.long.expected": "chorghmaH San'on mI' pIH", "parsing.long.invalid": "tIqbogh poH waS 'oH %s'e'", "parsing.quote.escape": "ghItlhHomvamDaq narghmeH mIw waS \"\\%s\" tu'", "parsing.quote.expected.end": "Unclosed quoted string", "parsing.quote.expected.start": "Expected quote to start a string", "particle.invalidOptions": "pay'an DuHmey yajlaHbe': %s", "particle.notFound": "pay'an Sovbe'lu'bogh: %s", "permissions.requires.entity": "naDev ra'meH ghItl<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "permissions.requires.player": "ra'meH ghItlh turmeH, QujwI' poQ", "potion.potency.1": "cha'", "potion.potency.2": "wej", "potion.potency.3": "loS", "potion.potency.4": "vagh", "potion.potency.5": "jav", "potion.whenDrank": "tlhutlhlu'DI':", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (poH: %s)", "predicate.unknown": "<PERSON><PERSON>h Sovbe'lu'bogh: %s", "quickplay.error.invalid_identifier": "Could not find world with the provided identifier", "quickplay.error.realm_connect": "Could not connect to Realm", "quickplay.error.realm_permission": "Lacking permission to connect to this Realm", "quickplay.error.title": "nom Quj 'e' Q<PERSON>H<PERSON>'", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "bIraSIw", "realms.configuration.region.central_india": "barat", "realms.configuration.region.central_us": "'ay''awa, 'amerI'qa' SepjIjQa'", "realms.configuration.region.east_asia": "<PERSON><PERSON> qang", "realms.configuration.region.east_us": "vIrjI<PERSON>', 'amerI'qa' SepjIjQa'", "realms.configuration.region.east_us_2": "nar<PERSON><PERSON>h qer<PERSON>'na", "realms.configuration.region.france_central": "vIraS", "realms.configuration.region.japan_east": "nIpon chan", "realms.configuration.region.japan_west": "nIpon tIng 'ev", "realms.configuration.region.korea_central": "Hanghuq tIng chan tIng", "realms.configuration.region.north_central_us": "'IlInoy", "realms.configuration.region.north_europe": "'eyre'", "realms.configuration.region.south_central_us": "teqSaS, 'amerI'qa' SepjIjQa'", "realms.configuration.region.southeast_asia": "SIngapor", "realms.configuration.region.sweden_central": "Suverya'", "realms.configuration.region.uae_north": "'<PERSON><PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.uk_south": "'Inglan tIng chan tIng", "realms.configuration.region.west_central_us": "yu'ta, 'amer<PERSON>'qa' SepjIjQa'", "realms.configuration.region.west_europe": "<PERSON><PERSON><PERSON><PERSON>", "realms.configuration.region.west_us": "q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 'amer<PERSON>'qa' SepjIjQa'", "realms.configuration.region.west_us_2": "wa'<PERSON><PERSON><PERSON><PERSON>, 'amer<PERSON>'qa' SepjIjQa'", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "tlhejchuqlaHbe' ngongmeH nIqHom realms je", "recipe.notFound": "mIw Sov<PERSON>'lu'bogh: %s", "recipe.toast.description": "mIw paqlIj yIlaD", "recipe.toast.title": "mIw chu' ngaQHa'moH!", "record.nowPlaying": "DaH bom: %s", "recover_world.bug_tracker": "<PERSON>agh ja'", "recover_world.button": "tlhapqa' 'e' nID", "recover_world.done.failed": "<PERSON>lh vorghvo' tlhapqa' 'e' luj", "recover_world.done.success": "Qap tlhapqa'meH mIw!", "recover_world.done.title": "tlhapqa' 'e' nIDchu'", "recover_world.issue.missing_file": "Dach ta", "recover_world.issue.none": "pagh <PERSON>", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "tlhapqa' 'e' nID", "recover_world.restoring": "qo' tlhapqa' 'e' nIDtaH...", "recover_world.state_entry": "%svo' Dotlh: ", "recover_world.state_entry.unknown": "Sovbe'lu'", "recover_world.title": "qo' lI' 'e' luj", "recover_world.warning": "Failed to load world summary", "resourcePack.broken_assets": "mIllogh vey 'ay' ghorlu'pu'bogh jempu'", "resourcePack.high_contrast.name": "jaS nguv", "resourcePack.load_fail": "mIllogh vey lI'qa' 'e' luj", "resourcePack.programmer_art.name": "ghunwI' m<PERSON><PERSON><PERSON>", "resourcePack.runtime_failure": "jo vey <PERSON>agh jemlu'", "resourcePack.server.name": "mIllogh vey tlhIn", "resourcePack.title": "mIllogh vey wIv", "resourcePack.vanilla.description": "minecraft tu'qom motlh", "resourcePack.vanilla.name": "motlh", "resourcepack.downloading": "mIllogh vey lI'lI'", "resourcepack.progress": "ta lI'lI' (%sSaD Hut'on)...", "resourcepack.requesting": "tlhoblI'...", "screenshot.failure": "laH wej jIHDaq bach toD: %s", "screenshot.success": "toD jIHDaq bach je %s", "selectServer.add": "turwI' chel", "selectServer.defaultName": "Minecraft turwI'", "selectServer.delete": "Qaw'", "selectServer.deleteButton": "Qaw'", "selectServer.deleteQuestion": "turwI'vam <PERSON>'a'?", "selectServer.deleteWarning": "reH ngab %s! (poH nI' 'oH!)", "selectServer.direct": "lInmeH pIQ", "selectServer.edit": "choH", "selectServer.hiddenAddress": "(So'lu')", "selectServer.refresh": "lI'qa'", "selectServer.select": "turwI' rar", "selectWorld.access_failure": "qo' naw' luj", "selectWorld.allowCommands": "ngor net chaw'", "selectWorld.allowCommands.info": "ra'meH ghItlhmey: /gamemode, /experience, latlh je", "selectWorld.allowCommands.new": "'<PERSON><PERSON><PERSON><PERSON><PERSON> chaw'", "selectWorld.backupEraseCache": "<PERSON>' choq<PERSON>'bogh <PERSON>'", "selectWorld.backupJoinConfirmButton": "chach chenmo<PERSON> 'ej lI'", "selectWorld.backupJoinSkipButton": "nuch jIHbe'! nom yIghoSqu'!", "selectWorld.backupQuestion.customized": "qo'mey tlhIn DIleHtaHbe'", "selectWorld.backupQuestion.downgrade": "qo' DubHa'lu' 'e' qotbe'", "selectWorld.backupQuestion.experimental": "ngongmeH DuHmey lo'bogh qo'mey'e' DIleHbe'", "selectWorld.backupQuestion.snapshot": "qo'vam <PERSON>' DaneHbej'a'?", "selectWorld.backupWarning.customized": "Do'Ha' qo'mey tlhIn leHtaHbe' De' chu'vam. qo'vam wIlI'laHtaH 'ej choHbe' puH, 'ach tlhIntaHbe' puH chu'. qay'mo' matlhIj!", "selectWorld.backupWarning.downgrade": "qaStaHvIS Quj poH ret, %s <PERSON><PERSON>'pu'; DaH %s <PERSON><PERSON>'taH. qo' DaDub<PERSON><PERSON><PERSON>chugh, chaq <PERSON>lmoH - QaptaH 'e' wIlay'laHbe. <PERSON><PERSON>, chach yIchenmoH!", "selectWorld.backupWarning.experimental": "ngongmeH DuHmey lo' qo'vam. reH QapHa'choHlaH DuHmeyvam. lI' Qap pagh 'e' wIlay'laHbe'. naDev lung'a' tu'lu'!", "selectWorld.backupWarning.snapshot": "qaSDI' qo'vam <PERSON>uj ret, nIqHom mI' %s lo'lu'. DaH nIqHom mI' %s Dalo'. qo' qal <PERSON>H qo' chach yIchenmoH!", "selectWorld.bonusItems": "DerlIq 'utbe'", "selectWorld.cheats": "ngor", "selectWorld.commands": "'<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.conversion": "choH net poQ", "selectWorld.conversion.tooltip": "qo'v<PERSON>ta<PERSON>vIS, QIHbe'lu'meH nIqHom ngo' Dalo'nIS, nIqHom mI' 1.6.4 rur", "selectWorld.create": "qo' chu' chenmoH", "selectWorld.customizeType": "qo' tlhInmoH", "selectWorld.dataPacks": "<PERSON><PERSON> <PERSON><PERSON>", "selectWorld.data_read": "qo' De' laDlI'...", "selectWorld.delete": "Qaw'", "selectWorld.deleteButton": "Qaw'", "selectWorld.deleteQuestion": "qo'vam <PERSON>' DaneH'a'?", "selectWorld.deleteWarning": "reH ngab %s! (poH nI' 'oH!)", "selectWorld.delete_failure": "Qo' delete luj", "selectWorld.edit": "choH", "selectWorld.edit.backup": "chach chen<PERSON>", "selectWorld.edit.backupCreated": "chach chenmo<PERSON>pu': %s", "selectWorld.edit.backupFailed": "luj<PERSON>' chach", "selectWorld.edit.backupFolder": "chach ta poSmoH", "selectWorld.edit.backupSize": "%s <PERSON><PERSON>'on 'uch", "selectWorld.edit.export_worldgen_settings": "qo' lIngwI' choHmey lI'", "selectWorld.edit.export_worldgen_settings.failure": "Qaghpu' lI'", "selectWorld.edit.export_worldgen_settings.success": "lI'pu'", "selectWorld.edit.openFolder": "qo' ta poSmoH", "selectWorld.edit.optimize": "qo' nIvmoH", "selectWorld.edit.resetIcon": "qo' mIlloghHom choH", "selectWorld.edit.save": "ngeq", "selectWorld.edit.title": "po' choH", "selectWorld.enterName": "qo' pong", "selectWorld.enterSeed": "qo' chenmoHwI'vaD mI'", "selectWorld.experimental": "waHmeH", "selectWorld.experimental.details": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "selectWorld.experimental.details.entry": "waHmeH DI'on poQlu': %s", "selectWorld.experimental.details.title": "Experimental Feature Requirements", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Experimental Features Warning", "selectWorld.experiments": "ngong", "selectWorld.experiments.info": "Experiments are potential new features. Be careful as things might break. Experiments can't be turned off after world creation.", "selectWorld.futureworld.error.text": "Vay' ghoS Qih poStaHvIS qo' load vo' pIq version 'e' nID. ghotvam'e' risky yo'SeH tagh je. jIQoS vIchel Qapbej.", "selectWorld.futureworld.error.title": "qaSpu' Qagh!", "selectWorld.gameMode": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure": "Hoq", "selectWorld.gameMode.adventure.info": "taHmeH lo' rur, 'ach buq'<PERSON>r <PERSON>'.", "selectWorld.gameMode.adventure.line1": "taHmeH lo' rur, 'ach buq'<PERSON><PERSON><PERSON>'", "selectWorld.gameMode.adventure.line2": "'e<PERSON>'", "selectWorld.gameMode.creative": "chenmoHmeH", "selectWorld.gameMode.creative.info": "bImu<PERSON><PERSON> 'ej bItlhargh. DavuSbe'lu'. bIpuvlaH, 'ej 'ej<PERSON><PERSON> jo <PERSON>'laH, 'ej nIQIHlaHbe' tlhapragh.", "selectWorld.gameMode.creative.line1": "flying tlhab joq ja'meH DataHvIS Sup 'ej", "selectWorld.gameMode.creative.line2": "nom ng<PERSON>h <PERSON>", "selectWorld.gameMode.hardcore": "Qatlhq<PERSON>'", "selectWorld.gameMode.hardcore.info": "taHmeH lo' rur lo'vam, 'ach reH Qatlh qaD patlh. vabDot bIHeghchugh bIboghqa'laHbe'.", "selectWorld.gameMode.hardcore.line1": "taHmeH lo' rur, 'ach qaD patlh Qatlhqu'", "selectWorld.gameMode.hardcore.line2": "Dalo'nIS 'ej bIHeghchugh rIn Quj", "selectWorld.gameMode.spectator": "bejwI'", "selectWorld.gameMode.spectator.info": "DaleghlaH 'ach yIHotQo'.", "selectWorld.gameMode.spectator.line1": "DabejlaH 'ach yIHotQo'", "selectWorld.gameMode.survival": "taHmeH Quj", "selectWorld.gameMode.survival.info": "qo' Sovbe'lu'b<PERSON>h <PERSON>. pa' jo <PERSON>; 'ej b<PERSON><PERSON><PERSON><PERSON>; 'ej t<PERSON><PERSON><PERSON><PERSON> DaSuv.", "selectWorld.gameMode.survival.line1": "jo <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> t<PERSON>,", "selectWorld.gameMode.survival.line2": "yIrac<PERSON>'<PERSON><PERSON>, yIghungchoH", "selectWorld.gameRules": "Quj che'", "selectWorld.import_worldgen_settings": "choHmey <PERSON>", "selectWorld.import_worldgen_settings.failure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings.select_file": "DuHmey ta wIv (.json)", "selectWorld.incompatible.description": "This world cannot be opened in this version.\nIt was last played in version %s.", "selectWorld.incompatible.info": "Incompatible version: %s", "selectWorld.incompatible.title": "Incompatible version", "selectWorld.incompatible.tooltip": "This world cannot be opened because it was created by an incompatible version.", "selectWorld.incompatible_series": "tlhoy pIm nIqHom Sar", "selectWorld.load_folder_access": "Quj qo' polbogh qawHaq naw'laHbe'!", "selectWorld.loading_list": "qo' tetlh lI'lI'", "selectWorld.locked": "NgaQ latlh qet instance vo' minecraft", "selectWorld.mapFeatures": "qachmey lIng", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON>b<PERSON>h, latlh je", "selectWorld.mapType": "qo' <PERSON>gh", "selectWorld.mapType.normal": "motlh", "selectWorld.moreWorldOptions": "latlh qo' DuHmey...", "selectWorld.newWorld": "qo' chu'", "selectWorld.recreate": "chenqa'moH", "selectWorld.recreate.customized.text": "qo'mey tlhIn leHtaHbe' minecraft Seghvam. qo'vam wIlIngqa'meH qo' mI'Daj DI'onmeyDaj je DIlo' 'e' wInIDlaH, 'ach ngab puH choHmey tlhIn. qay'mo' matlhIj!", "selectWorld.recreate.customized.title": "qo'mey tlhIn DIleHtaHbe'", "selectWorld.recreate.error.text": "qo' lIngqa' 'e' nIDtaHvIS qaS Qagh.", "selectWorld.recreate.error.title": "Qagh qaS!", "selectWorld.resource_load": "jo ghu<PERSON>lI'...", "selectWorld.resultFolder": "<PERSON><PERSON> <PERSON><PERSON>:", "selectWorld.search": "qo'mey nej", "selectWorld.seedInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>, qo' mI' Haw wIv Quj", "selectWorld.select": "qo've<PERSON><PERSON><PERSON><PERSON>uj", "selectWorld.targetFolder": "Save folder: %s", "selectWorld.title": "qo' yIwIv", "selectWorld.tooltip.fromNewerVersion1": "<PERSON>gh chu' ghaj qo',", "selectWorld.tooltip.fromNewerVersion2": "DalI'chugh DaqalmoHlaH!", "selectWorld.tooltip.snapshot1": "waHmeH Segh Dalo'pa' qo'vam", "selectWorld.tooltip.snapshot2": "<PERSON><PERSON>'meH, chach y<PERSON>chenmo<PERSON>.", "selectWorld.unable_to_load": "qo'mey lI'laHbe'", "selectWorld.version": "Segh:", "selectWorld.versionJoinButton": "<PERSON>ob <PERSON>, lI'", "selectWorld.versionQuestion": "qo'vam <PERSON>' DaneHbej'a'?", "selectWorld.versionUnknown": "Sovbe'lu'", "selectWorld.versionWarning": "qaSDI' qo'vam <PERSON>uj <PERSON>t, nIqHom mI' %s lo'lu'. <PERSON><PERSON><PERSON><PERSON>, qo'vam <PERSON>!", "selectWorld.warning.deprecated.question": "Some features used are deprecated and will stop working in the future. Do you wish to proceed?", "selectWorld.warning.deprecated.title": "ghuH! DI'onmey notlh lo' DuHmeyvam", "selectWorld.warning.experimental.question": "Du<PERSON><PERSON>y<PERSON><PERSON> waHlu' neH vaj 'opleS Qapbe'choHlaH. bIruch DaneH'a'?", "selectWorld.warning.experimental.title": "ghuH! waHmeH DI'onmey lo' DuHmeyvam", "selectWorld.warning.lowDiskSpace.description": "De'wI'DajDaq chIm qawHaq puS.\nQujlu'taHvIS buy'choHchu'chugh De' jengva' qawHaq vaj chaq qo'lIj QIH wanI'.", "selectWorld.warning.lowDiskSpace.title": "ghuH! puS De' jengva' qawHaq chIm!", "selectWorld.world": "qo'", "sign.edit": "<PERSON> <PERSON><PERSON><PERSON><PERSON> gher", "sleep.not_possible": "QongmeH poH yap tu'lu'be, ramvam juSmeH", "sleep.players_sleeping": "QongtaH %s/%s", "sleep.skipping_night": "qaStaHvIS ramvam <PERSON>'", "slot.only_single_allowed": "ngIq buq neH chaw', ‹%s› Suq", "slot.unknown": "<PERSON><PERSON>'bogh: %s", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "'umber", "soundCategory.block": "<PERSON><PERSON><PERSON><PERSON>", "soundCategory.hostile": "jagh yagh", "soundCategory.master": "Hoch wabmey", "soundCategory.music": "QoQ", "soundCategory.neutral": "jup yagh", "soundCategory.player": "QujwI'pu'", "soundCategory.record": "bom jengva' chu'wI'/'awtIr buq'Ir", "soundCategory.ui": "SeHlaw", "soundCategory.voice": "g<PERSON>h", "soundCategory.weather": "mu<PERSON>", "spectatorMenu.close": "<PERSON><PERSON><PERSON><PERSON>", "spectatorMenu.next_page": "tenwal veb", "spectatorMenu.previous_page": "tenwal vorgh", "spectatorMenu.root.prompt": "ra'meH ghItlh DawIvmeH leQ yI'uy, 'ej <PERSON><PERSON>'meH yI'uyqa'.", "spectatorMenu.team_teleport": "ghomlIj vInDa'Daq jol", "spectatorMenu.team_teleport.prompt": "bIjolmeH ghom yIwIv", "spectatorMenu.teleport": "Qujw<PERSON><PERSON><PERSON><PERSON> jol", "spectatorMenu.teleport.prompt": "bIjolmeH QujwI' yIwIv", "stat.generalButton": "le'be'", "stat.itemsButton": "<PERSON><PERSON><PERSON>", "stat.minecraft.animals_bred": "Ha'DIbaH 'ar <PERSON>'", "stat.minecraft.aviate_one_cm": "telDu'qoq chuq", "stat.minecraft.bell_ring": "'In 'ar chu'pu'", "stat.minecraft.boat_one_cm": "bI<PERSON> chu<PERSON> '<PERSON><PERSON>", "stat.minecraft.clean_armor": "may' Sut 'ar Say'moHpu'", "stat.minecraft.clean_banner": "joqwI' Say'moHlu'", "stat.minecraft.clean_shulker_box": "shulker ngaSwI' 'ar Say'moHpu'", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "wan<PERSON>a'taHv<PERSON> porghlIj, chu<PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.damage_absorbed": "QIH '<PERSON><PERSON>", "stat.minecraft.damage_blocked_by_shield": "QIH 'ar bot yoD", "stat.minecraft.damage_dealt": "DaQIHmoH 'ar", "stat.minecraft.damage_dealt_absorbed": "QIH 'ar <PERSON> (bumlu')", "stat.minecraft.damage_dealt_resisted": "QIH '<PERSON><PERSON> ('omlu')", "stat.minecraft.damage_resisted": "QI<PERSON> '<PERSON><PERSON>", "stat.minecraft.damage_taken": "QIH yaH", "stat.minecraft.deaths": "'<PERSON><PERSON><PERSON><PERSON> b<PERSON>Hegh", "stat.minecraft.drop": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.eat_cake_slice": "chab'a' bay<PERSON><PERSON> 'ar <PERSON>", "stat.minecraft.enchant_item": "'a<PERSON><PERSON><PERSON>'vaD tlheH DareS", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.fill_cauldron": "'arlogh bargh'a' <PERSON>b", "stat.minecraft.fish_caught": "g<PERSON><PERSON>' jonpu'", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.inspect_dispenser": "'a<PERSON><PERSON>h peSwI'<PERSON><PERSON> b<PERSON><PERSON>j", "stat.minecraft.inspect_dropper": "'<PERSON><PERSON><PERSON><PERSON> ch<PERSON>w<PERSON>'<PERSON><PERSON> b<PERSON><PERSON>j", "stat.minecraft.inspect_hopper": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>nej", "stat.minecraft.interact_with_anvil": "'arlogh 'achme' <PERSON><PERSON>'", "stat.minecraft.interact_with_beacon": "'arlogh rI'meH wovmoHwI' Dalo'", "stat.minecraft.interact_with_blast_furnace": "'<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>' tuj<PERSON><PERSON>' <PERSON><PERSON>'", "stat.minecraft.interact_with_brewingstand": "'arlogh taS vutmeH qal'aq <PERSON>'", "stat.minecraft.interact_with_campfire": "'arl<PERSON>h ra<PERSON> qul <PERSON>'", "stat.minecraft.interact_with_cartography_table": "'arlogh pu'jIn raS <PERSON>o'", "stat.minecraft.interact_with_crafting_table": "'arl<PERSON>h chenmo<PERSON>meH raS Dalo'", "stat.minecraft.interact_with_furnace": "'a<PERSON><PERSON><PERSON> v<PERSON>ncha' <PERSON><PERSON>'", "stat.minecraft.interact_with_grindstone": "'a<PERSON><PERSON><PERSON> j<PERSON>mo<PERSON>meH nagh Dalo'", "stat.minecraft.interact_with_lectern": "'arlogh paq raS Dalo'", "stat.minecraft.interact_with_loom": "'a<PERSON><PERSON>h nIqmeH qal'aq <PERSON>'", "stat.minecraft.interact_with_smithing_table": "'a<PERSON><PERSON><PERSON> m<PERSON><PERSON><PERSON><PERSON><PERSON> jan <PERSON>'", "stat.minecraft.interact_with_smoker": "'arlogh tlhIch vIncha' Dalo'", "stat.minecraft.interact_with_stonecutter": "'arlogh nagh pe'wI' Dalo'", "stat.minecraft.jump": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.leave_game": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>j", "stat.minecraft.minecart_one_cm": "tlhIlHal <PERSON> chuq 'ar <PERSON>", "stat.minecraft.mob_kills": "'a<PERSON><PERSON>h yagh Ho<PERSON>", "stat.minecraft.open_barrel": "'<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.open_chest": "<PERSON><PERSON><PERSON><PERSON><PERSON>po<PERSON>", "stat.minecraft.open_enderchest": "poS ender <PERSON>", "stat.minecraft.open_shulker_box": "'a<PERSON><PERSON><PERSON> shulker ngaSwI' DapoSmoH", "stat.minecraft.pig_one_cm": "<PERSON><PERSON><PERSON><PERSON><PERSON>u<PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.play_noteblock": "'a<PERSON><PERSON><PERSON> 'awt<PERSON>r bu<PERSON>'<PERSON>r <PERSON>'", "stat.minecraft.play_record": "'a<PERSON><PERSON><PERSON> bom jengva' DamuchmoH", "stat.minecraft.play_time": "bIQujtaHvIS poH 'ar", "stat.minecraft.player_kills": "QujwI'pu' HoHta'", "stat.minecraft.pot_flower": "'InSong balDaq San'emD<PERSON> '<PERSON><PERSON>", "stat.minecraft.raid_trigger": "yo<PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.raid_win": "yot '<PERSON><PERSON>", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.sneak_time": "bIjoDtaHvIS qaS poH 'ar", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.strider_one_cm": "yItwI' Dal<PERSON>ghtaHvIS, chu<PERSON> 'ar <PERSON>", "stat.minecraft.swim_one_cm": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.talked_to_villager": "'a<PERSON><PERSON><PERSON> vengHom ghotvaD bIjatlh", "stat.minecraft.target_hit": "DoS weq", "stat.minecraft.time_since_death": "poH 'ar ret DaHeghpu'", "stat.minecraft.time_since_rest": "poH 'ar ret DaQ<PERSON>pu'", "stat.minecraft.total_world_time": "qo'Daq poH", "stat.minecraft.traded_with_villager": "'a<PERSON><PERSON><PERSON> vengHom ghotvaD bImech", "stat.minecraft.trigger_trapped_chest": "'a<PERSON><PERSON><PERSON>' <PERSON><PERSON><PERSON><PERSON>'", "stat.minecraft.tune_noteblock": "'a<PERSON><PERSON><PERSON> 'awt<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "stat.minecraft.use_cauldron": "bargh'a'vo' bI<PERSON> 'ar <PERSON>", "stat.minecraft.walk_on_water_one_cm": "bIq beb <PERSON>taHvIS, chuq 'ar <PERSON>t", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "stat.minecraft.walk_under_water_one_cm": "bIQDaq SoHtaHvIS, chuq 'ar DayIt", "stat.mobsButton": "yagh", "stat_type.minecraft.broken": "'a<PERSON><PERSON>h ghorlu'pu'", "stat_type.minecraft.crafted": "'a<PERSON><PERSON><PERSON> chen<PERSON>", "stat_type.minecraft.dropped": "chaghlu'pu'", "stat_type.minecraft.killed": "HoH SoH %s %s", "stat_type.minecraft.killed.none": "not %s DaHoH", "stat_type.minecraft.killed_by": "%2$slogh Du<PERSON>oH %1$s", "stat_type.minecraft.killed_by.none": "not DuHoH %s", "stat_type.minecraft.mined": "'arl<PERSON>h tlhIl", "stat_type.minecraft.picked_up": "woHpu'", "stat_type.minecraft.used": "'arlogh lo'", "stats.none": "-", "structure_block.button.detect_size": "jem", "structure_block.button.load": "PU'HICH", "structure_block.button.save": "ngeq", "structure_block.custom_data": "De per pong tlhIn", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "HeH: %s", "structure_block.hover.data": "De': %s", "structure_block.hover.load": "qem: %s", "structure_block.hover.save": "ngeq: %s", "structure_block.include_entities": "Dol yugh:", "structure_block.integrity": "qach naQta<PERSON>ghach qo' mI' je", "structure_block.integrity.integrity": "qach na<PERSON>ach", "structure_block.integrity.seed": "qach mI'", "structure_block.invalid_structure_name": "qach pong waS 'oH %s'e'", "structure_block.load_not_found": "'%s' qach naw'laHbe'", "structure_block.load_prepare": "cher<PERSON>' qach %s <PERSON>q", "structure_block.load_success": "«%s»vo' qach <PERSON>'", "structure_block.mode.corner": "po'oH", "structure_block.mode.data": "<PERSON>'", "structure_block.mode.load": "qem", "structure_block.mode.save": "ngeq", "structure_block.mode_info.corner": "po'oH lo' - langhach tInghach je ghitlhwI'", "structure_block.mode_info.data": "De' lo' - Quj meq ghItlhwI'", "structure_block.mode_info.load": "lI'meH lo' - tavo' lab", "structure_block.mode_info.save": "choq lo' - taDaq ghitlh", "structure_block.position": "<PERSON><PERSON> tlhab<PERSON>'", "structure_block.position.x": "<PERSON>q tlhab<PERSON>a' «x»", "structure_block.position.y": "<PERSON>q tlhabHa' «y»", "structure_block.position.z": "<PERSON><PERSON> tlhab<PERSON>' «z»", "structure_block.save_failure": "%s qach ngeqlaHbe'", "structure_block.save_success": "%s qach ngeqta'", "structure_block.show_air": "bu<PERSON>'<PERSON><PERSON>'lu'bog<PERSON> 'ang:", "structure_block.show_boundingbox": "veH qal'aq 'ang:", "structure_block.size": "tIn qach pagh mach", "structure_block.size.x": "qach tIng<PERSON>ch «x»", "structure_block.size.y": "qach tInghach «y»", "structure_block.size.z": "qach t<PERSON><PERSON><PERSON>ch «z»", "structure_block.size_failure": "qach tInghach jemlaHbe'. nelchuqbogh qach pong ghajbogh po'oHmey'e' y<PERSON>chel.", "structure_block.size_success": "%s juvchu'pu'", "structure_block.strict": "narghmoHghach yItlh:", "structure_block.structure_name": "qach pong", "subtitles.ambient.cave": "taQ wab", "subtitles.ambient.sound": "wab taQ", "subtitles.block.amethyst_block.chime": "'In tlhen choSom nguv", "subtitles.block.amethyst_block.resonate": "choSom nguvvo' 'et wab", "subtitles.block.anvil.destroy": "'achme' Qaw'lu'", "subtitles.block.anvil.land": "pum 'achme'", "subtitles.block.anvil.use": "'achme' lo'lu'", "subtitles.block.barrel.close": "q<PERSON><PERSON>'", "subtitles.block.barrel.open": "qegh poSmoHlu'", "subtitles.block.beacon.activate": "rI'meH wovmoHwI' chu'lu'", "subtitles.block.beacon.ambient": "Huy rI'meH wovmoHwI'", "subtitles.block.beacon.deactivate": "rI'meH wovmoHwI' chu'Ha'lu'", "subtitles.block.beacon.power_select": "rI'meH wovmoHwI' HoS wIvlu'", "subtitles.block.beehive.drip": "nIj DayqIr", "subtitles.block.beehive.enter": "lachvoH 'el maqSung", "subtitles.block.beehive.exit": "lachvoH mej maqSung", "subtitles.block.beehive.shear": "tey cha''etlh pe'wI''a'", "subtitles.block.beehive.work": "vum maqSung", "subtitles.block.bell.resonate": "wal 'In", "subtitles.block.bell.use": "'In tlhawlu'", "subtitles.block.big_dripleaf.tilt_down": "ghIr por tom", "subtitles.block.big_dripleaf.tilt_up": "ghIrHa' por tom", "subtitles.block.blastfurnace.fire_crackle": "meQ vIncha' tujqu'", "subtitles.block.brewing_stand.brew": "ngon taS vutmeH qal'aq", "subtitles.block.bubble_column.bubble_pop": "ghor b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.upwards_ambient": "ghIt b<PERSON>'<PERSON><PERSON><PERSON>y", "subtitles.block.bubble_column.upwards_inside": "wab chen<PERSON><PERSON> b<PERSON>'<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.whirlpool_ambient": "DIng b<PERSON>'<PERSON><PERSON><PERSON>y", "subtitles.block.bubble_column.whirlpool_inside": "nom vIH ba'Suqmey", "subtitles.block.button.click": "rIS leQ", "subtitles.block.cake.add_candle": "chab'a'Daq weQ lanlu'", "subtitles.block.campfire.crackle": "rIS raQ qul", "subtitles.block.candle.crackle": "rIS weQ", "subtitles.block.candle.extinguish": "<PERSON><PERSON>'", "subtitles.block.chest.close": "DerlIq SoSoQmoHlu'", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.chest.open": "DerlIq SopoSmoHlu'", "subtitles.block.chorus_flower.death": "Hegh bomwI' 'InSong", "subtitles.block.chorus_flower.grow": "nenchoH bomwI' ghom 'InSong", "subtitles.block.comparator.click": "rIS juvwI'", "subtitles.block.composter.empty": "nonmoHwI' 'Ib tebHa'lu'", "subtitles.block.composter.fill": "nonmoHwI' 'Ib teblu'", "subtitles.block.composter.ready": "nonmoH nonmoHwI' 'Ib", "subtitles.block.conduit.activate": "'och chu'lu'", "subtitles.block.conduit.ambient": "joq 'och", "subtitles.block.conduit.attack.target": "HIv 'och", "subtitles.block.conduit.deactivate": "'och chu'Ha'lu'", "subtitles.block.copper_bulb.turn_off": "Sorpuq wovmoHwI' chu'Ha'lu'", "subtitles.block.copper_bulb.turn_on": "Sorpuq wovmoHwI' chu'lu'", "subtitles.block.copper_trapdoor.close": "SoQchoH vonmeH lojmIt", "subtitles.block.copper_trapdoor.open": "poSchoH vonmeH lojmIt", "subtitles.block.crafter.craft": "chenmoH chenmoHwI'", "subtitles.block.crafter.fail": "chenmoH 'e' luj chenmoHwI'", "subtitles.block.creaking_heart.hurt": "jach <PERSON>wI' tIq", "subtitles.block.creaking_heart.idle": "wab taQ", "subtitles.block.creaking_heart.spawn": "vem HaSwI' tIq", "subtitles.block.deadbush.idle": "'umber QaD wabmey", "subtitles.block.decorated_pot.insert": "mIllogh bal buy'moHlu'", "subtitles.block.decorated_pot.insert_fail": "ler m<PERSON><PERSON><PERSON> bal", "subtitles.block.decorated_pot.shatter": "ghor m<PERSON><PERSON><PERSON> bal", "subtitles.block.dispenser.dispense": "Doch peSlu'", "subtitles.block.dispenser.fail": "QapHa' peSwI'", "subtitles.block.door.toggle": "HaS lojmIt", "subtitles.block.dried_ghast.ambient": "tlhov ghast QaD", "subtitles.block.dried_ghast.ambient_water": "yIQqa' <PERSON><PERSON>t QaD", "subtitles.block.dried_ghast.place_in_water": "ghast QaD HaH", "subtitles.block.dried_ghast.transition": "pIvchoH ghast QaD", "subtitles.block.dry_grass.ambient": "SuS wabmey", "subtitles.block.enchantment_table.use": "reSmeH raS lo'lu'", "subtitles.block.end_portal.spawn": "end lojmIt poSmoHlu'", "subtitles.block.end_portal_frame.fill": "ender mIn vevlu'", "subtitles.block.eyeblossom.close": "SoQchoH mIn 'InSong", "subtitles.block.eyeblossom.idle": "tlhup mIn 'InSong", "subtitles.block.eyeblossom.open": "poSchoH mIn 'InSong", "subtitles.block.fence_gate.toggle": "HaS Solrogh lojmIt", "subtitles.block.fire.ambient": "meQ qul", "subtitles.block.fire.extinguish": "qul chu'Ha'lu'", "subtitles.block.firefly_bush.idle": "tlhen wewbogh ghewmey", "subtitles.block.frogspawn.hatch": "pel'a<PERSON><PERSON><PERSON> ghor yem<PERSON>aw", "subtitles.block.furnace.fire_crackle": "meQ vIncha'", "subtitles.block.generic.break": "bu<PERSON>'<PERSON><PERSON> ghor<PERSON>'", "subtitles.block.generic.fall": "bu<PERSON>'<PERSON><PERSON><PERSON><PERSON> pum vay'", "subtitles.block.generic.footsteps": "yIt", "subtitles.block.generic.hit": "bu<PERSON>'<PERSON><PERSON>'", "subtitles.block.generic.place": "bu<PERSON>'<PERSON><PERSON>'", "subtitles.block.grindstone.use": "jejmoHmeH nagh lo'lu'", "subtitles.block.growing_plant.crop": "poch pe’", "subtitles.block.hanging_sign.waxed_interact_fail": "ler QIn 'echlet", "subtitles.block.honey_block.slide": "DayqIr ngogh<PERSON>aq tlhamlu'taH", "subtitles.block.iron_trapdoor.close": "SoQchoH vonmeH lojmIt", "subtitles.block.iron_trapdoor.open": "poSchoH vonmeH lojmIt", "subtitles.block.lava.ambient": "rIS vaHbo'", "subtitles.block.lava.extinguish": "SuqwIl tlhen vaHbo'", "subtitles.block.lever.click": "chu'meH naQ chu'lu'", "subtitles.block.note_block.note": "'awt<PERSON>r buq'Ir chu'lu'", "subtitles.block.pale_hanging_moss.idle": "wab taQ", "subtitles.block.piston.move": "vIH ghunglItlh", "subtitles.block.pointed_dripstone.drip_lava": "pum vaHbo' 'onroS", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "bargh'a'Daq pum vaHbo' 'onroS", "subtitles.block.pointed_dripstone.drip_water": "pum bI<PERSON> 'onroS", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "bargh'a'Daq pum bIQ 'onroS", "subtitles.block.pointed_dripstone.land": "pum <PERSON>r", "subtitles.block.portal.ambient": "'<PERSON><PERSON> tlhen nether lojmIt", "subtitles.block.portal.travel": "nup nether lojmIt wab", "subtitles.block.portal.trigger": "ghur nether lojmIt wab", "subtitles.block.pressure_plate.click": "rIS Surmen 'echlet", "subtitles.block.pumpkin.carve": "quH cha''etlh pe'wI''a'", "subtitles.block.redstone_torch.burnout": "Sech natlhlu'", "subtitles.block.respawn_anchor.ambient": "'<PERSON><PERSON> tlhen nether lojmIt", "subtitles.block.respawn_anchor.charge": "b<PERSON><PERSON><PERSON>'me<PERSON> jan <PERSON>'", "subtitles.block.respawn_anchor.deplete": "bogh<PERSON>'meH jan natlhlu'", "subtitles.block.respawn_anchor.set_spawn": "bogh<PERSON>'meH <PERSON><PERSON> cher bogh<PERSON>'meH jan", "subtitles.block.sand.idle": "Do'ol wabmey", "subtitles.block.sand.wind": "SuS wabmey", "subtitles.block.sculk.charge": "ngon sculk", "subtitles.block.sculk.spread": "<PERSON>ch sculk", "subtitles.block.sculk_catalyst.bloom": "lew sculk lIngwI'", "subtitles.block.sculk_sensor.clicking": "jem<PERSON><PERSON> r<PERSON> sculk noch", "subtitles.block.sculk_sensor.clicking_stop": "jem<PERSON><PERSON>'mo' r<PERSON> sculk noch", "subtitles.block.sculk_shrieker.shriek": "jach sculk jachwI'", "subtitles.block.shulker_box.close": "SoQmoH shulker", "subtitles.block.shulker_box.open": "poSmoH shulker", "subtitles.block.sign.waxed_interact_fail": "ler QIn 'echlet", "subtitles.block.smithing_table.use": "mitlhmeH raS lo'lu'", "subtitles.block.smoker.smoke": "vut tlhIch vIncha'", "subtitles.block.sniffer_egg.crack": "rIS larghwI' QIm", "subtitles.block.sniffer_egg.hatch": "pel'a<PERSON><PERSON><PERSON> ghor larghwI'", "subtitles.block.sniffer_egg.plop": "yIt larghwI'", "subtitles.block.sponge.absorb": "bum va'chum", "subtitles.block.sweet_berry_bush.pick_berries": "pum na<PERSON>'", "subtitles.block.trapdoor.close": "SoQchoH vonmeH lojmIt", "subtitles.block.trapdoor.open": "poSchoH vonmeH lojmIt", "subtitles.block.trapdoor.toggle": "HaS vonmeH lojmIt", "subtitles.block.trial_spawner.about_to_spawn_item": "narghbeH maQmIgh Doch", "subtitles.block.trial_spawner.ambient": "rIS qaD boghmoHwI'", "subtitles.block.trial_spawner.ambient_charged": "rIS maQmIgh qaD boghmoHwI'", "subtitles.block.trial_spawner.ambient_ominous": "rIS maQmIgh qaD boghmoHwI'", "subtitles.block.trial_spawner.charge_activate": "maQmIgh luS qaD boghmoHwI'", "subtitles.block.trial_spawner.close_shutter": "SoQchoH qaD boghmoHwI'", "subtitles.block.trial_spawner.detect_player": "<PERSON><PERSON>'<PERSON>gh qaD boghmoHwI'", "subtitles.block.trial_spawner.eject_item": "<PERSON><PERSON><PERSON> vuj qaD boghmoHwI'", "subtitles.block.trial_spawner.ominous_activate": "maQmIgh luS qaD boghmoHwI'", "subtitles.block.trial_spawner.open_shutter": "poSchoH qaD boghmoHwI'", "subtitles.block.trial_spawner.spawn_item": "maQmI<PERSON> ch<PERSON>'", "subtitles.block.trial_spawner.spawn_item_begin": "nargh maQmIgh Doch", "subtitles.block.trial_spawner.spawn_mob": "bogh yagh", "subtitles.block.tripwire.attach": "vev tangmoHmeH SIrgh", "subtitles.block.tripwire.click": "rIS tangmoHmeH SIrgh", "subtitles.block.tripwire.detach": "vevHa' tangmoHmeH SIrgh", "subtitles.block.vault.activate": "vem qengHo<PERSON> bu<PERSON>'<PERSON>r", "subtitles.block.vault.ambient": "rIS qengHoD buq'Ir", "subtitles.block.vault.close_shutter": "SoQchoH qengHoD buq'Ir", "subtitles.block.vault.deactivate": "QongchoH qengHoD buq'Ir", "subtitles.block.vault.eject_item": "<PERSON><PERSON> vuj qeng<PERSON><PERSON><PERSON> bu<PERSON>'<PERSON>r", "subtitles.block.vault.insert_item": "ngaQHa'choH qengHoD buq'Ir", "subtitles.block.vault.insert_item_fail": "ngaQ<PERSON><PERSON>'cho<PERSON> <PERSON><PERSON>' luj qeng<PERSON>o<PERSON> buq'<PERSON>r", "subtitles.block.vault.open_shutter": "poSchoH qengHoD buq'Ir", "subtitles.block.vault.reject_rewarded_player": "pop Suq<PERSON>'bogh QujwI' lajQo' qengHoD buq'Ir", "subtitles.block.water.ambient": "ghIt bIQ", "subtitles.block.wet_sponge.dries": "QaDchoH va'chum", "subtitles.chiseled_bookshelf.insert": "paq lanlu'", "subtitles.chiseled_bookshelf.insert_enchanted": "tlheH paq lanlu'", "subtitles.chiseled_bookshelf.take": "paq lellu'", "subtitles.chiseled_bookshelf.take_enchanted": "tlheH paq lellu'", "subtitles.enchant.thorns.hit": "'urgh <PERSON>wI'mey", "subtitles.entity.allay.ambient_with_item": "Doch nej yIrwI'", "subtitles.entity.allay.ambient_without_item": "neH yIrwI'", "subtitles.entity.allay.death": "Hegh yIrwI'", "subtitles.entity.allay.hurt": "'oy' yIrwI'", "subtitles.entity.allay.item_given": "Hagh yIrwI'", "subtitles.entity.allay.item_taken": "yIr yIrwI'", "subtitles.entity.allay.item_thrown": "vo' yIrwI'", "subtitles.entity.armadillo.ambient": "ghugh woSwa'", "subtitles.entity.armadillo.brush": "woSwa' yach", "subtitles.entity.armadillo.death": "Hegh woSwa'", "subtitles.entity.armadillo.eat": "Sop woSwa'", "subtitles.entity.armadillo.hurt": "'oy' woSwa'", "subtitles.entity.armadillo.hurt_reduced": "yoD'egh woSwa'", "subtitles.entity.armadillo.land": "Saq woSwa'", "subtitles.entity.armadillo.peek": "ghan wo<PERSON>wa'", "subtitles.entity.armadillo.roll": "vatlh woSwa'", "subtitles.entity.armadillo.scute_drop": "pum ghISDen", "subtitles.entity.armadillo.unroll_finish": "vatlhHa' woSwa'", "subtitles.entity.armadillo.unroll_start": "vatlhHa'choH woSwa'", "subtitles.entity.armor_stand.fall": "pumlu'pu'", "subtitles.entity.arrow.hit": "mup naQjejHom", "subtitles.entity.arrow.hit_player": "Qujw<PERSON>' bach bach", "subtitles.entity.arrow.shoot": "naQjejHom bachlu'", "subtitles.entity.axolotl.attack": "HIv 'evta'", "subtitles.entity.axolotl.death": "Hegh 'evta'", "subtitles.entity.axolotl.hurt": "'oy' 'evta'", "subtitles.entity.axolotl.idle_air": "ghugh 'evta'", "subtitles.entity.axolotl.idle_water": "ghugh 'evta'", "subtitles.entity.axolotl.splash": "bob<PERSON>ar tlhen 'evta'", "subtitles.entity.axolotl.swim": "Qal 'evta'", "subtitles.entity.bat.ambient": "jach valqe'", "subtitles.entity.bat.death": "Hegh valqe'", "subtitles.entity.bat.hurt": "'oy' valqe'", "subtitles.entity.bat.takeoff": "puvchoH valqe'", "subtitles.entity.bee.ambient": "<PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON><PERSON> ma<PERSON>", "subtitles.entity.bee.hurt": "'oy' maq<PERSON>ung", "subtitles.entity.bee.loop": "<PERSON><PERSON>", "subtitles.entity.bee.loop_aggressive": "<PERSON><PERSON> ma<PERSON> QeH", "subtitles.entity.bee.pollinate": "<PERSON><PERSON> <PERSON>", "subtitles.entity.bee.sting": "'aw' maq<PERSON>ung", "subtitles.entity.blaze.ambient": "tlhuH qul tl<PERSON><PERSON>gh", "subtitles.entity.blaze.burn": "meQ qul tlha<PERSON>gh", "subtitles.entity.blaze.death": "<PERSON><PERSON> qul tlha<PERSON>gh", "subtitles.entity.blaze.hurt": "'oy' qul t<PERSON><PERSON><PERSON>gh", "subtitles.entity.blaze.shoot": "bach qul tl<PERSON><PERSON>gh", "subtitles.entity.boat.paddle_land": "bIQ Duj vo'lu'taH", "subtitles.entity.boat.paddle_water": "bIQ Duj vo'lu'", "subtitles.entity.bogged.ambient": "chuS nobmeD yIQ", "subtitles.entity.bogged.death": "Hegh nobmeD yIQ", "subtitles.entity.bogged.hurt": "'oy' nobmeD yIQ", "subtitles.entity.breeze.charge": "<PERSON><PERSON>gh", "subtitles.entity.breeze.death": "Hegh SuS tlhapragh", "subtitles.entity.breeze.deflect": "bach way' <PERSON><PERSON> t<PERSON>", "subtitles.entity.breeze.hurt": "'oy' <PERSON><PERSON>", "subtitles.entity.breeze.idle_air": "puv SuS tlhapragh", "subtitles.entity.breeze.idle_ground": "cheq SuS tlhapragh", "subtitles.entity.breeze.inhale": "pur <PERSON><PERSON> tl<PERSON>gh", "subtitles.entity.breeze.jump": "Sup SuS tlhapragh", "subtitles.entity.breeze.land": "<PERSON>q <PERSON> tl<PERSON>gh", "subtitles.entity.breeze.shoot": "baH SuS tlhapragh", "subtitles.entity.breeze.slide": "tlham SuS tlhapragh", "subtitles.entity.breeze.whirl": "ver <PERSON><PERSON>", "subtitles.entity.breeze.wind_burst": "jor <PERSON> bach", "subtitles.entity.camel.ambient": "ghugh qa'mel", "subtitles.entity.camel.dash": "Sup qa'mel", "subtitles.entity.camel.dash_ready": "SuprupchoH qa'mel", "subtitles.entity.camel.death": "Hegh qa'mel", "subtitles.entity.camel.eat": "Sop qa'mel", "subtitles.entity.camel.hurt": "'oy' qa'mel", "subtitles.entity.camel.saddle": "ba'qIn jom", "subtitles.entity.camel.sit": "ba'choH qa'mel", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON><PERSON> qa'mel", "subtitles.entity.camel.step": "yIt qa'mel", "subtitles.entity.camel.step_sand": "Do'olDaq yIt qa'mel", "subtitles.entity.cat.ambient": "'<PERSON><PERSON><PERSON> vIghro'", "subtitles.entity.cat.beg_for_food": "<PERSON><PERSON>' v<PERSON><PERSON><PERSON>'", "subtitles.entity.cat.death": "Hegh vIghro'", "subtitles.entity.cat.eat": "Sop vIghro'", "subtitles.entity.cat.hiss": "SuqwIl tlhen vIghro'", "subtitles.entity.cat.hurt": "'oy' vIghro'", "subtitles.entity.cat.purr": "Huy vIghro'", "subtitles.entity.chicken.ambient": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.chicken.death": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.chicken.egg": "QIm narghmo<PERSON>", "subtitles.entity.chicken.hurt": "'oy' 'u<PERSON><PERSON><PERSON>", "subtitles.entity.cod.death": "Hegh qaD ghotI'", "subtitles.entity.cod.flop": "Sup qaD ghotI'", "subtitles.entity.cod.hurt": "'oy' qaD ghotI'", "subtitles.entity.cow.ambient": "ghugh tangqa'", "subtitles.entity.cow.death": "<PERSON>gh tangqa'", "subtitles.entity.cow.hurt": "'oy' tangqa'", "subtitles.entity.cow.milk": "tangqa' nIm remlu'", "subtitles.entity.creaking.activate": "HaSwI' chu'lu'", "subtitles.entity.creaking.ambient": "HaS HaSwI'", "subtitles.entity.creaking.attack": "HIv HaSwI'", "subtitles.entity.creaking.deactivate": "HaSwI' chu'Ha'lu'", "subtitles.entity.creaking.death": "<PERSON>gh Ha<PERSON>w<PERSON>'", "subtitles.entity.creaking.freeze": "mev HaSwI'", "subtitles.entity.creaking.spawn": "yIn HaSwI'", "subtitles.entity.creaking.sway": "HaSwI' Qanlu'", "subtitles.entity.creaking.twitch": "jergh <PERSON>'", "subtitles.entity.creaking.unfreeze": "vIH HaSwI'", "subtitles.entity.creeper.death": "<PERSON>gh creeper", "subtitles.entity.creeper.hurt": "'oy' creeper", "subtitles.entity.creeper.primed": "SuqwIl tlhen creeper", "subtitles.entity.dolphin.ambient": "ghugh <PERSON>b<PERSON>", "subtitles.entity.dolphin.ambient_water": "<PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.attack": "HIv SolbIS", "subtitles.entity.dolphin.death": "<PERSON><PERSON>", "subtitles.entity.dolphin.eat": "Sop <PERSON>", "subtitles.entity.dolphin.hurt": "'oy' SolbIS", "subtitles.entity.dolphin.jump": "Su<PERSON>", "subtitles.entity.dolphin.play": "reH <PERSON>", "subtitles.entity.dolphin.splash": "bob<PERSON><PERSON> tlhen <PERSON>", "subtitles.entity.dolphin.swim": "Qal <PERSON>b<PERSON>", "subtitles.entity.donkey.ambient": "jach jach<PERSON><PERSON> Sargh", "subtitles.entity.donkey.angry": "ghugh jachbogh Sargh", "subtitles.entity.donkey.chest": "jachbogh Sargh <PERSON> jom", "subtitles.entity.donkey.death": "Hegh jachbogh Sargh", "subtitles.entity.donkey.eat": "<PERSON><PERSON> j<PERSON><PERSON><PERSON>h", "subtitles.entity.donkey.hurt": "'oy' jachbogh Sargh", "subtitles.entity.donkey.jump": "<PERSON><PERSON> j<PERSON><PERSON><PERSON> Sargh", "subtitles.entity.drowned.ambient": "ghugh yInbogh lom yIQ", "subtitles.entity.drowned.ambient_water": "bor yInbogh lom yIQ", "subtitles.entity.drowned.death": "Hegh yInbogh lom yIQ", "subtitles.entity.drowned.hurt": "'oy' yInbogh lom yIQ", "subtitles.entity.drowned.shoot": "chonnaQ chuH yInbogh lom yIQ", "subtitles.entity.drowned.step": "yIt yInbogh lom yIQ", "subtitles.entity.drowned.swim": "Qal yInbogh lom yIQ", "subtitles.entity.egg.throw": "puv QIn", "subtitles.entity.elder_guardian.ambient": "qIn 'avwI' qup", "subtitles.entity.elder_guardian.ambient_land": "joq 'avwI' qup", "subtitles.entity.elder_guardian.curse": "'IghmoH 'avwI' qup", "subtitles.entity.elder_guardian.death": "Hegh 'avwI' qup", "subtitles.entity.elder_guardian.flop": "Sup 'avwI' qup", "subtitles.entity.elder_guardian.hurt": "'oy 'avwI' qup", "subtitles.entity.ender_dragon.ambient": "bey lung'a'", "subtitles.entity.ender_dragon.death": "<PERSON>gh lung'a'", "subtitles.entity.ender_dragon.flap": "puv lung'a' tel", "subtitles.entity.ender_dragon.growl": "ghu<PERSON><PERSON> lung'a'", "subtitles.entity.ender_dragon.hurt": "'oy' lung'a'", "subtitles.entity.ender_dragon.shoot": "baH lung'a'", "subtitles.entity.ender_eye.death": "pum ender mIn", "subtitles.entity.ender_eye.launch": "ender mIn baHlu'", "subtitles.entity.ender_pearl.throw": "puv ender moQ", "subtitles.entity.enderman.ambient": "ghugh enderman", "subtitles.entity.enderman.death": "<PERSON><PERSON> enderman", "subtitles.entity.enderman.hurt": "'oy' enderman", "subtitles.entity.enderman.scream": "jach enderman", "subtitles.entity.enderman.stare": "jach enderman", "subtitles.entity.enderman.teleport": "<PERSON><PERSON> enderman", "subtitles.entity.endermite.ambient": "'eD endermite", "subtitles.entity.endermite.death": "<PERSON>gh endermite", "subtitles.entity.endermite.hurt": "'oy' endermite", "subtitles.entity.evoker.ambient": "jat reSwI'", "subtitles.entity.evoker.cast_spell": "tlheH reS reSwI'", "subtitles.entity.evoker.celebrate": "Hagh reSwI'", "subtitles.entity.evoker.death": "Hegh reSwI'", "subtitles.entity.evoker.hurt": "'oy' reSwI'", "subtitles.entity.evoker.prepare_attack": "HIvrup reSwI'", "subtitles.entity.evoker.prepare_summon": "rItrup reSwI'", "subtitles.entity.evoker.prepare_wololo": "vongrup reSwI'", "subtitles.entity.evoker_fangs.attack": "chop Ho'Du'", "subtitles.entity.experience_orb.pickup": "HenchoH", "subtitles.entity.firework_rocket.blast": "jor lop jorwI'", "subtitles.entity.firework_rocket.launch": "lop jorwI' bachlu'", "subtitles.entity.firework_rocket.twinkle": "ghon lop jorwI'", "subtitles.entity.fish.swim": "bob<PERSON><PERSON> tlhen", "subtitles.entity.fishing_bobber.retrieve": "'aywI' Suqqa'lu'", "subtitles.entity.fishing_bobber.splash": "bob<PERSON>ar tlhen 'aywI'", "subtitles.entity.fishing_bobber.throw": "'aywI' vo'lu'", "subtitles.entity.fox.aggro": "QeH qeSHoS", "subtitles.entity.fox.ambient": "HaS qeSHoS", "subtitles.entity.fox.bite": "chop qeSHoS", "subtitles.entity.fox.death": "Hegh qeSHoS", "subtitles.entity.fox.eat": "Sop qeSHoS", "subtitles.entity.fox.hurt": "'oy' qeSHoS", "subtitles.entity.fox.screech": "jach qeSHoS", "subtitles.entity.fox.sleep": "wuD qeSHoS", "subtitles.entity.fox.sniff": "largh qeSHoS", "subtitles.entity.fox.spit": "tuy' qeSHoS", "subtitles.entity.fox.teleport": "jol qeSHoS", "subtitles.entity.frog.ambient": "ghugh mabeb", "subtitles.entity.frog.death": "<PERSON><PERSON> mabeb", "subtitles.entity.frog.eat": "<PERSON>p mabeb", "subtitles.entity.frog.hurt": "'oy' mabeb", "subtitles.entity.frog.lay_spawn": "QImmey chenmo<PERSON> mabeb", "subtitles.entity.frog.long_jump": "<PERSON><PERSON> mabeb", "subtitles.entity.generic.big_fall": "pum vay'", "subtitles.entity.generic.burn": "meQtaH", "subtitles.entity.generic.death": "<PERSON><PERSON><PERSON>'", "subtitles.entity.generic.drink": "tlhutlh", "subtitles.entity.generic.eat": "SoptaH", "subtitles.entity.generic.explode": "jor", "subtitles.entity.generic.extinguish_fire": "qul chu'Ha'lu'", "subtitles.entity.generic.hurt": "'oy' vay'", "subtitles.entity.generic.small_fall": "tang vay'", "subtitles.entity.generic.splash": "bob<PERSON>ar tlhentaH", "subtitles.entity.generic.swim": "<PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "jor <PERSON> bach", "subtitles.entity.ghast.ambient": "<PERSON><PERSON> ghast", "subtitles.entity.ghast.death": "<PERSON><PERSON> ghast", "subtitles.entity.ghast.hurt": "'oy' ghast", "subtitles.entity.ghast.shoot": "bach ghast", "subtitles.entity.ghastling.ambient": "ghugh ghast puq", "subtitles.entity.ghastling.death": "<PERSON><PERSON> ghast puq", "subtitles.entity.ghastling.hurt": "'oy' ghast puq", "subtitles.entity.ghastling.spawn": "nargh ghast puq", "subtitles.entity.glow_item_frame.add_item": "wewbogh <PERSON>h bey' teblu'", "subtitles.entity.glow_item_frame.break": "wewbogh <PERSON>h bey' ghorlu'", "subtitles.entity.glow_item_frame.place": "wewbogh <PERSON>h bey' lanlu'", "subtitles.entity.glow_item_frame.remove_item": "wewbogh <PERSON>h bey' qeDlu'", "subtitles.entity.glow_item_frame.rotate_item": "rIS wewbogh Doch bey'", "subtitles.entity.glow_squid.ambient": "<PERSON>al wewbogh cheyIS", "subtitles.entity.glow_squid.death": "Hegh wewbogh cheyIS", "subtitles.entity.glow_squid.hurt": "'oy' wewbogh cheyIS", "subtitles.entity.glow_squid.squirt": "rItlh tuy' wewbogh cheyIS", "subtitles.entity.goat.ambient": "ghugh gha'cher", "subtitles.entity.goat.death": "<PERSON><PERSON> gha'cher", "subtitles.entity.goat.eat": "<PERSON><PERSON> gha'cher", "subtitles.entity.goat.horn_break": "gha'cher pu' wItlhlu' ", "subtitles.entity.goat.hurt": "'oy' gha'cher", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> g<PERSON>'cher", "subtitles.entity.goat.milk": "gha'cher nIm remlu'", "subtitles.entity.goat.prepare_ram": "yav mupqu' gha'cher lem", "subtitles.entity.goat.ram_impact": "vay' nge<PERSON> gha'cher", "subtitles.entity.goat.screaming.ambient": "jach gha'cher", "subtitles.entity.goat.step": "yIt gha'cher", "subtitles.entity.guardian.ambient": "qIn 'avwI'", "subtitles.entity.guardian.ambient_land": "joq 'avwI'", "subtitles.entity.guardian.attack": "bach 'avwI'", "subtitles.entity.guardian.death": "Hegh 'avwI'", "subtitles.entity.guardian.flop": "Sup 'avwI'", "subtitles.entity.guardian.hurt": "'oy' 'avwI'", "subtitles.entity.happy_ghast.ambient": "ghugh ghast <PERSON>", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> ghast <PERSON>", "subtitles.entity.happy_ghast.equip": "'a<PERSON><PERSON>' jom", "subtitles.entity.happy_ghast.harness_goggles_down": "puvrup ghast <PERSON>", "subtitles.entity.happy_ghast.harness_goggles_up": "mev ghast <PERSON>", "subtitles.entity.happy_ghast.hurt": "'oy' g<PERSON><PERSON>", "subtitles.entity.happy_ghast.unequip": "'aHra' jomHa'", "subtitles.entity.hoglin.ambient": "ghu<PERSON><PERSON> hoglin", "subtitles.entity.hoglin.angry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hoglin", "subtitles.entity.hoglin.attack": "HIv hoglin", "subtitles.entity.hoglin.converted_to_zombified": "zoglin moj hoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON> hoglin", "subtitles.entity.hoglin.hurt": "'oy' hoglin", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON>n", "subtitles.entity.hoglin.step": "yIt hoglin", "subtitles.entity.horse.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON>", "subtitles.entity.horse.armor": "<PERSON><PERSON><PERSON> may' <PERSON> jom", "subtitles.entity.horse.breathe": "tlhuH Sargh", "subtitles.entity.horse.death": "<PERSON><PERSON>", "subtitles.entity.horse.eat": "<PERSON><PERSON>", "subtitles.entity.horse.gallop": "qet Sargh", "subtitles.entity.horse.hurt": "'oy' <PERSON><PERSON>h", "subtitles.entity.horse.jump": "<PERSON><PERSON>", "subtitles.entity.horse.saddle": "ba'qIn jom", "subtitles.entity.husk.ambient": "qIn yub", "subtitles.entity.husk.converted_to_zombie": "yInbogh lom moj yub", "subtitles.entity.husk.death": "<PERSON>gh yub", "subtitles.entity.husk.hurt": "'oy' yub", "subtitles.entity.illusioner.ambient": "jat tojwI' 'IDnar pIn'a'", "subtitles.entity.illusioner.cast_spell": "tlheH reS tojwI' 'IDnar pIn'a'", "subtitles.entity.illusioner.death": "Hegh tojwI' 'IDnar pIn'a'", "subtitles.entity.illusioner.hurt": "'oy' tojwI' 'IDnar pIn'a'", "subtitles.entity.illusioner.mirror_move": "So''egh tojwI' 'IDnar pIn'a'", "subtitles.entity.illusioner.prepare_blindness": "leghbe'moHbogh tlheH reSrupchoH tojwI' 'IDnar pIn'a'", "subtitles.entity.illusioner.prepare_mirror": "So''eghbogh tlheH reSrupchoH tojwI' 'IDnar pIn'a'", "subtitles.entity.iron_golem.attack": "HIv 'uSqan velqa' nuv", "subtitles.entity.iron_golem.damage": "ghor 'u<PERSON>qan velqa' nuv", "subtitles.entity.iron_golem.death": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> velqa' nuv", "subtitles.entity.iron_golem.hurt": "'oy' 'u<PERSON>qan velqa' nuv", "subtitles.entity.iron_golem.repair": "'uSqan velqa' nuv tI'lu'pu'", "subtitles.entity.item.break": "<PERSON><PERSON> ghor<PERSON>'", "subtitles.entity.item.pickup": "Doch woHlu'", "subtitles.entity.item_frame.add_item": "<PERSON>h bey' teblu'", "subtitles.entity.item_frame.break": "<PERSON>h bey' ghorlu'", "subtitles.entity.item_frame.place": "<PERSON>h bey' lanlu'", "subtitles.entity.item_frame.remove_item": "<PERSON>h bey' chImm<PERSON><PERSON><PERSON>'", "subtitles.entity.item_frame.rotate_item": "rIS <PERSON><PERSON> bey'", "subtitles.entity.leash_knot.break": "rIjmeH tlhegh meS ghorlu'", "subtitles.entity.leash_knot.place": "rIjmeH tlhegh meSlu'", "subtitles.entity.lightning_bolt.impact": "mup pe'bIl", "subtitles.entity.lightning_bolt.thunder": "tuD", "subtitles.entity.llama.ambient": "ghu<PERSON> 'ImtIy", "subtitles.entity.llama.angry": "ghugh 'ImtIy QeH", "subtitles.entity.llama.chest": "'<PERSON><PERSON><PERSON><PERSON><PERSON> jom", "subtitles.entity.llama.death": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.llama.eat": "<PERSON><PERSON> <PERSON><PERSON>mtIy", "subtitles.entity.llama.hurt": "'oy' 'ImtIy", "subtitles.entity.llama.spit": "tuy' 'ImtIy", "subtitles.entity.llama.step": "yIt 'ImtIy", "subtitles.entity.llama.swag": "'ImtIy Sut tuQmoHlu'", "subtitles.entity.magma_cube.death": "<PERSON><PERSON> va<PERSON><PERSON>' <PERSON>u<PERSON><PERSON><PERSON>r", "subtitles.entity.magma_cube.hurt": "'oy' vaH<PERSON>' bu<PERSON>'<PERSON>r", "subtitles.entity.magma_cube.squish": "Sup vaH<PERSON>' <PERSON><PERSON><PERSON><PERSON><PERSON>r", "subtitles.entity.minecart.inside": "tlhIlHal Duj lIt", "subtitles.entity.minecart.inside_underwater": "bIQDaq tlhIlHal Duj lIt", "subtitles.entity.minecart.riding": "vIH tlhIlHal Duj", "subtitles.entity.mooshroom.convert": "moj mooshroom", "subtitles.entity.mooshroom.eat": "Sop mooshroom", "subtitles.entity.mooshroom.milk": "mooshroom nIm boSlu'", "subtitles.entity.mooshroom.suspicious_milk": "mooshroom nIm boSlu'. pIH", "subtitles.entity.mule.ambient": "jach <PERSON> nun<PERSON>", "subtitles.entity.mule.angry": "g<PERSON>gh <PERSON>rgh nungmaH", "subtitles.entity.mule.chest": "<PERSON>rgh nungmaH DerlIq jom", "subtitles.entity.mule.death": "<PERSON>gh <PERSON>rgh nungmaH", "subtitles.entity.mule.eat": "So<PERSON> nungma<PERSON>", "subtitles.entity.mule.hurt": "'oy' <PERSON><PERSON>h nungmaH", "subtitles.entity.mule.jump": "<PERSON><PERSON>h nungmaH", "subtitles.entity.painting.break": "nagh beQ ghorlu'", "subtitles.entity.painting.place": "nagh beQ lanlu'", "subtitles.entity.panda.aggressive_ambient": "ghugh panDa' vaQ", "subtitles.entity.panda.ambient": "tlhov panDa'", "subtitles.entity.panda.bite": "chop panDa'", "subtitles.entity.panda.cant_breed": "bam<PERSON>' laj<PERSON><PERSON>' panDa'", "subtitles.entity.panda.death": "<PERSON>gh panDa'", "subtitles.entity.panda.eat": "Sop panDa'", "subtitles.entity.panda.hurt": "'oy' panDa'", "subtitles.entity.panda.pre_sneeze": "panDa' ghIch qotlhlu'", "subtitles.entity.panda.sneeze": "chuy pan<PERSON>a'", "subtitles.entity.panda.step": "yIt panDa'", "subtitles.entity.panda.worried_ambient": "vIng panDa'", "subtitles.entity.parrot.ambient": "jatlh vIlInHoD", "subtitles.entity.parrot.death": "Hegh vIlInHoD", "subtitles.entity.parrot.eats": "Sop vIlInHoD", "subtitles.entity.parrot.fly": "puv vIlInHoD", "subtitles.entity.parrot.hurts": "'oy' vIlInHoD", "subtitles.entity.parrot.imitate.blaze": "qul tlhapragh lIl vIlInHoD", "subtitles.entity.parrot.imitate.bogged": "nobmeD yIQ lIl vIlInHoD", "subtitles.entity.parrot.imitate.breeze": "SuS tlhapragh lIl vIlInHoD", "subtitles.entity.parrot.imitate.creaking": "HaS vIlInHoD", "subtitles.entity.parrot.imitate.creeper": "SuqwIl tlhen vIlInHoD", "subtitles.entity.parrot.imitate.drowned": "yInbogh lom yIQ lIl vIlInHoD", "subtitles.entity.parrot.imitate.elder_guardian": "'avwI' qup lIl vIlInHoD", "subtitles.entity.parrot.imitate.ender_dragon": "lung'a' lIl vIlInHoD", "subtitles.entity.parrot.imitate.endermite": "endermite lIl vIlInHoD", "subtitles.entity.parrot.imitate.evoker": "reSwI' lIl vIlInHoD", "subtitles.entity.parrot.imitate.ghast": "SaQ vIlInHoD", "subtitles.entity.parrot.imitate.guardian": "'avwI' lIl vIlInHoD", "subtitles.entity.parrot.imitate.hoglin": "ghughugh vIlInHoD", "subtitles.entity.parrot.imitate.husk": "yub lIl vIlInHoD", "subtitles.entity.parrot.imitate.illusioner": "tojwI' 'IDnar pIn'a' lIl vIlInHoD", "subtitles.entity.parrot.imitate.magma_cube": "vaHbo' buq'Ir lIl vIlInHoD", "subtitles.entity.parrot.imitate.phantom": "ram toQ lIl vIlInHoD", "subtitles.entity.parrot.imitate.piglin": "Suy' ghot lIl vIlInHoD", "subtitles.entity.parrot.imitate.piglin_brute": "piglin mang lIl vIlInHoD", "subtitles.entity.parrot.imitate.pillager": "weHwI' lIl vIlInHoD", "subtitles.entity.parrot.imitate.ravager": "puywI' lIl vIlInHoD", "subtitles.entity.parrot.imitate.shulker": "shulker lIl vIlInHoD", "subtitles.entity.parrot.imitate.silverfish": "SIrlIy ghew lIl vIlInHoD", "subtitles.entity.parrot.imitate.skeleton": "nobmeD lIl vIlInHoD", "subtitles.entity.parrot.imitate.slime": "HuH lIl vIlInHoD", "subtitles.entity.parrot.imitate.spider": "voDchuch lIl vIlInHoD", "subtitles.entity.parrot.imitate.stray": "nobmeD taD lIl vIlInHoD", "subtitles.entity.parrot.imitate.vex": "nuQwI' lIl vIlInHoD", "subtitles.entity.parrot.imitate.vindicator": "'obmaQ ghajbogh weHwI' lIl vIlInHoD", "subtitles.entity.parrot.imitate.warden": "HubwI' lIl vIlInHoD", "subtitles.entity.parrot.imitate.witch": "'IDnar pIn'a' lIl vIlInHoD", "subtitles.entity.parrot.imitate.wither": "tlhapragh QaD lIl vIlInHoD", "subtitles.entity.parrot.imitate.wither_skeleton": "wither nobmeD lIl vIlInHoD", "subtitles.entity.parrot.imitate.zoglin": "ghughugh vIlInHoD", "subtitles.entity.parrot.imitate.zombie": "yInbogh lom lIl vIlInHoD", "subtitles.entity.parrot.imitate.zombie_villager": "yInbogh vengHom ghot lom lIl vIlInHoD", "subtitles.entity.phantom.ambient": "jach ram to<PERSON>", "subtitles.entity.phantom.bite": "chop ram toQ", "subtitles.entity.phantom.death": "<PERSON>gh ram toQ", "subtitles.entity.phantom.flap": "laq ram qa'", "subtitles.entity.phantom.hurt": "'oy' ram toQ", "subtitles.entity.phantom.swoop": "puv ram toQ", "subtitles.entity.pig.ambient": "Qog<PERSON>h Suy'", "subtitles.entity.pig.death": "<PERSON><PERSON>'", "subtitles.entity.pig.hurt": "'oy' Suy'", "subtitles.entity.pig.saddle": "ba'qIn jom", "subtitles.entity.piglin.admiring_item": "<PERSON><PERSON> piglin", "subtitles.entity.piglin.ambient": "<PERSON><PERSON><PERSON><PERSON> piglin", "subtitles.entity.piglin.angry": "<PERSON><PERSON><PERSON><PERSON> piglin <PERSON>e<PERSON>", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> piglin", "subtitles.entity.piglin.converted_to_zombified": "yInbogh piglin lom moj piglin", "subtitles.entity.piglin.death": "<PERSON><PERSON> piglin", "subtitles.entity.piglin.hurt": "'oy' piglin", "subtitles.entity.piglin.jealous": "<PERSON><PERSON><PERSON><PERSON> piglin ghal", "subtitles.entity.piglin.retreat": "<PERSON><PERSON>", "subtitles.entity.piglin.step": "yIt piglin", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON><PERSON> piglin mang", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON><PERSON><PERSON> piglin mang QeH", "subtitles.entity.piglin_brute.converted_to_zombified": "yInbogh piglin lom moj piglin mang", "subtitles.entity.piglin_brute.death": "<PERSON>gh piglin mang", "subtitles.entity.piglin_brute.hurt": "'oy' piglin mang", "subtitles.entity.piglin_brute.step": "yIt piglin mang", "subtitles.entity.pillager.ambient": "jat weHwI'", "subtitles.entity.pillager.celebrate": "Quch weHwI'", "subtitles.entity.pillager.death": "Hegh weHwI'", "subtitles.entity.pillager.hurt": "'oy' weHwI'", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON>'", "subtitles.entity.player.attack.knockback": "QachmeH yuv", "subtitles.entity.player.attack.strong": "QachmeH HoS", "subtitles.entity.player.attack.sweep": "QachmeH vaS", "subtitles.entity.player.attack.weak": "QachmeH puj", "subtitles.entity.player.burp": "ruq", "subtitles.entity.player.death": "<PERSON><PERSON>'", "subtitles.entity.player.freeze_hurt": "taDchoH QujwI'", "subtitles.entity.player.hurt": "'oy' QujwI'", "subtitles.entity.player.hurt_drown": "bIQDaq vIQtaH QujwI'", "subtitles.entity.player.hurt_on_fire": "meQtaH QujwI'", "subtitles.entity.player.levelup": "patlh veb chav <PERSON>ujw<PERSON>'", "subtitles.entity.player.teleport": "jol QujwI'", "subtitles.entity.polar_bear.ambient": "qIn chuch mIl'oD", "subtitles.entity.polar_bear.ambient_baby": "<PERSON>y chuch mIl'oD", "subtitles.entity.polar_bear.death": "Hegh chuch mIl'oD", "subtitles.entity.polar_bear.hurt": "'oy' chuch mIl'oD", "subtitles.entity.polar_bear.warning": "ghu<PERSON><PERSON><PERSON>' chuch mIl'oD", "subtitles.entity.potion.splash": "ghor bal", "subtitles.entity.potion.throw": "bal vo'lu'", "subtitles.entity.puffer_fish.blow_out": "lunHa' lunbogh ghotI'", "subtitles.entity.puffer_fish.blow_up": "lun lunbogh ghotI'", "subtitles.entity.puffer_fish.death": "<PERSON>gh lunbogh ghotI'", "subtitles.entity.puffer_fish.flop": "<PERSON>p lunbogh ghotI'", "subtitles.entity.puffer_fish.hurt": "'oy' lunbogh ghotI'", "subtitles.entity.puffer_fish.sting": "'ay' lun<PERSON>gh ghotI'", "subtitles.entity.rabbit.ambient": "HaS cheS", "subtitles.entity.rabbit.attack": "HIv cheS", "subtitles.entity.rabbit.death": "Hegh cheS", "subtitles.entity.rabbit.hurt": "'oy' cheS", "subtitles.entity.rabbit.jump": "Sup cheS", "subtitles.entity.ravager.ambient": "ghugh puywI'", "subtitles.entity.ravager.attack": "chop puywI'", "subtitles.entity.ravager.celebrate": "'ev puywI'", "subtitles.entity.ravager.death": "Hegh puywI'", "subtitles.entity.ravager.hurt": "'oy' puywI'", "subtitles.entity.ravager.roar": "jach puywI'", "subtitles.entity.ravager.step": "yIt puywI'", "subtitles.entity.ravager.stunned": "mot puywI'", "subtitles.entity.salmon.death": "<PERSON><PERSON>mon", "subtitles.entity.salmon.flop": "<PERSON><PERSON>", "subtitles.entity.salmon.hurt": "'oy' Se'mon", "subtitles.entity.sheep.ambient": "ghugh DI'raq", "subtitles.entity.sheep.death": "Hegh DI'raq", "subtitles.entity.sheep.hurt": "'oy' DI'raq", "subtitles.entity.shulker.ambient": "ghach shulker", "subtitles.entity.shulker.close": "So<PERSON>cho<PERSON> shulker", "subtitles.entity.shulker.death": "<PERSON><PERSON> shulker", "subtitles.entity.shulker.hurt": "'oy' shulker", "subtitles.entity.shulker.open": "poScho<PERSON> shulker", "subtitles.entity.shulker.shoot": "bach shulker", "subtitles.entity.shulker.teleport": "jol shulker", "subtitles.entity.shulker_bullet.hit": "jor shulker bach", "subtitles.entity.shulker_bullet.hurt": "ghor shulker bach", "subtitles.entity.silverfish.ambient": "SuqwIl tlhen SIrlIy ghew", "subtitles.entity.silverfish.death": "<PERSON><PERSON>rl<PERSON>y ghew", "subtitles.entity.silverfish.hurt": "'oy' SIrl<PERSON>y ghew", "subtitles.entity.skeleton.ambient": "chuS nobmeD", "subtitles.entity.skeleton.converted_to_stray": "nobmeD taD moj nobmeD", "subtitles.entity.skeleton.death": "Hegh nobmeD", "subtitles.entity.skeleton.hurt": "'oy' nobmeD", "subtitles.entity.skeleton.shoot": "bach nobmeD", "subtitles.entity.skeleton_horse.ambient": "ghugh nobmeD Sargh", "subtitles.entity.skeleton_horse.death": "Hegh nobmeD Sargh", "subtitles.entity.skeleton_horse.hurt": "'oy' nobmeD Sargh", "subtitles.entity.skeleton_horse.jump_water": "Sup nobmeD Sargh", "subtitles.entity.skeleton_horse.swim": "Qal nobmeD Sargh", "subtitles.entity.slime.attack": "HIv HuH", "subtitles.entity.slime.death": "<PERSON><PERSON>", "subtitles.entity.slime.hurt": "'oy' HuH", "subtitles.entity.slime.squish": "Sup <PERSON>", "subtitles.entity.sniffer.death": "Hegh larghwI'", "subtitles.entity.sniffer.digging": "tlhan larghwI'", "subtitles.entity.sniffer.digging_stop": "Hu' larghwI'", "subtitles.entity.sniffer.drop_seed": "raS'IS chagh larghwI'", "subtitles.entity.sniffer.eat": "Sop larghwI'", "subtitles.entity.sniffer.egg_crack": "rIS larghwI' QIm", "subtitles.entity.sniffer.egg_hatch": "pel'a<PERSON><PERSON><PERSON> ghor larghwI'", "subtitles.entity.sniffer.happy": "Quch larghwI'", "subtitles.entity.sniffer.hurt": "'oy' larghwI'", "subtitles.entity.sniffer.idle": "qIn larghwI'", "subtitles.entity.sniffer.scenting": "pur larghwI'", "subtitles.entity.sniffer.searching": "nej larghwI'", "subtitles.entity.sniffer.sniffing": "largh larghwI'", "subtitles.entity.sniffer.step": "yIt larghwI'", "subtitles.entity.snow_golem.death": "<PERSON>gh chal chuch velqa' nuv", "subtitles.entity.snow_golem.hurt": "'oy' chal chuch velqa' nuv", "subtitles.entity.snowball.throw": "puv chal chuch moQ", "subtitles.entity.spider.ambient": "SuqwIl tlhen voDchuch", "subtitles.entity.spider.death": "Hegh voDchuch", "subtitles.entity.spider.hurt": "'oy' vo<PERSON><PERSON>ch", "subtitles.entity.squid.ambient": "<PERSON><PERSON>", "subtitles.entity.squid.death": "<PERSON><PERSON> chey<PERSON>", "subtitles.entity.squid.hurt": "'oy' cheyIS", "subtitles.entity.squid.squirt": "rItlh tuy' cheyIS", "subtitles.entity.stray.ambient": "chuS nobmeD taD", "subtitles.entity.stray.death": "Hegh nobmeD taD", "subtitles.entity.stray.hurt": "'oy' nobmeD taD", "subtitles.entity.strider.death": "Hegh yItwI'", "subtitles.entity.strider.eat": "Sop yItwI'", "subtitles.entity.strider.happy": "ghugh yItwI'", "subtitles.entity.strider.hurt": "'oy' yItwI'", "subtitles.entity.strider.idle": "ghugh yItwI' Quch", "subtitles.entity.strider.retreat": "HeD yItwI'", "subtitles.entity.tadpole.death": "<PERSON>gh yemghaw", "subtitles.entity.tadpole.flop": "<PERSON><PERSON> ye<PERSON>", "subtitles.entity.tadpole.grow_up": "nencho<PERSON> yemghaw", "subtitles.entity.tadpole.hurt": "'oy' yemghaw", "subtitles.entity.tnt.primed": "chuS jorwI'", "subtitles.entity.tropical_fish.death": "Hegh bIQ ghun ghotI'", "subtitles.entity.tropical_fish.flop": "Sup bIQ ghun ghotI'", "subtitles.entity.tropical_fish.hurt": "'oy' bIQ ghun ghotI'", "subtitles.entity.turtle.ambient_land": "ghugh la'SIv", "subtitles.entity.turtle.death": "<PERSON><PERSON> la'SIv", "subtitles.entity.turtle.death_baby": "<PERSON>gh la'SIv ghu", "subtitles.entity.turtle.egg_break": "ghor la'SIv QIm", "subtitles.entity.turtle.egg_crack": "rIS la'SIv QIm", "subtitles.entity.turtle.egg_hatch": "pel'a<PERSON><PERSON><PERSON> ghor la'SIv", "subtitles.entity.turtle.hurt": "'oy' la'SIv", "subtitles.entity.turtle.hurt_baby": "'oy' la'SIv ghu", "subtitles.entity.turtle.lay_egg": "QIm narghmoH la'SIv", "subtitles.entity.turtle.shamble": "yIt la'SIv", "subtitles.entity.turtle.shamble_baby": "'eD la'SIv ghu", "subtitles.entity.turtle.swim": "Qal la'SIv", "subtitles.entity.vex.ambient": "nuQ nuQwI'", "subtitles.entity.vex.charge": "jach nuQwI'", "subtitles.entity.vex.death": "Hegh nuQwI'", "subtitles.entity.vex.hurt": "'oy' nuQwI'", "subtitles.entity.villager.ambient": "jat vengHom ghot", "subtitles.entity.villager.celebrate": "jach veng<PERSON>om ghot <PERSON>", "subtitles.entity.villager.death": "Hegh vengHom ghot", "subtitles.entity.villager.hurt": "'oy' vengHom ghot", "subtitles.entity.villager.no": "<PERSON>och vengHom ghot", "subtitles.entity.villager.trade": "mech vengHom ngan", "subtitles.entity.villager.work_armorer": "vum may' Sut chenmoHwI'", "subtitles.entity.villager.work_butcher": "vum Ha'DIbaH HoHwI'", "subtitles.entity.villager.work_cartographer": "vum pu'jIn chenmoHwI'", "subtitles.entity.villager.work_cleric": "vum maqlegh", "subtitles.entity.villager.work_farmer": "vum wIjwI'", "subtitles.entity.villager.work_fisherman": "vum ghotI' wamwI'", "subtitles.entity.villager.work_fletcher": "vum naQjejHom chenmoHwI'", "subtitles.entity.villager.work_leatherworker": "vum qo'rIn DIr chenmoHwI'", "subtitles.entity.villager.work_librarian": "vum paq nojwI'", "subtitles.entity.villager.work_mason": "vum nagh chenmoHwI'", "subtitles.entity.villager.work_shepherd": "vum bolmaq DevwI'", "subtitles.entity.villager.work_toolsmith": "vum SommI' chenmoHwI'", "subtitles.entity.villager.work_weaponsmith": "vum nuH chenmoHwI'", "subtitles.entity.villager.yes": "QochHa' vengHom ghot", "subtitles.entity.vindicator.ambient": "jat 'ob<PERSON><PERSON> ghajbogh weHwI'", "subtitles.entity.vindicator.celebrate": "ghu<PERSON> 'o<PERSON><PERSON><PERSON> ghaj<PERSON>gh weHwI' Quch", "subtitles.entity.vindicator.death": "<PERSON><PERSON> 'ob<PERSON><PERSON> ghajbogh weHwI'", "subtitles.entity.vindicator.hurt": "'oy' 'obma<PERSON> ghajbogh weHwI'", "subtitles.entity.wandering_trader.ambient": "jat yo<PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.disappeared": "ngab yo<PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.drink_milk": "nIm tlhu<PERSON>h yor<PERSON>", "subtitles.entity.wandering_trader.drink_potion": "'<PERSON>nar taS tlhu<PERSON>h yor<PERSON>", "subtitles.entity.wandering_trader.hurt": "'oy' yo<PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.reappeared": "nargh yo<PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.trade": "mech yor<PERSON><PERSON>", "subtitles.entity.wandering_trader.yes": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.agitated": "qIn HubwI' QeH", "subtitles.entity.warden.ambient": "vIng HubwI'", "subtitles.entity.warden.angry": "QeH HubwI'", "subtitles.entity.warden.attack_impact": "mup HubwI'", "subtitles.entity.warden.death": "<PERSON><PERSON>w<PERSON>'", "subtitles.entity.warden.dig": "tlhan HubwI'", "subtitles.entity.warden.emerge": "yav mej HubwI'", "subtitles.entity.warden.heartbeat": "joq HubwI' tIq", "subtitles.entity.warden.hurt": "'oy' HubwI'", "subtitles.entity.warden.listening": "vay' tu' HubwI'", "subtitles.entity.warden.listening_angry": "vay' tu' HubwI' QeH", "subtitles.entity.warden.nearby_close": "ghoS HubwI'", "subtitles.entity.warden.nearby_closer": "Duv <PERSON>w<PERSON>'", "subtitles.entity.warden.nearby_closest": "chol HubwI'", "subtitles.entity.warden.roar": "jach <PERSON>w<PERSON>'", "subtitles.entity.warden.sniff": "largh HubwI'", "subtitles.entity.warden.sonic_boom": "bey <PERSON>' jach <PERSON>'", "subtitles.entity.warden.sonic_charge": "yov HubwI'", "subtitles.entity.warden.step": "yIt HubwI'", "subtitles.entity.warden.tendril_clicks": "rIS HubwI' 'oQqar", "subtitles.entity.wind_charge.throw": "pum <PERSON> bach", "subtitles.entity.wind_charge.wind_burst": "jor <PERSON> bach", "subtitles.entity.witch.ambient": "Hagh 'IDnar pIn'a'", "subtitles.entity.witch.celebrate": "Quch 'IDnar pIn'a'", "subtitles.entity.witch.death": "<PERSON><PERSON> '<PERSON>nar pIn'a'", "subtitles.entity.witch.drink": "tlhutlh '<PERSON>nar pIn'a'", "subtitles.entity.witch.hurt": "'oy' '<PERSON>nar pIn'a'", "subtitles.entity.witch.throw": "'IDnar taS vo' 'IDnar pIn'a'", "subtitles.entity.wither.ambient": "<PERSON><PERSON><PERSON> wither", "subtitles.entity.wither.death": "<PERSON><PERSON> wither", "subtitles.entity.wither.hurt": "'oy' wither", "subtitles.entity.wither.shoot": "HIv wither", "subtitles.entity.wither.spawn": "wither rItlu'", "subtitles.entity.wither_skeleton.ambient": "chuS wither nobmeD", "subtitles.entity.wither_skeleton.death": "Hegh wither nobmeD", "subtitles.entity.wither_skeleton.hurt": "'oy' wither nobmeD", "subtitles.entity.wolf.ambient": "tlhov ngavyaw'", "subtitles.entity.wolf.bark": "ghugh ngavyaw'", "subtitles.entity.wolf.death": "<PERSON>gh ngavyaw'", "subtitles.entity.wolf.growl": "ghu<PERSON><PERSON> ngavyaw'", "subtitles.entity.wolf.hurt": "'oy' ngavyaw'", "subtitles.entity.wolf.pant": "tlhov ngavyaw'", "subtitles.entity.wolf.shake": "tav'egh ngavyaw'", "subtitles.entity.wolf.whine": "vIng ngavyaw'", "subtitles.entity.zoglin.ambient": "g<PERSON><PERSON><PERSON> zoglin", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.attack": "HIv zoglin", "subtitles.entity.zoglin.death": "<PERSON><PERSON> zoglin", "subtitles.entity.zoglin.hurt": "'oy' zoglin", "subtitles.entity.zoglin.step": "y<PERSON>t zoglin", "subtitles.entity.zombie.ambient": "qIn yInbogh lom", "subtitles.entity.zombie.attack_wooden_door": "jel lojm<PERSON>t", "subtitles.entity.zombie.break_wooden_door": "lojmIt ghorlu'", "subtitles.entity.zombie.converted_to_drowned": "yIQchoH yInbogh lom", "subtitles.entity.zombie.death": "Hegh yInbogh lom", "subtitles.entity.zombie.destroy_egg": "la'SIv QIm taplu'", "subtitles.entity.zombie.hurt": "'oy' yInbogh lom", "subtitles.entity.zombie.infect": "ngej yInbogh lom", "subtitles.entity.zombie_horse.ambient": "jach yInbogh lom Sargh", "subtitles.entity.zombie_horse.death": "Hegh yInbogh Sargh lom", "subtitles.entity.zombie_horse.hurt": "'oy' yInbogh lom Sargh", "subtitles.entity.zombie_villager.ambient": "qIn yInbogh vengHom ghot lom", "subtitles.entity.zombie_villager.converted": "yInbogh vengHom ghot lom vorlu'", "subtitles.entity.zombie_villager.cure": "yInbogh vengHom ghot lom rachchoHlu'", "subtitles.entity.zombie_villager.death": "Hegh yInbogh vengHom ghot lom", "subtitles.entity.zombie_villager.hurt": "'oy' yInbogh vengHom ghot lom", "subtitles.entity.zombified_piglin.ambient": "ghugh yInbogh piglin lom", "subtitles.entity.zombified_piglin.angry": "ghugh yInbogh piglin lom QeH", "subtitles.entity.zombified_piglin.death": "Hegh yInbogh piglin lom", "subtitles.entity.zombified_piglin.hurt": "'oy' yInbogh piglin lom", "subtitles.event.mob_effect.bad_omen": "QapchoH maQmIgh", "subtitles.event.mob_effect.raid_omen": "lIb yot Sum", "subtitles.event.mob_effect.trial_omen": "lIb maQmIgh qaD Sum", "subtitles.event.raid.horn": "ma<PERSON><PERSON><PERSON><PERSON> gheb rIllu'", "subtitles.item.armor.equip": "may' <PERSON><PERSON> jom", "subtitles.item.armor.equip_chain": "chuS mIr may' <PERSON>t", "subtitles.item.armor.equip_diamond": "chanmon may' Sut tuQmoHlu'", "subtitles.item.armor.equip_elytra": "'e'qIn tlhen telDu'qoq", "subtitles.item.armor.equip_gold": "chuS qol'om may' Sut", "subtitles.item.armor.equip_iron": "chuS 'u<PERSON><PERSON><PERSON> may' Sut", "subtitles.item.armor.equip_leather": "'e'qIn tlhen qo'rIn DIr may' Sut", "subtitles.item.armor.equip_netherite": "chuS netherite may' Sut", "subtitles.item.armor.equip_turtle": "chuS la'SIv nagh DIr", "subtitles.item.armor.equip_wolf": "ngavyaw' may' Sut tuQ", "subtitles.item.armor.unequip_wolf": "ngavyaw' may' Sut tuQHa'", "subtitles.item.axe.scrape": "tey 'obma<PERSON>", "subtitles.item.axe.strip": "Sor DIr teq 'obmaQ", "subtitles.item.axe.wax_off": "req teq 'obmaQ", "subtitles.item.bone_meal.use": "Hom Say'qIS lo'lu'", "subtitles.item.book.page_turn": "'e'qIn tlhen tenwal", "subtitles.item.book.put": "petben tlhen paq", "subtitles.item.bottle.empty": "Bal chIm", "subtitles.item.bottle.fill": "bal teblu'", "subtitles.item.brush.brushing.generic": "yach", "subtitles.item.brush.brushing.gravel": "yetmoS yach", "subtitles.item.brush.brushing.gravel.complete": "yetmoS yach 'e' rIn", "subtitles.item.brush.brushing.sand": "Do'ol yach", "subtitles.item.brush.brushing.sand.complete": "Do'ol yach 'e' rIn", "subtitles.item.bucket.empty": "HaySIn chImmoHlu'", "subtitles.item.bucket.fill": "HaySIn teblu'", "subtitles.item.bucket.fill_axolotl": "'evta' tlhurlu'", "subtitles.item.bucket.fill_fish": "g<PERSON><PERSON>' jonlu'", "subtitles.item.bucket.fill_tadpole": "yem<PERSON><PERSON> jonlu'", "subtitles.item.bundle.drop_contents": "<PERSON>j tlhum chim<PERSON>'", "subtitles.item.bundle.insert": "<PERSON><PERSON> tlhum 'el vay'", "subtitles.item.bundle.insert_fail": "buy' <PERSON><PERSON> buq", "subtitles.item.bundle.remove_one": "<PERSON>j tlhumvo' mej vay'", "subtitles.item.chorus_fruit.teleport": "jol QujwI'", "subtitles.item.crop.plant": "San'emDer pochlu'", "subtitles.item.crossbow.charge": "jo' <PERSON><PERSON><PERSON><PERSON>' Hurlu'", "subtitles.item.crossbow.hit": "mup naQjejHom", "subtitles.item.crossbow.load": "ghuS jo' HurwI'", "subtitles.item.crossbow.shoot": "bach jo' HurwI'", "subtitles.item.dye.use": "nguvmoH rItlh", "subtitles.item.elytra.flying": "SuS", "subtitles.item.firecharge.use": "qul moQ baHlu'", "subtitles.item.flintandsteel.use": "rIS qul chenmoHwI'", "subtitles.item.glow_ink_sac.use": "wewbogh rItlh 'enDeq taplu'", "subtitles.item.goat_horn.play": "gha'cher pu' rIllu'", "subtitles.item.hoe.till": "puH quH rutneD", "subtitles.item.honey_bottle.drink": "DayqIr tlhutlhlu'", "subtitles.item.honeycomb.wax_on": "req y<PERSON>lan", "subtitles.item.horse_armor.unequip": "<PERSON><PERSON><PERSON> may' Sut jomHa'", "subtitles.item.ink_sac.use": "rItlh 'enDeq taplu'", "subtitles.item.lead.break": "rIjmeH tlhegh ghor", "subtitles.item.lead.tied": "rIjmeH tlhegh bagh", "subtitles.item.lead.untied": "rIjmeH tlhegh baghHa'", "subtitles.item.llama_carpet.unequip": "tlhIm jomHa'", "subtitles.item.lodestone_compass.lock": "peQna<PERSON> 'uch peQnagh SInan", "subtitles.item.mace.smash_air": "tap ghanjaq", "subtitles.item.mace.smash_ground": "tap ghanjaq", "subtitles.item.nether_wart.plant": "San'emDer pochlu'", "subtitles.item.ominous_bottle.dispose": "bal ghorlu'", "subtitles.item.saddle.unequip": "ba'qIn jomHa'", "subtitles.item.shears.shear": "rIS cha''etlh pe'wI''a'", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "QIH bot yoD", "subtitles.item.shovel.flatten": "beQmoH ghevjur", "subtitles.item.spyglass.stop_using": "mach'eghmoH Hov tut", "subtitles.item.spyglass.use": "<PERSON>ch Hov tut", "subtitles.item.totem.use": "HewHom chu'lu'", "subtitles.item.trident.hit": "<PERSON><PERSON> ch<PERSON>", "subtitles.item.trident.hit_ground": "wal chonnaQ", "subtitles.item.trident.return": "tatlh'etlh chonnaQ", "subtitles.item.trident.riptide": "puv chonnaQ", "subtitles.item.trident.throw": "qIp chonnaQ", "subtitles.item.trident.thunder": "tuD chonna<PERSON>", "subtitles.item.wolf_armor.break": "ngavyaw' may' Sut ghorchu'lu'", "subtitles.item.wolf_armor.crack": "ngavyaw' may' <PERSON>t g<PERSON>'", "subtitles.item.wolf_armor.damage": "ngavyaw' may' Sut QIHlu'", "subtitles.item.wolf_armor.repair": "ngavyaw' may' Sut tI'lu'", "subtitles.particle.soul_escape": "nargh qa'", "subtitles.ui.cartography_table.take_result": "pu'jIn ghItlh", "subtitles.ui.hud.bubble_pop": "ngab tlhu<PERSON> b<PERSON>'<PERSON>q", "subtitles.ui.loom.take_result": "nIqmeH qal'aq lo'lu'", "subtitles.ui.stonecutter.take_result": "nagh pe'wI' lol'lu'", "subtitles.weather.rain": "SIS", "symlink_warning.message": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.pack": "Loading packs with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.more_info": "latlh De'", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.title.pack": "Added pack(s) contain(s) symbolic links", "symlink_warning.title.world": "The world folder contains symbolic links", "team.collision.always": "reH", "team.collision.never": "not", "team.collision.pushOtherTeams": "latlh ghom num", "team.collision.pushOwnTeam": "ghomlIj num", "team.notFound": "ghom Sov<PERSON>'lu'bogh: %s", "team.visibility.always": "reH", "team.visibility.hideForOtherTeams": "latlh ghommeH So'", "team.visibility.hideForOwnTeam": "ghomlIjvaD So'", "team.visibility.never": "not", "telemetry.event.advancement_made.description": "QujwI' Ser pat wIya<PERSON><PERSON> 'ej wIDubmeH, lI' chav Suqme<PERSON> ghu' wIyajmeH De'.", "telemetry.event.advancement_made.title": "chav chav", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Quj lI'meH poH", "telemetry.event.optional": "%s (Optional)", "telemetry.event.optional.disabled": "%s (poQbe') - Qotlhlu'", "telemetry.event.performance_metrics.description": "Knowing the overall performance profile of Minecraft helps us tune and optimize the game for a wide range of machine specifications and operating systems. \nGame version is included to help us compare the performance profile for new versions of Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (Required)", "telemetry.event.world_load_times.description": "It's important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "qo' lI'meH poH", "telemetry.event.world_loaded.description": "Knowing how players play Minecraft (such as Game Mode, client or server modded, and game version) allows us to focus game updates to improve the areas that players care about most.\nThe World Loaded event is paired with the World Unloaded event to calculate how long the play session has lasted.", "telemetry.event.world_loaded.title": "qo' lI'ta'", "telemetry.event.world_unloaded.description": "This event is paired with the World Loaded event to calculate how long the world session has lasted.\nThe duration (in seconds and ticks) is measured when a world session has ended (quitting to title, disconnecting from a server).", "telemetry.event.world_unloaded.title": "qo' lI'Ha'ta'", "telemetry.property.advancement_game_time.title": "<PERSON><PERSON><PERSON> poH (mIwHom)", "telemetry.property.advancement_id.title": "chav pong", "telemetry.property.client_id.title": "De'wI'vam ngu'meH mI'", "telemetry.property.client_modded.title": "nIqHomvam choHlu'", "telemetry.property.dedicated_memory_kb.title": "Dedicated Memory (kB)", "telemetry.property.event_timestamp_utc.title": "Event Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Frame Rate Samples (FPS)", "telemetry.property.game_mode.title": "<PERSON>uj lo'", "telemetry.property.game_version.title": "<PERSON><PERSON><PERSON>", "telemetry.property.launcher_name.title": "Launcher Name", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "minecraft Quj poH ngu'meH mI'", "telemetry.property.new_world.title": "qo' chu'", "telemetry.property.number_of_samples.title": "Sample Count", "telemetry.property.operating_system.title": "De'wI' pat", "telemetry.property.opt_in.title": "Opt-In", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "mIllogh chuq", "telemetry.property.render_time_samples.title": "Render Time Samples", "telemetry.property.seconds_since_load.title": "Time Since Load (Seconds)", "telemetry.property.server_modded.title": "Server Modded", "telemetry.property.server_type.title": "turw<PERSON>' <PERSON>gh", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "lo'wI' ngu'meH mI'", "telemetry.property.world_load_time_ms.title": "World Load Time (Milliseconds)", "telemetry.property.world_session_id.title": "World Session ID", "telemetry_info.button.give_feedback": "vuDvaD ja'", "telemetry_info.button.privacy_statement": "So''eghghach ghItlh", "telemetry_info.button.show_data": "De'wIj cha'", "telemetry_info.opt_in.description": "noch Hop De' poQbe'lu'bogh vIngeH 'e' vIghIb", "telemetry_info.property_title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "telemetry_info.screen.description": "minecraft wIDubmeH lI' De'vam, QujwI'ma'pu'vaD ngoQmey potlh 'angmo'.\nminecraft wIDubmeH Qu'vaD DaboQmeH, latlh vuD DangeHlaH je.", "telemetry_info.screen.title": "noch Hop De' boS", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "bu<PERSON><PERSON><PERSON><PERSON> waS: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "QIn:", "test_block.mode.accept": "laj", "test_block.mode.fail": "luj", "test_block.mode.log": "qon", "test_block.mode.start": "tagh", "test_block.mode_info.accept": "lajmeH lo' - qaD ('ay') Qapla' laj", "test_block.mode_info.fail": "lujmeH lo' - qaD luj", "test_block.mode_info.log": "qonmeH lo' - QIn qon", "test_block.mode_info.start": "taghm<PERSON><PERSON> lo' - qa<PERSON> tagh", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "qach ngeq", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "qaD ngu'meH mI' waS", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Dolmey:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "wejmaH cha' San'on De'wI' jem; 'op pIq QujmeH javmaH loS San'on De'wI' Dalo'nIS, vaj ngugh QujlIj botlaH De'wI'lIj!", "title.32bit.deprecation.realms": "tugh javmaH loS San'on poQ minecraft; realms QujlIj bot wanI'vam. <PERSON>, chIch realms qav'ap DabupnIS.", "title.32bit.deprecation.realms.check": "QInvam yI'angqa'Qo'", "title.32bit.deprecation.realms.header": "wejmaH cha' San'on De'wI' jem", "title.credits": "Hoch SeH Mojang AB. yIpeSQo'!", "title.multiplayer.disabled": "Multiplayer is disabled. Please check your Microsoft account settings.", "title.multiplayer.disabled.banned.name": "<PERSON>uj lIn<PERSON><PERSON>b<PERSON><PERSON><PERSON> ponglIj DachoHnIS", "title.multiplayer.disabled.banned.permanent": "Your account is permanently suspended from online play", "title.multiplayer.disabled.banned.temporary": "Your account is temporarily suspended from online play", "title.multiplayer.lan": "QujwI' law' (<PERSON><PERSON><PERSON><PERSON><PERSON>' <PERSON><PERSON>)", "title.multiplayer.other": "QujwI' law' (turwI' tlhab)", "title.multiplayer.realms": "QujwI' law' (realms)", "title.singleplayer": "wa' QujwI'", "translation.test.args": "%s %s", "translation.test.complex": "moHaq %s%2$s, %s jatlhqa' %1$s 'ej %s je %1$s jatlhqa'!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "qavan, %", "translation.test.invalid2": "qavan, %s", "translation.test.none": "qo' vIvan!", "translation.test.world": "qo'", "trim_material.minecraft.amethyst": "choSom nguv Hap", "trim_material.minecraft.copper": "<PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.diamond": "<PERSON>an<PERSON>", "trim_material.minecraft.emerald": "<PERSON><PERSON>", "trim_material.minecraft.gold": "qol'om <PERSON>p", "trim_material.minecraft.iron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim_material.minecraft.lapis": "Doltop Hap", "trim_material.minecraft.netherite": "netherite Hap", "trim_material.minecraft.quartz": "choSom Hap", "trim_material.minecraft.redstone": "redstone Hap", "trim_material.minecraft.resin": "<PERSON><PERSON><PERSON><PERSON>", "trim_pattern.minecraft.bolt": "Hut'In may' <PERSON>t Ha<PERSON>re<PERSON>", "trim_pattern.minecraft.coast": "<PERSON><PERSON> <PERSON>' <PERSON><PERSON>", "trim_pattern.minecraft.dune": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> may' <PERSON>t Ha<PERSON>reH", "trim_pattern.minecraft.eye": "mIn may' Sut HaSreH", "trim_pattern.minecraft.flow": "Dem'ot may' <PERSON><PERSON>re<PERSON>", "trim_pattern.minecraft.host": "ma'wI' may' <PERSON><PERSON>", "trim_pattern.minecraft.raiser": "pepwI' may' <PERSON>t Ha<PERSON>re<PERSON>", "trim_pattern.minecraft.rib": "joQ may' <PERSON><PERSON>", "trim_pattern.minecraft.sentry": "'avwI' may' <PERSON><PERSON> Ha<PERSON>re<PERSON>", "trim_pattern.minecraft.shaper": "chenmoHwI' may' <PERSON>t Ha<PERSON>reH", "trim_pattern.minecraft.silence": "tam may' <PERSON><PERSON>re<PERSON>", "trim_pattern.minecraft.snout": "ghIch may' Sut HaSreH", "trim_pattern.minecraft.spire": "chalqa<PERSON> may' <PERSON><PERSON> Ha<PERSON>re<PERSON>", "trim_pattern.minecraft.tide": "vI'Ir may' <PERSON>t HaSreH", "trim_pattern.minecraft.vex": "nuQ may' <PERSON>t HaSreH", "trim_pattern.minecraft.ward": "'av may' <PERSON><PERSON>", "trim_pattern.minecraft.wayfinder": "chIjwI' may' <PERSON>t HaSreH", "trim_pattern.minecraft.wild": "tlhab may' <PERSON><PERSON> HaSreH", "tutorial.bundleInsert.description": "<PERSON>h <PERSON>, nIH leQ yI'uy", "tutorial.bundleInsert.title": "<PERSON>j tlhum y<PERSON>lo'", "tutorial.craft_planks.description": "DuboQlaH mIw paq", "tutorial.craft_planks.title": "So<PERSON> '<PERSON><PERSON><PERSON>mey tIchenmoH", "tutorial.find_tree.description": "<PERSON><PERSON> <PERSON><PERSON>H, yIqIp", "tutorial.find_tree.title": "<PERSON>r <PERSON><PERSON>", "tutorial.look.description": "QujwI' tlhe' 'eQway'", "tutorial.look.title": "qo' <PERSON><PERSON><PERSON><PERSON>", "tutorial.move.description": "bISupmeH %s yI'uy", "tutorial.move.title": "bIvIHmeH %s, %s, %s, %s je yI'uy", "tutorial.open_inventory.description": "%s yI'uy", "tutorial.open_inventory.title": "buqmey tIpoSmoH", "tutorial.punch_tree.description": "%s yI'uytaH", "tutorial.punch_tree.title": "Sor yI<PERSON><PERSON>'", "tutorial.socialInteractions.description": "poSmoHmeH %s yI'uy", "tutorial.socialInteractions.title": "<PERSON><PERSON><PERSON><PERSON>", "upgrade.minecraft.netherite_upgrade": "netherite DubwI'"}