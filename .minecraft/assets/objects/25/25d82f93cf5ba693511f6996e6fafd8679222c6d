{"accessibility.onboarding.accessibility.button": "Stsineltak ochebal...", "accessibility.onboarding.screen.narrator": "Net'o Enter sventa xatsan li j-al lo'ilajele", "accessibility.onboarding.screen.title": "¡Ochan me ta Minecraft! \n\n¿Mi xak'an ava'i xatsan li j-al lo'ilajele o xavu'lan li stsineltak ochebale?", "addServer.add": "Pasbil", "addServer.enterIp": "<PERSON><PERSON> servilor", "addServer.enterName": "<PERSON><PERSON> servilor", "addServer.resourcePack": "Srekursotak servilor", "addServer.resourcePack.disabled": "<PERSON><PERSON><PERSON>", "addServer.resourcePack.enabled": "Tsakal", "addServer.resourcePack.prompt": "<PERSON><PERSON><PERSON><PERSON>", "addServer.title": "<PERSON><PERSON> servil<PERSON>", "advMode.command": "Mantal konsola", "advMode.mode": "<PERSON><PERSON>", "advMode.mode.auto": "<PERSON><PERSON>pasel", "advMode.mode.autoexec.bat": "Ts'akal o", "advMode.mode.conditional": "Kontisional", "advMode.mode.redstone": "<PERSON>mpul<PERSON>", "advMode.mode.redstoneTriggered": "Sk'an redstone", "advMode.mode.sequence": "<PERSON><PERSON>", "advMode.mode.unconditional": "Mu kontisional", "advMode.notAllowed": "Sk'an ch-elan j<PERSON> xchi'uk manera jpasvanej", "advMode.notEnabled": "Mu tsakalik ta servilor li'e li kubo mantaletike", "advMode.previousOutput": "<PERSON>tal ta pat", "advMode.setCommand": "<PERSON><PERSON><PERSON> jun mantal konsola sventa li kuboe", "advMode.setCommand.success": "Mantal komesbil: %s", "advMode.trackOutput": "Tsk'el li k'usi lok'e", "advMode.triggering": "<PERSON><PERSON> t<PERSON>an", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Mu ojtikinbil porokreso: %s", "advancements.adventure.adventuring_time.description": "Tao skotol li osiletike", "advancements.adventure.adventuring_time.title": "Ora aventura", "advancements.adventure.arbalistic.description": "Milo jo'ob k'ajomal chanuletik xchi'uk jun srisparo alkoltak'in", "advancements.adventure.arbalistic.title": "Alkoltak'inal", "advancements.adventure.avoid_vibration.description": "T'inil nopajan ta jbej sensol Sculk o jkot jchabich'en sventa mu cha'iot", "advancements.adventure.avoid_vibration.title": "Riskresion 100", "advancements.adventure.blowback.description": "Milo jkot j-ik' xchi'uk jbej sbala", "advancements.adventure.blowback.title": "Sutel ik'", "advancements.adventure.brush_armadillo.description": "Sa'o smik'altak jkot ib xchi'uk jun ch'ulobil", "advancements.adventure.brush_armadillo.title": "¿Mi ja' smail o smik'al?", "advancements.adventure.bullseye.description": "<PERSON><PERSON> li yut jbej rianae ta 30 metro snamal", "advancements.adventure.bullseye.title": "¡Sat Xik! ", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Paso jboch luchbil bin xchi'uk chank'os seramika", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "Nopajan ta jbej meltsanobil k'alal tsmeltsan jbej meltsanobil", "advancements.adventure.crafters_crafting_crafters.title": "Tsmeltsanik meltsanobiletik li meltsanobiletike", "advancements.adventure.fall_from_world_height.description": "Bajan li ta sjol balumile (li sts'ak snatilal va'anel) k'alal to yut xchi'uk kolan", "advancements.adventure.fall_from_world_height.title": "Ch'enetik xchi'uk vitsetik", "advancements.adventure.heart_transplanter.description": "Ak'o jun yo'onton jch'ak'ak'etajel ta lekil chol ta o'lol oxbej chumante'el sakpak'an tulan", "advancements.adventure.heart_transplanter.title": "<PERSON><PERSON>'ex<PERSON><PERSON>", "advancements.adventure.hero_of_the_village.description": "Lek pojo jun bik'it lum yu'un jun invasion", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON>e yu'un bik'it lum", "advancements.adventure.honey_block_slide.description": "Bitan ta jbej kubo ajapom sventa chikot", "advancements.adventure.honey_block_slide.title": "Noch'och' ora", "advancements.adventure.kill_a_mob.description": "<PERSON> li pukujil chanuletike", "advancements.adventure.kill_a_mob.title": "Jsa' monstro", "advancements.adventure.kill_all_mobs.description": "<PERSON> jujun pukujil monstro", "advancements.adventure.kill_all_mobs.title": "Milatik li monstroetike", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Milo jkot chanul ta nopol jbej jpas kanalisar sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "Ch-epaj", "advancements.adventure.lighten_up.description": "Jo<PERSON>'o jbej joko k'anal k'unil tak'in xchi'uk jtel ek'el sventa chapas masuk nop'ol", "advancements.adventure.lighten_up.title": "Ak'o xnop'op'et", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Pojo jun jbik'it lum ta jun anjel mu xchi'uk tslikes jun kolemk'ok'", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Jsom sopretension", "advancements.adventure.minecraft_trials_edition.description": "Ochan ta jk'ol tsatsalil", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Li Tsatsalile", "advancements.adventure.ol_betsy.description": "<PERSON>neso jun alkoltak'in", "advancements.adventure.ol_betsy.title": "Chavojtikin xa", "advancements.adventure.overoverkill.description": "Yaintaso 50 o'ontonal xchi'uk junuk smajel li mase", "advancements.adventure.overoverkill.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.play_jukebox_in_meadows.description": "Ak'o kuxuluk li yaxaltike xchi'uk li snuk'ilal jbej jvabajesvanej", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>al sonal", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "K'elo li senyail sju'el jbej kajanab xchi'uk jbej jko'oltasvanej", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "¡Ja' ju'el li ojtikinele!", "advancements.adventure.revaulting.description": "<PERSON><PERSON> jbej chopol arkon xchi'uk jun chopol yavi' tsatsalil", "advancements.adventure.revaulting.title": "Tsatsal arkon", "advancements.adventure.root.description": "<PERSON><PERSON><PERSON><PERSON>, xanbal xchi'uk k'op", "advancements.adventure.root.title": "Aventura", "advancements.adventure.salvage_sherd.description": "<PERSON>'ulo jbej jelel kubo sventa chasa' jk'os seramika", "advancements.adventure.salvage_sherd.title": "Yakal chich' ta muk' li k'usi k'ot ta pasele", "advancements.adventure.shoot_arrow.description": "<PERSON><PERSON> k'usi xchi'uk jun yolob", "advancements.adventure.shoot_arrow.title": "Lek paso apuntar", "advancements.adventure.sleep_in_bed.description": "<PERSON><PERSON><PERSON> sventa chajel li avina<PERSON>be", "advancements.adventure.sleep_in_bed.title": "<PERSON>'<PERSON> vaichijeletik", "advancements.adventure.sniper_duel.description": "Milo jkot jbakubel ta 50 metro snamal", "advancements.adventure.sniper_duel.title": "Pask'op ta balaetik", "advancements.adventure.spyglass_at_dragon.description": "K'elo li ender muk'ta ain xchi'uk jbej katalejoe", "advancements.adventure.spyglass_at_dragon.title": "¿Mi ja' jkot xulemtak'in?", "advancements.adventure.spyglass_at_ghast.description": "K'elo jkot ghast xchi'uk jun katalejo", "advancements.adventure.spyglass_at_ghast.title": "¿Mi ja' jbej klobo?", "advancements.adventure.spyglass_at_parrot.description": "K'elo jkot puyuch' xchi'uk jun katalejo", "advancements.adventure.spyglass_at_parrot.title": "¿Mi ja' jkot mut?", "advancements.adventure.summon_iron_golem.description": "Paso jkot jchabivanej tak'in sventa skolta ta spojel jun bik'it lum", "advancements.adventure.summon_iron_golem.title": "<PERSON>", "advancements.adventure.throw_trident.description": "Tenbo jtel trirente k'usi. \nYa'yejal: mi namal xaten li atrirentee, mu'yuk a<PERSON><PERSON><PERSON><PERSON><PERSON>.", "advancements.adventure.throw_trident.title": "Lek la apas", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON>o jun totem ta mu chamelal sventa chakontrain li chamele", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.trade.description": "Chonolajan xchi'uk jun jbik'it lum", "advancements.adventure.trade.title": "¡Jun lekil chonolajel!", "advancements.adventure.trade_at_world_height.description": "Chonolajan xchi'uk jun jbik'it lum ta sts'ak snamal balumil", "advancements.adventure.trade_at_world_height.title": "Jchonolajel k'anal", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Tuneso li plantiya tak'inaletik li'e ta j-ech'el no'ox: akuxa, eal, ch'ilte', j<PERSON><PERSON><PERSON><PERSON>, ch'inetel, jsa'sunvanej, balak'vo', jsa'be", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Yakal tstak'intas xchi'uk estilo", "advancements.adventure.trim_with_any_armor_pattern.description": "Meltsano jlik bojbil almarura ta sba jkot mexa tak'inal", "advancements.adventure.trim_with_any_armor_pattern.title": "<PERSON><PERSON> tspas jun ach' imajen", "advancements.adventure.two_birds_one_arrow.description": "Milo cha'kot muk'ta sots'etik xchi'uk jtel yolob xchi'uk ch'ojel", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON>kot mutetik, jtel yolob", "advancements.adventure.under_lock_and_key.description": "Tuneso jbej yavi' tsatsalil ta jbej arkon", "advancements.adventure.under_lock_and_key.title": "Lek makbil", "advancements.adventure.use_lodestone.description": "Tuneso jbej burujula ta maknetita", "advancements.adventure.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, ich'un ta jna", "advancements.adventure.very_very_frightening.description": "<PERSON><PERSON> jun jbik'it lum xchi'uk jun anjel", "advancements.adventure.very_very_frightening.title": "<PERSON><PERSON><PERSON> ti sibtasvane", "advancements.adventure.voluntary_exile.description": "<PERSON> li skapitanik makbeetike.\nLek nan aventa mi chanamaj ta bik'it lumetik ta j-ok'e...", "advancements.adventure.voluntary_exile.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "<PERSON>anavan ta p'up' taiv... pe mu xabaj ta yut", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Xikit jech k'ucha'al jkot t'ul", "advancements.adventure.who_needs_rockets.description": "Tuneso jmoj ik' sventa xaten aba ta yak'ol 8 kubo", "advancements.adventure.who_needs_rockets.title": "¿Buch'u sk'an yolonk'ok'etik?", "advancements.adventure.whos_the_pillager_now.description": "Ak'be jlech xpoxil jun makbe", "advancements.adventure.whos_the_pillager_now.title": "¿Buch'u ja' li makbe tanae?", "advancements.empty": "<PERSON><PERSON><PERSON> mu'yuk k'usi li'e...", "advancements.end.dragon_breath.description": "<PERSON><PERSON> li yik' muk'ta ain ta jboch limeta nene", "advancements.end.dragon_breath.title": "¡Tu li avee!", "advancements.end.dragon_egg.description": "Sa'o li ston muk'ta aine", "advancements.end.dragon_egg.title": "<PERSON> ach' nitilulale", "advancements.end.elytra.description": "Tao jlik end-xik'", "advancements.end.elytra.title": "Li vinajele ja' li ts'ake", "advancements.end.enter_end_gateway.description": "<PERSON><PERSON> anilajan ta isla", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON>", "advancements.end.find_end_city.description": "<PERSON><PERSON>, ¿k'usi xu' xkot ta pasel?", "advancements.end.find_end_city.title": "Li muk'ta jteklume ta slajeb tajimol", "advancements.end.kill_dragon.description": "<PERSON>'elo aba batel", "advancements.end.kill_dragon.title": "Koltao li Ende", "advancements.end.levitate.description": "Vilan ta yak'ol 50 kubo yu'un li smajeltak jbej shulkere", "advancements.end.levitate.title": "Al<PERSON>' sba chvinaj li'e", "advancements.end.respawn_dragon.description": "<PERSON>k'o nixtok li muk'ta aine", "advancements.end.respawn_dragon.title": "Laj... <PERSON>...", "advancements.end.root.description": "¿Mi melel ta xlaj li tajimole? O... ¿mi ja' no'ox lik?", "advancements.end.root.title": "<PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Paso skomes jbej pastel ta jbej kubo sonal li jkoltaobbaile", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Son sk'ak'alil vok'el", "advancements.husbandry.allay_deliver_item_to_player.description": "Paso chak'botuk k'usitik li jkoltaobbaile", "advancements.husbandry.allay_deliver_item_to_player.title": "<PERSON>", "advancements.husbandry.axolotl_in_a_bucket.description": "Tsako jkot axolote ta jboch valte", "advancements.husbandry.axolotl_in_a_bucket.title": "Li jti'vanej ti mas alak' sbae", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON>'<PERSON> skotol li k'usi xu' xve'ate, manchuk mu lekuk aventa", "advancements.husbandry.balanced_diet.title": "Lekil ve'elil", "advancements.husbandry.breed_all_animals.description": "¡Paso xchi'inuk sbaik skotol li chanuletike!", "advancements.husbandry.breed_all_animals.title": "Chib ta chib", "advancements.husbandry.breed_an_animal.description": "Paso xchi'inuk sbaik cha'kot chanuletik ti ko'olike", "advancements.husbandry.breed_an_animal.title": "Te oy ta ik' li k'anele", "advancements.husbandry.complete_catalogue.description": "¡Ak'bik mantal skotol li tos supetike!", "advancements.husbandry.complete_catalogue.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.feed_snifflet.description": "Mak'lino j<PERSON> unin j-uts'ivanej", "advancements.husbandry.feed_snifflet.title": "Bik'it uts'ieletik", "advancements.husbandry.fishy_business.description": "<PERSON><PERSON><PERSON> jun choy", "advancements.husbandry.fishy_business.title": "K'op xchi'uk mik'aliletik", "advancements.husbandry.froglights.description": "<PERSON><PERSON> skotol li nop'olkuboetike ta akejeb", "advancements.husbandry.froglights.title": "¡Xchi'uk li jkapbil ju'eltike!", "advancements.husbandry.kill_axolotl_target.description": "Paso ekipo xchi'uk jkot axolote xchi'uk paso kanal ta jun majbail", "advancements.husbandry.kill_axolotl_target.title": "¡Mas tsotsutik mi ts'akalutike!", "advancements.husbandry.leash_all_frog_variants.description": "Sa'o li jujun tos pok'ok'e xchi'uk jtuch' cho'", "advancements.husbandry.leash_all_frog_variants.title": "Li oxib mosketeroetike", "advancements.husbandry.make_a_sign_glow.description": "Paso nop'uk li sts'ib j<PERSON>j let<PERSON>oe", "advancements.husbandry.make_a_sign_glow.title": "¡Chtil xa li jokoe!", "advancements.husbandry.netherite_hoe.description": "Tuneso jbej linkote neterita sventa chalekubtas li jtel asarone, ta patil, nopo li ta akuxlejale", "advancements.husbandry.netherite_hoe.title": "<PERSON>'<PERSON>el ta abtel", "advancements.husbandry.obtain_sniffer_egg.description": "Sa'o li ston j-uts'ivaneje", "advancements.husbandry.obtain_sniffer_egg.title": "Toj mu", "advancements.husbandry.place_dried_ghast_in_water.description": "Ak'o jun takin ghast ta vo'", "advancements.husbandry.place_dried_ghast_in_water.title": "¡Uch'o vo'!", "advancements.husbandry.plant_any_sniffer_seed.description": "Ts'uno li sbek'tak j-uts'ivaneje", "advancements.husbandry.plant_any_sniffer_seed.title": "<PERSON><PERSON> chav li k'usi kot ta pasele", "advancements.husbandry.plant_seed.description": "Ts'uno jbej bek'il xchi'uk k'elo k'uxi chch'i", "advancements.husbandry.plant_seed.title": "Bek'<PERSON>ik", "advancements.husbandry.remove_wolf_armor.description": "Lok'esbo yalmarura jkot ok'il xchi'uk texerex", "advancements.husbandry.remove_wolf_armor.title": "Texerextao", "advancements.husbandry.repair_wolf_armor.description": "Lek meltsano jlik sokem almarura ok'il xchi'uk smik'altak ib", "advancements.husbandry.repair_wolf_armor.title": "Lek xa nixtok", "advancements.husbandry.ride_a_boat_with_a_goat.description": "<PERSON>yan ta jun bote xchi'uk jkot tentsun", "advancements.husbandry.ride_a_boat_with_a_goat.title": "¿Buch'u la yal mu xu' xnuxik li tentsunetike?", "advancements.husbandry.root.description": "Oy ep amikoetik xchi'uk ve'elil ta balumil", "advancements.husbandry.root.title": "Utsilal", "advancements.husbandry.safely_harvest_honey.description": "Tuneso jun k'ok' xchi'uk jboch limeta sventa chasa' li yajapom jbej tompom, pe mu xavilbajin li xchanul pometike", "advancements.husbandry.safely_harvest_honey.title": "Ochan me ta jtompomkutik", "advancements.husbandry.silk_touch_nest.description": "Vuyo jbej na pom o jbej nail xchi'uk oxkot xchanul pometik xchi'uk t'enolanel sera", "advancements.husbandry.silk_touch_nest.title": "Jsibtastik li xchanul pometike", "advancements.husbandry.tactical_fishing.description": "Tsako jun choy... ¡mu xchi'uk jtel te'el tsakchoy!", "advancements.husbandry.tactical_fishing.title": "Muk'ta tsakchoy", "advancements.husbandry.tadpole_in_a_bucket.description": "Tsako jkot lech ta jboch valte", "advancements.husbandry.tadpole_in_a_bucket.title": "Kroak kroak", "advancements.husbandry.tame_an_animal.description": "Kumpareino jkot chanul", "advancements.husbandry.tame_an_animal.title": "Amikoutik o", "advancements.husbandry.wax_off.description": "¡Ch'ulo li xchab jbej kubo k'anal k'unil tak'ine!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.wax_on.description": "¡Ak'bo chab jbej kubo k'anal k'unil tak'in xchi'uk jbej tompom!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON>' chab", "advancements.husbandry.whole_pack.description": "Kumpareino jun ta jujuntos ok'il", "advancements.husbandry.whole_pack.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.all_effects.description": "Sa'o skotol li ejektoetike ti xu'e", "advancements.nether.all_effects.title": "¿K'uxi livulotik li'e?", "advancements.nether.all_potions.description": "Sa'o skotol li yejektotakik poxiletike ti xu'e", "advancements.nether.all_potions.title": "Jboch koktel ti xt'ome", "advancements.nether.brew_potion.description": "<PERSON>jo jboch poxil", "advancements.nether.brew_potion.title": "<PERSON><PERSON><PERSON>", "advancements.nether.charge_respawn_anchor.description": "Paso nojuk li vinajebale", "advancements.nether.charge_respawn_anchor.title": "Mu'yuk ''vukub'' xkuxlejal ta melel", "advancements.nether.create_beacon.description": "<PERSON>, ts'ako xchi'uk tsano jbej jaro", "advancements.nether.create_beacon.title": "Vul xa li luse", "advancements.nether.create_full_beacon.description": "Paso lek abtejuk li jaroe", "advancements.nether.create_full_beacon.title": "Jarotasvanej", "advancements.nether.distract_piglin.description": "Ch'ayo li yo'onton jun pigline xchi'uk jutuk k'anal tak'in", "advancements.nether.distract_piglin.title": "Jme'tak'in", "advancements.nether.explore_nether.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> skotol li <PERSON> osi<PERSON>", "advancements.nether.explore_nether.title": "K'ak'al turismo", "advancements.nether.fast_travel.description": "Tuneso li Nethere sventa chaxanav 7 km ta Sba Balumil", "advancements.nether.fast_travel.title": "<PERSON><PERSON><PERSON>al ch'oj", "advancements.nether.find_bastion.description": "<PERSON>chan ta jbej sokem va'alte'", "advancements.nether.find_bastion.title": "Ta vo'one", "advancements.nether.find_fortress.description": "<PERSON>chan ta jbej snail pask'op", "advancements.nether.find_fortress.title": "Jbej xi'el na pask'op", "advancements.nether.get_wither_skull.description": "Sa'o li sbakil sjol jun jbakubel yu'un <PERSON>er", "advancements.nether.get_wither_skull.title": "<PERSON> jbej bakil jolil", "advancements.nether.loot_bastion.description": "Elk'ano jbej kaxa ta jbej sokem snail pask'op", "advancements.nether.loot_bastion.title": "Chitometik ta pask'op", "advancements.nether.netherite_armor.description": "Sa'o li sjunul almarura neteritae", "advancements.nether.netherite_armor.title": "Makun xchi'uk neterita", "advancements.nether.obtain_ancient_debris.description": "Sa'o li poko' xutetike", "advancements.nether.obtain_ancient_debris.title": "Nak'al ta utilal", "advancements.nether.obtain_blaze_rod.description": "Sa'o jtel ste'el jk'ok'", "advancements.nether.obtain_blaze_rod.title": "Chtajin xchi'uk k'ok'", "advancements.nether.obtain_crying_obsidian.description": "Sa'o j-ok'el xik' xulem", "advancements.nether.obtain_crying_obsidian.title": "¿Buch'u yakal tstul tuixetik?", "advancements.nether.return_to_sender.description": "Milo jkot ghast xchi'uk jbej bola k'ok'", "advancements.nether.return_to_sender.title": "Sutesbo li jtakvaneje", "advancements.nether.ride_strider.description": "Kajlibino jkot jxanbal tsuk' xchi'uk jtel vale' xchi'uk jpets ts'otol moni'", "advancements.nether.ride_strider.title": "¡Oy yo'tak li varko li'e!", "advancements.nether.ride_strider_in_overworld_lava.description": "Nom paxyajan ta jkot jxanbal tsuk' ta sba jun nab tsku' ta Sba Balumil", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON><PERSON> k'u<PERSON>'al li anae", "advancements.nether.root.description": "¡Oy ep k'ixin!", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON>o li <PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "Koltao jkot ghast ta <PERSON>her, lek ich'o li ta Sba Balumile... xchi'uk... milo", "advancements.nether.uneasy_alliance.title": "<PERSON> melel aliansa", "advancements.nether.use_lodestone.description": "Tuneso jbej burujula ta jbej kubo maknetita", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON><PERSON>, ich'un ta jna", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "K'unibtaso xchi'uk poxtao jun ipajesbil jbik'it lum", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Valk'uno jtel yolob xchi'uk jpech eskuro", "advancements.story.deflect_arrow.title": "<PERSON><PERSON><PERSON><PERSON>, kolaval", "advancements.story.enchant_item.description": "<PERSON><PERSON> jun item ta jbej mexa kapel", "advancements.story.enchant_item.title": "Jkapvanej", "advancements.story.enter_the_end.description": "Ochan ta jbej portal ta End", "advancements.story.enter_the_end.title": "¿Laj xa?", "advancements.story.enter_the_nether.description": "<PERSON>, tsano xchi'uk ochan ta jbej portal ta Nether", "advancements.story.enter_the_nether.title": "Sk'an chibatotik mas nat", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON><PERSON> j<PERSON>j sat ender", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> satil", "advancements.story.form_obsidian.description": "Sa'o jbej kubo xik' xulem", "advancements.story.form_obsidian.title": "Sikil nopel", "advancements.story.iron_tools.description": "Meltsano jtel piko ta tak'in", "advancements.story.iron_tools.title": "Ak'be ak'in li tak'ine", "advancements.story.lava_bucket.description": "But'o jbej valte xchi'uk tsuk'", "advancements.story.lava_bucket.title": "K'ak'al k'usitik", "advancements.story.mine_diamond.description": "<PERSON>'o riamanteetik", "advancements.story.mine_diamond.title": "¡Riamanteetik!", "advancements.story.mine_stone.description": "Jok'o ton xchi'uk li ach' apikoe", "advancements.story.mine_stone.title": "Sk'ak'alil ton", "advancements.story.obtain_armor.description": "Pojo aba xchi'uk jk'os almarura tak'in", "advancements.story.obtain_armor.title": "<PERSON><PERSON> aba", "advancements.story.root.description": "Li yo'ontone xchi'uk li slo'il<PERSON>el ta<PERSON>le", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Xu' s<PERSON><PERSON>ot li almarura riamantee", "advancements.story.shiny_gear.title": "Makun xchi'uk riamante", "advancements.story.smelt_iron.description": "Unijeso tak'in sventa chasa' li slinkotee", "advancements.story.smelt_iron.title": "Sk'ak'alil tak'in", "advancements.story.upgrade_tools.description": "Paso jtel mas lekil piko", "advancements.story.upgrade_tools.title": "Yakal tslekubtas li abtejebaletike", "advancements.toast.challenge": "¡Tsatsalil ts'akanbil!", "advancements.toast.goal": "¡Objetivo tabil!", "advancements.toast.task": "¡Porokreso pasbil!", "argument.anchor.invalid": "Chopol li sposision stsinel entirale: %s", "argument.angle.incomplete": "Mu ts'akal (sk'an 1 ankolo)", "argument.angle.invalid": "<PERSON><PERSON>", "argument.block.id.invalid": "Mu ojtikinbil tos kubo: %s", "argument.block.property.duplicate": "Li u'uninele ''%s'' yu'un kubo %s xu' no'ox xkomesat j-ech'el", "argument.block.property.invalid": "<PERSON> kuboe %s mu chch'am ''%s\" sventa li u'uninele %s", "argument.block.property.novalue": "Sk'an jun tojollial sventa li yu'uninel ''%s'' kuboe %s", "argument.block.property.unclosed": "Sk'an ] sventa smak li yu'unineltak kuboe", "argument.block.property.unknown": "Li kuboe %s mu'yuk yu'uninel ''%s''", "argument.block.tag.disallowed": "<PERSON> xu' xtunesatik li et<PERSON> (#), ja' no'ox mele<PERSON> kub<PERSON>k", "argument.color.invalid": "Mu ojtikinbil bonolil: %s", "argument.component.invalid": "Chopol komponenteil lo'il: %s", "argument.criteria.invalid": "Mu ojtikinbil kriterio: %s", "argument.dimension.invalid": "Mu'yuk li rimensione ''%s''", "argument.double.big": "<PERSON> xu' x-elan mas muk' ti %s li tojolilal cha'pasele (la sta: %s)", "argument.double.low": "<PERSON> xu' x-elan mas bik'it ke %s li tojolilal cha'pasele (la sta: %s)", "argument.entity.invalid": "Chopol biil o UUID", "argument.entity.notfound.entity": "Mu la sta entiraletik", "argument.entity.notfound.player": "Mu la sta jtajimoletik", "argument.entity.options.advancements.description": "Jtajimoletik xchi'uk porokresoetik", "argument.entity.options.distance.description": "Namal ta entiral", "argument.entity.options.distance.negative": "Mu xu' ja' nekativo li namale", "argument.entity.options.dx.description": "Entiraletik ta o'lol X xchi'uk X+dX", "argument.entity.options.dy.description": "Entiraletik ta o'lol Y xchi'uk Y+dY", "argument.entity.options.dz.description": "Entiraletik ta o'lol Z xchi'uk Z+dZ", "argument.entity.options.gamemode.description": "Jtajimoletik xchi'uk tos tajimol", "argument.entity.options.inapplicable": "Mu xu' xtunesat li' li t'ujele ''%s''", "argument.entity.options.level.description": "<PERSON><PERSON>", "argument.entity.options.level.negative": "Mu xu' ja' nekativo li nivele", "argument.entity.options.limit.description": "Sts'ak yatolalik entiraletik ta sutel", "argument.entity.options.limit.toosmall": "Sk'an ja' 1 li ts'ake", "argument.entity.options.mode.invalid": "Chopol o mu ojtikinbil tos tajimol: %s", "argument.entity.options.name.description": "Sbi entiral", "argument.entity.options.nbt.description": "Entiraletik xchi'uk NBT", "argument.entity.options.predicate.description": "<PERSON><PERSON><PERSON>", "argument.entity.options.scores.description": "Entiraletik xchi'uk atol", "argument.entity.options.sort.description": "Chch'ak li entiraletike", "argument.entity.options.sort.irreversible": "Chopol o mu x-ojtikinat li tos ch'akele ''%s''", "argument.entity.options.tag.description": "Entiraletik xchi'uk etiketa", "argument.entity.options.team.description": "Entiraletik ta yut jun ekipo", "argument.entity.options.type.description": "Entiraletik ta tos", "argument.entity.options.type.invalid": "Chopol o mu ojtikinbil tos entiral: %s", "argument.entity.options.unknown": "Mu ojti<PERSON>bil t'ujel: %s", "argument.entity.options.unterminated": "Sk'an '']'' s<PERSON>a smak li t'ujeletike", "argument.entity.options.valueless": "Sk'an jun tojo<PERSON>lal sventa li t'ujele ''%s''", "argument.entity.options.x.description": "posision x", "argument.entity.options.x_rotation.description": "Joyob<PERSON><PERSON> x yu'un entiral", "argument.entity.options.y.description": "posision y", "argument.entity.options.y_rotation.description": "<PERSON><PERSON><PERSON><PERSON> y yu'un entiral", "argument.entity.options.z.description": "posision z", "argument.entity.selector.allEntities": "Skotol li entiraletike", "argument.entity.selector.allPlayers": "Skotol li jtajimoletike", "argument.entity.selector.missing": "Sk'an li tos jt'ujvaneje", "argument.entity.selector.nearestEntity": "Mas no<PERSON> entiral", "argument.entity.selector.nearestPlayer": "Mas tijil j<PERSON>ji<PERSON>l", "argument.entity.selector.not_allowed": "Mu xu' xtunesat li jt'ujvaneje", "argument.entity.selector.randomPlayer": "B<PERSON>'uuk no'ox j<PERSON>jimol", "argument.entity.selector.self": "Entiral ta tana", "argument.entity.selector.unknown": "Mu ojtikinbil tos jt'ujvanej: %s", "argument.entity.toomany": "Xu' no'ox xtunesat jun entiral, pe xu' sts'ik mas ke jun li <PERSON>bil jt'ujvaneje", "argument.enum.invalid": "<PERSON><PERSON> to<PERSON>lal ''%s''", "argument.float.big": "Mu xu' x-elan mas muk' ti %s li tojolilale ''float'' (la sta: %s)", "argument.float.low": "Mu xu' x-elan mas bik'it ti %s li tojolilale ''float'' (la sta: %s)", "argument.gamemode.invalid": "Mu ojtikinbil tos tajimol: %s", "argument.hexcolor.invalid": "Chopol eksaresimal kotiko bonolil ''%s''", "argument.id.invalid": "Chopol ID", "argument.id.unknown": "Mu ojtikinbil ID: %s", "argument.integer.big": "<PERSON> xu' x-elan mas muk' ti %s li ts'akal atolale (la sta: %s)", "argument.integer.low": "<PERSON> xu' x-elan mas bik'it ti %s li ts'akal atolale (la sta: %s)", "argument.item.id.invalid": "Mu ojtikinbil item: %s", "argument.item.tag.disallowed": "<PERSON> xu' xtunesatik li et<PERSON> (#), ja' no'ox melel k'usitik", "argument.literal.incorrect": "Sk'an li tojolilal literale %s", "argument.long.big": "<PERSON> xu' x-elan mas muk' ti %s li tojolilale ''long'' (la jta: %s)", "argument.long.low": "<PERSON> xu' x-elan mas bik'it ke %s li tojolilale ''long'' (la sta: %s)", "argument.message.too_long": "Toj nat li mantale (%s > li ts'ake ja' %s xot)", "argument.nbt.array.invalid": "Chopol tos cholel: %s", "argument.nbt.array.mixed": "Mu xu' stik' %s ta %s", "argument.nbt.expected.compound": "Sk'an ox jun meltsanbil etiketa", "argument.nbt.expected.key": "Sk'an yavi'", "argument.nbt.expected.value": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "argument.nbt.list.mixed": "Mu xu' stik' %s ta slista %s", "argument.nbt.trailing": "Oy ep tajyalel ale<PERSON>", "argument.player.entities": "Chtunesat no'ox xchi'uk jtajimoletik li mantal li'e, pe tskap entiraletik ek li tunesbil jt'ujvaneje", "argument.player.toomany": "Xu' no'ox xtunesat jun j<PERSON>, pe xu' sts'ik mas ke jun li <PERSON> jt'ujvaneje", "argument.player.unknown": "<PERSON> x-elan li j<PERSON> le'e", "argument.pos.missing.double": "S<PERSON>'an jun koortenara", "argument.pos.missing.int": "Sk'an li sposision jbej kuboe", "argument.pos.mixed": "Mu xu' skap tijil koortenaraetik xchi'uk koortenara balumiletik (o chtunesat ^ xchi'uk skotol o mu chtunesat)", "argument.pos.outofbounds": "Te oy ta pana ts'ak li posision le'e.", "argument.pos.outofworld": "¡Te oy ta pana balumil li posision le'e!", "argument.pos.unloaded": "<PERSON> xvinaj to li posision le'e", "argument.pos2d.incomplete": "Mu ts'akal (sk'an 2 koortenara)", "argument.pos3d.incomplete": "Mu ts'akal (sk'an 3 koortenara)", "argument.range.empty": "Sk'an jun tojolilal o jun chol tojolilal", "argument.range.ints": "Xu' no'ox xtunesatik ts'akal atolaletik, mu'yuk resimaletik", "argument.range.swapped": "<PERSON> xu' x-elan mas muk' ke ts'ak li minimoe", "argument.resource.invalid_type": "Li elementoe ''%s'' oy xchopol tos ''%s'' (sk'an ''%s'')", "argument.resource.not_found": "Mu xu' sta li elementoe ''%s'' ta tos ''%s''", "argument.resource_or_id.failed_to_parse": "<PERSON><PERSON><PERSON> k'alal tspas ox analisar li estrukturae: ''%s''", "argument.resource_or_id.invalid": "Chopol ID o etiketa", "argument.resource_or_id.no_such_element": "Mu xu' sta li elementoe ''%s'' ta ts'ib ''%s''", "argument.resource_selector.not_found": "Mu'yuk k'usi tsko'oltas li jt'ujvaneje ''%s'' ta tos ''%s''", "argument.resource_tag.invalid_type": "Li etiketae ''%s'' oy xchopol tos ''%s'' (sk'an ''%s'')", "argument.resource_tag.not_found": "Mu xu' sta li etiketae ''%s'' ta tos ''%s''", "argument.rotation.incomplete": "Mu ts'akal (sk'an 2 koortenara)", "argument.scoreHolder.empty": "<PERSON> sta markarol atoletik", "argument.scoreboardDisplaySlot.invalid": "Mu ojtikinbil av ak'el ta ilel: %s", "argument.style.invalid": "Chopol estilo: %s", "argument.time.invalid_tick_count": "Sk'an ja' positivoik li yatolalik sikloetike", "argument.time.invalid_unit": "Chopol uniral", "argument.time.tick_count_too_low": "Mu xu' x-elan mas bik'it ke %s li yatolalik sikloetike (la sta: %s)", "argument.uuid.invalid": "Chopol UUID", "argument.waypoint.invalid": "Mu ja' jun restino li t'ujbil entirale", "arguments.block.tag.unknown": "Mu ojtikinbil etiketa kubo ''%s''", "arguments.function.tag.unknown": "Mu ojtikinbil etiketa tunel ''%s''", "arguments.function.unknown": "Mu ojtikinbil tunel %s", "arguments.item.component.expected": "Sk'an jun komponente item", "arguments.item.component.malformed": "Sokem komponente ''%s'': ''%s''", "arguments.item.component.repeated": "Cha'pasat li komponentee ''%s'', pe jun no'ox tojolilal xu' xich' ts'ibael", "arguments.item.component.unknown": "Mu ojtikinbil komponente item ''%s''", "arguments.item.malformed": "Sokbil item: ''%s''", "arguments.item.overstacked": "Li %se xu' no'ox slats sba %s to", "arguments.item.predicate.malformed": "Pretikaro ''%s'' chopol meltsanbil: ''%s''", "arguments.item.predicate.unknown": "Mu ojtikinbil pretikaro item ''%s''", "arguments.item.tag.unknown": "Mu ojtikinbil etiketa item: %s", "arguments.nbtpath.node.invalid": "Chopol be NBT", "arguments.nbtpath.nothing_found": "Mu'yuk elementoetik xko'olajik k'ucha'al %s", "arguments.nbtpath.too_deep": "NBT ti lok'e toj nat la stasin sba", "arguments.nbtpath.too_large": "Ja' toj nat li NBT ti lok'e", "arguments.objective.notFound": "Mu ojtikinbil yobjetivo pisaron atol ''%s''", "arguments.objective.readonly": "Xu' no'ox xk'elat li yobjetivo ''%s'' pisaron atole", "arguments.operation.div0": "Mu xu' xch'ak sba ta som", "arguments.operation.invalid": "Chopol operasion", "arguments.swizzle.invalid": "Chopol ejeetik: sk'an li skapele X, Y xchi'uk Z", "attribute.modifier.equals.0": "%2$s: %1$s", "attribute.modifier.equals.1": "%2$s: %1$s %%", "attribute.modifier.equals.2": "%2$s: %1$s %%", "attribute.modifier.plus.0": "%2$s: +%1$s", "attribute.modifier.plus.1": "%2$s: +%1$s %%", "attribute.modifier.plus.2": "%2$s: +%1$s %%", "attribute.modifier.take.0": "%2$s: -%1$s", "attribute.modifier.take.1": "%2$s: -%1$s %%", "attribute.modifier.take.2": "%2$s: -%1$s %%", "attribute.name.armor": "Almarura", "attribute.name.armor_toughness": "Sts'ikel almarura", "attribute.name.attack_damage": "<PERSON><PERSON><PERSON><PERSON> ta majel", "attribute.name.attack_knockback": "<PERSON><PERSON><PERSON> majel", "attribute.name.attack_speed": "<PERSON><PERSON><PERSON> ma<PERSON>", "attribute.name.block_break_speed": "Yanilal sk'asel kubo", "attribute.name.block_interaction_range": "<PERSON><PERSON>ko stuneselik kuboetik", "attribute.name.burning_time": "Sk'ak'alil k'ak'emal", "attribute.name.camera_distance": "<PERSON><PERSON><PERSON>", "attribute.name.entity_interaction_range": "Sranko stuneselik entiraletik", "attribute.name.explosion_knockback_resistance": "Ts'ikel ta xujelik t'omeletik", "attribute.name.fall_damage_multiplier": "J-e<PERSON><PERSON><PERSON> yaintasel ta bajel", "attribute.name.flying_speed": "<PERSON><PERSON><PERSON> vilel", "attribute.name.follow_range": "<PERSON><PERSON>", "attribute.name.generic.armor": "Almarura", "attribute.name.generic.armor_toughness": "Sts'ikel almarura", "attribute.name.generic.attack_damage": "<PERSON><PERSON><PERSON><PERSON> ta majel", "attribute.name.generic.attack_knockback": "<PERSON><PERSON><PERSON> majel", "attribute.name.generic.attack_speed": "<PERSON><PERSON><PERSON> ma<PERSON>", "attribute.name.generic.block_interaction_range": "<PERSON><PERSON>ko stuneselik kuboetik", "attribute.name.generic.burning_time": "Sk'ak'alil k'ak'emal", "attribute.name.generic.entity_interaction_range": "Sranko stuneselik entiraletik", "attribute.name.generic.explosion_knockback_resistance": "Ts'ikel ta xujelik t'omeletik", "attribute.name.generic.fall_damage_multiplier": "J-e<PERSON><PERSON><PERSON> yaintasel ta bajel", "attribute.name.generic.flying_speed": "<PERSON><PERSON><PERSON> vilel", "attribute.name.generic.follow_range": "<PERSON><PERSON>", "attribute.name.generic.gravity": "<PERSON><PERSON><PERSON>", "attribute.name.generic.jump_strength": "<PERSON><PERSON> bitel", "attribute.name.generic.knockback_resistance": "Ts'ikel ta xujel", "attribute.name.generic.luck": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.max_absorption": "Ts'ak absorsion", "attribute.name.generic.max_health": "<PERSON><PERSON>'ak k<PERSON>l", "attribute.name.generic.movement_efficiency": "<PERSON><PERSON><PERSON> bak'el", "attribute.name.generic.movement_speed": "<PERSON><PERSON><PERSON>", "attribute.name.generic.oxygen_bonus": "Skomenal ik'", "attribute.name.generic.safe_fall_distance": "Snamal mu xi'el lomel", "attribute.name.generic.scale": "Eskala", "attribute.name.generic.step_height": "Snatilal pech'el", "attribute.name.generic.water_movement_efficiency": "Tunelal ja'albak'el", "attribute.name.gravity": "<PERSON><PERSON><PERSON>", "attribute.name.horse.jump_strength": "<PERSON><PERSON> sbitel ka'", "attribute.name.jump_strength": "<PERSON><PERSON> bitel", "attribute.name.knockback_resistance": "Ts'ikel ta xujel", "attribute.name.luck": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.max_absorption": "Ts'ak absorsion", "attribute.name.max_health": "<PERSON><PERSON>'ak k<PERSON>l", "attribute.name.mining_efficiency": "Tunelal ta jok'el", "attribute.name.movement_efficiency": "<PERSON><PERSON><PERSON> bak'el", "attribute.name.movement_speed": "Anil", "attribute.name.oxygen_bonus": "Skomenal ik'", "attribute.name.player.block_break_speed": "Yanilal sk'asel kubo", "attribute.name.player.block_interaction_range": "<PERSON><PERSON>ko stuneselik kuboetik", "attribute.name.player.entity_interaction_range": "Sranko stuneselik entiraletik", "attribute.name.player.mining_efficiency": "Tunelal ta jok'el", "attribute.name.player.sneaking_speed": "<PERSON><PERSON><PERSON> k'alal cht'ini", "attribute.name.player.submerged_mining_speed": "<PERSON><PERSON><PERSON> ta jok'el", "attribute.name.player.sweeping_damage_ratio": "<PERSON><PERSON><PERSON> me<PERSON>v", "attribute.name.safe_fall_distance": "Snamal mu xi'el lomel", "attribute.name.scale": "Eskala", "attribute.name.sneaking_speed": "<PERSON><PERSON><PERSON> k'alal cht'ini", "attribute.name.spawn_reinforcements": "<PERSON><PERSON><PERSON> sventa j<PERSON>mel<PERSON>", "attribute.name.step_height": "Snatilal pech'el", "attribute.name.submerged_mining_speed": "<PERSON><PERSON><PERSON> ta jok'el ta yut vo'", "attribute.name.sweeping_damage_ratio": "<PERSON><PERSON><PERSON> me<PERSON>v", "attribute.name.tempt_range": "Sranko snopajeselik chanuletik", "attribute.name.water_movement_efficiency": "Tunelal ja'albak'el", "attribute.name.waypoint_receive_range": "Ranko yich'el restino", "attribute.name.waypoint_transmit_range": "Ranko taransmision restino", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON> sventa j<PERSON>mel<PERSON>", "biome.minecraft.badlands": "Lumtik", "biome.minecraft.bamboo_jungle": "Ton-a<PERSON><PERSON><PERSON> jabnal", "biome.minecraft.basalt_deltas": "Vasaltotik", "biome.minecraft.beach": "Ti' muk'ta nab", "biome.minecraft.birch_forest": "Averultik", "biome.minecraft.cherry_grove": "Ch'ixte'tik", "biome.minecraft.cold_ocean": "<PERSON>kil muk'ta nab", "biome.minecraft.crimson_forest": "<PERSON><PERSON><PERSON><PERSON> te'tik", "biome.minecraft.dark_forest": "<PERSON><PERSON>'pulan te'tik", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON> sikil muk'ta nab", "biome.minecraft.deep_dark": "<PERSON><PERSON>l", "biome.minecraft.deep_frozen_ocean": "<PERSON><PERSON> taiv muk'ta nab", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON> chamal muk'ta nab", "biome.minecraft.deep_ocean": "<PERSON><PERSON> muk'ta nab", "biome.minecraft.desert": "<PERSON><PERSON><PERSON> bal<PERSON>l", "biome.minecraft.dripstone_caves": "Chuꞌch'enetik", "biome.minecraft.end_barrens": "End takin-osiltik", "biome.minecraft.end_highlands": "End toyol osiletik", "biome.minecraft.end_midlands": "End osiletik ta o'lol", "biome.minecraft.eroded_badlands": "Sokem lumtik", "biome.minecraft.flower_forest": "<PERSON><PERSON><PERSON> te'tik", "biome.minecraft.forest": "<PERSON>'tik", "biome.minecraft.frozen_ocean": "Taiv muk'ta nab", "biome.minecraft.frozen_peaks": "Taiv jol vitsetik", "biome.minecraft.frozen_river": "Taiv uk'um", "biome.minecraft.grove": "<PERSON>'tik", "biome.minecraft.ice_spikes": "Jol vits botetik", "biome.minecraft.jagged_peaks": "Ts'ubts'ub jol vitsetik", "biome.minecraft.jungle": "Jab<PERSON>", "biome.minecraft.lukewarm_ocean": "<PERSON><PERSON> muk'ta nab", "biome.minecraft.lush_caves": "Ts'i'leltikal ch'enetik", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON>'tik", "biome.minecraft.meadow": "Yaxaltik", "biome.minecraft.mushroom_fields": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.nether_wastes": "<PERSON>her xokol bal<PERSON>l", "biome.minecraft.ocean": "<PERSON><PERSON>'ta nab", "biome.minecraft.old_growth_birch_forest": "Poko' averultik", "biome.minecraft.old_growth_pine_taiga": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_spruce_taiga": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "biome.minecraft.pale_garden": "Sakpak'an te'tik", "biome.minecraft.plains": "Vomoltik", "biome.minecraft.river": "<PERSON><PERSON>'<PERSON>", "biome.minecraft.savanna": "<PERSON><PERSON> yaxaltik", "biome.minecraft.savanna_plateau": "<PERSON><PERSON><PERSON> takin yaxaltik", "biome.minecraft.small_end_islands": "End bik'it islaetik", "biome.minecraft.snowy_beach": "Taiv ti' muk'ta nab", "biome.minecraft.snowy_plains": "Taiv vomoltik", "biome.minecraft.snowy_slopes": "Ti'iletik xchi'uk nieve", "biome.minecraft.snowy_taiga": "<PERSON><PERSON> to<PERSON>k", "biome.minecraft.soul_sand_valley": "<PERSON><PERSON><PERSON> ch'ul<PERSON>l", "biome.minecraft.sparse_jungle": "<PERSON> j<PERSON>", "biome.minecraft.stony_peaks": "Tontikal jol vitsetik", "biome.minecraft.stony_shore": "Tontikal ti'il", "biome.minecraft.sunflower_plains": "Suntik", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.the_end": "<PERSON>", "biome.minecraft.the_void": "<PERSON>", "biome.minecraft.warm_ocean": "<PERSON><PERSON> muk'ta nab", "biome.minecraft.warped_forest": "Lukluk te'tik", "biome.minecraft.windswept_forest": "<PERSON><PERSON>'al te'tik", "biome.minecraft.windswept_gravelly_hills": "Ik'al xixibtontikal ba osiletik", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON><PERSON> ba osiletik", "biome.minecraft.windswept_savanna": "<PERSON><PERSON><PERSON>al takin yaxaltik", "biome.minecraft.wooded_badlands": "Te'tikal lumtik", "block.minecraft.acacia_button": "Voton akasia", "block.minecraft.acacia_door": "Ti'na akasia", "block.minecraft.acacia_fence": "Mok akasia", "block.minecraft.acacia_fence_gate": "Ti' mok akasia", "block.minecraft.acacia_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> akasia", "block.minecraft.acacia_leaves": "Yanaltak akasia", "block.minecraft.acacia_log": "Chumante' akasia", "block.minecraft.acacia_planks": "Tenelte' akasia<PERSON>k", "block.minecraft.acacia_pressure_plate": "Plaka net'el akasia", "block.minecraft.acacia_sapling": "K'elom akasia", "block.minecraft.acacia_sign": "<PERSON><PERSON><PERSON> akasia", "block.minecraft.acacia_slab": "Losa akasia", "block.minecraft.acacia_stairs": "Ixkalera akasia", "block.minecraft.acacia_trapdoor": "Ti'pets' akasia", "block.minecraft.acacia_wall_hanging_sign": "<PERSON><PERSON><PERSON> let<PERSON>o akasia ta kubo", "block.minecraft.acacia_wall_sign": "<PERSON><PERSON>o akasia ta kubo", "block.minecraft.acacia_wood": "Te'el akasia", "block.minecraft.activator_rail": "<PERSON><PERSON><PERSON>", "block.minecraft.air": "<PERSON><PERSON>'", "block.minecraft.allium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.amethyst_block": "Kubo ta amatista", "block.minecraft.amethyst_cluster": "Tsobol amatista", "block.minecraft.ancient_debris": "Poko' k'osetik", "block.minecraft.andesite": "<PERSON><PERSON><PERSON>", "block.minecraft.andesite_slab": "Losa antesita", "block.minecraft.andesite_stairs": "Ixkalera antesita", "block.minecraft.andesite_wall": "Yi'bel na antesita", "block.minecraft.anvil": "<PERSON><PERSON>", "block.minecraft.attached_melon_stem": "<PERSON><PERSON> ch'ut xancha", "block.minecraft.attached_pumpkin_stem": "Kakal xch'ut ch'um", "block.minecraft.azalea": "Asalea", "block.minecraft.azalea_leaves": "Yanaltak asalea", "block.minecraft.azure_bluet": "Sakpak'an nichim", "block.minecraft.bamboo": "<PERSON><PERSON>-aj", "block.minecraft.bamboo_block": "<PERSON>bo ta ton-aj", "block.minecraft.bamboo_button": "Voton ton-aj", "block.minecraft.bamboo_door": "Ti'na ton-aj", "block.minecraft.bamboo_fence": "<PERSON><PERSON> ton-aj", "block.minecraft.bamboo_fence_gate": "Ti' mok ton-aj", "block.minecraft.bamboo_hanging_sign": "<PERSON><PERSON><PERSON> leterero ton-aj", "block.minecraft.bamboo_mosaic": "<PERSON><PERSON><PERSON> ton-aj", "block.minecraft.bamboo_mosaic_slab": "<PERSON>a mosaiko ton-aj", "block.minecraft.bamboo_mosaic_stairs": "<PERSON>x<PERSON><PERSON> mosaiko ton-aj", "block.minecraft.bamboo_planks": "<PERSON><PERSON><PERSON>' ton-ajetik", "block.minecraft.bamboo_pressure_plate": "<PERSON><PERSON><PERSON> net'el ton-aj", "block.minecraft.bamboo_sapling": "K'elom ton-aj", "block.minecraft.bamboo_sign": "Let<PERSON>o ton-aj", "block.minecraft.bamboo_slab": "<PERSON><PERSON> ton-aj", "block.minecraft.bamboo_stairs": "Ixkalera ton-aj", "block.minecraft.bamboo_trapdoor": "Ti'pets' ton-aj", "block.minecraft.bamboo_wall_hanging_sign": "<PERSON><PERSON><PERSON> leterero ton-aj ta kubo", "block.minecraft.bamboo_wall_sign": "Leterero ton-aj ta kubo", "block.minecraft.banner.base.black": "Ik'al tajmek tela", "block.minecraft.banner.base.blue": "Yaxal tela", "block.minecraft.banner.base.brown": "K'anch'etan tela", "block.minecraft.banner.base.cyan": "Ya<PERSON>-elan tela", "block.minecraft.banner.base.gray": "K'anch'etan tela", "block.minecraft.banner.base.green": "Yaxal tela", "block.minecraft.banner.base.light_blue": "Yaxal tela", "block.minecraft.banner.base.light_gray": "K'anch'etan tela", "block.minecraft.banner.base.lime": "Yoxyoxtik tela", "block.minecraft.banner.base.magenta": "Tsojtsojtik tela", "block.minecraft.banner.base.orange": "K'onk'ontik tela", "block.minecraft.banner.base.pink": "Tsojtsojtik tela", "block.minecraft.banner.base.purple": "Ik'pok'an tela", "block.minecraft.banner.base.red": "<PERSON><PERSON><PERSON><PERSON> tela", "block.minecraft.banner.base.white": "Sakil tela", "block.minecraft.banner.base.yellow": "K'anal pok'al", "block.minecraft.banner.border.black": "<PERSON><PERSON>'al ti'il", "block.minecraft.banner.border.blue": "<PERSON>xa<PERSON> ti'il", "block.minecraft.banner.border.brown": "K'anch'etan ti'il", "block.minecraft.banner.border.cyan": "Ya<PERSON>-elan ti'il", "block.minecraft.banner.border.gray": "K'anch'etan ti'il", "block.minecraft.banner.border.green": "<PERSON>xa<PERSON> ti'il", "block.minecraft.banner.border.light_blue": "<PERSON>xa<PERSON> ti'il", "block.minecraft.banner.border.light_gray": "K'anch'etan ti'il", "block.minecraft.banner.border.lime": "Yoxyoxtik ti'il", "block.minecraft.banner.border.magenta": "T<PERSON>jtsojtik ti'il", "block.minecraft.banner.border.orange": "K'onk'ontik ti'il", "block.minecraft.banner.border.pink": "T<PERSON>jtsojtik ti'il", "block.minecraft.banner.border.purple": "<PERSON>k'pok'an ti'il", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON><PERSON> ti'il", "block.minecraft.banner.border.white": "Sakil ti'il", "block.minecraft.banner.border.yellow": "K'anal ti'il", "block.minecraft.banner.bricks.black": "<PERSON><PERSON><PERSON>al latriyoal", "block.minecraft.banner.bricks.blue": "Yaxal latriyoal", "block.minecraft.banner.bricks.brown": "K'anch'etan latriyoal", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON>-<PERSON>an la<PERSON>", "block.minecraft.banner.bricks.gray": "K'anch'etan latriyoal", "block.minecraft.banner.bricks.green": "Yaxal latriyoal", "block.minecraft.banner.bricks.light_blue": "Yaxal latriyoal", "block.minecraft.banner.bricks.light_gray": "K'anch'etan latriyoal", "block.minecraft.banner.bricks.lime": "Yoxyoxtik latriyoal", "block.minecraft.banner.bricks.magenta": "Tsojtsojtik latriyoal", "block.minecraft.banner.bricks.orange": "K'onk'ontik latriyoal", "block.minecraft.banner.bricks.pink": "Tsojtsojtik latriyoal", "block.minecraft.banner.bricks.purple": "Ik'pok'an latriyoal", "block.minecraft.banner.bricks.red": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "block.minecraft.banner.bricks.white": "Sakil latriyoal", "block.minecraft.banner.bricks.yellow": "K'anal latriyoal", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON><PERSON> setset", "block.minecraft.banner.circle.blue": "Yaxal setset", "block.minecraft.banner.circle.brown": "K'anch'etan setset", "block.minecraft.banner.circle.cyan": "Yax-<PERSON>an setset", "block.minecraft.banner.circle.gray": "K'anch'etan setset", "block.minecraft.banner.circle.green": "Yaxal setset", "block.minecraft.banner.circle.light_blue": "Yaxal setset", "block.minecraft.banner.circle.light_gray": "K'anch'etan setset", "block.minecraft.banner.circle.lime": "Yoxyoxtik setset", "block.minecraft.banner.circle.magenta": "Tsojtsojtik setset", "block.minecraft.banner.circle.orange": "K'onk'ontik setset", "block.minecraft.banner.circle.pink": "Tsojtsojtik setset", "block.minecraft.banner.circle.purple": "Ik'pok'an setset", "block.minecraft.banner.circle.red": "T<PERSON><PERSON><PERSON> setset", "block.minecraft.banner.circle.white": "Sakil setset", "block.minecraft.banner.circle.yellow": "K'anal setset", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.blue": "Yaxal creeper", "block.minecraft.banner.creeper.brown": "K'anch'etan creeper", "block.minecraft.banner.creeper.cyan": "Yax-<PERSON>an creeper", "block.minecraft.banner.creeper.gray": "K'anch'etan creeper", "block.minecraft.banner.creeper.green": "Yaxal creeper", "block.minecraft.banner.creeper.light_blue": "Yaxal creeper", "block.minecraft.banner.creeper.light_gray": "K'anch'etan creeper", "block.minecraft.banner.creeper.lime": "Yoxyoxtik creeper", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.orange": "K'onk'ontik creeper", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.purple": "Ik'pok'an creeper", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON><PERSON> creeper", "block.minecraft.banner.creeper.white": "Sakil creeper", "block.minecraft.banner.creeper.yellow": "K'anal creeper", "block.minecraft.banner.cross.black": "<PERSON><PERSON><PERSON><PERSON> sovter", "block.minecraft.banner.cross.blue": "<PERSON><PERSON><PERSON> sovter", "block.minecraft.banner.cross.brown": "K'anch'etan sovter", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON>-<PERSON><PERSON> sovter", "block.minecraft.banner.cross.gray": "K'anch'etan sovter", "block.minecraft.banner.cross.green": "<PERSON><PERSON><PERSON> sovter", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON><PERSON> sovter", "block.minecraft.banner.cross.light_gray": "K'anch'etan sovter", "block.minecraft.banner.cross.lime": "Yoxyoxtik sovter", "block.minecraft.banner.cross.magenta": "Tsojtsojtik sovter", "block.minecraft.banner.cross.orange": "K'onk'ontik sovter", "block.minecraft.banner.cross.pink": "Tsojtsojtik sovter", "block.minecraft.banner.cross.purple": "Ik'pok'an sovter", "block.minecraft.banner.cross.red": "<PERSON><PERSON><PERSON><PERSON> sovter", "block.minecraft.banner.cross.white": "Sakil sovter", "block.minecraft.banner.cross.yellow": "K'anal sovter", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON><PERSON>al tanal-eal ti'il", "block.minecraft.banner.curly_border.blue": "<PERSON><PERSON><PERSON> tanal-eal ti'il", "block.minecraft.banner.curly_border.brown": "K'anch'etan tanal-eal ti'il", "block.minecraft.banner.curly_border.cyan": "Ya<PERSON>-elan tanal-eal ti'il", "block.minecraft.banner.curly_border.gray": "K'anch'etan tanal-eal ti'il", "block.minecraft.banner.curly_border.green": "<PERSON><PERSON><PERSON> tanal-eal ti'il", "block.minecraft.banner.curly_border.light_blue": "<PERSON><PERSON><PERSON> tanal-eal ti'il", "block.minecraft.banner.curly_border.light_gray": "K'anch'etan tanal-eal ti'il", "block.minecraft.banner.curly_border.lime": "Yoxyoxtik tanal-eal ti'il", "block.minecraft.banner.curly_border.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tanal-eal ti'il", "block.minecraft.banner.curly_border.orange": "K'onk'ontik tanal-eal ti'il", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tanal-eal ti'il", "block.minecraft.banner.curly_border.purple": "<PERSON><PERSON>'pok'an tanal-eal ti'il", "block.minecraft.banner.curly_border.red": "<PERSON><PERSON><PERSON><PERSON> tanal-eal ti'il", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON>l tanal-eal ti'il", "block.minecraft.banner.curly_border.yellow": "<PERSON>'anal tanal-eal ti'il", "block.minecraft.banner.diagonal_left.black": "<PERSON><PERSON><PERSON><PERSON> bojel", "block.minecraft.banner.diagonal_left.blue": "<PERSON><PERSON><PERSON> bo<PERSON>l", "block.minecraft.banner.diagonal_left.brown": "<PERSON><PERSON><PERSON><PERSON>'<PERSON><PERSON> bojel", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> bo<PERSON>l", "block.minecraft.banner.diagonal_left.gray": "<PERSON><PERSON><PERSON><PERSON>'<PERSON><PERSON> bojel", "block.minecraft.banner.diagonal_left.green": "<PERSON><PERSON><PERSON> bo<PERSON>l", "block.minecraft.banner.diagonal_left.light_blue": "<PERSON><PERSON><PERSON> bo<PERSON>l", "block.minecraft.banner.diagonal_left.light_gray": "<PERSON><PERSON><PERSON><PERSON>'<PERSON><PERSON> bojel", "block.minecraft.banner.diagonal_left.lime": "Yoxyoxtik bojel", "block.minecraft.banner.diagonal_left.magenta": "T<PERSON>jtsojtik bojel", "block.minecraft.banner.diagonal_left.orange": "K'onk'ontik bojel", "block.minecraft.banner.diagonal_left.pink": "T<PERSON>jtsojtik bojel", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON>'pok'an bojel", "block.minecraft.banner.diagonal_left.red": "<PERSON><PERSON><PERSON><PERSON> bo<PERSON>l", "block.minecraft.banner.diagonal_left.white": "<PERSON><PERSON><PERSON> bojel", "block.minecraft.banner.diagonal_left.yellow": "<PERSON><PERSON><PERSON> bojel", "block.minecraft.banner.diagonal_right.black": "Ik'al t'oxel", "block.minecraft.banner.diagonal_right.blue": "Yaxal t'oxel", "block.minecraft.banner.diagonal_right.brown": "K'anch'etan t'oxel", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON>-<PERSON><PERSON> t'oxel", "block.minecraft.banner.diagonal_right.gray": "K'anch'etan t'oxel", "block.minecraft.banner.diagonal_right.green": "Yaxal t'oxel", "block.minecraft.banner.diagonal_right.light_blue": "Yaxal t'oxel", "block.minecraft.banner.diagonal_right.light_gray": "K'anch'etan t'oxel", "block.minecraft.banner.diagonal_right.lime": "Yoxyoxtik t'oxel", "block.minecraft.banner.diagonal_right.magenta": "Tsojtsojtik t'oxel", "block.minecraft.banner.diagonal_right.orange": "K'onk'ontik t'oxel", "block.minecraft.banner.diagonal_right.pink": "Tsojtsojtik t'oxel", "block.minecraft.banner.diagonal_right.purple": "Ik'pok'an t'oxel", "block.minecraft.banner.diagonal_right.red": "<PERSON><PERSON><PERSON><PERSON> t'oxel", "block.minecraft.banner.diagonal_right.white": "Sakil t'oxel", "block.minecraft.banner.diagonal_right.yellow": "K'anal t'oxel", "block.minecraft.banner.diagonal_up_left.black": "Valk'unbil ik'al t'oxel", "block.minecraft.banner.diagonal_up_left.blue": "<PERSON><PERSON>'un<PERSON> yaxal t'oxel", "block.minecraft.banner.diagonal_up_left.brown": "<PERSON><PERSON>'unbil k'anch'etan t'oxel", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> yax-elan t'oxel", "block.minecraft.banner.diagonal_up_left.gray": "<PERSON><PERSON>'unbil k'anch'etan t'oxel", "block.minecraft.banner.diagonal_up_left.green": "<PERSON><PERSON>'un<PERSON> yaxal t'oxel", "block.minecraft.banner.diagonal_up_left.light_blue": "<PERSON><PERSON>'un<PERSON> yaxal t'oxel", "block.minecraft.banner.diagonal_up_left.light_gray": "<PERSON><PERSON>'unbil k'anch'etan t'oxel", "block.minecraft.banner.diagonal_up_left.lime": "Valk'unbil yoxyoxtik t'oxel", "block.minecraft.banner.diagonal_up_left.magenta": "<PERSON><PERSON>'<PERSON><PERSON> t<PERSON> t'oxel", "block.minecraft.banner.diagonal_up_left.orange": "Valk'unbil k'onk'ontik t'oxel", "block.minecraft.banner.diagonal_up_left.pink": "<PERSON><PERSON>'<PERSON><PERSON> t<PERSON> t'oxel", "block.minecraft.banner.diagonal_up_left.purple": "Valk'unbil ik'pok'an t'oxel", "block.minecraft.banner.diagonal_up_left.red": "<PERSON><PERSON>'<PERSON><PERSON> tsajal t'oxel", "block.minecraft.banner.diagonal_up_left.white": "Valk'unbil sakil t'oxel", "block.minecraft.banner.diagonal_up_left.yellow": "<PERSON><PERSON>'unbil k'anal t'oxel", "block.minecraft.banner.diagonal_up_right.black": "Valk'unbil i<PERSON>'al bojel", "block.minecraft.banner.diagonal_up_right.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> yaxal bojel", "block.minecraft.banner.diagonal_up_right.brown": "<PERSON><PERSON>'un<PERSON> k'anch'etan bojel", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> yax-el<PERSON> bojel", "block.minecraft.banner.diagonal_up_right.gray": "<PERSON><PERSON>'un<PERSON> k'anch'etan bojel", "block.minecraft.banner.diagonal_up_right.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> yaxal bojel", "block.minecraft.banner.diagonal_up_right.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> yaxal bojel", "block.minecraft.banner.diagonal_up_right.light_gray": "<PERSON><PERSON>'un<PERSON> k'anch'etan bojel", "block.minecraft.banner.diagonal_up_right.lime": "<PERSON><PERSON>'<PERSON><PERSON> yoxy<PERSON><PERSON>k bojel", "block.minecraft.banner.diagonal_up_right.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bo<PERSON>l", "block.minecraft.banner.diagonal_up_right.orange": "Valk'unbil k'onk'ontik bojel", "block.minecraft.banner.diagonal_up_right.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bo<PERSON>l", "block.minecraft.banner.diagonal_up_right.purple": "Valk'unbil ik'pok'an bojel", "block.minecraft.banner.diagonal_up_right.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON> bo<PERSON>l", "block.minecraft.banner.diagonal_up_right.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> sakil bojel", "block.minecraft.banner.diagonal_up_right.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> k'<PERSON> bojel", "block.minecraft.banner.flow.black": "Ik'al espiral", "block.minecraft.banner.flow.blue": "Yaxal espiral", "block.minecraft.banner.flow.brown": "K'anch'etan espiral", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>-<PERSON><PERSON> espi<PERSON>", "block.minecraft.banner.flow.gray": "K'anch'etan espiral", "block.minecraft.banner.flow.green": "Yaxal espiral", "block.minecraft.banner.flow.light_blue": "Yaxal espiral", "block.minecraft.banner.flow.light_gray": "K'anch'etan espiral", "block.minecraft.banner.flow.lime": "Yoxyoxtik espiral", "block.minecraft.banner.flow.magenta": "Tsojtsojtik espiral", "block.minecraft.banner.flow.orange": "K'onk'ontik espiral", "block.minecraft.banner.flow.pink": "Tsojtsojtik espiral", "block.minecraft.banner.flow.purple": "Ik'pok'an espiral", "block.minecraft.banner.flow.red": "T<PERSON>ja<PERSON> es<PERSON>", "block.minecraft.banner.flow.white": "Sakil espiral", "block.minecraft.banner.flow.yellow": "K'anal espiral", "block.minecraft.banner.flower.black": "<PERSON><PERSON><PERSON><PERSON> nichim", "block.minecraft.banner.flower.blue": "Yaxal nichim", "block.minecraft.banner.flower.brown": "K'anch'etan nichim", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON>-<PERSON><PERSON> nichim", "block.minecraft.banner.flower.gray": "K'anch'etan nichim", "block.minecraft.banner.flower.green": "Yaxal nichim", "block.minecraft.banner.flower.light_blue": "Yaxal nichim", "block.minecraft.banner.flower.light_gray": "K'anch'etan nichim", "block.minecraft.banner.flower.lime": "Yoxyoxtik nichim", "block.minecraft.banner.flower.magenta": "Tsojtsojtik nichim", "block.minecraft.banner.flower.orange": "K'onk'ontik nichim", "block.minecraft.banner.flower.pink": "Tsojtsojtik nichim", "block.minecraft.banner.flower.purple": "<PERSON>k'pok'an nichim", "block.minecraft.banner.flower.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.white": "Sakil nichim", "block.minecraft.banner.flower.yellow": "K'<PERSON> nichim", "block.minecraft.banner.globe.black": "<PERSON><PERSON><PERSON><PERSON> bal<PERSON>l", "block.minecraft.banner.globe.blue": "<PERSON>xal balumil", "block.minecraft.banner.globe.brown": "K'anch'etan balumil", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> bal<PERSON>l", "block.minecraft.banner.globe.gray": "K'anch'etan balumil", "block.minecraft.banner.globe.green": "<PERSON>xal balumil", "block.minecraft.banner.globe.light_blue": "<PERSON>xal balumil", "block.minecraft.banner.globe.light_gray": "K'anch'etan balumil", "block.minecraft.banner.globe.lime": "Yoxyoxtik balumil", "block.minecraft.banner.globe.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> balumil", "block.minecraft.banner.globe.orange": "K'onk'ontik balumil", "block.minecraft.banner.globe.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> balumil", "block.minecraft.banner.globe.purple": "<PERSON>k'pok'an balumil", "block.minecraft.banner.globe.red": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "block.minecraft.banner.globe.white": "<PERSON><PERSON><PERSON> bal<PERSON>l", "block.minecraft.banner.globe.yellow": "K'anal balumil", "block.minecraft.banner.gradient.black": "<PERSON><PERSON><PERSON><PERSON> karat<PERSON>e", "block.minecraft.banner.gradient.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.brown": "<PERSON>'an<PERSON>'etan karat<PERSON>e", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON>'an<PERSON>'etan karat<PERSON>e", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.light_gray": "<PERSON>'an<PERSON>'etan karat<PERSON>e", "block.minecraft.banner.gradient.lime": "Yoxyoxtik karatiente", "block.minecraft.banner.gradient.magenta": "Tsojtsojtik karatiente", "block.minecraft.banner.gradient.orange": "K'onk'ontik karatiente", "block.minecraft.banner.gradient.pink": "Tsojtsojtik karatiente", "block.minecraft.banner.gradient.purple": "<PERSON>k'pok'an karatiente", "block.minecraft.banner.gradient.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.white": "<PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.banner.gradient.yellow": "<PERSON><PERSON><PERSON> karatiente", "block.minecraft.banner.gradient_up.black": "Ik'al karatiente ta base", "block.minecraft.banner.gradient_up.blue": "Yaxal karatiente ta base", "block.minecraft.banner.gradient_up.brown": "K'anch'etan karatiente ta base", "block.minecraft.banner.gradient_up.cyan": "Yax-elan karat<PERSON>e ta base", "block.minecraft.banner.gradient_up.gray": "K'anch'etan karatiente ta base", "block.minecraft.banner.gradient_up.green": "Yaxal karatiente ta base", "block.minecraft.banner.gradient_up.light_blue": "Yaxal karatiente ta base", "block.minecraft.banner.gradient_up.light_gray": "K'anch'etan karatiente ta base", "block.minecraft.banner.gradient_up.lime": "Yoxyoxtik karatiente ta base", "block.minecraft.banner.gradient_up.magenta": "Tsojtsojtik karatiente ta base", "block.minecraft.banner.gradient_up.orange": "K'onk'ontik karatiente ta base", "block.minecraft.banner.gradient_up.pink": "Tsojtsojtik karatiente ta base", "block.minecraft.banner.gradient_up.purple": "Ik'pok'an karatiente ta base", "block.minecraft.banner.gradient_up.red": "Tsajal karat<PERSON>e ta base", "block.minecraft.banner.gradient_up.white": "Sakil karatiente ta base", "block.minecraft.banner.gradient_up.yellow": "K'anal karatiente ta base", "block.minecraft.banner.guster.black": "<PERSON><PERSON>'al sutub-ik'", "block.minecraft.banner.guster.blue": "Yaxal sutub-ik'", "block.minecraft.banner.guster.brown": "K'anch'etan sutub-ik'", "block.minecraft.banner.guster.cyan": "Yax-elan su<PERSON>-ik'", "block.minecraft.banner.guster.gray": "K'anch'etan sutub-ik'", "block.minecraft.banner.guster.green": "Yaxal sutub-ik'", "block.minecraft.banner.guster.light_blue": "Yaxal sutub-ik'", "block.minecraft.banner.guster.light_gray": "K'anch'etan sutub-ik'", "block.minecraft.banner.guster.lime": "Yoxyoxtik sutub-ik'", "block.minecraft.banner.guster.magenta": "Tsojtsojtik sutub-ik'", "block.minecraft.banner.guster.orange": "K'onk'ontik sutub-ik'", "block.minecraft.banner.guster.pink": "Tsojtsojtik sutub-ik'", "block.minecraft.banner.guster.purple": "Ik'pok'an sutub-ik'", "block.minecraft.banner.guster.red": "<PERSON><PERSON><PERSON><PERSON>i<PERSON>'", "block.minecraft.banner.guster.white": "Sakil sutub-ik'", "block.minecraft.banner.guster.yellow": "K'anal sutub-ik'", "block.minecraft.banner.half_horizontal.black": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.half_horizontal.blue": "<PERSON><PERSON><PERSON>el", "block.minecraft.banner.half_horizontal.brown": "K'anch'etan setel", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON>-<PERSON><PERSON> setel", "block.minecraft.banner.half_horizontal.gray": "K'anch'etan setel", "block.minecraft.banner.half_horizontal.green": "<PERSON><PERSON><PERSON>el", "block.minecraft.banner.half_horizontal.light_blue": "<PERSON><PERSON><PERSON>el", "block.minecraft.banner.half_horizontal.light_gray": "K'anch'etan setel", "block.minecraft.banner.half_horizontal.lime": "Yoxyoxtik setel", "block.minecraft.banner.half_horizontal.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.orange": "K'onk'ontik setel", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.purple": "Ik'pok'an setel", "block.minecraft.banner.half_horizontal.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON><PERSON> setel", "block.minecraft.banner.half_horizontal.yellow": "K'anal setel", "block.minecraft.banner.half_horizontal_bottom.black": "<PERSON><PERSON>'un<PERSON> i<PERSON>'al setel", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> yaxal setel", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON><PERSON>'un<PERSON> k'anch'etan setel", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> yax-elan setel", "block.minecraft.banner.half_horizontal_bottom.gray": "<PERSON><PERSON>'un<PERSON> k'anch'etan setel", "block.minecraft.banner.half_horizontal_bottom.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> yaxal setel", "block.minecraft.banner.half_horizontal_bottom.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> yaxal setel", "block.minecraft.banner.half_horizontal_bottom.light_gray": "<PERSON><PERSON>'un<PERSON> k'anch'etan setel", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON><PERSON>'unbil yoxyoxtik setel", "block.minecraft.banner.half_horizontal_bottom.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.orange": "Valk'unbil k'onk'ontik setel", "block.minecraft.banner.half_horizontal_bottom.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.purple": "Valk'unbil ik'pok'an setel", "block.minecraft.banner.half_horizontal_bottom.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>el", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> sakil setel", "block.minecraft.banner.half_horizontal_bottom.yellow": "<PERSON><PERSON>'un<PERSON> k'anal setel", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON><PERSON><PERSON> javel", "block.minecraft.banner.half_vertical.blue": "<PERSON><PERSON><PERSON> javel", "block.minecraft.banner.half_vertical.brown": "K'anch'etan javel", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> javel", "block.minecraft.banner.half_vertical.gray": "K'anch'etan javel", "block.minecraft.banner.half_vertical.green": "<PERSON><PERSON><PERSON> javel", "block.minecraft.banner.half_vertical.light_blue": "<PERSON><PERSON><PERSON> javel", "block.minecraft.banner.half_vertical.light_gray": "K'anch'etan javel", "block.minecraft.banner.half_vertical.lime": "Yoxyoxtik javel", "block.minecraft.banner.half_vertical.magenta": "T<PERSON>jtsojtik javel", "block.minecraft.banner.half_vertical.orange": "K'onk'ontik javel", "block.minecraft.banner.half_vertical.pink": "T<PERSON>jtsojtik javel", "block.minecraft.banner.half_vertical.purple": "<PERSON>k'pok'an javel", "block.minecraft.banner.half_vertical.red": "<PERSON>k'pok'an javel", "block.minecraft.banner.half_vertical.white": "Sa<PERSON>l javel", "block.minecraft.banner.half_vertical.yellow": "<PERSON>'<PERSON> javel", "block.minecraft.banner.half_vertical_right.black": "Valk'un<PERSON> i<PERSON>'al javel", "block.minecraft.banner.half_vertical_right.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> yaxal javel", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON>'un<PERSON> k'anch'etan javel", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> yax-elan javel", "block.minecraft.banner.half_vertical_right.gray": "<PERSON><PERSON>'un<PERSON> k'anch'etan javel", "block.minecraft.banner.half_vertical_right.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> yaxal javel", "block.minecraft.banner.half_vertical_right.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> yaxal javel", "block.minecraft.banner.half_vertical_right.light_gray": "<PERSON><PERSON>'un<PERSON> k'anch'etan javel", "block.minecraft.banner.half_vertical_right.lime": "<PERSON><PERSON>'un<PERSON> yoxyox<PERSON>k javel", "block.minecraft.banner.half_vertical_right.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> javel", "block.minecraft.banner.half_vertical_right.orange": "Valk'unbil k'onk'ontik javel", "block.minecraft.banner.half_vertical_right.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> javel", "block.minecraft.banner.half_vertical_right.purple": "Valk'unbil ik'pok'an javel", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>l javel", "block.minecraft.banner.half_vertical_right.white": "<PERSON><PERSON><PERSON><PERSON><PERSON> sakil javel", "block.minecraft.banner.half_vertical_right.yellow": "<PERSON><PERSON><PERSON><PERSON><PERSON> k'anal javel", "block.minecraft.banner.mojang.black": "<PERSON><PERSON><PERSON><PERSON> k'usi", "block.minecraft.banner.mojang.blue": "<PERSON><PERSON><PERSON> k'usi", "block.minecraft.banner.mojang.brown": "K'anch'etan k'usi", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON>-<PERSON><PERSON> k'usi", "block.minecraft.banner.mojang.gray": "K'anch'etan k'usi", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON> k'usi", "block.minecraft.banner.mojang.light_blue": "<PERSON><PERSON><PERSON> k'usi", "block.minecraft.banner.mojang.light_gray": "K'anch'etan k'usi", "block.minecraft.banner.mojang.lime": "Yoxyoxtik k'usi", "block.minecraft.banner.mojang.magenta": "T<PERSON>jtsojtik k'usi", "block.minecraft.banner.mojang.orange": "K'onk'ontik k'usi", "block.minecraft.banner.mojang.pink": "T<PERSON>jtsojtik k'usi", "block.minecraft.banner.mojang.purple": "Ik'pok'an k'usi", "block.minecraft.banner.mojang.red": "<PERSON><PERSON><PERSON><PERSON>'usi", "block.minecraft.banner.mojang.white": "Sa<PERSON><PERSON> k'usi", "block.minecraft.banner.mojang.yellow": "<PERSON>'<PERSON> k'usi", "block.minecraft.banner.piglin.black": "<PERSON><PERSON><PERSON>al eil", "block.minecraft.banner.piglin.blue": "Yaxal eil", "block.minecraft.banner.piglin.brown": "K'anch'etan eil", "block.minecraft.banner.piglin.cyan": "<PERSON><PERSON>-elan eil", "block.minecraft.banner.piglin.gray": "K'anch'etan eil", "block.minecraft.banner.piglin.green": "Yaxal eil", "block.minecraft.banner.piglin.light_blue": "Yaxal eil", "block.minecraft.banner.piglin.light_gray": "K'anch'etan eil", "block.minecraft.banner.piglin.lime": "Yoxyoxtik eil", "block.minecraft.banner.piglin.magenta": "Tsojtsojtik eil", "block.minecraft.banner.piglin.orange": "K'onk'ontik eil", "block.minecraft.banner.piglin.pink": "Tsojtsojtik eil", "block.minecraft.banner.piglin.purple": "Ik'pok'an eil", "block.minecraft.banner.piglin.red": "<PERSON><PERSON><PERSON><PERSON> eil", "block.minecraft.banner.piglin.white": "Sakil eil", "block.minecraft.banner.piglin.yellow": "K'anal eil", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.brown": "<PERSON>'an<PERSON>'et<PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "<PERSON>'an<PERSON>'et<PERSON>", "block.minecraft.banner.rhombus.green": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_blue": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.light_gray": "<PERSON>'an<PERSON>'et<PERSON>", "block.minecraft.banner.rhombus.lime": "Yoxyoxtik losanje", "block.minecraft.banner.rhombus.magenta": "Tsojtsojtik losanje", "block.minecraft.banner.rhombus.orange": "K'onk'ontik losanje", "block.minecraft.banner.rhombus.pink": "Tsojtsojtik losanje", "block.minecraft.banner.rhombus.purple": "Ik'pok'an los<PERSON>je", "block.minecraft.banner.rhombus.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.black": "<PERSON><PERSON><PERSON><PERSON> bakil jolil", "block.minecraft.banner.skull.blue": "<PERSON><PERSON><PERSON> bakil jolil", "block.minecraft.banner.skull.brown": "<PERSON>'an<PERSON>'<PERSON>an bakil jolil", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON>-<PERSON><PERSON> bakil jolil", "block.minecraft.banner.skull.gray": "<PERSON>'an<PERSON>'<PERSON>an bakil jolil", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON> bakil jolil", "block.minecraft.banner.skull.light_blue": "<PERSON><PERSON><PERSON> bakil jolil", "block.minecraft.banner.skull.light_gray": "<PERSON>'an<PERSON>'<PERSON>an bakil jolil", "block.minecraft.banner.skull.lime": "Yoxyoxtik bakil jolil", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bakil jolil", "block.minecraft.banner.skull.orange": "K'onk'ontik bakil jolil", "block.minecraft.banner.skull.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bakil jolil", "block.minecraft.banner.skull.purple": "<PERSON><PERSON>'p<PERSON>'an bakil jolil", "block.minecraft.banner.skull.red": "<PERSON><PERSON><PERSON><PERSON> bakil j<PERSON>l", "block.minecraft.banner.skull.white": "<PERSON><PERSON><PERSON> bakil jolil", "block.minecraft.banner.skull.yellow": "<PERSON><PERSON><PERSON> bakil jolil", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON><PERSON><PERSON> nam<PERSON>'al", "block.minecraft.banner.small_stripes.blue": "<PERSON><PERSON><PERSON>al", "block.minecraft.banner.small_stripes.brown": "<PERSON>'an<PERSON>'etan nam<PERSON>'al", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>al", "block.minecraft.banner.small_stripes.gray": "<PERSON>'an<PERSON>'etan nam<PERSON>'al", "block.minecraft.banner.small_stripes.green": "<PERSON><PERSON><PERSON>al", "block.minecraft.banner.small_stripes.light_blue": "<PERSON><PERSON><PERSON>al", "block.minecraft.banner.small_stripes.light_gray": "<PERSON>'an<PERSON>'etan nam<PERSON>'al", "block.minecraft.banner.small_stripes.lime": "Yoxyoxtik namte'al", "block.minecraft.banner.small_stripes.magenta": "Tsojtsojtik namte'al", "block.minecraft.banner.small_stripes.orange": "K'onk'ontik namte'al", "block.minecraft.banner.small_stripes.pink": "Tsojtsojtik namte'al", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON>'pok'an namte'al", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.white": "Sakil nam<PERSON>'al", "block.minecraft.banner.small_stripes.yellow": "<PERSON>'<PERSON> namte'al", "block.minecraft.banner.square_bottom_left.black": "Ik'al bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.blue": "Yaxal bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.brown": "K'anch'etan bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.cyan": "Yax-elan bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.gray": "K'anch'etan bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.green": "Yaxal bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.light_blue": "Yaxal bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.light_gray": "K'anch'etan bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.lime": "Yoxyoxtik bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.magenta": "Tsojtsojtik bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.orange": "K'onk'ontik bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.pink": "Tsojtsojtik bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.purple": "Ik'pok'an bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.red": "Tsajal bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.white": "Sakil bats'i kanton ta base", "block.minecraft.banner.square_bottom_left.yellow": "K'anal bats'i kanton ta base", "block.minecraft.banner.square_bottom_right.black": "Ik'al ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.blue": "Yaxal ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.brown": "K'anch'etan ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.cyan": "Yax-elan ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.gray": "K'anch'etan ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.green": "Yaxal ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.light_blue": "Yaxal ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.light_gray": "K'anch'etan ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.lime": "Yoxyoxtik ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.magenta": "Tsojtsojtik ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.orange": "K'onk'ontik ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.pink": "Tsojtsojtik ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.purple": "Ik'pok'an ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.red": "T<PERSON><PERSON>l ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.white": "Sakil ts'et kanton ta base", "block.minecraft.banner.square_bottom_right.yellow": "K'anal ts'et kanton ta base", "block.minecraft.banner.square_top_left.black": "Ik'al bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.blue": "Yaxal bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.brown": "K'anch'etan bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.cyan": "Yax-elan bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.gray": "K'anch'etan bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.green": "Yaxal bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.light_blue": "Yaxal bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.light_gray": "K'anch'etan bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.lime": "Yoxyoxtik bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.magenta": "Tsojtsojtik bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.orange": "K'onk'ontik bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.pink": "Tsojtsojtik bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.purple": "Ik'pok'an bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.red": "Tsajal bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.white": "Sakil bats'i kanton ta jolil", "block.minecraft.banner.square_top_left.yellow": "K'anal bats'i kanton ta jolil", "block.minecraft.banner.square_top_right.black": "Ik'al ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.blue": "Yaxal ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.brown": "K'anch'etan ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.cyan": "Yax-elan ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.gray": "K'anch'etan ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.green": "Yaxal ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.light_blue": "Yaxal ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.light_gray": "K'anch'etan ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.lime": "Yoxyoxtik ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.magenta": "T<PERSON>jtsojtik ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.orange": "K'onk'ontik ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.pink": "T<PERSON>jtsojtik ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.purple": "Ik'pok'an ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON><PERSON><PERSON> ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.white": "Sakil ts'et kanton ta jolil", "block.minecraft.banner.square_top_right.yellow": "K'anal ts'et kanton ta jolil", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON><PERSON><PERSON> kurus", "block.minecraft.banner.straight_cross.blue": "Yaxal kurus", "block.minecraft.banner.straight_cross.brown": "K'anch'etan kurus", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON>-<PERSON><PERSON> kurus", "block.minecraft.banner.straight_cross.gray": "K'anch'etan kurus", "block.minecraft.banner.straight_cross.green": "Yaxal kurus", "block.minecraft.banner.straight_cross.light_blue": "Yaxal kurus", "block.minecraft.banner.straight_cross.light_gray": "K'anch'etan kurus", "block.minecraft.banner.straight_cross.lime": "Yoxyoxtik kurus", "block.minecraft.banner.straight_cross.magenta": "Tsojtsojtik kurus", "block.minecraft.banner.straight_cross.orange": "K'onk'ontik kurus", "block.minecraft.banner.straight_cross.pink": "Tsojtsojtik kurus", "block.minecraft.banner.straight_cross.purple": "Ik'pok'an kurus", "block.minecraft.banner.straight_cross.red": "<PERSON><PERSON><PERSON><PERSON> kurus", "block.minecraft.banner.straight_cross.white": "Sakil kurus", "block.minecraft.banner.straight_cross.yellow": "K'anal kurus", "block.minecraft.banner.stripe_bottom.black": "Ik'al base", "block.minecraft.banner.stripe_bottom.blue": "Yaxal base", "block.minecraft.banner.stripe_bottom.brown": "K'anch'etan base", "block.minecraft.banner.stripe_bottom.cyan": "Yax-elan base", "block.minecraft.banner.stripe_bottom.gray": "K'anch'etan base", "block.minecraft.banner.stripe_bottom.green": "Yaxal base", "block.minecraft.banner.stripe_bottom.light_blue": "Yaxal base", "block.minecraft.banner.stripe_bottom.light_gray": "K'anch'etan base", "block.minecraft.banner.stripe_bottom.lime": "Yoxyoxtik base", "block.minecraft.banner.stripe_bottom.magenta": "Tsojtsojtik base", "block.minecraft.banner.stripe_bottom.orange": "K'onk'ontik base", "block.minecraft.banner.stripe_bottom.pink": "Tsojtsojtik base", "block.minecraft.banner.stripe_bottom.purple": "Ik'pok'an base", "block.minecraft.banner.stripe_bottom.red": "Tsajal base", "block.minecraft.banner.stripe_bottom.white": "Sakil base", "block.minecraft.banner.stripe_bottom.yellow": "K'anal base", "block.minecraft.banner.stripe_center.black": "Ik'al te'", "block.minecraft.banner.stripe_center.blue": "Yaxal te'", "block.minecraft.banner.stripe_center.brown": "K'anch'etan te'", "block.minecraft.banner.stripe_center.cyan": "Yax-elan te'", "block.minecraft.banner.stripe_center.gray": "K'anch'etan te'", "block.minecraft.banner.stripe_center.green": "Yaxal te'", "block.minecraft.banner.stripe_center.light_blue": "Yaxal te'", "block.minecraft.banner.stripe_center.light_gray": "K'anch'etan te'", "block.minecraft.banner.stripe_center.lime": "Yoxyoxtik te'", "block.minecraft.banner.stripe_center.magenta": "Tsojtsojtik te'", "block.minecraft.banner.stripe_center.orange": "K'onk'ontik te'", "block.minecraft.banner.stripe_center.pink": "Tsojtsojtik te'", "block.minecraft.banner.stripe_center.purple": "Ik'pok'an te'", "block.minecraft.banner.stripe_center.red": "<PERSON><PERSON><PERSON><PERSON> te'", "block.minecraft.banner.stripe_center.white": "Sakil te'", "block.minecraft.banner.stripe_center.yellow": "K'anal te'", "block.minecraft.banner.stripe_downleft.black": "Ik'al te'eltak'in", "block.minecraft.banner.stripe_downleft.blue": "Yaxal te'eltak'in", "block.minecraft.banner.stripe_downleft.brown": "K'anch'etan te'eltak'in", "block.minecraft.banner.stripe_downleft.cyan": "Yax-elan te'eltak'in", "block.minecraft.banner.stripe_downleft.gray": "K'anch'etan te'eltak'in", "block.minecraft.banner.stripe_downleft.green": "Yaxal te'eltak'in", "block.minecraft.banner.stripe_downleft.light_blue": "Yaxal te'eltak'in", "block.minecraft.banner.stripe_downleft.light_gray": "K'anch'etan te'eltak'in", "block.minecraft.banner.stripe_downleft.lime": "Yoxyoxtik te'eltak'in", "block.minecraft.banner.stripe_downleft.magenta": "T<PERSON>jtsojtik te'eltak'in", "block.minecraft.banner.stripe_downleft.orange": "K'onk'ontik te'eltak'in", "block.minecraft.banner.stripe_downleft.pink": "T<PERSON>jtsojtik te'eltak'in", "block.minecraft.banner.stripe_downleft.purple": "Ik'pok'an te'eltak'in", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON><PERSON><PERSON> te'eltak'in", "block.minecraft.banner.stripe_downleft.white": "Sakil te'eltak'in", "block.minecraft.banner.stripe_downleft.yellow": "K'anal te'eltak'in", "block.minecraft.banner.stripe_downright.black": "<PERSON><PERSON><PERSON><PERSON> tabpat", "block.minecraft.banner.stripe_downright.blue": "Yaxal tabpat", "block.minecraft.banner.stripe_downright.brown": "K'anch'etan tabpat", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON>-<PERSON><PERSON> tabpat", "block.minecraft.banner.stripe_downright.gray": "K'anch'etan tabpat", "block.minecraft.banner.stripe_downright.green": "Yaxal tabpat", "block.minecraft.banner.stripe_downright.light_blue": "Yaxal tabpat", "block.minecraft.banner.stripe_downright.light_gray": "K'anch'etan tabpat", "block.minecraft.banner.stripe_downright.lime": "Yoxyoxtik tabpat", "block.minecraft.banner.stripe_downright.magenta": "Tsojtsojtik tabpat", "block.minecraft.banner.stripe_downright.orange": "K'onk'ontik tabpat", "block.minecraft.banner.stripe_downright.pink": "Tsojtsojtik tabpat", "block.minecraft.banner.stripe_downright.purple": "<PERSON>k'pok'an tabpat", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON><PERSON><PERSON> tabpat", "block.minecraft.banner.stripe_downright.white": "Sakil tabpat", "block.minecraft.banner.stripe_downright.yellow": "K'anal tabpat", "block.minecraft.banner.stripe_left.black": "Ik'al te' ta bats'i", "block.minecraft.banner.stripe_left.blue": "Yaxal te' ta bats'i", "block.minecraft.banner.stripe_left.brown": "K'anch'etan te' ta bats'i", "block.minecraft.banner.stripe_left.cyan": "Yax-elan te' ta bats'i", "block.minecraft.banner.stripe_left.gray": "K'anch'etan te' ta bats'i", "block.minecraft.banner.stripe_left.green": "Yaxal te' ta bats'i", "block.minecraft.banner.stripe_left.light_blue": "Yaxal te' ta bats'i", "block.minecraft.banner.stripe_left.light_gray": "K'anch'etan te' ta bats'i", "block.minecraft.banner.stripe_left.lime": "Yoxyoxtik te' ta bats'i", "block.minecraft.banner.stripe_left.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>jtik te' ta bats'i", "block.minecraft.banner.stripe_left.orange": "K'onk'ontik te' ta bats'i", "block.minecraft.banner.stripe_left.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>jtik te' ta bats'i", "block.minecraft.banner.stripe_left.purple": "Ik'pok'an te' ta bats'i", "block.minecraft.banner.stripe_left.red": "<PERSON><PERSON><PERSON><PERSON> te' ta bats'i", "block.minecraft.banner.stripe_left.white": "Sakil te' ta bats'i", "block.minecraft.banner.stripe_left.yellow": "K'anal te' ta bats'i", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON><PERSON><PERSON> xinchail", "block.minecraft.banner.stripe_middle.blue": "Yaxal xinchail", "block.minecraft.banner.stripe_middle.brown": "K'anch'etan xinchail", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON>-<PERSON><PERSON> x<PERSON>il", "block.minecraft.banner.stripe_middle.gray": "K'anch'etan xinchail", "block.minecraft.banner.stripe_middle.green": "Yaxal xinchail", "block.minecraft.banner.stripe_middle.light_blue": "Yaxal xinchail", "block.minecraft.banner.stripe_middle.light_gray": "K'anch'etan xinchail", "block.minecraft.banner.stripe_middle.lime": "Yoxyoxtik xinchail", "block.minecraft.banner.stripe_middle.magenta": "Tsojtsojtik xinchail", "block.minecraft.banner.stripe_middle.orange": "K'onk'ontik xinchail", "block.minecraft.banner.stripe_middle.pink": "Tsojtsojtik xinchail", "block.minecraft.banner.stripe_middle.purple": "<PERSON>k'pok'an xinchail", "block.minecraft.banner.stripe_middle.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.white": "Sakil xinchail", "block.minecraft.banner.stripe_middle.yellow": "K'anal xinchail", "block.minecraft.banner.stripe_right.black": "Ik'al te' ta ts'et", "block.minecraft.banner.stripe_right.blue": "Yaxal te' ta ts'et", "block.minecraft.banner.stripe_right.brown": "K'anch'etan te' ta ts'et", "block.minecraft.banner.stripe_right.cyan": "Yax-elan te' ta ts'et", "block.minecraft.banner.stripe_right.gray": "K'anch'etan te' ta ts'et", "block.minecraft.banner.stripe_right.green": "Yaxal te' ta ts'et", "block.minecraft.banner.stripe_right.light_blue": "Yaxal te' ta ts'et", "block.minecraft.banner.stripe_right.light_gray": "K'anch'etan te' ta ts'et", "block.minecraft.banner.stripe_right.lime": "Yoxyoxtik te' ta ts'et", "block.minecraft.banner.stripe_right.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON> te' ta ts'et", "block.minecraft.banner.stripe_right.orange": "K'onk'ontik te' ta ts'et", "block.minecraft.banner.stripe_right.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON> te' ta ts'et", "block.minecraft.banner.stripe_right.purple": "Ik'pok'an te' ta ts'et", "block.minecraft.banner.stripe_right.red": "T<PERSON><PERSON><PERSON> te' ta ts'et", "block.minecraft.banner.stripe_right.white": "Sakil te' ta ts'et", "block.minecraft.banner.stripe_right.yellow": "K'anal te' ta ts'et", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON><PERSON><PERSON> jolil", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON> j<PERSON>l", "block.minecraft.banner.stripe_top.brown": "<PERSON>'an<PERSON>'<PERSON>an jolil", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>l", "block.minecraft.banner.stripe_top.gray": "<PERSON>'an<PERSON>'<PERSON>an jolil", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON> j<PERSON>l", "block.minecraft.banner.stripe_top.light_blue": "<PERSON><PERSON><PERSON> j<PERSON>l", "block.minecraft.banner.stripe_top.light_gray": "<PERSON>'an<PERSON>'<PERSON>an jolil", "block.minecraft.banner.stripe_top.lime": "Yoxyoxtik jolil", "block.minecraft.banner.stripe_top.magenta": "Tsojtsojtik jolil", "block.minecraft.banner.stripe_top.orange": "K'onk'ontik jolil", "block.minecraft.banner.stripe_top.pink": "Tsojtsojtik jolil", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON>'pok'an jolil", "block.minecraft.banner.stripe_top.red": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON> jolil", "block.minecraft.banner.stripe_top.yellow": "<PERSON><PERSON><PERSON> jolil", "block.minecraft.banner.triangle_bottom.black": "<PERSON><PERSON><PERSON>al chevron", "block.minecraft.banner.triangle_bottom.blue": "Yaxal chevron", "block.minecraft.banner.triangle_bottom.brown": "K'anch'etan chevron", "block.minecraft.banner.triangle_bottom.cyan": "Yax-elan chevron", "block.minecraft.banner.triangle_bottom.gray": "K'anch'etan chevron", "block.minecraft.banner.triangle_bottom.green": "Yaxal chevron", "block.minecraft.banner.triangle_bottom.light_blue": "Yaxal chevron", "block.minecraft.banner.triangle_bottom.light_gray": "K'anch'etan chevron", "block.minecraft.banner.triangle_bottom.lime": "Yoxyoxtik chevron", "block.minecraft.banner.triangle_bottom.magenta": "Tsojtsojtik chevron", "block.minecraft.banner.triangle_bottom.orange": "K'onk'ontik chevron", "block.minecraft.banner.triangle_bottom.pink": "Tsojtsojtik chevron", "block.minecraft.banner.triangle_bottom.purple": "Ik'pok'an chevron", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON><PERSON> chevron", "block.minecraft.banner.triangle_bottom.white": "Sakil chevron", "block.minecraft.banner.triangle_bottom.yellow": "K'anal chevron", "block.minecraft.banner.triangle_top.black": "Valk'unbil ik'al chevron", "block.minecraft.banner.triangle_top.blue": "<PERSON><PERSON>'<PERSON><PERSON> yaxal chevron", "block.minecraft.banner.triangle_top.brown": "<PERSON><PERSON>'un<PERSON> k'anch'etan chevron", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON> yax-elan chevron", "block.minecraft.banner.triangle_top.gray": "<PERSON><PERSON>'un<PERSON> k'anch'etan chevron", "block.minecraft.banner.triangle_top.green": "<PERSON><PERSON>'<PERSON><PERSON> yaxal chevron", "block.minecraft.banner.triangle_top.light_blue": "<PERSON><PERSON>'<PERSON><PERSON> yaxal chevron", "block.minecraft.banner.triangle_top.light_gray": "<PERSON><PERSON>'un<PERSON> k'anch'etan chevron", "block.minecraft.banner.triangle_top.lime": "Valk'unbil yoxyoxtik chevron", "block.minecraft.banner.triangle_top.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> chevron", "block.minecraft.banner.triangle_top.orange": "Valk'unbil k'onk'ontik chevron", "block.minecraft.banner.triangle_top.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> chevron", "block.minecraft.banner.triangle_top.purple": "Valk'unbil ik'pok'an chevron", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON><PERSON><PERSON><PERSON> tsajal chevron", "block.minecraft.banner.triangle_top.white": "Val<PERSON>'unbil sakil chevron", "block.minecraft.banner.triangle_top.yellow": "<PERSON><PERSON>'unbil k'anal chevron", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON>'al tanal-eal base", "block.minecraft.banner.triangles_bottom.blue": "Yaxal tanal-eal base", "block.minecraft.banner.triangles_bottom.brown": "K'anch'etan tanal-eal base", "block.minecraft.banner.triangles_bottom.cyan": "Yax-elan tanal-eal base", "block.minecraft.banner.triangles_bottom.gray": "K'anch'etan tanal-eal base", "block.minecraft.banner.triangles_bottom.green": "Yaxal tanal-eal base", "block.minecraft.banner.triangles_bottom.light_blue": "Yaxal tanal-eal base", "block.minecraft.banner.triangles_bottom.light_gray": "K'anch'etan tanal-eal base", "block.minecraft.banner.triangles_bottom.lime": "Yoxyoxtik tanal-eal base", "block.minecraft.banner.triangles_bottom.magenta": "Tsojtsojtik tanal-eal base", "block.minecraft.banner.triangles_bottom.orange": "K'onk'ontik tanal-eal base", "block.minecraft.banner.triangles_bottom.pink": "Tsojtsojtik tanal-eal base", "block.minecraft.banner.triangles_bottom.purple": "Ik'pok'an tanal-eal base", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON><PERSON><PERSON> tanal-eal base", "block.minecraft.banner.triangles_bottom.white": "Sakil tanal-eal base", "block.minecraft.banner.triangles_bottom.yellow": "K'anal tanal-eal base", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON><PERSON><PERSON> tanal-eal jolil", "block.minecraft.banner.triangles_top.blue": "<PERSON><PERSON><PERSON> tanal-eal jolil", "block.minecraft.banner.triangles_top.brown": "<PERSON>'anch'etan tanal-eal jolil", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON>-<PERSON>an tanal-eal jolil", "block.minecraft.banner.triangles_top.gray": "<PERSON>'anch'etan tanal-eal jolil", "block.minecraft.banner.triangles_top.green": "<PERSON><PERSON><PERSON> tanal-eal jolil", "block.minecraft.banner.triangles_top.light_blue": "<PERSON><PERSON><PERSON> tanal-eal jolil", "block.minecraft.banner.triangles_top.light_gray": "<PERSON>'anch'etan tanal-eal jolil", "block.minecraft.banner.triangles_top.lime": "Yoxyoxtik tanal-eal jolil", "block.minecraft.banner.triangles_top.magenta": "T<PERSON>jts<PERSON>j<PERSON><PERSON> tanal-eal jolil", "block.minecraft.banner.triangles_top.orange": "K'onk'ontik tanal-eal jolil", "block.minecraft.banner.triangles_top.pink": "T<PERSON>jts<PERSON>j<PERSON><PERSON> tanal-eal jolil", "block.minecraft.banner.triangles_top.purple": "<PERSON><PERSON>'pok'an tanal-eal jolil", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON><PERSON><PERSON> tan<PERSON>-e<PERSON> jolil", "block.minecraft.banner.triangles_top.white": "<PERSON><PERSON><PERSON> tanal-eal jolil", "block.minecraft.banner.triangles_top.yellow": "<PERSON>'<PERSON> tanal-eal jolil", "block.minecraft.barrel": "<PERSON><PERSON><PERSON>", "block.minecraft.barrier": "Varera", "block.minecraft.basalt": "Vasalt<PERSON>", "block.minecraft.beacon": "J<PERSON>", "block.minecraft.beacon.primary": "Ba'yel ju'el", "block.minecraft.beacon.secondary": "Xchibal ju'el", "block.minecraft.bed.no_sleep": "Xu' no'ox chavay ta ak'obal o ta ik'al-o'etik", "block.minecraft.bed.not_safe": "<PERSON> xu' xavay tana: oy monstroetik ta nopol", "block.minecraft.bed.obstructed": "<PERSON><PERSON><PERSON> li tem li'e", "block.minecraft.bed.occupied": "Mu xokol li tem li'e", "block.minecraft.bed.too_far_away": "<PERSON> xu' xavay tana: toj nom li teme", "block.minecraft.bedrock": "Bats'i ton", "block.minecraft.bee_nest": "Na pom", "block.minecraft.beehive": "Nail x<PERSON> pom", "block.minecraft.beetroots": "<PERSON><PERSON><PERSON>", "block.minecraft.bell": "Kampana", "block.minecraft.big_dripleaf": "Muk'ta toyte'", "block.minecraft.big_dripleaf_stem": "<PERSON>ch'ut muk'ta toyte'", "block.minecraft.birch_button": "Voton averul", "block.minecraft.birch_door": "Ti'na averul", "block.minecraft.birch_fence": "Mok averul", "block.minecraft.birch_fence_gate": "Ti' mok averul", "block.minecraft.birch_hanging_sign": "<PERSON><PERSON><PERSON> let<PERSON>o averul", "block.minecraft.birch_leaves": "Yanaltak averul", "block.minecraft.birch_log": "Chumante'el averul", "block.minecraft.birch_planks": "Tenelte'el averuletik", "block.minecraft.birch_pressure_plate": "Plakail net'el averul", "block.minecraft.birch_sapling": "<PERSON>'elom averul", "block.minecraft.birch_sign": "Leterero averul", "block.minecraft.birch_slab": "Losa averul", "block.minecraft.birch_stairs": "Ixkalera averul", "block.minecraft.birch_trapdoor": "Ti'pets' averul", "block.minecraft.birch_wall_hanging_sign": "<PERSON><PERSON><PERSON> leterero averul ta kubo", "block.minecraft.birch_wall_sign": "Leterero averul ta kubo", "block.minecraft.birch_wood": "Te'el averul", "block.minecraft.black_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_bed": "<PERSON><PERSON><PERSON>al tem", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON><PERSON> kantela", "block.minecraft.black_candle_cake": "Pastel xchi'uk ik'al kantela", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON><PERSON> tapete", "block.minecraft.black_concrete": "<PERSON><PERSON><PERSON><PERSON> konker<PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON><PERSON><PERSON> semento", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON>al kaxa yu'un shulker", "block.minecraft.black_stained_glass": "<PERSON><PERSON><PERSON><PERSON> nen", "block.minecraft.black_stained_glass_pane": "Ik'al <PERSON> nen", "block.minecraft.black_terracotta": "<PERSON><PERSON>'al lakal ach'el", "block.minecraft.black_wool": "<PERSON><PERSON><PERSON><PERSON> tsots", "block.minecraft.blackstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.blackstone_slab": "Losa i<PERSON>'alton", "block.minecraft.blackstone_stairs": "Ixkalera ik'alton", "block.minecraft.blackstone_wall": "<PERSON>'bel na ik'alton", "block.minecraft.blast_furnace": "<PERSON><PERSON>'ta jorno", "block.minecraft.blue_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_bed": "Yaxal tem", "block.minecraft.blue_candle": "<PERSON><PERSON><PERSON> kantela", "block.minecraft.blue_candle_cake": "Pastel xchi'uk yaxal kantela", "block.minecraft.blue_carpet": "Yaxal tapete", "block.minecraft.blue_concrete": "<PERSON><PERSON><PERSON> kon<PERSON>", "block.minecraft.blue_concrete_powder": "Yaxal semento", "block.minecraft.blue_glazed_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_ice": "Yaxal taiv", "block.minecraft.blue_orchid": "Yaxal ech'", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON> kaxa yu'un shulker", "block.minecraft.blue_stained_glass": "<PERSON><PERSON><PERSON> nen", "block.minecraft.blue_stained_glass_pane": "Yaxal panel nen", "block.minecraft.blue_terracotta": "<PERSON>xal lakal ach'el", "block.minecraft.blue_wool": "<PERSON><PERSON><PERSON> tsots", "block.minecraft.bone_block": "<PERSON><PERSON> bak", "block.minecraft.bookshelf": "<PERSON><PERSON><PERSON>", "block.minecraft.brain_coral": "<PERSON><PERSON>'", "block.minecraft.brain_coral_block": "<PERSON><PERSON> chinab nabalte'", "block.minecraft.brain_coral_fan": "<PERSON>b korkonia", "block.minecraft.brain_coral_wall_fan": "Chinab korkonia ta kubo", "block.minecraft.brewing_stand": "<PERSON><PERSON><PERSON>", "block.minecraft.brick_slab": "<PERSON><PERSON> la<PERSON>yo", "block.minecraft.brick_stairs": "Ixkalera latriyo", "block.minecraft.brick_wall": "<PERSON>'bel na latriyo", "block.minecraft.bricks": "Latriyoetik", "block.minecraft.brown_banner": "<PERSON>'<PERSON><PERSON>'<PERSON><PERSON>", "block.minecraft.brown_bed": "K'anch'etan tem", "block.minecraft.brown_candle": "K'anch'etan kantela", "block.minecraft.brown_candle_cake": "Pastel xchi'uk k'anch'etan kantela", "block.minecraft.brown_carpet": "K'anch'etan tapete", "block.minecraft.brown_concrete": "K'anch'etan konker<PERSON>", "block.minecraft.brown_concrete_powder": "K'anch'etan semento", "block.minecraft.brown_glazed_terracotta": "<PERSON>'anch'et<PERSON>", "block.minecraft.brown_mushroom": "K'anch'etan moni'", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON> k'anch'etan moni'", "block.minecraft.brown_shulker_box": "K'anch'etan kaxa yu'un shulker", "block.minecraft.brown_stained_glass": "<PERSON>'an<PERSON>'etan nen", "block.minecraft.brown_stained_glass_pane": "K'anch'etan panel nen", "block.minecraft.brown_terracotta": "K'anch'etan lakal ach'el", "block.minecraft.brown_wool": "K'anch'etan tsots", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON> balbu<PERSON>", "block.minecraft.bubble_coral": "Balbunel nabalte'", "block.minecraft.bubble_coral_block": "Kubo balbunel nabalte'", "block.minecraft.bubble_coral_fan": "Balbunel korkonia", "block.minecraft.bubble_coral_wall_fan": "Balbunel korkonia ta kubo", "block.minecraft.budding_amethyst": "Jlok'<PERSON><PERSON><PERSON> am<PERSON>", "block.minecraft.bush": "Xk'ib", "block.minecraft.cactus": "Petok", "block.minecraft.cactus_flower": "<PERSON><PERSON><PERSON> petok", "block.minecraft.cake": "Pastel", "block.minecraft.calcite": "<PERSON><PERSON><PERSON>", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON><PERSON> j-<PERSON>'<PERSON><PERSON><PERSON>-sculk", "block.minecraft.campfire": "K'ok'", "block.minecraft.candle": "<PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "Pastel xchi'uk kantela", "block.minecraft.carrots": "Sanaoriaetik", "block.minecraft.cartography_table": "Mexa lok'tael", "block.minecraft.carved_pumpkin": "<PERSON><PERSON><PERSON> ch'um", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "Ch'enal ik'", "block.minecraft.cave_vines": "Ch'enal ak'etik", "block.minecraft.cave_vines_plant": "Ch'enal ak'", "block.minecraft.chain": "<PERSON><PERSON>", "block.minecraft.chain_command_block": "<PERSON><PERSON> mantal karena", "block.minecraft.cherry_button": "Voton ch'ixtot", "block.minecraft.cherry_door": "Ti'na ch'ixtot", "block.minecraft.cherry_fence": "Mok ch'ixtot", "block.minecraft.cherry_fence_gate": "Ti' mok ch'ixtot", "block.minecraft.cherry_hanging_sign": "<PERSON><PERSON><PERSON> leterero ch'ixtot", "block.minecraft.cherry_leaves": "Yanaltak ch'ixtot", "block.minecraft.cherry_log": "Chumante'el ch'ixtot", "block.minecraft.cherry_planks": "Tenelte'el ch'ixtotetik", "block.minecraft.cherry_pressure_plate": "Plakail net'el ch'ixtot", "block.minecraft.cherry_sapling": "K'elom ch'ixtot", "block.minecraft.cherry_sign": "Leterero ch'ixtot", "block.minecraft.cherry_slab": "Losa ch'ixtot", "block.minecraft.cherry_stairs": "Ixkalera ch'ixtot", "block.minecraft.cherry_trapdoor": "Ti'pets' ch'ixtot", "block.minecraft.cherry_wall_hanging_sign": "<PERSON><PERSON><PERSON> leterero ch'ixtot ta kubo", "block.minecraft.cherry_wall_sign": "Leterero ch'ixtot ta kubo", "block.minecraft.cherry_wood": "Te'el ch'ixtot", "block.minecraft.chest": "<PERSON><PERSON>", "block.minecraft.chipped_anvil": "Sokem yunke", "block.minecraft.chiseled_bookshelf": "Chak'alte'", "block.minecraft.chiseled_copper": "Lok'tabil k'anal k'unil tak'in", "block.minecraft.chiseled_deepslate": "Lok'tabil ik'pulanton", "block.minecraft.chiseled_nether_bricks": "Lok'tabil <PERSON>tri<PERSON>", "block.minecraft.chiseled_polished_blackstone": "Lok'tabil ch'ulbil ik'alton", "block.minecraft.chiseled_quartz_block": "<PERSON>'tabil kuarso", "block.minecraft.chiseled_red_sandstone": "<PERSON><PERSON>ta<PERSON> tsa<PERSON><PERSON>a", "block.minecraft.chiseled_resin_bricks": "Lok'tabil latriyo xuch'etik", "block.minecraft.chiseled_sandstone": "<PERSON><PERSON>ta<PERSON> aren<PERSON>a", "block.minecraft.chiseled_stone_bricks": "Lok'tabil latriyo tonetik", "block.minecraft.chiseled_tuff": "<PERSON>'tabil tova", "block.minecraft.chiseled_tuff_bricks": "Lok'tabil latriyo tova<PERSON>k", "block.minecraft.chorus_flower": "<PERSON><PERSON><PERSON>", "block.minecraft.chorus_plant": "Choruste'", "block.minecraft.clay": "<PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Makal satilal nichim", "block.minecraft.coal_block": "Kubo ta ak'al", "block.minecraft.coal_ore": "Mineral ak'al", "block.minecraft.coarse_dirt": "Chamem lum", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON>", "block.minecraft.cobbled_deepslate_slab": "Losa josbil i<PERSON>'pu<PERSON>on", "block.minecraft.cobbled_deepslate_stairs": "Ixkalera j<PERSON> i<PERSON>'pu<PERSON>on", "block.minecraft.cobbled_deepslate_wall": "Yi'bel na josbil ik'pulanton", "block.minecraft.cobblestone": "Josbil ton", "block.minecraft.cobblestone_slab": "<PERSON>a josbil ton", "block.minecraft.cobblestone_stairs": "Ixkalera josbil <PERSON>", "block.minecraft.cobblestone_wall": "<PERSON>'bel na josbil ton", "block.minecraft.cobweb": "Na'om", "block.minecraft.cocoa": "<PERSON><PERSON>", "block.minecraft.command_block": "<PERSON><PERSON> mantal", "block.minecraft.comparator": "Jko'oltasvanej redstone", "block.minecraft.composter": "Jpas k'a'al lum", "block.minecraft.conduit": "Jpas kanalisar", "block.minecraft.copper_block": "<PERSON>bo ta k'anal k'unil tak'in", "block.minecraft.copper_bulb": "<PERSON><PERSON> k'anal k'unil tak'in", "block.minecraft.copper_door": "Ti'na k'anal k'unil tak'in", "block.minecraft.copper_grate": "<PERSON><PERSON><PERSON> k'anal k'unil tak'in", "block.minecraft.copper_ore": "<PERSON><PERSON> k'anal k'unil tak'in", "block.minecraft.copper_trapdoor": "Ti'pets' k'anal k'unil tak'in", "block.minecraft.cornflower": "Asiano", "block.minecraft.cracked_deepslate_bricks": "Jomol latriyo ik'pulantonetik", "block.minecraft.cracked_deepslate_tiles": "Jo<PERSON><PERSON>ulejo i<PERSON>'pulantonetik", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_polished_blackstone_bricks": "Jo<PERSON><PERSON> ch'ulbil latriyo ik'altonetik", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON> latriyo tonetik", "block.minecraft.crafter": "Meltsanobil", "block.minecraft.crafting_table": "Mexa meltsanel", "block.minecraft.creaking_heart": "Yo'onton jch'ak'ak'etajel", "block.minecraft.creeper_head": "<PERSON><PERSON><PERSON> creeper", "block.minecraft.creeper_wall_head": "<PERSON><PERSON>l creeper ta kubo", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON> voton", "block.minecraft.crimson_door": "<PERSON><PERSON><PERSON><PERSON> ti'na", "block.minecraft.crimson_fence": "<PERSON><PERSON><PERSON><PERSON> mok", "block.minecraft.crimson_fence_gate": "<PERSON><PERSON><PERSON><PERSON> ti' mok", "block.minecraft.crimson_fungus": "Nether moni'", "block.minecraft.crimson_hanging_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_hyphae": "Tsajal ipax", "block.minecraft.crimson_nylium": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_planks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_pressure_plate": "<PERSON><PERSON><PERSON><PERSON> plak<PERSON> net'el", "block.minecraft.crimson_roots": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_sign": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_stem": "<PERSON><PERSON><PERSON><PERSON>'util", "block.minecraft.crimson_trapdoor": "T<PERSON><PERSON><PERSON> ti'pets'", "block.minecraft.crimson_wall_hanging_sign": "<PERSON><PERSON><PERSON><PERSON> ji<PERSON>j leterero ta kubo", "block.minecraft.crimson_wall_sign": "<PERSON><PERSON><PERSON><PERSON> leterero ta kubo", "block.minecraft.crying_obsidian": "J-<PERSON>'el xik' xulem", "block.minecraft.cut_copper": "Mo<PERSON>l k'anal k'unil tak'in", "block.minecraft.cut_copper_slab": "Mo<PERSON>l losa k'anal k'unil tak'in", "block.minecraft.cut_copper_stairs": "Mokol ixkalera k'anal k'unil tak'in", "block.minecraft.cut_red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.cut_red_sandstone_slab": "Mo<PERSON><PERSON> losa tsajal areniska", "block.minecraft.cut_sandstone": "<PERSON><PERSON><PERSON> aren<PERSON>", "block.minecraft.cut_sandstone_slab": "Mokol losa areniska", "block.minecraft.cyan_banner": "<PERSON><PERSON>-<PERSON><PERSON>", "block.minecraft.cyan_bed": "Ya<PERSON>-elan tem", "block.minecraft.cyan_candle": "<PERSON><PERSON>-<PERSON><PERSON> kantela", "block.minecraft.cyan_candle_cake": "Pastel xchi'uk yax-elan kantela", "block.minecraft.cyan_carpet": "<PERSON><PERSON>-<PERSON>an tapete", "block.minecraft.cyan_concrete": "<PERSON><PERSON>-<PERSON><PERSON> kon<PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON>-<PERSON><PERSON> semento", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cyan_shulker_box": "Ya<PERSON>-elan kaxa yu'un shulker", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON>-<PERSON><PERSON> nen", "block.minecraft.cyan_stained_glass_pane": "Ya<PERSON>-<PERSON><PERSON> panel nen", "block.minecraft.cyan_terracotta": "Ya<PERSON>-elan lakal ach'el", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.damaged_anvil": "<PERSON>j sokem yunke", "block.minecraft.dandelion": "Amar<PERSON>", "block.minecraft.dark_oak_button": "Voton ik'pulan tulan", "block.minecraft.dark_oak_door": "Ti'na ik'pulan tulan", "block.minecraft.dark_oak_fence": "Mok ik'pulan tulan", "block.minecraft.dark_oak_fence_gate": "Ti' mok ik'pulan tulan", "block.minecraft.dark_oak_hanging_sign": "<PERSON><PERSON><PERSON> leterero i<PERSON>'pulan tulan", "block.minecraft.dark_oak_leaves": "Yanaltak ik'pulan tulan", "block.minecraft.dark_oak_log": "Chumante'el i<PERSON>'pulan tulan", "block.minecraft.dark_oak_planks": "Tenelte'el i<PERSON>'pulan tulanetik", "block.minecraft.dark_oak_pressure_plate": "<PERSON><PERSON><PERSON> net'el ik'pulan tulan", "block.minecraft.dark_oak_sapling": "K'elom ik'pulan tulan", "block.minecraft.dark_oak_sign": "Leterero ik'pulan tulan", "block.minecraft.dark_oak_slab": "Losa i<PERSON>'pulan tulan", "block.minecraft.dark_oak_stairs": "Ixkalera i<PERSON>'pulan tulan", "block.minecraft.dark_oak_trapdoor": "Ti'pets' ik'pulan tulan", "block.minecraft.dark_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> leterero ik'pulan tulan ta kubo", "block.minecraft.dark_oak_wall_sign": "Leterero ik'pulan tulan ta kubo", "block.minecraft.dark_oak_wood": "Te'el ik'pulan tulan", "block.minecraft.dark_prismarine": "<PERSON><PERSON>'<PERSON><PERSON> prismarina", "block.minecraft.dark_prismarine_slab": "Losa ik'pulan prismarina", "block.minecraft.dark_prismarine_stairs": "Ixkalera ik'pulan prismarina", "block.minecraft.daylight_detector": "J-a'i xojobal k'ak'al", "block.minecraft.dead_brain_coral": "<PERSON><PERSON>m chinab nabalte'", "block.minecraft.dead_brain_coral_block": "<PERSON><PERSON>m kubo chinab nabalte'", "block.minecraft.dead_brain_coral_fan": "<PERSON><PERSON><PERSON> chin<PERSON> k<PERSON>konia", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON> chinab korkonia ta kubo", "block.minecraft.dead_bubble_coral": "Chamem balbunel nabalte'", "block.minecraft.dead_bubble_coral_block": "Chamem kubo balbunel nabalte'", "block.minecraft.dead_bubble_coral_fan": "Chamem balbunel korkonia", "block.minecraft.dead_bubble_coral_wall_fan": "Chamem balbunel korkonia ta kubo", "block.minecraft.dead_bush": "Takin xk'ib", "block.minecraft.dead_fire_coral": "<PERSON><PERSON><PERSON> k'ok' nabalte'", "block.minecraft.dead_fire_coral_block": "<PERSON><PERSON><PERSON> kubo k'ok' nabalte'", "block.minecraft.dead_fire_coral_fan": "Chamem k'ok' korkonia", "block.minecraft.dead_fire_coral_wall_fan": "Chamem k'ok' korkonia ta kubo", "block.minecraft.dead_horn_coral": "Chamem kachu nabalte'", "block.minecraft.dead_horn_coral_block": "Cha<PERSON>m kubo kachu nabalte'", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON><PERSON> kachu korkonia", "block.minecraft.dead_horn_coral_wall_fan": "Cha<PERSON>m kachu korkonia ta kubo", "block.minecraft.dead_tube_coral": "Chamem tubo nabalte'", "block.minecraft.dead_tube_coral_block": "Cha<PERSON>m kubo tubo nabalte'", "block.minecraft.dead_tube_coral_fan": "Chamem tubo korkonia", "block.minecraft.dead_tube_coral_wall_fan": "Chamem tubo korkonia ta kubo", "block.minecraft.decorated_pot": "<PERSON><PERSON><PERSON>'", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_brick_slab": "Losa latriyo ik'pulanton", "block.minecraft.deepslate_brick_stairs": "Ixkalera latriyo i<PERSON>'pu<PERSON>on", "block.minecraft.deepslate_brick_wall": "Yi'bel na latriyo ik'pulanton", "block.minecraft.deepslate_bricks": "Latriyo ik'pulanton", "block.minecraft.deepslate_coal_ore": "Mineral ak'al ta ik'pulanton", "block.minecraft.deepslate_copper_ore": "<PERSON><PERSON> k'anal k'unil tak'in ta ik'pulanton", "block.minecraft.deepslate_diamond_ore": "Mineral riamante ta ik'pulanton", "block.minecraft.deepslate_emerald_ore": "Mineral esmeralta ta ik'pulanton", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON> k'anal tak'in ta ik'pulanton", "block.minecraft.deepslate_iron_ore": "Mineral tak'in ta ik'pulanton", "block.minecraft.deepslate_lapis_ore": "Mineral lapislasuli ta ik'pulanton", "block.minecraft.deepslate_redstone_ore": "Mineral redstone ta ik'pulanton", "block.minecraft.deepslate_tile_slab": "Losa <PERSON>ulejo <PERSON>pu<PERSON>", "block.minecraft.deepslate_tile_stairs": "<PERSON>x<PERSON><PERSON>", "block.minecraft.deepslate_tile_wall": "<PERSON>'bel na asulejo ik'pulanton", "block.minecraft.deepslate_tiles": "<PERSON><PERSON><PERSON>", "block.minecraft.detector_rail": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.diamond_block": "<PERSON><PERSON> ta riamante", "block.minecraft.diamond_ore": "<PERSON><PERSON> riamante", "block.minecraft.diorite": "<PERSON><PERSON><PERSON>", "block.minecraft.diorite_slab": "<PERSON>a tiorita", "block.minecraft.diorite_stairs": "Ixkalera tiorita", "block.minecraft.diorite_wall": "<PERSON>'bel na tiorita", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "Lumi<PERSON> be", "block.minecraft.dispenser": "Pukobil", "block.minecraft.dragon_egg": "Ton muk'ta ain", "block.minecraft.dragon_head": "<PERSON><PERSON><PERSON> muk'ta ain", "block.minecraft.dragon_wall_head": "<PERSON><PERSON>l muk'ta ain ta kubo", "block.minecraft.dried_ghast": "<PERSON><PERSON> ghast", "block.minecraft.dried_kelp_block": "Kubo takin alka", "block.minecraft.dripstone_block": "<PERSON>'ch'enal kubo", "block.minecraft.dropper": "Tenobil", "block.minecraft.emerald_block": "Kubo ta esmeralta", "block.minecraft.emerald_ore": "Mineral esmeralta", "block.minecraft.enchanting_table": "Mexa kapel", "block.minecraft.end_gateway": "Ti' End", "block.minecraft.end_portal": "End ochebal", "block.minecraft.end_portal_frame": "Smarko ochebal ta End", "block.minecraft.end_rod": "End-te'", "block.minecraft.end_stone": "End-ton", "block.minecraft.end_stone_brick_slab": "Losa latriyo end-ton", "block.minecraft.end_stone_brick_stairs": "Ixkalera latriyo end-ton", "block.minecraft.end_stone_brick_wall": "Yi'bel na latriyo end-ton", "block.minecraft.end_stone_bricks": "Latriyo end-tonetik", "block.minecraft.ender_chest": "<PERSON><PERSON> kaxa", "block.minecraft.exposed_chiseled_copper": "K'oxetajesbil lok'tabil k'anal k'unil tak'in", "block.minecraft.exposed_copper": "K'oxetajesbil k'anal k'unil tak'in", "block.minecraft.exposed_copper_bulb": "Jo<PERSON> k'oxetajesbil k'anal k'unil tak'in", "block.minecraft.exposed_copper_door": "Ti'na k'oxetajesbil k'anal k'unil tak'in", "block.minecraft.exposed_copper_grate": "Rejiya k'oxetajesbil k'anal k'unil tak'in", "block.minecraft.exposed_copper_trapdoor": "Ti'pets' k'oxetajesbil k'anal k'unil tak'in", "block.minecraft.exposed_cut_copper": "K'oxetajesbil mokol k'anal k'unil tak'in", "block.minecraft.exposed_cut_copper_slab": "Losa k'oxetajesbil mokol k'anal k'unil tak'in", "block.minecraft.exposed_cut_copper_stairs": "Ixkalera k'oxetajesbil mokol k'anal k'unil tak'in", "block.minecraft.farmland": "Lum ts'un", "block.minecraft.fern": "<PERSON><PERSON><PERSON>", "block.minecraft.fire": "K'ok'", "block.minecraft.fire_coral": "K'ok' nabalte'", "block.minecraft.fire_coral_block": "<PERSON><PERSON> k'ok' nabalte'", "block.minecraft.fire_coral_fan": "K'ok' korkonia", "block.minecraft.fire_coral_wall_fan": "K'ok' k<PERSON>konia ta kubo", "block.minecraft.firefly_bush": "Xk'ib xchi'uk kukayetik", "block.minecraft.fletching_table": "Mexa alkolal", "block.minecraft.flower_pot": "<PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>", "block.minecraft.flowering_azalea_leaves": "Yanaltak ni<PERSON> al<PERSON>a", "block.minecraft.frogspawn": "Ton pok'pok'", "block.minecraft.frosted_ice": "<PERSON>v", "block.minecraft.furnace": "Jorno", "block.minecraft.gilded_blackstone": "<PERSON><PERSON><PERSON> <PERSON><PERSON>'alton", "block.minecraft.glass": "<PERSON><PERSON>", "block.minecraft.glass_pane": "Panel nen", "block.minecraft.glow_lichen": "Nop'ol liken", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "<PERSON>bo ta k'anal tak'in", "block.minecraft.gold_ore": "<PERSON><PERSON> k'anal tak'in", "block.minecraft.granite": "<PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "<PERSON><PERSON>", "block.minecraft.granite_stairs": "Ixkalera karani<PERSON>", "block.minecraft.granite_wall": "<PERSON>'bel na karanito", "block.minecraft.grass": "Ya<PERSON>l", "block.minecraft.grass_block": "<PERSON><PERSON> yaxal", "block.minecraft.gravel": "Xixibton", "block.minecraft.gray_banner": "<PERSON>'<PERSON><PERSON>'<PERSON><PERSON>", "block.minecraft.gray_bed": "K'anch'etan tem", "block.minecraft.gray_candle": "K'anch'etan kantela", "block.minecraft.gray_candle_cake": "Pastel xchi'uk k'anch'etan kantela", "block.minecraft.gray_carpet": "K'anch'etan tapete", "block.minecraft.gray_concrete": "Konkereto", "block.minecraft.gray_concrete_powder": "Semento", "block.minecraft.gray_glazed_terracotta": "<PERSON>'anch'et<PERSON>", "block.minecraft.gray_shulker_box": "K'anch'etan kaxa yu'un shulker", "block.minecraft.gray_stained_glass": "<PERSON>'an<PERSON>'etan nen", "block.minecraft.gray_stained_glass_pane": "K'anch'etan panel nen", "block.minecraft.gray_terracotta": "K'anch'etan lakal ach'el", "block.minecraft.gray_wool": "K'anch'etan tsots", "block.minecraft.green_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.green_bed": "Yaxal tem", "block.minecraft.green_candle": "<PERSON><PERSON><PERSON> kantela", "block.minecraft.green_candle_cake": "Pastel xchi'uk yaxal kantela", "block.minecraft.green_carpet": "Yaxal tapete", "block.minecraft.green_concrete": "<PERSON><PERSON><PERSON> kon<PERSON>", "block.minecraft.green_concrete_powder": "Yaxal semento", "block.minecraft.green_glazed_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON> kaxa yu'un shulker", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON> nen", "block.minecraft.green_stained_glass_pane": "Yaxal panel nen", "block.minecraft.green_terracotta": "<PERSON>xal lakal ach'el", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON> tsots", "block.minecraft.grindstone": "Ju<PERSON><PERSON>", "block.minecraft.hanging_roots": "Jipiej ibeletik", "block.minecraft.hay_block": "<PERSON><PERSON> an<PERSON>", "block.minecraft.heavy_core": "Alal util", "block.minecraft.heavy_weighted_pressure_plate": "Plakail net'el sventa ep alalil", "block.minecraft.honey_block": "<PERSON><PERSON>", "block.minecraft.honeycomb_block": "<PERSON><PERSON>", "block.minecraft.hopper": "Malobil", "block.minecraft.horn_coral": "<PERSON>chu na<PERSON>'", "block.minecraft.horn_coral_block": "<PERSON>bo kachu nabalte'", "block.minecraft.horn_coral_fan": "<PERSON><PERSON> k<PERSON>", "block.minecraft.horn_coral_wall_fan": "Kachu korkonia ta kubo", "block.minecraft.ice": "<PERSON>v", "block.minecraft.infested_chiseled_stone_bricks": "Xujul lok'tabil latriyo tonetik", "block.minecraft.infested_cobblestone": "<PERSON><PERSON><PERSON> jos<PERSON> ton", "block.minecraft.infested_cracked_stone_bricks": "Xujul jomol latriyo tonetik", "block.minecraft.infested_deepslate": "Xujul i<PERSON>'pu<PERSON>on", "block.minecraft.infested_mossy_stone_bricks": "Xujul tsonte'al latriyo tonetik", "block.minecraft.infested_stone": "Xuju<PERSON> ton", "block.minecraft.infested_stone_bricks": "<PERSON><PERSON><PERSON> la<PERSON> tone<PERSON>k", "block.minecraft.iron_bars": "Te'eltak'inetik", "block.minecraft.iron_block": "<PERSON>bo ta tak'in", "block.minecraft.iron_door": "Ti'na tak'in", "block.minecraft.iron_ore": "Mineral tak'in", "block.minecraft.iron_trapdoor": "Ti'pets' tak'in", "block.minecraft.jack_o_lantern": "<PERSON>'inal ch'um", "block.minecraft.jigsaw": "Kubo j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jukebox": "Jvabajesvanej", "block.minecraft.jungle_button": "Jabnaltikal voton", "block.minecraft.jungle_door": "Jabnaltikal ti'na", "block.minecraft.jungle_fence": "Jabnaltikal mok", "block.minecraft.jungle_fence_gate": "Jabnaltikal ti' mok", "block.minecraft.jungle_hanging_sign": "Jabnaltikal jipiej leterero", "block.minecraft.jungle_leaves": "Yanal<PERSON>k jab<PERSON>", "block.minecraft.jungle_log": "Jabnaltikal chumante'", "block.minecraft.jungle_planks": "Jabnaltikal tenelte'etik", "block.minecraft.jungle_pressure_plate": "Jabnaltikal plaka net'el", "block.minecraft.jungle_sapling": "Jabnaltikal k'elom", "block.minecraft.jungle_sign": "<PERSON><PERSON><PERSON> let<PERSON>o", "block.minecraft.jungle_slab": "Jabnaltikal losa", "block.minecraft.jungle_stairs": "Jabnaltikal ixkalera", "block.minecraft.jungle_trapdoor": "Jabnaltikal ti'pets'", "block.minecraft.jungle_wall_hanging_sign": "Jabnaltikal jipiej leterero ta kubo", "block.minecraft.jungle_wall_sign": "Jabnaltikal leterero ta kubo", "block.minecraft.jungle_wood": "Jabnaltikal te'", "block.minecraft.kelp": "Alka", "block.minecraft.kelp_plant": "Xch'ut alka", "block.minecraft.ladder": "Tek'omte'", "block.minecraft.lantern": "<PERSON><PERSON>", "block.minecraft.lapis_block": "<PERSON><PERSON> ta lapis<PERSON>uli", "block.minecraft.lapis_ore": "<PERSON>ral lapislasuli", "block.minecraft.large_amethyst_bud": "Muk'ta lok'el amatista", "block.minecraft.large_fern": "<PERSON><PERSON>", "block.minecraft.lava": "<PERSON><PERSON><PERSON>'", "block.minecraft.lava_cauldron": "<PERSON><PERSON><PERSON> xchi'uk tsuk'", "block.minecraft.leaf_litter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lectern": "Atiril", "block.minecraft.lever": "<PERSON><PERSON><PERSON>", "block.minecraft.light": "<PERSON><PERSON>", "block.minecraft.light_blue_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_bed": "Yaxal tem", "block.minecraft.light_blue_candle": "<PERSON><PERSON><PERSON> kantela", "block.minecraft.light_blue_candle_cake": "Pastel xchi'uk yaxal kantela", "block.minecraft.light_blue_carpet": "Yaxal tapete", "block.minecraft.light_blue_concrete": "<PERSON><PERSON><PERSON> kon<PERSON>", "block.minecraft.light_blue_concrete_powder": "Yaxal semento", "block.minecraft.light_blue_glazed_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON> kaxa yu'un shulker", "block.minecraft.light_blue_stained_glass": "<PERSON><PERSON><PERSON> nen", "block.minecraft.light_blue_stained_glass_pane": "Yaxal panel nen", "block.minecraft.light_blue_terracotta": "<PERSON>xal lakal ach'el", "block.minecraft.light_blue_wool": "<PERSON><PERSON><PERSON> tsots", "block.minecraft.light_gray_banner": "<PERSON>'<PERSON><PERSON>'<PERSON><PERSON>", "block.minecraft.light_gray_bed": "K'anch'etan tem", "block.minecraft.light_gray_candle": "K'anch'etan kantela", "block.minecraft.light_gray_candle_cake": "Pastel xchi'uk k'anch'etan kantela", "block.minecraft.light_gray_carpet": "K'anch'etan tapete", "block.minecraft.light_gray_concrete": "Konkereto", "block.minecraft.light_gray_concrete_powder": "Semento", "block.minecraft.light_gray_glazed_terracotta": "<PERSON>'anch'et<PERSON>", "block.minecraft.light_gray_shulker_box": "K'anch'etan kaxa yu'un shulker", "block.minecraft.light_gray_stained_glass": "<PERSON>'an<PERSON>'etan nen", "block.minecraft.light_gray_stained_glass_pane": "K'anch'etan panel nen", "block.minecraft.light_gray_terracotta": "K'anch'etan lakal ach'el", "block.minecraft.light_gray_wool": "K'anch'etan tsots", "block.minecraft.light_weighted_pressure_plate": "Plakail net'el sventa jutuk alalil", "block.minecraft.lightning_rod": "J-ik anjel", "block.minecraft.lilac": "<PERSON>", "block.minecraft.lily_of_the_valley": "Yoy ta soplej", "block.minecraft.lily_pad": "Sapalota", "block.minecraft.lime_banner": "Yoxyoxtik vantera", "block.minecraft.lime_bed": "Yoxyoxtik tem", "block.minecraft.lime_candle": "Yoxyoxtik kantela", "block.minecraft.lime_candle_cake": "Pastel xchi'uk yoxyoxtik kantela", "block.minecraft.lime_carpet": "Yoxyoxtik tapete", "block.minecraft.lime_concrete": "Yoxyoxtik konkereto", "block.minecraft.lime_concrete_powder": "Yoxyoxtik semento", "block.minecraft.lime_glazed_terracotta": "Yoxyoxtik asulejo", "block.minecraft.lime_shulker_box": "Yoxyoxtik kaxa yu'un shulker", "block.minecraft.lime_stained_glass": "Yoxyoxtik nen", "block.minecraft.lime_stained_glass_pane": "Yoxyoxtik panel nen", "block.minecraft.lime_terracotta": "Yoxyoxtik lakal ach'el", "block.minecraft.lime_wool": "Yoxyoxtik tsots", "block.minecraft.lodestone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.loom": "Abtejebal pok'", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_bed": "Tsojtsojtik tem", "block.minecraft.magenta_candle": "T<PERSON>jtsojtik kantela", "block.minecraft.magenta_candle_cake": "Pastel xchi'uk tsojtsojtik kantela", "block.minecraft.magenta_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tapete", "block.minecraft.magenta_concrete": "Tsojtsojtik konkereto", "block.minecraft.magenta_concrete_powder": "Tsojtsojtik semento", "block.minecraft.magenta_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON>jtik kaxa yu'un shulker", "block.minecraft.magenta_stained_glass": "T<PERSON>jtsojtik nen", "block.minecraft.magenta_stained_glass_pane": "Tsojtsojtik panel nen", "block.minecraft.magenta_terracotta": "Tsojtsojtik lakal ach'el", "block.minecraft.magenta_wool": "Tsojtsojtik tsots", "block.minecraft.magma_block": "<PERSON><PERSON> makma", "block.minecraft.mangrove_button": "Voton ja'alte'", "block.minecraft.mangrove_door": "Ti'na ja'alte'", "block.minecraft.mangrove_fence": "<PERSON><PERSON> ja'alte'", "block.minecraft.mangrove_fence_gate": "Ti' mok ja'alte'", "block.minecraft.mangrove_hanging_sign": "<PERSON><PERSON><PERSON> leterero ja'alte'", "block.minecraft.mangrove_leaves": "Yanaltak ja'alte'", "block.minecraft.mangrove_log": "Chuman<PERSON>'el ja'alte'", "block.minecraft.mangrove_planks": "Tenelte'el ja'alte'etik", "block.minecraft.mangrove_pressure_plate": "<PERSON><PERSON>a net'el ja'alte'", "block.minecraft.mangrove_propagule": "<PERSON><PERSON><PERSON><PERSON> ja'alte'", "block.minecraft.mangrove_roots": "<PERSON>'beltak ja'alte'", "block.minecraft.mangrove_sign": "Leterero ja'alte'", "block.minecraft.mangrove_slab": "Losa ja'alte'", "block.minecraft.mangrove_stairs": "Ixkalera ja'alte'", "block.minecraft.mangrove_trapdoor": "Ti'pets' ja'alte'", "block.minecraft.mangrove_wall_hanging_sign": "<PERSON><PERSON><PERSON> leterero ja'alte' ta kubo", "block.minecraft.mangrove_wall_sign": "Leterero ja'alte' ta kubo", "block.minecraft.mangrove_wood": "Te'el ja'alte'", "block.minecraft.medium_amethyst_bud": "Natil lok'el amatista", "block.minecraft.melon": "<PERSON><PERSON><PERSON>", "block.minecraft.melon_stem": "<PERSON><PERSON>'ut xancha", "block.minecraft.moss_block": "<PERSON><PERSON> tsonte'", "block.minecraft.moss_carpet": "<PERSON><PERSON>e tson<PERSON>'", "block.minecraft.mossy_cobblestone": "Tsonte'al josbil ton", "block.minecraft.mossy_cobblestone_slab": "Tsonte'al losa josbil ton", "block.minecraft.mossy_cobblestone_stairs": "Tsonte'al ixkalera josbil ton", "block.minecraft.mossy_cobblestone_wall": "Tson<PERSON>'al yi'bel na josbil ton", "block.minecraft.mossy_stone_brick_slab": "Tsonte'al losa latriyo End ton", "block.minecraft.mossy_stone_brick_stairs": "Tsonte'al ixkalera latriyo End ton", "block.minecraft.mossy_stone_brick_wall": "Tsonte'al yi'bel na latriyo End ton", "block.minecraft.mossy_stone_bricks": "Tsonte'al End latriyo tonetik", "block.minecraft.moving_piston": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.mud": "Ach'el", "block.minecraft.mud_brick_slab": "Losa latriyo xamit", "block.minecraft.mud_brick_stairs": "Ixkalera latriyo xamit", "block.minecraft.mud_brick_wall": "<PERSON>'bel na latriyo xamit", "block.minecraft.mud_bricks": "Latriyo xami<PERSON>", "block.minecraft.muddy_mangrove_roots": "Ach'elal yi'beltak ja'alte'", "block.minecraft.mushroom_stem": "Xch'ut moni'", "block.minecraft.mycelium": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_fence": "Nether mok latriyo", "block.minecraft.nether_brick_slab": "Nether losa latriyo", "block.minecraft.nether_brick_stairs": "Nether ix<PERSON>era latriyo", "block.minecraft.nether_brick_wall": "Nether yi'bel na latriyo", "block.minecraft.nether_bricks": "<PERSON><PERSON> la<PERSON>k", "block.minecraft.nether_gold_ore": "Nether mineral k'anal tak'in", "block.minecraft.nether_portal": "<PERSON><PERSON> ochebal", "block.minecraft.nether_quartz_ore": "Nether mineral kuarso", "block.minecraft.nether_sprouts": "<PERSON>her k'elometik", "block.minecraft.nether_wart": "Nether ch'okte'", "block.minecraft.nether_wart_block": "<PERSON><PERSON> Nether ch'okte'", "block.minecraft.netherite_block": "Kubo ta neterita", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "<PERSON>al kubo", "block.minecraft.oak_button": "<PERSON>oton tulan", "block.minecraft.oak_door": "Ti'na tulan", "block.minecraft.oak_fence": "<PERSON><PERSON> tulan", "block.minecraft.oak_fence_gate": "Ti' mok tulan", "block.minecraft.oak_hanging_sign": "<PERSON><PERSON><PERSON> let<PERSON> tulan", "block.minecraft.oak_leaves": "Yanaltak tulan", "block.minecraft.oak_log": "Chumante'el tulan", "block.minecraft.oak_planks": "Tenelte'el tulanetik", "block.minecraft.oak_pressure_plate": "<PERSON><PERSON><PERSON> net'el tulan", "block.minecraft.oak_sapling": "<PERSON>'elom tulan", "block.minecraft.oak_sign": "<PERSON><PERSON><PERSON> tulan", "block.minecraft.oak_slab": "<PERSON><PERSON> tulan", "block.minecraft.oak_stairs": "<PERSON><PERSON><PERSON><PERSON> tulan", "block.minecraft.oak_trapdoor": "Ti'pets' tulan", "block.minecraft.oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> leterero tulan ta kubo", "block.minecraft.oak_wall_sign": "<PERSON><PERSON>o tulan ta kubo", "block.minecraft.oak_wood": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "block.minecraft.observer": "Jk'elvanej", "block.minecraft.obsidian": "Xik' <PERSON><PERSON><PERSON>", "block.minecraft.ochre_froglight": "K'anal nop'olkubo", "block.minecraft.ominous_banner": "<PERSON><PERSON>", "block.minecraft.open_eyeblossom": "<PERSON> satilal nichim", "block.minecraft.orange_banner": "K'onk'on<PERSON><PERSON>", "block.minecraft.orange_bed": "K'onk'ontik tem", "block.minecraft.orange_candle": "K'onk'ontik kantela", "block.minecraft.orange_candle_cake": "Pastel xchi'uk k'onk'ontik kantela", "block.minecraft.orange_carpet": "K'onk'ontik tapete", "block.minecraft.orange_concrete": "K'onk'ontik konkereto", "block.minecraft.orange_concrete_powder": "K'onk'ontik semento", "block.minecraft.orange_glazed_terracotta": "K'onk'ontik as<PERSON><PERSON>", "block.minecraft.orange_shulker_box": "K'onk'ontik kaxa yu'un shulker", "block.minecraft.orange_stained_glass": "K'onk'ontik nen", "block.minecraft.orange_stained_glass_pane": "K'onk'ontik panel nen", "block.minecraft.orange_terracotta": "K'onk'ontik lakal ach'el", "block.minecraft.orange_tulip": "K'onk'ontik tulipan", "block.minecraft.orange_wool": "K'onk'ontik tsots", "block.minecraft.oxeye_daisy": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Kux lok'tabil k'anal k'unil tak'in", "block.minecraft.oxidized_copper": "Kux k'anal k'unil tak'in", "block.minecraft.oxidized_copper_bulb": "<PERSON>x joko k'anal k'unil tak'in", "block.minecraft.oxidized_copper_door": "Ti'na kux k'anal k'unil tak'in", "block.minecraft.oxidized_copper_grate": "<PERSON><PERSON><PERSON> kux k'anal k'unil tak'in", "block.minecraft.oxidized_copper_trapdoor": "Ti'pets' kux k'anal k'unil tak'in", "block.minecraft.oxidized_cut_copper": "Kux mokol k'anal k'unil tak'in", "block.minecraft.oxidized_cut_copper_slab": "Losa kux mokol k'anal k'unil tak'in", "block.minecraft.oxidized_cut_copper_stairs": "Ixkalera kux mokol k'anal k'unil tak'in", "block.minecraft.packed_ice": "<PERSON><PERSON><PERSON>", "block.minecraft.packed_mud": "<PERSON><PERSON><PERSON>", "block.minecraft.pale_hanging_moss": "Sakpak'an jipiej tsonte'", "block.minecraft.pale_moss_block": "Sakpak'an kubo tsonte'", "block.minecraft.pale_moss_carpet": "Sakpak'an tapete tsonte'", "block.minecraft.pale_oak_button": "Voton sakpa<PERSON>'an tulan", "block.minecraft.pale_oak_door": "Ti'na sakpak'an tulan", "block.minecraft.pale_oak_fence": "Mok sakpa<PERSON>'an tulan", "block.minecraft.pale_oak_fence_gate": "Ti' mok sakpak'an tulan", "block.minecraft.pale_oak_hanging_sign": "<PERSON><PERSON><PERSON> let<PERSON>o sak<PERSON>'an tulan", "block.minecraft.pale_oak_leaves": "Yanaltak sakpa<PERSON>'an tulan", "block.minecraft.pale_oak_log": "Chumante'el sakpa<PERSON>'an tulan", "block.minecraft.pale_oak_planks": "Tenelte'el sakpak'an tulanetik", "block.minecraft.pale_oak_pressure_plate": "<PERSON><PERSON><PERSON> net'el sakpak'an tulan", "block.minecraft.pale_oak_sapling": "K'elom sakpak'an tulan", "block.minecraft.pale_oak_sign": "<PERSON><PERSON>o sak<PERSON>'an tulan", "block.minecraft.pale_oak_slab": "Losa sak<PERSON>'an tulan", "block.minecraft.pale_oak_stairs": "Ixkalera sak<PERSON>'an tulan", "block.minecraft.pale_oak_trapdoor": "Ti'pets' sakpak'an tulan", "block.minecraft.pale_oak_wall_hanging_sign": "<PERSON><PERSON><PERSON> leterero sakpa<PERSON>'an tulan ta kubo", "block.minecraft.pale_oak_wall_sign": "Leterero sakpak'an tulan ta kubo", "block.minecraft.pale_oak_wood": "<PERSON>'el sakpak'an tulan", "block.minecraft.pearlescent_froglight": "Ik'pok'an nop'olkubo", "block.minecraft.peony": "Peonia", "block.minecraft.petrified_oak_slab": "Tanubem losa tulan", "block.minecraft.piglin_head": "<PERSON><PERSON><PERSON>lin", "block.minecraft.piglin_wall_head": "<PERSON><PERSON><PERSON> piglin ta kubo", "block.minecraft.pink_banner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_bed": "Tsojtsojtik tem", "block.minecraft.pink_candle": "T<PERSON>jtsojtik kantela", "block.minecraft.pink_candle_cake": "Pastel xchi'uk tsojtsojtik kantela", "block.minecraft.pink_carpet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tapete", "block.minecraft.pink_concrete": "Tsojtsojtik konkereto", "block.minecraft.pink_concrete_powder": "Tsojtsojtik semento", "block.minecraft.pink_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pink_petals": "Tsojtsojtik nichimetik", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON><PERSON><PERSON>jtik kaxa yu'un shulker", "block.minecraft.pink_stained_glass": "T<PERSON>jtsojtik nen", "block.minecraft.pink_stained_glass_pane": "Tsojtsojtik panel nen", "block.minecraft.pink_terracotta": "Tsojtsojtik lakal ach'el", "block.minecraft.pink_tulip": "Tsojtsojtik tulipan", "block.minecraft.pink_wool": "Tsojtsojtik tsots", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "<PERSON><PERSON><PERSON>", "block.minecraft.pitcher_crop": "Ts'un binalte'", "block.minecraft.pitcher_plant": "Binalte'", "block.minecraft.player_head": "<PERSON><PERSON><PERSON>l", "block.minecraft.player_head.named": "Sjol %s", "block.minecraft.player_wall_head": "<PERSON><PERSON><PERSON> jtajimol ta kubo", "block.minecraft.podzol": "Ch'ajil lum", "block.minecraft.pointed_dripstone": "Ts'ubts'ub chu'ch'en", "block.minecraft.polished_andesite": "Ch'ulbil antesita", "block.minecraft.polished_andesite_slab": "Losa ch'ulbil antesita", "block.minecraft.polished_andesite_stairs": "Ixkalera ch'ulbil antesita", "block.minecraft.polished_basalt": "<PERSON><PERSON><PERSON><PERSON> vasalto", "block.minecraft.polished_blackstone": "<PERSON>'ulbil ik'alton", "block.minecraft.polished_blackstone_brick_slab": "Losa ch'ulbil latriyo ik'alton", "block.minecraft.polished_blackstone_brick_stairs": "Ixkalera ch'ulbil latriyo ik'alton", "block.minecraft.polished_blackstone_brick_wall": "<PERSON>'bel na ch'ulbil latriyo ik'alton", "block.minecraft.polished_blackstone_bricks": "Ch'ulbil latriyo ik'altonetik", "block.minecraft.polished_blackstone_button": "<PERSON>'ulbil voton ik'alton", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON>'ul<PERSON> plakail net'el ik'alton", "block.minecraft.polished_blackstone_slab": "Losa ch'ulbil i<PERSON>'alton", "block.minecraft.polished_blackstone_stairs": "Ixkalera ch'ulbil ik'alton", "block.minecraft.polished_blackstone_wall": "<PERSON>'bel na ch'ulbil ik'alton", "block.minecraft.polished_deepslate": "Ch'ulbil ik'pulanton", "block.minecraft.polished_deepslate_slab": "Losa ch'ulbil ik'pulanton", "block.minecraft.polished_deepslate_stairs": "Ixkalera ch'ulbil ik'pulanton", "block.minecraft.polished_deepslate_wall": "<PERSON>'bel na ch'ulbil ik'pulanton", "block.minecraft.polished_diorite": "<PERSON><PERSON><PERSON><PERSON> tiorita", "block.minecraft.polished_diorite_slab": "<PERSON><PERSON> ch'ulbil tiorita", "block.minecraft.polished_diorite_stairs": "Ixkalera ch'ulbil ik'pulanton", "block.minecraft.polished_granite": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "block.minecraft.polished_granite_slab": "<PERSON><PERSON> <PERSON>'ul<PERSON> karani<PERSON>", "block.minecraft.polished_granite_stairs": "Ixkalera ch'ulbil karanito", "block.minecraft.polished_tuff": "<PERSON><PERSON><PERSON><PERSON> tova", "block.minecraft.polished_tuff_slab": "<PERSON><PERSON> ch'ulbil tova", "block.minecraft.polished_tuff_stairs": "<PERSON>x<PERSON><PERSON> ch'ulbil tova", "block.minecraft.polished_tuff_wall": "<PERSON>'bel na ch'ulbil tova", "block.minecraft.poppy": "Amapola", "block.minecraft.potatoes": "Isak'<PERSON>ik", "block.minecraft.potted_acacia_sapling": "<PERSON>seta xchi'uk k'elom akasia", "block.minecraft.potted_allium": "<PERSON><PERSON><PERSON> x<PERSON>'uk tuix<PERSON>him", "block.minecraft.potted_azalea_bush": "Maseta xchi'uk asalea", "block.minecraft.potted_azure_bluet": "Maseta xchi'uk sakpak'an nichim", "block.minecraft.potted_bamboo": "<PERSON><PERSON>a xchi'uk ton-aj", "block.minecraft.potted_birch_sapling": "Maseta xchi'uk k'elom averul", "block.minecraft.potted_blue_orchid": "<PERSON><PERSON>a xchi'uk yaxal ech'", "block.minecraft.potted_brown_mushroom": "Maseta xchi'uk k'anch'etan moni'", "block.minecraft.potted_cactus": "Maseta xchi'uk k'anch'etan petok", "block.minecraft.potted_cherry_sapling": "Maseta xchi'uk k'elom ch'ixtot", "block.minecraft.potted_closed_eyeblossom": "<PERSON><PERSON>a xchi'uk makal satilal nichim", "block.minecraft.potted_cornflower": "Maseta xchi'uk asiano", "block.minecraft.potted_crimson_fungus": "<PERSON><PERSON>a xchi'uk Nether moni'", "block.minecraft.potted_crimson_roots": "Maseta xchi'uk tsaja i<PERSON>k", "block.minecraft.potted_dandelion": "Maseta xchi'uk amarkon", "block.minecraft.potted_dark_oak_sapling": "<PERSON>seta xchi'uk k'elom ik'pulan tulan", "block.minecraft.potted_dead_bush": "Maseta xchi'uk takin xk'ib", "block.minecraft.potted_fern": "<PERSON><PERSON><PERSON> xchi'uk tsib", "block.minecraft.potted_flowering_azalea_bush": "Maseta xchi'uk nichi<PERSON><PERSON>m alasea", "block.minecraft.potted_jungle_sapling": "Maseta xchi'uk jabnaltikal k'elom", "block.minecraft.potted_lily_of_the_valley": "<PERSON><PERSON><PERSON> xchi'uk yoy ta soplej", "block.minecraft.potted_mangrove_propagule": "Maseta xchi'uk poropakulo ja'alte'", "block.minecraft.potted_oak_sapling": "<PERSON><PERSON><PERSON> xchi'uk k'elom tulan", "block.minecraft.potted_open_eyeblossom": "<PERSON><PERSON>a xchi'uk jamal satilal nichim", "block.minecraft.potted_orange_tulip": "Maseta xchi'uk k'onk'ontik tulipan", "block.minecraft.potted_oxeye_daisy": "Maseta xchi'uk markarita", "block.minecraft.potted_pale_oak_sapling": "<PERSON><PERSON><PERSON> xchi'uk k'elom sakpak'an tulan", "block.minecraft.potted_pink_tulip": "Maseta xchi'uk tsojtsojtik tulipan", "block.minecraft.potted_poppy": "Maseta xchi'uk amapola", "block.minecraft.potted_red_mushroom": "<PERSON><PERSON><PERSON> xchi'uk tsajal moni'", "block.minecraft.potted_red_tulip": "Maseta xchi'uk tsajal tulipan", "block.minecraft.potted_spruce_sapling": "<PERSON><PERSON><PERSON> xchi'uk k'elom toj", "block.minecraft.potted_torchflower": "Maseta xchi'uk nop'olnichim", "block.minecraft.potted_warped_fungus": "Maseta xchi'uk ts'otol moni'", "block.minecraft.potted_warped_roots": "Maseta xchi'uk ts'otol ibeletik", "block.minecraft.potted_white_tulip": "Maseta xchi'uk sakil tulipan", "block.minecraft.potted_wither_rose": "Maseta xchi'uk x<PERSON><PERSON>", "block.minecraft.powder_snow": "P'up' taiv", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON>a xchi'uk p'up' taiv", "block.minecraft.powered_rail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine": "Prismarina", "block.minecraft.prismarine_brick_slab": "Losa latriyo prismarina", "block.minecraft.prismarine_brick_stairs": "Ixkalera latriyo prismarina", "block.minecraft.prismarine_bricks": "Latriyo prismarina<PERSON>k", "block.minecraft.prismarine_slab": "Losa prismarina", "block.minecraft.prismarine_stairs": "Ixkalera prismarina", "block.minecraft.prismarine_wall": "Yi'bel na prismarina", "block.minecraft.pumpkin": "Ch'um", "block.minecraft.pumpkin_stem": "Xch'ut ch'um", "block.minecraft.purple_banner": "<PERSON><PERSON>'pok'an vantera", "block.minecraft.purple_bed": "Ik'pok'an tem", "block.minecraft.purple_candle": "Ik'pok'an kantela", "block.minecraft.purple_candle_cake": "Pastel xchi'uk ik'pok'an kantela", "block.minecraft.purple_carpet": "Ik'pok'an tapete", "block.minecraft.purple_concrete": "Ik'pok'an konkereto", "block.minecraft.purple_concrete_powder": "Ik'pok'an semento", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON>'pok'an <PERSON><PERSON><PERSON>", "block.minecraft.purple_shulker_box": "<PERSON><PERSON>'pok'an kaxa yu'un shulker", "block.minecraft.purple_stained_glass": "<PERSON><PERSON>'pok'an nen", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON>'pok'an panel nen", "block.minecraft.purple_terracotta": "Ik'pok'an lakal ach'el", "block.minecraft.purple_wool": "<PERSON>k'pok'an tsots", "block.minecraft.purpur_block": "Purpur", "block.minecraft.purpur_pillar": "Oy purpur", "block.minecraft.purpur_slab": "<PERSON>a purpur", "block.minecraft.purpur_stairs": "Ixkalera purpur", "block.minecraft.quartz_block": "<PERSON>bo ta kuarso", "block.minecraft.quartz_bricks": "Latriyo k<PERSON>", "block.minecraft.quartz_pillar": "<PERSON>y kuarso", "block.minecraft.quartz_slab": "Losa kuarso", "block.minecraft.quartz_stairs": "Ixkalera kuarso", "block.minecraft.rail": "<PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "<PERSON>bo ta lumil k'anal k'unil tak'in", "block.minecraft.raw_gold_block": "<PERSON>bo ta lumil k'anal tak'in", "block.minecraft.raw_iron_block": "<PERSON>bo ta lumil tak'in", "block.minecraft.red_banner": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON><PERSON> tem", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON><PERSON> kantela", "block.minecraft.red_candle_cake": "Pastel xchi'uk tsajal kantela", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON><PERSON> tapete", "block.minecraft.red_concrete": "<PERSON><PERSON><PERSON><PERSON> kon<PERSON>", "block.minecraft.red_concrete_powder": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_mushroom": "<PERSON><PERSON><PERSON><PERSON>'", "block.minecraft.red_mushroom_block": "<PERSON><PERSON> tsajal moni'", "block.minecraft.red_nether_brick_slab": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON>bel na tsajal Nether latriyo", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_sand": "<PERSON><PERSON><PERSON><PERSON> yi'", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "<PERSON><PERSON> tsa<PERSON><PERSON> aren<PERSON>a", "block.minecraft.red_sandstone_stairs": "Ixkalera tsa<PERSON> aren<PERSON>a", "block.minecraft.red_sandstone_wall": "<PERSON>'be<PERSON> na tsajal areniska", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON><PERSON> kaxa yu'un shulker", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON><PERSON> nen", "block.minecraft.red_stained_glass_pane": "T<PERSON>jal panel nen", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON><PERSON> lakal a<PERSON>'el", "block.minecraft.red_tulip": "<PERSON><PERSON><PERSON><PERSON> tulipan", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.redstone_block": "Kubo ta redstone", "block.minecraft.redstone_lamp": "Lampara redstone", "block.minecraft.redstone_ore": "Mineral redstone", "block.minecraft.redstone_torch": "Jch'ob redstone", "block.minecraft.redstone_wall_torch": "Jch'ob redstone ta yi'bel na", "block.minecraft.redstone_wire": "Alampre redstone", "block.minecraft.reinforced_deepslate": "Tsatsubtasbil ik'pulanton", "block.minecraft.repeater": "J<PERSON>'pasvanej redstone", "block.minecraft.repeating_command_block": "<PERSON><PERSON> mantal cha'pasel", "block.minecraft.resin_block": "<PERSON>bo ta xuch'", "block.minecraft.resin_brick_slab": "Losa latriyo xuch'", "block.minecraft.resin_brick_stairs": "Ixkalera latriyo xuch'", "block.minecraft.resin_brick_wall": "Yi'bel na latriyo xuchꞌ", "block.minecraft.resin_bricks": "Latriyo xuchꞌetik", "block.minecraft.resin_clump": "<PERSON><PERSON><PERSON>", "block.minecraft.respawn_anchor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.rooted_dirt": "Te<PERSON>'<PERSON><PERSON><PERSON><PERSON><PERSON> lum", "block.minecraft.rose_bush": "<PERSON><PERSON><PERSON>", "block.minecraft.sand": "<PERSON>'", "block.minecraft.sandstone": "Areniska", "block.minecraft.sandstone_slab": "Los<PERSON> areniska", "block.minecraft.sandstone_stairs": "Ixkalera areniska", "block.minecraft.sandstone_wall": "Yi'bel na areniska", "block.minecraft.scaffolding": "An<PERSON><PERSON>", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Jpas kanalisar sculk", "block.minecraft.sculk_sensor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-sculk", "block.minecraft.sculk_shrieker": "<PERSON>-<PERSON>'el-sculk", "block.minecraft.sculk_vein": "<PERSON><PERSON><PERSON>sculk", "block.minecraft.sea_lantern": "<PERSON><PERSON>", "block.minecraft.sea_pickle": "<PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON>", "block.minecraft.set_spawn": "Cha'vinajebal ak'bil", "block.minecraft.short_dry_grass": "Komkom takin yaxal", "block.minecraft.short_grass": "Komkom yaxal", "block.minecraft.shroomlight": "<PERSON><PERSON>'olm<PERSON>'", "block.minecraft.shulker_box": "<PERSON>xa yu'un shulker", "block.minecraft.skeleton_skull": "Sba<PERSON>l sjol j<PERSON>", "block.minecraft.skeleton_wall_skull": "Sbakil sjol jbakubel ta kubo", "block.minecraft.slime_block": "<PERSON><PERSON> simil", "block.minecraft.small_amethyst_bud": "Bik'it lok'el amatista", "block.minecraft.small_dripleaf": "Bik'it toyte'", "block.minecraft.smithing_table": "Mexa tak'inal", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Ch'ulul vasalto", "block.minecraft.smooth_quartz": "<PERSON><PERSON> ch'ulul kuarso", "block.minecraft.smooth_quartz_slab": "Losa ch'ulul kuarso", "block.minecraft.smooth_quartz_stairs": "Ixkalera ch'ulul kuarso", "block.minecraft.smooth_red_sandstone": "<PERSON>'ul<PERSON> tsa<PERSON> aren<PERSON>a", "block.minecraft.smooth_red_sandstone_slab": "Losa ch'ulul tsajal aren<PERSON>a", "block.minecraft.smooth_red_sandstone_stairs": "Ixkalera ch'ulul tsajal areniska", "block.minecraft.smooth_sandstone": "Ch'ulul areniska", "block.minecraft.smooth_sandstone_slab": "Losa ch'ulul areniska", "block.minecraft.smooth_sandstone_stairs": "Ixkalera ch'ulul areniska", "block.minecraft.smooth_stone": "Ch'ulul ton", "block.minecraft.smooth_stone_slab": "<PERSON>a ch'ulul ton", "block.minecraft.sniffer_egg": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.snow": "<PERSON>v", "block.minecraft.snow_block": "<PERSON><PERSON> taiv", "block.minecraft.soul_campfire": "K'ok' ch'ulelal", "block.minecraft.soul_fire": "K'ok' ch'ulelal", "block.minecraft.soul_lantern": "<PERSON><PERSON>'ul<PERSON>l", "block.minecraft.soul_sand": "<PERSON><PERSON> <PERSON>'ul<PERSON><PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON> ch'ulelal", "block.minecraft.soul_torch": "<PERSON><PERSON>'ob<PERSON><PERSON> ch'ulelal", "block.minecraft.soul_wall_torch": "Jch'obvan<PERSON> ch'ulelal ta kubo", "block.minecraft.spawn.not_valid": "<PERSON>'yuk atem o mu nojem li acha'vinajebe, o ja' makal", "block.minecraft.spawner": "<PERSON><PERSON><PERSON>ob monstro", "block.minecraft.spawner.desc1": "Tuneso jbej vinajesobil:", "block.minecraft.spawner.desc2": "Chak' li tos chanule", "block.minecraft.sponge": "Esponja", "block.minecraft.spore_blossom": "Espora nichim", "block.minecraft.spruce_button": "<PERSON><PERSON><PERSON> toj", "block.minecraft.spruce_door": "<PERSON><PERSON><PERSON><PERSON> toj", "block.minecraft.spruce_fence": "<PERSON><PERSON> toj", "block.minecraft.spruce_fence_gate": "<PERSON><PERSON>' mok toj", "block.minecraft.spruce_hanging_sign": "<PERSON><PERSON><PERSON> let<PERSON> toj", "block.minecraft.spruce_leaves": "Yanal<PERSON>k toj", "block.minecraft.spruce_log": "<PERSON><PERSON><PERSON><PERSON> toj", "block.minecraft.spruce_planks": "Tenelte'el tojetik", "block.minecraft.spruce_pressure_plate": "<PERSON><PERSON><PERSON> net'el toj", "block.minecraft.spruce_sapling": "<PERSON>'elom toj", "block.minecraft.spruce_sign": "<PERSON><PERSON><PERSON> toj", "block.minecraft.spruce_slab": "<PERSON><PERSON> toj", "block.minecraft.spruce_stairs": "<PERSON><PERSON><PERSON><PERSON> toj", "block.minecraft.spruce_trapdoor": "Ti'pets' toj", "block.minecraft.spruce_wall_hanging_sign": "<PERSON><PERSON><PERSON> leterero toj ta kubo", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON>o toj ta kubo", "block.minecraft.spruce_wood": "<PERSON>'el toj", "block.minecraft.sticky_piston": "Noch'och' piston", "block.minecraft.stone": "Ton", "block.minecraft.stone_brick_slab": "Losa latriyo ton", "block.minecraft.stone_brick_stairs": "Ixkalera latriyo ton", "block.minecraft.stone_brick_wall": "Yi'bel na latriyo ton", "block.minecraft.stone_bricks": "La<PERSON><PERSON>", "block.minecraft.stone_button": "Voton ton", "block.minecraft.stone_pressure_plate": "Plakail net'el ton", "block.minecraft.stone_slab": "Losa ton", "block.minecraft.stone_stairs": "Ixkalera ton", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Chuman<PERSON>'el akasia mu xchi'uk spat", "block.minecraft.stripped_acacia_wood": "<PERSON>'el akasia mu xchi'uk spat", "block.minecraft.stripped_bamboo_block": "<PERSON>bo ta ton-aj mu xchi'uk spat", "block.minecraft.stripped_birch_log": "Chumante'el averul mu xchi'uk spat", "block.minecraft.stripped_birch_wood": "Te'el averul mu xchi'uk spat", "block.minecraft.stripped_cherry_log": "Chumante'el ch'ixtot mu xchi'uk spat", "block.minecraft.stripped_cherry_wood": "Te'el ch'ixtot mu xchi'uk spat", "block.minecraft.stripped_crimson_hyphae": "Tsajal ipax mu xchi'uk spat", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON><PERSON><PERSON> ch'util mu xchi'uk spat", "block.minecraft.stripped_dark_oak_log": "Chumante'el ik'pulan tulan mu xchi'uk spat", "block.minecraft.stripped_dark_oak_wood": "Te'el ik'pulan tulan mu xchi'uk spat", "block.minecraft.stripped_jungle_log": "Jabnaltikal chumante' mu xchi'uk spat", "block.minecraft.stripped_jungle_wood": "Jabnaltikal te' mu xchi'uk spat", "block.minecraft.stripped_mangrove_log": "Chuman<PERSON>'el ja'alte' mu xchi'uk spat", "block.minecraft.stripped_mangrove_wood": "Te'el ja'alte' mu xchi'uk spat", "block.minecraft.stripped_oak_log": "Chuman<PERSON>'el tulan mu xchi'uk spat", "block.minecraft.stripped_oak_wood": "<PERSON>'el tulan mu xchi'uk spat", "block.minecraft.stripped_pale_oak_log": "Chumante'el sakpak'an tulan mu xchi'uk spat", "block.minecraft.stripped_pale_oak_wood": "<PERSON>'el sakpak'an tulan mu xchi'uk spat", "block.minecraft.stripped_spruce_log": "<PERSON>'el toj mu xchi'uk spat", "block.minecraft.stripped_spruce_wood": "<PERSON>'el toj mu xchi'uk spat", "block.minecraft.stripped_warped_hyphae": "Ts'otol ipax mu xchi'uk spat", "block.minecraft.stripped_warped_stem": "Ts'otol ch'util mu xchi'uk spat", "block.minecraft.structure_block": "<PERSON><PERSON>", "block.minecraft.structure_void": "<PERSON><PERSON><PERSON>", "block.minecraft.sugar_cane": "Vale'", "block.minecraft.sunflower": "Sun", "block.minecraft.suspicious_gravel": "Chopol xixibton", "block.minecraft.suspicious_sand": "<PERSON>pol yi'", "block.minecraft.sweet_berry_bush": "Te'el<PERSON>a", "block.minecraft.tall_dry_grass": "<PERSON>il takin yaxal", "block.minecraft.tall_grass": "<PERSON><PERSON>l", "block.minecraft.tall_seagrass": "<PERSON><PERSON> na<PERSON> yaxal", "block.minecraft.target": "Riana", "block.minecraft.terracotta": "Lakal ach'el", "block.minecraft.test_block": "<PERSON><PERSON> preva", "block.minecraft.test_instance_block": "<PERSON><PERSON> yilobil preva", "block.minecraft.tinted_glass": "<PERSON><PERSON><PERSON><PERSON><PERSON> nen", "block.minecraft.tnt": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tnt.disabled": "Tubbilik li t'omesrinamitaetike", "block.minecraft.torch": "<PERSON><PERSON>'ob toj", "block.minecraft.torchflower": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.torchflower_crop": "Ts'un nop'olnichim", "block.minecraft.trapped_chest": "Kaxa pets'", "block.minecraft.trial_spawner": "Jvina<PERSON>s t<PERSON>", "block.minecraft.tripwire": "No tsanel", "block.minecraft.tripwire_hook": "Kancho no", "block.minecraft.tube_coral": "<PERSON>bo nabalte'", "block.minecraft.tube_coral_block": "Kubo tubo nabalte'", "block.minecraft.tube_coral_fan": "Tubo korkonia", "block.minecraft.tube_coral_wall_fan": "Tubo korkonia ta kubo", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "<PERSON>a latriyo tova", "block.minecraft.tuff_brick_stairs": "Ixkalera latriyo tova", "block.minecraft.tuff_brick_wall": "<PERSON>'bel na latriyo tova", "block.minecraft.tuff_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.tuff_slab": "<PERSON><PERSON> tova", "block.minecraft.tuff_stairs": "<PERSON><PERSON><PERSON><PERSON> tova", "block.minecraft.tuff_wall": "<PERSON>'bel na tova", "block.minecraft.turtle_egg": "Ton ok", "block.minecraft.twisting_vines": "Ts'otol ak'etik", "block.minecraft.twisting_vines_plant": "Ts'otol ak'", "block.minecraft.vault": "Arkon", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON> no<PERSON>'o<PERSON><PERSON>o", "block.minecraft.vine": "Ak'etik", "block.minecraft.void_air": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.wall_torch": "J<PERSON>'ob toj ta kubo", "block.minecraft.warped_button": "Lukluk voton", "block.minecraft.warped_door": "Lukluk ti'na", "block.minecraft.warped_fence": "Lukluk mok", "block.minecraft.warped_fence_gate": "Lukluk ti' mok", "block.minecraft.warped_fungus": "Ts'otol moni'", "block.minecraft.warped_hanging_sign": "Lukluk ji<PERSON>j leterero", "block.minecraft.warped_hyphae": "Ts'otol ipaxetik", "block.minecraft.warped_nylium": "Ts'otol nilio", "block.minecraft.warped_planks": "Lukluk tenelte'etik", "block.minecraft.warped_pressure_plate": "Lukluk plakail net'el", "block.minecraft.warped_roots": "Ts'otol ibeletik", "block.minecraft.warped_sign": "Lukluk let<PERSON>o", "block.minecraft.warped_slab": "Lukluk losa", "block.minecraft.warped_stairs": "Lukluk ixkalera", "block.minecraft.warped_stem": "Ts'otol ch'util", "block.minecraft.warped_trapdoor": "Lukluk ti'pets'", "block.minecraft.warped_wall_hanging_sign": "Lukluk jipiej leterero ta kubo", "block.minecraft.warped_wall_sign": "Lukluk leterero ta kubo", "block.minecraft.warped_wart_block": "<PERSON>bo ts'otol ch'okte'", "block.minecraft.water": "Vo'", "block.minecraft.water_cauldron": "Kaltera xchi'uk vo'", "block.minecraft.waxed_chiseled_copper": "Chabtabil lok'tabil k'anal k'unil tak'in", "block.minecraft.waxed_copper_block": "Cha<PERSON>abil kubo ta k'anal k'unil tak'in", "block.minecraft.waxed_copper_bulb": "<PERSON><PERSON><PERSON><PERSON> joko k'anal k'unil tak'in", "block.minecraft.waxed_copper_door": "Chabtabil ti'na k'anal k'unil tak'in", "block.minecraft.waxed_copper_grate": "<PERSON><PERSON><PERSON><PERSON> rejiya k'anal k'unil tak'in", "block.minecraft.waxed_copper_trapdoor": "Chabtasbil ti'pets' k'anal k'unil tak'in", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON><PERSON>l mokol k'anal k'unil tak'in", "block.minecraft.waxed_cut_copper_slab": "Losa chabtabil mokol k'anal k'unil tak'in", "block.minecraft.waxed_cut_copper_stairs": "Ixkalera chabtabil mokol k'anal k'unil tak'in", "block.minecraft.waxed_exposed_chiseled_copper": "Chabtabil k'oxetajesbil lok'tabil k'anal k'unil tak'in", "block.minecraft.waxed_exposed_copper": "Chabtabil k'oxetajesbil k'anal k'unil tak'in", "block.minecraft.waxed_exposed_copper_bulb": "<PERSON><PERSON> ch<PERSON> k'oxetajesbil k'anal k'unil tak'in", "block.minecraft.waxed_exposed_copper_door": "Ti'na chabtabil k'oxetajesbil k'anal k'unil tak'in", "block.minecraft.waxed_exposed_copper_grate": "<PERSON><PERSON><PERSON> chab<PERSON> k'oxetajesbil k'anal k'unil tak'in", "block.minecraft.waxed_exposed_copper_trapdoor": "Ti'pets' chabtabil k'oxetajesbil k'anal k'unil tak'in", "block.minecraft.waxed_exposed_cut_copper": "Chabtabil k'oxetajesbil mokol k'anal k'unil tak'in", "block.minecraft.waxed_exposed_cut_copper_slab": "Losa chabtabil k'oxetajesbil mokol k'anal k'unil tak'in", "block.minecraft.waxed_exposed_cut_copper_stairs": "Ixkalera chabtabil k'oxetajesbil mokol k'anal k'unil tak'in", "block.minecraft.waxed_oxidized_chiseled_copper": "Chabtabil kux lok'tabil k'anal k'unil tak'in", "block.minecraft.waxed_oxidized_copper": "Chabtabil kux k'anal k'unil tak'in", "block.minecraft.waxed_oxidized_copper_bulb": "<PERSON><PERSON> ch<PERSON> kux k'anal k'unil tak'in", "block.minecraft.waxed_oxidized_copper_door": "Ti'na chabtabil kux k'anal k'unil tak'in", "block.minecraft.waxed_oxidized_copper_grate": "<PERSON><PERSON><PERSON> chab<PERSON> kux k'anal k'unil tak'in", "block.minecraft.waxed_oxidized_copper_trapdoor": "Ti'pets' chabtabil kux k'anal k'unil tak'in", "block.minecraft.waxed_oxidized_cut_copper": "Cha<PERSON><PERSON>l kux mokol k'anal k'unil tak'in", "block.minecraft.waxed_oxidized_cut_copper_slab": "Losa chabtabil kux mokol k'anal k'unil tak'in", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Ixkalera chabtabil kux mokol k'anal k'unil tak'in", "block.minecraft.waxed_weathered_chiseled_copper": "Chabtabil sokem lok'tabil k'anal k'unil tak'in", "block.minecraft.waxed_weathered_copper": "Chabtabil sokem k'anal k'unil tak'in", "block.minecraft.waxed_weathered_copper_bulb": "<PERSON><PERSON> chab<PERSON> sokem k'anal k'unil tak'in", "block.minecraft.waxed_weathered_copper_door": "Ti'na chabtabil sokem k'anal k'unil tak'in", "block.minecraft.waxed_weathered_copper_grate": "<PERSON><PERSON><PERSON> chabtabil sokem k'anal k'unil tak'in", "block.minecraft.waxed_weathered_copper_trapdoor": "Ti'pets' chabtabil sokem k'anal k'unil tak'in", "block.minecraft.waxed_weathered_cut_copper": "Chabtabil sokem mokol k'anal k'unil tak'in", "block.minecraft.waxed_weathered_cut_copper_slab": "Losa chabtabil sokem mokol k'anal k'unil tak'in", "block.minecraft.waxed_weathered_cut_copper_stairs": "Ixkalera chabtabil sokem mokol k'anal k'unil tak'in", "block.minecraft.weathered_chiseled_copper": "Sokem lok'tabil k'anal k'unil tak'in", "block.minecraft.weathered_copper": "Sokem k'anal k'unil tak'in", "block.minecraft.weathered_copper_bulb": "<PERSON><PERSON> sokem k'anal k'unil tak'in", "block.minecraft.weathered_copper_door": "Ti'na sokem k'anal k'unil tak'in", "block.minecraft.weathered_copper_grate": "<PERSON><PERSON>ya sokem k'anal k'unil tak'in", "block.minecraft.weathered_copper_trapdoor": "Ti'pets' sokem k'anal k'unil tak'in", "block.minecraft.weathered_cut_copper": "Sokem mokol k'anal k'unil tak'in", "block.minecraft.weathered_cut_copper_slab": "Losa sokem mokol k'anal k'unil tak'in", "block.minecraft.weathered_cut_copper_stairs": "Ixkalera sokem mokol k'anal k'unil tak'in", "block.minecraft.weeping_vines": "J-ok'el ak'etik", "block.minecraft.weeping_vines_plant": "J-ok'el ak'", "block.minecraft.wet_sponge": "Losol esponja", "block.minecraft.wheat": "Ts'un kaxlan ixim", "block.minecraft.white_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.white_bed": "Sakil tem", "block.minecraft.white_candle": "<PERSON><PERSON><PERSON> kantela", "block.minecraft.white_candle_cake": "Pastel xchi'uk sakil kantela", "block.minecraft.white_carpet": "Sakil tapete", "block.minecraft.white_concrete": "Sakil konkereto", "block.minecraft.white_concrete_powder": "Sakil semento", "block.minecraft.white_glazed_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.white_shulker_box": "Sakil kaxa yu'un shulker", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON> nen", "block.minecraft.white_stained_glass_pane": "Sakil panel nen", "block.minecraft.white_terracotta": "Sakil lakal ach'el", "block.minecraft.white_tulip": "Sakil tulipan", "block.minecraft.white_wool": "Saki<PERSON> tsots", "block.minecraft.wildflowers": "Te'tikal nichimetik", "block.minecraft.wither_rose": "<PERSON><PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON><PERSON><PERSON> sjol <PERSON> ta kubo", "block.minecraft.yellow_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_bed": "K'anal tem", "block.minecraft.yellow_candle": "<PERSON>'<PERSON> kantela", "block.minecraft.yellow_candle_cake": "Pastel xchi'uk k'anal kantela", "block.minecraft.yellow_carpet": "K'anal tapete", "block.minecraft.yellow_concrete": "<PERSON>'<PERSON> konkereto", "block.minecraft.yellow_concrete_powder": "K'anal semento", "block.minecraft.yellow_glazed_terracotta": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_shulker_box": "K'anal kaxa yu'un shulker", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON> nen", "block.minecraft.yellow_stained_glass_pane": "K'anal panel nen", "block.minecraft.yellow_terracotta": "K'anal lakal ach'el", "block.minecraft.yellow_wool": "<PERSON><PERSON><PERSON> tsots", "block.minecraft.zombie_head": "<PERSON><PERSON><PERSON>", "block.minecraft.zombie_wall_head": "<PERSON><PERSON><PERSON> j<PERSON><PERSON> ta kubo", "book.byAuthor": "yu'un %1$s", "book.edit.title": "Spantaya sjelel vun", "book.editTitle": "Sbi vun:", "book.finalizeButton": "Tstik' abi xchi'uk tsmak", "book.finalizeWarning": "¡Avi! K'alal xatik'be abi li vune, mu xu' xa xajel.", "book.generation.0": "Bats'i", "book.generation.1": "Slok'tael bats'i", "book.generation.2": "Slok'tael jun lok'tael", "book.generation.3": "Sokem", "book.invalid.tag": "* chopol etiketa *", "book.pageIndicator": "Pajina %1$s ta %2$s", "book.page_button.next": "<PERSON>jina ta stuk'il", "book.page_button.previous": "<PERSON><PERSON><PERSON> ta pat", "book.sign.title": "Spantaya stik'el sbi vun", "book.sign.titlebox": "Biil", "book.signButton": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "book.view.title": "Spantaya sk'elel vun", "build.tooHigh": "Li sts'ak natilale sventa va'anel ja' %s kubo", "chat.cannotSend": "Mu xu' stak li mantale, k'elano li at'ujel lo'ile", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Paso klik sventa xatak aba", "chat.copy": "Tslok'ta ts'akel", "chat.copy.click": "Paso klik sventa xalok'ta", "chat.deleted_marker": "Tup'at yu'un servilor li mantal li'e.", "chat.disabled.chain_broken": "<PERSON>bbil li lo'ile yu'un jatem li karenail mantale. Cha'ts'ako aba, avokoluk.", "chat.disabled.expiredProfileKey": "<PERSON>bb<PERSON> li lo'ile yu'un laj li komon yavi' perxile. Cha'ts'ako aba, avokoluk.", "chat.disabled.invalid_command_signature": "Oy stik'el sbiik arkumentoetik li mantale ti mu smalaoj o ti sk'an toe.", "chat.disabled.invalid_signature": "Oy xchopol tik'el sbi li lo'ile. Cha'ts'ako aba, avokoluk.", "chat.disabled.launcher": "Tubbil ta st'ujeltak jlikestajimol li lo'ile. Mu xu' stak li mantale.", "chat.disabled.missingProfileKey": "Tubbil li lo'ile yu'un sk'an li komon yavi' perxile. Cha'ts'ako aba, avokoluk.", "chat.disabled.options": "Tubbil ta st'ujeltak kiliente li lo'ile.", "chat.disabled.out_of_order_chat": "Tana mu x-abtej li lo'ile. ¿Mi la sjel sba li sk'ak'alil asistemae?", "chat.disabled.profile": "Mu ak'bil ta <PERSON>inel akuenta li lo'ile. Net'o ''%s'' nixtok sventa mas alelal.", "chat.disabled.profile.moreInfo": "<PERSON>bbil ta stsinel akuenta li lo'ile. Mu xu' stak o sk'el mantaletike.", "chat.editBox": "lo'il", "chat.filtered": "<PERSON>ch'esbil yu'un li servilore.", "chat.filtered_full": "La snak' amantaltak yu'un jayibuk jtajimoletik li servilore.", "chat.link.confirm": "¿Mi melel xak'an xajam li av internet li'e?", "chat.link.confirmTrusted": "¿Mi xak'an xajam li enlase li'e o xalok'ta?", "chat.link.open": "Tsjam enlase", "chat.link.warning": "¡Mu xajam enlaseetik yu'un mu ojtikinbil krixchanoetik!", "chat.queue": "[+%s mantaletik sk'an to]", "chat.square_brackets": "[%s]", "chat.tag.error": "La stak jun chopol mantal li servilore.", "chat.tag.modified": "Mantal jelbil yu'un li servilore. Bats'i:", "chat.tag.not_secure": "<PERSON> k'elan<PERSON> mantal. Mu xu' xal smul.", "chat.tag.system": "<PERSON><PERSON><PERSON> servilor. Mu xu' xal smul.", "chat.tag.system_single_player": "<PERSON><PERSON><PERSON> servilor.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "La sts'akan li tsatsalile %2$s li %1$se", "chat.type.advancement.goal": "La sta li objetivoe %2$s li %1$se", "chat.type.advancement.task": "La spas li porokresoe %2$s li %1$se", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Tstakbe mantal li ekipoe", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "Xal li %se: %s", "chat.validation_error": "<PERSON><PERSON><PERSON> k'alal ta xa'i ox li lo'ile", "chat_screen.message": "Mantal ta takel: %s", "chat_screen.title": "Pantaya lo'il", "chat_screen.usage": "Ts'ibao jun mantal xchi'uk net'o Enter sventa xatak", "chunk.toast.checkLog": "<PERSON>y mas alelal ta <PERSON><PERSON><PERSON> ta<PERSON>l", "chunk.toast.loadFailure": "Cho<PERSON><PERSON> k'alal tsvinajes ox ta %s li set'ele", "chunk.toast.lowDiskSpace": "¡Oy jutuk yav li riskoe!", "chunk.toast.lowDiskSpace.description": "Mu sk'ej nan li balumile.", "chunk.toast.saveFailure": "<PERSON><PERSON><PERSON> k'alal tsk'ej ox ta %s li set'ele", "clear.failed.multiple": "Mu la sta k'usitik ta sk'ejebik %s jtajimoletik", "clear.failed.single": "Mu la sta k'usitik ta sk'ejeb %s", "color.minecraft.black": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.blue": "Ya<PERSON>l", "color.minecraft.brown": "K'anch'etan", "color.minecraft.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color.minecraft.gray": "K'anch'etan", "color.minecraft.green": "Ya<PERSON>l", "color.minecraft.light_blue": "Ya<PERSON>l", "color.minecraft.light_gray": "K'anch'etan", "color.minecraft.lime": "Yoxyoxtik", "color.minecraft.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON>", "color.minecraft.orange": "K'onk'ontik", "color.minecraft.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON>", "color.minecraft.purple": "Ik'pok'an", "color.minecraft.red": "<PERSON><PERSON><PERSON><PERSON>", "color.minecraft.white": "Sakil", "color.minecraft.yellow": "K'anal", "command.context.here": "<--[LI'E]", "command.context.parse_error": "%s ta posision %s: %s", "command.exception": "Mu xu' spas analisar li mantale: %s", "command.expected.separator": "Sk'an jun pojol avil sventa tslajes jun arkumento. K'elano xchi'uk ch'ako li alelale", "command.failed": "K'ot ta pasel jun chopolal k'alal ta ox sk'an chabtelan li mantal le'e", "command.forkLimit": "Sts'ak yatolalik kontekstoetik (%s) tabil", "command.unknown.argument": "Chopol arkumento", "command.unknown.command": "Mu ojtikinbil o mu ts'akal mantal, k'elano li ch'ay-o'ontonale ta olon", "commands.advancement.criterionNotFound": "Mu'yuk kriterio ''%2$s\" yu'un porokreso %1$s", "commands.advancement.grant.criterion.to.many.failure": "Mu xu' xak'bik kriterio \"%s\" yu'un porokreso %s %s jtajimoletik yu'un oy xa yu'unik", "commands.advancement.grant.criterion.to.many.success": "La yak'bik kriterio ''%s'' yu'un porokreso %s %s jtajimoletik", "commands.advancement.grant.criterion.to.one.failure": "Mu xu' xak'be kriterio \"%s\" yu'un porokreso %s %s yu'un oy xa yu'un", "commands.advancement.grant.criterion.to.one.success": "La yak'be kriterio ''%s'' yu'un porokreso %s %s", "commands.advancement.grant.many.to.many.failure": "Mu xu' xak'bik %s porokreso %s jtajimoletik yu'un oy xa yu'unik", "commands.advancement.grant.many.to.many.success": "La yak'bik %s porokreso %s jtajimoletik", "commands.advancement.grant.many.to.one.failure": "Mu xu' xak'be %s porokreso %s yu'un oy xa yu'un", "commands.advancement.grant.many.to.one.success": "La yak'be %s porokreso %s", "commands.advancement.grant.one.to.many.failure": "Mu xu' xak'bik porokreso %s %s jtajimoletik yu'un oy xa yu'unik", "commands.advancement.grant.one.to.many.success": "La yak'bik porokreso %s %s jtajimoletik", "commands.advancement.grant.one.to.one.failure": "Mu xu' xak'be porokreso %s %s yu'un oy xa yu'un", "commands.advancement.grant.one.to.one.success": "La yak'be porokreso %s %s", "commands.advancement.revoke.criterion.to.many.failure": "Mu xu' slok'esbik kriterio ''%s'' yu'un porokreso ''%s'' %s jtajimoletik yu'un ch'abal yu'unik", "commands.advancement.revoke.criterion.to.many.success": "La slok'esbik kriterio ''%s'' yu'un porokreso %s %s jtajimoletik", "commands.advancement.revoke.criterion.to.one.failure": "Mu xu' slok'esbe kriterio \"%s\" yu'un porokreso %s %s yu'un ch'abal yu'un", "commands.advancement.revoke.criterion.to.one.success": "La slok'esbe kriterio ''%s'' yu'un porokreso %s %s", "commands.advancement.revoke.many.to.many.failure": "Mu xu' slok'esbik %s porokreso %s jtajimoletik yu'un ch'abal yu'unik", "commands.advancement.revoke.many.to.many.success": "La slok'esbik %s porokreso %s jtajimoletik", "commands.advancement.revoke.many.to.one.failure": "Mu xu' slo<PERSON>'esbe %s porokreso %s yu'un ch'abal yu'un", "commands.advancement.revoke.many.to.one.success": "La slok'esbe %s porokreso %s", "commands.advancement.revoke.one.to.many.failure": "Mu xu' slok'esbe porokreso %s %s jtajimoletik yu'un ch'abal yu'unik", "commands.advancement.revoke.one.to.many.success": "La slok'esbik porokreso %s %s jtajimoletik", "commands.advancement.revoke.one.to.one.failure": "Mu xu' slok'esbe porokreso %s %s yu'un ch'abal yu'un", "commands.advancement.revoke.one.to.one.success": "La slok'esbe porokreso %s %s", "commands.attribute.base_value.get.success": "<PERSON> sbats'i tojol atiributoe %s yu'un entiral %s ja' %s", "commands.attribute.base_value.reset.success": "Li sbat<PERSON>'i tojolilal atiributoe %s yu'un entiral %s cha'likesat ta %s", "commands.attribute.base_value.set.success": "Li sbat<PERSON>'i tojolilal atiributoe %s yu'un entiral %s la yak' sba ta %s", "commands.attribute.failed.entity": "%s ja' jun chopol entiral sventa li mantal li'e", "commands.attribute.failed.modifier_already_present": "Li jelvaneje %s te xa oy ta atiributo %s yu'un entiral %s", "commands.attribute.failed.no_attribute": "Li entirale %s ch'abal yu'un li atiributoe %s", "commands.attribute.failed.no_modifier": "Li atiributoe %s yu'un entiral %s ch'abal yu'un li jelvaneje %s", "commands.attribute.modifier.add.success": "La skapbe jelvanej %s atiributo %s yu'un entiral %s", "commands.attribute.modifier.remove.success": "La stup'be jelvaneje %s atiributo %s yu'un entiral %s", "commands.attribute.modifier.value.get.success": "Li stojolilal jelvaneje %s ta atiributo %s yu'un entiral %s ja' %s", "commands.attribute.value.get.success": "<PERSON> stojolilal atiributoe %s yu'un entiral %s ja' %s", "commands.ban.failed": "<PERSON>'<PERSON>uk jelvan: lok'esbil xa li j<PERSON><PERSON><PERSON>le", "commands.ban.success": "La slok'es %s: %s", "commands.banip.failed": "Mu'yuk jelvan: lok'esbil xa li IP le'e", "commands.banip.info": "Li' tslok'es %s jtajimol(etik)e: %s", "commands.banip.invalid": "Chopol IP o mu ojtikinbil jtajimol", "commands.banip.success": "La slok'es IP %s: %s", "commands.banlist.entry": "%s lok'esat yu'un %s: %s", "commands.banlist.entry.unknown": "(<PERSON>)", "commands.banlist.list": "Oy %s lok'esbil jtajimol(etik):", "commands.banlist.none": "Mu'yuk lok'esbil jtajimoletik", "commands.bossbar.create.failed": "Oy xa jtel bara xchi'uk ID ''%s''", "commands.bossbar.create.success": "La spas li jelbil barae %s", "commands.bossbar.get.max": "Li jelbil barae %s oy sts'ak ta %s", "commands.bossbar.get.players.none": "Ch'abal ts'akbil jtajimoletik ta jelbil bara %s", "commands.bossbar.get.players.some": "Ta jelbil bara %s tana oy %s ts'akbil jtajimol(etik): %s", "commands.bossbar.get.value": "Li jelbil barae %s oy stojolilal ta %s", "commands.bossbar.get.visible.hidden": "Nak'al xa li jelbil barae %s", "commands.bossbar.get.visible.visible": "Ak'bil xa ta ilel li jelbil barae %s", "commands.bossbar.list.bars.none": "<PERSON><PERSON><PERSON><PERSON> tsa<PERSON> jel<PERSON>k", "commands.bossbar.list.bars.some": "Oy %s tsakal jelbil bara: %s", "commands.bossbar.remove.success": "La stup' li jelbil barae %s", "commands.bossbar.set.color.success": "La sjel sbon li jelbil barae %s", "commands.bossbar.set.color.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: le' xa sbon li bara li'e", "commands.bossbar.set.max.success": "Li jelbil barae %s la sjel sts'ak ta %s", "commands.bossbar.set.max.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: le' xa sts'ak li bara li'e", "commands.bossbar.set.name.success": "La sjel sbi li jelbil barae %s", "commands.bossbar.set.name.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: le' xa ja' sbi li bara li'e", "commands.bossbar.set.players.success.none": "Ch'abal xa jtajimoletik ta jelbil bara %s", "commands.bossbar.set.players.success.some": "Ta jelbil bara %s tana oy %s jtajimol(etik): %s", "commands.bossbar.set.players.unchanged": "Mu'yuk jelvan: te xa oyik ta bara li jtajimoletik le'e mu xchi'uk jun krixchano sventa skapik o slok'esik", "commands.bossbar.set.style.success": "La sjel estilo li jelbil barae %s", "commands.bossbar.set.style.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: le' xa ja' yestilo li bara li'e", "commands.bossbar.set.value.success": "Li jelbil barae %s la sjel stojolilal ta %s", "commands.bossbar.set.value.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: le' xa ja' sto<PERSON><PERSON>lal li bara li'e", "commands.bossbar.set.visibility.unchanged.hidden": "<PERSON><PERSON><PERSON><PERSON> jelvan: nak'al xa li barae", "commands.bossbar.set.visibility.unchanged.visible": "<PERSON><PERSON><PERSON><PERSON> jelvan: xvinaj xa li barae", "commands.bossbar.set.visible.success.hidden": "<PERSON><PERSON> nak'al li jelbil barae %s", "commands.bossbar.set.visible.success.visible": "Tana x<PERSON>j li jelbil barae %s", "commands.bossbar.unknown": "Ch'abal junuk bara xchi'uk ID ''%s''", "commands.clear.success.multiple": "La slok'esbik %s item(etik) %s jtajimoletik", "commands.clear.success.single": "La slok'esbe %s item(etik) %s", "commands.clear.test.multiple": "Oy %s item(etik) ko'olik ta %s jtajimoletik", "commands.clear.test.single": "Oy %s item(etik) ko'olik ta jtajimol %s", "commands.clone.failed": "Mu la spas klonar kuboetik", "commands.clone.overlap": "Mu xu' ko'olik li yave xchi'uk li srestinoe", "commands.clone.success": "Lek la spas klonar %s kubo", "commands.clone.toobig": "Oy ep kuboetik ta k'anbil avil (sts'ak ja' %s, la sk'an %s)", "commands.damage.invulnerable": "Tsots xchi'uk tos yajel le'e li objetivoe", "commands.damage.success": "La spasbe %s ta yajel %s", "commands.data.block.get": "%s yu'un kubo ta %s, %s, %s, ta spat paktor eskala ta %s jelat ta %s", "commands.data.block.invalid": "<PERSON> ja' jbej entiral li k'anbil kuboe", "commands.data.block.modified": "<PERSON><PERSON> li ya<PERSON>lal kuboe ta %s, %s, %s", "commands.data.block.query": "Li kuboe ta %s, %s, %s chu'unin li alelal li'e: %s", "commands.data.entity.get": "%s ta %s ta spat paktor eskala ta %s, jelat ta %s", "commands.data.entity.invalid": "<PERSON> xu' s<PERSON><PERSON> li ya<PERSON> j<PERSON>", "commands.data.entity.modified": "La sjel li yalelal entirale %s", "commands.data.entity.query": "Te oy ta entiral %s li alelal li'e: %s", "commands.data.get.invalid": "Mu xu' sa' %s, ch-ak'atik no'ox li etiketa atolaletike", "commands.data.get.multiple": "Ta xak' no'ox jun tojo<PERSON>lal NBT li arkumento li'e", "commands.data.get.unknown": "<PERSON> xu' sa' %s, ch'abal li etiketae", "commands.data.merge.failed": "<PERSON>'<PERSON>uk jelvan: oy xa li stojolilaltak le'e li k'anbil u'unineletike", "commands.data.modify.expected_list": "Sk'an jun lista (la yich': %s)", "commands.data.modify.expected_object": "Sk'an jun k'usi (la yich': %s)", "commands.data.modify.expected_value": "<PERSON>k'an jun tojo<PERSON> (la yich': %s)", "commands.data.modify.invalid_index": "Chopol sa'obil lista: %s", "commands.data.modify.invalid_substring": "Chopol sa'obil subkarenaetik: %s ta %s", "commands.data.storage.get": "%s ta k'ejobil %s ta spat paktor eskala ta %s jelvan ta %s", "commands.data.storage.modified": "Je<PERSON> li k'ejobile %s", "commands.data.storage.query": "Li k'ejobile %s oy yu'un: %s", "commands.datapack.create.already_exists": "Oy xa li pakete alelale ''%s''", "commands.datapack.create.invalid_full_name": "Sbi chopol ach' pakete: ''%s''", "commands.datapack.create.invalid_name": "Chopol karakteretik ta ach' sbi pakete alelal ''%s''", "commands.datapack.create.io_failure": "Mu xu' spas pakete alelal xchi'uk sbi ''%s'', k'elano li ts'ibaeletike", "commands.datapack.create.metadata_encode_failure": "Cho<PERSON><PERSON> k'alal chak'bik ox skotikoik li metaalelaletike sventa li paketee xchi'uk sbi ''%s'': %s", "commands.datapack.create.success": "La spas jun ach' pakete alelal xchi'uk sbi ''%s''", "commands.datapack.disable.failed": "¡Mu tsakal ox li paketee ''%s''!", "commands.datapack.disable.failed.feature": "¡Mu xu' stub sba li paketee ''%s'' yu'un tsmeltsan jun tsakal jelbilal!", "commands.datapack.enable.failed": "¡Tsakal xa li paketee ''%s''!", "commands.datapack.enable.failed.no_flags": "Mu xu' stsoy sba li paketee ''%s'' yu'un sk'an jelbilaletik mu tsakalik ta balumil li'e: %s", "commands.datapack.list.available.none": "Ch'abal yantik pakete alelaletik xu' xtunesatik", "commands.datapack.list.available.success": "Oy %s pakete alelal xu' xtunesat: %s", "commands.datapack.list.enabled.none": "<PERSON>'abal yantik tsakal pakete alelaletik", "commands.datapack.list.enabled.success": "Oy %s tsakal pakete alelal: %s", "commands.datapack.modify.disable": "Yakal tstub li pakete alelale %s", "commands.datapack.modify.enable": "Yakal tstsan li pakete alelale %s", "commands.datapack.unknown": "Mu ojtikinbil pakete alelal ''%s''", "commands.debug.alreadyRunning": "Lik xa ox li analisis sikloe", "commands.debug.function.noRecursion": "Mu xu' st'unbe yav ta yut jun tunel", "commands.debug.function.noReturnRun": "Mu xu' st'unbe yav xchi'uk ''/return run''", "commands.debug.function.success.multiple": "La st'unbe yav %s mantal ta %s tunel ta archivo %s", "commands.debug.function.success.single": "La st'unbe yav %s mantal ta tunel %s ta archivo %s", "commands.debug.function.traceFailed": "<PERSON><PERSON><PERSON> k'alal st'unbe ox yav li tunele", "commands.debug.notRunning": "Mu lik ox li analisis sikloe", "commands.debug.started": "Lik li analisis sikloe", "commands.debug.stopped": "Paj li analisis sikloe ta spat %s sekunto xchi'uk %s siklo (%s siklo ta sekunto)", "commands.defaultgamemode.success": "Tana ja' %s li bats'i tos tajimole", "commands.deop.failed": "<PERSON> jelvan: mu ja' ox jun jcha<PERSON><PERSON>ej li j<PERSON><PERSON>le", "commands.deop.success": "%s mu ja' xa jun j<PERSON><PERSON>ej", "commands.dialog.clear.multiple": "La stup'bik lo'il %s jtajimoletik", "commands.dialog.clear.single": "La stup'be lo'il %s", "commands.dialog.show.multiple": "La yak'bik ta ilel lo'il %s jtajimoletik", "commands.dialog.show.single": "La yak'be ta ilel lo'il %s", "commands.difficulty.failure": "Mu'yuk jelvan: komesbil xa li tsatsalile ta %s", "commands.difficulty.query": "Ja' %s li tsatsalile", "commands.difficulty.success": "<PERSON><PERSON> ta %s li tsatsalile", "commands.drop.no_held_items": "<PERSON> xu' s<PERSON><PERSON> k'usitik li entirale", "commands.drop.no_loot_table": "Ch'abal xmexa elek' li entirale %s", "commands.drop.no_loot_table.block": "Ch'abal xmexa elek' li kuboe %s", "commands.drop.success.multiple": "La spajes %s item", "commands.drop.success.multiple_with_table": "La spajes %s item k'alal ta mexa elek' %s", "commands.drop.success.single": "La spajes %s k'usi ta %s", "commands.drop.success.single_with_table": "La spajes %s k'usi ta %s k'alal ta mexa elek' %s", "commands.effect.clear.everything.failed": "Ch'<PERSON>bal <PERSON> li objetivoe sventa slok'esbe", "commands.effect.clear.everything.success.multiple": "La stup' skotol li yejektotakik %s objetivoetike", "commands.effect.clear.everything.success.single": "Tup'atik skotol li yejektotak %se", "commands.effect.clear.specific.failed": "Mu xu'unin li k'anbil ejekto li objetivoe", "commands.effect.clear.specific.success.multiple": "La stup'bik ejekto %s %s objetivoetik", "commands.effect.clear.specific.success.single": "La stup'be ejekto %s %s", "commands.effect.give.failed": "Mu xu' stunes li ejekto li'e (tsots xchi'uk ejektoetik li objetivoe o oy yu'un k'usi mas tsots)", "commands.effect.give.success.multiple": "La yak'bik ejekto %s %s objetivoetik", "commands.effect.give.success.single": "La yak'bik ejekto %s %s", "commands.enchant.failed": "Mu jelvan: ch'abal ox jun item ta sk'ob objetivo o mu xch'un kapel li iteme", "commands.enchant.failed.entity": "%s ja' jun chopol entiral sventa li mantal li'e", "commands.enchant.failed.incompatible": "%s mu sts'ik li kapel li'e", "commands.enchant.failed.itemless": "%s mu sjap junuk item", "commands.enchant.failed.level": "%s ja' mas muk' ke li sts'ak snivel kapele (%s)", "commands.enchant.success.multiple": "La yak'be %s %s entiral", "commands.enchant.success.single": "La yak'be kapel %s yitem %s", "commands.execute.blocks.toobig": "Oy ep kuboetik ta k'anbil avil (sts'ak ja' %s, la sk'an %s)", "commands.execute.conditional.fail": "<PERSON><PERSON><PERSON> li pruebae", "commands.execute.conditional.fail_count": "<PERSON><PERSON><PERSON> li <PERSON>, yepal: %s", "commands.execute.conditional.pass": "<PERSON><PERSON> li pruebae", "commands.execute.conditional.pass_count": "<PERSON><PERSON> li <PERSON>, yepal: %s", "commands.execute.function.instantiationFailure": "Cho<PERSON><PERSON> k'alal smeltsan ox li tunele %s: %s", "commands.experience.add.levels.success.multiple": "La yak'bik %s nivel k<PERSON>lejal %s jtajimoletik", "commands.experience.add.levels.success.single": "La yak'be %s nivel kuxlejal %s", "commands.experience.add.points.success.multiple": "La yak'bik %s kuxlejal %s jtajimoletik", "commands.experience.add.points.success.single": "La yak'be %s kuxlejal %s", "commands.experience.query.levels": "%s oy %s snivel kuxlejal", "commands.experience.query.points": "%s oy %s xkuxlejal", "commands.experience.set.levels.success.multiple": "La sjel li snivel xkuxlejalik %2$s jtajimoletike ta %1$s", "commands.experience.set.levels.success.single": "La sjel li snivel xkuxlejal %2$se ta %1$s", "commands.experience.set.points.invalid": "Mu xu' xak'be kuxlejal ta yak'ol sts'ak snivel jtajimol", "commands.experience.set.points.success.multiple": "La sjel li xkuxlejalik %2$s jtajimoletike ta %1$s", "commands.experience.set.points.success.single": "La sjel li xkuxlejal %2$se ta %1$s", "commands.fill.failed": "Mu la sbut' kuboetik", "commands.fill.success": "Lek la sbut' %s kubo", "commands.fill.toobig": "Oy ep kuboetik ta k'anbil avil (sts'ak ja' %s, la sk'an %s)", "commands.fillbiome.success": "Komesbilik li osiletike ta o'lol %s, %s, %s xchi'uk %s, %s, %s", "commands.fillbiome.success.count": "Oy %s kubo osil ta o'lol %s, %s, %s xchi'uk %s, %s, %s", "commands.fillbiome.toobig": "Oy ep kuboetik ta k'anbil volumen (sts'ak ja' %s, la sk'an %s)", "commands.forceload.added.failure": "<PERSON><PERSON><PERSON><PERSON> jelvan: mu meltsaj li tsatsal vinajesele yu'un set'eletik", "commands.forceload.added.multiple": "La stsoy tsatsal vinajesel sventa %s ta set' ta %s (ta %s ta %s)", "commands.forceload.added.none": "Ch'abal set'eletik xchi'uk tsatsal vinajesel ta %s", "commands.forceload.added.single": "La stsoy tsatsal vinajesel sventa li set'ele ta %s", "commands.forceload.list.multiple": "Oy %s ta set' xchi'uk tsatsal vinajesel ta %s: %s", "commands.forceload.list.single": "Oy jset' xchi'uk tsatsal vinajesel ta %s: %s", "commands.forceload.query.failure": "Tubbil li stsatsal vina<PERSON>sel set'ele %s ta %s", "commands.forceload.query.success": "Tsakal li stsatsal vinajesel set'ele %s ta %s", "commands.forceload.removed.all": "Tsatsal vinajesel tubbil sventa skotol li set'eletike ta %s", "commands.forceload.removed.failure": "<PERSON><PERSON><PERSON><PERSON> jelvan: mu la stub sba li tsatsal vinajesele sventa set'eletik", "commands.forceload.removed.multiple": "Tsatsal vinajesel tubbil sventa %s ta set' ta %s (ta %s ta %s)", "commands.forceload.removed.single": "Tsatsal vinajesel tubbil sventa li set'ele %s ta %s", "commands.forceload.toobig": "Oy ep set'eletik ta k'anbil avil (sts'ak ja' %s, la sk'an %s)", "commands.function.error.argument_not_compound": "Chopol tos arkumento: %s, la sk'an meltsajel", "commands.function.error.missing_argument": "Sk'an li arkumentoe %2$s ta tunel %1$s", "commands.function.error.missing_arguments": "Sk'an arkumentoetik ta tunel %s", "commands.function.error.parse": "K'alal smeltsan ox li makroe %s: li mantale ''%s'' la spas li ch'ay-o'ontonale: %s", "commands.function.instantiationFailure": "<PERSON><PERSON><PERSON> k'alal tsm<PERSON>an ox li tunele %s: %s", "commands.function.result": "Li tunele %s la sutes %s", "commands.function.scheduled.multiple": "Tsakal tuneletik %s", "commands.function.scheduled.no_functions": "Mu xu' sta tuneletik xchi'uk biil %s", "commands.function.scheduled.single": "Tsakal tunel %s", "commands.function.success.multiple": "La smeltsan %s mantal ta %s tunel", "commands.function.success.multiple.result": "La smeltsan %s tunel", "commands.function.success.single": "La smeltsan %s mantal ta tunel ''%s''", "commands.function.success.single.result": "Li tunele ''%2$s'' la sutes %1$s", "commands.gamemode.success.other": "La sjel tos tajimol yu'un %s ta %s", "commands.gamemode.success.self": "La sjel tos tajimol ta %s", "commands.gamerule.query": "Li leye %s ja' komesbil jech k'ucha'al ''%s''", "commands.gamerule.set": "La sjel li leye %s ta ''%s''", "commands.give.failed.toomanyitems": "Mu xu' xak' mas ti %s ta %s", "commands.give.success.multiple": "La yak'bik %s ta %s %s jtajimoletik", "commands.give.success.single": "La yak'be %s ta %s %s", "commands.help.failed": "Mu ojtikinbil mantal o mu xe'el ak'eletik", "commands.item.block.set.success": "La sk'exta jun avil ta %s, %s, %s xchi'uk %s", "commands.item.entity.set.success.multiple": "La sk'exta jun avil ta %s entiral xchi'uk %s", "commands.item.entity.set.success.single": "La sk'exta jun avil ta %s xchi'uk %s", "commands.item.source.no_such_slot": "Ch'abal avil %s yu'un li objetivoe", "commands.item.source.not_a_container": "Mu ja' jun k'ejobil li posisione %s, %s, %s", "commands.item.target.no_changed.known_item": "Ch'abal objetivoetik ti la xch'unik li iteme %s ta avil %s", "commands.item.target.no_changes": "Ch'abal objetivoetik ti la xch'unik li iteme ta avil %s", "commands.item.target.no_such_slot": "Ch'abal avil %s yu'un li objetivoe", "commands.item.target.not_a_container": "Mu ja' jun k'ejobil li objetivoe ta koortenaraetik %s, %s, %s", "commands.jfr.dump.failed": "Chopolaj k'alal tslok'ta ox li alele JFR: %s", "commands.jfr.start.failed": "Cho<PERSON><PERSON> k'alal tslikes ox li alele JFR", "commands.jfr.started": "Lik li alele JFR", "commands.jfr.stopped": "Paj xchi'uk la slok'ta sba ta %s li alele JFR", "commands.kick.owner.failed": "Mu xu' slok'es li yajval jun balumile ta LAN", "commands.kick.singleplayer.failed": "Mu xu' x<PERSON><PERSON>'esvan ta jun balumil sventa jun no'ox jtajimol", "commands.kick.success": "La slok'es %s: %s", "commands.kill.success.multiple": "La smil %s entiraletik", "commands.kill.success.single": "La amil %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Oy %s ta %s jtajimoletik xchi'uk internet: %s", "commands.locate.biome.not_found": "Mu xu' sta li osile ''%s'' ta jun lekil namal", "commands.locate.biome.success": "Li mas nopol osile ''%s'' te oy ta %s (Ta %s kubo snamal)", "commands.locate.poi.not_found": "Mu xu' sta li av interese ''%s'' ta jun lekil namal", "commands.locate.poi.success": "Li mas nopol estrukturae ''%s'' te oy ta %s (Ta %s kubo snamal)", "commands.locate.structure.invalid": "Ch'abal estruktura xchi'uk tos ''%s''", "commands.locate.structure.not_found": "Mu xu' sta li mas nopol estrukturae ''%s''", "commands.locate.structure.success": "Li mas nopol estrukturae ''%s'' te oy ta %s (Ta %s kubo snamal)", "commands.message.display.incoming": "%s tsa<PERSON><PERSON><PERSON><PERSON> avu'un: %s", "commands.message.display.outgoing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yu'un %s: %s", "commands.op.failed": "<PERSON> jelvan: mu ja' ox jun jcha<PERSON><PERSON>ej li j<PERSON><PERSON>le", "commands.op.success": "%s k'ataj ta jchapanvanej ta servilor", "commands.pardon.failed": "<PERSON><PERSON><PERSON><PERSON> jelvan: mu lok'esbil ox li j<PERSON><PERSON><PERSON>le", "commands.pardon.success": "La xcha'ak' %s", "commands.pardonip.failed": "<PERSON>'yuk jelvan: mu lok'esbil ox li IP le'e", "commands.pardonip.invalid": "Chopol IP", "commands.pardonip.success": "La xcha'ak' IP %s", "commands.particle.failed": "Mu buch'u xu' ox xil li ts'ubilale", "commands.particle.success": "Yakal chak' ta ilel li ts'ubilale %s", "commands.perf.alreadyRunning": "Lik xa ox li analisis rentimientoe", "commands.perf.notRunning": "Mu lik ox li analisis rentimientoe", "commands.perf.reportFailed": "Cho<PERSON><PERSON> k'alal tsts'iba ox li al-kusele", "commands.perf.reportSaved": "La spas li al-kusele ta %s", "commands.perf.started": "Lik li analisis rentimientoe ta 10 sekunto (tuneso ''/perf stop'' sventa xapajes ta ba'i)", "commands.perf.stopped": "Paj li analisis rentimientoe ech'em xa %s sekunto xchi'uk %s siklo (%s siklo ta sekunto)", "commands.place.feature.failed": "<PERSON><PERSON><PERSON> k'alal chak' ox li elementoe", "commands.place.feature.invalid": "Ch'abal elemento xchi'uk tos ''%s''", "commands.place.feature.success": "La yak' ''%s'' ta %s, %s, %s", "commands.place.jigsaw.failed": "<PERSON><PERSON><PERSON> k'alal chak' ox li k'echobe", "commands.place.jigsaw.invalid": "Ch'abal tsobol palantiya ''%s''", "commands.place.jigsaw.success": "La spas li k'echobe ta %s, %s, %s", "commands.place.structure.failed": "<PERSON><PERSON><PERSON> k'alal chak' ox li estrukturae", "commands.place.structure.invalid": "Ch'abal estruktura xchi'uk tos ''%s''", "commands.place.structure.success": "La spas li estrukturae ''%s'' ta %s, %s, %s", "commands.place.template.failed": "<PERSON><PERSON><PERSON> k'alal chak' ox li palantiyae", "commands.place.template.invalid": "Ch'abal palantiya ''%s''", "commands.place.template.success": "La svinajes li palantiyae ''%s'' ta %s, %s, %s", "commands.playsound.failed": "Toj nom xchi'uk mu xu' x-a'iat li nuk'ilale", "commands.playsound.success.multiple": "Tij li nuk'ilale %s yu'un %s jtajimoletik", "commands.playsound.success.single": "Tij li nuk'ilale %s yu'un %s", "commands.publish.alreadyPublished": "Jamal xa ox ta LAN avu'un ta puerto %s li balumile", "commands.publish.failed": "<PERSON> xu' sjam ta LAN avu'un li balumile", "commands.publish.started": "Jamal ta puerto %s li balumile ta LAN", "commands.publish.success": "Jamal ta LAN avu'un ta puerto %s li balumile", "commands.random.error.range_too_large": "Mu xu' ja' mas muk' ti 2147483646 li yintervalo k'uk elanuk tojo<PERSON>e", "commands.random.error.range_too_small": "Mu xu' x-elan mas bik'it ti 2 li yintervalo k'uk elanuk tojolilale", "commands.random.reset.all.success": "La xcha'likes %s k'uk no'ox sekuensia", "commands.random.reset.success": "La xcha'likes li k'uk no'ox sekuensiae %s", "commands.random.roll": "Li %se la slok'es %s (ta o'lol %s xchi'uk %s)", "commands.random.sample.success": "K'uk no'ox tojolilal: %s", "commands.recipe.give.failed": "Mu la xchan ach' resetaetik", "commands.recipe.give.success.multiple": "La sjambik %s reseta %s jtajimoletik", "commands.recipe.give.success.single": "La sjambe %s reseta %s", "commands.recipe.take.failed": "<PERSON>'a<PERSON> resetaetik xu' xch'ayik ta jolil", "commands.recipe.take.success.multiple": "La stup'bik %s reseta %s jtajimoletik", "commands.recipe.take.success.single": "La stup'be %s reseta %s", "commands.reload.failure": "<PERSON><PERSON><PERSON> k'alal tsbut' ox, la sk'ej li poko' alelale", "commands.reload.success": "¡Yakal chbut'!", "commands.ride.already_riding": "Li %se yakal xa tskajlibin %s", "commands.ride.dismount.success": "Li %se mu xa skajlibin %s", "commands.ride.mount.failure.cant_ride_players": "Mu xu' s<PERSON><PERSON><PERSON><PERSON> jun j<PERSON>l", "commands.ride.mount.failure.generic": "Li %se mu xu' skajlibin %s", "commands.ride.mount.failure.loop": "Mu xu' skaj<PERSON>bin sba o skajlibin yajxanbaltak li jun entirale", "commands.ride.mount.failure.wrong_dimension": "Mu xu' xkajlibinat ta yan rimension li jun entirale", "commands.ride.mount.success": "Li %se lik ta skajlibinel %s", "commands.ride.not_riding": "Mu yakal tskajlibin jkot karo li %se", "commands.rotate.success": "La sjoyobta %s", "commands.save.alreadyOff": "<PERSON><PERSON><PERSON> xa li k'ejele", "commands.save.alreadyOn": "Tsakal xa li k'ejele", "commands.save.disabled": "<PERSON><PERSON><PERSON> li k'ejel ta stuke", "commands.save.enabled": "Tsakal li k'ejel ta stuke", "commands.save.failed": "Mu xu' sk'ej li taji<PERSON>le (¿mi oy to avil ta tsat<PERSON>ko?)", "commands.save.saving": "Yakal tsk'ej li taji<PERSON> (¡li' xjok'tsaj nan j-ok'ele!)", "commands.save.success": "La sk'ej li taji<PERSON>le", "commands.schedule.cleared.failure": "Ch'abal nopbil abteletik xchi'uk ID %s", "commands.schedule.cleared.success": "La stup' %s nopbil abtel xchi'uk ID %s", "commands.schedule.created.function": "Nopbil li tunele ''%s'' ta %s siklo (ora ta tajimol: %s)", "commands.schedule.created.tag": "Nopbil li etiketae ''%s'' ta %s siklo (ora ta tajimol: %s)", "commands.schedule.macro": "<PERSON> xu' snop jun makro", "commands.schedule.same_tick": "<PERSON> xu' snop sba sventa li siklo ti oy tanae", "commands.scoreboard.objectives.add.duplicate": "Oy xa jun objetivo xchi'uk li bill li'e", "commands.scoreboard.objectives.add.success": "La spas li objetivoe %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Mu'yuk jelvan: pojol xa ox li av ak'el ta ilel li'e", "commands.scoreboard.objectives.display.alreadySet": "<PERSON>'yuk jelvan: yakal xa chak' ta ilel li objetivo li'e li avile li'e", "commands.scoreboard.objectives.display.cleared": "La stup' li objetivoetike te oyik ox ta av ak'el ta ilel %s", "commands.scoreboard.objectives.display.set": "Li avile %s chak' ta ilel li objetivoe %s", "commands.scoreboard.objectives.list.empty": "<PERSON>'abal objet<PERSON><PERSON>k", "commands.scoreboard.objectives.list.success": "Oy %s objetivo: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "La stub li yach'ubel ta stuke sbi objetivoe %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "La stsoy li yach'ubel ta stuke sbi objetivoe %s", "commands.scoreboard.objectives.modify.displayname": "La sjel li sbi %se ta %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "La stup' li spormato atolal objetivoe %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "La sjel li spormato atolal objetivoe %s", "commands.scoreboard.objectives.modify.rendertype": "La sjel li tos slekubtasel yimajen objetivoe %s", "commands.scoreboard.objectives.remove.success": "La stup' objetivo %s", "commands.scoreboard.players.add.success.multiple": "La skapbik %s punto objetivo %s sventa %s entiraletik", "commands.scoreboard.players.add.success.single": "La skapbe %s punto objetivo %s sventa %s (oy xa yu'un %s punto)", "commands.scoreboard.players.display.name.clear.success.multiple": "La stup' li sbi %s entiraletik xak' ta ilel ta %s", "commands.scoreboard.players.display.name.clear.success.single": "La stup' li sbi %se xak' ta ilel ta %s", "commands.scoreboard.players.display.name.set.success.multiple": "La sjel ta %s li sbi %s entiraletike xak' ta ilel ta %s", "commands.scoreboard.players.display.name.set.success.single": "La sjel ta %s li sbi %se xak' ta ilel ta %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "La stup'bik pormato atolal %s entiraletik ta %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "La stup'be pormato atolal %s ta %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "La sjelbik pormato atolal %s entiraletik ta %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "La sjelbe pormato atolal %s ta %s", "commands.scoreboard.players.enable.failed": "<PERSON><PERSON><PERSON><PERSON> jelvan: tsakal xa ox li j<PERSON>e", "commands.scoreboard.players.enable.invalid": "Ja' no'ox ch-abtej xchi'uk objetivoetik ta tos ''jlikesvanej'' li tunele ''enable''", "commands.scoreboard.players.enable.success.multiple": "La stsoybik jlikesvanej %s %s entiraletik", "commands.scoreboard.players.enable.success.single": "La stsoybe jlikesvanej %s %s", "commands.scoreboard.players.get.null": "Mu xu' sta li yatol objetivoe %s sventa %s, ch'abal tik'el ta vun", "commands.scoreboard.players.get.success": "Li %se oy yatol ta %s sventa objetivo %s", "commands.scoreboard.players.list.empty": "Ch'abal tik'il ta vun entiraletik", "commands.scoreboard.players.list.entity.empty": "Li %se ch'abal yatoltak sventa chak' ta ilel", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "Li %se oy %s yatol:", "commands.scoreboard.players.list.success": "Oy %s tik'il ta vun entiral(etik): %s", "commands.scoreboard.players.operation.success.multiple": "La yach'ubtasbik yatol objetivo %s %s entiraletik", "commands.scoreboard.players.operation.success.single": "La sjel yatol objetivo %s sventa %s ta %s", "commands.scoreboard.players.remove.success.multiple": "La stup'be %s punto objetivo %s sventa %s entiraletik", "commands.scoreboard.players.remove.success.single": "La stup'be %s punto objetivo %s sventa %s (oy xa yu'un %s)", "commands.scoreboard.players.reset.all.multiple": "Cha'likesatik skotol li yatoltakik %s entiraletike", "commands.scoreboard.players.reset.all.single": "Cha'likesatik skotol li yatoltak %se", "commands.scoreboard.players.reset.specific.multiple": "La xcha'likesbik yatol objetivo %s %s entiraletik", "commands.scoreboard.players.reset.specific.single": "La xcha'likesbe yatol objetivo %s %s", "commands.scoreboard.players.set.success.multiple": "La sjel yatol objetivo %s sventa %s entiraletik ta %s", "commands.scoreboard.players.set.success.single": "La sjel yatol objetivo %s yu'un %s ta %s", "commands.seed.success": "Bek'il: %s", "commands.setblock.failed": "<PERSON> xu' xak' li kuboe", "commands.setblock.success": "La sjel li kuboe ta %s, %s, %s", "commands.setidletimeout.success": "<PERSON><PERSON> ta %s minuto li sts'ak sk'ak'alil tiniele", "commands.setidletimeout.success.disabled": "Tubbil xa li sts'ak sk'ak'alil tiniele", "commands.setworldspawn.failure.not_overworld": "<PERSON>' no'ox te oy ta sba balumil li cha'vina<PERSON><PERSON> balumile", "commands.setworldspawn.success": "La yak' ta %s, %s, %s [%s] li cha'vinajebal balumile", "commands.spawnpoint.success.multiple": "La yak' ta %s, %s, %s [%s] ta yut rimension %s li xcha'vinajebik %s jtajimoletike", "commands.spawnpoint.success.single": "La yak' ta %s, %s, %s [%s] ta yut rimension %s li xcha'vinajeb %se", "commands.spectate.not_spectator": "Tana mu ja' jun jk'elvanej li %se", "commands.spectate.self": "Mu xu' xak'el aba", "commands.spectate.success.started": "Tana tsk'el %s li j<PERSON>jimole", "commands.spectate.success.stopped": "Paj ta sk'elel jun entiral li j<PERSON>ji<PERSON>le", "commands.spreadplayers.failed.entities": "Mu xu' xpuk %s entiral(etik) ta nopol %s, %s (epik tajmek entiraletik sventa li avil le'e; k'elo mi xu' xapuk ta %s no'oxe)", "commands.spreadplayers.failed.invalid.height": "Chopol maxHeight %s, sk'an jun mas muk'ta tojolilal ke li minimoe %s yu'un balumil", "commands.spreadplayers.failed.teams": "Mu xu' xpuk %s ekipo ta nopol %s, %s (epik tajmek entiraletik sventa li avil le'e; k'elo mi xu' xapuk ta %s no'oxe)", "commands.spreadplayers.success.entities": "La xpuk %s jtajimol(etik) ta nopol %s, %s xchi'uk %s kubo snamal ta o'lol", "commands.spreadplayers.success.teams": "La xpuk %s ekipo ta nopol %s, %s xchi'uk %s kubo snamal ta o'lol", "commands.stop.stopping": "<PERSON><PERSON> tspajes li servilore", "commands.stopsound.success.source.any": "Pajesatik skotol li nuk'ilaletike ''%s''", "commands.stopsound.success.source.sound": "Pajesat li snuk'ilal ''%s'' avile ''%s''", "commands.stopsound.success.sourceless.any": "Pajesatik skotol li nuk'ilaletike", "commands.stopsound.success.sourceless.sound": "Pajesat li nuk'ilale ''%s''", "commands.summon.failed": "Mu xu' s<PERSON><PERSON>s li entirale", "commands.summon.failed.uuid": "Mu xu' s<PERSON>jes li entirale yu'un chib UUID ko'olik", "commands.summon.invalidPosition": "Chopol posision sventa svinajes", "commands.summon.success": "La svinajes entiral %s", "commands.tag.add.failed": "Chu'unin xa ox li etiketae o ep chu'unin li objetivoe", "commands.tag.add.success.multiple": "La skapbik etiketa ''%s'' %s entiraletik", "commands.tag.add.success.single": "La skapbe etiketa ''%s'' %s", "commands.tag.list.multiple.empty": "Ch'abal etiketaetik li ta %s entiraletike", "commands.tag.list.multiple.success": "Li %s entiraletike oy %s yetiketatakik: %s", "commands.tag.list.single.empty": "Ch'abal <PERSON> li %se", "commands.tag.list.single.success": "Li %se oy %s yetiketatak: %s", "commands.tag.remove.failed": "Mu xu'unin li etiketa li'e li objetivoe", "commands.tag.remove.success.multiple": "La stup'bik etiketa ''%s'' %s entiraletik", "commands.tag.remove.success.single": "La stup'be etiketa ''%s'' %s", "commands.team.add.duplicate": "Oy xa jun ekipo xchi'uk li biil li'e", "commands.team.add.success": "La spas ekipo %s", "commands.team.empty.success": "La slok'esbe %s krixchano(etik) ekipo %s", "commands.team.empty.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: pojol xa ox li ekipoe", "commands.team.join.success.multiple": "La skapbe %s krixchanoetik ekipo %s", "commands.team.join.success.single": "La skapbe %s ekipo %s", "commands.team.leave.success.multiple": "La slok'es %s krixchanoetik ta yekipotakik", "commands.team.leave.success.single": "La slok'es %s ta yekipo", "commands.team.list.members.empty": "Ch'abal krixchanoetik ta ekipo %s", "commands.team.list.members.success": "Ta ekipo %s oy %s krixchano(etik): %s", "commands.team.list.teams.empty": "<PERSON><PERSON><PERSON><PERSON>", "commands.team.list.teams.success": "Oy %s ekipo: %s", "commands.team.option.collisionRule.success": "Li ley majbaile sventa ekipo %s jelat ta '''%s''", "commands.team.option.collisionRule.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: oy xa ox li stojolilal li'e li ley majbaile", "commands.team.option.color.success": "La sjel sbon ekipo %s ta %s", "commands.team.option.color.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: oy xa ox li sbon li'e li ekipoe", "commands.team.option.deathMessageVisibility.success": "<PERSON> vinajelal mantal lajelale sventa ekipo %s jelat ta ''%s''", "commands.team.option.deathMessageVisibility.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: oy xa ox li stojolilal li'e li vinajelal mantal lajelale", "commands.team.option.friendlyfire.alreadyDisabled": "<PERSON><PERSON><PERSON><PERSON> jelvan: tubbil xa ox ta ekipo li lekil k'ok'e", "commands.team.option.friendlyfire.alreadyEnabled": "<PERSON>'<PERSON>uk jelvan: tsakal xa ox ta ekipo li lekil k'ok'e", "commands.team.option.friendlyfire.disabled": "La stubbe lekil k'ok' ekipo %s", "commands.team.option.friendlyfire.enabled": "La stsoybe lekil k'ok' ekipo %s", "commands.team.option.name.success": "La sjel li sbi ekipoe %s", "commands.team.option.name.unchanged": "<PERSON>'<PERSON>uk jelvan: oy xa ox li sbi le'e li ekipoe", "commands.team.option.nametagVisibility.success": "Li vinajelal biile sventa li yajtajimoltak ekipoe %s jelvan ta ''%s''", "commands.team.option.nametagVisibility.unchanged": "<PERSON><PERSON><PERSON><PERSON> jelvan: oy xa ox li stojolilal li'e li vinajelal biile", "commands.team.option.prefix.success": "<PERSON><PERSON><PERSON> ta %s li slikeb sts'ak k'op yu'un ekipoe", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Mu'yuk jelvan: mu xu' xa ox xilik xchi'iltak mu xvinajik ta k'elel li ekipo le'e", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Mu'yuk jelvan: xu' xa ox xilik xchi'iltak mu xvinajik ta k'elel li ekipo le'e", "commands.team.option.seeFriendlyInvisibles.disabled": "Mu xa xu' xil chi'ililetik mu xvinajik ta k'elel li ekipoe %s", "commands.team.option.seeFriendlyInvisibles.enabled": "Xu' xa xil chi'ililetik mu xvinajik ta k'elel li ekipoe %s", "commands.team.option.suffix.success": "<PERSON><PERSON><PERSON> ta <PERSON>s li slajeb sts'ak k'op yu'un ekipoe", "commands.team.remove.success": "La stup' li ekipoe %s", "commands.teammsg.failed.noteam": "Sventa xu' xatakbeik mantaletik li avekipoe, sk'an te oyot ta jun", "commands.teleport.invalidPosition": "Chopol posision sventa xatak aba", "commands.teleport.success.entity.multiple": "La stak %s entiraletik ta %s", "commands.teleport.success.entity.single": "La stak %s ta %s", "commands.teleport.success.location.multiple": "La stak %s entiraletik ta %s, %s, %s", "commands.teleport.success.location.single": "La stak %s ta %s, %s, %s", "commands.test.batch.starting": "Yakal tslikes li entornoe %s yu'un tsobol %s", "commands.test.clear.error.no_tests": "Mu xu' la sta prevaetik sventa chtup'atik", "commands.test.clear.success": "La stup' %s esturuktura", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Paso klik sventa xalok'ta", "commands.test.create.success": "La spasbe sts'ikel li prevae %s", "commands.test.error.no_test_containing_pos": "<PERSON> xu' sta jun yilobil preva chu'unin %s, %s, %s", "commands.test.error.no_test_instances": "Mu la sta yilobil prevaetik", "commands.test.error.non_existant_test": "Mu xu' la sta li prevae %s", "commands.test.error.structure_not_found": "Mu xu' la sta li estruktura prevae %s", "commands.test.error.test_instance_not_found": "Mu xu' la sta li entiral kubo yilobil prevae", "commands.test.error.test_instance_not_found.position": "Mu xu' la sta li entiral yilobil prevae ta %s, %s, %s", "commands.test.error.too_large": "Sk'an ch-elan mas bik'it ti %s kubo ta jujun eje li smuk'ul esturukturae", "commands.test.locate.done": "Laj li sa'ele, la sta %s estruktura", "commands.test.locate.found": "La sta jun estruktura ta: %s (snamal: %s)", "commands.test.locate.started": "Lik ta sa'el estruktura prevaetik, li'e chjalij nan j<PERSON>luk...", "commands.test.no_tests": "<PERSON><PERSON><PERSON><PERSON> prevaetik sventa ch-abtelanatik", "commands.test.relative_position": "Parsial posision ta %s: %s", "commands.test.reset.error.no_tests": "<PERSON> staoj prevaetik sventa chcha'likesatik", "commands.test.reset.success": "La xcha'likes %s esturuktura", "commands.test.run.no_tests": "Mu la sta prevaetik", "commands.test.run.running": "Yakal chabtelan %s preva...", "commands.test.summary": "¡Ts'akal xa li preva tajimole! La yabtelan %s preva", "commands.test.summary.all_required_passed": "Lek abtejik skotol li k'anbil prevaetike :)", "commands.test.summary.failed": "Chopolaj %s k'anbil preva :(", "commands.test.summary.optional_failed": "Chopolaj %s st'<PERSON><PERSON><PERSON> preva", "commands.tick.query.percentiles": "Persentiletik: P50: %sms P95: %sms P99: %sms; k'u sba stalel: %s", "commands.tick.query.rate.running": "K'anbil tasa siklo: %s ta sekunto. Sk'ak'alil: %sms (objetivo: %sms)", "commands.tick.query.rate.sprinting": "K'anbil tasa siklo: %s ta sekunto (mu na'bil, ja' no'ox sventa k'u sba stalel).\nSk'ak'alil ta siklo: %sms", "commands.tick.rate.success": "Kom ta %s ta sekunto li k'anbil tasa sikloe", "commands.tick.sprint.report": "Sujel xchi'uk %s siklo ta sekunto, o %s ms ta siklo", "commands.tick.sprint.stop.fail": "<PERSON><PERSON> ch'abal sujel siklo", "commands.tick.sprint.stop.success": "La spajes li sujel sikloe", "commands.tick.status.frozen": "<PERSON><PERSON><PERSON> li taji<PERSON>", "commands.tick.status.lagging": "<PERSON><PERSON> ch-abtej li <PERSON>, pe mu xu' sma<PERSON>'lin li k'anbil tasa sikloe", "commands.tick.status.running": "Lek yakal ch-abtej li taji<PERSON>le", "commands.tick.status.sprinting": "<PERSON>kal suj li tajimole", "commands.tick.step.fail": "<PERSON> xu' s<PERSON>chan li ta<PERSON>; ba'yel sk'an xbotib", "commands.tick.step.stop.fail": "<PERSON><PERSON> ch'abal sujel siklo", "commands.tick.step.stop.success": "Pajesat li sikloe(tik) ta bats'i anile", "commands.tick.step.success": "Yakal tst'ot'etajes %s siklo", "commands.time.query": "Ora: %s", "commands.time.set": "La stsin li sk'ak'alile ta %s", "commands.title.cleared.multiple": "La stup'bik sbitakik %s jtajimoletik", "commands.title.cleared.single": "La stup'be sbitak %s", "commands.title.reset.multiple": "La xcha'likesbik t'ujel biiletik %s jtajimoletik", "commands.title.reset.single": "La xcha'likesbe t'ujel biiletik %s", "commands.title.show.actionbar.multiple": "Yakal chak' ta ilel ta sbara paselik %s jtajimoletik li ach' biilale", "commands.title.show.actionbar.single": "Yakal chak' ta ilel ta sbara pasel %s li ach' biilale", "commands.title.show.subtitle.multiple": "Yakal chak'bik ta ilel ach' bik'it xot %s jtajimoletik", "commands.title.show.subtitle.single": "Yakal chak'be ta ilel ach' bik'it xot %s", "commands.title.show.title.multiple": "Yakal chak'bik ta ilel ach' biilal %s jtajimoletik", "commands.title.show.title.single": "Yakal chak'be ta ilel ach' biilal %s", "commands.title.times.multiple": "La sjel li sk'ak'alil sk'elel sbiik %s jtajimoletike", "commands.title.times.single": "La sjel li sk'ak'alil sk'elel sbi %se", "commands.transfer.error.no_players": "Sk'an xats'iba jun jtaji<PERSON>l sventa xavuy", "commands.transfer.success.multiple": "Yakal tsvuy %s jtajimoletik ta %s:%s", "commands.transfer.success.single": "Yakal tsvuy %s ta %s:%s", "commands.trigger.add.success": "La stsan %s (la skapbe %s tojolilal)", "commands.trigger.failed.invalid": "Xu' no'ox xatsan objetivoetik xchi'uk tos ''jlikesvanej''", "commands.trigger.failed.unprimed": "Mu xu' to chatsan li objetivo jlikesvanej li'e", "commands.trigger.set.success": "La stsan %s (la skomes ta %s)", "commands.trigger.simple.success": "La stsan %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "Mu'yuk restinoetik ta %s", "commands.waypoint.list.success": "%s restino ta %s: %s", "commands.waypoint.modify.color": "Tana ja' %s li sbon restinoe", "commands.waypoint.modify.color.reset": "Ch<PERSON>'pas li sbon restinoe", "commands.waypoint.modify.style": "<PERSON><PERSON> li yestilo restinoe", "commands.weather.set.clear": "Yakal tsk'epes li osile", "commands.weather.set.rain": "Yakal chlik ta yak'el vo'", "commands.weather.set.thunder": "Yakal chlik li ik'al-o'e", "commands.whitelist.add.failed": "Te oy xa ox ta sakil lista li jtajimole", "commands.whitelist.add.success": "La skapbe %s sakil lista", "commands.whitelist.alreadyOff": "Tubbil xa ox li sakil listae", "commands.whitelist.alreadyOn": "Tsakal xa ox li sakil listae", "commands.whitelist.disabled": "<PERSON>a tubbil li sakil listae", "commands.whitelist.enabled": "<PERSON>a tsakal li sakil listae", "commands.whitelist.list": "Oy %s jtajimol(etik) ta sakil lista: %s", "commands.whitelist.none": "Ch'abal jtajimoletik ta sakil lista", "commands.whitelist.reloaded": "But'at li sakil listae", "commands.whitelist.remove.failed": "Ch'abal ox ta sakil lista li jtajimole", "commands.whitelist.remove.success": "La slok'es xa %s ta sakil lista", "commands.worldborder.center.failed": "<PERSON>'yuk jelvan: te oy xa ox li ta posision le'e li yut sts'ak balumile", "commands.worldborder.center.success": "La skomes ta %s, %s li yut sts'ak balumile", "commands.worldborder.damage.amount.failed": "<PERSON><PERSON><PERSON><PERSON> jelvan: tspas xa li yepalil yaintasel le'e li yut sts'ak balumile", "commands.worldborder.damage.amount.success": "La stsin ta %s kubo ta jujun sekunto li syaintasel sts'ak balumile", "commands.worldborder.damage.buffer.failed": "<PERSON>'<PERSON>uk jelvan: te oy xa ox li ta namal li'e li lekil avil ta pat balumile", "commands.worldborder.damage.buffer.success": "La skomes ta %s kubo li lekil avil ta pat balumile", "commands.worldborder.get": "Oy %s skubo ta natil li sts'ak balumile", "commands.worldborder.set.failed.big": "Mu xu' oy mas ti %s skubo ta sjamalil li ts'ak balumile", "commands.worldborder.set.failed.far": "Mu xu' oy mas ti %s skubo ta sjamalil li ts'ak balumile", "commands.worldborder.set.failed.nochange": "<PERSON><PERSON><PERSON><PERSON> jelvan: oy xa ox li smuk'ul li'e li ts'ak balumile", "commands.worldborder.set.failed.small": "Mu xu' oy menos ti 1 skubo ta sjamalil li sts'ak balumile", "commands.worldborder.set.grow": "Yakal tsp'oles ta %s kubo ta sjamalil k'alal %s sekunto li sts'ak balumile", "commands.worldborder.set.immediate": "La skomes ta %s kubo ta sjamalil li sts'ak balumile", "commands.worldborder.set.shrink": "Yakal tsbik'tajes ta %s kubo ta sjamalil k'alal %s sekunto li sts'ak balumile", "commands.worldborder.warning.distance.failed": "<PERSON>'<PERSON>uk jelvan: te oy xa ox li ta namal li'e li yalel sts'ak balumile", "commands.worldborder.warning.distance.success": "La skomes ta %s kubo li yalel sts'ak balumile", "commands.worldborder.warning.time.failed": "<PERSON><PERSON><PERSON><PERSON> jelvan: chja<PERSON>j xa ox li yepal sk'ak'alil li'e li yalel sts'ak balumile", "commands.worldborder.warning.time.success": "La skomes ta %s sekunto li yalel sts'ak balumile", "compliance.playtime.greaterThan24Hours": "Latajin k'alal mas ke 24 ora", "compliance.playtime.hours": "Latajin k'alal %s ora", "compliance.playtime.message": "Mi ep x<PERSON><PERSON>e, xu' xas<PERSON> li a<PERSON>le", "connect.aborted": "<PERSON><PERSON><PERSON><PERSON>", "connect.authorizing": "Yakal tslikes li sesione...", "connect.connecting": "Yakal tsts'ak sba ta servilor...", "connect.encrypting": "Yakal tsjelubtas...", "connect.failed": "Mu xu' sts'ak sba ta servilor", "connect.failed.transfer": "<PERSON><PERSON><PERSON> li ts'akele k'alal chbak' ox ta servilor", "connect.joining": "Yakal ch-och ta balumil...", "connect.negotiating": "Yakal tsts'ak sba...", "connect.reconfiging": "Yakal chcha'tsin...", "connect.reconfiguring": "Yakal chcha'tsin...", "connect.transferring": "Yakal tsvuy sba ta ach' servilor...", "container.barrel": "<PERSON><PERSON><PERSON>", "container.beacon": "J<PERSON>", "container.beehive.bees": "Xchanul pometik: %s/%s", "container.beehive.honey": "Ajapom: %s/%s", "container.blast_furnace": "<PERSON><PERSON>'ta jorno", "container.brewing": "<PERSON><PERSON><PERSON>", "container.cartography_table": "Mexa lok'ta mapa", "container.chest": "<PERSON><PERSON>", "container.chestDouble": "Muk'ta kaxa", "container.crafter": "Meltsanobil", "container.crafting": "Mel<PERSON><PERSON><PERSON>", "container.creative": "T'uj-item", "container.dispenser": "Pukobil", "container.dropper": "Tenobil", "container.enchant": "Tskap", "container.enchant.clue": "¿%s . . . ?", "container.enchant.lapis.many": "%s lapislasuli", "container.enchant.lapis.one": "1 lapislasuli", "container.enchant.level.many": "%s nivel kapel", "container.enchant.level.one": "1 nivel kapel", "container.enchant.level.requirement": "<PERSON><PERSON><PERSON><PERSON> nivel: %s", "container.enderchest": "<PERSON><PERSON> kaxa", "container.furnace": "Jorno", "container.grindstone_title": "Tsmeltsan xchi'uk chch'ak", "container.hopper": "Malobil", "container.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container.isLocked": "¡Makal li %se!", "container.lectern": "Atiril", "container.loom": "Abtejebal pok'", "container.repair": "Tsmeltsan xchi'uk tsbiin", "container.repair.cost": "Stojol: %1$s", "container.repair.expensive": "¡Toj toyol!", "container.shulkerBox": "<PERSON><PERSON><PERSON> s<PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "xchi'uk %s mas...", "container.shulkerBox.unknownContents": "¿¿¿???", "container.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container.spectatorCantOpen": "<PERSON> xu' xjam: mu xvinajik to li k'usitike.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Tslekubtas ekipamiento", "container.upgrade.error_tooltip": "Mu xu' xlekub xi li item li'e", "container.upgrade.missing_template_tooltip": "Ak'o jun plantiya tak'inal", "controls.keybinds": "Cha<PERSON>el tekla...", "controls.keybinds.duplicateKeybinds": "Li tekla li'e xtunesat ek sventa: %s", "controls.keybinds.title": "Cha<PERSON>el tekla", "controls.reset": "<PERSON><PERSON>'likes", "controls.resetAll": "<PERSON><PERSON>'likes te<PERSON><PERSON><PERSON>", "controls.title": "<PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "T'ujo li yosil balumile", "createWorld.customize.buffet.title": "<PERSON><PERSON><PERSON>b<PERSON><PERSON><PERSON>", "createWorld.customize.flat.height": "<PERSON><PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Okil - %s", "createWorld.customize.flat.layer.top": "Bail - %s", "createWorld.customize.flat.removeLayer": "Tst<PERSON>' k'olol", "createWorld.customize.flat.tile": "Smaterial k'olol", "createWorld.customize.flat.title": "<PERSON><PERSON><PERSON><PERSON> ch'ulul balumil", "createWorld.customize.presets": "Plantiyaetik", "createWorld.customize.presets.list": "O mi chak'ane, ¡la jpaskutik xa jayibuk!", "createWorld.customize.presets.select": "Tstunes plantiya", "createWorld.customize.presets.share": "¿Mi chak'an chapuk li aplantiyae? ¡Tuneso li kaxae ta olon!", "createWorld.customize.presets.title": "Tst'uj jun plantiya", "createWorld.preparing": "<PERSON><PERSON> tsm<PERSON>an li s<PERSON> balumi<PERSON>...", "createWorld.tab.game.title": "Tajimol", "createWorld.tab.more.title": "<PERSON><PERSON>", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "Atiribusionetik", "credits_and_attribution.button.credits": "Kereritoetik", "credits_and_attribution.button.licenses": "Lisensiaetik", "credits_and_attribution.screen.title": "Kereritoetik xchi'uk atiribusionetik", "dataPack.bundle.description": "Tstsan li morale jech k'ucha'al jun k'anelal item", "dataPack.bundle.name": "Moraletik", "dataPack.locator_bar.description": "Chak' ta ilel bu te oyik li yan jtajimoletike ta tajinebal", "dataPack.locator_bar.name": "<PERSON><PERSON> nupel", "dataPack.minecart_improvements.description": "Lekubtasbil bak'el sventa vakonetik", "dataPack.minecart_improvements.name": "Slekubtaselik vakonetik", "dataPack.redstone_experiments.description": "Jeleletik ta redstone ta preva", "dataPack.redstone_experiments.name": "Prevaetik ta redstone", "dataPack.title": "Tst'uj li pakete alelaletike", "dataPack.trade_rebalance.description": "Yach'ubtaselik chonolajeletik xchi'uk jbik'it lumetik", "dataPack.trade_rebalance.name": "Stsineltak chonolajel xchi'uk jbik'it lum", "dataPack.update_1_20.description": "Ach' elaniletik xchi'uk k'usitik sventa Minecraft 1.20", "dataPack.update_1_20.name": "<PERSON><PERSON><PERSON><PERSON>bt<PERSON><PERSON> 1.20", "dataPack.update_1_21.description": "Ach' elaniletik xchi'uk k'usitik sventa Minecraft 1.21", "dataPack.update_1_21.name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 1.21", "dataPack.validation.back": "Ta sut", "dataPack.validation.failed": "¡Chopolaj k'alal tsk'elan ox li pakete alelaletike!", "dataPack.validation.reset": "Ta sutes pakete ta bats'i", "dataPack.validation.working": "Yakal tsk'elan li t'ujbil pakete alelaletike...", "dataPack.vanilla.description": "<PERSON> s<PERSON>i <PERSON><PERSON>", "dataPack.vanilla.name": "Bats'i", "dataPack.winter_drop.description": "Ach' elaniletik xchi'uk k'usitik sventa li bik'it ach'ubtasele ta yorail k'epelal", "dataPack.winter_drop.name": "Bik'it ach'ubtasel ta yorail k'epelal", "datapackFailure.safeMode": "Mu xi'el manera", "datapackFailure.safeMode.failed.description": "Oy chopol o sokbil alelaletik li ta balumil li'e.", "datapackFailure.safeMode.failed.title": "Cho<PERSON><PERSON> k'alal tsvinajes ox li balumile ta mu xi'el manera.", "datapackFailure.title": "Mu xu' s<PERSON><PERSON>s li balumile yu'un oy chopolaletik ta t'ujbil pakete alelaletik. Xu' xak'an chavinajes ja' no'ox xchi'uk li bats'i pakete alelale (''mu xi'el manera'') o chasut ta sbi tajimol xchi'uk chasa' jun chapk'op atuk.", "death.attack.anvil": "Net'at yu'un jun yunke li %1$se", "death.attack.anvil.player": "La snet' %1$s li jbej yunkee k'alal spas ox k'op xchi'uk %2$s", "death.attack.arrow": "T'omesat li %1$se yu'un %2$s", "death.attack.arrow.item": "T'omesat li %1$se yu'un %2$s xchi'uk %3$s", "death.attack.badRespawnPoint.link": "Sk'anbil lok'tael tajimol", "death.attack.badRespawnPoint.message": "<PERSON>lat li %1$se yu'un %2$s", "death.attack.cactus": "La spech' ch'ix ja'to mil li %1$se", "death.attack.cactus.player": "Xanav ta nopol jtek' petok li %1$se k'alal sk'an ox chkol yu'un %2$s", "death.attack.cramming": "Ep net'at li %1$s", "death.attack.cramming.player": "Net'at yu'un %2$s li %1$se", "death.attack.dragonBreath": "La svo %1$s li yik' muk'ta aine", "death.attack.dragonBreath.player": "La svo %1$s li yik' muk'ta aine yu'un %2$s", "death.attack.drown": "Jik'av li %1$se", "death.attack.drown.player": "Jik'av li %1$se k'alal sk'an ox chkol yu'un %2$s", "death.attack.dryout": "Laj li %1$se yu'un taki li yo'ontone", "death.attack.dryout.player": "Laj li %1$se yu'un taki li yo'ontone k'alal sk'an ox chkol yu'un %2$s", "death.attack.even_more_magic": "\nLaj li %1$se yu'un jun muk'ta majia", "death.attack.explosion": "T'om li %1$se", "death.attack.explosion.player": "T'omesat li %1$se yu'un %2$s", "death.attack.explosion.player.item": "La st'omes %1$s li %2$se xchi'uk %3$s", "death.attack.fall": "Tsots lom li %1$se", "death.attack.fall.player": "Tsots lom li %1$se k'alal sk'an ox chkol yu'un %2$s", "death.attack.fallingBlock": "Net'at li %1$se yu'un jbej kubo", "death.attack.fallingBlock.player": "Net'at li %1$se yu'un jbej kubo k'alal spas ox k'op xchi'uk %2$s", "death.attack.fallingStalactite": "Ch'ojat yu'un lom jtel chu'ch'en li %1$se", "death.attack.fallingStalactite.player": "Ch'ojat li %1$se yu'un lom jtel chu'ch'en k'alal tspas ox k'op xchi'uk %2$s", "death.attack.fireball": "Chik'at yu'un sbola k'ok' %2$s li %1$se", "death.attack.fireball.item": "Chik'at li %1$se yu'un sbola k'ok' %2$s xchi'uk %3$s", "death.attack.fireworks": "T'om xchi'uk yolonk'ok'etik li %1$se", "death.attack.fireworks.item": "T'om li %1$s yu'un jtel yolonk'ok' t'omesbil ta %3$s yu'un %2$s", "death.attack.fireworks.player": "T'om xchi'uk yolonk'ok'etik li %1$se k'alal spas ox k'op xchi'uk %2$s", "death.attack.flyIntoWall": "La sta ju'elal bak'el li %1$se", "death.attack.flyIntoWall.player": "La sta li ju'elal bak'ele li %1$se k'alal sk'an ox chjatav yu'un %2$s", "death.attack.freeze": "Botib xchi'uk laj li %1$se", "death.attack.freeze.player": "Botib xchi'uk laj yu'un %2$s li %1$se", "death.attack.generic": "Laj li %1$se", "death.attack.generic.player": "Laj yu'un %2$s li %1$se", "death.attack.genericKill": "Laj li %1$se", "death.attack.genericKill.player": "Laj li %1$se k'alal spas ox k'op xchi'uk %2$s", "death.attack.hotFloor": "La sta ja' tsuk' li balumile li %1$se", "death.attack.hotFloor.player": "La sta ja' tsuk' li balumile li %1$se yu'un %2$s", "death.attack.inFire": "K'ak' li %1$se", "death.attack.inFire.player": "K'ak' li %1$se k'alal spas ox k'op xchi'uk %2$s", "death.attack.inWall": "Kup ik'aj ta jbej kubo li %1$se", "death.attack.inWall.player": "Kup ik'aj ta jbej kubo li %1$se k'alal spas ox k'op xchi'uk %2$s", "death.attack.indirectMagic": "La smil %1$s li %2$se xchi'uk majia", "death.attack.indirectMagic.item": "La smil %1$s li %2$se xchi'uk %3$s", "death.attack.lava": "La sk'an chnux ta tsuk' li %1$se", "death.attack.lava.player": "La sk'an chnux ta tsuk' li %1$se sventa chjatav yu'un %2$s", "death.attack.lightningBolt": "Majat yu'un jun anjel li %1$se", "death.attack.lightningBolt.player": "Majat yu'un jun anjel li %1$se k'alal spas ox k'op xchi'uk %2$s", "death.attack.mace_smash": "Net'at li %1$se yu'un %2$s", "death.attack.mace_smash.item": "Net'at yu'un %2$s xchi'uk %3$s li %1$se", "death.attack.magic": "Laj yu'un majia li %1$se", "death.attack.magic.player": "Laj yu'un majia li %1$se k'alal sk'an ox chjatav yu'un %2$s", "death.attack.message_too_long": "Nat ox tajmek li mantale sventa ts'akal chvinaj. ¡Ak'bunkutik perton! Li' te oy li ko'ole, pe mas bik'it: %s", "death.attack.mob": "<PERSON>lat li %1$se yu'un %2$s", "death.attack.mob.item": "Milat li %1$se yu'un %2$s xchi'uk %3$s", "death.attack.onFire": "K'ak' ja'to mil li %1$s", "death.attack.onFire.item": "K'ak' ja'to k'ataj ta tan li %1$se k'alal spas ox k'op xchi'uk %2$s xchi'uk %3$s", "death.attack.onFire.player": "K'ak' ja'to k'ataj ta tan li %1$se k'alal spas ox k'op xchi'uk %2$s", "death.attack.outOfWorld": "Lom ta pat balumil li %1$se", "death.attack.outOfWorld.player": "Mu sk'an ox chkuxi ta balumil xchi'uk %2$s li %1$s", "death.attack.outsideBorder": "Lok' li ta balumil li'e li %1$se", "death.attack.outsideBorder.player": "Lok' li ta balumil li'e li %1$s k'alal spas ox k'op xchi'uk %2$s", "death.attack.player": "<PERSON>lat li %1$se yu'un %2$s", "death.attack.player.item": "Milat li %1$se yu'un %2$s xchi'uk %3$s", "death.attack.sonic_boom": "Laj yu'un jun tsatsal ok'el li %1$se", "death.attack.sonic_boom.item": "Laj yu'un jun tsatsal ok'el li %1$se k'alal sk'an ox chjatav yu'un %2$s xchi'uk %3$s", "death.attack.sonic_boom.player": "Laj yu'un jun tsatsal ok'el li %1$se k'alal sk'an ox chjatav yu'un %2$s", "death.attack.stalagmite": "Lom ta jtel chu'ch'en li %1$s", "death.attack.stalagmite.player": "Lom ta jtel chu'ch'en li %1$s k'alal spas ox k'op xchi'uk %2$s", "death.attack.starve": "Laj yu'un vi'nal li %1$se", "death.attack.starve.player": "Laj yu'un vi'nal li %1$se k'alal spas ox k'op xchi'uk %2$s", "death.attack.sting": "Ch'ojat ja'to mil li %1$se", "death.attack.sting.item": "Ch'ojat ja'to mil li %1$se yu'un %2$s xchi'uk %3$s", "death.attack.sting.player": "Ch'ojat ja'to mil li %1$se yu'un %2$s", "death.attack.sweetBerryBush": "Ch'ojat yu'un jpets te'elbaya ja'to mil li %1$se", "death.attack.sweetBerryBush.player": "Ch'ojat yu'un jpets te'elbaya ja'to mil li %1$se k'alal sk'an ox chjatav yu'un %2$s", "death.attack.thorns": "Laj li %1$se k'alal la sk'an smaj li %2$se", "death.attack.thorns.item": "Laj yu'un %3$s li %1$se k'alal la sk'an smaj li %2$se", "death.attack.thrown": "Majat yu'un %2$s li %1$se", "death.attack.thrown.item": "Majat yu'un %2$s xchi'uk %3$s li %1$se", "death.attack.trident": "Ch'ojat yu'un %2$s li %1$se", "death.attack.trident.item": "Ch'ojat yu'un %2$s xchi'uk %3$s li %1$se", "death.attack.wither": "K'a'ub li %1$se", "death.attack.wither.player": "K'a'ub li %1$se k'alal spas ox k'op xchi'uk %2$s", "death.attack.witherSkull": "T'omesat yu'un jbej sbakil sjol %2$s li %1$se", "death.attack.witherSkull.item": "T'omesat yu'un jbej sbakil sjol %2$s xchi'uk %3$s li %1$se", "death.fell.accident.generic": "Lom ta jun toyol avil li %1$se", "death.fell.accident.ladder": "Lom ta jtel tek'omte' li %1$se", "death.fell.accident.other_climbable": "Lom k'alal chluchanvan li %1$se", "death.fell.accident.scaffolding": "Lom ta jbej antamio li %1$se", "death.fell.accident.twisting_vines": "Lom ta jayibuk ts'otol ak'etik li %1$se", "death.fell.accident.vines": "Lom ta jayibuk ak'etik li %1$se", "death.fell.accident.weeping_vines": "Lom ta jayibuk j-ok'el ak'etik li %1$se", "death.fell.assist": "Xujat ta jun toyol avil yu'un %2$s li %1$se", "death.fell.assist.item": "Sujat ta lomel yu'un %2$s xchi'uk %3$s li %1$se", "death.fell.finish": "Lom ta toj toyol xchi'uk milat yu'un %2$s li %1$se", "death.fell.finish.item": "Lom ta toj toyol xchi'uk milat yu'un %2$s xchi'uk %3$s li %1$se", "death.fell.killer": "Sujat ta lomel ta toj toyol li %1$se", "deathScreen.quit.confirm": "¿Mi melel chak'an chalok'?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "Atol", "deathScreen.score.value": "Atol: %s", "deathScreen.spectate": "Tsk'el balumil", "deathScreen.title": "¡Chamemot!", "deathScreen.title.hardcore": "¡Laj xa li tajimole!", "deathScreen.titleScreen": "Spantaya sbi tajimol", "debug.advanced_tooltips.help": "F3 + H = Lekil alel k'usitik", "debug.advanced_tooltips.off": "Lekil al k'usitik: mu'yuk", "debug.advanced_tooltips.on": "Lekil al k'usitik: tana", "debug.chunk_boundaries.help": "F3 + G = Cha<PERSON>' ta ilel li ts'ak set'eletik", "debug.chunk_boundaries.off": "Ts'ak set'eletik: nak'al", "debug.chunk_boundaries.on": "Ts'ak set'eletik: ak'bil ta ilel", "debug.clear_chat.help": "F3 + D = Tskus lo'il", "debug.copy_location.help": "F3 + C = Tslok'ta li uvikasione jech k'ucha'al mantal /tp. Mak'lino sventa suj ta makel li tajimole", "debug.copy_location.message": "La slok'ta li uvikasione ta j-ich'vun", "debug.crash.message": "<PERSON><PERSON> chanet' F3 + <PERSON><PERSON> mu chapaje, mu xa ch-abtej li taji<PERSON>.", "debug.crash.warning": "Mu xa ch-abtej ta %s...", "debug.creative_spectator.error": "Mu cha-ak'at ta sjelel li manera tajimole", "debug.creative_spectator.help": "F3 + N = Tsjel li manera tajimole ta ba'i <-> j<PERSON>'<PERSON><PERSON><PERSON>", "debug.dump_dynamic_textures": "La sk'ej li bak'elal lok'taeletik ta %s", "debug.dump_dynamic_textures.help": "F3 + S = Tslok'ta bak'elal lok'tael", "debug.gamemodes.error": "Mu cha-ak'at ta sjelel li menu manera tajimole", "debug.gamemodes.help": "F3 + F4 = Tsjam menu manera tajimol", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s ta stuk'il", "debug.help.help": "F3 + Q = Chak' ta ilel li lista li'e", "debug.help.message": "Teklaetik:", "debug.inspect.client.block": "La slok'ta li yalelal kuboe ta j-ich'vun (te oy ox ta yut kiliente)", "debug.inspect.client.entity": "La slok'ta li yalelal entirale ta j-ich'vun (te oy ox ta yut kiliente)", "debug.inspect.help": "F3 + I = Tslok'ta li yalelal entiral o kuboe ta j-ich'vun", "debug.inspect.server.block": "La slok'ta li yalelal kuboe ta j-ich'vun (te oy ox ta yut servilor)", "debug.inspect.server.entity": "La slok'ta li yalelal entirale ta j-ich'vun (te oy ox ta yut servilor)", "debug.pause.help": "F3 + Esc = Tspajes li tajimole mu xchi'uk smenu tajimol (ja' no'ox mi xu'e)", "debug.pause_focus.help": "F3 + P = <PERSON><PERSON><PERSON><PERSON> ta<PERSON> k'alal sjel li ventanae", "debug.pause_focus.off": "<PERSON><PERSON><PERSON><PERSON> taji<PERSON> k'alal sjel li ventanae: mu'yuk", "debug.pause_focus.on": "<PERSON><PERSON><PERSON><PERSON> taji<PERSON> k'alal sjel li ventanae: tana", "debug.prefix": "[<PERSON><PERSON>]:", "debug.profiling.help": "F3 + L = Tslikes/tspajes li analisise", "debug.profiling.start": "Ta xjalij %s sekunto li analisis kusele. Tuneso F3 + L sventa chapajes ta ba'i", "debug.profiling.stop": "Laj li analisise. La sk'ej li k'usi lok'e ta %s", "debug.reload_chunks.help": "F3 + A = Tsbut' set'eletik", "debug.reload_chunks.message": "<PERSON><PERSON> tsbut' s<PERSON><PERSON> li set'eletike", "debug.reload_resourcepacks.help": "F3 + T = Tsbut' pakete rekursoetik", "debug.reload_resourcepacks.message": "La sbut' pakete rekursoetik", "debug.show_hitboxes.help": "F3 + B = Chak' ta ilel kaxa majeletik", "debug.show_hitboxes.off": "Kaxa majeletik: mu'yuk", "debug.show_hitboxes.on": "Kaxa majeletik: tana", "debug.version.header": "Yalelal version kiliente:", "debug.version.help": "F3 + V = yalelal version kiliente", "demo.day.1": "Ta xjalij jo'ob k'ak'al ta tajimol li preva li'e. ¡Paso skotol li k'usi xu'e!", "demo.day.2": "<PERSON><PERSON><PERSON><PERSON><PERSON>al chib", "demo.day.3": "<PERSON>'ak'al oxib", "demo.day.4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> chanib", "demo.day.5": "¡Ja' li ak'ak'alil sla<PERSON>be!", "demo.day.6": "Laj li svo'obal a<PERSON>'a<PERSON>'al<PERSON>. Tuneso %s sventa chak'ej jun slok'ol li k'usi la apase.", "demo.day.warning": "¡Ta xlaj xa li sk'ak'alile!", "demo.demoExpired": "¡Laj li prevae!", "demo.help.buy": "¡Tsman tana!", "demo.help.fullWrapped": "Ta xjalij 5 k'ak'al ta yut Minecraft li preva li'e (nopol 1 ora xchi'uk 40 minuto ta melel sk'ak'alil). ¡K'elano li porokresoetike sventa chasa' puntoetik! ¡Elovajan!", "demo.help.inventory": "Net'o %1$s sventa chajam li ak'ejebe", "demo.help.jump": "Net'o %1$s sventa chabit", "demo.help.later": "¡Ak'o aba ta tajimol!", "demo.help.movement": "Tuneso %1$s, %2$s, %3$s, %4$s sventa chabak' xchi'uk li ch'oe sventa chak'el", "demo.help.movementMouse": "Tuneso li ch'oe sventa chak'el ta ats'el", "demo.help.movementShort": "Net'o %1$s, %2$s, %3$s o %4$s sventa chabak'", "demo.help.title": "Preva Minecraft", "demo.remainingTime": "Sk'ak'alil ti sk'an toe: %s", "demo.reminder": "Laj li prevae. ¡Mano Minecraft sventa xayaket o likeso yan ach' balumil!", "difficulty.lock.question": "¿Mi chak'an chamak li stsatsalil balumile ta %1$s? <PERSON>k nopo, k'alal chat'uj ''Tana'', mu'yuk o xu' chajel.", "difficulty.lock.title": "Tsmak li <PERSON><PERSON><PERSON> bal<PERSON>", "disconnect.endOfStream": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>'akel", "disconnect.exceeded_packet_rate": "Lok'esat yu'un jelav ta ts'ak pakete", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "<PERSON> sna' k'anel estaro", "disconnect.loginFailedInfo": "<PERSON><PERSON><PERSON> k'alal tslikes ox li sesione: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Tubb<PERSON> li tajinebale. K'elano li xchapanel akuentae yu'un Microsoft, avokoluk.", "disconnect.loginFailedInfo.invalidSession": "Chopol li sesione (cha'<PERSON>o li tajimole xchi'uk li jlikesta<PERSON>)", "disconnect.loginFailedInfo.serversUnavailable": "Chopolaj k'alal tsts'ak ox sba ta servilor k'elaneletik. Cha'paso, avokoluk.", "disconnect.loginFailedInfo.userBanned": "Chalok'esat ta tajimol ta internet", "disconnect.lost": "Ch'ay li ts'akele", "disconnect.packetError": "Xch'ay-o'ontonal protokolo internet", "disconnect.spam": "Lok'esat yu'un la spas espam", "disconnect.timeout": "<PERSON>j li sk'ak'al<PERSON> ma<PERSON>", "disconnect.transfer": "Bak' ta yan servilor", "disconnect.unknownHost": "<PERSON> oj<PERSON><PERSON><PERSON> servilor", "download.pack.failed": "Chopolaj k'alal tsyales ox %s ta %s pakete", "download.pack.progress.bytes": "Porokreso: %s (mu ch-ojtikinat li sjunul muk'ulile)", "download.pack.progress.percent": "Porokreso: %s%%", "download.pack.title": "Yakal tsyales pakete rekurso %s/%s", "editGamerule.default": "Bats'i: %s", "editGamerule.title": "T<PERSON>jel li sleytak tajimole", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "Absorsion", "effect.minecraft.bad_omen": "Chopol alel", "effect.minecraft.blindness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.conduit_power": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.darkness": "<PERSON><PERSON>' <PERSON><PERSON><PERSON>", "effect.minecraft.dolphins_grace": "Sk'upilal telpin", "effect.minecraft.fire_resistance": "Ts'ikel ta k'ok'", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.haste": "<PERSON><PERSON><PERSON>", "effect.minecraft.health_boost": "Lekubt<PERSON><PERSON> k<PERSON>", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON>e yu'un bik'it lum", "effect.minecraft.hunger": "<PERSON><PERSON><PERSON><PERSON>l", "effect.minecraft.infested": "<PERSON><PERSON><PERSON>", "effect.minecraft.instant_damage": "<PERSON><PERSON>l", "effect.minecraft.instant_health": "<PERSON><PERSON>", "effect.minecraft.invisibility": "<PERSON>", "effect.minecraft.jump_boost": "Muk'ta bitel", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON>", "effect.minecraft.luck": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.mining_fatigue": "Ch'enal lubel", "effect.minecraft.nausea": "<PERSON><PERSON><PERSON> jolil", "effect.minecraft.night_vision": "<PERSON><PERSON><PERSON><PERSON><PERSON> il<PERSON>", "effect.minecraft.oozing": "<PERSON><PERSON><PERSON>", "effect.minecraft.poison": "<PERSON>mebal poxil", "effect.minecraft.raid_omen": "Al pask'op", "effect.minecraft.regeneration": "Poxtajel", "effect.minecraft.resistance": "<PERSON><PERSON>'<PERSON>kel", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "Ch'ajil lomel", "effect.minecraft.slowness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.speed": "Anil", "effect.minecraft.strength": "<PERSON><PERSON>", "effect.minecraft.trial_omen": "Al tsatsalil", "effect.minecraft.unluck": "<PERSON><PERSON>al", "effect.minecraft.water_breathing": "Ich'-ik' ta vo'", "effect.minecraft.weakness": "K'unibel", "effect.minecraft.weaving": "<PERSON><PERSON><PERSON>", "effect.minecraft.wind_charged": "Sutub-ik'", "effect.minecraft.wither": "<PERSON>'a'ubel", "effect.none": "Mu xchi'uk ejektoetik", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "K'upinel ta vo'", "enchantment.minecraft.bane_of_arthropods": "Ch'ayel ta artroporoetik", "enchantment.minecraft.binding_curse": "<PERSON><PERSON> k'optael ta chukel", "enchantment.minecraft.blast_protection": "Som yu'un t'omeletik", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.depth_strider": "Anil ta vo'", "enchantment.minecraft.efficiency": "Tunelal", "enchantment.minecraft.feather_falling": "Bulel", "enchantment.minecraft.fire_aspect": "<PERSON>'a<PERSON>'al aspekto", "enchantment.minecraft.fire_protection": "Som yu'un k'ok'", "enchantment.minecraft.flame": "K'ok'", "enchantment.minecraft.fortune": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.frost_walker": "Taiv avil", "enchantment.minecraft.impaling": "Ch'ojel", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON>l ta jech'el", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Elek'", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "<PERSON>'inal yu'un muk'ta nab", "enchantment.minecraft.lure": "Atraksion", "enchantment.minecraft.mending": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.multishot": "<PERSON><PERSON> t'omesel", "enchantment.minecraft.piercing": "Ch'ojel", "enchantment.minecraft.power": "Ju'el", "enchantment.minecraft.projectile_protection": "Som yu'un valaetik", "enchantment.minecraft.protection": "Som", "enchantment.minecraft.punch": "<PERSON><PERSON>", "enchantment.minecraft.quick_charge": "<PERSON><PERSON>", "enchantment.minecraft.respiration": "Ich'-ik'", "enchantment.minecraft.riptide": "Yuk'lajetel ta vo'", "enchantment.minecraft.sharpness": "Ts'ubts'ubal", "enchantment.minecraft.silk_touch": "T'enolanel sera", "enchantment.minecraft.smite": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON>'ul<PERSON>l", "enchantment.minecraft.sweeping": "<PERSON><PERSON>v", "enchantment.minecraft.sweeping_edge": "<PERSON><PERSON>v", "enchantment.minecraft.swift_sneak": "Anil t'iniel", "enchantment.minecraft.thorns": "Ch'ixetik", "enchantment.minecraft.unbreaking": "<PERSON>", "enchantment.minecraft.vanishing_curse": "Chopol k'optael ta sakch'ayel", "enchantment.minecraft.wind_burst": "T'omel ik'", "entity.minecraft.acacia_boat": "<PERSON><PERSON><PERSON> akasia", "entity.minecraft.acacia_chest_boat": "Varko akasia xchi'uk kaxa", "entity.minecraft.allay": "Jkoltaobbail", "entity.minecraft.area_effect_cloud": "Tok ejekto ti xjalije", "entity.minecraft.armadillo": "Ib", "entity.minecraft.armor_stand": "<PERSON><PERSON><PERSON>", "entity.minecraft.arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.axolotl": "Axolote", "entity.minecraft.bamboo_chest_raft": "Valsa xchi'uk kaxa", "entity.minecraft.bamboo_raft": "Valsa", "entity.minecraft.bat": "Sots'", "entity.minecraft.bee": "<PERSON><PERSON>ul pom", "entity.minecraft.birch_boat": "Varko averul", "entity.minecraft.birch_chest_boat": "Varko averul xchi'uk kaxa", "entity.minecraft.blaze": "Jk'ok'", "entity.minecraft.block_display": "<PERSON><PERSON><PERSON><PERSON> kubo", "entity.minecraft.boat": "Varko", "entity.minecraft.bogged": "J-a<PERSON>'el<PERSON><PERSON> k<PERSON>o", "entity.minecraft.breeze": "J-ik'", "entity.minecraft.breeze_wind_charge": "<PERSON>jel i<PERSON>'", "entity.minecraft.camel": "Toromerario", "entity.minecraft.cat": "<PERSON><PERSON>", "entity.minecraft.cave_spider": "Ch'enal om", "entity.minecraft.cherry_boat": "Varko ch'ixtot", "entity.minecraft.cherry_chest_boat": "Varko ch'ixtot xchi'uk kaxa", "entity.minecraft.chest_boat": "Varko xchi'uk kaxa", "entity.minecraft.chest_minecart": "Vakon xchi'uk kaxa", "entity.minecraft.chicken": "<PERSON><PERSON>'", "entity.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Vakon x<PERSON>'uk kubo mantal", "entity.minecraft.cow": "Vakax", "entity.minecraft.creaking": "<PERSON>ch'ak'ak'et<PERSON><PERSON>", "entity.minecraft.creaking_transient": "<PERSON>ch'ak'ak'et<PERSON><PERSON>", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Varko i<PERSON>'pulan tulan", "entity.minecraft.dark_oak_chest_boat": "Varko ik'pulan tulan xchi'uk kaxa", "entity.minecraft.dolphin": "<PERSON><PERSON>", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "<PERSON>la k'ok' muk'ta ain", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Tenbil ton alak'", "entity.minecraft.elder_guardian": "<PERSON><PERSON>", "entity.minecraft.end_crystal": "<PERSON>-<PERSON> kristal", "entity.minecraft.ender_dragon": "Ender muk'ta ain", "entity.minecraft.ender_pearl": "<PERSON><PERSON> ender perla", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker_fangs": "Ts'uts'up j-i<PERSON>'<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.experience_bottle": "Tenbil limeta k<PERSON>l", "entity.minecraft.experience_orb": "<PERSON><PERSON>", "entity.minecraft.eye_of_ender": "Sat ender", "entity.minecraft.falling_block": "<PERSON>bo ti chlome", "entity.minecraft.falling_block_type": "Yakal chlom li %se", "entity.minecraft.fireball": "<PERSON>la k'ok'", "entity.minecraft.firework_rocket": "<PERSON>ten yolonk'ok'", "entity.minecraft.fishing_bobber": "Tsak", "entity.minecraft.fox": "<PERSON><PERSON><PERSON><PERSON> vet", "entity.minecraft.frog": "Pok'ok'", "entity.minecraft.furnace_minecart": "Vakon xchi'uk jorno", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON>'ta j<PERSON>mel", "entity.minecraft.glow_item_frame": "Nop'ol marko", "entity.minecraft.glow_squid": "Nop'ol muk'ta ja'al-om", "entity.minecraft.goat": "Tentsun", "entity.minecraft.guardian": "Jchabivanej", "entity.minecraft.happy_ghast": "<PERSON><PERSON><PERSON><PERSON><PERSON> ghast", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Vakon xchi'uk malobil", "entity.minecraft.horse": "Ka'", "entity.minecraft.husk": "<PERSON><PERSON> j<PERSON><PERSON>", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "Tunesel", "entity.minecraft.iron_golem": "Jchabivanej tak'in", "entity.minecraft.item": "<PERSON><PERSON>", "entity.minecraft.item_display": "Olokrama item", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "Jabnaltikal varko", "entity.minecraft.jungle_chest_boat": "Jabnaltikal varko xchi'uk kaxa", "entity.minecraft.killer_bunny": "<PERSON> jmil<PERSON>ej t'ule", "entity.minecraft.leash_knot": "Sts'akavil ch'ojon", "entity.minecraft.lightning_bolt": "<PERSON><PERSON><PERSON>", "entity.minecraft.lingering_potion": "<PERSON><PERSON><PERSON><PERSON> poxil", "entity.minecraft.llama": "<PERSON><PERSON>'ta chij", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON><PERSON> muk'ta chij", "entity.minecraft.magma_cube": "<PERSON>'ak'al simchon", "entity.minecraft.mangrove_boat": "<PERSON>arko ja'alte'", "entity.minecraft.mangrove_chest_boat": "Varko ja'alte' xchi'uk kaxa", "entity.minecraft.marker": "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "<PERSON><PERSON><PERSON>", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "<PERSON><PERSON><PERSON> tulan", "entity.minecraft.oak_chest_boat": "Varko tulan xchi'uk kaxa", "entity.minecraft.ocelot": "<PERSON>'ox bolom", "entity.minecraft.ominous_item_spawner": "Chopol jvinajes item", "entity.minecraft.painting": "Bonobil", "entity.minecraft.pale_oak_boat": "Varko sak<PERSON>'an tulan", "entity.minecraft.pale_oak_chest_boat": "Varko sakpa<PERSON>'an tulan xchi'uk kaxa", "entity.minecraft.panda": "China oso chon", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>'", "entity.minecraft.phantom": "Muk'ta sots'", "entity.minecraft.pig": "<PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON>'el piglin", "entity.minecraft.pillager": "Mak<PERSON>", "entity.minecraft.player": "Jtajimol", "entity.minecraft.polar_bear": "<PERSON><PERSON><PERSON> oso chon", "entity.minecraft.potion": "Poxil", "entity.minecraft.pufferfish": "Ch'ixal choy", "entity.minecraft.rabbit": "T'ul", "entity.minecraft.ravager": "Jsokesvanej", "entity.minecraft.salmon": "<PERSON>al<PERSON>", "entity.minecraft.sheep": "<PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "Jbakubel", "entity.minecraft.skeleton_horse": "Jich'il ka'", "entity.minecraft.slime": "Simchon", "entity.minecraft.small_fireball": "Bik'it bola k'ak'al", "entity.minecraft.sniffer": "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Jchabivanej ta<PERSON>", "entity.minecraft.snowball": "Bola taiv", "entity.minecraft.spawner_minecart": "Vakon xchi'uk jvina<PERSON>", "entity.minecraft.spectral_arrow": "Nop'ol yolob", "entity.minecraft.spider": "Om", "entity.minecraft.splash_potion": "Jtenbail poxil", "entity.minecraft.spruce_boat": "<PERSON><PERSON><PERSON> toj", "entity.minecraft.spruce_chest_boat": "Varko toj xchi'uk kaxa", "entity.minecraft.squid": "<PERSON><PERSON>'ta ja'al-om", "entity.minecraft.stray": "<PERSON><PERSON>'<PERSON><PERSON>ba<PERSON>", "entity.minecraft.strider": "<PERSON><PERSON><PERSON><PERSON> tsuk'", "entity.minecraft.tadpole": "Avob", "entity.minecraft.text_display": "J<PERSON><PERSON>s ts'ib", "entity.minecraft.tnt": "<PERSON><PERSON><PERSON>", "entity.minecraft.tnt_minecart": "Vakon x<PERSON>'uk r<PERSON><PERSON>a", "entity.minecraft.trader_llama": "Smuk'ta chij j<PERSON>", "entity.minecraft.trident": "Trirente", "entity.minecraft.tropical_fish": "Jabnaltikal choy", "entity.minecraft.tropical_fish.predefined.0": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON>l choy", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON><PERSON><PERSON>al choy", "entity.minecraft.tropical_fish.predefined.13": "<PERSON>'choy", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON><PERSON><PERSON> parko", "entity.minecraft.tropical_fish.predefined.17": "<PERSON><PERSON><PERSON> choy", "entity.minecraft.tropical_fish.predefined.18": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.19": "Alkolal choy", "entity.minecraft.tropical_fish.predefined.2": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.20": "<PERSON>'<PERSON> puyuch'al choy", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON><PERSON> javan<PERSON>y", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.5": "Tajimolchoy", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON><PERSON> choy", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON>o<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Muk'ta tsa<PERSON><PERSON><PERSON>y", "entity.minecraft.tropical_fish.predefined.9": "Xalmonete", "entity.minecraft.tropical_fish.type.betty": "Beta choy", "entity.minecraft.tropical_fish.type.blockfish": "Kuboalchoy", "entity.minecraft.tropical_fish.type.brinely": "<PERSON>' pik'an choy", "entity.minecraft.tropical_fish.type.clayfish": "Lumilchoy", "entity.minecraft.tropical_fish.type.dasher": "Basa<PERSON><PERSON> choy", "entity.minecraft.tropical_fish.type.flopper": "Bitomchoy", "entity.minecraft.tropical_fish.type.glitter": "Kantilachoy", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "J<PERSON>'<PERSON>yaxaltikalchoy", "entity.minecraft.tropical_fish.type.spotty": "Butum choy", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON> choy", "entity.minecraft.tropical_fish.type.sunstreak": "Xojobalchoy", "entity.minecraft.turtle": "Ok", "entity.minecraft.vex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager": "Jbik'it lum", "entity.minecraft.villager.armorer": "Jpas eskuro", "entity.minecraft.villager.butcher": "Jmil-vakax", "entity.minecraft.villager.cartographer": "Ts'ibajom mapa", "entity.minecraft.villager.cleric": "<PERSON><PERSON>", "entity.minecraft.villager.farmer": "Jchabajom", "entity.minecraft.villager.fisherman": "Jtsak choy", "entity.minecraft.villager.fletcher": "<PERSON><PERSON> yolob", "entity.minecraft.villager.leatherworker": "Jchon nukul", "entity.minecraft.villager.librarian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.mason": "Jpas na", "entity.minecraft.villager.nitwit": "Yoloch", "entity.minecraft.villager.none": "Jbik'it lum", "entity.minecraft.villager.shepherd": "Jk'el-chij", "entity.minecraft.villager.toolsmith": "Jpas tak'in", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON> bo<PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "Jpakvanej", "entity.minecraft.wandering_trader": "V<PERSON>'<PERSON><PERSON><PERSON><PERSON> j<PERSON>l", "entity.minecraft.warden": "Jchabich'en", "entity.minecraft.wind_charge": "<PERSON>jel i<PERSON>'", "entity.minecraft.witch": "Chopol ants", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON><PERSON><PERSON> yu'un <PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON>il", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "Ipajesbil ka'", "entity.minecraft.zombie_villager": "Ipajesbil jbik'it lum", "entity.minecraft.zombified_piglin": "Ipajes<PERSON> piglin", "entity.not_summonable": "Mu xu' xvinaj li entirale ta tos %s", "event.minecraft.raid": "Pask'op", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Pask'op - Ch'ayelal", "event.minecraft.raid.raiders_remaining": "Makbeetik oy to: %s", "event.minecraft.raid.victory": "Paskanal", "event.minecraft.raid.victory.full": "Pask'op - Paskanal", "filled_map.buried_treasure": "S<PERSON>pa me'tak'in", "filled_map.explorer_jungle": "Mapa vu'lan jabnal", "filled_map.explorer_swamp": "Mapa vu'lan ach'eltik", "filled_map.id": "ID #%s", "filled_map.level": "(Nivel %s/%s)", "filled_map.locked": "Makbil", "filled_map.mansion": "Mapa vu'lan ik'pulan te'tik", "filled_map.monument": "Mapa vu'lan muk'ta nab", "filled_map.scale": "Eskala 1:%s", "filled_map.trial_chambers": "Mapa kamara tsatsalil", "filled_map.unknown": "Mu ojti<PERSON><PERSON> mapa", "filled_map.village_desert": "Mapa bik'it lum ta xokol balumil", "filled_map.village_plains": "Mapa yaxaltikal bik'it lum", "filled_map.village_savanna": "Mapa takin yaxaltikal bik'it lum", "filled_map.village_snowy": "Mapa taiv yaxaltikal bik'it lum", "filled_map.village_taiga": "Mapa tojtikal bik'it lum", "flat_world_preset.minecraft.bottomless_pit": "Sat vo' mu xchi'uk yok", "flat_world_preset.minecraft.classic_flat": "Bats'i", "flat_world_preset.minecraft.desert": "<PERSON><PERSON><PERSON> bal<PERSON>l", "flat_world_preset.minecraft.overworld": "Sba Balumil", "flat_world_preset.minecraft.redstone_ready": "Chapaj sventa redstone", "flat_world_preset.minecraft.snowy_kingdom": "<PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.the_void": "<PERSON>", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "<PERSON><PERSON><PERSON><PERSON>", "gameMode.changed": "La sjel sba ta %s li smanera ataj<PERSON>le", "gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "gameMode.hardcore": "¡Toj tsatsal!", "gameMode.spectator": "Jk'elvanej", "gameMode.survival": "<PERSON><PERSON><PERSON> k<PERSON>l", "gamerule.allowFireTicksAwayFromPlayer": "Yepajel k'ok' ta nom jtajimoletik", "gamerule.allowFireTicksAwayFromPlayer.description": "Chak' mi sk'an ya'i ch-epajik li k'ok'e o li tsuk'e ta jun namal ta mas ti vaxakset' ta nom k'uk elanuk jtajimol", "gamerule.announceAdvancements": "Chal porokresoetik", "gamerule.blockExplosionDropDecay": "Ta t'omeletik yu'un <PERSON><PERSON>, jay<PERSON><PERSON> kuboetik mu spajesik li k'usitik yu'unike", "gamerule.blockExplosionDropDecay.description": "Jayibuk k'usitik pajesatik xa yu'un t'omeskuboetik chch'ayik ta yut t'omel.", "gamerule.category.chat": "<PERSON><PERSON>il", "gamerule.category.drops": "<PERSON><PERSON>' k'usitik", "gamerule.category.misc": "<PERSON><PERSON>", "gamerule.category.mobs": "<PERSON><PERSON><PERSON>", "gamerule.category.player": "Jtajimoletik", "gamerule.category.spawning": "<PERSON><PERSON><PERSON>anul", "gamerule.category.updates": "A<PERSON>'ubtas balumiletik", "gamerule.commandBlockOutput": "Chal li stunesel kubo mantale", "gamerule.commandModificationBlockLimit": "<PERSON>s'ak sjelel kubo mantal", "gamerule.commandModificationBlockLimit.description": "Li yatolalik kuboetike xu' s<PERSON>l sbaik ta jmoj yu'un jun mantal, jech k'uch'al ''/fill'' o ''/clone''.", "gamerule.disableElytraMovementCheck": "<PERSON><PERSON><PERSON> k'elan vilel End xik'", "gamerule.disablePlayerMovementCheck": "Tstub k'elan-bak'el yu'unik jtajimoletik", "gamerule.disableRaids": "T<PERSON><PERSON> p<PERSON>'opetik", "gamerule.doDaylightCycle": "K'ak'al xchi'uk ak'obal", "gamerule.doEntityDrops": "<PERSON><PERSON><PERSON><PERSON> k'usitik k'alal sk'as entiral<PERSON>k", "gamerule.doEntityDrops.description": "Chak' spajesik k'usitik li entiraletike k'alal sokik. Tskap vakonetik (xchi'uk sk'ejebik), <PERSON><PERSON><PERSON><PERSON>, var<PERSON><PERSON><PERSON>, ets.", "gamerule.doFireTick": "P'ol k'ok'", "gamerule.doImmediateRespawn": "<PERSON><PERSON> cha'v<PERSON><PERSON>", "gamerule.doInsomnia": "Tsvinajes muk'ta sots'etik", "gamerule.doLimitedCrafting": "Tsk'an resetaetik sventa meltsanel", "gamerule.doLimitedCrafting.description": "<PERSON>, xu' no'ox xmeltsanatik li chanbil resetaetike.", "gamerule.doMobLoot": "Tspajes k'usitik k'alal milatik li chanuletike", "gamerule.doMobLoot.description": "Chak' spajesik k'usitik xchi'uk bola kuxlejaletik li entiraletike.", "gamerule.doMobSpawning": "Tsvinajes chanuletik", "gamerule.doMobSpawning.description": "Jayibuk entiraletik oy nan sleyik stukik.", "gamerule.doPatrolSpawning": "Tsvinajes tsobol makbeetik", "gamerule.doTileDrops": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.doTileDrops.description": "Chak' spajesik k'usitik xchi'uk bola kuxlejaletik k'alal sokik li kuboetike.", "gamerule.doTraderSpawning": "Tsvinajes va'vunajel jchonolajeletik", "gamerule.doVinesSpread": "Epajel ak'", "gamerule.doVinesSpread.description": "Chak' ch-epajik li ak'etike ta kuboetik te oyik ta nopol. Mu skap li yantik tos ak'etike jech k'ucha'al j-ok'el ak'etik, ts'otol ak'etik, ets.", "gamerule.doWardenSpawning": "Tsvina<PERSON>s <PERSON>eneti<PERSON>", "gamerule.doWeatherCycle": "Osil", "gamerule.drowningDamage": "Ya<PERSON><PERSON>l ta jik'avel", "gamerule.enderPearlsVanishOnDeath": "Chch'ayik li tenbil ender perlaetike k'alal chalaj", "gamerule.enderPearlsVanishOnDeath.description": "Li ender perlaetike tenbil yu'un jun jtajimol ta xch'ayik k'alal xlaj li jtajimol le'e.", "gamerule.entitiesWithPassengersCanUsePortals": "Entiraletik xchi'uk jxanbaletik xu' stunesik ochebaletik", "gamerule.entitiesWithPassengersCanUsePortals.description": "Chak' spasik xanbal li entiraletike xchi'uk jxanbaletik ta o'lol ochebaletik ta Nether, End xchi'uk ti' Endetik.", "gamerule.fallDamage": "Ya<PERSON>asel ta lomel", "gamerule.fireDamage": "Ya<PERSON><PERSON>l ta k'ok'", "gamerule.forgiveDeadPlayers": "Chak'bik perton li chamem jtajimoletike", "gamerule.forgiveDeadPlayers.description": "Ta xtiniik li ilinem neotral chanuletik k'alal li jtajimole le'ik sp'ajik ta xlaj ta nopol.", "gamerule.freezeDamage": "Yaintasel ta botibel", "gamerule.globalSoundEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON> balumil", "gamerule.globalSoundEvents.description": "K'alal chk'otik ta pasel jayibuk tajimoltiketik,, j<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ch<PERSON>j jun <PERSON>, chtij buyuk li nuk'ilale.", "gamerule.keepInventory": "Tsmak'<PERSON><PERSON> k'e<PERSON><PERSON> ta <PERSON> lajelal", "gamerule.lavaSourceConversion": "T<PERSON>'atajes tsuk' ta avil", "gamerule.lavaSourceConversion.description": "<PERSON><PERSON>alal chib avil tsuk' tsjoyobtaik jun beinel tsuk', chk'ataj ta jun no'ox avil.", "gamerule.locatorBar": "Tstsan li bara snupel j<PERSON>jimole", "gamerule.locatorBar.description": "<PERSON>, ch<PERSON>j jtel bara sventa chak' ta ilel bu te oyik li jtajimoletike.", "gamerule.logAdminCommands": "Chal li smantal<PERSON>k j-a<PERSON><PERSON><PERSON>van<PERSON>e", "gamerule.maxCommandChainLength": "Ts'ak karena mantal", "gamerule.maxCommandChainLength.description": "Tsts'ak sba ta karena kubo mantaletik xchi'uk tuneletik.", "gamerule.maxCommandForkCount": "Sts'ak skontekstotak mantal", "gamerule.maxCommandForkCount.description": "Sts'ak yatolalik kontekstoetik xu' xtunesatik ta mantaletik jech k'ucha'al ''execute as''.", "gamerule.maxEntityCramming": "Ts'ak entiral ta kubo", "gamerule.minecartMaxSpeed": "Sts'<PERSON>k yanilal vakon", "gamerule.minecartMaxSpeed.description": "Sts'ak bats'i anilal yu'un jbej vakon chbak' ta lum.", "gamerule.mobExplosionDropDecay": "Ta t'o<PERSON>, jay<PERSON><PERSON> kuboetik mu spajesik li k'usitik yu'unike", "gamerule.mobExplosionDropDecay.description": "Jayibuk k'usitik pajesatik xa yu'un t'omes chanuletik chch'ayik ta yut t'omel.", "gamerule.mobGriefing": "Chak' <PERSON><PERSON>linvanik li chanuletike", "gamerule.naturalRegeneration": "<PERSON><PERSON><PERSON>", "gamerule.playersNetherPortalCreativeDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ochebal ta Nether ta manera j<PERSON>ej", "gamerule.playersNetherPortalCreativeDelay.description": "Sk'a<PERSON>'al<PERSON> (ta sikloetik) sk'an chkom jun jtajimol ta sba ochebal ta manera jpasvanej sventa sjel li rimensione.", "gamerule.playersNetherPortalDefaultDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ochebal ta Nether ta maneraetik mu ja'uk jpasvanej", "gamerule.playersNetherPortalDefaultDelay.description": "Sk'a<PERSON>'al<PERSON> (ta sikloetik) sk'an chkom jun jtajimol ta sba ochebal ta maneraetik mu ja'uk jpasvanej sventa sjel li rimensione.", "gamerule.playersSleepingPercentage": "Svo'vinikalil vayem jtajimoletik", "gamerule.playersSleepingPercentage.description": "Li k'anbil svo'vinikalilik jtajimoletike sk'an chvayik sventa sujik li ak'obale.", "gamerule.projectilesCanBreakBlocks": "Xu' sk'asik kuboetik li valaetike", "gamerule.projectilesCanBreakBlocks.description": "Chak' mi xu' sk'as kuboetik li sma<PERSON>lik valaetike xu' xa ox chich'ik k'asel.", "gamerule.randomTickSpeed": "Perekuensia yu'un k'uk no'ox sikloetike", "gamerule.reducedDebugInfo": "Tsbik'tajes <PERSON>lal F3", "gamerule.reducedDebugInfo.description": "Tsbik'tajes li k'usitik yu'un pantaya kusele k'alal snet' F3.", "gamerule.sendCommandFeedback": "Chak' ta ilel li stak'eltakik mantaletike", "gamerule.showDeathMessages": "Chak' ta ilel li mantal lajelaletike", "gamerule.snowAccumulationHeight": "Snatilal lats taiv", "gamerule.snowAccumulationHeight.description": "<PERSON><PERSON><PERSON><PERSON> chyal ta<PERSON>, chvinajik k'olol taivetik ta lum, sts'ak to li ya<PERSON><PERSON> k'ololetik li'e.", "gamerule.spawnChunkRadius": "<PERSON><PERSON><PERSON><PERSON> set'el cha'vinajeletik", "gamerule.spawnChunkRadius.description": "Yepalik set'eletik te nojik to ta nopol cha'vinajebal.", "gamerule.spawnRadius": "<PERSON><PERSON><PERSON>v<PERSON>", "gamerule.spawnRadius.description": "Cha<PERSON>' li smuk'ul avile ta nopol cha'vina<PERSON>bal bu xu' xvinajik li jtajimoletike.", "gamerule.spectatorsGenerateChunks": "Tsvinajes osil ta manera jk'elvanej", "gamerule.tntExplodes": "Chak' chtsanat xchi'uk cht'om li rinamitae", "gamerule.tntExplosionDropDecay": "Ta t'o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jay<PERSON><PERSON> kuboetik mu spajesik li k'usitik yu'unike", "gamerule.tntExplosionDropDecay.description": "Jayibuk k'usitik pajesatik xa yu'un t'omes rinamitaetik chch'ayik ta yut t'omel.", "gamerule.universalAnger": "<PERSON><PERSON> k'ak'al o'onto<PERSON>l", "gamerule.universalAnger.description": "<PERSON>, ta smajik buch'uuk no'ox ta tseltseltik li ilinem neotral chanuletik. Mas lek ch-abtej mi tubbil li leye ''Chak'bik perton chamem jtajimoletik''.", "gamerule.waterSourceConversion": "Tsk'atajes vo' ta avil", "gamerule.waterSourceConversion.description": "<PERSON>'alal chib avil vo' tsjoy<PERSON>ta jun beinel vo', chk'ataj ta jun no'ox avil.", "generator.custom": "<PERSON><PERSON><PERSON>", "generator.customized": "<PERSON><PERSON><PERSON> (poko')", "generator.minecraft.amplified": "MUK'UBTASBIL", "generator.minecraft.amplified.info": "Alel: ¡ja' no'ox sventa tajimol! Sk'an jbej lekil chinab tak'in.", "generator.minecraft.debug_all_block_states": "<PERSON><PERSON>", "generator.minecraft.flat": "<PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.large_biomes": "Muk'ta osiletik", "generator.minecraft.normal": "Bats'i", "generator.minecraft.single_biome_surface": "<PERSON><PERSON><PERSON><PERSON><PERSON> osil", "generator.single_biome_caves": "Ch'enetik", "generator.single_biome_floating_islands": "Islaetik ta vinajel", "gui.abuseReport.attestation": "Mi chatak li al-mulil li'e, chaval li alelal la avak'e ja' melel xchi'uk ts'akal.", "gui.abuseReport.comments": "Lo'iletik", "gui.abuseReport.describe": "<PERSON>'alal chavak'bunk<PERSON>k alelal, chakoltaunkutik ta lekil nopel.", "gui.abuseReport.discard.content": "Mi chalok'e, ta xach'ay li al-mulil li'e xchi'uk li alo'iltake. \n¿Mi chak'an chalok'?", "gui.abuseReport.discard.discard": "Chlok' xchi'uk chikta li al-mulile", "gui.abuseReport.discard.draft": "Tsk'ej jech k'u<PERSON>'al voraror", "gui.abuseReport.discard.return": "Cha<PERSON>' sba ta jelel", "gui.abuseReport.discard.title": "¿Mi chak'an chavikta li al-mulile xchi'uk li lo'iletike?", "gui.abuseReport.draft.content": "¿Mi chak'an to chajel li al-mulile te xa oy o chavikta xchi'uk chapas jun ach'?", "gui.abuseReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.abuseReport.draft.edit": "Cha<PERSON>' sba ta jelel", "gui.abuseReport.draft.quittotitle.content": "¿Mi chak'an to chajel o chavikta?", "gui.abuseReport.draft.quittotitle.title": "Oy svoraro aval-mul-lo'il ta xch'ay mi chalok'e", "gui.abuseReport.draft.title": "¿Mi chak'an chajel li svoraro al-mul-lo'ile?", "gui.abuseReport.error.title": "Ch'ay xa'i k'alal stak ox li aval-mule", "gui.abuseReport.message": "¿Bu la ak'el li chopol ch'unel li'e? Li' skoltaunkutike ta st'unel li akasoe.", "gui.abuseReport.more_comments": "Alo li k'usi k'ot ta pasele:", "gui.abuseReport.name.comment_box_label": "Albunkutik k'u yu'un chak'an chaval li smul li biil li'e:", "gui.abuseReport.name.reporting": "Yakal chaval li smul ''%se''.", "gui.abuseReport.name.title": "Chal li smul sbi j<PERSON>le", "gui.abuseReport.observed_what": "¿K'ucha'al yakal chaval li smul li'e?", "gui.abuseReport.read_info": "Mas chano ta al-muliletik", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Chopol poxiletik o alkol", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Li jun krixchanoe yakal ch-ik'van ta spasel partisipar ta mu ak'bil abtelaletik, o chik' ololetik ta uch'bajel.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Ilbajin-olol o tsatsal tsak-olol", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Li jun krixchanoe yakal chlo'ilaj o ch-ik'van ta chopol ch'uneletik ta ololetik.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON>, elek' alelal o jutk'opetik", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Li jun krixchanoe yakal tsyaintas li areputasione o li sreputasion yan krixchanoe, yakal chelk'an li yalelal jun krixchanoe o chpuk chopol alelal sventa slo'la yantik.", "gui.abuseReport.reason.description": "Alel:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON> al-mulil", "gui.abuseReport.reason.generic": "Ta jk'an chkal li smule", "gui.abuseReport.reason.generic.description": "La sa'sunun / oy k'usi la spas mu jk'upin.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON>'sunel o ilba<PERSON>el", "gui.abuseReport.reason.harassment_or_bullying.description": "Li jun krixchanoe yakal chil<PERSON>ot, tsbik'tal-k'oponot, tsnut<PERSON>t o tsnuts yan krixchano. Li' tskap mi ep ta sk'an sk'oponot o sk'opon yan krixchano mu xchi'uk yak'ele, o mi chpuk li avalelale o li yalelal yan krixchanoe mu xchi'uk yak'el (''doxing'').", "gui.abuseReport.reason.hate_speech": "K'op p'ajel", "gui.abuseReport.reason.hate_speech.description": "Li jun krixchanoe yakal tsbik'tal-k'oponot o tsbik'tal-k'opon yan jtajimol yu'un k'uxi x-elan, xch'unel yo'onton, slum<PERSON><PERSON> o mi ja' vinik o antse.", "gui.abuseReport.reason.imminent_harm": "Xi'el ta anil:\nSib ta syaintasel yantik", "gui.abuseReport.reason.imminent_harm.description": "Li jun krixchanoe yakal chal syaintasot o syaintas yan krixchano ta melel kuxlejal.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Slok'olik kats'al krixchanoetik mu xchi'uk yak'elik", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Li jun krixchanoe yakal chal, ch<PERSON><PERSON>, o ch-ik'van ta spukel snak'al lok'olik kats'al krixchanoetik.", "gui.abuseReport.reason.self_harm_or_suicide": "Xi'el ta anil:\n<PERSON><PERSON><PERSON><PERSON><PERSON> o milbail", "gui.abuseReport.reason.self_harm_or_suicide.description": "Li jun krixchanoe yakal chal syaintas nan sba ta melel kuxlejal o chal ta spas ta melel.", "gui.abuseReport.reason.sexually_inappropriate": "Chopol chi'invinik o chi'in-ants", "gui.abuseReport.reason.sexually_inappropriate.description": "Vinajeletik chak'ik ta ilel kats'al bek'tal takupal<PERSON>k, atil o ichonil, xchi'uk tsatsal tsak-ants o tsakvinik.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Pasxi'elal o xi'el chuvajil", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON> jun krix<PERSON>oe yakal chal, ch-<PERSON><PERSON><PERSON><PERSON> o sibtasvan sventa chk'ot ta pasel li pasxi'elale o li xi'el chuvajile yu'un politika, xch'unel o'ontonal, nopoletik o yantik k'usitik.", "gui.abuseReport.reason.title": "T<PERSON>'uj li tos al-mulile", "gui.abuseReport.report_sent_msg": "Lek la kich'kutik li avalele. ¡Kolaval!\n\nTa sk'elan ta anil li kekipokutike.", "gui.abuseReport.select_reason": "T<PERSON>'uj li tos al-mulile", "gui.abuseReport.send": "Tstak li al-mulile", "gui.abuseReport.send.comment_too_long": "Bik'tajeso li lo'ile", "gui.abuseReport.send.error_message": "K'ot ta pasel li jun ch'ay-o'ontonale k'alal stak ox li aval-mule:\n''%s''", "gui.abuseReport.send.generic_error": "K'ot ta pasel ta ora no'ox li jun ch'ay-o'ontonale k'alal stak ox li aval-mule.", "gui.abuseReport.send.http_error": "K'ot ta pasel ta ora no'ox li ch'ay-o'ontonale HTTP k'alal stak ox li aval-mule.", "gui.abuseReport.send.json_error": "La sta jun ch'ay-o'ontonal k'alal stak ox li aval-mule.", "gui.abuseReport.send.no_reason": "T'ujo jun tos sventa li al-mulile", "gui.abuseReport.send.not_attested": "K'elo li ts'ibe xchi'uk k'elano mi la avak'be senyail li kaxa k'elanele sventa chatak li al-mulile", "gui.abuseReport.send.service_unavailable": "Mu xu' sta li tunelal al-mulil yu'un tsatsal tsakele. K'elano mi chats'ak aba ta internet xchi'uk paso nixtok, avokoluk.", "gui.abuseReport.sending.title": "Yakal tstak li aval-mule...", "gui.abuseReport.sent.title": "Al-mulil takbil", "gui.abuseReport.skin.title": "Chal li smul s<PERSON><PERSON> j<PERSON>", "gui.abuseReport.title": "Chal li smul j<PERSON><PERSON><PERSON>", "gui.abuseReport.type.chat": "Smantaltak lo'il", "gui.abuseReport.type.name": "Sbi jtajimol", "gui.abuseReport.type.skin": "Svinajelal jtajimol", "gui.acknowledge": "Chka'i", "gui.advancements": "Porokresoetik", "gui.all": "Skotol", "gui.back": "<PERSON>", "gui.banned.description": "%s\n\n%s\n\nMas chano li ta ts'akel li'e: %s", "gui.banned.description.permanent": "Lok'esat ta jech'el li a<PERSON>, li' sk'an chal mu xu' xatajin ta internete o xa-och ta Realms.", "gui.banned.description.reason": "Ach'ach' to la kich'kutik jun alel yu'un chopol ch'unel yu'un akuenta. La sk'elan akaso li kekipokutike xchi'uk la sko'oltas jech k'ucha'al %s, ja' tskontrain li sleytak skomon osil Minecrafte.", "gui.banned.description.reason_id": "Yavi': %s", "gui.banned.description.reason_id_message": "Yavi': %s - %s", "gui.banned.description.temporary": "%s <PERSON><PERSON><PERSON><PERSON> to cha'e, mu xu' xatajin ta internet o xa-och ta Realms.", "gui.banned.description.temporary.duration": "Lok'esat o li akuentae xchi'uk ta x-ak'at nixtok ta %s.", "gui.banned.description.unknownreason": "Ach'ach' to la kich'kutik jun alel yu'un chopol ch'unel yu'un akuenta. La sk'elan akaso li kekipokutike xchi'uk la sta ja' tskontrain li sleytak skomon osil Minecrafte.", "gui.banned.name.description": "Li abi avie (''%s'') mu xch'un li sleytak komon osile. Xu' xatajin ta smanera jun no'ox jtajimol pe sk'an chajel li abie sventa chatajin ta internet.\n\nMas chano o tako jun kaso xchi'uk li ts'akel li'e: %s", "gui.banned.name.title": "Biil mu ak'bil ta tajinebal", "gui.banned.reason.defamation_impersonation_false_information": "Li k'exta alelale o li jel-alelale sventa lo'lavanel", "gui.banned.reason.drugs": "Aleletik ta mu ak'bil poxiletik", "gui.banned.reason.extreme_violence_or_gore": "Lekil o epal aleletik ta ilinemal o milvanel ta melel kuxlejal", "gui.banned.reason.false_reporting": "Epal chopol aleletik", "gui.banned.reason.fraud": "Chopol tamel o tunesel yu'un konteniro", "gui.banned.reason.generic_violation": "Chopol ch'unmantal yu'un sleytak komon osil", "gui.banned.reason.harassment_or_bullying": "Chopol k'op tunesbil sventa chipal k'opta", "gui.banned.reason.hate_speech": "K'op p'ajeletik o ch'akel", "gui.banned.reason.hate_terrorism_notorious_figure": "Aleletik ta tsobol p'ajeletik, xi'el tsoboletik o toj ojtikinbil tsobajeletik", "gui.banned.reason.imminent_harm_to_person_or_property": "<PERSON><PERSON><PERSON><PERSON> sventa syaintas krixchanoetik o naetik ta melel kuxlejal", "gui.banned.reason.nudity_or_pornography": "Ak' ta ilel yu'un slok'olik kats'al krixchanoetik", "gui.banned.reason.sexually_inappropriate": "K'usitik ta chi'in-ants o chi'invinik", "gui.banned.reason.spam_or_advertising": "Espam o puk k'op", "gui.banned.skin.description": "Li avinajelal avie (''%s'') mu xch'un li sleytak komon osile. Xu' xatajin xchi'uk jun bats'i vinajelal o chat'uj jun ach'.\n\nMas chano o tako jun kaso xchi'uk li ts'akel li'e: %s", "gui.banned.skin.title": "<PERSON><PERSON><PERSON><PERSON> mu ak'bil", "gui.banned.title.permanent": "Kuenta lok'esbil ta jech'el", "gui.banned.title.temporary": "<PERSON>enta lok'esbil j-ok'", "gui.cancel": "Tsvos", "gui.chatReport.comments": "Lo'iletik", "gui.chatReport.describe": "<PERSON>'alal chavak'bunk<PERSON>k alelal, chakoltaunkutik ta lekil nopel.", "gui.chatReport.discard.content": "Mi chalok'e, ta xach'ay li al-mulil li'e xchi'uk li alo'iltake. \n¿Mi chak'an chalok'?", "gui.chatReport.discard.discard": "Chlok' xchi'uk chikta li al-mulile", "gui.chatReport.discard.draft": "Tsk'ej jech k'u<PERSON>'al voraror", "gui.chatReport.discard.return": "Cha<PERSON>' sba ta jelel", "gui.chatReport.discard.title": "¿Mi chak'an chavikta li al-mulile xchi'uk li lo'iletike?", "gui.chatReport.draft.content": "¿Mi chak'an to chajel li al-mulile te xa oy o chavikta xchi'uk chapas jun ach'?", "gui.chatReport.draft.discard": "<PERSON><PERSON><PERSON>", "gui.chatReport.draft.edit": "Cha<PERSON>' sba ta jelel", "gui.chatReport.draft.quittotitle.content": "¿Mi chak'an to chajel o chavikta?", "gui.chatReport.draft.quittotitle.title": "Oy svoraro aval-mul-lo'il ta xch'ay mi chalok'e", "gui.chatReport.draft.title": "¿Mi chak'an chajel li svoraro al-mul-lo'ile?", "gui.chatReport.more_comments": "Alo li k'usi k'ot ta pasele:", "gui.chatReport.observed_what": "¿K'ucha'al yakal chaval li smul li'e?", "gui.chatReport.read_info": "Chan<PERSON> ta al-muliletik", "gui.chatReport.report_sent_msg": "Lek la kich'kutik li avalele. ¡Kolaval!\n\nTa sk'elan ta anil li kekipokutike.", "gui.chatReport.select_chat": "Tst'uj li smantaltak lo'ile sventa chal li smulike", "gui.chatReport.select_reason": "T<PERSON>'uj li tos al-mulile", "gui.chatReport.selected_chat": "La at'uj %s mantal sventa chaval li smule", "gui.chatReport.send": "Tstak li al-mulile", "gui.chatReport.send.comments_too_long": "Bik'tajeso li lo'ile", "gui.chatReport.send.no_reason": "T'ujo jun tos sventa li al-mulile", "gui.chatReport.send.no_reported_messages": "T'ujo jun smantal lo'il sventa chaval li smule", "gui.chatReport.send.too_many_messages": "<PERSON><PERSON> chak'an chakap epal mantaletik ta al-mulil", "gui.chatReport.title": "Chal li smul j<PERSON><PERSON><PERSON>", "gui.chatSelection.context": "Li mantaletike yu'un li t'ujel li'e ta skap sbaik sventa skap yan konteksto", "gui.chatSelection.fold": "%s nak'al mantal", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "Och ta lo'il li %se", "gui.chatSelection.message.narrate": "Xi li %se: ''%s'' ta %s", "gui.chatSelection.selected": "%s/%s t'ujbil mantal", "gui.chatSelection.title": "T'ujo li mantaletike chak'an chaval li smulike", "gui.continue": "Xyaket", "gui.copy_link_to_clipboard": "Tslok'ta li ts'akele", "gui.days": "%s k'ak'al", "gui.done": "Pasbil", "gui.down": "<PERSON>lon", "gui.entity_tooltip.type": "Tos: %s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "La sp'aj %s archivo", "gui.fileDropFailure.title": "<PERSON><PERSON><PERSON> k'alal skap ox archivoetik", "gui.hours": "%s ora", "gui.loadingMinecraft": "Yakal tsmeltsan Minecraft", "gui.minutes": "%s minuto", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "Voton ta %s", "gui.narrate.editBox": "<PERSON><PERSON>ya ts'ib ta %s: %s", "gui.narrate.slider": "Vara ta %s", "gui.narrate.tab": "Pestanya ta %s", "gui.no": "<PERSON><PERSON><PERSON><PERSON>", "gui.none": "<PERSON><PERSON><PERSON><PERSON>", "gui.ok": "Lek", "gui.open_report_dir": "<PERSON><PERSON><PERSON><PERSON> sna al-mulil", "gui.proceed": "Xyaket", "gui.recipebook.moreRecipes": "Paso bats'i klik sventa mas", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Tsa'...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON> chak' ta ilel skotol", "gui.recipebook.toggleRecipes.blastable": "Cha<PERSON>' ta ilel li k'usi xu' x-unije", "gui.recipebook.toggleRecipes.craftable": "Chak' ta ilel li k'usi xu' xmeltsaje", "gui.recipebook.toggleRecipes.smeltable": "Chak' ta ilel li k'usi xu' xlakie", "gui.recipebook.toggleRecipes.smokable": "Chak' ta ilel li k'usi xu' xch'ataje", "gui.report_to_server": "<PERSON><PERSON><PERSON> smul li servilore", "gui.socialInteractions.blocking_hint": "Chchapanvan xchi'uk kuenta yu'un Microsoft", "gui.socialInteractions.empty_blocked": "Ch'abal lok'esbil jtajimoletik ta lo'il", "gui.socialInteractions.empty_hidden": "Ch'abal nak'al jtajimoletik ta lo'il", "gui.socialInteractions.hidden_in_chat": "Ta snak' sbaik li smantal lo'iltak %se", "gui.socialInteractions.hide": "Tsnak' ta lo'il", "gui.socialInteractions.narration.hide": "Tsnak' li smantaltak %se", "gui.socialInteractions.narration.report": "Chal li smul %se", "gui.socialInteractions.narration.show": "Chak' ta ilel li smantaltak %se", "gui.socialInteractions.report": "Chal smul", "gui.socialInteractions.search_empty": "Mu la sta jun jtajimol xchi'uk li biil le'e", "gui.socialInteractions.search_hint": "Tsa'...", "gui.socialInteractions.server_label.multiple": "%s: %s jtajimoletik", "gui.socialInteractions.server_label.single": "%s: %s jtajimol", "gui.socialInteractions.show": "Chak' ta ilel ta lo'il", "gui.socialInteractions.shown_in_chat": "Ta xvinajik li smantal lo'iltak %se", "gui.socialInteractions.status_blocked": "Lok'esbil", "gui.socialInteractions.status_blocked_offline": "Lok'esbil (mu xchi'uk internet)", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Nak'al (mu xchi'uk internet)", "gui.socialInteractions.status_offline": "Mu xchi'uk internet", "gui.socialInteractions.tab_all": "Skotol", "gui.socialInteractions.tab_blocked": "Lok'esbil", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Jipetbateletik", "gui.socialInteractions.tooltip.hide": "Tsnak' li mantaletike", "gui.socialInteractions.tooltip.report": "Chal li smul j<PERSON><PERSON><PERSON>", "gui.socialInteractions.tooltip.report.disabled": "<PERSON> yakal ch-a<PERSON><PERSON> li <PERSON> al<PERSON>uli<PERSON>", "gui.socialInteractions.tooltip.report.no_messages": "<PERSON>'<PERSON><PERSON> smantaltak %s xu' chal smulik", "gui.socialInteractions.tooltip.report.not_reportable": "Mu xu' chal li smul li jtajimol li'e yu'un smantaltak mu k'elanatik li ta servilor li'e", "gui.socialInteractions.tooltip.show": "Chak' ta ilel li mantaletike", "gui.stats": "Estaristikaetik", "gui.toMenu": "Ta sut ta lista servilor", "gui.toRealms": "Ta sut ta lista Realms", "gui.toTitle": "Ta sut ta spantaya sbi tajimol", "gui.toWorld": "Ta sut ta lista balumil", "gui.togglable_slot": "Paso klik sventa chatub li avile", "gui.up": "Ak'ol", "gui.waitingForResponse.button.inactive": "Pat (%ss)", "gui.waitingForResponse.title": "<PERSON><PERSON> tsmala li servilore", "gui.yes": "<PERSON><PERSON>", "hanging_sign.edit": "<PERSON><PERSON><PERSON><PERSON> li smantal ji<PERSON>j let<PERSON>", "instrument.minecraft.admire_goat_horn": "Le<PERSON><PERSON> ilel", "instrument.minecraft.call_goat_horn": "Ik'el", "instrument.minecraft.dream_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.feel_goat_horn": "<PERSON>'<PERSON>el", "instrument.minecraft.ponder_goat_horn": "<PERSON>l", "instrument.minecraft.seek_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.sing_goat_horn": "K'ejimol", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "inventory.binSlot": "<PERSON><PERSON><PERSON><PERSON>", "inventory.hotbarInfo": "K'ejo li bara avie xchi'uk %1$s+%2$s", "inventory.hotbarSaved": "La sk'ej li bara iteme (%1$s+%2$s sventa chatunes)", "item.canBreak": "Xu' sk'as:", "item.canPlace": "<PERSON><PERSON> x<PERSON>' sba ta sba:", "item.canUse.unknown": "<PERSON> oj<PERSON>", "item.color": "Bonolil: %s", "item.components": "%s komponente", "item.disabled": "Tubbil item", "item.durability": "Jalijelal: %s / %s", "item.dyed": "<PERSON><PERSON>", "item.minecraft.acacia_boat": "<PERSON><PERSON><PERSON> akasia", "item.minecraft.acacia_chest_boat": "Varko akasia xchi'uk kaxa", "item.minecraft.allay_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.amethyst_shard": "Xut amatista", "item.minecraft.angler_pottery_shard": "<PERSON>t seram<PERSON> j<PERSON>ak<PERSON>y", "item.minecraft.angler_pottery_sherd": "<PERSON>t seram<PERSON> j<PERSON>ak<PERSON>y", "item.minecraft.apple": "Mantsana", "item.minecraft.archer_pottery_shard": "<PERSON>t seramika j-alkol", "item.minecraft.archer_pottery_sherd": "<PERSON>t seramika j-alkol", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON><PERSON><PERSON> ib", "item.minecraft.armadillo_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> ib", "item.minecraft.armor_stand": "<PERSON><PERSON><PERSON>", "item.minecraft.arms_up_pottery_shard": "<PERSON><PERSON> seramika axinal", "item.minecraft.arms_up_pottery_sherd": "<PERSON><PERSON> seramika axinal", "item.minecraft.arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.axolotl_bucket": "Valte xchi'uk axolote", "item.minecraft.axolotl_spawn_egg": "<PERSON><PERSON><PERSON>ob axolote", "item.minecraft.baked_potato": "<PERSON><PERSON><PERSON><PERSON><PERSON> isak'", "item.minecraft.bamboo_chest_raft": "Valsa xchi'uk kaxa", "item.minecraft.bamboo_raft": "Valsa", "item.minecraft.bat_spawn_egg": "Vinaj<PERSON>ob sots'", "item.minecraft.bee_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> x<PERSON> pom", "item.minecraft.beef": "Tse bek'tal vakax", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_soup": "<PERSON><PERSON>", "item.minecraft.birch_boat": "Varko averul", "item.minecraft.birch_chest_boat": "Varko averul xchi'uk kaxa", "item.minecraft.black_bundle": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.black_dye": "<PERSON><PERSON><PERSON><PERSON> bonolil", "item.minecraft.black_harness": "<PERSON>k'al pek'", "item.minecraft.blade_pottery_shard": "Xut seramika espara", "item.minecraft.blade_pottery_sherd": "Xut seramika espara", "item.minecraft.blaze_powder": "Ts'ub jk'ok'", "item.minecraft.blaze_rod": "Te'el jk'ok'", "item.minecraft.blaze_spawn_egg": "Vinajesob jk'ok'", "item.minecraft.blue_bundle": "Yaxa<PERSON> moral", "item.minecraft.blue_dye": "Yaxal bonolil", "item.minecraft.blue_egg": "Yaxal ton alak'", "item.minecraft.blue_harness": "Yaxal pek'", "item.minecraft.bogged_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> j-a<PERSON>'el<PERSON><PERSON> k<PERSON>o", "item.minecraft.bolt_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.bolt_armor_trim_smithing_template.new": "Ornamento anjel", "item.minecraft.bone": "Bakelil", "item.minecraft.bone_meal": "<PERSON><PERSON>'ub bakelil", "item.minecraft.book": "<PERSON><PERSON>", "item.minecraft.bordure_indented_banner_pattern": "Lok'tael ta tanal-eal ti'il", "item.minecraft.bow": "Alkol", "item.minecraft.bowl": "<PERSON><PERSON>", "item.minecraft.bread": "<PERSON><PERSON><PERSON> vaj", "item.minecraft.breeze_rod": "Te'el j-ik'", "item.minecraft.breeze_spawn_egg": "<PERSON><PERSON><PERSON>ob j-ik'", "item.minecraft.brewer_pottery_shard": "<PERSON>t seramika poxil", "item.minecraft.brewer_pottery_sherd": "<PERSON>t seramika poxil", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON>", "item.minecraft.brick": "<PERSON><PERSON><PERSON>", "item.minecraft.brown_bundle": "K'anch'etan moral", "item.minecraft.brown_dye": "K'anch'etan bonolil", "item.minecraft.brown_egg": "K'anch'etan ton alak'", "item.minecraft.brown_harness": "K'anch'etan pek'", "item.minecraft.brush": "Ch'ulobil", "item.minecraft.bucket": "Valte", "item.minecraft.bundle": "Moral", "item.minecraft.bundle.empty": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty.description": "Xu' skuch jun jelbil tsobol ta k'usitik", "item.minecraft.bundle.full": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON>t seramika k'ok'", "item.minecraft.burn_pottery_sherd": "<PERSON>t seramika k'ok'", "item.minecraft.camel_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.carrot": "Sanaoria", "item.minecraft.carrot_on_a_stick": "Te' xchi'uk sanaoria", "item.minecraft.cat_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> sup", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> ch'enal om", "item.minecraft.chainmail_boots": "<PERSON><PERSON>b nuti'tak'inetik", "item.minecraft.chainmail_chestplate": "<PERSON><PERSON><PERSON> nuti'tak'in", "item.minecraft.chainmail_helmet": "<PERSON><PERSON> nuti'tak'in", "item.minecraft.chainmail_leggings": "Vex nuti'tak'in", "item.minecraft.charcoal": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cherry_boat": "Varko ch'ixtot", "item.minecraft.cherry_chest_boat": "Varko ch'ixtot xchi'uk kaxa", "item.minecraft.chest_minecart": "Vakon xchi'uk kaxa", "item.minecraft.chicken": "Tse bek'tal alak'", "item.minecraft.chicken_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>'", "item.minecraft.chorus_fruit": "Chorus sat-te'", "item.minecraft.clay_ball": "<PERSON><PERSON><PERSON> bola", "item.minecraft.clock": "K'elob-ora", "item.minecraft.coal": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.coast_armor_trim_smithing_template.new": "Ornamento ti'il", "item.minecraft.cocoa_beans": "<PERSON><PERSON>", "item.minecraft.cod": "<PERSON>se bek'tal vakalao", "item.minecraft.cod_bucket": "Valte xchi'uk vakalao", "item.minecraft.cod_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.command_block_minecart": "Vakon x<PERSON>'uk kubo mantal", "item.minecraft.compass": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "<PERSON><PERSON><PERSON> <PERSON>et", "item.minecraft.cooked_chicken": "<PERSON><PERSON><PERSON> bek'tal alak'", "item.minecraft.cooked_cod": "<PERSON><PERSON><PERSON> be<PERSON>'tal v<PERSON>o", "item.minecraft.cooked_mutton": "<PERSON><PERSON><PERSON> be<PERSON>'tal chij", "item.minecraft.cooked_porkchop": "<PERSON><PERSON><PERSON> bek'tal chitom", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON> bek'tal t'ul", "item.minecraft.cooked_salmon": "<PERSON><PERSON><PERSON> be<PERSON>'tal xalmon", "item.minecraft.cookie": "Kaye<PERSON>", "item.minecraft.copper_ingot": "<PERSON><PERSON> k'anal k'unil tak'in", "item.minecraft.cow_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "item.minecraft.creaking_spawn_egg": "<PERSON>aj<PERSON>ob jch'ak'ak'et<PERSON><PERSON>", "item.minecraft.creeper_banner_pattern": "Lok'tael yu'un vantera", "item.minecraft.creeper_banner_pattern.desc": "C<PERSON>per", "item.minecraft.creeper_banner_pattern.new": "Lok'tael ta creeper", "item.minecraft.creeper_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> creeper", "item.minecraft.crossbow": "Alkoltak'in", "item.minecraft.crossbow.projectile": "Vala:", "item.minecraft.crossbow.projectile.multiple": "Vala: %s x %s", "item.minecraft.crossbow.projectile.single": "Vala: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON>-<PERSON><PERSON> moral", "item.minecraft.cyan_dye": "<PERSON><PERSON>-<PERSON><PERSON> bono<PERSON>l", "item.minecraft.cyan_harness": "Yax-elan pek'", "item.minecraft.danger_pottery_shard": "Xut seramika xi'el", "item.minecraft.danger_pottery_sherd": "Xut seramika xi'el", "item.minecraft.dark_oak_boat": "Varko i<PERSON>'pulan tulan", "item.minecraft.dark_oak_chest_boat": "Varko ik'pulan tulan xchi'uk kaxa", "item.minecraft.debug_stick": "Te'el kusel", "item.minecraft.debug_stick.empty": "Ch'abal yu'unineltak li %se", "item.minecraft.debug_stick.select": "la st'uj ''%s'' (%s)", "item.minecraft.debug_stick.update": "''%s'' jelat ta %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON><PERSON> riamante", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_helmet": "<PERSON><PERSON> r<PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON> r<PERSON>", "item.minecraft.diamond_horse_armor": "<PERSON><PERSON><PERSON> riamante sventa ka'", "item.minecraft.diamond_leggings": "Vex riamante", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON> r<PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON> r<PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON><PERSON> riam<PERSON>", "item.minecraft.disc_fragment_5": "<PERSON><PERSON> risko", "item.minecraft.disc_fragment_5.desc": "Risko: 5", "item.minecraft.dolphin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> telpin", "item.minecraft.donkey_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> vuro", "item.minecraft.dragon_breath": "Yik' muk'ta ain", "item.minecraft.dried_kelp": "Takin alka", "item.minecraft.drowned_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> ja'al j<PERSON>mel", "item.minecraft.dune_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.dune_armor_trim_smithing_template.new": "Ornamento runa", "item.minecraft.echo_shard": "<PERSON>t ech'omal", "item.minecraft.egg": "Ton alak'", "item.minecraft.elder_guardian_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.elytra": "End-xik'etik", "item.minecraft.emerald": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.enchanted_book": "<PERSON><PERSON><PERSON> vun", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON> mantsana k'anal tak'in", "item.minecraft.end_crystal": "End-kristal", "item.minecraft.ender_dragon_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> muk'ta ain", "item.minecraft.ender_eye": "Sat ender", "item.minecraft.ender_pearl": "<PERSON><PERSON> perla", "item.minecraft.enderman_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> enderman", "item.minecraft.endermite_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> endermite", "item.minecraft.evoker_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.experience_bottle": "Limeta xchi'uk kuxlejal", "item.minecraft.explorer_pottery_shard": "<PERSON><PERSON> se<PERSON> j<PERSON>nbal", "item.minecraft.explorer_pottery_sherd": "<PERSON><PERSON> se<PERSON> j<PERSON>nbal", "item.minecraft.eye_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.eye_armor_trim_smithing_template.new": "Ornamento satil", "item.minecraft.feather": "K'uk'umil", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sat om", "item.minecraft.field_masoned_banner_pattern": "Lok'tael ta latriyoal", "item.minecraft.filled_map": "Mapa", "item.minecraft.fire_charge": "<PERSON><PERSON><PERSON> k'ok'", "item.minecraft.firework_rocket": "Sibak yolonk'ok'", "item.minecraft.firework_rocket.flight": "<PERSON><PERSON><PERSON><PERSON><PERSON> vilel:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Sibak yolok'ok'", "item.minecraft.firework_star.black": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.blue": "Ya<PERSON>l", "item.minecraft.firework_star.brown": "K'anch'etan", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.cyan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Chk'ataj ta", "item.minecraft.firework_star.flicker": "Nop'op'etel", "item.minecraft.firework_star.gray": "K'anch'etan", "item.minecraft.firework_star.green": "Ya<PERSON>l", "item.minecraft.firework_star.light_blue": "Ya<PERSON>l", "item.minecraft.firework_star.light_gray": "K'anch'etan", "item.minecraft.firework_star.lime": "Yoxyoxtik", "item.minecraft.firework_star.magenta": "<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON>", "item.minecraft.firework_star.orange": "K'onk'ontik", "item.minecraft.firework_star.pink": "<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON>", "item.minecraft.firework_star.purple": "Ik'pok'an", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Mu ojti<PERSON>bil chak k'ucha'al", "item.minecraft.firework_star.shape.burst": "T'omel", "item.minecraft.firework_star.shape.creeper": "Chak k'u<PERSON>'al creper", "item.minecraft.firework_star.shape.large_ball": "Muk'ta bola", "item.minecraft.firework_star.shape.small_ball": "Bik'it bola", "item.minecraft.firework_star.shape.star": "Chak k'u<PERSON>'al k'anal", "item.minecraft.firework_star.trail": "Av okil", "item.minecraft.firework_star.white": "Sakil", "item.minecraft.firework_star.yellow": "K'anal", "item.minecraft.fishing_rod": "<PERSON>'el tsakchoy", "item.minecraft.flint": "Suyton", "item.minecraft.flint_and_steel": "Jtsanvanej", "item.minecraft.flow_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.flow_armor_trim_smithing_template.new": "Ornamento espiral", "item.minecraft.flow_banner_pattern": "Lok'tael yu'un vantera", "item.minecraft.flow_banner_pattern.desc": "Espiral", "item.minecraft.flow_banner_pattern.new": "Lok'tael ta espiral", "item.minecraft.flow_pottery_sherd": "Xut seramika espiral", "item.minecraft.flower_banner_pattern": "Lok'tael yu'un vantera", "item.minecraft.flower_banner_pattern.desc": "Nichim", "item.minecraft.flower_banner_pattern.new": "Lok'tael ta nichim", "item.minecraft.flower_pot": "<PERSON><PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>l vet", "item.minecraft.friend_pottery_shard": "<PERSON>t seramika amikoal", "item.minecraft.friend_pottery_sherd": "<PERSON>t seramika amikoal", "item.minecraft.frog_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> pok'ok'", "item.minecraft.furnace_minecart": "Vakon xchi'uk jorno", "item.minecraft.ghast_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> ghast", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON> sat ghast", "item.minecraft.glass_bottle": "Limeta", "item.minecraft.glistering_melon_slice": "Nop'ol xet' xancha", "item.minecraft.globe_banner_pattern": "Lok'tael yu'un vantera", "item.minecraft.globe_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.globe_banner_pattern.new": "Lok'tael ta balumil", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON><PERSON> bay<PERSON><PERSON><PERSON>", "item.minecraft.glow_ink_sac": "Nop'ol moral tinta", "item.minecraft.glow_item_frame": "Nop'ol marko", "item.minecraft.glow_squid_spawn_egg": "Vinaj<PERSON>ob nop'ol muk'ta ja'al-om", "item.minecraft.glowstone_dust": "Ts'ub nop'olton", "item.minecraft.goat_horn": "<PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gold_ingot": "<PERSON><PERSON> k'anal tak'in", "item.minecraft.gold_nugget": "Bek' k'anal tak'in", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON> k'anal tak'in", "item.minecraft.golden_axe": "Ek'el k'anal tak'in", "item.minecraft.golden_boots": "Xonob k'anal tak'inetik", "item.minecraft.golden_carrot": "Sanaoria k'anal tak'in", "item.minecraft.golden_chestplate": "<PERSON><PERSON><PERSON> k'anal tak'in", "item.minecraft.golden_helmet": "<PERSON><PERSON> k'anal tak'in", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON> k'anal tak'in", "item.minecraft.golden_horse_armor": "<PERSON><PERSON><PERSON> k'anal tak'in sventa ka'", "item.minecraft.golden_leggings": "Vex k'anal tak'in", "item.minecraft.golden_pickaxe": "<PERSON><PERSON> k'anal tak'in", "item.minecraft.golden_shovel": "Pala k'anal tak'in", "item.minecraft.golden_sword": "Espara k'anal tak'in", "item.minecraft.gray_bundle": "K'anch'etan moral", "item.minecraft.gray_dye": "K'anch'etan bonolil", "item.minecraft.gray_harness": "K'anch'etan pek'", "item.minecraft.green_bundle": "Yaxa<PERSON> moral", "item.minecraft.green_dye": "Yaxal bonolil", "item.minecraft.green_harness": "Yaxal pek'", "item.minecraft.guardian_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.gunpowder": "Sibak", "item.minecraft.guster_banner_pattern": "Lok'tael yu'un vantera", "item.minecraft.guster_banner_pattern.desc": "Sutub-ik'", "item.minecraft.guster_banner_pattern.new": "Lok'tael ta sutub-ik'", "item.minecraft.guster_pottery_sherd": "<PERSON>t seramika sutub-ik'", "item.minecraft.happy_ghast_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> muy<PERSON> ghast", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Yo'onton muk'ta nab", "item.minecraft.heart_pottery_shard": "<PERSON><PERSON> seram<PERSON> o'<PERSON>nal", "item.minecraft.heart_pottery_sherd": "<PERSON><PERSON> seram<PERSON> o'<PERSON>nal", "item.minecraft.heartbreak_pottery_shard": "Xut seramika jomol o'ontonal", "item.minecraft.heartbreak_pottery_sherd": "Xut seramika jomol o'ontonal", "item.minecraft.hoglin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>n", "item.minecraft.honey_bottle": "Limeta xchi'uk ajapom", "item.minecraft.honeycomb": "<PERSON><PERSON>", "item.minecraft.hopper_minecart": "Vakon xchi'uk malobil", "item.minecraft.horse_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> ka'", "item.minecraft.host_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.host_armor_trim_smithing_template.new": "Ornamento j-ik'vanej", "item.minecraft.howl_pottery_shard": "Xut seramika me'nal-ok'el", "item.minecraft.howl_pottery_sherd": "Xut seramika me'nal-ok'el", "item.minecraft.husk_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> takin jchamel", "item.minecraft.ink_sac": "Moral tinta", "item.minecraft.iron_axe": "Ek'el", "item.minecraft.iron_boots": "Xonob tak'inetik", "item.minecraft.iron_chestplate": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "<PERSON><PERSON><PERSON>ob j<PERSON> tak'in", "item.minecraft.iron_helmet": "Kasko tak'in", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "<PERSON><PERSON><PERSON> sventa ka'", "item.minecraft.iron_ingot": "Linkote tak'in", "item.minecraft.iron_leggings": "Vex tak'in", "item.minecraft.iron_nugget": "Bek' tak'in", "item.minecraft.iron_pickaxe": "<PERSON><PERSON>", "item.minecraft.iron_shovel": "<PERSON><PERSON>", "item.minecraft.iron_sword": "Espara", "item.minecraft.item_frame": "<PERSON><PERSON>", "item.minecraft.jungle_boat": "Jabnaltikal varko", "item.minecraft.jungle_chest_boat": "Jabnaltikal varko xchi'uk kaxa", "item.minecraft.knowledge_book": "<PERSON><PERSON>'ta vun", "item.minecraft.lapis_lazuli": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lava_bucket": "Valte xchi'uk tsuk'", "item.minecraft.lead": "Ch'ojon", "item.minecraft.leather": "<PERSON><PERSON><PERSON>", "item.minecraft.leather_boots": "Nukul xonobiletik", "item.minecraft.leather_chestplate": "<PERSON><PERSON><PERSON> tunika", "item.minecraft.leather_helmet": "<PERSON><PERSON><PERSON> sna jolil", "item.minecraft.leather_horse_armor": "<PERSON><PERSON><PERSON> alma<PERSON>ra sventa ka'", "item.minecraft.leather_leggings": "Nukul vexil", "item.minecraft.light_blue_bundle": "Yaxa<PERSON> moral", "item.minecraft.light_blue_dye": "Yaxal bonolil", "item.minecraft.light_blue_harness": "Yaxal pek'", "item.minecraft.light_gray_bundle": "K'anch'etan moral", "item.minecraft.light_gray_dye": "K'anch'etan bonolil", "item.minecraft.light_gray_harness": "K'anch'etan pek'", "item.minecraft.lime_bundle": "Yoxyoxtik moral", "item.minecraft.lime_dye": "Yoxyoxtik bonolil", "item.minecraft.lime_harness": "Yaxal pek'", "item.minecraft.lingering_potion": "<PERSON><PERSON><PERSON><PERSON> poxil", "item.minecraft.lingering_potion.effect.awkward": "Mu komon jalijej poxil", "item.minecraft.lingering_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON> poxil mu xu' xmeltsaj", "item.minecraft.lingering_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON> poxil ta ts'ik k'ok'", "item.minecraft.lingering_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON> poxil ta yaintasel", "item.minecraft.lingering_potion.effect.healing": "J<PERSON>jej poxil ta poxtael", "item.minecraft.lingering_potion.effect.infested": "<PERSON><PERSON><PERSON><PERSON> poxil ta xujulal", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON> poxil ta mu vinajel ta k'elel", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON> poxil ta <PERSON>l", "item.minecraft.lingering_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON> poxil ta vilel", "item.minecraft.lingering_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON> poxil ta k'inal", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON> jali<PERSON> poxil", "item.minecraft.lingering_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON> poxil ta ak'o<PERSON> ilel", "item.minecraft.lingering_potion.effect.oozing": "<PERSON><PERSON><PERSON><PERSON> poxil ta similal", "item.minecraft.lingering_potion.effect.poison": "Jalijej chopol poxil", "item.minecraft.lingering_potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON> poxil ta cha'vina<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON> poxil ta ch'ajil lomel", "item.minecraft.lingering_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON> poxil ta ch'a<PERSON>lal", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON> poxil ta ipal", "item.minecraft.lingering_potion.effect.swiftness": "J<PERSON><PERSON>j poxil ta anilal", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON> jali<PERSON> poxil", "item.minecraft.lingering_potion.effect.turtle_master": "<PERSON><PERSON><PERSON><PERSON> poxil yu'un <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.water": "Jalijej limeta xchi'uk vo'", "item.minecraft.lingering_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON> poxil ta ich'-ik' ta vo'", "item.minecraft.lingering_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON> poxil ta k'unibel", "item.minecraft.lingering_potion.effect.weaving": "<PERSON><PERSON><PERSON><PERSON> poxil ta jalel", "item.minecraft.lingering_potion.effect.wind_charged": "<PERSON><PERSON><PERSON><PERSON> poxil ta sutub-ik'", "item.minecraft.llama_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> muk'ta chij", "item.minecraft.lodestone_compass": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "item.minecraft.mace": "<PERSON><PERSON>", "item.minecraft.magenta_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> moral", "item.minecraft.magenta_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bonolil", "item.minecraft.magenta_harness": "Tsojtsojtik pek'", "item.minecraft.magma_cream": "K'ak'al simil", "item.minecraft.magma_cube_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>'ak'al simchon", "item.minecraft.mangrove_boat": "<PERSON>arko ja'alte'", "item.minecraft.mangrove_chest_boat": "Varko ja'alte' xchi'uk kaxa", "item.minecraft.map": "<PERSON>jo<PERSON> mapa", "item.minecraft.melon_seeds": "Sbek'tak xancha", "item.minecraft.melon_slice": "Jxet' x<PERSON><PERSON>", "item.minecraft.milk_bucket": "Valte xchi'uk chu' vakax", "item.minecraft.minecart": "<PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "<PERSON><PERSON> seram<PERSON> j<PERSON>van<PERSON>", "item.minecraft.miner_pottery_sherd": "<PERSON><PERSON> seram<PERSON> j<PERSON>van<PERSON>", "item.minecraft.mojang_banner_pattern": "Lok'tael yu'un vantera", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON><PERSON>i", "item.minecraft.mojang_banner_pattern.new": "Lok'tael ta k'usi", "item.minecraft.mooshroom_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> mooshroom", "item.minecraft.mourner_pottery_shard": "<PERSON>t seram<PERSON> j<PERSON>'en", "item.minecraft.mourner_pottery_sherd": "<PERSON>t seram<PERSON> j<PERSON>'en", "item.minecraft.mule_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> mula", "item.minecraft.mushroom_stew": "Sopa moni'", "item.minecraft.music_disc_11": "<PERSON><PERSON>", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "<PERSON><PERSON>", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON>", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON>", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON>", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "<PERSON><PERSON>", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "<PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "<PERSON><PERSON>", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "<PERSON><PERSON>", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Music Disc", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON>", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "<PERSON><PERSON>", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON>", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "<PERSON><PERSON>", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON>", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON>", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "<PERSON><PERSON>", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON>", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON>", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON> bek'tal chij", "item.minecraft.name_tag": "Etiketa", "item.minecraft.nautilus_shell": "Spat navtilo", "item.minecraft.nether_brick": "<PERSON>her latriyo", "item.minecraft.nether_star": "<PERSON>her k'anal", "item.minecraft.nether_wart": "Nether ch'okte'", "item.minecraft.netherite_axe": "Ek'el neterita", "item.minecraft.netherite_boots": "Xonob neteritaetik", "item.minecraft.netherite_chestplate": "<PERSON><PERSON><PERSON> net<PERSON>ta", "item.minecraft.netherite_helmet": "<PERSON><PERSON> neterita", "item.minecraft.netherite_hoe": "<PERSON><PERSON><PERSON>ta", "item.minecraft.netherite_ingot": "Linkote neterita", "item.minecraft.netherite_leggings": "Vex neterita", "item.minecraft.netherite_pickaxe": "<PERSON><PERSON>", "item.minecraft.netherite_scrap": "Xut neterita", "item.minecraft.netherite_shovel": "<PERSON>la neterita", "item.minecraft.netherite_sword": "Espara neterita", "item.minecraft.netherite_upgrade_smithing_template": "Plantiya tak'inal", "item.minecraft.netherite_upgrade_smithing_template.new": "Le<PERSON><PERSON><PERSON><PERSON> neterita", "item.minecraft.oak_boat": "<PERSON><PERSON><PERSON> tulan", "item.minecraft.oak_chest_boat": "Varko tulan xchi'uk kaxa", "item.minecraft.ocelot_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> k'ox bolom", "item.minecraft.ominous_bottle": "Chopol limeta", "item.minecraft.ominous_trial_key": "<PERSON><PERSON> yavi' tsat<PERSON>il", "item.minecraft.orange_bundle": "K'onk'ontik moral", "item.minecraft.orange_dye": "K'onk'ontik bonolil", "item.minecraft.orange_harness": "K'onk'ontik pek'", "item.minecraft.painting": "Bonobil", "item.minecraft.pale_oak_boat": "Varko sak<PERSON>'an tulan", "item.minecraft.pale_oak_chest_boat": "Varko sakpa<PERSON>'an tulan xchi'uk kaxa", "item.minecraft.panda_spawn_egg": "Vinajesob china oso chon", "item.minecraft.paper": "<PERSON><PERSON>", "item.minecraft.parrot_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>'", "item.minecraft.phantom_membrane": "<PERSON><PERSON><PERSON><PERSON> muk'ta sots'", "item.minecraft.phantom_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> muk'ta sots'", "item.minecraft.pig_spawn_egg": "<PERSON>aj<PERSON>ob chitom", "item.minecraft.piglin_banner_pattern": "Lok'tael yu'un vantera", "item.minecraft.piglin_banner_pattern.desc": "Ni' piglin", "item.minecraft.piglin_banner_pattern.new": "Lok'tael ta ni' piglin", "item.minecraft.piglin_brute_spawn_egg": "Vinajesob xi'el piglin", "item.minecraft.piglin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> piglin", "item.minecraft.pillager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON>be", "item.minecraft.pink_bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> moral", "item.minecraft.pink_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bonolil", "item.minecraft.pink_harness": "Tsojtsojtik pek'", "item.minecraft.pitcher_plant": "Binalte'", "item.minecraft.pitcher_pod": "Ts'utuj bin<PERSON>'", "item.minecraft.plenty_pottery_shard": "<PERSON>t seramika xe'elal", "item.minecraft.plenty_pottery_sherd": "<PERSON>t seramika xe'elal", "item.minecraft.poisonous_potato": "<PERSON><PERSON><PERSON> is<PERSON>'", "item.minecraft.polar_bear_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> sakil oso chon", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON><PERSON> sat choruste'", "item.minecraft.porkchop": "Tse bek'tal chitom", "item.minecraft.potato": "Isak'", "item.minecraft.potion": "Poxil", "item.minecraft.potion.effect.awkward": "Mu komon poxil", "item.minecraft.potion.effect.empty": "Poxil mu xu' xmeltsaj", "item.minecraft.potion.effect.fire_resistance": "Poxil ta ts'ik k'ok'", "item.minecraft.potion.effect.harming": "Poxil ta yaintasel", "item.minecraft.potion.effect.healing": "Poxil ta poxtael", "item.minecraft.potion.effect.infested": "Poxil ta xujulal", "item.minecraft.potion.effect.invisibility": "Poxil ta mu vinajel ta k'elel", "item.minecraft.potion.effect.leaping": "Poxil ta bitel", "item.minecraft.potion.effect.levitation": "Poxil ta vilel", "item.minecraft.potion.effect.luck": "Poxil ta k'inal", "item.minecraft.potion.effect.mundane": "Chopol poxil", "item.minecraft.potion.effect.night_vision": "Poxil ta ak'obal ilel", "item.minecraft.potion.effect.oozing": "Poxil ta similal", "item.minecraft.potion.effect.poison": "<PERSON>mebal poxil", "item.minecraft.potion.effect.regeneration": "Poxil ta cha'vinajesel", "item.minecraft.potion.effect.slow_falling": "Poxil ta ch'ajil lomel", "item.minecraft.potion.effect.slowness": "Poxil ta ch'ajilal", "item.minecraft.potion.effect.strength": "Poxil ta ipal", "item.minecraft.potion.effect.swiftness": "Poxil ta anilal", "item.minecraft.potion.effect.thick": "Tot poxil", "item.minecraft.potion.effect.turtle_master": "Poxil yu'un <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water": "Limeta xchi'uk vo'", "item.minecraft.potion.effect.water_breathing": "Poxil ta ich'-ik' ta vo'", "item.minecraft.potion.effect.weakness": "Poxil ta k'unibel", "item.minecraft.potion.effect.weaving": "Poxil ta jalel", "item.minecraft.potion.effect.wind_charged": "Poxil ta sutub-ik'", "item.minecraft.pottery_shard_archer": "<PERSON>t seramika j-alkol", "item.minecraft.pottery_shard_arms_up": "<PERSON><PERSON> seramika axinal", "item.minecraft.pottery_shard_prize": "Xut seramika me'tak'in", "item.minecraft.pottery_shard_skull": "<PERSON><PERSON> seramika bakil jolil", "item.minecraft.powder_snow_bucket": "Valte xchi'uk p'up' taiv", "item.minecraft.prismarine_crystals": "<PERSON><PERSON> p<PERSON>", "item.minecraft.prismarine_shard": "Xut prismarina", "item.minecraft.prize_pottery_shard": "Xut seramika me'tak'in", "item.minecraft.prize_pottery_sherd": "Xut seramika me'tak'in", "item.minecraft.pufferfish": "Ch'ixal choy", "item.minecraft.pufferfish_bucket": "Valte xchi'uk ch'ixal choy", "item.minecraft.pufferfish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> ch'ixal choy", "item.minecraft.pumpkin_pie": "Pastel ch'um", "item.minecraft.pumpkin_seeds": "Sbek'tak ch'um", "item.minecraft.purple_bundle": "Ik'pok'an moral", "item.minecraft.purple_dye": "Ik'pok'an bonolil", "item.minecraft.purple_harness": "Ik'pok'an pek'", "item.minecraft.quartz": "Nether kuarso", "item.minecraft.rabbit": "Tse bik'tal t'ul", "item.minecraft.rabbit_foot": "Yok t'ul", "item.minecraft.rabbit_hide": "<PERSON><PERSON><PERSON> t'ul", "item.minecraft.rabbit_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> t'ul", "item.minecraft.rabbit_stew": "Sopa t'ul", "item.minecraft.raiser_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.raiser_armor_trim_smithing_template.new": "Ornamento toyesel", "item.minecraft.ravager_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.raw_copper": "Lumil k'anal k'unil tak'in", "item.minecraft.raw_gold": "Lumil k'anal tak'in", "item.minecraft.raw_iron": "Lumil sakil tak'in", "item.minecraft.recovery_compass": "<PERSON><PERSON><PERSON><PERSON> cha'sa'el", "item.minecraft.red_bundle": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.red_dye": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>l", "item.minecraft.red_harness": "<PERSON><PERSON><PERSON><PERSON> pek'", "item.minecraft.redstone": "Ts'ub redstone", "item.minecraft.resin_brick": "Latriyo xuchꞌ", "item.minecraft.resin_clump": "<PERSON><PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.rib_armor_trim_smithing_template.new": "Ornamento ch'ilte'", "item.minecraft.rotten_flesh": "<PERSON>'a'al bek'talil", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "<PERSON><PERSON> bek'tal xalmon", "item.minecraft.salmon_bucket": "Valte xchi'uk xalmon", "item.minecraft.salmon_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.scrape_pottery_sherd": "<PERSON>t seramika jot'el", "item.minecraft.scute": "<PERSON><PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "Palantiya tak'inal", "item.minecraft.sentry_armor_trim_smithing_template.new": "Ornamento jk'el-osil", "item.minecraft.shaper_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.shaper_armor_trim_smithing_template.new": "Ornamento morelarol", "item.minecraft.sheaf_pottery_shard": "<PERSON>t seramika kaxlan ixim", "item.minecraft.sheaf_pottery_sherd": "<PERSON>t seramika kaxlan ixim", "item.minecraft.shears": "Texerex", "item.minecraft.sheep_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> chij", "item.minecraft.shelter_pottery_shard": "<PERSON>t seramika tsatsal na", "item.minecraft.shelter_pottery_sherd": "<PERSON>t seramika tsatsal na", "item.minecraft.shield": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON><PERSON> es<PERSON>ro", "item.minecraft.shield.blue": "<PERSON><PERSON><PERSON> es<PERSON>ro", "item.minecraft.shield.brown": "K'anch'etan eskuro", "item.minecraft.shield.cyan": "<PERSON><PERSON>-<PERSON><PERSON> es<PERSON>", "item.minecraft.shield.gray": "K'anch'etan eskuro", "item.minecraft.shield.green": "<PERSON><PERSON><PERSON> es<PERSON>ro", "item.minecraft.shield.light_blue": "<PERSON><PERSON><PERSON> es<PERSON>ro", "item.minecraft.shield.light_gray": "K'anch'etan eskuro", "item.minecraft.shield.lime": "Yoxyoxtik eskuro", "item.minecraft.shield.magenta": "Tsojtsojtik eskuro", "item.minecraft.shield.orange": "K'onk'ontik eskuro", "item.minecraft.shield.pink": "Tsojtsojtik eskuro", "item.minecraft.shield.purple": "Ik'pok'an eskuro", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.shield.white": "Sakil eskuro", "item.minecraft.shield.yellow": "<PERSON>'<PERSON> eskuro", "item.minecraft.shulker_shell": "<PERSON><PERSON> shulker", "item.minecraft.shulker_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.sign": "Let<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.silence_armor_trim_smithing_template.new": "Ornamento ch'inetel", "item.minecraft.silverfish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skeleton_horse_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> jich'il ka'", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern": "Lok'tael yu'un vantera", "item.minecraft.skull_banner_pattern.desc": "Bakil jolil", "item.minecraft.skull_banner_pattern.new": "Lok'tael ta bakil jolil", "item.minecraft.skull_pottery_shard": "<PERSON><PERSON> seramika bakil jolil", "item.minecraft.skull_pottery_sherd": "<PERSON><PERSON> seramika bakil jolil", "item.minecraft.slime_ball": "<PERSON><PERSON> simil", "item.minecraft.slime_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>n", "item.minecraft.smithing_template": "Plantiya tak'inal", "item.minecraft.smithing_template.applies_to": "Chak'be:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Ak'o jbej linkote o kristal", "item.minecraft.smithing_template.armor_trim.applies_to": "Almarura", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Ak'o jk'os almarura", "item.minecraft.smithing_template.armor_trim.ingredients": "Linkoteetik xchi'uk kristaletik", "item.minecraft.smithing_template.ingredients": "Skapetik:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Ak'o jbej linkote neterita", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "K'usitik pasbil ta riamante", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Ak'o jlik alma<PERSON>ra o jtel abtejebal pasbil ta riamante", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Linkote neterita", "item.minecraft.smithing_template.upgrade": "Tslekubtas: ", "item.minecraft.sniffer_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.snort_pottery_shard": "<PERSON>t seram<PERSON> j-u<PERSON>'i<PERSON>", "item.minecraft.snort_pottery_sherd": "<PERSON>t seram<PERSON> j-u<PERSON>'i<PERSON>", "item.minecraft.snout_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.snout_armor_trim_smithing_template.new": "Ornamento eal", "item.minecraft.snow_golem_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.snowball": "Bola taiv", "item.minecraft.spectral_arrow": "Nop'ol yolob", "item.minecraft.spider_eye": "Sat om", "item.minecraft.spider_spawn_egg": "Vinajesob om", "item.minecraft.spire_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.spire_armor_trim_smithing_template.new": "Ornamento akuxa", "item.minecraft.splash_potion": "Jtenbail poxil", "item.minecraft.splash_potion.effect.awkward": "Mu komon jtenbail poxil", "item.minecraft.splash_potion.effect.empty": "Jtenbail poxil mu xu' xmeltsaj", "item.minecraft.splash_potion.effect.fire_resistance": "Jtenbail poxil ta ts'ik k'ok'", "item.minecraft.splash_potion.effect.harming": "Jtenbail poxil ta yaintasel", "item.minecraft.splash_potion.effect.healing": "Jtenbail poxil ta poxtael", "item.minecraft.splash_potion.effect.infested": "Jtenbail poxil ta xujulal", "item.minecraft.splash_potion.effect.invisibility": "Jtenbail poxil ta mu vinajel ta k'elel", "item.minecraft.splash_potion.effect.leaping": "Jtenbail poxil ta bitel", "item.minecraft.splash_potion.effect.levitation": "Jtenbail poxil ta vilel", "item.minecraft.splash_potion.effect.luck": "Jtenbail poxil ta k'inal", "item.minecraft.splash_potion.effect.mundane": "Chopol jtenbail poxil", "item.minecraft.splash_potion.effect.night_vision": "Jtenbail poxil ta ak'obal ilel", "item.minecraft.splash_potion.effect.oozing": "Jtenbail poxil ta similal", "item.minecraft.splash_potion.effect.poison": "Jtenbail chamebal poxil", "item.minecraft.splash_potion.effect.regeneration": "Jtenbail poxil ta cha'vinajesel", "item.minecraft.splash_potion.effect.slow_falling": "Jtenbail poxil ta ch'ajil lomel", "item.minecraft.splash_potion.effect.slowness": "Jtenbail poxil ta ch'a<PERSON>lal", "item.minecraft.splash_potion.effect.strength": "Jtenbail poxil ta ipal", "item.minecraft.splash_potion.effect.swiftness": "Jtenbail poxil ta anilal", "item.minecraft.splash_potion.effect.thick": "Jtenbail tot poxil", "item.minecraft.splash_potion.effect.turtle_master": "Jtenbail poxil yu'un <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.water": "Jtenbail limeta xchi'uk vo'", "item.minecraft.splash_potion.effect.water_breathing": "Jtenbail poxil ta ich'-ik' ta vo'", "item.minecraft.splash_potion.effect.weakness": "Jtenbail poxil ta k'unibel", "item.minecraft.splash_potion.effect.weaving": "Jtenbail poxil ta jalel", "item.minecraft.splash_potion.effect.wind_charged": "Jtenbail poxil ta sutub-ik'", "item.minecraft.spruce_boat": "<PERSON><PERSON><PERSON> toj", "item.minecraft.spruce_chest_boat": "Varko toj xchi'uk kaxa", "item.minecraft.spyglass": "Katalejo", "item.minecraft.squid_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> muk'ta ja'al-om", "item.minecraft.stick": "Te'", "item.minecraft.stone_axe": "Ek'el ton", "item.minecraft.stone_hoe": "Asaron ton", "item.minecraft.stone_pickaxe": "<PERSON><PERSON>", "item.minecraft.stone_shovel": "Pala ton", "item.minecraft.stone_sword": "Espara ton", "item.minecraft.stray_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.strider_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON> tsuk'", "item.minecraft.string": "No", "item.minecraft.sugar": "Askal", "item.minecraft.suspicious_stew": "<PERSON><PERSON> sopa", "item.minecraft.sweet_berries": "<PERSON><PERSON><PERSON> bayaetik", "item.minecraft.tadpole_bucket": "Valte xchi'uk avob", "item.minecraft.tadpole_spawn_egg": "<PERSON><PERSON><PERSON>ob avob", "item.minecraft.tide_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.tide_armor_trim_smithing_template.new": "Ornamento balak'vo'", "item.minecraft.tipped_arrow": "Lekubtasbil yolob", "item.minecraft.tipped_arrow.effect.awkward": "Lekubtasbil yolob", "item.minecraft.tipped_arrow.effect.empty": "Lekubtas<PERSON> yolob mu xu' xmeltsaj", "item.minecraft.tipped_arrow.effect.fire_resistance": "Yolob ta ts'ik k'ok'", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON> ta yaintasel", "item.minecraft.tipped_arrow.effect.healing": "Yolob ta poxtael", "item.minecraft.tipped_arrow.effect.infested": "<PERSON><PERSON><PERSON> ta xujulal", "item.minecraft.tipped_arrow.effect.invisibility": "<PERSON><PERSON><PERSON> ta mu vinajel ta k'elel", "item.minecraft.tipped_arrow.effect.leaping": "Yolob ta bitel", "item.minecraft.tipped_arrow.effect.levitation": "Yolob ta vilel", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON><PERSON> ta k'inal", "item.minecraft.tipped_arrow.effect.mundane": "Lekubtasbil yolob", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON><PERSON> ta ak'obal ilel", "item.minecraft.tipped_arrow.effect.oozing": "<PERSON><PERSON><PERSON> ta similal", "item.minecraft.tipped_arrow.effect.poison": "Yolob ta chamebal poxil", "item.minecraft.tipped_arrow.effect.regeneration": "Yo<PERSON><PERSON> ta cha'vinajesel", "item.minecraft.tipped_arrow.effect.slow_falling": "Yolob ta ch'ajil lomel", "item.minecraft.tipped_arrow.effect.slowness": "<PERSON><PERSON><PERSON> ta <PERSON>'a<PERSON>lal", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON><PERSON> ta ipal", "item.minecraft.tipped_arrow.effect.swiftness": "Yolob ta anilal", "item.minecraft.tipped_arrow.effect.thick": "Lekubtasbil yolob", "item.minecraft.tipped_arrow.effect.turtle_master": "<PERSON><PERSON><PERSON> yu'<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.water": "Jts'ites<PERSON><PERSON> yolob", "item.minecraft.tipped_arrow.effect.water_breathing": "Yolob ta ich'-ik' ta vo'", "item.minecraft.tipped_arrow.effect.weakness": "Yolob ta k'unibel", "item.minecraft.tipped_arrow.effect.weaving": "Yolob ta jalel", "item.minecraft.tipped_arrow.effect.wind_charged": "Yolob ta sutub-ik'", "item.minecraft.tnt_minecart": "Vakon x<PERSON>'uk r<PERSON><PERSON>a", "item.minecraft.torchflower_seeds": "S<PERSON>'tak nop'olnichim", "item.minecraft.totem_of_undying": "Totem ta mu lajelal", "item.minecraft.trader_llama_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> muk'ta chij yu'un jchonolajel", "item.minecraft.trial_key": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "item.minecraft.trident": "Trirente", "item.minecraft.tropical_fish": "Jabnaltikal choy", "item.minecraft.tropical_fish_bucket": "Valte xchi'uk jabnaltikal choy", "item.minecraft.tropical_fish_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> jab<PERSON><PERSON> choy", "item.minecraft.turtle_helmet": "Spat ok", "item.minecraft.turtle_scute": "Smi<PERSON>'al ok", "item.minecraft.turtle_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> ok", "item.minecraft.vex_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.vex_armor_trim_smithing_template.new": "Ornamento jsa'sunvanej", "item.minecraft.vex_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.villager_spawn_egg": "Vinajesob jbik'it lum", "item.minecraft.vindicator_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wandering_trader_spawn_egg": "Vinajesob va'vunajel jchonolajel", "item.minecraft.ward_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.ward_armor_trim_smithing_template.new": "Ornamento jchabivanej", "item.minecraft.warden_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>en", "item.minecraft.warped_fungus_on_a_stick": "Te' xchi'uk ts'otol moni'", "item.minecraft.water_bucket": "Valte xchi'uk vo'", "item.minecraft.wayfinder_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Ornamento jsa'be", "item.minecraft.wheat": "Ka<PERSON>lan ixim", "item.minecraft.wheat_seeds": "Sbek'tak kaxlan ixim", "item.minecraft.white_bundle": "Sakil moral", "item.minecraft.white_dye": "<PERSON><PERSON>l bonolil", "item.minecraft.white_harness": "Sakil pek'", "item.minecraft.wild_armor_trim_smithing_template": "Plantiya tak'inal", "item.minecraft.wild_armor_trim_smithing_template.new": "Te'tikal ornamento", "item.minecraft.wind_charge": "<PERSON>jel i<PERSON>'", "item.minecraft.witch_spawn_egg": "Vinajesob chopol ants", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON> yu'un <PERSON>er", "item.minecraft.wither_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.wolf_armor": "<PERSON><PERSON><PERSON> sventa ok'il", "item.minecraft.wolf_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>'il", "item.minecraft.wooden_axe": "Ek'el te'", "item.minecraft.wooden_hoe": "<PERSON><PERSON>n te'", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON> te'", "item.minecraft.wooden_shovel": "Pala te'", "item.minecraft.wooden_sword": "Espara te'", "item.minecraft.writable_book": "<PERSON>un xchi'uk k'uk'umil", "item.minecraft.written_book": "Ts'ibtabil vun", "item.minecraft.yellow_bundle": "K'anal moral", "item.minecraft.yellow_dye": "K'anal bonolil", "item.minecraft.yellow_harness": "K'anal pek'", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>lin", "item.minecraft.zombie_horse_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> ka'", "item.minecraft.zombie_spawn_egg": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.zombie_villager_spawn_egg": "<PERSON><PERSON><PERSON>ob ipajesbil jbik'it lum", "item.minecraft.zombified_piglin_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> piglin", "item.modifiers.any": "K'alal ch-ak'at:", "item.modifiers.armor": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>:", "item.modifiers.body": "K'alal ch-ak'at:", "item.modifiers.chest": "Ta sba chu'il:", "item.modifiers.feet": "Ta akaniletik:", "item.modifiers.hand": "Ta k'obil:", "item.modifiers.head": "Ta jolil:", "item.modifiers.legs": "Ta o'iletik:", "item.modifiers.mainhand": "Ta bats'ik'obil:", "item.modifiers.offhand": "Ta ts'et k'obil:", "item.modifiers.saddle": "Ta xila:", "item.nbt_tags": "NBT: %s etiketa", "item.op_block_warning.line1": "<PERSON><PERSON><PERSON> me:", "item.op_block_warning.line2": "Mi xtunesat li item li'e, tsm<PERSON><PERSON>an nan jun mantal", "item.op_block_warning.line3": "¡Mu xatunes mi mu chana' li melel konteniroe!", "item.unbreakable": "<PERSON> xu' sok", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON>", "itemGroup.coloredBlocks": "Bonoliletik", "itemGroup.combat": "Pask'op", "itemGroup.consumables": "Xu' <PERSON><PERSON><PERSON><PERSON>", "itemGroup.crafting": "Mel<PERSON><PERSON><PERSON>", "itemGroup.foodAndDrink": "Ve'lil xchi'uk uch'bajel", "itemGroup.functional": "Boloke tunelaletik", "itemGroup.hotbar": "<PERSON><PERSON><PERSON><PERSON><PERSON> bara k'usitik", "itemGroup.ingredients": "Materialetik", "itemGroup.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.natural": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemGroup.op": "Yabtejebtak jchapanvanej", "itemGroup.redstone": "Redstone", "itemGroup.search": "Ta sa' k'usitik", "itemGroup.spawnEggs": "<PERSON><PERSON><PERSON>", "itemGroup.tools": "Abtejebaletik", "item_modifier.unknown": "Mu ojtikinbil jel-item: %s", "jigsaw_block.final_state": "Chk'ataj ta:", "jigsaw_block.generate": "Tsvinajes", "jigsaw_block.joint.aligned": "<PERSON>j<PERSON>", "jigsaw_block.joint.rollable": "<PERSON><PERSON> <PERSON><PERSON><PERSON>'uj", "jigsaw_block.joint_label": "Tos ts'akel:", "jigsaw_block.keep_jigsaws": "Tsmak'l<PERSON> j<PERSON>'<PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.levels": "Niveletik: %s", "jigsaw_block.name": "Biil:", "jigsaw_block.placement_priority": "Ak'el:", "jigsaw_block.placement_priority.tooltip": "K'alal ta sts'ak sba ta jk'os li kubo j<PERSON>'<PERSON><PERSON><PERSON><PERSON>, li' ja' li chole tunesbil yu'un k'os sventa chchapaj sventa ts'akeletik ta mas jamal estrukturaetik.\n\nChchol sbaik ta ak'ol olon to li k'o<PERSON><PERSON>, tsna' no'ox k'uxi la sts'ak sbaik.", "jigsaw_block.pool": "<PERSON><PERSON><PERSON><PERSON> chop:", "jigsaw_block.selection_priority": "T'ujel:", "jigsaw_block.selection_priority.tooltip": "<PERSON>'al<PERSON> chchapaj li k'os ts'akanbil ta ts'akeletike, li' ja' li chole tunesbil yu'un kubo jk'asjolil sventa sts'ak sba ta k'anbil k'os.\n\nChchol sbaik ta ak'ol olon to li kubo jk'as<PERSON><PERSON><PERSON><PERSON>, mu chak' sjol k'uxi la sts'ak sbaik.", "jigsaw_block.target": "Sbi objetivo:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON> (sonal kaxa)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Porokresoetik", "key.attack": "Tsmaj/Tslilin", "key.back": "Chxanav ta pat", "key.categories.creative": "<PERSON><PERSON>", "key.categories.gameplay": "Spaseltak jtajimol", "key.categories.inventory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.categories.misc": "<PERSON><PERSON>", "key.categories.movement": "Bak'el", "key.categories.multiplayer": "Ta<PERSON><PERSON><PERSON>", "key.categories.ui": "Interpas yu'un tajimol", "key.chat": "<PERSON><PERSON><PERSON><PERSON> lo'il", "key.command": "Ts<PERSON>m mantal ta lo'il", "key.drop": "<PERSON><PERSON><PERSON><PERSON> t'ujbil item", "key.forward": "Chxanav ta ba be", "key.fullscreen": "Ts'akal pantaya", "key.hotbar.1": "Avil 1", "key.hotbar.2": "Avil 2", "key.hotbar.3": "Avil 3", "key.hotbar.4": "Avil 4", "key.hotbar.5": "Avil 5", "key.hotbar.6": "Avil 6", "key.hotbar.7": "Avil 7", "key.hotbar.8": "Avil 8", "key.hotbar.9": "Avil 9", "key.inventory": "Tsjam/Tsmak k'ejebal", "key.jump": "Chbit", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Sutel", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON>", "key.keyboard.comma": ",", "key.keyboard.delete": "Tstup'", "key.keyboard.down": "<PERSON><PERSON> yolob", "key.keyboard.end": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.enter": "Enter", "key.keyboard.equal": "=", "key.keyboard.escape": "Eskape", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "<PERSON><PERSON>", "key.keyboard.insert": "Tstik'", "key.keyboard.keypad.0": "0 (TN)", "key.keyboard.keypad.1": "1 (TN)", "key.keyboard.keypad.2": "2 (TN)", "key.keyboard.keypad.3": "3 (TN)", "key.keyboard.keypad.4": "4 (TN)", "key.keyboard.keypad.5": "5 (TN)", "key.keyboard.keypad.6": "6 (TN)", "key.keyboard.keypad.7": "7 (TN)", "key.keyboard.keypad.8": "8 (TN)", "key.keyboard.keypad.9": "9 (TN)", "key.keyboard.keypad.add": "+ (TN)", "key.keyboard.keypad.decimal": ". (TN)", "key.keyboard.keypad.divide": "/ (TN)", "key.keyboard.keypad.enter": "Enter (TN)", "key.keyboard.keypad.equal": "= (TN)", "key.keyboard.keypad.multiply": "* (TN)", "key.keyboard.keypad.subtract": "- (TN)", "key.keyboard.left": "Ts'et yolob", "key.keyboard.left.alt": "Ts'et Alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Ts'et kontrol", "key.keyboard.left.shift": "Ts'et muk'ta xot", "key.keyboard.left.win": "Ts'et Win", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Blo<PERSON>", "key.keyboard.page.down": "Av P<PERSON>g", "key.keyboard.page.up": "<PERSON>", "key.keyboard.pause": "<PERSON><PERSON><PERSON>", "key.keyboard.period": ".", "key.keyboard.print.screen": "Tslok'ta pantaya", "key.keyboard.right": "Bat<PERSON>'i yolob", "key.keyboard.right.alt": "Bats'i Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Bats'i kontrol", "key.keyboard.right.shift": "Bats'i muk'ta xot", "key.keyboard.right.win": "Bats'i Win", "key.keyboard.scroll.lock": "Bloq Despl", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "Avil", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Mu xchi'uk chop", "key.keyboard.up": "Ak'ol yolob", "key.keyboard.world.1": "Balumil 1", "key.keyboard.world.2": "Balumil 2", "key.left": "Chxanav ta ts'et", "key.loadToolbarActivator": "Tsvinajes bara k'usitik", "key.mouse": "Voton %1$s", "key.mouse.left": "Ts'et voton", "key.mouse.middle": "Voton ta o'lol", "key.mouse.right": "<PERSON><PERSON>'i voton", "key.pickItem": "Tslok'ta kubo", "key.playerlist": "Lista jtajimol", "key.quickActions": "<PERSON><PERSON>", "key.right": "Chxanav ta bats'i", "key.saveToolbarActivator": "T<PERSON>'ej bara k'usitik", "key.screenshot": "Tslok'ta pantaya", "key.smoothCamera": "<PERSON>'o<PERSON>il bak'el", "key.sneak": "Cht'ini", "key.socialInteractions": "Pantaya <PERSON>", "key.spectatorOutlines": "Chak'bik senyail jtajimoletik (jk'elvanejetik)", "key.sprint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.swapOffhand": "Tsvuy item ta ts'et k'obil", "key.togglePerspective": "<PERSON><PERSON><PERSON><PERSON> li il<PERSON>le", "key.use": "Tstunes item/Chak' kubo", "known_server_link.announcements": "Aleletik", "known_server_link.community": "<PERSON><PERSON>", "known_server_link.community_guidelines": "Sleytak komon osil", "known_server_link.feedback": "Lo'iletik", "known_server_link.forums": "Joroetik", "known_server_link.news": "Lo'iletik", "known_server_link.report_bug": "Chal smul servilor", "known_server_link.status": "Estaro", "known_server_link.support": "<PERSON><PERSON><PERSON>", "known_server_link.website": "Pajina internet", "lanServer.otherPlayers": "<PERSON><PERSON>el sventa yantik jtajimoletik", "lanServer.port": "Atolal puerto", "lanServer.port.invalid": "Chopol puerto.\nAk'o pojol li kasiya ts'ibe o ak'o jun atolal ta o'lol 1024 xchi'uk 65535.", "lanServer.port.invalid.new": "Chopol puerto.\nAk'o pojol li kasiya ts'ibe o ak'o jun atolal ta o'lol %s xchi'uk %s.", "lanServer.port.unavailable": "Mu xu' xtunesat li puertoe.\nAk'o pojol li kasiya ts'ibe o ak'o jun atolal ta o'lol 1024 xchi'uk 65535.", "lanServer.port.unavailable.new": "Mu xu' xtunesat li puertoe.\nAk'o pojol li kasiya ts'ibe o ak'o jun atolal ta o'lol %s xchi'uk %s.", "lanServer.scanning": "Yakal ta sa' balumiletik ta LAN", "lanServer.start": "Tsjam balumil ta LAN", "lanServer.title": "Balumil ta LAN", "language.code": "tzo_MX", "language.name": "Bats'i k'op", "language.region": "<PERSON><PERSON>", "lectern.take_book": "Tslok'es vun", "loading.progress": "%s%%", "mco.account.privacy.info": "Mas chano ta Mojang xchi'uk ley mukulaletik", "mco.account.privacy.info.button": "Mas alelal ta RGDP", "mco.account.privacy.information": "Chak' jayibuk mantaletik sventa skolta ta stuk'ulanel ololetik xchi'uk smukulalik li Mojang-e; ja' yu'un, chch'un smantal Ley ta Stuk'ulanel Smukulalik Ololetik ta Internet (COPPA ta inkles k'op) xchi'uk Komon Mantalal ta Stuk'ulanel Alelal (RGPD ta kaxlan k'op).\n\nSk'an nan chak'bot ak'el li atot ame'e ta ba'i sventa xu' chavich' li akuentae yu'un Realms.", "mco.account.privacyinfo": "Chak' jayibuk mantaletik sventa skolta ta stuk'ulanel ololetik xchi'uk smukulalik li Mojang-e k'alal chch'un smantal Ley ta Stuk'ulanel Smukulalik Ololetik ta Internet (COPPA ta inkles k'op) xchi'uk Komon Mantalal ta Stuk'ulanel <PERSON>elal (RGPD ta kaxlan k'op).\n\nSk'an nan chak'bot ak'el li atot ame'e ta ba'i sventa xu' xavich' li akuentae yu'un Realms.\n\nMi oy to apoko' kuentae yu'un Minecraft (sventa chalikes sesion sk'an chatunes jun biilal jtunesvanej, mu'yuk jun koreo), sk'an chavuy li akuentae ta jun kuenta yu'un Mojang sventa chavich' Realms.", "mco.account.update": "<PERSON><PERSON><PERSON><PERSON> kuenta", "mco.activity.noactivity": "Ch'abal paseletik k'u sjalil %s k'ak'al", "mco.activity.title": "Spaselik jtajimoletik", "mco.backup.button.download": "Tsyales li slajebe", "mco.backup.button.reset": "<PERSON><PERSON>'likes balumil", "mco.backup.button.restore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.backup.button.upload": "<PERSON><PERSON><PERSON> bal<PERSON>", "mco.backup.changes.tooltip": "Jeleletik", "mco.backup.entry": "Lok'tael (%s)", "mco.backup.entry.description": "<PERSON><PERSON>", "mco.backup.entry.enabledPack": "Tsakal pakete(etik)", "mco.backup.entry.gameDifficulty": "Tsatsalil", "mco.backup.entry.gameMode": "<PERSON><PERSON> ta<PERSON>", "mco.backup.entry.gameServerVersion": "Sversion servilor tajimol", "mco.backup.entry.name": "Biil", "mco.backup.entry.seed": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.templateName": "Sbi palantiya", "mco.backup.entry.undefined": "Mu bajbil jelel", "mco.backup.entry.uploaded": "<PERSON><PERSON><PERSON>", "mco.backup.entry.worldType": "<PERSON><PERSON> b<PERSON>l", "mco.backup.generate.world": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "mco.backup.info.title": "Jeleletik yu'un slajeb lok'tael", "mco.backup.narration": "Lok'tael yu'un %s", "mco.backup.nobackups": "Ch'abal slok'taeltak li Realm li'e.", "mco.backup.restoring": "Yakal chcha'meltsan <PERSON>", "mco.backup.unknown": "MU OJTIKINBIL", "mco.brokenworld.download": "Tsyales", "mco.brokenworld.downloaded": "Yalesbil", "mco.brokenworld.message.line1": "<PERSON>'<PERSON>o li balumile o t'ujo yan, a<PERSON><PERSON><PERSON>.", "mco.brokenworld.message.line2": "<PERSON>' <PERSON><PERSON><PERSON> li balumile sventa smanera jun no'ox jtajimol ek.", "mco.brokenworld.minigame.title": "Mu xa ch'unat li bik'it tajimol li'e", "mco.brokenworld.nonowner.error": "<PERSON><PERSON> ja'to chcha'likes s<PERSON><PERSON><PERSON> li aj<PERSON>, a<PERSON><PERSON><PERSON>", "mco.brokenworld.nonowner.title": "Mu ja' ach'ubtasbil li balumile", "mco.brokenworld.play": "Chtajin", "mco.brokenworld.reset": "<PERSON><PERSON>'likes", "mco.brokenworld.title": "Mu xa xch'unat li abalumil avie", "mco.client.incompatible.msg.line1": "Mu chts'ikat yu'un Realms li akilientee.", "mco.client.incompatible.msg.line2": "<PERSON><PERSON>o li mas ach' versione yu'un Minecraft, avokoluk.", "mco.client.incompatible.msg.line3": "Realms mu chts'ikat yu'un prevaetik.", "mco.client.incompatible.title": "¡Mu t<PERSON>'i<PERSON> kiliente!", "mco.client.outdated.stable.version": "Mu xch'unat yu'un Realms li sversion akilientee (%s).\n\nTuneso li slajeb versione yu'un Minecraft, avokoluk.", "mco.client.unsupported.snapshot.version": "Mu xch'unat yu'un Realms li sversion akilientee (%s).\n\nRealms mu xu' xtunesat li ta preva li'e.", "mco.compatibility.downgrade": "Tsmalubtas", "mco.compatibility.downgrade.description": "Latajin ta slajeb velta li balumil li'e ta ach'ubtasel %s, te oyot ta ach'ubtasel %s. Mi chamalubtas jun balumil ja' tspas nan sokel. Mu xu' chkalkutik ta xvinaj o ta x-abtej.\n\nLa sk'ej sba ta ''Lok'taeletik'' li jun slok'tael abalumile. Cha'meltsano li abalumil mi sk'ane, avokoluk.", "mco.compatibility.incompatible.popup.title": "Mu ts'ikbil version", "mco.compatibility.incompatible.releaseType.popup.message": "<PERSON> chts'ikat yu'un sversion atajimol li balumile chak'an chats'ak aba.", "mco.compatibility.incompatible.series.popup.message": "Latajin ta slajeb velta li balumil li'e ta version %s xchi'uk avu'un ja' %s.\n\nMu sts'ik sbaik li serieetik li'e. Sk'an jun ach' balumil sventa xu' chatajin li ta version li'e.", "mco.compatibility.unverifiable.message": "Li versione latajin ta slajeb velta li balumil li'e mu xu' xk'elanat. Mi chavach'ubtas o chamalubtas li balumile, ta slok'ta sba xchi'uk ta sk'ej sba ta ''Lok'taeletik''.", "mco.compatibility.unverifiable.title": "Mu xu' xk'elanat li ch'unele", "mco.compatibility.upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.compatibility.upgrade.description": "Latajin ta slajeb velta li balumil li'e ta version %s, yakal chatunes %s.\n\nTa sk'ej sba ta ''Lok'taeletik'' li jun slok'tael abalumile. Cha'meltsano li abalumil mi sk'ane.", "mco.compatibility.upgrade.friend.description": "Latajin ta slajeb velta li balumil li'e ta version %s, yakal chatunes %s.\n\nTa sk'ej sba ta ''Lok'taeletik'' li jun slok'tael balumile. \n\nXu' smeltsan balumil mi sk'ane li yaj<PERSON>.", "mco.compatibility.upgrade.title": "¿Mi melel chak'an chavach'ubtas li balumile?", "mco.configure.current.minigame": "Ta tana", "mco.configure.world.activityfeed.disabled": "La stub sba j-ok' li yalelik jtajimoletik", "mco.configure.world.backup": "Lok'taeletik", "mco.configure.world.buttons.activity": "Spaselik jtajimoletik", "mco.configure.world.buttons.close": "Tsmak Realm jlikeluk", "mco.configure.world.buttons.delete": "Tstup'", "mco.configure.world.buttons.done": "Pasbil", "mco.configure.world.buttons.edit": "Tsineletik", "mco.configure.world.buttons.invite": "Chik' jtajimol", "mco.configure.world.buttons.moreoptions": "Mas t'ujeletik", "mco.configure.world.buttons.newworld": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>l", "mco.configure.world.buttons.open": "Tsjam Realm nixtok", "mco.configure.world.buttons.options": "Stsineltak balumil", "mco.configure.world.buttons.players": "Jtajimoletik", "mco.configure.world.buttons.region_preference": "Tst'uj osil...", "mco.configure.world.buttons.resetworld": "<PERSON><PERSON>'likes balumil", "mco.configure.world.buttons.save": "Tsk'ej", "mco.configure.world.buttons.settings": "Tsineletik", "mco.configure.world.buttons.subscription": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.switchminigame": "Tsjel bik'it tajimol", "mco.configure.world.close.question.line1": "Xu' xamak jlikeluk li Realm avu'une, yo' mu'yuk buch'u chtajin k'alal yakal chajel. <PERSON><PERSON> nixtok k'alal chapalot xa.\n\nLi'e mu tsvos li anochanele yu'un Realms.", "mco.configure.world.close.question.line2": "¿Mi melel chak'an xayaket?", "mco.configure.world.close.question.title": "¿Mi sk'an chajelvan mu xchi'uk k'atanel?", "mco.configure.world.closing": "Yakal tsjam Realm jlikeluk...", "mco.configure.world.commandBlocks": "<PERSON><PERSON> mantale<PERSON>k", "mco.configure.world.delete.button": "Tstup' Realm", "mco.configure.world.delete.question.line1": "Ta xtup'at ta jech'el li Realm avu'une", "mco.configure.world.delete.question.line2": "¿Mi melel chak'an xayaket?", "mco.configure.world.description": "Yalel Realm", "mco.configure.world.edit.slot.name": "<PERSON><PERSON> bal<PERSON>l", "mco.configure.world.edit.subscreen.adventuremap": "Tubbilik li jayibuk t'ujeletike yu'un oy amapa aventura", "mco.configure.world.edit.subscreen.experience": "Tubbilik li jayibuk t'ujeletik yu'un oy amapa kuxlejal", "mco.configure.world.edit.subscreen.inspiration": "Tubbilik li jayibuk t'ujeletik yu'un oy amapa ta motivasion", "mco.configure.world.forceGameMode": "Ta suj smanera taji<PERSON>l", "mco.configure.world.invite.narration": "Oy %s avach' ik'vanel(tak)", "mco.configure.world.invite.profile.name": "Biil", "mco.configure.world.invited": "<PERSON><PERSON><PERSON>bil", "mco.configure.world.invited.number": "Ik'bil (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Jchapanvan<PERSON>", "mco.configure.world.invites.remove.tooltip": "Tstup'", "mco.configure.world.leave.question.line1": "Mi chalok' li Realm li'e mu xa xavil ja'to cha-ik'at nixtok", "mco.configure.world.leave.question.line2": "¿Mi melel chak'an xayaket?", "mco.configure.world.loading": "<PERSON><PERSON> t<PERSON>vinajes li Realme", "mco.configure.world.location": "Uvikasion", "mco.configure.world.minigame": "Tana: %s", "mco.configure.world.name": "Sbi Realm", "mco.configure.world.opening": "Yakal tsjam Realm...", "mco.configure.world.players.error": "Ch'abal jun jtajimol xchi'uk li biil le'e", "mco.configure.world.players.inviting": "<PERSON>kal chik' li j<PERSON>...", "mco.configure.world.players.title": "Jtajimoletik", "mco.configure.world.pvp": "PVP (Jtajimol ta kontra jtajimol)", "mco.configure.world.region_preference": "Sk'upinel osil", "mco.configure.world.region_preference.title": "St'ujel sk'upinel osil", "mco.configure.world.reset.question.line1": "Ta xcha'vinajesat li abalumile xchi'uk ta xch'ay li abalumil avie", "mco.configure.world.reset.question.line2": "¿Mi melel chak'an xayaket?", "mco.configure.world.resourcepack.question": "Sk'an jun jelbil pakete rekurso sventa chatajin li ta Realm li'e\n\n¿Mi chak'an chayales xchi'uk chatajin?", "mco.configure.world.resourcepack.question.line1": "Sk'an jun jelbil pakete rekurso sventa chatajin li ta <PERSON> li'e", "mco.configure.world.resourcepack.question.line2": "¿Mi chak'an chayales xchi'uk chatajin?", "mco.configure.world.restore.download.question.line1": "Ta xyalesat xchi'uk ta xkapat ta abalumiltak ta ''jun no'ox jtajimol'' li balumile.", "mco.configure.world.restore.download.question.line2": "¿Mi chak'an xayaket?", "mco.configure.world.restore.question.line1": "Li Realm avu'une ta sutesat ta sk'ak'alil ''%s'' (%s)", "mco.configure.world.restore.question.line2": "¿Mi melel chak'an xayaket?", "mco.configure.world.settings.expired": "<PERSON> xu' x<PERSON><PERSON> li <PERSON>ltak jun Realm ti lajem xae", "mco.configure.world.settings.title": "Tsineletik", "mco.configure.world.slot": "Balumil %s", "mco.configure.world.slot.empty": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot.switch.question.line1": "Ta xjelat ta yan balumil li Realm avu'une", "mco.configure.world.slot.switch.question.line2": "¿Mi melel chak'an xayaket?", "mco.configure.world.slot.tooltip": "Tsjel ta balumil", "mco.configure.world.slot.tooltip.active": "Ch-och", "mco.configure.world.slot.tooltip.minigame": "Tsjel ta bik'it tajimol", "mco.configure.world.spawnAnimals": "Tsvinajes chanuletik", "mco.configure.world.spawnMonsters": "Tsvina<PERSON>s mon<PERSON>k", "mco.configure.world.spawnNPCs": "Tsvinajes jbik'it lumetik", "mco.configure.world.spawnProtection": "Som ba'yel avil", "mco.configure.world.spawn_toggle.message": "Mi chatub li t'ujel li'e, ta xatup' s<PERSON>l li entiraletike li ta tos le'e", "mco.configure.world.spawn_toggle.message.npc": "Mi chatub li t'ujel li'e, ta xatup' s<PERSON>l li entiraletike li ta tos le'e, k'ucha'al li jbik'it lumetike", "mco.configure.world.spawn_toggle.title": "¡<PERSON>ijan me!", "mco.configure.world.status": "Estaro", "mco.configure.world.subscription.day": "k'ak'al", "mco.configure.world.subscription.days": "k'ak'aletik", "mco.configure.world.subscription.expired": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.less_than_a_day": "<PERSON>os ke jun k'ak'al", "mco.configure.world.subscription.month": "u", "mco.configure.world.subscription.months": "uetik", "mco.configure.world.subscription.recurring.daysleft": "A<PERSON>'ubtasbil ta stuk ta", "mco.configure.world.subscription.recurring.info": "<PERSON> xak' sbaik ja'to li sk'ak'alil tojel chvul toe li sjeleltak anochanele ta Realms, jech k'ucha'al li smuk'ubtasel sk'ak'alil nochanele o li ach'ubtasel ta stuke.", "mco.configure.world.subscription.remaining.days": "%1$s k'ak'al", "mco.configure.world.subscription.remaining.months": "%1$s u", "mco.configure.world.subscription.remaining.months.days": "%1$s 'u, %2$s k'ak'al", "mco.configure.world.subscription.start": "Sk'a<PERSON>'alil likemal", "mco.configure.world.subscription.tab": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.timeleft": "Sk'ak'alil oy to", "mco.configure.world.subscription.title": "<PERSON><PERSON> ta nochanel", "mco.configure.world.subscription.unknown": "<PERSON> oj<PERSON>", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON> bal<PERSON>", "mco.configure.world.switch.slot.subtitle": "<PERSON>jol li balumile. T'ujo li k'usi chak'an chapase", "mco.configure.world.title": "Tstsin Realm:", "mco.configure.world.uninvite.player": "¿Mi melel mu xa chak'an chavik' li ''%s''e?", "mco.configure.world.uninvite.question": "¿Mi melel mu xa chak'an chavik'", "mco.configure.worlds.title": "Balumiletik", "mco.connect.authorizing": "Yakal tslikes sesion...", "mco.connect.connecting": "Yakal tsts'ak sba ta Realm...", "mco.connect.failed": "Mu xu' sts'ak sba ta Realm", "mco.connect.region": "<PERSON><PERSON><PERSON> servilor: %s", "mco.connect.success": "Pasbil", "mco.create.world": "Tspas", "mco.create.world.error": "¡Sk'an chats'ibta jun biil!", "mco.create.world.failed": "¡Chopolaj k'alal tsmeltsan ox li balumile!", "mco.create.world.reset.title": "Yakal tspas balumil...", "mco.create.world.skip": "Mu sna'", "mco.create.world.subtitle": "Mi chak'ane, t'ujo jun balumil sventa chakomes li ta ach' Realm avu'une", "mco.create.world.wait": "Yakal tspas Realm...", "mco.download.cancelled": "Yalesel vosbil", "mco.download.confirmation.line1": "<PERSON>a' mas muk' ke %s li balumile chak'an chayales", "mco.download.confirmation.line2": "Mu xu' xamuy li balumil li'e ta Realm avu'un nixtok", "mco.download.confirmation.oversized": "Ja' mas nat ti %s li balumile chak'an chayalese.\n\nMu xu' xamuy li balumil li'e ta Realm avu'un nixtok", "mco.download.done": "Yalesel pasbil", "mco.download.downloading": "Ya<PERSON> t<PERSON>", "mco.download.extracting": "Yakal ta xul", "mco.download.failed": "<PERSON><PERSON><PERSON> li ya<PERSON>ele", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON><PERSON> tsm<PERSON>tsan li ya<PERSON>ele", "mco.download.resourcePack.fail": "¡Chopolaj k'alal tsyales ox li pakete rekursoe!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "<PERSON><PERSON> t<PERSON> li sla<PERSON><PERSON> bal<PERSON>", "mco.error.invalid.session.message": "<PERSON><PERSON><PERSON>o Minecraft xchi'uk paso nixtok", "mco.error.invalid.session.title": "Chopol li sesione", "mco.errorMessage.6001": "<PERSON> a<PERSON>'ubtas<PERSON> kiliente", "mco.errorMessage.6002": "Sleytak servisio mu ch'unbilik", "mco.errorMessage.6003": "Sts'ak yalesel tabil", "mco.errorMessage.6004": "<PERSON><PERSON>'ak muyel tabil", "mco.errorMessage.6005": "<PERSON><PERSON><PERSON> ma<PERSON>", "mco.errorMessage.6006": "<PERSON> <PERSON><PERSON>'ubtas<PERSON> balumil", "mco.errorMessage.6007": "Te oy ta ep Realms li j<PERSON>le", "mco.errorMessage.6008": "Sbi Realm ja' chopol", "mco.errorMessage.6009": "Yalel Realm ja' chopol", "mco.errorMessage.connectionFailure": "K'ot ta pasel jun chopolal. Paso nixtok, avokoluk.", "mco.errorMessage.generic": "K'ot ta pasel li jun ch'ay-o'ontonale: ", "mco.errorMessage.initialize.failed": "<PERSON><PERSON><PERSON> k'alal tslikes ox li <PERSON>e", "mco.errorMessage.noDetails": "Mu la yak' yaleltak ch'ay-o'ontonal", "mco.errorMessage.realmsService": "K'ot ta pasel li jun ch'ay-o'ontonale (%s):", "mco.errorMessage.realmsService.configurationError": "K'ot ta pasel jun chopolal k'alal tsjel ox li st'ujeltak balumile", "mco.errorMessage.realmsService.connectivity": "Mu xu' sts'ak sba ta Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Mu xu' xk'elanat li ch'unbil versione, la sta li tak'ele: %s", "mco.errorMessage.retry": "<PERSON><PERSON><PERSON>pas <PERSON>ion", "mco.errorMessage.serviceBusy": "Mu xokol li servisio Minecraft Realms tanae.\nPaso nixtok ta patil, avokoluk.", "mco.gui.button": "Voton", "mco.gui.ok": "Lek", "mco.info": "¡Alelal!", "mco.invited.player.narration": "La yik' li %se", "mco.invites.button.accept": "Ch<PERSON>'un", "mco.invites.button.reject": "T<PERSON>'aj", "mco.invites.nopending": "¡Ch'abal xa mas ik'vaneletik!", "mco.invites.pending": "¡Ach' ik'vaneletik!", "mco.invites.title": "Mas ik'vanele<PERSON>", "mco.minigame.world.changeButton": "Tst'uj yan bik'it tajimol", "mco.minigame.world.info.line1": "¡Li' ta sk'exta li abalumile xchi'uk jun bik'it tajimol jlikel!", "mco.minigame.world.info.line2": "Xu' xasut ta patil ta abats'i balumil xchi'uk mu xach'ay k'usitik.", "mco.minigame.world.noSelection": "T'ujo jun bik'it tajimol, avokoluk", "mco.minigame.world.restore": "Yakal tslajes li bik'it tajimole...", "mco.minigame.world.restore.question.line1": "Ta xlaj li bik'it tajimole xchi'uk ta sut ta k'uxi ch-elan ox li Realm avu'une.", "mco.minigame.world.restore.question.line2": "¿Mi melel chak'an xayaket?", "mco.minigame.world.selected": "T'ujbil bik'it tajimol:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON> tsjel li balumi<PERSON>...", "mco.minigame.world.startButton": "<PERSON><PERSON><PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Yakal tslikes li bik'it tajimole...", "mco.minigame.world.stopButton": "Tslajes bik'it tajimol", "mco.minigame.world.switch.new": "¿Mi chak'an chat'uj yan bik'it tajimol?", "mco.minigame.world.switch.title": "Tsjel bik'it tajimol", "mco.minigame.world.title": "Tsjel ta bik'it tajimol", "mco.news": "Slo'iltak Minecraft Realm", "mco.notification.dismiss": "Mu sna'", "mco.notification.transferSubscription.buttonText": "<PERSON><PERSON><PERSON><PERSON> tana", "mco.notification.transferSubscription.message": "Bak'ik ta Microsoft Store li nochaneletike ta Realms sventa Java. ¡Mu xavak' xlaj li anochanele! \nVuyo tana xchi'uk tao 30 k'ak'al ta Realms mu xchi'uk stojol.\nBatan ta aperxil ta minecraft.net sventa chavuy li anochanele.", "mco.notification.visitUrl.buttonText.default": "<PERSON><PERSON><PERSON><PERSON> ts'akel", "mco.notification.visitUrl.message.default": "<PERSON><PERSON>'lano li ts'akele ta olon, avokoluk", "mco.onlinePlayers": "Jtajimoletik ta internet", "mco.play.button.realm.closed": "Makal li Realme", "mco.question": "Jak'el", "mco.reset.world.adventure": "Aventuraetik", "mco.reset.world.experience": "Kuxlejaletik", "mco.reset.world.generate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>l", "mco.reset.world.inspiration": "Motivasion", "mco.reset.world.resetting.screen.title": "Yakal chcha'likes balumil...", "mco.reset.world.seed": "Be<PERSON>'il (st'ujelal)", "mco.reset.world.template": "Plantiya balumiletik", "mco.reset.world.title": "<PERSON><PERSON>'likes balumil", "mco.reset.world.upload": "<PERSON><PERSON><PERSON> bal<PERSON>", "mco.reset.world.warning": "Li' ta sk'exta li sbalumil avie Realm avu'une", "mco.selectServer.buy": "¡Mano jun Realm!", "mco.selectServer.close": "Tsmak", "mco.selectServer.closed": "Tubbil Realm", "mco.selectServer.closeserver": "Tsmak Realm", "mco.selectServer.configure": "<PERSON><PERSON><PERSON>", "mco.selectServer.configureRealm": "Tstsin Realm", "mco.selectServer.create": "Tspas Realm", "mco.selectServer.create.subtitle": "T'ujo li balumile chak'an chavak' li ta ach' Realm avu'une", "mco.selectServer.expired": "<PERSON>jem <PERSON>", "mco.selectServer.expiredList": "<PERSON><PERSON>m li anochanele", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "Tsnochan", "mco.selectServer.expiredTrial": "Laj li aprevae", "mco.selectServer.expires.day": "Chlaj ta jun k'ak'al", "mco.selectServer.expires.days": "Chlaj ta %s k'ak'al", "mco.selectServer.expires.soon": "Po'ot ta xlaj", "mco.selectServer.leave": "Chlok'", "mco.selectServer.loading": "<PERSON><PERSON> tsvinajes li listae yu'un Realms", "mco.selectServer.mapOnlySupportedForVersion": "Mu ch'unbil ta %s li mapa li'e", "mco.selectServer.minigame": "Bik'it tajimol:", "mco.selectServer.minigameName": "Bik'it tajimol: %s", "mco.selectServer.minigameNotSupportedInVersion": "Mu xu' xtajin ta %s li bik'it tajimol li'e", "mco.selectServer.noRealms": "<PERSON><PERSON><PERSON> ch'abal Realm avu'un. <PERSON><PERSON> jun Realm sventa chatajin xchi'uk avamikotak.", "mco.selectServer.note": "<PERSON><PERSON>ye<PERSON><PERSON>:", "mco.selectServer.open": "<PERSON>", "mco.selectServer.openserver": "Tsjam Realm", "mco.selectServer.play": "Chtajin", "mco.selectServer.popup": "Realms ja' jun lekil xchi'uk k'unil manera sventa chatajin <PERSON>craft asta xchi'uk lajuneb avamikotak ta jmoj. ¡Tsts'ik li bik'it tajimoletike xchi'uk li jelbil balumiletike! Ja' no'ox chtojvan li ajvalile yu'un Realm.", "mco.selectServer.purchase": "Tskap Realm", "mco.selectServer.trial": "¡Paso jun preva!", "mco.selectServer.uninitialized": "¡Paso klik sventa chapas li ach' Realm avu'une!", "mco.snapshot.createSnapshotPopup.text": "Ta xapas jun Realm sventa ach'ubtasel prevaetik mu xchi'uk stojol ti ta sts'ak sba ta Realm avu'une xchi'uk stojol. Xu' xata li Realm sventa ach'ubtasel prevaetik li'e mi ja' tsakal li nochanele. Mu ta sjel li Realm avu'une xchi'uk stojol.", "mco.snapshot.createSnapshotPopup.title": "¿Mi xak'an xapas jun Realm sventa prevaetik?", "mco.snapshot.creating": "Yakal tspas li Realm sventa prevaetike...", "mco.snapshot.description": "Ts'akbil ta ''%s''", "mco.snapshot.friendsRealm.downgrade": "Sk'an te oyot ta version %s sventa chats'ak aba li ta Realm li'e", "mco.snapshot.friendsRealm.upgrade": "Li %se sk'an chach'ubtas li Realm yu'une ta ba'i xu' xatajin li ta version li'e", "mco.snapshot.paired": "Ja' ts'akbil ta ''%s'' li Realm sventa ach'ubtasel prevaetik li'e", "mco.snapshot.parent.tooltip": "Tu<PERSON>o li mas ach' ach'ubtasele yu'un Minecraft sventa chatajin li ta Realm li'e", "mco.snapshot.start": "Tsjam Realm sventa ach'ubtasel prevaetik mu xchi'uk stojol", "mco.snapshot.subscription.info": "Li' ja' jun Realm sventa ach'ubtasel prevaetike ts'akbil ta snochanel Realm avu'un ''%s''. Tsakal o mi ja' tsakal li ts'akbil Realm eke.", "mco.snapshot.tooltip": "Tuneso li Realms sventa version prevaetike sventa chak'el li versionetike chvulik to, ja'ik xu' skapik ach' karakteristikaetik xchi'uk yantik jeleletik.\n\nXu' xata li bats'i Realms avu'une ta sversion tajimol.", "mco.snapshotRealmsPopup.message": "Realms tana xu' xtunesatik ta ach'ubtasel prevaetike chlik ta preva 23w41a. ¡Skotol li nochaneletike ta Realms chvulik xchi'uk jun Realm ta prevaetik mu xchi'uk stojol ti chvul ch'akbil ta bats'i Realm avu'une!", "mco.snapshotRealmsPopup.title": "Realms tana xu' xtunesatik ta prevaetik", "mco.snapshotRealmsPopup.urlText": "<PERSON><PERSON> chano", "mco.template.button.publisher": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.button.select": "Tst'uj", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "<PERSON><PERSON> balumil", "mco.template.info.tooltip": "<PERSON><PERSON><PERSON>", "mco.template.name": "Plantiya", "mco.template.select.failure": "Mu xu' jtakutik nixtok li slistaik k'usitike sventa li tos li'e.\nK'elano li ats'akel ta internete o paso nixtok ta patil, avokoluk.", "mco.template.select.narrate.authors": "Jpasvanejetik: %s", "mco.template.select.narrate.version": "version %s", "mco.template.select.none": "A, yilel ch'abal k'usitik li ta tos li'e tana.\nK'elano nixtok ta patil, o mi jpasvanejote,\n%s.", "mco.template.select.none.linkTitle": "chatakbunkutik nan jun k'usi avu'un", "mco.template.title": "Plantiya balumiletik", "mco.template.title.minigame": "Bik'it tajimoletik", "mco.template.trailer.tooltip": "Tsk'el strailer mapa", "mco.terms.buttons.agree": "Ta jch'un", "mco.terms.buttons.disagree": "Mu jch'un", "mco.terms.sentence.1": "Ta jch'un li sleytak servisioe", "mco.terms.sentence.2": "yu'un Minecraft Realms", "mco.terms.title": "Sleytak servisio Minecraft Realms", "mco.time.daysAgo": "%1$s k'ak'al xa", "mco.time.hoursAgo": "%1$s ora xa", "mco.time.minutesAgo": "%1$s minuto xa", "mco.time.now": "tana", "mco.time.secondsAgo": "%1$s sekunto xa", "mco.trial.message.line1": "¿Mi chak'an chavu'unin jun Realm?", "mco.trial.message.line2": "¡Paso klik li'e sventa mas alelal!", "mco.upload.button.name": "<PERSON><PERSON><PERSON>", "mco.upload.cancelled": "<PERSON><PERSON><PERSON> v<PERSON>", "mco.upload.close.failure": "Mu xu' xmak li Realm avu'une. Paso nixtok ta patil, avokoluk", "mco.upload.done": "<PERSON><PERSON><PERSON>", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "¡Chopolaj li muyele! (%s)", "mco.upload.failed.too_big.description": "<PERSON><PERSON> muk' li t'u<PERSON><PERSON> bal<PERSON>. Li sts'ak ak'bil muk'ulil ja' %s.", "mco.upload.failed.too_big.title": "<PERSON><PERSON> muk' li bal<PERSON>", "mco.upload.hardcore": "¡Mu xu' muyatik li toj tsatsal balumiletik!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON> tsm<PERSON>tsan li abalumile", "mco.upload.select.world.none": "¡Mu la sta balumiletik xchi'uk jun no'ox jtajimol!", "mco.upload.select.world.subtitle": "T'ujo jun balumil xchi'uk jun no'ox jtajimol sventa chamuy", "mco.upload.select.world.title": "<PERSON><PERSON><PERSON> bal<PERSON>", "mco.upload.size.failure.line1": "¡''%s'' ja' toj muk'!", "mco.upload.size.failure.line2": "Ja' %s yalal. Ja' %s li ch'unbil ts'ake.", "mco.upload.uploading": "Yakal tsmuy ''%s''", "mco.upload.verifying": "Yakal tsk'elan li a<PERSON>", "mco.version": "Version: %s", "mco.warning": "¡<PERSON>ijan me!", "mco.worldSlot.minigame": "Bik'it tajimol", "menu.custom_options": "<PERSON><PERSON><PERSON> t'ujeletik...", "menu.custom_options.title": "<PERSON><PERSON><PERSON> t'uje<PERSON>", "menu.custom_options.tooltip": "Ya'yejal: Ch-ak'atik yu'un servilortakik o spasobtakik yoxibaletik li jelel t'ujeletike.\n¡Bijan me!", "menu.custom_screen_info.button_narration": "<PERSON>'e ja' jun jelel pantaya. Mas chano.", "menu.custom_screen_info.contents": "Li k'usitik te oyik li ta pantaya li'e ch-ak'batik smantaltakik yu'un servilortakik xchi'uk smapatakik yoxibaletik ti mu u'uninbilik, abtelanbilik o chabibilik yu'un Mojang Studios o Microsoft.\n\n¡Kuentaan! Bijan me k'alal chajam ts'akeletik xchi'uk mu'yuk o xavak' li yalelal akuxleja<PERSON>, ja' tskap ek li yalelal slikesel akuentae.\n\nMi tspajesot ta tajinel li pantaya li'e, xu' xalok' ta servilor li'e ek xchi'uk li votone ta olon.", "menu.custom_screen_info.disconnect": "<PERSON><PERSON><PERSON> p'a<PERSON><PERSON>", "menu.custom_screen_info.title": "Ya'yejal ta venta jelel panta<PERSON>", "menu.custom_screen_info.tooltip": "<PERSON>'e ja' jun jelel panta<PERSON>. Paso klik li'e sventa mas chachanvan.", "menu.disconnect": "Chch'ak sba", "menu.feedback": "Lo'iletik...", "menu.feedback.title": "Lo'iletik", "menu.game": "<PERSON><PERSON>", "menu.modded": " (<PERSON><PERSON><PERSON>)", "menu.multiplayer": "Ta<PERSON><PERSON><PERSON>", "menu.online": "Minecraft Realms", "menu.options": "T'ujeletik...", "menu.paused": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "menu.playdemo": "Chtajin ta balumil preva", "menu.playerReporting": "Chal smul j<PERSON><PERSON>l", "menu.preparingSpawn": "Yakal tsmeltsan vinajebal: %s %%", "menu.quick_actions": "Anil p<PERSON>...", "menu.quick_actions.title": "<PERSON><PERSON>", "menu.quit": "Tsmak Minecraft", "menu.reportBugs": "Chal ch'ay-o'ontonal", "menu.resetdemo": "<PERSON><PERSON>'likes li balumil prevae", "menu.returnToGame": "Ta sut ta tajimol", "menu.returnToMenu": "Tsk'ej xchi'uk chlok' ta menu", "menu.savingChunks": "Yakal tsk'ej set'eletik", "menu.savingLevel": "Yakal tsk'ej...", "menu.sendFeedback": "<PERSON><PERSON><PERSON> <PERSON>tal", "menu.server_links": "Sts'akeltak servilor...", "menu.server_links.title": "Sts'akeltak servilor", "menu.shareToLan": "Tsjam ta LAN", "menu.singleplayer": "Jun no'ox jtajimol", "menu.working": "Yakal ch-abtej...", "merchant.deprecated": "Tsbut' sba cha'koj ta k'ak'al.", "merchant.level.1": "J-ach'", "merchant.level.2": "<PERSON><PERSON><PERSON>", "merchant.level.3": "J-ojtikinvanej", "merchant.level.4": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.level.5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merchant.title": "%s: %s", "merchant.trades": "Chonolajeletik", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "%1$s sventa chayal", "multiplayer.applyingPack": "<PERSON><PERSON> chak' pakete rekurso", "multiplayer.confirm_command.parse_errors": "Yakal chak'an chameltsan jun mu ojtikinbil o chopol mantal.\n¿Mi chak'an ta melel?\nMantal: %s", "multiplayer.confirm_command.permissions_required": "Yakal chak'an chameltsan jun mantal ti sk'an toyol k'opetik.\nLi'e ep tsok nan li atajimole.\n¿Mi chak'an ya melel? \nMantal: %s", "multiplayer.confirm_command.title": "Chch'un li smeltsanel mantale", "multiplayer.disconnect.authservers_down": "Mu ch-abtejik li servilor k'elaneletike. Paso me nixtok ta patil, avo<PERSON><PERSON>. ¡Ak'bunkutik me perton!", "multiplayer.disconnect.bad_chat_index": "La avich' jun ch'aybil o cha'tsinbil mantal ta servilor", "multiplayer.disconnect.banned": "Chalok'esat li ta servilor li'e", "multiplayer.disconnect.banned.expiration": "\nTa xlaj li alok'esele ta %s", "multiplayer.disconnect.banned.reason": "Lok'esbilot li ta servilor li'e.\n<PERSON>: %s", "multiplayer.disconnect.banned_ip.expiration": "\nTa xlaj li alok'esele ta %s", "multiplayer.disconnect.banned_ip.reason": "Lok'esbil li IP avu'une li ta servilor li'e.\n<PERSON>: %s", "multiplayer.disconnect.chat_validation_failed": "<PERSON><PERSON><PERSON> k'alal tsk'elan ox li smantal lo'ile", "multiplayer.disconnect.duplicate_login": "La ats'ak aba xchi'uk yan rireksion", "multiplayer.disconnect.expired_public_key": "Lajem li komon yavi' perxile. K'elano mi ja' ko'ol li sk'ak'alil asistemae xchi'uk cha'likeso li atajimole.", "multiplayer.disconnect.flying": "Mu xu' xavil li ta servilor li'e", "multiplayer.disconnect.generic": "Ch'akbil", "multiplayer.disconnect.idling": "¡Al latini!", "multiplayer.disconnect.illegal_characters": "Mu ch'unbil xotetik ta lo'il", "multiplayer.disconnect.incompatible": "¡Mu ts'ikbil kiliente! Tuneso li versione %s, avokoluk", "multiplayer.disconnect.invalid_entity_attacked": "Cha<PERSON>'an ox chamaj jun chopol entiral", "multiplayer.disconnect.invalid_packet": "La stak jun chopol pakete li servilore", "multiplayer.disconnect.invalid_player_data": "Xchopol alelal jtajimol", "multiplayer.disconnect.invalid_player_movement": "La sta jun xchopol bak'el jtajimol", "multiplayer.disconnect.invalid_public_key_signature": "Chopol tik'-biil sventa komon yavi' perxil.\n<PERSON>'<PERSON>o li atajimole.", "multiplayer.disconnect.invalid_public_key_signature.new": "Chopol tik'-biil sventa komon yavi' perxil.\n<PERSON>'<PERSON>o li atajimole.", "multiplayer.disconnect.invalid_vehicle_movement": "La sta jun chopol bak'el karo", "multiplayer.disconnect.ip_banned": "Lok'esbil li IP avu'une li ta servilor li'e", "multiplayer.disconnect.kicked": "Chalok'esat yu'un jun j<PERSON>", "multiplayer.disconnect.missing_tags": "La sta jun tsobol ta mu ts'akal etiketaetik li servilore. Ik'o li ya<PERSON><PERSON><PERSON><PERSON><PERSON> servilore, avokoluk.", "multiplayer.disconnect.name_taken": "Oy xa jun krixchano xchi'uk abi", "multiplayer.disconnect.not_whitelisted": "¡Mu te oyot ta sakil lista servilor!", "multiplayer.disconnect.out_of_order_chat": "La sta jun pakete lo'il ta pat servisio. ¿Mi la sjel sba li sk'ak'alil asistemae?", "multiplayer.disconnect.outdated_client": "¡Mu ts'ikbil kiliente! Tuneso li versione %s, avokoluk", "multiplayer.disconnect.outdated_server": "¡Mu ts'ikbil kiliente! Tuneso li versione %s, avokoluk", "multiplayer.disconnect.server_full": "¡Noj li servilore!", "multiplayer.disconnect.server_shutdown": "Servilor makbil", "multiplayer.disconnect.slow_login": "Al jok'tsaj li ts'akele", "multiplayer.disconnect.too_many_pending_chats": "Toj ep mu ch'unbil mantal lo'iletik", "multiplayer.disconnect.transfers_disabled": "Mu chch'un vuyeletik li servilore", "multiplayer.disconnect.unexpected_query_response": "La sta mu ojtikinbil alelal yu'un kiliente", "multiplayer.disconnect.unsigned_chat": "La sta jun pakete lo'il mu xchi'uk tik'-biil o xchi'uk chopol tik'-biil.", "multiplayer.disconnect.unverified_username": "¡Mu xu' sk'elan li sbi avajtunesvaneje!", "multiplayer.downloadingStats": "<PERSON>kal chich' estaristikaetik...", "multiplayer.downloadingTerrain": "<PERSON><PERSON> t<PERSON> bal<PERSON>...", "multiplayer.lan.server_found": "A<PERSON>' servilor tabil: %s", "multiplayer.message_not_delivered": "Mu xu' stak li mantale, k'elano li yapuntetak servilore: %s", "multiplayer.player.joined": "Och ta tajimol li %se", "multiplayer.player.joined.renamed": "Och ta tajimol li %se (ba'yel ojtikinat k'ucha'al %s)", "multiplayer.player.left": "Lok' li %se", "multiplayer.player.list.hp": "%s PS", "multiplayer.player.list.narration": "Jtajimoletik ta internet: %s", "multiplayer.requiredTexturePrompt.disconnect": "Sk'an jun jelbil pakete rekurso li servilore", "multiplayer.requiredTexturePrompt.line1": "Sk'an stunesel spakete srekurso li servilor li'e.", "multiplayer.requiredTexturePrompt.line2": "<PERSON> chap'aje, mu xu' xa chats'ak aba.", "multiplayer.socialInteractions.not_available": "Ja' no'ox te oyik ta balumiletik ta tajinebal li jipetbateletike", "multiplayer.status.and_more": "... xchi'uk %s mas ...", "multiplayer.status.cancelled": "Vosbil", "multiplayer.status.cannot_connect": "Mu xu' sts'ak sba ta servilor", "multiplayer.status.cannot_resolve": "<PERSON> xu' x<PERSON><PERSON> li rireksione", "multiplayer.status.finished": "Laj li ts'akele", "multiplayer.status.incompatible": "¡Mu ts'ikbil version!", "multiplayer.status.motd.narration": "<PERSON><PERSON><PERSON> k'ak'al: %s", "multiplayer.status.no_connection": "(ch'abal ts'akel)", "multiplayer.status.old": "<PERSON><PERSON>'", "multiplayer.status.online": "Ta internet", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Latensia %s milisekunto", "multiplayer.status.pinging": "Yakal tsts'ak sba...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s ta %s jtajimoletik ta internet", "multiplayer.status.quitting": "Yakal chch'ak sba", "multiplayer.status.request_handled": "La xch'am li k'an-estaroe", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "La yich' jun mu k'anbil k'an<PERSON>el", "multiplayer.status.version.narration": "Version servilor: %s", "multiplayer.stopSleeping": "Chlik ta tem", "multiplayer.texturePrompt.failure.line1": "<PERSON> xu' xak' li spakete srekurso servilore", "multiplayer.texturePrompt.failure.line2": "K'uk elanuk tunelal ti sk'an jelbil rekursoetike mu ch-abtej nan k'ucha'al sk'an", "multiplayer.texturePrompt.line1": "Chalbot ti ak'o xatunes spakete srekurso li servilor li'e.", "multiplayer.texturePrompt.line2": "¿Mi chak'an chayales xchi'uk ak'o chak' sba stuk?", "multiplayer.texturePrompt.serverPrompt": "%s\n\n<PERSON><PERSON><PERSON> servilor:\n%s", "multiplayer.title": "Ta<PERSON><PERSON><PERSON>", "multiplayer.unsecureserver.toast": "Li mantaletike takbil li ta servilor li'e xu' jelatik xchi'uk mu x-elanik nan k'ucha'al li bats'i mantale", "multiplayer.unsecureserver.toast.title": "Mu xu' xk'elanatik li mantal lo'iletik", "multiplayerWarning.check": "<PERSON> xak' ta ilel nixtok li mantal li'e", "multiplayerWarning.header": "Bijan me: stajimolik yoxibaletik ta internet", "multiplayerWarning.message": "Bijan me: li manera tajinebale ja' pasbil ta serviloretik yu'un yoxibaletik mu u'uninbilik, chabibilik o k'elanbilik yu'un Mojang Studios o Microsoft. K'alal chatajin, chak'el nan chopol mantal lo'iletik o yantik k'usitik pasbil yu'un komon osil. Ja' yu'un, chopol nan sventa jayibuk krixchanoetik.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> On <PERSON>", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Lab<PERSON><PERSON><PERSON>", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Voton: %s", "narration.button.usage.focused": "Net'o <PERSON>ter sventa chatsan", "narration.button.usage.hovered": "Paso ts'et klik sventa chatsan", "narration.checkbox": "Kasiya: %s", "narration.checkbox.usage.focused": "Net'o <PERSON>ter sventa chajel", "narration.checkbox.usage.hovered": "Paso ts'et klik sventa chajel", "narration.component_list.usage": "Net'o Tab sventa chabat ta yan elemento", "narration.cycle_button.usage.focused": "Net'o Enter sventa chajel ta %s", "narration.cycle_button.usage.hovered": "Paso ts'et klik sventa chajel ta %s", "narration.edit_box": "<PERSON><PERSON><PERSON> ts'ib: %s", "narration.item": "Item: %s", "narration.recipe": "Reseta sventa %s", "narration.recipe.usage": "Paso ts'et klik sventa chat'uj", "narration.recipe.usage.more": "Paso bats'i klik sventa chak' ta ilel mas resetaetik", "narration.selection.usage": "Net'o li teklaetike ak'ol xchi'uk olon sventa chabak' ta yan ochebal", "narration.slider.usage.focused": "Net'o li teklaetike ts'et xchi'uk bats'i sventa chajel li tojolilale", "narration.slider.usage.hovered": "<PERSON><PERSON> li j<PERSON>leje sventa chajel li tojolilale", "narration.suggestion": "La at'uj li mantale %s ta %s: %s", "narration.suggestion.tooltip": "La at'uj li mantale %s ta %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Net'o Tab sventa chabat ta yan mantal", "narration.suggestion.usage.cycle.hidable": "Net'o Tab sventa chabat ta yan mantal, o Esc sventa chalok' ta mantaletik", "narration.suggestion.usage.fill.fixed": "Net'o Tab sventa chatunes li mantale", "narration.suggestion.usage.fill.hidable": "Net'o Tab sventa chatunes li mantale, o Esc sventa chalok' ta mantaletik", "narration.tab_navigation.usage": "Net'o Ctrl xchi'uk Tab sventa chajelvan ta o'lol pestanyaetik", "narrator.button.accessibility": "Ochebal", "narrator.button.difficulty_lock": "<PERSON><PERSON> t<PERSON><PERSON>", "narrator.button.difficulty_lock.locked": "Makal", "narrator.button.difficulty_lock.unlocked": "<PERSON>", "narrator.button.language": "K'op", "narrator.controls.bound": "<PERSON> pasele %s ja' ts'ak<PERSON> ta %s", "narrator.controls.reset": "<PERSON><PERSON>'likes voton ta %s", "narrator.controls.unbound": "<PERSON> pasele %s mu ja' ts'akbil", "narrator.joining": "Yakal ch-och", "narrator.loading": "Yakal tsvinajes: %s", "narrator.loading.done": "Pasbil", "narrator.position.list": "T'ujbil chol lista %s ta %s", "narrator.position.object_list": "T'ujbil elemento chol %s ta %s", "narrator.position.screen": "Elemento ta pantaya %s ta %s", "narrator.position.tab": "T'ujbil pestanya %s ta %s", "narrator.ready_to_play": "<PERSON><PERSON> sventa taji<PERSON>l", "narrator.screen.title": "Spantaya sbi tajimol", "narrator.screen.usage": "Tuneso li skursor ch'oe o Tab sventa chat'uj jun elemento", "narrator.select": "La st'uj: %s", "narrator.select.world": "La st'uj ''%s'', tajin ta slajeb velta: %s, %s, %s, ta version: %s", "narrator.select.world_info": "La st'uj ''%s'', tajin ta slajeb velta: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> tub<PERSON>", "narrator.toast.enabled": "<PERSON><PERSON><PERSON> lo'il<PERSON><PERSON> tsakal", "optimizeWorld.confirm.description": "Li' ta sk'an slekubtas li abalumile k'alal sk'elan ak'o sk'ej sbaik ta mas ach' pormato skotol li alelaletike. Li' xu' xjalij mas o menos yu'un smuk'ul abalumil. <PERSON>'alal chlaj xa, ta x-abtej nan mas anil li abalumile pe mu xa xu' xtunesat ta poko' versionetik. ¿Mi melel chak'an xayaket?", "optimizeWorld.confirm.proceed": "Paso jun lok'tael xchi'uk lekubtaso", "optimizeWorld.confirm.title": "Tslekubtas balumil", "optimizeWorld.info.converted": "A<PERSON>'ubtas<PERSON> set'eletik: %s", "optimizeWorld.info.skipped": "Mu na'bil set'eletik: %s", "optimizeWorld.info.total": "<PERSON><PERSON><PERSON> li set'eletike: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Yakal chat li set'eletik...", "optimizeWorld.stage.failed": "¡Chopolaj! :(", "optimizeWorld.stage.finished": "Yakal chlaj...", "optimizeWorld.stage.finished.chunks": "<PERSON><PERSON> tsla<PERSON> sle<PERSON> set'eletik...", "optimizeWorld.stage.finished.entities": "Yakal tslajes slekubtaselik entiraletik...", "optimizeWorld.stage.finished.poi": "Yakal tslajes slekubtaselik av interesetik...", "optimizeWorld.stage.upgrading": "<PERSON>kal chach'ubtas skotol li set'eletike...", "optimizeWorld.stage.upgrading.chunks": "<PERSON><PERSON> tslekubtas skotol li set'eletike...", "optimizeWorld.stage.upgrading.entities": "Yakal tslekubtas skotol li entiraletik...", "optimizeWorld.stage.upgrading.poi": "Yakal tslekubtas skotol li av interesetike...", "optimizeWorld.title": "Yakal tslekubtas li balumile ''%s''", "options.accessibility": "Ochebal...", "options.accessibility.high_contrast": "<PERSON><PERSON>'<PERSON><PERSON> kont<PERSON>te", "options.accessibility.high_contrast.error.tooltip": "Mu xu' xtunesat li pakete rekurso ta ak'ol kontrastee.", "options.accessibility.high_contrast.tooltip": "Tslekubtas li skontrasteik yelementotak interpase.", "options.accessibility.high_contrast_block_outline": "Ts'aketik ta toyol kontraste", "options.accessibility.high_contrast_block_outline.tooltip": "Tslekubtas li kontraste ts'ak kuboe yu'un k'anbil kubo.", "options.accessibility.link": "Sbei<PERSON><PERSON><PERSON><PERSON> ochebal", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON>' <PERSON><PERSON>l patil", "options.accessibility.menu_background_blurriness.tooltip": "<PERSON><PERSON><PERSON><PERSON> li yik' osil spat menue.", "options.accessibility.narrator_hotkey": "Tekla sventa j-al lo'ilajel", "options.accessibility.narrator_hotkey.mac.tooltip": "Chak' stsan o stub li j-al lo'ilajele xchi'uk Cmd + B.", "options.accessibility.narrator_hotkey.tooltip": "Chak' stsan o stub li j-al lo'ilajele xchi'uk Ctrl + B.", "options.accessibility.panorama_speed": "Yanilal panorama", "options.accessibility.text_background": "Spat ts'ib", "options.accessibility.text_background.chat": "<PERSON><PERSON>il", "options.accessibility.text_background.everywhere": "<PERSON><PERSON>", "options.accessibility.text_background_opacity": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> spat ts'ib", "options.accessibility.title": "St'ujeltak ochebal", "options.allowServerListing": "Chak' ch<PERSON>j ta listaetik", "options.allowServerListing.tooltip": "Xu' xak'ik ta ilel sbiik jayibuk jtajimoletik ts'akbil ta internet li serviloretike jech k'ucha'al skomon estaroik.\nMi chatub li t'ujel li'e, mu ta xvinaj li ta listaetik le'e li abie.", "options.ao": "<PERSON>'<PERSON>il tilel", "options.ao.max": "Ts'ak", "options.ao.min": "Minimo", "options.ao.off": "MO'OJ", "options.attack.crosshair": "Mira", "options.attack.hotbar": "Bara", "options.attackIndicator": "J-ak' ta ilel majel", "options.audioDevice": "Rispositivo", "options.audioDevice.default": "Bats'i sistema", "options.autoJump": "Bitel ta stuk", "options.autoSuggestCommands": "Chak' mantaletik", "options.autosaveIndicator": "Chak' ta ilel li k'ejel ta stuke", "options.biomeBlendRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.biomeBlendRadius.1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (mas anil)", "options.biomeBlendRadius.11": "11×11 (tsatsal)", "options.biomeBlendRadius.13": "13x13 (toj tsatsal)", "options.biomeBlendRadius.15": "15x15 (ts'ak)", "options.biomeBlendRadius.3": "3×3 (anil)", "options.biomeBlendRadius.5": "5×5 (bats'i)", "options.biomeBlendRadius.7": "7×7 (ak'ol)", "options.biomeBlendRadius.9": "9×9 (toj ak'ol)", "options.chat": "Lo'il...", "options.chat.color": "Bonoliletik", "options.chat.delay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lo'il: %s sekunto", "options.chat.delay_none": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>il: M<PERSON>'OJ", "options.chat.height.focused": "Natilal xchi'uk enpoke", "options.chat.height.unfocused": "Natilal mu xchi'uk enpoke", "options.chat.line_spacing": "<PERSON><PERSON><PERSON>", "options.chat.links": "Ts'akeletik ta internet", "options.chat.links.prompt": "<PERSON><PERSON> k'alal tsjam ts'akeletik", "options.chat.opacity": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> lo'il", "options.chat.scale": "<PERSON><PERSON><PERSON>'ul ts'ib", "options.chat.title": "St'ujeltak lo'il", "options.chat.visibility": "<PERSON><PERSON>il", "options.chat.visibility.full": "<PERSON><PERSON><PERSON> ta k'elel", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.visibility.system": "Ja' no'ox mantaletik", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s ta set'", "options.clouds.fancy": "<PERSON><PERSON>' sba", "options.clouds.fast": "Anil", "options.controls": "<PERSON><PERSON><PERSON><PERSON>...", "options.credits_and_attribution": "Kereritoetik xchi'uk atiribusionetik...", "options.damageTiltStrength": "Ts'e'etel ta yaintasel", "options.damageTiltStrength.tooltip": "Li sts'e'etel kamarae yu'un ich'bil yajel.", "options.darkMojangStudiosBackgroundColor": "Lok'tael xchi'uk jun no'ox bonolil", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON><PERSON><PERSON><PERSON> li spat pantaya vinajele yu'un Mojang Studios ta ik'.", "options.darknessEffectScale": "St'ixt'unel ik' osil", "options.darknessEffectScale.tooltip": "Chchapan k'uxi cht'ixt'un li ejekto ik' osile k'alal jkot jchabich'en o jkot j-ok'el-sculk chak'bot.", "options.difficulty": "Tsatsalil", "options.difficulty.easy": "<PERSON><PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "Chvinajik li xi'el chanuletike pe jutuk chyaintasvanik. Chlaj li bara vi'nale xchi'uk xu' syales li kuxle<PERSON>le k'alal to ta 5 o'ontonal.", "options.difficulty.hard": "Tsatsal", "options.difficulty.hard.info": "Chvinajik li xi'el chanuletike pe mas chyaintasvanik. Chlaj li bara vi'nale xchi'uk xu' syales sjunul li kuxle<PERSON>le.", "options.difficulty.hardcore": "<PERSON><PERSON>", "options.difficulty.normal": "Bats'i", "options.difficulty.normal.info": "Chvinajik li xi'el chanuletike xchi'uk chyaintasvanik no'ox. Chlaj li bara vi'nale xchi'uk xu' syales li kuxle<PERSON>le k'alal to ta o'lol o'ontonal.", "options.difficulty.online": "Stsatsalil servilor", "options.difficulty.peaceful": "Mu xi'el", "options.difficulty.peaceful.info": "Ch'abal xi'el chanuletike xchi'uk oy no'ox jayibuk neotral chanuletik. Mu chlaj li bara vi'nale xchi'uk k'unk'un sbut' sba li kuxle<PERSON>le.", "options.directionalAudio": "Rireksional nuk'ilal", "options.directionalAudio.off.tooltip": "Bats'i nuk'ilal estereo.", "options.directionalAudio.on.tooltip": "Tstunes rireksional nuk'ilal ta HRTF sventa slekubtas li simulasione ta nuk'ilal 3D. Sk'an hardware nuk'ilal xu' xtunesat xchi'uk HRTF, xchi'uk mas lek cha'i xchi'uk chikintak'inetik.", "options.discrete_mouse_scroll": "Ts'ijil bak'el", "options.entityDistanceScaling": "Snamalik entiraletik", "options.entityShadows": "Yaxinalik entiraletik", "options.font": "Ts'ib...", "options.font.title": "St'ujeltak ts'ib", "options.forceUnicodeFont": "Ts'ib unicode", "options.fov": "<PERSON><PERSON><PERSON> <PERSON>'elel", "options.fov.max": "<PERSON><PERSON>al", "options.fov.min": "Bats'i", "options.fovEffectScale": "Yejektotak osil k'elel", "options.fovEffectScale.tooltip": "Chchapan k'uxi sjel sba li osil k'elele k'alal oy yejektotak.", "options.framerate": "%s", "options.framerateLimit": "Ts'ak yu'un FPS", "options.framerateLimit.max": "Mu xchi'uk ts'ak", "options.fullscreen": "Ts'akal pantaya", "options.fullscreen.current": "Ta tana", "options.fullscreen.entry": "%s×%s ta %s Hz (%s bit)", "options.fullscreen.resolution": "Sresolusion ts'akal pantaya", "options.fullscreen.unavailable": "<PERSON>'abal", "options.gamma": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.gamma.default": "Bats'i", "options.gamma.max": "Nop'ol", "options.gamma.min": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON><PERSON>", "options.glintSpeed.tooltip": "<PERSON><PERSON><PERSON> li yanilal snop'olalik kapbil k'usitike.", "options.glintStrength": "<PERSON><PERSON>", "options.glintStrength.tooltip": "Chchapan li sakilal snop'olalik kapbil k'usitike.", "options.graphics": "Bik'it lok'taeletik", "options.graphics.fabulous": "¡Alak' sba!", "options.graphics.fabulous.tooltip": "Tstunesik j-ak'-axinal pantayaetik li bik'it lok'taeletike %s sventa slok'taik li osile, li to<PERSON>, xchi'uk li ts'ubilale ta spat sakil kuboetik xchi'uk vo'.\nLi' ep syaintas nan li srentimientoik rispositivoetike xu' ch-ich'atik xchi'uk k'alal stunes pantayaetik ta 4K.", "options.graphics.fancy": "<PERSON><PERSON>' sba", "options.graphics.fancy.tooltip": "Tsko'oltasik rentimiento xchi'uk kaliral ta ep rispositivoetik li bik'it lok'taeletike alak' sbaik. \nMu chvinajik nan li osile, li to<PERSON>, xchi'uk li ts'ubilale ta spat sakil kuboetik o vo'.", "options.graphics.fast": "Anil", "options.graphics.fast.tooltip": "Tsbik'tajes yepal vo' xchi'uk taiv chvinajik ta k'elel li anil bik'it lok'taeletike.\nChtup'ik li ejekto sakilaletike sventa kuboetik jech k'ucha'al analetik.", "options.graphics.warning.accept": "Xyaket mu xchi'uk koltael", "options.graphics.warning.cancel": "<PERSON><PERSON><PERSON><PERSON>", "options.graphics.warning.message": "La jtakutik atarjeta vireo mu ch'unat yu'un li t'uj bik'it lok'taele %s.\n\nXu' mu xana' li mantal li'e xchi'uk xayaket, pe mu chak'bekutik koltael arispositivo k'alal chatunes li bik'it lok'taeletike %s.", "options.graphics.warning.renderer": "Jlekubtas-imajen tabil: [%s]", "options.graphics.warning.title": "Mu ch'unbil rispositivo ta bik'it lok'taeletik", "options.graphics.warning.vendor": "Jmeltsanvanej tabil: [%s]", "options.graphics.warning.version": "Version OpenGL tabil: [%s]", "options.guiScale": "<PERSON><PERSON><PERSON><PERSON><PERSON> interpas", "options.guiScale.auto": "Ta stuk", "options.hidden": "<PERSON><PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "Tsnak' anjeletik", "options.hideLightningFlashes.tooltip": "<PERSON> chak' chak'beik xojobal vinajel li anjeletike. Pe ta xvinajik to ta k'elel.", "options.hideMatchedNames": "Tsna<PERSON>' yu'un biil", "options.hideMatchedNames.tooltip": "Ta stakik nan mantaletik ta chopol pormatoetik li servilortakik yoxibaletike.\nK'alal stsan sba, ta xchol sbaik yu'un sbiik ta lo'il li nak'al jtajimoletike.", "options.hideSplashTexts": "Tsnak' k'uk elanuk ts'ibetik", "options.hideSplashTexts.tooltip": "Tsnak' li k'anal k'uk elanuk ts'ibetike ta ba'yel menu.", "options.inactivityFpsLimit": "Tsbik'tajes FPS mi", "options.inactivityFpsLimit.afk": "Batem xa", "options.inactivityFpsLimit.afk.tooltip": "<PERSON><PERSON><PERSON><PERSON> li ya<PERSON>lal imajene 30 to k'alal mu yakal chich' spaselal jtajimol li tajimole ta mas ti jun minutoe. Mas spajes 10 to ta spat 9 minuto mas.", "options.inactivityFpsLimit.minimized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.inactivityFpsLimit.minimized.tooltip": "Tsbik'tajes li yanilal imajene k'alal chbik'taj li ventana tajimole.", "options.invertMouse": "Tsvalk'un ch'o", "options.japaneseGlyphVariants": "Yantik japon-xotetik", "options.japaneseGlyphVariants.tooltip": "Tstunes yantik japon-xotetik ta CJK ta bats'i ts'ib.", "options.key.hold": "<PERSON><PERSON><PERSON>'lin", "options.key.toggle": "<PERSON><PERSON><PERSON><PERSON>", "options.language": "K'op...", "options.language.title": "K'op", "options.languageAccuracyWarning": "(Jayibuk jelubtaseletik mu lekik nan ta sjo'vinik svo'vinikalil)", "options.languageWarning": "Jayibuk jelubtaseletik mu lekik nan ta sjo'vinik svo'vinikalil", "options.mainHand": "Ba'yel k'obil", "options.mainHand.left": "Ts'et k'obil", "options.mainHand.right": "<PERSON><PERSON>'i k'obil", "options.mipmapLevels": "Snivel k'unibtasel", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "Pixolil", "options.modelPart.jacket": "Xaki<PERSON>", "options.modelPart.left_pants_leg": "Ts'et o'il", "options.modelPart.left_sleeve": "Ts'et manka", "options.modelPart.right_pants_leg": "<PERSON><PERSON>'i o'il", "options.modelPart.right_sleeve": "Bats'i manka", "options.mouseWheelSensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON> bak'el", "options.mouse_settings": "St'ujeltak ch'o...", "options.mouse_settings.title": "St'ujeltak ch'o", "options.multiplayer.title": "Tajinebal...", "options.multiplier": "%sx", "options.music_frequency": "Stolalil sonal", "options.music_frequency.constant": "<PERSON><PERSON>", "options.music_frequency.default": "Bats'i", "options.music_frequency.frequent": "So<PERSON><PERSON>", "options.music_frequency.tooltip": "Ta sjel k'uxi tol chtij li sonale k'alal chatajin.", "options.narrator": "<PERSON><PERSON><PERSON> lo'ilaj<PERSON>", "options.narrator.all": "<PERSON><PERSON>", "options.narrator.chat": "Chal lo'il", "options.narrator.notavailable": "<PERSON>'abal", "options.narrator.off": "MO'OJ", "options.narrator.system": "Chal sistema", "options.notifications.display_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>l", "options.notifications.display_time.tooltip": "Chchapan k'uxi xjalijik ta pantaya li aleletike chvinajik ta k'elel.", "options.off": "MO'OJ", "options.off.composed": "%s: M<PERSON><PERSON><PERSON><PERSON>", "options.on": "TANA", "options.on.composed": "%s: TANA", "options.online": "Ta internet...", "options.online.title": "St'ujeltakik tajimoletik ta internet", "options.onlyShowSecureChat": "Ja' no'ox mu xi'el mantaletik", "options.onlyShowSecureChat.tooltip": "Ja' no'ox chak' ta ilel k'elanbil mantaletik takbil yu'un jtajimoletik, mu jelbilik to'ox.", "options.operatorItemsTab": "K'usitik yu'un jchapanvanej", "options.particles": "Ts'ubilaletik", "options.particles.all": "Skotol", "options.particles.decreased": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.particles.minimal": "Minimo", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "<PERSON><PERSON><PERSON><PERSON><PERSON> set'eletik", "options.prioritizeChunkUpdates.byPlayer": "<PERSON>'un pasel", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Jayibuk paseletik ta yut jset' ta stsobik li set'el le'e ta stukik. Li' tskap ek li ak'-kuboe xchi'uk li lilin-kuboe.", "options.prioritizeChunkUpdates.nearby": "S<PERSON><PERSON> ora", "options.prioritizeChunkUpdates.nearby.tooltip": "Ta xtusiik ta stukik ta skotol ora li tijil set'eletike. Li' tsyaintas nan li srentimiento tajimole k'alal ch-ak'atik o lilinatik li kuboetike.", "options.prioritizeChunkUpdates.none": "Ta noetik", "options.prioritizeChunkUpdates.none.tooltip": "Chtusiik ta ko'ol noetik li tijil set'eletike. Li' ta xak' nan ta ilel ch'oj k'eleletike mu xjalijik k'alal chalilin kuboetik.", "options.rawMouseInput": "<PERSON><PERSON><PERSON><PERSON> ochebal", "options.realmsNotifications": "Aleletik yu'un Realms", "options.realmsNotifications.tooltip": "Ta sa' aleletik xchi'uk ik'vaneletik yu'un Realms ta ba'yel menu xchi'uk chak' ta ilel slok'taeltak ta voton ta Realms.", "options.reducedDebugInfo": "Tsbik'tajes <PERSON>lal F3", "options.renderClouds": "Toketik", "options.renderCloudsDistance": "<PERSON><PERSON><PERSON> tok", "options.renderDistance": "<PERSON><PERSON><PERSON>", "options.resourcepack": "Pakete rekursoetik...", "options.rotateWithMinecart": "Joyobajel xchi'uk vakonetik", "options.rotateWithMinecart.tooltip": "Cha<PERSON>' mi ch<PERSON>aj li sk'elel jtajimo<PERSON> k'alal yakal tskajlibin jkot vakon. Ja' no'ox xu' xtunesat ta balumiletik xchi'uk li t'ujele ta preva ''Slekubtaselik vakonetik'' t<PERSON><PERSON>.", "options.screenEffectScale": "Ejektoetik ta bikbunajesel", "options.screenEffectScale.tooltip": "Tsjel li yipalik ejektoetike yu'un nether-ochebaletik xchi'uk me' viniketik.\nMi pek'el li to<PERSON>, ta sk'exta sba xchi'uk jun yaxal bonolil li yejektoik me' viniketike.", "options.sensitivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sensitivity.max": "¡¡¡ANIL TAJMEK!!!", "options.sensitivity.min": "*j<PERSON>'<PERSON><PERSON><PERSON>*", "options.showNowPlayingToast": "Chak' ta ilel li yalel sonale", "options.showNowPlayingToast.tooltip": "<PERSON><PERSON><PERSON> <PERSON> ilel jun alel k'alal chlik ta tijel jun son. <PERSON><PERSON>j ek ta smenu pajel.", "options.showSubtitles": "Bik'it ts'ib", "options.simulationDistance": "Simulasion", "options.skinCustomisation": "K'uxi chilikun...", "options.skinCustomisation.title": "K'uxi chilikun", "options.sounds": "Sonal xchi'uk nuk'ilaletik...", "options.sounds.title": "T'ujeletik ta sonal xchi'uk nuk'ilal", "options.telemetry": "Telemetria...", "options.telemetry.button": "Tsob-<PERSON><PERSON>", "options.telemetry.button.tooltip": "%s: tskap no'ox li k'anbil alelale.\n%s: tskap li st'ujelal xchi'uk li k'anbil alelaletike.", "options.telemetry.disabled": "Tubbil li telemetriae.", "options.telemetry.state.all": "<PERSON><PERSON><PERSON>", "options.telemetry.state.minimal": "Minimo", "options.telemetry.state.none": "<PERSON><PERSON><PERSON><PERSON>", "options.title": "T'ujeletik", "options.touchscreen": "Taktil pantaya", "options.video": "Stsineltak vireo...", "options.videoTitle": "Stsineltak vireo", "options.viewBobbing": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "options.visible": "<PERSON><PERSON><PERSON> ta k'elel", "options.vsync": "VSync", "outOfMemory.message": "Minecratf ch'abal xa yav.\n\nLi' k'ot nan ta pasele yu'un chopolaj li tajimole o yu'un mu ak'batik xe'el avil li arkumentoetike ta JVM yu'un ts'akel ta jimvanej.\n\nSventa mu chyaj li tajimole, sma<PERSON>j sba. La jk'ankutik jkolkutik li k'anbil avile sventa chasut ta ba'yel menu xchi'uk chasut ta tajimol, pe mu abtejem nan li'e.\n\nCha'likeso li tajimole mi chavil nixtok li mantal li'e, avo<PERSON><PERSON>.", "outOfMemory.title": "¡Ch'abal avil!", "pack.available.title": "Xu' xtunesat", "pack.copyFailure": "<PERSON><PERSON><PERSON> k'alal tslok'ta ox li paketeetike", "pack.dropConfirm": "¿Mi chak'an chakapbe li paketeetik li'e li Minecrafte?", "pack.dropInfo": "Jocho archivoetik li ta ventana li'e sventa chakap paketeetik", "pack.dropRejected.message": "Chopol paketeik ox li ocheletik li'e xchi'uk mu la slok'ta sbaik:\n%s", "pack.dropRejected.title": "Ts'ibaeletik mu paketeik", "pack.folderInfo": "(A<PERSON>'o li' li paketeetike)", "pack.incompatible": "Mu ts'ikbil", "pack.incompatible.confirm.new": "Pasat sventa jun mas ach' version yu'un Minecraft li pakete li'e xchi'uk mu ch-abtej nan lek.", "pack.incompatible.confirm.old": "Pasat sventa jun mas poko' version yu'un Minecraft li pakete li'e xchi'uk mu ch-abtej nan lek.", "pack.incompatible.confirm.title": "¿Mi melel chak'an chavinajes li pakete li'e?", "pack.incompatible.new": "(<PERSON><PERSON><PERSON> sventa jun mas ach' version yu'un Minecraft)", "pack.incompatible.old": "(<PERSON><PERSON><PERSON> sventa jun mas poko' version yu'un Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON><PERSON>m li karp<PERSON>e", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "ka<PERSON><PERSON>", "pack.source.feature": "<PERSON>rak<PERSON><PERSON><PERSON>", "pack.source.local": "tijil", "pack.source.server": "servilor", "pack.source.world": "balumil", "painting.dimensions": "%s×%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "J-albania", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Pan<PERSON>", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Muk'ta nail", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Lek la sjip bombaetik ta sba li objetivoe", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON> roxa", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Bakil jolil ta k'ok'", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Ch'enal mut", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Kotan", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "¿Mi li' oyote, toti<PERSON> Courbet?", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "<PERSON><PERSON>", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Jpas-k'opetik", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "<PERSON><PERSON>", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "K'ok'", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "Me'on", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lumil tok", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Posporo", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Jnopnunel", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "Kuxkux xchi'uk elmonixetik", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "<PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Lok'tael chitom", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "J-ak' ta ilel", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "Li pisinae", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Xanbal ta yaxaltik", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "<PERSON><PERSON>' muk'ta nab", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "<PERSON>j xi'el chukel", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Bakil jolil xchi'uk roxaetik", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "<PERSON>pal xa", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunetik", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "Malk'ak'al", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Balak'vo'", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Lok'esbil", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "<PERSON> pojolale", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Jxanbal", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "<PERSON><PERSON><PERSON> bal<PERSON>l", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Vo'", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "<PERSON><PERSON>'", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "K'uk no'ox tos", "parsing.bool.expected": "<PERSON><PERSON>'an jun tojo<PERSON> booleano", "parsing.bool.invalid": "<PERSON><PERSON> tojo<PERSON>lal booleano: ''%s'' mu ja' ''true'' o ''false''", "parsing.double.expected": "<PERSON><PERSON>'an jun tojo<PERSON>lal cha'pasel", "parsing.double.invalid": "<PERSON><PERSON> to<PERSON>lal cha'pasel: %s", "parsing.expected": "Sk'an ''%s''", "parsing.float.expected": "Sk'an jun tojo<PERSON> ''float''", "parsing.float.invalid": "<PERSON><PERSON> to<PERSON> ''float'': %s", "parsing.int.expected": "Sk'an jun ts'akal atolal", "parsing.int.invalid": "Chopol ts'akal atolal: %s", "parsing.long.expected": "Sk'an jun tojolilal ''long''", "parsing.long.invalid": "<PERSON><PERSON> to<PERSON>lal ''long'': %s", "parsing.quote.escape": "Chopol ta jun karena xchi'uk smotsobal li sekuensia lok'ele ''\\%s''", "parsing.quote.expected.end": "Sk'an s<PERSON><PERSON><PERSON> s<PERSON>bal karena", "parsing.quote.expected.start": "Sk'an smotsobal ta slikeb karena", "particle.invalidOptions": "Mu xu' sk'elan li st'ujeltak ts'ubilale: %s", "particle.notFound": "Mu ojtikinbil ts'ubilal: %s", "permissions.requires.entity": "Sk'an jun entiral sventa chak' li' li mantal taje", "permissions.requires.player": "Sk'an jun jtajimol sventa chak' li' li mantal taje", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "K'alal ak'bil:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Mu ojtikinbil pretikaro: %s", "quickplay.error.invalid_identifier": "Mu xu' sta li balumile xchi'uk li tunesbil j-ojtikinvaneje", "quickplay.error.realm_connect": "Mu xu' sts'ak sba ta Realm", "quickplay.error.realm_permission": "Mu cha-ak'at ta ochel li ta Realm li'e", "quickplay.error.title": "<PERSON><PERSON><PERSON> k'alal ch-och ox ta anil tajimol", "realms.configuration.region.australia_east": "Ach' Gales ta <PERSON>, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brasil", "realms.configuration.region.central_india": "Intia", "realms.configuration.region.central_us": "Iowa, Tsobol Lumaletik", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, Tsobol Lumaletik", "realms.configuration.region.east_us_2": "Carolina ta Xokon <PERSON>, Tsobol Lumaletik", "realms.configuration.region.france_central": "Pransia", "realms.configuration.region.japan_east": "Ak'oltikal Japon", "realms.configuration.region.japan_west": "Olontikal Japon", "realms.configuration.region.korea_central": "Korea ta Xokon Vinajel", "realms.configuration.region.north_central_us": "Illinois, Tsobol Lumaletik", "realms.configuration.region.north_europe": "Irlanta", "realms.configuration.region.south_central_us": "Texas, Tsobol Lumaletik", "realms.configuration.region.southeast_asia": "Sinkapur", "realms.configuration.region.sweden_central": "Suesia", "realms.configuration.region.uae_north": "Tsobol J-arabia Osiletik (EAU)", "realms.configuration.region.uk_south": "Xokon vinajel ta Inklatera", "realms.configuration.region.west_central_us": "Utah, Tsobol Lumaletik", "realms.configuration.region.west_europe": "Olanta", "realms.configuration.region.west_us": "California, Tsobol Lumaletik", "realms.configuration.region.west_us_2": "Washington, Tsobol Lumaletik", "realms.configuration.region_preference.automatic_owner": "Ta stuk (yosil yajval Realm)", "realms.configuration.region_preference.automatic_player": "Ta stuk (ta buch'u och ta ba'yel)", "realms.missing.snapshot.error.text": "Minecraft Realms ja' mu ch'unbil ta ach'ubtas<PERSON>el prevaetik", "recipe.notFound": "Mu ojtikinbil reseta: %s", "recipe.toast.description": "K'elo li avun resetae", "recipe.toast.title": "¡Ach' resetaetik!", "record.nowPlaying": "Yakal chtij: %s", "recover_world.bug_tracker": "Chal ch'ay-o'ontonal", "recover_world.button": "Tsk'an xcha'vinajes", "recover_world.done.failed": "<PERSON><PERSON><PERSON> k'alal chcha'vinajes ox jun poko' estaro.", "recover_world.done.success": "¡Lek la stam nixtok!", "recover_world.done.title": "<PERSON><PERSON><PERSON><PERSON>", "recover_world.issue.missing_file": "Sk'an jun archivo", "recover_world.issue.none": "Ch'abal k'opetik", "recover_world.message": "K'otik ta pasel li k'opetik li'e k'alal sk'an ox sk'el li skaperta balumile ''%s''.\nTana nan ta smeltsan li balumile ta jun mas poko' estaro o ta xal smul li ch'ay-o'ontonal li'e ta sts'ibael ch'ay-o'ontonal.", "recover_world.no_fallback": "<PERSON><PERSON><PERSON><PERSON> estaro<PERSON>k sventa chmeltsajik", "recover_world.restore": "Tsk'an smeltsan", "recover_world.restoring": "Yakal tsk'an smeltsan balumil...", "recover_world.state_entry": "Estaro ta %s: ", "recover_world.state_entry.unknown": "mu ojti<PERSON><PERSON>", "recover_world.title": "Cho<PERSON><PERSON> k'alal tsvinajes ox li balumile", "recover_world.warning": "Cho<PERSON><PERSON> k'alal tsvinajes ox li sresumen balumile", "resourcePack.broken_assets": "SOKEM REKURSOETIK TABIL", "resourcePack.high_contrast.name": "Toyol kontraste", "resourcePack.load_fail": "<PERSON><PERSON><PERSON> k'alal chcha'vinajes ox li rekursoe", "resourcePack.programmer_art.name": "Arte mantaltak'inal", "resourcePack.runtime_failure": "La sta jun chopolal ta yut pakete rekurso", "resourcePack.server.name": "Sbajbil rekursotak balumil", "resourcePack.title": "Tst'uj pakete rekursoetik", "resourcePack.vanilla.description": "Li bats'i vinajele yu'un Minecraft", "resourcePack.vanilla.name": "Bats'i", "resourcepack.downloading": "Yakal tsyales pakete rekurso", "resourcepack.progress": "Yakal tsyales archivo (%s MB)...", "resourcepack.requesting": "Yakal ch<PERSON>'anvan...", "screenshot.failure": "Mu xu' st'uj li slok'ol pantayae: %s", "screenshot.success": "Slok'ol pantaya k'ejbil jech k'ucha'al %s", "selectServer.add": "<PERSON><PERSON><PERSON> servilor", "selectServer.defaultName": "Servilor ta Minecraft", "selectServer.delete": "Tstup'", "selectServer.deleteButton": "Tstup'", "selectServer.deleteQuestion": "¿Mi melel chak'an chatup' li servilor li'e?", "selectServer.deleteWarning": "''%s'' ta xch'ay... ¡ta jech'el! (¡al!)", "selectServer.direct": "Tuk'il ts'akel", "selectServer.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectServer.hiddenAddress": "(Nak'al IP)", "selectServer.refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectServer.select": "Ch-och ta servilor", "selectWorld.access_failure": "<PERSON><PERSON><PERSON> k'alal ch-och ox ta balumil", "selectWorld.allowCommands": "Chak' mantaletik", "selectWorld.allowCommands.info": "Mantaletik jech' k'ucha'al /gamemode, /experience", "selectWorld.allowCommands.new": "Chak' mantaletik", "selectWorld.backupEraseCache": "Tstup' alelal<PERSON>k ta kache", "selectWorld.backupJoinConfirmButton": "Tspas lok'tael xchi'uk svinajes", "selectWorld.backupJoinSkipButton": "¡Ta jna' li k'usi yakal ta jpase!", "selectWorld.backupQuestion.customized": "Mu xa ch'unatik li jelbil balumiletike", "selectWorld.backupQuestion.downgrade": "Mu xu' s<PERSON><PERSON><PERSON> balumiletik", "selectWorld.backupQuestion.experimental": "Mu ch'unatik li balumiletike xchi'uk t'uj-vinajeseletik ta preva", "selectWorld.backupQuestion.snapshot": "¿Mi melel chak'an chavinajes li balumil li'e?", "selectWorld.backupWarning.customized": "<PERSON><PERSON> chopol, mu ta jch'unkutik jelbil balumiletik li ta version yu'un Minecraft li'e. <PERSON>' to chavinajes xchi'uk chatajin jech k'ucha'al ta ba'yel, pe ta xvinaj jech k'ucha'al jun bats'i balumil li ach' vinajesbil osile. ¡Ak'unkutik ta perton!", "selectWorld.backupWarning.downgrade": "Latajin ta slajeb velta li balumil li'e ta version %s, te oyot ta version %s. Mi chamalubtas jun balumile ja' xu' spas sokel. Mu xu' chkalkutik ta xvinaj o ta x-abtej. Mi chak'an to xaya<PERSON>, paso me jun lok'tael, avokoluk.", "selectWorld.backupWarning.experimental": "Tstunes t'ujeletik ta preva li balumil li'e chpajik nan ta abtejel ta k'uk elanuk ora. Mu xu' xkalkutik ja' ta xvinaj o x-abtej. ¡Bijan me!", "selectWorld.backupWarning.snapshot": "Latajin ta slajeb velta li balumil li'e ta version %s; te oyot tana ta version %s. ¡Paso me jun lok'tael ta ba'yel sokuk li balumile!", "selectWorld.bonusItems": "<PERSON><PERSON> matanil", "selectWorld.cheats": "Mantaletik", "selectWorld.commands": "Mantaletik", "selectWorld.conversion": "¡Sk'an chk'atajesat!", "selectWorld.conversion.tooltip": "Sk'an chjam li balumil li'e ta jun poko' version (j<PERSON> k'ucha'al 1.6.4) sventa lek chk'atajesat", "selectWorld.create": "<PERSON><PERSON><PERSON> jun ach' balumil", "selectWorld.customizeType": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.dataPacks": "Pakete alelaletik", "selectWorld.data_read": "Yakal tsk'el li ya<PERSON>lal<PERSON>k balumile...", "selectWorld.delete": "Tstup'", "selectWorld.deleteButton": "Tstup'", "selectWorld.deleteQuestion": "¿Mi melel chak'an chatup' li balumil li'e?", "selectWorld.deleteWarning": "''%s'' ta xch'ay... ¡ta jech'el! (¡al!)", "selectWorld.delete_failure": "<PERSON><PERSON><PERSON> k'alal stup' ox li balumile", "selectWorld.edit": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.backup": "Tspas lok'tael", "selectWorld.edit.backupCreated": "Lok'tabil: %s", "selectWorld.edit.backupFailed": "Cho<PERSON><PERSON> li lok'taele", "selectWorld.edit.backupFolder": "Tsjam karpeta lok'taeletik", "selectWorld.edit.backupSize": "muk'ulil: %s MB", "selectWorld.edit.export_worldgen_settings": "Tstsak t'ujvanel vinajeseletik", "selectWorld.edit.export_worldgen_settings.failure": "<PERSON><PERSON><PERSON> li takele", "selectWorld.edit.export_worldgen_settings.success": "Takbil", "selectWorld.edit.openFolder": "<PERSON><PERSON><PERSON><PERSON> skarpeta bal<PERSON>l", "selectWorld.edit.optimize": "Tslekubtas balumil", "selectWorld.edit.resetIcon": "Ta sutes ikono", "selectWorld.edit.save": "Tsk'ej", "selectWorld.edit.title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "selectWorld.enterName": "<PERSON><PERSON> bal<PERSON>l", "selectWorld.enterSeed": "Be<PERSON>'il sventa svinajes li balumile", "selectWorld.experimental": "Ta preva", "selectWorld.experimental.details": "Aleletik", "selectWorld.experimental.details.entry": "Sk'an karakteristikaetik ta preva: %s", "selectWorld.experimental.details.title": "K'anvanel karakteristikaetik ta preva", "selectWorld.experimental.message": "¡Bijan me! \nSk'an tuneletik chmeltsajik to li tsinel li'e. Ta xmak nan, sok nan o mu ch-abtej nan li abalumile ta ach'ubtaseletik chvulik to.", "selectWorld.experimental.title": "Al-karakteristika ta preva", "selectWorld.experiments": "Prevaetik", "selectWorld.experiments.info": "Xu' x-elanik ach' karakteristikaetik ta patil li prevaetike. Bijan me yu'un chpajik nan ta lek abtejel jayibuk k'usitik. Mu xu' tubatik li prevaetike mi pasat xa li balumile.", "selectWorld.futureworld.error.text": "Oy k'usi ch'ay xa'i k'alal svinajes ox jun balumil ta jun mas ach' version. Li'e xi'el ox, ak'unkutik ta perton yu'un mu abtej.", "selectWorld.futureworld.error.title": "¡Oy k'usi ch'ay xa'i!", "selectWorld.gameMode": "<PERSON><PERSON>", "selectWorld.gameMode.adventure": "Aventura", "selectWorld.gameMode.adventure.info": "<PERSON><PERSON> k'u<PERSON>'al t<PERSON> k<PERSON>, pe mu xu' x-ak'atik o xlokesatik li kuboetike.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON> k'u<PERSON>'al tsatsal balumil, pe mu", "selectWorld.gameMode.adventure.line2": "xu' chavak' o chalokes li kuboetike", "selectWorld.gameMode.creative": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.creative.info": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, xchi'uk ju'lajan mu xchi'uk ts'aketik. <PERSON><PERSON> <PERSON><PERSON><PERSON>, oy amaterialtak mu chlajik xchi'uk mu xu' xayaintasat yu'un monstroetik.", "selectWorld.gameMode.creative.line1": "Rekursoetik mu chlajik, kolem vilel", "selectWorld.gameMode.creative.line2": "xchi'uk lilin kuboetik ta anil", "selectWorld.gameMode.hardcore": "<PERSON><PERSON>", "selectWorld.gameMode.hardcore.info": "<PERSON><PERSON> k'u<PERSON>'al tsatsal k<PERSON>, pe tsatsalil ak'bil ta tsatsal. Mu xu' xacha'vinaj mi chalaje.", "selectWorld.gameMode.hardcore.line1": "<PERSON><PERSON> k'u<PERSON>'al tsatsal k<PERSON>, pe", "selectWorld.gameMode.hardcore.line2": "makbil ta tsatsal xchi'uk jun no'ox kuxlejal", "selectWorld.gameMode.spectator": "Jk'elvanej", "selectWorld.gameMode.spectator.info": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, pe mu xapik.", "selectWorld.gameMode.spectator.line1": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, pe mu xapik", "selectWorld.gameMode.survival": "<PERSON><PERSON><PERSON> k<PERSON>l", "selectWorld.gameMode.survival.info": "<PERSON><PERSON>'lano jun mu ojtikinbil balumil ta bu xu' xava'an, xata<PERSON>, xameltsan xchi'uk xapasbik k'op li monstroetike.", "selectWorld.gameMode.survival.line1": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, tamo", "selectWorld.gameMode.survival.line2": "k<PERSON><PERSON><PERSON><PERSON>, ve'an xchi'uk bijan me", "selectWorld.gameRules": "Sleytak tajimol", "selectWorld.import_worldgen_settings": "Tstak t'ujeletik", "selectWorld.import_worldgen_settings.failure": "Cho<PERSON><PERSON> k'alal stak ox li t'ujeletike", "selectWorld.import_worldgen_settings.select_file": "T'ujo jun archivo t'ujel (.json)", "selectWorld.incompatible.description": "Mu xu' xjam ta version taje li balumil li'e.\nLatajin ta slajeb velta ta version %s.", "selectWorld.incompatible.info": "Mu ts'ikbil version: %s", "selectWorld.incompatible.title": "Mu ts'ikbil version", "selectWorld.incompatible.tooltip": "Mu xu' xjam li balumil li'e yu'un pasat ta jun mu ts'ikbil version.", "selectWorld.incompatible_series": "Pasbil ta jun mu ts'ikbil version", "selectWorld.load_folder_access": "¡Mu xu' sk'el o ch-och ta karpeta ta bu k'ejbilik li balumiletike!", "selectWorld.loading_list": "Yakal tsvinajes li lista balumile", "selectWorld.locked": "Mak yu'un yan jamal sesion yu'un Minecraft", "selectWorld.mapFeatures": "Tsva'an estrukturaetik", "selectWorld.mapFeatures.info": "Bik'it lume<PERSON>, sokvarkoetik, ets.", "selectWorld.mapType": "<PERSON><PERSON> b<PERSON>l", "selectWorld.mapType.normal": "Bats'i", "selectWorld.moreWorldOptions": "Mas st'ujeltak balumil...", "selectWorld.newWorld": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>l", "selectWorld.recreate": "<PERSON><PERSON>'pas", "selectWorld.recreate.customized.text": "Mu xa chts'ikatik li ta sversion Minecraft li'e li jelbil balumiletike. Xu' jk'ankutik jcha'paskutik xchi'uk li ko'ol bek'ile xchi'uk li ko'ol karakteristikaetike, pe ta xvinaj jech k'ucha'al jun bats'i balumil. ¡Ak'bunkutik perton yu'un k'opetik!", "selectWorld.recreate.customized.title": "Mu xa chts'ikatik li jelbil balumiletike", "selectWorld.recreate.error.text": "Oy k'usi ch'ay xa'i k'alal sk'an ox chcha'pas li balumile.", "selectWorld.recreate.error.title": "¡Oy k'usi ch'ay xa'i!", "selectWorld.resource_load": "Yakal chchapan rekursoetik...", "selectWorld.resultFolder": "Ta sk'ej sba ta:", "selectWorld.search": "ta sa' balumiletik", "selectWorld.seedInfo": "Komeso pojol sventa chvinaj stuk jbej bek'il", "selectWorld.select": "Chtajin ta t'ujbil balumil", "selectWorld.targetFolder": "Karpeta k'ejel: %s", "selectWorld.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> bal<PERSON>l", "selectWorld.tooltip.fromNewerVersion1": "¡K'ejat ta jun mas ach' version li balumile,", "selectWorld.tooltip.fromNewerVersion2": "mi chavinajes li ta version avie, ta sok nan!", "selectWorld.tooltip.snapshot1": "Mu xach'ay ta jol ta slok'tael li balumil li'e", "selectWorld.tooltip.snapshot2": "ta ba'yel chavinajes li ta preva li'e.", "selectWorld.unable_to_load": "Mu xu' xvinajik li balumiletike", "selectWorld.version": "Version:", "selectWorld.versionJoinButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> ono'ox", "selectWorld.versionQuestion": "¿Mi melel chak'an chavinajes li balumil li'e?", "selectWorld.versionUnknown": "mu ojti<PERSON><PERSON>", "selectWorld.versionWarning": "¡K'ejat ta slajeb velta ta version %s li balumil li'e! ¡Mi chvinaj li ta version li'e, ta sok nan!", "selectWorld.warning.deprecated.question": "Jayibuk karakteristikaetik tunesbil ja' molik xchi'uk ta xpaj ta abtejel ta patil. ¿Mi chak'an xayaket?", "selectWorld.warning.deprecated.title": "¡Bijan me! Yakal tstunesik poko' karakteristikaetik li tsineletik li'e", "selectWorld.warning.experimental.question": "Ja' prevaik xchi'uk chpajik nan ta abtejel ta ora no'ox li tsineletik li'e. ¿Mi chak'an xayaket?", "selectWorld.warning.experimental.title": "¡Bijan me! Yakal tstunesik karakteristikaetik ta preva li tsineletik li'e", "selectWorld.warning.lowDiskSpace.description": "Ch'abal xa ep avil ta arispositivo. Ta sok nan li abalumile mi ch'abal xa avil ta risko k'alal yakal chatajin.", "selectWorld.warning.lowDiskSpace.title": "¡<PERSON>ijan me! ¡Oy jutuk avil ta rispositivo!", "selectWorld.world": "<PERSON><PERSON><PERSON>", "sign.edit": "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>", "sleep.not_possible": "Ch'abal xe'el vayem jtajimoletik sventa sujik li ak'obale", "sleep.players_sleeping": "%s/%s jtajimoletik yakal chvayik", "sleep.skipping_night": "<PERSON>kal chavay li ak'obal li'e", "slot.only_single_allowed": "Ch-ak'atik no'ox stukik aviletik, la yich': %s", "slot.unknown": "Mu ojtikinbil avil: %s", "snbt.parser.empty_key": "Mu xu' pojoluk li yavi'e", "snbt.parser.expected_binary_numeral": "Sk'an ox jun binario atolal", "snbt.parser.expected_decimal_numeral": "Sk'an jun resimal atolal", "snbt.parser.expected_float_type": "Sk'an ox jun atolal xchi'uk vilel punto", "snbt.parser.expected_hex_escape": "Sk'an ox jun karakter literal ta natil %s", "snbt.parser.expected_hex_numeral": "Sk'an jun eksaresimal atolal", "snbt.parser.expected_integer_type": "Sk'an ox jun ts'akal atolal", "snbt.parser.expected_non_negative_number": "Sk'an ox jun atolal ma'uk nekativo", "snbt.parser.expected_number_or_boolean": "Sk'an jun atolal o tojolilal booleano", "snbt.parser.expected_string_uuid": "Sk'an jun karena xchi'uk jun lekil UUID", "snbt.parser.expected_unquoted_string": "Sk'an ox jun lekil karena mu xchi'uk smotsobal", "snbt.parser.infinity_not_allowed": "Mu x-ak'atik li atolaletik ti mu xlajike", "snbt.parser.invalid_array_element_type": "<PERSON><PERSON> tos chol elemento", "snbt.parser.invalid_character_name": "Chopol biilal karakter Unicode", "snbt.parser.invalid_codepoint": "Chopol karakter Unicode: %s", "snbt.parser.invalid_string_contents": "<PERSON><PERSON> k'usitik yu'un karena", "snbt.parser.invalid_unquoted_start": "Mu xu' xlikik xchi'uk atolaletik 0-9, + o - li karenaetike mu xchi'uk smotsobal", "snbt.parser.leading_zero_not_allowed": "Mu xu' xlikik xchi'uk 0 li resimal atolaletike", "snbt.parser.no_such_operation": "Ch'abal li operasion le'e: %s", "snbt.parser.number_parse_failure": "<PERSON><PERSON><PERSON> k'alal tspas ox analisar li atolale: ''%s''", "snbt.parser.undescore_not_allowed": "Mu x-ak'atik ta slikeb o ta slajeb jun atolal li olon ch'akbenaletike", "soundCategory.ambient": "Ambiente", "soundCategory.block": "Kuboetik", "soundCategory.hostile": "Xi'el chanuletik", "soundCategory.master": "Ba'yel volumen", "soundCategory.music": "Sonal", "soundCategory.neutral": "Lekil chanuletik", "soundCategory.player": "Jtajimoletik", "soundCategory.record": "Sonal kaxaetik", "soundCategory.ui": "Interpas ta jtunesvanej", "soundCategory.voice": "Nuk'ilal xchi'uk k'opetik", "soundCategory.weather": "Osil", "spectatorMenu.close": "Tsmak menu", "spectatorMenu.next_page": "<PERSON>jina ta stuk'il", "spectatorMenu.previous_page": "<PERSON><PERSON><PERSON> ta pat", "spectatorMenu.root.prompt": "Net'o jbej tekla sventa chat'uj jun mantal, xchi'uk net'o nixtok sventa chatunes.", "spectatorMenu.team_teleport": "Tstak sba ta jun xkrixchano ekipo", "spectatorMenu.team_teleport.prompt": "T'ujo jun ekipo sventa chatak aba", "spectatorMenu.teleport": "Tstak sba ta jun jtajimol", "spectatorMenu.teleport.prompt": "T'u<PERSON> jun jtajimol sventa chatak aba", "stat.generalButton": "<PERSON><PERSON>", "stat.itemsButton": "K'usitik", "stat.minecraft.animals_bred": "Chanuletik ts'itesatik", "stat.minecraft.aviate_one_cm": "Namal ta end-xik'", "stat.minecraft.bell_ring": "Kampanaetik tijatik", "stat.minecraft.boat_one_cm": "<PERSON>al ta varko", "stat.minecraft.clean_armor": "K'os almaruraetik kusatik", "stat.minecraft.clean_banner": "Vanteraetik kusatik", "stat.minecraft.clean_shulker_box": "Kokonbil kaxaetik yu'un shulker", "stat.minecraft.climb_one_cm": "<PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON> la<PERSON>'ini", "stat.minecraft.damage_absorbed": "Ya<PERSON><PERSON>l ich'at", "stat.minecraft.damage_blocked_by_shield": "<PERSON><PERSON><PERSON><PERSON> pajesat yu'un eskuro", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON><PERSON> pasat (ich'bil)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> (ts'ikbil)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ya<PERSON>l", "stat.minecraft.damage_taken": "Ya<PERSON><PERSON>l ich'at", "stat.minecraft.deaths": "Ya<PERSON>lal<PERSON> lajelaletik", "stat.minecraft.drop": "K'usitik tenatik", "stat.minecraft.eat_cake_slice": "Xet'el pasteletik ve'atik", "stat.minecraft.enchant_item": "K'usitik kapatik", "stat.minecraft.fall_one_cm": "<PERSON><PERSON>", "stat.minecraft.fill_cauldron": "<PERSON><PERSON>ae<PERSON><PERSON> but'atik", "stat.minecraft.fish_caught": "Choyetik tsakatik", "stat.minecraft.fly_one_cm": "<PERSON><PERSON>", "stat.minecraft.happy_ghast_one_cm": "Namal ta muyubajem ghast", "stat.minecraft.horse_one_cm": "Namal ta ka'", "stat.minecraft.inspect_dispenser": "Pukobiletik k'elanatik", "stat.minecraft.inspect_dropper": "Tenvanobiletik k'elanatik", "stat.minecraft.inspect_hopper": "Malobiletik k'elanatik", "stat.minecraft.interact_with_anvil": "Velta ta bu la atunes yunkeetik", "stat.minecraft.interact_with_beacon": "Velta ta bu la atunes jaroetik", "stat.minecraft.interact_with_blast_furnace": "Velta ta bu la atunes muk'ta jornoetik", "stat.minecraft.interact_with_brewingstand": "Veltaetik ta bu la atunes chijobiletik", "stat.minecraft.interact_with_campfire": "Velta ta bu la atunes k'ok'etik", "stat.minecraft.interact_with_cartography_table": "Velta ta bu la atunes mexa lok'ta mapaetik", "stat.minecraft.interact_with_crafting_table": "Velta ta bu la atunes mexa meltsaneletik", "stat.minecraft.interact_with_furnace": "Velta ta bu la atunes jornoetik", "stat.minecraft.interact_with_grindstone": "Velta ta bu la atunes juxumetik", "stat.minecraft.interact_with_lectern": "Velta ta bu la atunes atiriletik", "stat.minecraft.interact_with_loom": "Velta ta bu la atunes abtejebal pok'etik", "stat.minecraft.interact_with_smithing_table": "Velta ta bu la atunes mexa tak'inaletik", "stat.minecraft.interact_with_smoker": "Velta ta bu la atunes jch'atavanejetik", "stat.minecraft.interact_with_stonecutter": "Velta ta bu la atunes javtonetik", "stat.minecraft.jump": "Biteletik", "stat.minecraft.leave_game": "Veltaetik lalok' li ta<PERSON>", "stat.minecraft.minecart_one_cm": "<PERSON><PERSON> ta vakon", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON> milatik", "stat.minecraft.open_barrel": "Bariletik jamatik", "stat.minecraft.open_chest": "Kaxaetik jamatik", "stat.minecraft.open_enderchest": "<PERSON><PERSON> jamatik", "stat.minecraft.open_shulker_box": "<PERSON><PERSON> ka<PERSON>etik yu'un shulker", "stat.minecraft.pig_one_cm": "Namal ta chitom", "stat.minecraft.play_noteblock": "Sonal kuboetik tijatik", "stat.minecraft.play_record": "Riskoetik tijatik", "stat.minecraft.play_time": "Sk'ak'al<PERSON> la<PERSON>", "stat.minecraft.player_kills": "Jtajimoletik milatik", "stat.minecraft.pot_flower": "Te'etik ak'atik ta masetaetik", "stat.minecraft.raid_trigger": "Pask'opetik evanatik", "stat.minecraft.raid_win": "Pask'opetik ta bu la apas kanal", "stat.minecraft.sleep_in_bed": "A<PERSON>'obal<PERSON>k ta bu lavay", "stat.minecraft.sneak_time": "Sk'ak'alil lat'ini", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON>", "stat.minecraft.strider_one_cm": "Namal ta jxanbal tsuk'", "stat.minecraft.swim_one_cm": "<PERSON><PERSON> lanux", "stat.minecraft.talked_to_villager": "Lo'iletik xchi'uk jbik'it lumetik", "stat.minecraft.target_hit": "Rianaetik majatik ta yutik", "stat.minecraft.time_since_death": "Sk'a<PERSON>'al<PERSON> k'u sja<PERSON>j", "stat.minecraft.time_since_rest": "Sk'ak'alil k'u sjalil la akux", "stat.minecraft.total_world_time": "Sk'ak'alil xchi'uk jamal balumil", "stat.minecraft.traded_with_villager": "Chonolajeletik xchi'uk jbik'it lumetik", "stat.minecraft.trigger_trapped_chest": "Kaxa pets'etik tsanatik", "stat.minecraft.tune_noteblock": "Sonal kuboetik tsinatik", "stat.minecraft.use_cauldron": "Vo' tsakat ta kalteraetik", "stat.minecraft.walk_on_water_one_cm": "<PERSON>al laxanav ta sba vo'", "stat.minecraft.walk_one_cm": "<PERSON><PERSON> la<PERSON>", "stat.minecraft.walk_under_water_one_cm": "Namal laxanav ta yolon vo'", "stat.mobsButton": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.broken": "Veltaetik sokem", "stat_type.minecraft.crafted": "Veltaetik meltsanat", "stat_type.minecraft.dropped": "Veltaetik jimat", "stat_type.minecraft.killed": "La amil %s", "stat_type.minecraft.killed.none": "<PERSON><PERSON><PERSON>uk bu amiloj %s", "stat_type.minecraft.killed_by": "%s la smilot %s velta", "stat_type.minecraft.killed_by.none": "%s mu'yuk bu smi<PERSON><PERSON>t", "stat_type.minecraft.mined": "Veltaetik <PERSON>umaj", "stat_type.minecraft.picked_up": "Veltaetik tamat", "stat_type.minecraft.used": "Veltaetik tunesat", "stats.none": "-", "structure_block.button.detect_size": "TSTA", "structure_block.button.load": "TSVINAJES", "structure_block.button.save": "TSK'EJ", "structure_block.custom_data": "Sjelbil bi dataTag", "structure_block.detect_size": "<PERSON><PERSON> muk'ulil xchi'uk posision:", "structure_block.hover.corner": "Chikin na: %s", "structure_block.hover.data": "Alelaletik: %s", "structure_block.hover.load": "Tsvinajes: %s", "structure_block.hover.save": "Tsk'ej: %s", "structure_block.include_entities": "Tskap entiraletik:", "structure_block.integrity": "Sts'akalil xchi'uk sbek' estruktura", "structure_block.integrity.integrity": "Sts'akalil estruk<PERSON>", "structure_block.integrity.seed": "Sbek' estruktura", "structure_block.invalid_structure_name": "''%s'' ja' jun chopol biil sventa estruktura", "structure_block.load_not_found": "Mu xu' xtunesat li estrukturae ''%s''", "structure_block.load_prepare": "Sposision estruktura ''%s'' chapal", "structure_block.load_success": "Estruktura ''%s'' vinajesbil", "structure_block.mode.corner": "<PERSON><PERSON> na", "structure_block.mode.data": "Alelaletik", "structure_block.mode.load": "<PERSON><PERSON><PERSON><PERSON>", "structure_block.mode.save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.mode_info.corner": "Chikin na: avil xchi'uk muk'ulil", "structure_block.mode_info.data": "Alelaletik: snopnunel tajimol", "structure_block.mode_info.load": "Vinajesel: tsvinajes ta archivo", "structure_block.mode_info.save": "K'ejel: tsk'ej ta archivo", "structure_block.position": "Parsial posision", "structure_block.position.x": "parsial posision x", "structure_block.position.y": "parsial posision y", "structure_block.position.z": "parsial posision z", "structure_block.save_failure": "Mu xu' sk'ej li estrukturae ''%s''", "structure_block.save_success": "Estruktura k'ejbil jech k'ucha'al ''%s''", "structure_block.show_air": "Tsvinajes kuboetik mu chvinajik ta k'elel:", "structure_block.show_boundingbox": "Chak' ta ilel ts'aketik:", "structure_block.size": "Smuk'ul estruktura", "structure_block.size.x": "smuk'ul estruktura x", "structure_block.size.y": "smuk'ul estruktura y", "structure_block.size.z": "smuk'ul estruktura z", "structure_block.size_failure": "Mu xu' sta li smuk'ul estrukturae. <PERSON><PERSON> mas chikin naetik xchi'uk sko'ol bi estruktura", "structure_block.size_success": "Smuk'ul ''%s'' tabil", "structure_block.strict": "Sujbil ak'el:", "structure_block.structure_name": "Sbi estruktura", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON> nuk'ilal", "subtitles.ambient.sound": "<PERSON><PERSON><PERSON> nuk'ilal", "subtitles.block.amethyst_block.chime": "Chnik li amatistae", "subtitles.block.amethyst_block.resonate": "Cht'om li amatistae", "subtitles.block.anvil.destroy": "<PERSON><PERSON> lili<PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON> li yunkee", "subtitles.block.anvil.use": "Yunke <PERSON>", "subtitles.block.barrel.close": "Chmak li barile", "subtitles.block.barrel.open": "<PERSON>jam li barile", "subtitles.block.beacon.activate": "Chtsanat li barile", "subtitles.block.beacon.ambient": "<PERSON><PERSON><PERSON><PERSON> li jaroe", "subtitles.block.beacon.deactivate": "Chtup' li jaroe", "subtitles.block.beacon.power_select": "S<PERSON>'el jaro t'ujbil", "subtitles.block.beehive.drip": "Chts'uj li a<PERSON><PERSON>me", "subtitles.block.beehive.enter": "Ch-och ta tompom li xchanul pome", "subtitles.block.beehive.exit": "Chlok' ta tompom li xchanul pome", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON><PERSON>van li texerexe", "subtitles.block.beehive.work": "Ch-abtejik li xchanul pometike", "subtitles.block.bell.resonate": "Cht'om li kampanae", "subtitles.block.bell.use": "<PERSON><PERSON><PERSON>jon li kamp<PERSON>e", "subtitles.block.big_dripleaf.tilt_down": "Chni' ta olon li toyte'e", "subtitles.block.big_dripleaf.tilt_up": "Chni' ta ak'ol li toyte'e", "subtitles.block.blastfurnace.fire_crackle": "Chp'it snich k'ak'al li muk'ta jornoe", "subtitles.block.brewing_stand.brew": "Chbalbun li chijobile", "subtitles.block.bubble_column.bubble_pop": "Cht'omik li balbuneletike", "subtitles.block.bubble_column.upwards_ambient": "Chbeinik li balbuneletike", "subtitles.block.bubble_column.upwards_inside": "Chbainik li balbuneletike", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON> balbunel", "subtitles.block.bubble_column.whirlpool_inside": "Chyalik li balbuneletike", "subtitles.block.button.click": "Tsnet' li votone", "subtitles.block.cake.add_candle": "Chputsk'ij li pastele", "subtitles.block.campfire.crackle": "Chp'it snich k'ak'al", "subtitles.block.candle.crackle": "Chp'it snich k'ak'al li kantelae", "subtitles.block.candle.extinguish": "Chtu<PERSON>' li kante<PERSON>e", "subtitles.block.chest.close": "Chmak li kaxae", "subtitles.block.chest.locked": "Ka<PERSON> mak<PERSON>", "subtitles.block.chest.open": "<PERSON>jam li kaxae", "subtitles.block.chorus_flower.death": "Chta<PERSON><PERSON> li <PERSON>", "subtitles.block.chorus_flower.grow": "<PERSON><PERSON>'i li <PERSON>e", "subtitles.block.comparator.click": "Tstsan la jko'oltasvaneje", "subtitles.block.composter.empty": "Jpas k'a'al lum kokonbil", "subtitles.block.composter.fill": "Jpas ik'luman noje<PERSON>bil", "subtitles.block.composter.ready": "Tspas k'a'al lum li jpas k'a'al lume", "subtitles.block.conduit.activate": "Chtsanat li jpas kanalisare", "subtitles.block.conduit.ambient": "Cht'ixt'un li jpas kanalisare", "subtitles.block.conduit.attack.target": "Chmajvan li jpas kanalisare", "subtitles.block.conduit.deactivate": "Chtup' li jpas kanalisare", "subtitles.block.copper_bulb.turn_off": "Chtup' li joko k'anal k'unil tak'ine", "subtitles.block.copper_bulb.turn_on": "Chtsanat li joko k'anal k'unil tak'ine", "subtitles.block.copper_trapdoor.close": "Chmak li ti'pets'e", "subtitles.block.copper_trapdoor.open": "Chjam li ti'pets'e", "subtitles.block.crafter.craft": "Chmeltsanvan li meltsanobile", "subtitles.block.crafter.fail": "<PERSON><PERSON><PERSON> li meltsanobile", "subtitles.block.creaking_heart.hurt": "X-aket li yo'onton jch'ak'ak'etajele", "subtitles.block.creaking_heart.idle": "<PERSON><PERSON><PERSON> nuk'ilal", "subtitles.block.creaking_heart.spawn": "<PERSON><PERSON><PERSON> li yo'onton jch'ak'ak'etaj<PERSON>", "subtitles.block.deadbush.idle": "Takin yets'al", "subtitles.block.decorated_pot.insert": "But'at li xalu'e", "subtitles.block.decorated_pot.insert_fail": "Ta xts'e'et li xalu'e", "subtitles.block.decorated_pot.shatter": "Ta sok li xalu'e", "subtitles.block.dispenser.dispense": "La spuk item", "subtitles.block.dispenser.fail": "Chopolaj li pukobile", "subtitles.block.door.toggle": "Ta xchak'ak'et li ti'nae", "subtitles.block.dried_ghast.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> takiel", "subtitles.block.dried_ghast.ambient_water": "Chuch' vo' li takin ghaste", "subtitles.block.dried_ghast.place_in_water": "<PERSON><PERSON>'uxij li takin ghaste", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON><PERSON><PERSON> li takin ghaste", "subtitles.block.dry_grass.ambient": "<PERSON><PERSON>'al yets'al", "subtitles.block.enchantment_table.use": "Mexa kapel tunesbil", "subtitles.block.end_portal.spawn": "<PERSON>jam li ochebale ta <PERSON>", "subtitles.block.end_portal_frame.fill": "Chnap'at li sat endere", "subtitles.block.eyeblossom.close": "Chmak li satilal nichime", "subtitles.block.eyeblossom.idle": "Chtsajtsunaj li satilal nichime", "subtitles.block.eyeblossom.open": "<PERSON>jam li satilal nichime", "subtitles.block.fence_gate.toggle": "Ta xchak'ak'et li ti' moke", "subtitles.block.fire.ambient": "Chp'it snich k'ak'al", "subtitles.block.fire.extinguish": "Chtu<PERSON>' li k'ok'e", "subtitles.block.firefly_bush.idle": "Chjumjonik li kukayetike", "subtitles.block.frogspawn.hatch": "Chvok' li avobe", "subtitles.block.furnace.fire_crackle": "Chp'it snich k'ak'al li jornoe", "subtitles.block.generic.break": "Sokem li kuboe", "subtitles.block.generic.fall": "<PERSON>y k'usi chlom ta jbej kubo", "subtitles.block.generic.footsteps": "Okiletik", "subtitles.block.generic.hit": "Yakal ta sok li kuboe", "subtitles.block.generic.place": "Kubo ak'bil", "subtitles.block.grindstone.use": "<PERSON><PERSON> tunesbil", "subtitles.block.growing_plant.crop": "La stuch'be li sk'ob te'e", "subtitles.block.hanging_sign.waxed_interact_fail": "Ta xts'e'et li letereroe", "subtitles.block.honey_block.slide": "Yakal chtsul ta jbej kubo ajapom", "subtitles.block.iron_trapdoor.close": "Chmak li ti'pets'e", "subtitles.block.iron_trapdoor.open": "Chjam li ti'pets'e", "subtitles.block.lava.ambient": "Chbalbun li tsuk'e", "subtitles.block.lava.extinguish": "Ta sisibaj li tsuk'e", "subtitles.block.lever.click": "Chtunesat li palankae", "subtitles.block.note_block.note": "<PERSON><PERSON><PERSON> li sonal kuboe", "subtitles.block.pale_hanging_moss.idle": "<PERSON><PERSON><PERSON> nuk'ilal", "subtitles.block.piston.move": "Chbak' li pistone", "subtitles.block.pointed_dripstone.drip_lava": "Ch<PERSON>'uj li tsuk'e", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Chts'uj ta jbej kaltera li tsuk'e", "subtitles.block.pointed_dripstone.drip_water": "Chts'uj li vo'e", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "Chts'uj ta jbej kaltera li vo'e", "subtitles.block.pointed_dripstone.land": "<PERSON>lom li chu'ch'ene", "subtitles.block.portal.ambient": "<PERSON><PERSON><PERSON><PERSON> li ochebale", "subtitles.block.portal.travel": "Chch'ani li ochebale", "subtitles.block.portal.trigger": "Chlik ta tsots jumjonel li ochebale", "subtitles.block.pressure_plate.click": "Chtunesat li plaka net'ele", "subtitles.block.pumpkin.carve": "<PERSON>-<PERSON><PERSON> li texerexe", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON> li jch'ob toje", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON> li cha'v<PERSON><PERSON><PERSON>e", "subtitles.block.respawn_anchor.charge": "<PERSON><PERSON><PERSON> li cha'v<PERSON><PERSON><PERSON>e", "subtitles.block.respawn_anchor.deplete": "Ch-altsaj li cha'v<PERSON><PERSON>bale", "subtitles.block.respawn_anchor.set_spawn": "<PERSON><PERSON><PERSON><PERSON> li cha'v<PERSON><PERSON><PERSON>e", "subtitles.block.sand.idle": "<PERSON><PERSON>al yets'al", "subtitles.block.sand.wind": "<PERSON><PERSON>'al yets'al", "subtitles.block.sculk.charge": "Chbal<PERSON><PERSON> li sculke", "subtitles.block.sculk.spread": "Ch-e<PERSON>j li sculke", "subtitles.block.sculk_catalyst.bloom": "Ch-epaj li j<PERSON> kanal<PERSON>r sculke", "subtitles.block.sculk_sensor.clicking": "<PERSON><PERSON><PERSON><PERSON> li j-a'<PERSON><PERSON><PERSON>-sculke", "subtitles.block.sculk_sensor.clicking_stop": "<PERSON><PERSON><PERSON><PERSON> <PERSON>i j-<PERSON>'<PERSON><PERSON><PERSON>-sculke", "subtitles.block.sculk_shrieker.shriek": "Ch-ok' li j-ok'el-sculke", "subtitles.block.shulker_box.close": "Chmak li kaxae yu'un shulker", "subtitles.block.shulker_box.open": "<PERSON>jam li kaxae yu'un shulker", "subtitles.block.sign.waxed_interact_fail": "Ta xts'e'et li letereroe", "subtitles.block.smithing_table.use": "Mexa tak'inal tunesbil", "subtitles.block.smoker.smoke": "Ch<PERSON>'atavan li jch'atavaneje", "subtitles.block.sniffer_egg.crack": "Cht'aj li ton j-uts'ivaneje", "subtitles.block.sniffer_egg.hatch": "Chvok' li ton j-uts'ivaneje", "subtitles.block.sniffer_egg.plop": "<PERSON><PERSON><PERSON> li j-uts'ivan<PERSON>e", "subtitles.block.sponge.absorb": "Ch-u<PERSON>'van li esponjae", "subtitles.block.sweet_berry_bush.pick_berries": "Tstsak li bayaetike", "subtitles.block.trapdoor.close": "Chmak li ti'pets'e", "subtitles.block.trapdoor.open": "Chjam li ti'pets'e", "subtitles.block.trapdoor.toggle": "Ta xchak'ak'et li ti'pets'e", "subtitles.block.trial_spawner.about_to_spawn_item": "Chchapaj li chopol iteme", "subtitles.block.trial_spawner.ambient": "Chp'it snich k'ak'al li j<PERSON>jes tsatsalile", "subtitles.block.trial_spawner.ambient_charged": "Xchopol p'itel snich k'ak'al", "subtitles.block.trial_spawner.ambient_ominous": "Xchopol p'itel snich k'ak'al", "subtitles.block.trial_spawner.charge_activate": "Chpix jbej j<PERSON>s tsat<PERSON>il li alele", "subtitles.block.trial_spawner.close_shutter": "Chmak li j<PERSON> t<PERSON>", "subtitles.block.trial_spawner.detect_player": "Chnoj li j<PERSON> t<PERSON>", "subtitles.block.trial_spawner.eject_item": "Tsten k'usitik li j<PERSON>jes tsatsalile", "subtitles.block.trial_spawner.ominous_activate": "Chpix jbej j<PERSON>s tsat<PERSON>il li alele", "subtitles.block.trial_spawner.open_shutter": "<PERSON><PERSON><PERSON> li j<PERSON> t<PERSON>", "subtitles.block.trial_spawner.spawn_item": "T<PERSON>ajes li chopol iteme", "subtitles.block.trial_spawner.spawn_item_begin": "<PERSON><PERSON><PERSON> li chopol iteme", "subtitles.block.trial_spawner.spawn_mob": "Tsvinajes chanuletik li jvinajes tsatsalile", "subtitles.block.tripwire.attach": "<PERSON><PERSON> li noe", "subtitles.block.tripwire.click": "Chtunesat li noe", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON> li noe", "subtitles.block.vault.activate": "Chtsanat li arkone", "subtitles.block.vault.ambient": "Chp'it snich k'ak'al li arkone", "subtitles.block.vault.close_shutter": "Chmak li arkone", "subtitles.block.vault.deactivate": "Ch<PERSON><PERSON>' li a<PERSON>one", "subtitles.block.vault.eject_item": "Tsten k'usitik li arkone", "subtitles.block.vault.insert_item": "<PERSON><PERSON><PERSON> li a<PERSON>one", "subtitles.block.vault.insert_item_fail": "Tsp'aj item li arkone", "subtitles.block.vault.open_shutter": "<PERSON><PERSON><PERSON> li arkone", "subtitles.block.vault.reject_rewarded_player": "Tsp'aj j<PERSON><PERSON> li arkone", "subtitles.block.water.ambient": "Chbein li vo'e", "subtitles.block.wet_sponge.dries": "Chtakij li esponjae", "subtitles.chiseled_bookshelf.insert": "<PERSON><PERSON> ak'bil", "subtitles.chiseled_bookshelf.insert_enchanted": "<PERSON><PERSON><PERSON> vun ak'bil", "subtitles.chiseled_bookshelf.take": "<PERSON><PERSON> tambil", "subtitles.chiseled_bookshelf.take_enchanted": "<PERSON><PERSON><PERSON> vun tambil", "subtitles.enchant.thorns.hit": "Xch'ojelik ch'ixetik", "subtitles.entity.allay.ambient_with_item": "Ta sa'van li j<PERSON>ltaobbaile", "subtitles.entity.allay.ambient_without_item": "Chk'anvan li jkoltaobbaile", "subtitles.entity.allay.death": "Ch<PERSON>j li j<PERSON>ltaobbaile", "subtitles.entity.allay.hurt": "Ch<PERSON>j li j<PERSON>ltaobbaile", "subtitles.entity.allay.item_given": "Chtse'in li jkoltaobbaile", "subtitles.entity.allay.item_taken": "Ch-abolaj li jkoltaobbaile", "subtitles.entity.allay.item_thrown": "Tstenvan li jkoltaobbaile", "subtitles.entity.armadillo.ambient": "<PERSON> xjiet li ibe", "subtitles.entity.armadillo.brush": "<PERSON><PERSON>'ul li ibe", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> li ibe", "subtitles.entity.armadillo.eat": "Ch<PERSON>' li ibe", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> li ibe", "subtitles.entity.armadillo.hurt_reduced": "Tspoj sba li ibe", "subtitles.entity.armadillo.land": "<PERSON><PERSON> li ibe", "subtitles.entity.armadillo.peek": "Chk'elvan ta pana li ibe", "subtitles.entity.armadillo.roll": "<PERSON><PERSON> li ibe", "subtitles.entity.armadillo.scute_drop": "<PERSON><PERSON> smik'al li ibe", "subtitles.entity.armadillo.unroll_finish": "Ta sjol sba li ibe", "subtitles.entity.armadillo.unroll_start": "Chk'elvan ta pana li ibe", "subtitles.entity.armor_stand.fall": "<PERSON>y k'usi lom", "subtitles.entity.arrow.hit": "Chmajvan li yolobe", "subtitles.entity.arrow.hit_player": "Jtajimol majbil", "subtitles.entity.arrow.shoot": "Cht'omesat li yolobe", "subtitles.entity.axolotl.attack": "Chmajvan li axolotee", "subtitles.entity.axolotl.death": "<PERSON><PERSON><PERSON> li axolo<PERSON>", "subtitles.entity.axolotl.hurt": "<PERSON><PERSON><PERSON> li axolo<PERSON>", "subtitles.entity.axolotl.idle_air": "Chkits'its'uj li axolotee", "subtitles.entity.axolotl.idle_water": "Chkits'its'uj li axolotee", "subtitles.entity.axolotl.splash": "Chts'itesvan li axolotee", "subtitles.entity.axolotl.swim": "Chnux li axolotee", "subtitles.entity.bat.ambient": "Ch-ok' li sots'e", "subtitles.entity.bat.death": "<PERSON><PERSON>j li sots'e", "subtitles.entity.bat.hurt": "<PERSON><PERSON><PERSON> li sots'e", "subtitles.entity.bat.takeoff": "Chvil li sots'e", "subtitles.entity.bee.ambient": "Chjumjon li xchanul pome", "subtitles.entity.bee.death": "<PERSON><PERSON><PERSON> li xchanul pome", "subtitles.entity.bee.hurt": "<PERSON><PERSON><PERSON> li xchanul pome", "subtitles.entity.bee.loop": "Chjumjon li xchanul pome", "subtitles.entity.bee.loop_aggressive": "Chjumjon li pukujil xchanul pome", "subtitles.entity.bee.pollinate": "Chjumjon li muyubajem xchanul pome", "subtitles.entity.bee.sting": "Chjomvan li xchanul pome", "subtitles.entity.blaze.ambient": "Chich' ik' li jk'ok'e", "subtitles.entity.blaze.burn": "Chp'it snich jk'ok'", "subtitles.entity.blaze.death": "Ch<PERSON>j li jk'ok'e", "subtitles.entity.blaze.hurt": "Chyaj li jk'ok'e", "subtitles.entity.blaze.shoot": "Cht'omesvan li jk'ok'e", "subtitles.entity.boat.paddle_land": "Chnikesvan ta lum", "subtitles.entity.boat.paddle_water": "<PERSON><PERSON>", "subtitles.entity.bogged.ambient": "Chnik li j-ach'elti<PERSON> krixchanoe", "subtitles.entity.bogged.death": "<PERSON><PERSON><PERSON> li j-ach'el<PERSON><PERSON> k<PERSON>oe", "subtitles.entity.bogged.hurt": "<PERSON><PERSON><PERSON> li j-ach'el<PERSON><PERSON> k<PERSON>oe", "subtitles.entity.breeze.charge": "Chnoj li j-ik'e", "subtitles.entity.breeze.death": "<PERSON><PERSON><PERSON> li j-ik'e", "subtitles.entity.breeze.deflect": "Ch<PERSON>'e<PERSON>van li j-ik'e", "subtitles.entity.breeze.hurt": "<PERSON><PERSON><PERSON> li j-ik'e", "subtitles.entity.breeze.idle_air": "<PERSON>vil li j-ik'e", "subtitles.entity.breeze.idle_ground": "Chjumjon li j-ik'e", "subtitles.entity.breeze.inhale": "Chich' ik' li j-ik'e", "subtitles.entity.breeze.jump": "Chbit li j-ik'e", "subtitles.entity.breeze.land": "<PERSON>yal li j-ik'e", "subtitles.entity.breeze.shoot": "Cht'omesvan li j-ik'e", "subtitles.entity.breeze.slide": "Ch<PERSON>l li j-ik'e", "subtitles.entity.breeze.whirl": "Chjoyobaj li j-ik'e", "subtitles.entity.breeze.wind_burst": "Cht'om li j-ik'e", "subtitles.entity.camel.ambient": "Ta xjiet li toromerarioe", "subtitles.entity.camel.dash": "Cht'al li toromerarioe", "subtitles.entity.camel.dash_ready": "Chkol li toromerarioe", "subtitles.entity.camel.death": "<PERSON><PERSON>j li toromerar<PERSON>e", "subtitles.entity.camel.eat": "Chve' li toromerarioe", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON> li toromerar<PERSON>e", "subtitles.entity.camel.saddle": "Chak'be xila", "subtitles.entity.camel.sit": "Chchotij li toromerarioe", "subtitles.entity.camel.stand": "Chvechi li toromerarioe", "subtitles.entity.camel.step": "Chxanav li toromerarioe", "subtitles.entity.camel.step_sand": "Chxanav ta yi' li toromerar<PERSON>e", "subtitles.entity.cat.ambient": "Chme'munaj li supe", "subtitles.entity.cat.beg_for_food": "Tsk'an ve'lil li supe", "subtitles.entity.cat.death": "<PERSON><PERSON><PERSON> li supe", "subtitles.entity.cat.eat": "Chve' li supe", "subtitles.entity.cat.hiss": "<PERSON>-<PERSON>' li supe", "subtitles.entity.cat.hurt": "<PERSON><PERSON><PERSON> li supe", "subtitles.entity.cat.purr": "Ch<PERSON>l<PERSON><PERSON><PERSON> li supe", "subtitles.entity.chicken.ambient": "Xkeket li alak'e", "subtitles.entity.chicken.death": "<PERSON><PERSON><PERSON> li alak'e", "subtitles.entity.chicken.egg": "<PERSON><PERSON><PERSON> li alak'e", "subtitles.entity.chicken.hurt": "<PERSON><PERSON><PERSON> li alak'e", "subtitles.entity.cod.death": "<PERSON><PERSON><PERSON> li vaka<PERSON>", "subtitles.entity.cod.flop": "Chpochpunaj li vaka<PERSON>oe", "subtitles.entity.cod.hurt": "<PERSON><PERSON><PERSON> li vaka<PERSON>", "subtitles.entity.cow.ambient": "<PERSON><PERSON><PERSON><PERSON> li vakaxe", "subtitles.entity.cow.death": "<PERSON><PERSON><PERSON> li vakaxe", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON> li vakaxe", "subtitles.entity.cow.milk": "Tsp<PERSON>'be li xchu' vakaxe", "subtitles.entity.creaking.activate": "Chk'elvan li jch'ak'ak'etaj<PERSON>", "subtitles.entity.creaking.ambient": "Xch'ak'ak'et li jch'ak'ak'etajele", "subtitles.entity.creaking.attack": "Chmajvan li jch'ak'ak'et<PERSON><PERSON>", "subtitles.entity.creaking.deactivate": "Chvaxi li jch'ak'ak'etajele", "subtitles.entity.creaking.death": "X-aket li jch'ak'ak'etajele", "subtitles.entity.creaking.freeze": "Chpaj li jch'ak'ak'et<PERSON><PERSON>", "subtitles.entity.creaking.spawn": "<PERSON><PERSON><PERSON> li jch'ak'ak'et<PERSON><PERSON>", "subtitles.entity.creaking.sway": "Ch<PERSON>jat li jch'ak'ak'et<PERSON><PERSON>", "subtitles.entity.creaking.twitch": "Chbikbunaj li jch'ak'ak'etajele", "subtitles.entity.creaking.unfreeze": "Chbak' li jch'ak'ak'et<PERSON><PERSON>", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> li <PERSON>ere", "subtitles.entity.creeper.hurt": "<PERSON><PERSON><PERSON> li <PERSON>ere", "subtitles.entity.creeper.primed": "Ta sisibaj li creepere", "subtitles.entity.dolphin.ambient": "Chkits'its'uj li telpine", "subtitles.entity.dolphin.ambient_water": "Chxuxubaj li telpine", "subtitles.entity.dolphin.attack": "Ch<PERSON>jvan li telpine", "subtitles.entity.dolphin.death": "<PERSON><PERSON><PERSON> li telpine", "subtitles.entity.dolphin.eat": "<PERSON><PERSON>' li telpine", "subtitles.entity.dolphin.hurt": "<PERSON><PERSON><PERSON> li telpine", "subtitles.entity.dolphin.jump": "Chbit li telpine", "subtitles.entity.dolphin.play": "Chtajin li telpine", "subtitles.entity.dolphin.splash": "Chts'itesvan li telpine", "subtitles.entity.dolphin.swim": "Chnux li telpine", "subtitles.entity.donkey.ambient": "<PERSON>-avan li vuroe", "subtitles.entity.donkey.angry": "<PERSON>-avan li vuroe", "subtitles.entity.donkey.chest": "<PERSON><PERSON>'be kaxa vuro", "subtitles.entity.donkey.death": "<PERSON><PERSON><PERSON> li vuroe", "subtitles.entity.donkey.eat": "Chve' li vuroe", "subtitles.entity.donkey.hurt": "<PERSON><PERSON><PERSON> li vuroe", "subtitles.entity.donkey.jump": "Chbit li vuroe", "subtitles.entity.drowned.ambient": "Chk'opoj li ja'al jchamel", "subtitles.entity.drowned.ambient_water": "Chk'opoj li ja'al jchamele", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> li ja'al j<PERSON><PERSON>e", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON> li ja'al j<PERSON><PERSON>e", "subtitles.entity.drowned.shoot": "Tsten strirente li ja'al jchamele", "subtitles.entity.drowned.step": "Chxanav li ja'al jchamele", "subtitles.entity.drowned.swim": "Chnux li ja'al jchamele", "subtitles.entity.egg.throw": "Chtenat li ton alak'e", "subtitles.entity.elder_guardian.ambient": "<PERSON><PERSON><PERSON><PERSON> li malal j<PERSON>e", "subtitles.entity.elder_guardian.ambient_land": "Chpochpunaj li malal j<PERSON>vaneje", "subtitles.entity.elder_guardian.curse": "Chchopol k'opoj li malal jchabivaneje", "subtitles.entity.elder_guardian.death": "<PERSON><PERSON><PERSON> li malal j<PERSON>e", "subtitles.entity.elder_guardian.flop": "Chpochpunaj li malal j<PERSON>vaneje", "subtitles.entity.elder_guardian.hurt": "<PERSON><PERSON><PERSON> li malal j<PERSON>", "subtitles.entity.ender_dragon.ambient": "Ta xlomomet li muk'ta aine", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON> li muk'ta aine", "subtitles.entity.ender_dragon.flap": "Chpoch<PERSON><PERSON>j li muk'ta aine", "subtitles.entity.ender_dragon.growl": "Ta xjiet li muk'ta aine", "subtitles.entity.ender_dragon.hurt": "<PERSON><PERSON><PERSON> li muk'ta aine", "subtitles.entity.ender_dragon.shoot": "Cht'omesvan li muk'ta aine", "subtitles.entity.ender_eye.death": "<PERSON>lom li sat endere", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON> li sat endere", "subtitles.entity.ender_pearl.throw": "Chtenat li ender perlae", "subtitles.entity.enderman.ambient": "Chk'opoj li endermane", "subtitles.entity.enderman.death": "<PERSON><PERSON><PERSON> li endermane", "subtitles.entity.enderman.hurt": "<PERSON><PERSON><PERSON> li endermane", "subtitles.entity.enderman.scream": "<PERSON>-<PERSON><PERSON> li endermane", "subtitles.entity.enderman.stare": "<PERSON><PERSON><PERSON><PERSON> li end<PERSON>e", "subtitles.entity.enderman.teleport": "Tstak sba li endermane", "subtitles.entity.endermite.ambient": "Chtsul li endermitee", "subtitles.entity.endermite.death": "Ch<PERSON>j li endermitee", "subtitles.entity.endermite.hurt": "<PERSON><PERSON>j li endermitee", "subtitles.entity.evoker.ambient": "Chvulvunaj li j-i<PERSON>'van<PERSON>e", "subtitles.entity.evoker.cast_spell": "Tsten chamel li j-ik'vaneje", "subtitles.entity.evoker.celebrate": "<PERSON><PERSON><PERSON> k'in li j-ik'vaneje", "subtitles.entity.evoker.death": "<PERSON><PERSON><PERSON> li j-i<PERSON>'van<PERSON>e", "subtitles.entity.evoker.hurt": "<PERSON><PERSON><PERSON> li j-i<PERSON>'van<PERSON>e", "subtitles.entity.evoker.prepare_attack": "<PERSON><PERSON><PERSON><PERSON><PERSON> jun majel li j-ik'van<PERSON>e", "subtitles.entity.evoker.prepare_summon": "Tsmeltsan jun ik'el li j-ik'vanej", "subtitles.entity.evoker.prepare_wololo": "<PERSON><PERSON><PERSON><PERSON><PERSON> jun kapel li j-ik'vanej", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON>'vanik li ts'uts'upiletike", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON><PERSON><PERSON> tambil", "subtitles.entity.firework_rocket.blast": "Yolonk'ok'etik", "subtitles.entity.firework_rocket.launch": "Chtenat li yolonk'ok'e", "subtitles.entity.firework_rocket.twinkle": "Ta xnop'op'et li yolonk'ok'e", "subtitles.entity.fish.swim": "Chts'itesvan", "subtitles.entity.fishing_bobber.retrieve": "Tsak cha'tambil", "subtitles.entity.fishing_bobber.splash": "Chts'itesvan li tsake", "subtitles.entity.fishing_bobber.throw": "Tsak tenbil", "subtitles.entity.fox.aggro": "<PERSON>-<PERSON><PERSON> li tsajal vete", "subtitles.entity.fox.ambient": "<PERSON>-<PERSON>' li tsajal vete", "subtitles.entity.fox.bite": "<PERSON><PERSON><PERSON>van li tsajal vete", "subtitles.entity.fox.death": "<PERSON><PERSON><PERSON> li tsajal vete", "subtitles.entity.fox.eat": "<PERSON><PERSON>' li tsajal vete", "subtitles.entity.fox.hurt": "<PERSON><PERSON><PERSON> li tsajal vete", "subtitles.entity.fox.screech": "<PERSON><PERSON>'nal-ok' li tsajal vete", "subtitles.entity.fox.sleep": "<PERSON><PERSON><PERSON> li tsajal vete", "subtitles.entity.fox.sniff": "Ch-<PERSON><PERSON>'<PERSON><PERSON> li tsajal vete", "subtitles.entity.fox.spit": "Ch<PERSON>aj li tsajal vete", "subtitles.entity.fox.teleport": "Tstak sba li tsajal vete", "subtitles.entity.frog.ambient": "Chk'ejin li pok'ok'e", "subtitles.entity.frog.death": "<PERSON><PERSON>j li pok'ok'e", "subtitles.entity.frog.eat": "Chve' li pok'ok'e", "subtitles.entity.frog.hurt": "<PERSON><PERSON><PERSON> li pok'ok'e", "subtitles.entity.frog.lay_spawn": "<PERSON><PERSON>in li pok'ok'e", "subtitles.entity.frog.long_jump": "Chbit li pok'ok'e", "subtitles.entity.generic.big_fall": "<PERSON>y k'usi lom", "subtitles.entity.generic.burn": "Yakal chk'ak'", "subtitles.entity.generic.death": "<PERSON><PERSON> ch<PERSON>j", "subtitles.entity.generic.drink": "<PERSON><PERSON> ch-uch'baj", "subtitles.entity.generic.eat": "Yakal chve'", "subtitles.entity.generic.explode": "T'omel", "subtitles.entity.generic.extinguish_fire": "Chtu<PERSON>' li k'ok'e", "subtitles.entity.generic.hurt": "<PERSON><PERSON> k'usi chyaj", "subtitles.entity.generic.small_fall": "Oy k'usi la sp'osi yok", "subtitles.entity.generic.splash": "<PERSON><PERSON><PERSON>it<PERSON>l", "subtitles.entity.generic.swim": "<PERSON><PERSON> chnux", "subtitles.entity.generic.wind_burst": "Cht'om li sutub-ik'e", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON><PERSON> li ghaste", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> li ghaste", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> li ghaste", "subtitles.entity.ghast.shoot": "Ch<PERSON>'<PERSON><PERSON><PERSON> li ghaste", "subtitles.entity.ghastling.ambient": "<PERSON><PERSON>'<PERSON><PERSON> li unen ghaste", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> li unen ghaste", "subtitles.entity.ghastling.hurt": "<PERSON><PERSON><PERSON> li unen ghaste", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> li unen ghaste", "subtitles.entity.glow_item_frame.add_item": "La yak' item ta jbej nop'ol marko", "subtitles.entity.glow_item_frame.break": "Nop'ol marko lilinbil", "subtitles.entity.glow_item_frame.place": "Nop'ol marko ak'bil", "subtitles.entity.glow_item_frame.remove_item": "Chx<PERSON>b li nop'ol markoe", "subtitles.entity.glow_item_frame.rotate_item": "Tsjoyobta li nop'ol markoe", "subtitles.entity.glow_squid.ambient": "Chnux li nop'ol muk'ta ja'al-ome", "subtitles.entity.glow_squid.death": "<PERSON><PERSON>j li nop'ol muk'ta ja'al-ome", "subtitles.entity.glow_squid.hurt": "<PERSON><PERSON><PERSON> li nop'ol muk'ta ja'al-ome", "subtitles.entity.glow_squid.squirt": "Tsten stinta li nop'ol muk'ta ja'al-ome", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON><PERSON> li <PERSON>une", "subtitles.entity.goat.death": "<PERSON><PERSON><PERSON> li <PERSON>une", "subtitles.entity.goat.eat": "<PERSON><PERSON>' li <PERSON>une", "subtitles.entity.goat.horn_break": "<PERSON>'ok li skachu <PERSON>une", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> li <PERSON>une", "subtitles.entity.goat.long_jump": "Chbit li tentsune", "subtitles.entity.goat.milk": "Tspits'be li xchu' tentsune", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON><PERSON><PERSON> li <PERSON>une", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON><PERSON> li <PERSON>une", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON><PERSON><PERSON> li <PERSON>une", "subtitles.entity.goat.step": "Chxanav li tentsune", "subtitles.entity.guardian.ambient": "<PERSON><PERSON>a<PERSON> li j<PERSON>e", "subtitles.entity.guardian.ambient_land": "Chpochpunaj li jchabivaneje", "subtitles.entity.guardian.attack": "Cht'omesvan li jchabivaneje", "subtitles.entity.guardian.death": "<PERSON><PERSON><PERSON> li j<PERSON>", "subtitles.entity.guardian.flop": "Chpochpunaj li jchabivaneje", "subtitles.entity.guardian.hurt": "<PERSON><PERSON><PERSON> li j<PERSON>e", "subtitles.entity.happy_ghast.ambient": "<PERSON>'un chk'ejin li muyuba<PERSON>m ghaste", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON><PERSON> li muy<PERSON><PERSON> ghaste", "subtitles.entity.happy_ghast.equip": "<PERSON>k'be li pek'e", "subtitles.entity.happy_ghast.harness_goggles_down": "<PERSON><PERSON> xa li muyuba<PERSON>m ghaste", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON><PERSON><PERSON> li muyuba<PERSON>m ghaste", "subtitles.entity.happy_ghast.hurt": "<PERSON><PERSON><PERSON> li muyuba<PERSON>m ghaste", "subtitles.entity.happy_ghast.unequip": "Tspojbe li pek'e", "subtitles.entity.hoglin.ambient": "Ta xjiet li hogline", "subtitles.entity.hoglin.angry": "Ta xjiet li pukujil hogline", "subtitles.entity.hoglin.attack": "Ch<PERSON>j<PERSON> li hogline", "subtitles.entity.hoglin.converted_to_zombified": "Chk'ataj ta zoglin li hogline", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON> li hogline", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> li hogline", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON>'bat' li hogline", "subtitles.entity.hoglin.step": "Chxanav li hogline", "subtitles.entity.horse.ambient": "<PERSON>-a<PERSON> li ka'e", "subtitles.entity.horse.angry": "<PERSON>-a<PERSON> li ka'e", "subtitles.entity.horse.armor": "Chak'be almarura li ka'e", "subtitles.entity.horse.breathe": "Chich' ik' li ka'e", "subtitles.entity.horse.death": "<PERSON><PERSON><PERSON> li ka'e", "subtitles.entity.horse.eat": "Chve' li ka'e", "subtitles.entity.horse.gallop": "<PERSON>-an<PERSON><PERSON> li ka'e", "subtitles.entity.horse.hurt": "<PERSON><PERSON><PERSON> li ka'e", "subtitles.entity.horse.jump": "Chbit li ka'e", "subtitles.entity.horse.saddle": "<PERSON><PERSON>'be li xilae", "subtitles.entity.husk.ambient": "Ta xjiet li takin jchamele", "subtitles.entity.husk.converted_to_zombie": "Tslomes yi' ta sbek'tal stakupal li takin jchamele", "subtitles.entity.husk.death": "<PERSON><PERSON><PERSON> li takin jchamele", "subtitles.entity.husk.hurt": "<PERSON><PERSON><PERSON> li takin jchamele", "subtitles.entity.illusioner.ambient": "Chvulvunaj li j<PERSON>'sati<PERSON>svan<PERSON>", "subtitles.entity.illusioner.cast_spell": "Tsten chamel li jcha'satijesvaneje", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON> li j<PERSON>'sat<PERSON>", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON> li j<PERSON>'sat<PERSON><PERSON>", "subtitles.entity.illusioner.mirror_move": "Tstak sba li jcha'satijesvaneje", "subtitles.entity.illusioner.prepare_blindness": "<PERSON><PERSON><PERSON> ma'satilal li jcha'satijesvan<PERSON>e", "subtitles.entity.illusioner.prepare_mirror": "<PERSON><PERSON><PERSON> cha'satijel li jcha'sati<PERSON><PERSON><PERSON>e", "subtitles.entity.iron_golem.attack": "Chmajvan li jchabivanej tak'ine", "subtitles.entity.iron_golem.damage": "Ta sok li jchabivanej tak'ine", "subtitles.entity.iron_golem.death": "<PERSON><PERSON><PERSON> li jchabivanej tak'ine", "subtitles.entity.iron_golem.hurt": "<PERSON><PERSON>j li jchabivanej tak'ine", "subtitles.entity.iron_golem.repair": "Chmeltsaj li jchabivanej tak'ine", "subtitles.entity.item.break": "Tslilin item", "subtitles.entity.item.pickup": "Tstam item", "subtitles.entity.item_frame.add_item": "La yak' jun k'usi ta jbej marko", "subtitles.entity.item_frame.break": "<PERSON><PERSON> l<PERSON>", "subtitles.entity.item_frame.place": "<PERSON><PERSON>bil", "subtitles.entity.item_frame.remove_item": "Chx<PERSON>b li markoe", "subtitles.entity.item_frame.rotate_item": "Tsjoyobta li markoe", "subtitles.entity.leash_knot.break": "Sts'akavil ch'ojon lilinbil", "subtitles.entity.leash_knot.place": "Sts'akavil ch'ojon chukbil", "subtitles.entity.lightning_bolt.impact": "Chnom nopol li anjele", "subtitles.entity.lightning_bolt.thunder": "Cht'om li chauke", "subtitles.entity.llama.ambient": "<PERSON><PERSON><PERSON><PERSON> li muk'ta chije", "subtitles.entity.llama.angry": "<PERSON>-a<PERSON> li pukujil muk'ta chije", "subtitles.entity.llama.chest": "Cha<PERSON>'be kaxa li muk'ta chije", "subtitles.entity.llama.death": "<PERSON><PERSON><PERSON> li muk'ta chije", "subtitles.entity.llama.eat": "Ch<PERSON>' li muk'ta chije", "subtitles.entity.llama.hurt": "<PERSON><PERSON><PERSON> li muk'ta chije", "subtitles.entity.llama.spit": "<PERSON><PERSON>aj li muk'ta chije", "subtitles.entity.llama.step": "Chxanav li muk'ta chije", "subtitles.entity.llama.swag": "Chluchat li muk'ta chije", "subtitles.entity.magma_cube.death": "<PERSON><PERSON><PERSON> li k'ak'al simchone", "subtitles.entity.magma_cube.hurt": "<PERSON><PERSON><PERSON> li k'ak'al simchone", "subtitles.entity.magma_cube.squish": "Chbit li k'ak'al simchone", "subtitles.entity.minecart.inside": "Ta xk'uxuxet li vakone", "subtitles.entity.minecart.inside_underwater": "Ta xk'uxuxet ta yut vo' li vakone", "subtitles.entity.minecart.riding": "Chbak' li v<PERSON>one", "subtitles.entity.mooshroom.convert": "Chk'at<PERSON> li mooshr<PERSON>e", "subtitles.entity.mooshroom.eat": "<PERSON><PERSON>' li mooshr<PERSON>e", "subtitles.entity.mooshroom.milk": "Tsp<PERSON>'be li xchu' mooshroome", "subtitles.entity.mooshroom.suspicious_milk": "Tspits'be li xchu' mooshroome xchi'uk xi'el", "subtitles.entity.mule.ambient": "<PERSON>-a<PERSON> li mulae", "subtitles.entity.mule.angry": "<PERSON>-a<PERSON> li mulae", "subtitles.entity.mule.chest": "<PERSON><PERSON>'be kaxa li mulae", "subtitles.entity.mule.death": "<PERSON><PERSON><PERSON> li mulae", "subtitles.entity.mule.eat": "Chve' li mulae", "subtitles.entity.mule.hurt": "<PERSON><PERSON><PERSON> li mulae", "subtitles.entity.mule.jump": "Chbit li mulae", "subtitles.entity.painting.break": "<PERSON><PERSON><PERSON> lilin<PERSON>", "subtitles.entity.painting.place": "Bonobil ak'bil", "subtitles.entity.panda.aggressive_ambient": "Chjaxjun li china oso chone", "subtitles.entity.panda.ambient": "Ta xjuxlajet li china oso chone", "subtitles.entity.panda.bite": "<PERSON><PERSON>'van li china oso chone", "subtitles.entity.panda.cant_breed": "Ch-avan li china oso chone", "subtitles.entity.panda.death": "<PERSON><PERSON>j li china oso chone", "subtitles.entity.panda.eat": "Chve' li china oso chone", "subtitles.entity.panda.hurt": "<PERSON><PERSON><PERSON> li china oso chone", "subtitles.entity.panda.pre_sneeze": "Oy xchik ta sni' li china oso chone", "subtitles.entity.panda.sneeze": "<PERSON>ja't'isaj li china oso chone", "subtitles.entity.panda.step": "Chxanav li china oso chone", "subtitles.entity.panda.worried_ambient": "Ta xjiet li china oso chone", "subtitles.entity.parrot.ambient": "Chk'opoj li puyuch'e", "subtitles.entity.parrot.death": "<PERSON><PERSON><PERSON> li puyuch'e", "subtitles.entity.parrot.eats": "Ch<PERSON>' li puyuch'e", "subtitles.entity.parrot.fly": "Ta xvilet li puyuch'e", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON> li puyuch'e", "subtitles.entity.parrot.imitate.blaze": "Tspas jech k'ucha'al jkot jk'ok' li puyuch'e", "subtitles.entity.parrot.imitate.bogged": "Tspas jech k'ucha'al jun j-ach'eltikal krixchano li puyuch'e", "subtitles.entity.parrot.imitate.breeze": "Tspas jech k'ucha'al jkot j-ik' li puyuch'e", "subtitles.entity.parrot.imitate.creaking": "Tspas jech k'ucha'al jkot jch'ak'ak'etajele li puyuch'e", "subtitles.entity.parrot.imitate.creeper": "Tspas jech k'ucha'al jkot creeper li puyuch'e", "subtitles.entity.parrot.imitate.drowned": "Tspas jech k'ucha'al jun ja'al jchamel li puyuch'e", "subtitles.entity.parrot.imitate.elder_guardian": "Tspas jech k'ucha'al jun jchabivanej li puyuch'e", "subtitles.entity.parrot.imitate.ender_dragon": "Tspas jech k'ucha'al jkot muk'ta ain li puyuch'e", "subtitles.entity.parrot.imitate.endermite": "Tspas jech k'ucha'al jkot endermite li puyuch'e", "subtitles.entity.parrot.imitate.evoker": "Tspas jech k'ucha'al jun j-ik'vanej li puyuch'e", "subtitles.entity.parrot.imitate.ghast": "Tspas jech k'ucha'al jkot ghast li puyuch'e", "subtitles.entity.parrot.imitate.guardian": "Tspas jech k'ucha'al jun jchabivanej li puyuch'e", "subtitles.entity.parrot.imitate.hoglin": "Tspas jech k'ucha'al jkot hoglin li puyuch'e", "subtitles.entity.parrot.imitate.husk": "Tspas jech k'ucha'al jun takin jchamel li puyuch'e", "subtitles.entity.parrot.imitate.illusioner": "Tspas jech k'ucha'al jun jcha'satijesvanej li puyuch'e", "subtitles.entity.parrot.imitate.magma_cube": "Tspas jech k'ucha'al jbej k'ak'al simchon li puyuch'e", "subtitles.entity.parrot.imitate.phantom": "Tspas jech k'ucha'al jkot muk'ta sots' li puyuch'e", "subtitles.entity.parrot.imitate.piglin": "Tspas jech k'ucha'al jun piglin li puyuch'e", "subtitles.entity.parrot.imitate.piglin_brute": "Tspas jech k'ucha'al jun xi'el piglin li puyuch'e", "subtitles.entity.parrot.imitate.pillager": "Tspas jech k'ucha'al jun makbe li puyuch'e", "subtitles.entity.parrot.imitate.ravager": "Tspas jech k'ucha'al jkot jsokesvanej li puyuch'e", "subtitles.entity.parrot.imitate.shulker": "Tspas jech k'ucha'al jbej shulker li puyuch'e", "subtitles.entity.parrot.imitate.silverfish": "Tspas jech k'ucha'al jun tonilchoy li puyuch'e", "subtitles.entity.parrot.imitate.skeleton": "Tspas jech k'ucha'al jun jbakubel li puyuch'e", "subtitles.entity.parrot.imitate.slime": "Tspas jech k'ucha'al jbej simchon li puyuch'e", "subtitles.entity.parrot.imitate.spider": "Tspas jech k'ucha'al jkot om li puyuch'e", "subtitles.entity.parrot.imitate.stray": "T<PERSON>as jech k'ucha'al jun jch'aybail li puyuch'e", "subtitles.entity.parrot.imitate.vex": "Tspas jech k'ucha'al jun jsa'sunvanej li puyuch'e", "subtitles.entity.parrot.imitate.vindicator": "Tspas jech k'ucha'al jun jjpakvanej li puyuch'e", "subtitles.entity.parrot.imitate.warden": "Tspas jech k'ucha'al jun jchabich'en li puyuch'e", "subtitles.entity.parrot.imitate.witch": "Tspas jech k'ucha'al jun chopol ants li puyuch'e", "subtitles.entity.parrot.imitate.wither": "Tspas jech k'u<PERSON>'al Wither li puyuch'e", "subtitles.entity.parrot.imitate.wither_skeleton": "Tspas jech k'ucha'al jun jbakubel yu'un Wither li puyuch'e", "subtitles.entity.parrot.imitate.zoglin": "Tspas jech k'ucha'al jkot zoglin li puyuch'e", "subtitles.entity.parrot.imitate.zombie": "Tspas jech k'ucha'al jun jchamel li puyuch'e", "subtitles.entity.parrot.imitate.zombie_villager": "Tspas jech k'ucha'al jun ipajesbil jbik'it lum li puyuch'e", "subtitles.entity.phantom.ambient": "Ch-ok' li muk'ta sots'e", "subtitles.entity.phantom.bite": "<PERSON><PERSON>'van li muk'ta sots'e", "subtitles.entity.phantom.death": "<PERSON><PERSON>j li muk'ta sots'e", "subtitles.entity.phantom.flap": "Chpochpunaj li muk'ta sots'e", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON> li muk'ta sots'e", "subtitles.entity.phantom.swoop": "Chnopaj li muk'ta sots'e", "subtitles.entity.pig.ambient": "Ta xjiet li chitome", "subtitles.entity.pig.death": "<PERSON><PERSON><PERSON> li chitome", "subtitles.entity.pig.hurt": "<PERSON><PERSON><PERSON> li chitome", "subtitles.entity.pig.saddle": "<PERSON><PERSON>'be li xilae", "subtitles.entity.piglin.admiring_item": "Lek sba chil jun item li pigline", "subtitles.entity.piglin.ambient": "Ta xjiet li pigline", "subtitles.entity.piglin.angry": "Ta xjiet li pukujil pigline", "subtitles.entity.piglin.celebrate": "T<PERSON>as k'in li pigline", "subtitles.entity.piglin.converted_to_zombified": "Ch-ipaj li pigline", "subtitles.entity.piglin.death": "<PERSON><PERSON>j li pigline", "subtitles.entity.piglin.hurt": "<PERSON><PERSON><PERSON> li pigline", "subtitles.entity.piglin.jealous": "Ta xjiet li pigline yu'un k'ak'al yo'onton", "subtitles.entity.piglin.retreat": "<PERSON><PERSON>'bat' li pigline", "subtitles.entity.piglin.step": "Chxanav li pigline", "subtitles.entity.piglin_brute.ambient": "Ta xjiet li xi'el pigline", "subtitles.entity.piglin_brute.angry": "Ta xjiet li pukujil xi'el pigline", "subtitles.entity.piglin_brute.converted_to_zombified": "Ch-ipaj li xi'el pigline", "subtitles.entity.piglin_brute.death": "Chlaj li xi'el pigline", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON>j li xi'el pigline", "subtitles.entity.piglin_brute.step": "Chxanav li xi'el pigline", "subtitles.entity.pillager.ambient": "<PERSON><PERSON>l<PERSON><PERSON><PERSON> li makbee", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON> k'in li makbee", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON> li makbee", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON> li makbee", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON> majel", "subtitles.entity.player.attack.knockback": "<PERSON>el x<PERSON>'uk xujel", "subtitles.entity.player.attack.strong": "<PERSON><PERSON><PERSON> majel", "subtitles.entity.player.attack.sweep": "Majel xchi'uk mesov", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON><PERSON> majel", "subtitles.entity.player.burp": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.player.death": "<PERSON><PERSON><PERSON> li j<PERSON><PERSON><PERSON>", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON><PERSON> li j<PERSON>", "subtitles.entity.player.hurt": "<PERSON><PERSON><PERSON> li j<PERSON><PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "Chjik'av li j<PERSON>jimole", "subtitles.entity.player.hurt_on_fire": "Chk'ak' li <PERSON><PERSON>", "subtitles.entity.player.levelup": "Chlekub li j<PERSON>ji<PERSON>le", "subtitles.entity.player.teleport": "Chtsakat li j<PERSON>", "subtitles.entity.polar_bear.ambient": "Ta xjiet li sakil oso chone", "subtitles.entity.polar_bear.ambient_baby": "Ch<PERSON><PERSON>jon li yol sakil oso chone", "subtitles.entity.polar_bear.death": "<PERSON><PERSON>j li sakil oso chone", "subtitles.entity.polar_bear.hurt": "<PERSON><PERSON>j li sakil oso chone", "subtitles.entity.polar_bear.warning": "Ta xlomomet li sakil oso chone", "subtitles.entity.potion.splash": "Ch<PERSON>linat li limetae", "subtitles.entity.potion.throw": "Limeta tenbil", "subtitles.entity.puffer_fish.blow_out": "Chlok'esbat yik'al li ch'ixal choye", "subtitles.entity.puffer_fish.blow_up": "Chtik'bat yik'al li ch'ixal choye", "subtitles.entity.puffer_fish.death": "<PERSON><PERSON><PERSON> li ch'ixal choye", "subtitles.entity.puffer_fish.flop": "Chpochpunaj li ch'ixal choye", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON> li ch'ixal choye", "subtitles.entity.puffer_fish.sting": "<PERSON><PERSON><PERSON><PERSON> li ch'ixal choye", "subtitles.entity.rabbit.ambient": "Ch-ok' li t'ule", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON><PERSON> li t'ule", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> li t'ule", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON><PERSON> li t'ule", "subtitles.entity.rabbit.jump": "Chbit li t'ule", "subtitles.entity.ravager.ambient": "Ch<PERSON>a<PERSON> li j<PERSON>van<PERSON>e", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON><PERSON> li jsokesvaneje", "subtitles.entity.ravager.celebrate": "Tspas k'in li jsokesvaneje", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON> li jsokesvaneje", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON> li jsokesvaneje", "subtitles.entity.ravager.roar": "Ta xlomomet li jsokesvaneje", "subtitles.entity.ravager.step": "Chxanav li jsokesvaneje", "subtitles.entity.ravager.stunned": "Ch'ay yo'on li jsokesvaneje", "subtitles.entity.salmon.death": "<PERSON><PERSON><PERSON> li xalmone", "subtitles.entity.salmon.flop": "Chpochpunaj li xalmone", "subtitles.entity.salmon.hurt": "<PERSON><PERSON><PERSON> li xalmone", "subtitles.entity.sheep.ambient": "<PERSON>-<PERSON><PERSON> li chije", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON> li chije", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON> li chije", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> li <PERSON>e", "subtitles.entity.shulker.close": "Chmak li shulkere", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> li shul<PERSON>e", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> li shul<PERSON>e", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> li shulkere", "subtitles.entity.shulker.shoot": "Cht'o<PERSON><PERSON> li shulkere", "subtitles.entity.shulker.teleport": "Tstsak sba li shulkere", "subtitles.entity.shulker_bullet.hit": "Cht'om li sbala shulkere", "subtitles.entity.shulker_bullet.hurt": "Ta sok li sbala shulkere", "subtitles.entity.silverfish.ambient": "Ta sisibaj li tonilchoye", "subtitles.entity.silverfish.death": "<PERSON><PERSON><PERSON> li ton<PERSON>ye", "subtitles.entity.silverfish.hurt": "<PERSON><PERSON><PERSON> li ton<PERSON>ye", "subtitles.entity.skeleton.ambient": "Chnik li jbakubele", "subtitles.entity.skeleton.converted_to_stray": "Chk'ataj ta jch'aybail li jbakubele", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON> li j<PERSON>e", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> li j<PERSON>", "subtitles.entity.skeleton.shoot": "Cht'omesvan li j<PERSON>e", "subtitles.entity.skeleton_horse.ambient": "Ch-avan li jich'il ka'e", "subtitles.entity.skeleton_horse.death": "<PERSON><PERSON><PERSON> li jich'il ka'e", "subtitles.entity.skeleton_horse.hurt": "<PERSON><PERSON>j li jich'il ka'e", "subtitles.entity.skeleton_horse.jump_water": "Chbit li jich'il ka'e", "subtitles.entity.skeleton_horse.swim": "Chnux li jich'il ka'e", "subtitles.entity.slime.attack": "Ch<PERSON>j<PERSON> li simchone", "subtitles.entity.slime.death": "<PERSON><PERSON><PERSON> li simchone", "subtitles.entity.slime.hurt": "<PERSON><PERSON><PERSON> li simchone", "subtitles.entity.slime.squish": "Chbit li simchone", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> li j-u<PERSON>'ivan<PERSON>e", "subtitles.entity.sniffer.digging": "Chjots'omaj li j-uts'ivaneje", "subtitles.entity.sniffer.digging_stop": "Chvechi li j-uts'ivaneje", "subtitles.entity.sniffer.drop_seed": "T<PERSON>ajes jbej bek'il li j-uts'ivaneje", "subtitles.entity.sniffer.eat": "Ch<PERSON>' li j-u<PERSON>'i<PERSON><PERSON>e", "subtitles.entity.sniffer.egg_crack": "Cht'aj li ton j-uts'ivaneje", "subtitles.entity.sniffer.egg_hatch": "Chvok' li ton j-uts'ivaneje", "subtitles.entity.sniffer.happy": "Chmuyubaj li j-uts'ivaneje", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON> li j-u<PERSON>'ivan<PERSON>e", "subtitles.entity.sniffer.idle": "<PERSON>-<PERSON><PERSON> li j-u<PERSON>'i<PERSON>e", "subtitles.entity.sniffer.scenting": "Ch-u<PERSON>'ivan li j-uts'ivan<PERSON>e", "subtitles.entity.sniffer.searching": "Ta sa'van li j-uts'ivaneje", "subtitles.entity.sniffer.sniffing": "Ch-u<PERSON>'ivan li j-uts'ivan<PERSON>e", "subtitles.entity.sniffer.step": "Chxanav li j-uts'ivaneje", "subtitles.entity.snow_golem.death": "<PERSON><PERSON><PERSON> li j<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "subtitles.entity.snow_golem.hurt": "<PERSON><PERSON><PERSON> li j<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "subtitles.entity.snowball.throw": "Chtenat li bola taive", "subtitles.entity.spider.ambient": "Ta sisibaj li ome", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON> li ome", "subtitles.entity.spider.hurt": "<PERSON><PERSON><PERSON> li ome", "subtitles.entity.squid.ambient": "Chnux li muk'ta ja'al-ome", "subtitles.entity.squid.death": "<PERSON><PERSON><PERSON> li muk'ta ja'al-ome", "subtitles.entity.squid.hurt": "<PERSON><PERSON><PERSON> li muk'ta ja'al-ome", "subtitles.entity.squid.squirt": "Tsten stinta li muk'ta ja'al-ome", "subtitles.entity.stray.ambient": "Chnik li jch'aybaile", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON> li j<PERSON>'<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.stray.hurt": "<PERSON><PERSON><PERSON> li j<PERSON>'<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON> li jxan<PERSON> tsuk'e", "subtitles.entity.strider.eat": "<PERSON><PERSON>' li jxan<PERSON> tsuk'e", "subtitles.entity.strider.happy": "Chk'ejin li jxanbal tsuk'e", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON> li jxanbal tsuk'e", "subtitles.entity.strider.idle": "Chkits'its'uj li jxanbal tsuk'e", "subtitles.entity.strider.retreat": "<PERSON><PERSON>'<PERSON>' li jxanbal tsuk'e", "subtitles.entity.tadpole.death": "Ch<PERSON>j li avobe", "subtitles.entity.tadpole.flop": "Chpochpunaj li avobe", "subtitles.entity.tadpole.grow_up": "Chch'i li avobe", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON>j li avobe", "subtitles.entity.tnt.primed": "Chtsanat li rina<PERSON>ae", "subtitles.entity.tropical_fish.death": "<PERSON><PERSON><PERSON> li jab<PERSON><PERSON> choye", "subtitles.entity.tropical_fish.flop": "Chpochpunaj li jabnaltikal choye", "subtitles.entity.tropical_fish.hurt": "<PERSON><PERSON><PERSON> li jab<PERSON><PERSON> choye", "subtitles.entity.turtle.ambient_land": "Chkits'its'uj li oke", "subtitles.entity.turtle.death": "<PERSON><PERSON><PERSON> li oke", "subtitles.entity.turtle.death_baby": "<PERSON><PERSON>j li yol oke", "subtitles.entity.turtle.egg_break": "Ta sok li ton oke", "subtitles.entity.turtle.egg_crack": "Cht'aj li ton oke", "subtitles.entity.turtle.egg_hatch": "Chvok' li ton oke", "subtitles.entity.turtle.hurt": "<PERSON><PERSON><PERSON> li oke", "subtitles.entity.turtle.hurt_baby": "<PERSON><PERSON><PERSON> li yol oke", "subtitles.entity.turtle.lay_egg": "<PERSON><PERSON><PERSON> li oke", "subtitles.entity.turtle.shamble": "Ta xkilet li oke", "subtitles.entity.turtle.shamble_baby": "Ta xkilet li yol oke", "subtitles.entity.turtle.swim": "<PERSON><PERSON>x li oke", "subtitles.entity.vex.ambient": "Ta sa'sunvan li jsa'sunvaneje", "subtitles.entity.vex.charge": "<PERSON>-<PERSON>' li j<PERSON>'<PERSON><PERSON>e", "subtitles.entity.vex.death": "<PERSON><PERSON><PERSON> li j<PERSON>'sun<PERSON>e", "subtitles.entity.vex.hurt": "<PERSON><PERSON><PERSON> li j<PERSON>'sun<PERSON>e", "subtitles.entity.villager.ambient": "Chvulvunaj li jbik'it lume", "subtitles.entity.villager.celebrate": "T<PERSON>as k'in li jbik'it lume", "subtitles.entity.villager.death": "Chlaj li jbik'it lume", "subtitles.entity.villager.hurt": "Chyaj li jbik'it lume", "subtitles.entity.villager.no": "Ch<PERSON>'ajvan li jbik'it lume", "subtitles.entity.villager.trade": "Chchonolaj li jbik'it lume", "subtitles.entity.villager.work_armorer": "Ch-abtej li jpas eskuroe", "subtitles.entity.villager.work_butcher": "Ch-abt<PERSON> li jmil-vaka<PERSON>", "subtitles.entity.villager.work_cartographer": "Ch-abtej li ts'iba<PERSON>m mapae", "subtitles.entity.villager.work_cleric": "Ch-abt<PERSON> li palee", "subtitles.entity.villager.work_farmer": "Ch-abt<PERSON> li j<PERSON>", "subtitles.entity.villager.work_fisherman": "Ch-abt<PERSON> li jtsak choye", "subtitles.entity.villager.work_fletcher": "Ch-abtej li jpas yolobe", "subtitles.entity.villager.work_leatherworker": "Ch-abtej li jchon nukule", "subtitles.entity.villager.work_librarian": "<PERSON>-a<PERSON><PERSON> li j<PERSON>n", "subtitles.entity.villager.work_mason": "Ch-abtej li jpas nae", "subtitles.entity.villager.work_shepherd": "Ch-abt<PERSON> li jk'el-chije", "subtitles.entity.villager.work_toolsmith": "Ch-abtej li jpas tak'ine", "subtitles.entity.villager.work_weaponsmith": "Ch-abtej li j<PERSON> bojo<PERSON><PERSON>", "subtitles.entity.villager.yes": "Ch<PERSON>'unvan li jbik'it lume", "subtitles.entity.vindicator.ambient": "Chvulvunaj li jpakvaneje", "subtitles.entity.vindicator.celebrate": "Tspas k'in li jpakvaneje", "subtitles.entity.vindicator.death": "Ch<PERSON>j li j<PERSON>k<PERSON>e", "subtitles.entity.vindicator.hurt": "Ch<PERSON>j li j<PERSON>k<PERSON>e", "subtitles.entity.wandering_trader.ambient": "Chvulvunaj li va'vunajel jchonolajele", "subtitles.entity.wandering_trader.death": "Chlaj li va'vunajel jchonolajele", "subtitles.entity.wandering_trader.disappeared": "Chch'ay li va'vunajel jchonolajele", "subtitles.entity.wandering_trader.drink_milk": "Chuch' ya'lel xchu' vakax li va'vunajel jcho<PERSON>le", "subtitles.entity.wandering_trader.drink_potion": "Chuch' poxil li va'v<PERSON>jel jchono<PERSON>jele", "subtitles.entity.wandering_trader.hurt": "Ch<PERSON>j li va'v<PERSON>jel jchonolajele", "subtitles.entity.wandering_trader.no": "Chp'ajvan li va'vunajel jchonolajele", "subtitles.entity.wandering_trader.reappeared": "Chvinaj li va'vunajel jchonolajele", "subtitles.entity.wandering_trader.trade": "Chchonolaj li va'vunajel jchonolajele", "subtitles.entity.wandering_trader.yes": "Chch'unvan li va'v<PERSON>jel jcho<PERSON>lajele", "subtitles.entity.warden.agitated": "Ta xjiet li pukujil j<PERSON>'ene", "subtitles.entity.warden.ambient": "<PERSON><PERSON>'nal-ok' li j<PERSON><PERSON>'ene", "subtitles.entity.warden.angry": "<PERSON>-<PERSON><PERSON> li j<PERSON>'ene", "subtitles.entity.warden.attack_impact": "Ch<PERSON>jvan li j<PERSON>'ene", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON> li j<PERSON><PERSON>'ene", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON><PERSON><PERSON> li <PERSON>'ene", "subtitles.entity.warden.emerge": "<PERSON><PERSON><PERSON> li j<PERSON>'ene", "subtitles.entity.warden.heartbeat": "Ch<PERSON>'itulaj li yo'onton j<PERSON><PERSON>'ene", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON> li j<PERSON>'ene", "subtitles.entity.warden.listening": "Ch-a'<PERSON><PERSON> li <PERSON>'ene", "subtitles.entity.warden.listening_angry": "Ch-a'ivan li pukujil j<PERSON>'ene", "subtitles.entity.warden.nearby_close": "Chnopaj li jchabic<PERSON>'ene", "subtitles.entity.warden.nearby_closer": "Te oy toj nopol li j<PERSON>'ene", "subtitles.entity.warden.nearby_closest": "Te oy nopol tajmek li jchabich'ene", "subtitles.entity.warden.roar": "Ta xlomomet li j<PERSON>'ene", "subtitles.entity.warden.sniff": "Ch-<PERSON><PERSON>'<PERSON><PERSON> li <PERSON>'ene", "subtitles.entity.warden.sonic_boom": "Cht'om li j<PERSON><PERSON>'ene", "subtitles.entity.warden.sonic_charge": "Chchapaj li j<PERSON><PERSON>'ene", "subtitles.entity.warden.step": "Chxanav li jchabic<PERSON>'ene", "subtitles.entity.warden.tendril_clicks": "Chnikik li xulubtak jchabich'ene", "subtitles.entity.wind_charge.throw": "Chtenat li nojel ik'e", "subtitles.entity.wind_charge.wind_burst": "Cht'om li sutub-ik'e", "subtitles.entity.witch.ambient": "Chtse'in li chopol antse", "subtitles.entity.witch.celebrate": "T<PERSON>as k'in li chopol antse", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON> li chopol antse", "subtitles.entity.witch.drink": "Ch-u<PERSON>'baj li chopol antse", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> li chopol antse", "subtitles.entity.witch.throw": "Tsten poxiletik li chopol antse", "subtitles.entity.wither.ambient": "<PERSON><PERSON><PERSON><PERSON> li <PERSON>", "subtitles.entity.wither.death": "<PERSON><PERSON><PERSON> <PERSON>i <PERSON>", "subtitles.entity.wither.hurt": "<PERSON><PERSON><PERSON> <PERSON>i <PERSON>", "subtitles.entity.wither.shoot": "<PERSON><PERSON><PERSON><PERSON> li <PERSON>", "subtitles.entity.wither.spawn": "<PERSON><PERSON> kolem", "subtitles.entity.wither_skeleton.ambient": "Chnik li jbakubele yu'un <PERSON>er", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON><PERSON> li j<PERSON><PERSON><PERSON>e yu'un <PERSON>er", "subtitles.entity.wither_skeleton.hurt": "<PERSON><PERSON><PERSON> li j<PERSON><PERSON><PERSON>e yu'un <PERSON>", "subtitles.entity.wolf.ambient": "Ta xjuxlajet li ok'ile", "subtitles.entity.wolf.bark": "<PERSON><PERSON><PERSON><PERSON> li ok'ile", "subtitles.entity.wolf.death": "<PERSON><PERSON><PERSON> li ok'ile", "subtitles.entity.wolf.growl": "Ta xjiet li ok'ile", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON> li ok'ile", "subtitles.entity.wolf.pant": "Xjuxlajet li ok'ile", "subtitles.entity.wolf.shake": "Ta xt'elet li ok'ile", "subtitles.entity.wolf.whine": "<PERSON><PERSON><PERSON> li ok'ile", "subtitles.entity.zoglin.ambient": "Ta xjiet li zogline", "subtitles.entity.zoglin.angry": "Ta xjiet li pukujil zogline", "subtitles.entity.zoglin.attack": "Ch<PERSON>jvan li zogline", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> li zogline", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> li zogline", "subtitles.entity.zoglin.step": "Chxanav li zogline", "subtitles.entity.zombie.ambient": "Ta xjiet li jchamele", "subtitles.entity.zombie.attack_wooden_door": "<PERSON><PERSON><PERSON>t li ti'nae", "subtitles.entity.zombie.break_wooden_door": "Ta sok li ti'nae", "subtitles.entity.zombie.converted_to_drowned": "Chjik'av li j<PERSON><PERSON>e", "subtitles.entity.zombie.death": "<PERSON><PERSON><PERSON> li j<PERSON><PERSON>e", "subtitles.entity.zombie.destroy_egg": "Chtek'anat li ton oke", "subtitles.entity.zombie.hurt": "<PERSON><PERSON><PERSON> li j<PERSON><PERSON>e", "subtitles.entity.zombie.infect": "Ch-ipajesvan li j<PERSON>e", "subtitles.entity.zombie_horse.ambient": "<PERSON>-a<PERSON> li i<PERSON> ka'e", "subtitles.entity.zombie_horse.death": "<PERSON><PERSON><PERSON> li ipa<PERSON>bil ka'e", "subtitles.entity.zombie_horse.hurt": "<PERSON><PERSON><PERSON> li ipa<PERSON> ka'e", "subtitles.entity.zombie_villager.ambient": "Ta xjiet li ipajesbil jbik'it lume", "subtitles.entity.zombie_villager.converted": "Chkol li ipajesbil jbik'it lume", "subtitles.entity.zombie_villager.cure": "<PERSON><PERSON>ts chich' ik' li ipajesbil jbik'it lume", "subtitles.entity.zombie_villager.death": "Chlaj li ipajesbil jbik'it lume", "subtitles.entity.zombie_villager.hurt": "Chyaj li ipajesbil jbik'it lume", "subtitles.entity.zombified_piglin.ambient": "Ch-avan li ipajesbil pigline", "subtitles.entity.zombified_piglin.angry": "Ch-avan li pukujil ipajesbil pigline", "subtitles.entity.zombified_piglin.death": "Chlaj li ipajesbil pigline", "subtitles.entity.zombified_piglin.hurt": "Chyaj li ipajesbil pigline", "subtitles.event.mob_effect.bad_omen": "Chts'akat li chopol alele", "subtitles.event.mob_effect.raid_omen": "Chnopaj li invasione", "subtitles.event.mob_effect.trial_omen": "Chnopaj li chopol tsatsalile", "subtitles.event.raid.horn": "<PERSON><PERSON> a<PERSON>", "subtitles.item.armor.equip": "Chak'be li alma<PERSON>rae", "subtitles.item.armor.equip_chain": "Ch<PERSON>j li almarura nuti'tak'ine", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON><PERSON> li almarura riamantee", "subtitles.item.armor.equip_elytra": "Xk'uxuxetelik end-xik'etik", "subtitles.item.armor.equip_gold": "Ta xk'uxuxet li almarura k'anal tak'ine", "subtitles.item.armor.equip_iron": "Ta xk'uxuxet li almarura tak'ine", "subtitles.item.armor.equip_leather": "Ta xk'uxuxet li nukul almarurae", "subtitles.item.armor.equip_netherite": "Ta xk'uxuxet li almarura neteritae", "subtitles.item.armor.equip_turtle": "<PERSON><PERSON>' s<PERSON> li spat oke", "subtitles.item.armor.equip_wolf": "<PERSON><PERSON>'be ya<PERSON><PERSON><PERSON> li ok'ile", "subtitles.item.armor.unequip_wolf": "Tslok'esbe yalma<PERSON>ra li ok'ile", "subtitles.item.axe.scrape": "<PERSON><PERSON><PERSON><PERSON>van li ek'ele", "subtitles.item.axe.strip": "Ch<PERSON>van li ek'ele", "subtitles.item.axe.wax_off": "Tslok'es li chabe", "subtitles.item.bone_meal.use": "Ta xchak'ak'et li ts'ub bakelile", "subtitles.item.book.page_turn": "Ta xk'uxuxet li vune", "subtitles.item.book.put": "Chak' li vune", "subtitles.item.bottle.empty": "Chxokob li limetae", "subtitles.item.bottle.fill": "Chnoj li limetae", "subtitles.item.brush.brushing.generic": "<PERSON><PERSON> chch'ul", "subtitles.item.brush.brushing.gravel": "<PERSON><PERSON> chch'ul li xixibtone", "subtitles.item.brush.brushing.gravel.complete": "Chlaj ta xch'ulel li xixibtone", "subtitles.item.brush.brushing.sand": "Yakal chch'ul li yi'e", "subtitles.item.brush.brushing.sand.complete": "Chlaj ta xch'ulel li yi'e", "subtitles.item.bucket.empty": "Chxokob li valtee", "subtitles.item.bucket.fill": "<PERSON><PERSON>j li valtee", "subtitles.item.bucket.fill_axolotl": "Axolote tambil", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON> tsa<PERSON><PERSON>", "subtitles.item.bucket.fill_tadpole": "Avob tsakbil", "subtitles.item.bundle.drop_contents": "Chxokob li morale", "subtitles.item.bundle.insert": "<PERSON><PERSON> la<PERSON>", "subtitles.item.bundle.insert_fail": "Noj xa li morale", "subtitles.item.bundle.remove_one": "<PERSON>em lo<PERSON>'esbil", "subtitles.item.chorus_fruit.teleport": "Chtsakat li j<PERSON>", "subtitles.item.crop.plant": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON>", "subtitles.item.crossbow.charge": "Chnoj li alkoltak'ine", "subtitles.item.crossbow.hit": "Chmajvan li yolobe", "subtitles.item.crossbow.load": "Nojem li alkoltak'ine", "subtitles.item.crossbow.shoot": "Cht'omesvan li alkoltak'ine", "subtitles.item.dye.use": "Ch-i<PERSON>'ubt<PERSON>van li tintae", "subtitles.item.elytra.flying": "Xux<PERSON><PERSON><PERSON>", "subtitles.item.firecharge.use": "Chtenat li bola k'ok'e", "subtitles.item.flintandsteel.use": "Jtsanvan<PERSON>", "subtitles.item.glow_ink_sac.use": "Ch-i<PERSON>'ubt<PERSON>van li nop'ol moral tintae", "subtitles.item.goat_horn.play": "<PERSON><PERSON><PERSON> li kachu <PERSON>une", "subtitles.item.hoe.till": "Tslok osil li asarone", "subtitles.item.honey_bottle.drink": "Yakal chbik'van", "subtitles.item.honeycomb.wax_on": "<PERSON>kal chak' chab", "subtitles.item.horse_armor.unequip": "Chlok'es<PERSON> ya<PERSON> li ka'e", "subtitles.item.ink_sac.use": "Ch-i<PERSON>'ubtasvan li moral tintae", "subtitles.item.lead.break": "<PERSON><PERSON><PERSON> li ch'ojone", "subtitles.item.lead.tied": "Ch<PERSON>at li ch'ojone", "subtitles.item.lead.untied": "Chjitunat li ch'ojone", "subtitles.item.llama_carpet.unequip": "Chlok'esat li tapetee", "subtitles.item.lodestone_compass.lock": "B<PERSON><PERSON>la <PERSON>bil ta maknetita", "subtitles.item.mace.smash_air": "<PERSON><PERSON><PERSON><PERSON> li mase", "subtitles.item.mace.smash_ground": "<PERSON><PERSON><PERSON><PERSON> li mase", "subtitles.item.nether_wart.plant": "Ch'okte' avbil", "subtitles.item.ominous_bottle.dispose": "Ta sok li limetae", "subtitles.item.saddle.unequip": "Chlok'esat li xilae", "subtitles.item.shears.shear": "Ta set li texerexe", "subtitles.item.shears.snip": "Chsetvan li texerexe", "subtitles.item.shield.block": "Chpojvan li eskuroe", "subtitles.item.shovel.flatten": "Tspach' osil li palae", "subtitles.item.spyglass.stop_using": "Tsk'ej sba li katale<PERSON>e", "subtitles.item.spyglass.use": "<PERSON><PERSON><PERSON>' s<PERSON> li katalejoe", "subtitles.item.totem.use": "Chtsanat li toteme", "subtitles.item.trident.hit": "Ch<PERSON>jvan li trirentee", "subtitles.item.trident.hit_ground": "Ch<PERSON> li trirentee", "subtitles.item.trident.return": "Ta sut talel li trirentee", "subtitles.item.trident.riptide": "Ta xyuk'lajet li trirentee", "subtitles.item.trident.throw": "Cht'om li trirentee", "subtitles.item.trident.thunder": "Chik' anjeletik li trirentee", "subtitles.item.wolf_armor.break": "Ta sok li almarura sventa ok'ile", "subtitles.item.wolf_armor.crack": "Cht'aj li almarura sventa ok'ile", "subtitles.item.wolf_armor.damage": "Ta sokesat li almarura sventa ok'ile", "subtitles.item.wolf_armor.repair": "Chmeltsaj li almarura sventa ok'ile", "subtitles.particle.soul_escape": "Chjatav li ch'ulelale", "subtitles.ui.cartography_table.take_result": "Mapa lok'tabil", "subtitles.ui.hud.bubble_pop": "<PERSON><PERSON><PERSON> li ik'e", "subtitles.ui.loom.take_result": "Abtejebal pok' tunesbil", "subtitles.ui.stonecutter.take_result": "Javton tunesbil", "subtitles.weather.rain": "Chak' vo'", "symlink_warning.message": "Mi chavinajes balumiletik ta archivoetik xchi'uk ts'akeletik ta senyaile, ja' nan xi'el sba mi mu lek chana' li k'usi yakal chapase. Vu'lano %s sventa chachan.", "symlink_warning.message.pack": "Mi chavinajes paketeetik ta archivoetik xchi'uk ts'akeletik ta senyaile, ja' nan xi'el sba mi mu lek chana' li k'usi yakal chapase. Vu'lano %s sventa chachan.", "symlink_warning.message.world": "Mi chavinajes balumiletik ta archivoetik xchi'uk ts'akeletik ta senyaile, ja' nan xi'el sba mi mu lek chana' li k'usi yakal chapase. Vu'lano %s sventa chachan.", "symlink_warning.more_info": "<PERSON><PERSON> <PERSON>", "symlink_warning.title": "Oy sts'akeltak ta senyail li skarpeta balumile", "symlink_warning.title.pack": "Oy sts'akeltakik ta senyail li kapbil paketeetike", "symlink_warning.title.world": "Oy sts'akeltak ta senyail li skarpeta balumile", "team.collision.always": "S<PERSON><PERSON> ora", "team.collision.never": "<PERSON><PERSON><PERSON>uk o", "team.collision.pushOtherTeams": "Majbail xchi'uk jtajimoletik ta yan ekipo", "team.collision.pushOwnTeam": "Majbail xchi'uk jtajimoletik ta ko'ol ekipo", "team.notFound": "Mu ojtikinbil ekipo: %s", "team.visibility.always": "S<PERSON><PERSON> ora", "team.visibility.hideForOtherTeams": "Tsnak' s<PERSON><PERSON> yan <PERSON>k", "team.visibility.hideForOwnTeam": "Tsnak' s<PERSON>a ekipo yu'un", "team.visibility.never": "<PERSON><PERSON><PERSON>uk o", "telemetry.event.advancement_made.description": "Mi chka'ikutik smelol li k'usi chk'ot ta pasel k'alal chpasat jun porokresoe, ja' tskoltaunkutik ta ya'iel xchi'uk slekubtasel li sporokresion tajimole.", "telemetry.event.advancement_made.title": "Porokreso pasbil", "telemetry.event.game_load_times.description": "Xu' skoltaunkutik ta yilel bu sk'an lekubtaseletik ta rentimiento likel li tajimoltik li'e k'alal chat li sk'ak'alil ak'eletike.", "telemetry.event.game_load_times.title": "Sk'a<PERSON>'<PERSON><PERSON> s<PERSON> ta<PERSON>l", "telemetry.event.optional": "%s (st'<PERSON><PERSON><PERSON>)", "telemetry.event.optional.disabled": "%s (st'<PERSON><PERSON><PERSON>): Tubbil", "telemetry.event.performance_metrics.description": "Mi jna'kutik li analis rentimientoe yu'un Minecraft ta tsobol ja' tskoltaunkutik ta slekubtasel li tajimole sventa li smas-toyol-atolalik k'aneletik xchi'uk operasional sistemaetik. \nTskap sba li sversion tajimole sventa jko'oltaskutik li analisis rentimientoe xchi'uk ach' versionetik yu'un Minecraft.", "telemetry.event.performance_metrics.title": "Estaristika rentimientoetik", "telemetry.event.required": "%s (k'anbil)", "telemetry.event.world_load_times.description": "Sk'an jna'kutik k'u yepal sk'ak'alil chjok'tsaj sventa cha-och ta jun balumil xchi'uk k'uxi le'e sjel sba ta patil. Ta yilobil, k'alal jkapkutik ach' karakteristikaetik o jpaskutik muk'ta jeleletik ta teknika, sk'an jk'elkutik k'usi majel oy ta sk'ak'alil vinajeseletik.", "telemetry.event.world_load_times.title": "Sk'<PERSON><PERSON><PERSON><PERSON><PERSON> bal<PERSON>", "telemetry.event.world_loaded.description": "Mi jna'kutik k'uxi chtajinik Minecraft li jtajimoletike (k'usi manera tajimol, kiliente o jelel servilor xchi'uk sversion tajimol tstunesik le'ike) ja' tskoltaunkutik ta slekubtasel li aspektoetike mas chak'ik ta venta li jtajimoletike.\nLi tajimoltike ''vinajesbil balumil'' ja' ts'akal ta tajimoltik ''yalesbil balumil'' sventa chat k'u yepal sk'ak'alil jalijem li sesion tajimole.", "telemetry.event.world_loaded.title": "<PERSON><PERSON><PERSON><PERSON> bal<PERSON>l", "telemetry.event.world_unloaded.description": "Ts'akal ta tajimoltik ''vina<PERSON><PERSON><PERSON> balumil'' li tajimoltik li'e sventa chat k'u yepal jalijem li sesion tajimole.\n<PERSON> jali<PERSON>le (ta sekuntoetik xchi'uk sikloetik) ja' chp'isat k'alal chlaj jun sesion tajimol (k'alal chalok' ta ba'yel menu o k'alal chach'ak aba ta jun servilor).", "telemetry.event.world_unloaded.title": "<PERSON><PERSON><PERSON> bal<PERSON>l", "telemetry.property.advancement_game_time.title": "Sk'ak'<PERSON><PERSON> (sikloetik)", "telemetry.property.advancement_id.title": "ID yu'un porokreso", "telemetry.property.client_id.title": "ID yu'un kiliente", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON> j<PERSON>", "telemetry.property.dedicated_memory_kb.title": "Ak'bil avil (kB)", "telemetry.property.event_timestamp_utc.title": "<PERSON><PERSON> (UTC)", "telemetry.property.frame_rate_samples.title": "Yak'el ta ilel lok'oletik ta sekunto (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON> ta<PERSON>", "telemetry.property.game_version.title": "Sversion tajimol", "telemetry.property.launcher_name.title": "Sbi jlikestajimol", "telemetry.property.load_time_bootstrap_ms.title": "Sk'a<PERSON>'al<PERSON> likel (milisekuntoetik)", "telemetry.property.load_time_loading_overlay_ms.title": "Sk'ak'alil ta pantaya vina<PERSON> (milisekuntoetik)", "telemetry.property.load_time_pre_window_ms.title": "Sk'ak'alil ta ba'yel chjam li ventanae (milisekuntoetik)", "telemetry.property.load_time_total_time_ms.title": "Sjunul sk'ak'<PERSON><PERSON> (milisekuntoetik)", "telemetry.property.minecraft_session_id.title": "ID yu'un sesion Minecraft", "telemetry.property.new_world.title": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>l", "telemetry.property.number_of_samples.title": "Yatolalik ak'el ta ileletik", "telemetry.property.operating_system.title": "Operasional sistema", "telemetry.property.opt_in.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "telemetry.property.platform.title": "Toyobil", "telemetry.property.realms_map_content.title": "K'usitik yu'un smapa Realms (sbi bik'it tajimol)", "telemetry.property.render_distance.title": "<PERSON><PERSON><PERSON> sle<PERSON>l imajen", "telemetry.property.render_time_samples.title": "Ak'el ta ileletik yu'un sk'ak'alil slekubtasel lok'ol", "telemetry.property.seconds_since_load.title": "Sk'ak'alil k'u sjalil vinajesat (sekuntoetik)", "telemetry.property.server_modded.title": "<PERSON><PERSON><PERSON> jelbil", "telemetry.property.server_type.title": "<PERSON><PERSON> servilor", "telemetry.property.ticks_since_load.title": "Sk'ak'alil k'u sjalil vinajesat (sikloetik)", "telemetry.property.used_memory_samples.title": "Tunesbil avil RAM", "telemetry.property.user_id.title": "ID yu'un jtunesvanej", "telemetry.property.world_load_time_ms.title": "Sk'a<PERSON>'<PERSON><PERSON> bal<PERSON>l (milisekuntoetik)", "telemetry.property.world_session_id.title": "ID yu'un sesion balumil", "telemetry_info.button.give_feedback": "<PERSON><PERSON><PERSON> <PERSON>tal", "telemetry_info.button.privacy_statement": "<PERSON><PERSON><PERSON><PERSON>", "telemetry_info.button.show_data": "Tsk'el kalelal", "telemetry_info.opt_in.description": "Ta jch'un ta stakel li st'ujelal telemetriae mi jk'ane", "telemetry_info.property_title": "<PERSON><PERSON><PERSON>", "telemetry_info.screen.description": "Mi jtamkutik alelal, ja' tskoltaunkutik ta slekubtasel Minecraft k'alal sbeiltasunkutik ta rireksionetik ti chak'ik ta venta li kajtajimoltakutike.\nXu' chatak mantaletik ek sventa chakoltaunkutik ta slekubtasel o Minecraft.", "telemetry_info.screen.title": "Stamelik alelal telemetriaetik", "test.error.block_property_mismatch": "<PERSON> u'uninele %s sk'an ch-elanuk %s, ch-elan ox %s", "test.error.block_property_missing": "Sk'an jun yu'uninel kubo, li u'uninele %s sk'an ch-elanuk %s", "test.error.entity_property": "Chopolaj li entirale %s ta preva: %s", "test.error.entity_property_details": "<PERSON><PERSON><PERSON> li entirale %s ta preva: %s, sk'an: %s, ch-elan ox: %s", "test.error.expected_block": "Sk'an kubo %s, la stam %s", "test.error.expected_block_tag": "Sk'an kubo ta #%s, la stam %s", "test.error.expected_container_contents": "<PERSON> k'ejobile sk'an chu'unin: %s", "test.error.expected_container_contents_single": "Li k'ejobile sk'an chu'unin jun no'ox: %s", "test.error.expected_empty_container": "Sk'an pojol li k'ejobile", "test.error.expected_entity": "Sk'an %s", "test.error.expected_entity_around": "%s sk'an te oyuk ta sjoyobal %s, %s, %s", "test.error.expected_entity_count": "Sk'an %s entiraletik ta tos %s, la sta %s", "test.error.expected_entity_data": "Li yalelaltakik entiraletike sk'an ch-elanuk: %s, ch-elan ox: %s", "test.error.expected_entity_data_predicate": "Mu sko'oltasik %s li yalelaltakik entiraletike", "test.error.expected_entity_effect": "%s sk'an chu'unin ejekto %s %s", "test.error.expected_entity_having": "Sk'an chu'unin %s li sk'ejeb entirale", "test.error.expected_entity_holding": "Sk'an sjap %s li entirale", "test.error.expected_entity_in_test": "%s sk'an te oyuk ta preva", "test.error.expected_entity_not_touching": "%s mu sk'an ox sk'ojuk %s, %s, %s (parsial: %s, %s, %s)", "test.error.expected_entity_touching": "%s sk'an sk'ojuk %s, %s, %s (parsial: %s, %s, %s)", "test.error.expected_item": "Sk'an item ta tos %s", "test.error.expected_items_count": "Sk'an %s k'usitik ta tos %s, la sta %s", "test.error.fail": "Ts'akal xa li k'anvanel chopolaletike", "test.error.invalid_block_type": "La sta jtos mu malabil kubo: %s", "test.error.missing_block_entity": "Sk'an jun entiral kubo", "test.error.position": "%s ta %s, %s, %s (parsial: %s, %s, %s) ta siklo %s", "test.error.sequence.condition_already_triggered": "Tsakal xa li kontisione ta %s", "test.error.sequence.condition_not_triggered": "Mu tsakal li kontisione", "test.error.sequence.invalid_tick": "Lek abtel ta chopol siklo: sk'an %s", "test.error.sequence.not_completed": "Laj li prevae ta ba'yel ts'akaluk li sekuensiae", "test.error.set_biome": "Mu xu' la skomes li osile sventa li prevae", "test.error.spawn_failure": "Mu xu' la smeltsan li entirale %s", "test.error.state_not_equal": "Chopol estaro. Sk'an %s, ch-elan ox %s", "test.error.structure.failure": "<PERSON><PERSON><PERSON> k'alal chak' ox li preva estrukturae sventa ''%s''", "test.error.tick": "%s ta siklo %s", "test.error.ticking_without_structure": "A'ibaj li xch'ijch'unel prevae ta ba'yel chak' li esturukturae", "test.error.timeout.no_result": "Mu la spas kanal o chopolaj ta o'lol %s siklo", "test.error.timeout.no_sequences_finished": "Ch'abal sekuensiaetik lajemik ta %s siklo", "test.error.too_many_entities": "Sk'an te oyuk jun no'ox %s ta sjoyobal %s, %s, %s, pe la sta %s", "test.error.unexpected_block": "Mu sk'an ox ch-elanuk %s li kuboe", "test.error.unexpected_entity": "Mu sk'an ox te oyuk li %se", "test.error.unexpected_item": "Mu sk'an ox jun item ta tos %s", "test.error.unknown": "Mu ojtikinbil utilal chopolajel: %s", "test.error.value_not_equal": "Li %se sk'an ch-elanuk %s, ch-elan ox %s", "test.error.wrong_block_entity": "Chopol tos entiral kubo: %s", "test_block.error.missing": "Sk'an li kuboe %s ta estruktura preva", "test_block.error.too_many": "Ep tajmek kuboetik %s", "test_block.invalid_timeout": "Chopol sk'a<PERSON>'<PERSON><PERSON> (%s): sk'an ch-elan jun positivo atolal ta sikloetik", "test_block.message": "Mantal:", "test_block.mode.accept": "Ch'unel", "test_block.mode.fail": "<PERSON><PERSON><PERSON><PERSON>", "test_block.mode.log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test_block.mode.start": "<PERSON><PERSON>", "test_block.mode_info.accept": "Ch'unel: chch'un jun paskanal sventa (jk'os) preva", "test_block.mode_info.fail": "Chopolajel: ch<PERSON><PERSON><PERSON> li prevae", "test_block.mode_info.log": "Ts'i<PERSON><PERSON>:, tsts'iba jun mantal", "test_block.mode_info.start": "Likesel: li s<PERSON>b jun prevae", "test_instance.action.reset": "<PERSON><PERSON>'likes xchi'uk svinajes", "test_instance.action.run": "Tsvinajes xchi'uk chabtelan", "test_instance.action.save": "Tsk'ej li esturukturae", "test_instance.description.batch": "Tsobol: %s", "test_instance.description.failed": "Chopolaj: %s", "test_instance.description.function": "Tunelal: %s", "test_instance.description.invalid_id": "Chopol ID yu'un preva", "test_instance.description.no_test": "<PERSON>'abal jun preva", "test_instance.description.structure": "Esturuktura: %s", "test_instance.description.type": "Tos: %s", "test_instance.type.block_based": "Preva ta kuboetik", "test_instance.type.function": "Preva ta u'ninbil tunelal", "test_instance_block.entities": "Entiraletik:", "test_instance_block.error.no_test": "<PERSON> xu' chab<PERSON>an li yilobil prevae ta %s, %s, %s yu'un yabtelanoj jun preva", "test_instance_block.error.no_test_structure": "Mu xu' chab<PERSON>an li yilobil prevae ta %s, %s, %s yu'un ch'abal esturuktura preva", "test_instance_block.error.unable_to_save": "Mu xu' sk'ej li spalantiya esturukturae sventa yak'el sk'elel preva ta %s, %s, %s", "test_instance_block.invalid": "[chopol]", "test_instance_block.reset_success": "Lek la xcha'likes li prevae: %s", "test_instance_block.rotation": "<PERSON><PERSON><PERSON><PERSON>:", "test_instance_block.size": "<PERSON><PERSON><PERSON><PERSON>ul esturuktura preva", "test_instance_block.starting": "Lik li prevae %s", "test_instance_block.test_id": "ID yu'un yilobil preva", "title.32bit.deprecation": "Ja' ta 32 bit li asistemae. ¡Li' tspajesot nan ta tajinel ta patil, yu'un ta sk'an jun sistema ta 64 bit!", "title.32bit.deprecation.realms": "Minecraft po'ot ta sk'an jun sistema ta 64 bit, li k'usi spajesot nan ta tajinel o stunesel Realms li ta rispositivo li'e. Ta xak'an chapajes k'uk elanuk nochanel ta Realms atuk.", "title.32bit.deprecation.realms.check": "<PERSON> xak' ta ilel nixtok li mantal li'e", "title.32bit.deprecation.realms.header": "Sistema ta 32 bit tabil", "title.credits": "Copyright Mojang AB. ¡Mu chapuk!", "title.multiplayer.disabled": "Tubbil li tajinebale. K'elano li stsineltak akuentae yu'un Microsoft, avokoluk.", "title.multiplayer.disabled.banned.name": "Sk'an xajel li sbi avajtunesvaneje ta ba'yel xu' xatajin ta internet", "title.multiplayer.disabled.banned.permanent": "Pajesat ta jech'el li akuentae xchi'uk mu xu' xatajin ta internet", "title.multiplayer.disabled.banned.temporary": "Pajesat j-ok' li akuentae xchi'uk mu xu' xatajin ta internet", "title.multiplayer.lan": "<PERSON><PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "Ta<PERSON><PERSON><PERSON> (servilor yu'un yoxibaletik)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Jun no'ox jtajimol", "translation.test.args": "%s %s", "translation.test.complex": "¡Slikeb sts'ak k'op, %s%2$s nixtok %s xchi'uk %1$s ta slajeb %s xchi'uk %1$s nixtok ek!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "ka'itik me %", "translation.test.invalid2": "ka'itik me %s", "translation.test.none": "¡Ka'itik me, bal<PERSON><PERSON>!", "translation.test.world": "balumil", "trim_material.minecraft.amethyst": "Material amatista", "trim_material.minecraft.copper": "Material k'anal k’unil tak'in", "trim_material.minecraft.diamond": "Material riamante", "trim_material.minecraft.emerald": "Material esmeralta", "trim_material.minecraft.gold": "Material k'anal tak'in", "trim_material.minecraft.iron": "Material tak'in", "trim_material.minecraft.lapis": "Material lapislasuli", "trim_material.minecraft.netherite": "Material neterita", "trim_material.minecraft.quartz": "Material kuarso", "trim_material.minecraft.redstone": "Material redstone", "trim_material.minecraft.resin": "Material xuch'", "trim_pattern.minecraft.bolt": "Ornamento anjel", "trim_pattern.minecraft.coast": "Ornamento ti'il", "trim_pattern.minecraft.dune": "Ornamento runa", "trim_pattern.minecraft.eye": "Ornamento satil", "trim_pattern.minecraft.flow": "Ornamento espiral", "trim_pattern.minecraft.host": "Ornamento j-ik'vanej", "trim_pattern.minecraft.raiser": "Ornamento toyesel", "trim_pattern.minecraft.rib": "Ornamento ch'ilte'", "trim_pattern.minecraft.sentry": "Ornamento jk'el-osil", "trim_pattern.minecraft.shaper": "Ornamento morelarol", "trim_pattern.minecraft.silence": "Ornamento ch'inetel", "trim_pattern.minecraft.snout": "Ornamento eal", "trim_pattern.minecraft.spire": "Ornamento akuxa", "trim_pattern.minecraft.tide": "Ornamento balak'vo'", "trim_pattern.minecraft.vex": "Ornamento jsa'sunvanej", "trim_pattern.minecraft.ward": "Ornamento jchabivanej", "trim_pattern.minecraft.wayfinder": "Ornamento jsa'be", "trim_pattern.minecraft.wild": "Te'tikal ornamento", "tutorial.bundleInsert.description": "<PERSON><PERSON> k'usitik xchi'uk bats'i klik", "tutorial.bundleInsert.title": "¡Tuneso jlik moral!", "tutorial.craft_planks.description": "<PERSON>'elo li vun resetae.", "tutorial.craft_planks.title": "¡Meltsano tenelte'etik!", "tutorial.find_tree.description": "<PERSON><PERSON> li <PERSON>'e.", "tutorial.find_tree.title": "¡Tao jtop'ol te'!", "tutorial.look.description": "<PERSON><PERSON><PERSON> yu'un, <PERSON>o li ch'oe", "tutorial.look.title": "¡K'elavil ta ajoyobal!", "tutorial.move.description": "Bitan xchi'uk %s.", "tutorial.move.title": "¡Xanavan xchi'uk %s, %s, %s xchi'uk %s!", "tutorial.open_inventory.description": "Net'o %s.", "tutorial.open_inventory.title": "¡Jamo li ak'ejebe!", "tutorial.punch_tree.description": "Net'o o %s.", "tutorial.punch_tree.title": "¡<PERSON>ino li te'e!", "tutorial.socialInteractions.description": "Net'o %s sventa chajam", "tutorial.socialInteractions.title": "Jipetbateletik", "upgrade.minecraft.netherite_upgrade": "Le<PERSON><PERSON><PERSON><PERSON> neterita"}