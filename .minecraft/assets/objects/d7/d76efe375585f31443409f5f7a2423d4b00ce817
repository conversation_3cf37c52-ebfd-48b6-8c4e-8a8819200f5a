{"accessibility.onboarding.accessibility.button": "<PERSON>to W<PERSON>w<PERSON>...", "accessibility.onboarding.screen.narrator": "Tẹ tẹ sii lati jeki on<PERSON>hin ṣiṣẹ", "accessibility.onboarding.screen.title": "Ka<PERSON>o si Minecraft!\n\nṢe iwọ yoo fẹ lati jẹ ki Oniranran ṣiṣẹ tabi ṣabẹwo si Eto Wiwọle bi?", "addServer.add": "Ṣe", "addServer.enterIp": "Adirẹsi olupin", "addServer.enterName": "Oruk<PERSON> olupin", "addServer.resourcePack": "Awopọ Awọn apamọ olupin", "addServer.resourcePack.disabled": "Alaabo", "addServer.resourcePack.enabled": "Ti ṣiṣẹ", "addServer.resourcePack.prompt": "Gbọ", "addServer.title": "Ṣatunkọ Alaye Nẹtiwọki", "advMode.command": "<PERSON><PERSON>", "advMode.mode": "Mode", "advMode.mode.auto": "<PERSON><PERSON><PERSON>", "advMode.mode.autoexec.bat": "Ṣiṣẹ Nigbagbogbo", "advMode.mode.conditional": "<PERSON>", "advMode.mode.redstone": "<PERSON><PERSON><PERSON>", "advMode.mode.redstoneTriggered": "Nilo Redstone", "advMode.mode.sequence": "S po", "advMode.mode.unconditional": "<PERSON><PERSON> ipo", "advMode.notAllowed": "Gbọdọ jẹ oṣere opped ni ipo ẹda", "advMode.notEnabled": "Awọn bulọọki pipaṣẹ ko ṣiṣẹ lori olupin yii", "advMode.previousOutput": "I<PERSON><PERSON>ju ti tẹlẹ", "advMode.setCommand": "Ṣeto <PERSON>ana <PERSON>na fun Àkọsílẹ", "advMode.setCommand.success": "Eto pipaṣẹ: %s", "advMode.trackOutput": "Ṣiṣejade orin", "advMode.triggering": "Nfa", "advMode.type": "<PERSON><PERSON>", "advancement.advancementNotFound": "Ilọsiwaju aimọ: %s", "advancements.adventure.adventuring_time.description": "Ṣawari gbogbo biome", "advancements.adventure.adventuring_time.title": "Ìrìn Time", "advancements.adventure.arbalistic.description": "Pa awọn agbajo alailẹgbẹ marun pẹlu ibọn agbelebu kan", "advancements.adventure.arbalistic.title": "Arbalistic", "advancements.adventure.avoid_vibration.description": "Sunmọ Sculk Sensor tabi Warden lati ṣe idiwọ rẹ lati ṣawari rẹ", "advancements.adventure.avoid_vibration.title": "Ajiwo 100", "advancements.adventure.blowback.description": "Kill a Breeze with a deflected Breeze-shot <PERSON> Charge", "advancements.adventure.blowback.title": "Rongbachkim", "advancements.adventure.brush_armadillo.description": "Get Armadillo Scutes gikan sa Usa Ka Armadillo sa paggamit sa Usa Ka Brush", "advancements.adventure.brush_armadillo.title": "Isn't It Scute?", "advancements.adventure.bullseye.description": "Lu bullseye ti Àkọlé àkọsílẹ lati o kere ju awọn mita 30 sẹhin", "advancements.adventure.bullseye.title": "Bullseye", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Ṣe ikoko ti a ṣe ọṣọ lati inu 4 Sherds Pottery", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Imupadabọ iṣọra", "advancements.adventure.crafters_crafting_crafters.description": "Be near a Crafter when it crafts a Crafter", "advancements.adventure.crafters_crafting_crafters.title": "Crafters Crafting Crafters", "advancements.adventure.fall_from_world_height.description": "Isubu ọfẹ lati oke agbaye (kọ opin) si isalẹ agbaye ki o ye", "advancements.adventure.fall_from_world_height.title": "ihò inú àpáta ati gẹ̀rẹ́gẹ̀rẹ́ òkè.", "advancements.adventure.heart_transplanter.description": "Place a Creaking Heart with the correct alignment between two Pale Oak Log blocks", "advancements.adventure.heart_transplanter.title": "Heart Transplanter", "advancements.adventure.hero_of_the_village.description": "Successfully defend a village from a raid", "advancements.adventure.hero_of_the_village.title": "<PERSON><PERSON><PERSON> ti <PERSON>", "advancements.adventure.honey_block_slide.description": "Lọ sinu Afara oyin kan lati fọ isubu rẹ", "advancements.adventure.honey_block_slide.title": "<PERSON><PERSON>", "advancements.adventure.kill_a_mob.description": "<PERSON> <PERSON><PERSON><PERSON><PERSON> ad<PERSON><PERSON> ad<PERSON><PERSON>", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_all_mobs.description": "<PERSON> <PERSON><PERSON> ninu gbogbo ad<PERSON><PERSON><PERSON> alatako", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON><PERSON> ohun ibanilẹru ọdẹ", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Pa agbajo eniyan kan nitosi ayase Sculk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "It Spreads", "advancements.adventure.lighten_up.description": "Scrape a Copper Bulb with an Axe to make it brighter", "advancements.adventure.lighten_up.title": "Lighten Up", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "<PERSON><PERSON>ob<PERSON> kan lati ipaya ti ko fẹ lai bẹrẹ ina", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Olugbeja gbaradi", "advancements.adventure.minecraft_trials_edition.description": "Step foot in a Trial Chamber", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Trial(s) Edition", "advancements.adventure.ol_betsy.description": "<PERSON><PERSON><PERSON><PERSON> a crossbow", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON>' <PERSON>", "advancements.adventure.overoverkill.description": "Deal 50 hearts of damage in a single hit using the Mace", "advancements.adventure.overoverkill.title": "Over-Overkill", "advancements.adventure.play_jukebox_in_meadows.description": "Jẹ ki awọn Awọn igbo wa laaye pẹlu ohun orin lati inu apoti juke kan", "advancements.adventure.play_jukebox_in_meadows.title": "<PERSON><PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Ka ifihan agbara ti Chiseled Bookshelf nipa lilo <PERSON>", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.revaulting.description": "Unlock an Ominous Vault with an Ominous Trial Key", "advancements.adventure.revaulting.title": "Revaulting", "advancements.adventure.root.description": "Ìrìn, iwa<PERSON>ri ati ija", "advancements.adventure.root.title": "Ìrírí", "advancements.adventure.salvage_sherd.description": "Fẹlẹ bulọọki ifura kan lati gba Sherd Pottery", "advancements.adventure.salvage_sherd.title": "Bọwọ Awọn iyokù", "advancements.adventure.shoot_arrow.description": "Iyaworan nkankan pẹlu ọfà", "advancements.adventure.shoot_arrow.title": "Gba Ero", "advancements.adventure.sleep_in_bed.description": "Sùn ni ibusun kan lati yi aaye atunṣe rẹ pada", "advancements.adventure.sleep_in_bed.title": "Ala ti o dara", "advancements.adventure.sniper_duel.description": "Pa Egungun kan lati o kere ju awọn mita 50 lọ", "advancements.adventure.sniper_duel.title": "Sniper Mubahila", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON> Ender Dragon nipasẹ spyglass kan", "advancements.adventure.spyglass_at_dragon.title": "Ṣe O Ofurufu?", "advancements.adventure.spyglass_at_ghast.description": "Wo kan <PERSON> nipasẹ spyglass kan", "advancements.adventure.spyglass_at_ghast.title": "Ṣe O jẹ Balloon kan?", "advancements.adventure.spyglass_at_parrot.description": "Wo agbada kan nipasẹ spyglass kan", "advancements.adventure.spyglass_at_parrot.title": "Ṣé ẹyẹ ni?", "advancements.adventure.summon_iron_golem.description": "Pe Iron Golem lati ṣe iranlọwọ lati daabobo abule kan", "advancements.adventure.summon_iron_golem.title": "Iranlọwọ Alagbaṣe", "advancements.adventure.throw_trident.description": "<PERSON>ab<PERSON> nkan ti o ni. <PERSON>: fifọ ohun ija rẹ nikan kii ṣe imọran to dara.", "advancements.adventure.throw_trident.title": "Gẹgẹbi okuta ni omi", "advancements.adventure.totem_of_undying.description": "Lo Totem of Undying lati ṣe iyanjẹ iku", "advancements.adventure.totem_of_undying.title": "<PERSON><PERSON>", "advancements.adventure.trade.description": "Iṣowo ni ifijišẹ pẹlu abule kan", "advancements.adventure.trade.title": "Kini <PERSON>o kan!", "advancements.adventure.trade_at_world_height.description": "Ṣe iṣowo pẹlu Abúlé kan ni opin iga giga", "advancements.adventure.trade_at_world_height.title": "Onisowo Star", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Lo awọn awoṣe smithing wọnyi o kere ju lẹẹkan: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Silence, Vex, Tide, Wayfinder", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Smithing Ọna rẹ", "advancements.adventure.trim_with_any_armor_pattern.description": "Ṣiṣẹ ihamọra gige kan ni tabili Smithing kan", "advancements.adventure.trim_with_any_armor_pattern.title": "Ṣiṣẹda <PERSON><PERSON><PERSON>", "advancements.adventure.two_birds_one_arrow.description": "Pa Phantoms meji pẹlu itọka lilu", "advancements.adventure.two_birds_one_arrow.title": "Ẹyẹ <PERSON><PERSON>, Ọfa Kan", "advancements.adventure.under_lock_and_key.description": "Unlock a Vault with a Trial Key", "advancements.adventure.under_lock_and_key.title": "Under Lock and Key", "advancements.adventure.use_lodestone.description": "Use a Compass on a Lodestone", "advancements.adventure.use_lodestone.title": "Country Lode, Take Me Home", "advancements.adventure.very_very_frightening.description": "Mu thunderbolt pẹlu monomono", "advancements.adventure.very_very_frightening.title": "Gan gan idẹruba", "advancements.adventure.voluntary_exile.description": "Pa a igbogun ti olori.\nBoya ronu jijinna si awọn abule fun akoko naa...", "advancements.adventure.voluntary_exile.title": "Ìgbèkùn <PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Rin lori egbon lulú... laisi rirọ ninu rẹ", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Imọlẹ <PERSON>i <PERSON>", "advancements.adventure.who_needs_rockets.description": "Use a Wind Charge to launch yourself upward 8 blocks", "advancements.adventure.who_needs_rockets.title": "Who Needs Rockets?", "advancements.adventure.whos_the_pillager_now.description": "Fun Pillager ni itọwo ti oogun tiwọn", "advancements.adventure.whos_the_pillager_now.title": "Tani Pillager Bayi?", "advancements.empty": "Ko dabi pe ohunkohun wa nibi...", "advancements.end.dragon_breath.description": "Gba ẹmi dragoni ni igo gilasi kan", "advancements.end.dragon_breath.title": "O nilo Mint kan", "advancements.end.dragon_egg.description": "Mu Ẹyin Dragon naa", "advancements.end.dragon_egg.title": "Iran Itele", "advancements.end.elytra.description": "Wa Elytra", "advancements.end.elytra.title": "<PERSON>w<PERSON>n opin Ọrun", "advancements.end.enter_end_gateway.description": "Sa erekusu", "advancements.end.enter_end_gateway.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.end.find_end_city.description": "<PERSON><PERSON> sinu, kini o le ṣẹlẹ?", "advancements.end.find_end_city.title": "Ilu ni Ipari <PERSON>re naa", "advancements.end.kill_dragon.description": "<PERSON><PERSON> da<PERSON>", "advancements.end.kill_dragon.title": "Fi opin si Ọfẹ", "advancements.end.levitate.description": "Levitẹ soke awọn bulọọki 50 lati awọn ikọlu ti <PERSON> kan", "advancements.end.levitate.title": "Wiwo Nla Lati oke Ni<PERSON>", "advancements.end.respawn_dragon.description": "Respawn dragoni ender", "advancements.end.respawn_dragon.title": "Opin... Lẹẹkansi...", "advancements.end.root.description": "Tabi ibẹrẹ?", "advancements.end.root.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Jẹ ki Allay ju akara oyinbo kan silẹ ni Àkọsílẹ Akọsilẹ kan", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON> ojo ibi", "advancements.husbandry.allay_deliver_item_to_player.description": "Ṣe Allay fi awọn nkan ranṣẹ si ọ", "advancements.husbandry.allay_deliver_item_to_player.title": "O <PERSON> Kan <PERSON>", "advancements.husbandry.axolotl_in_a_bucket.description": "Mu yẹ axolotl kan ninu garawa kan", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON><PERSON>un ti o dara julọ", "advancements.husbandry.balanced_diet.description": "Je ohun gbogbo ti o jẹ e jẹ, paapaa ti ko ba dara fun ọ", "advancements.husbandry.balanced_diet.title": "Ounjẹ <PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.breed_all_animals.description": "Ṣe ajọbi gbogbo awọn ẹranko!", "advancements.husbandry.breed_all_animals.title": "<PERSON><PERSON> nipasẹ Meji", "advancements.husbandry.breed_an_animal.description": "Ṣe ajọbi awọn ẹranko meji papọ", "advancements.husbandry.breed_an_animal.title": "Awọn Parro<PERSON> ati awọn adan", "advancements.husbandry.complete_catalogue.description": "Tame gbogbo awọn iyatọ ologbo!", "advancements.husbandry.complete_catalogue.title": "A pipe Catalog", "advancements.husbandry.feed_snifflet.description": "<PERSON><PERSON><PERSON> a <PERSON>", "advancements.husbandry.feed_snifflet.title": "Kekere sniffs", "advancements.husbandry.fishy_business.description": "<PERSON> ẹja kan", "advancements.husbandry.fishy_business.title": "Iṣowo Ẹja", "advancements.husbandry.froglights.description": "Ni gbogbo Froglights ninu akojo oja rẹ", "advancements.husbandry.froglights.title": "Pẹlu Awọn <PERSON>!", "advancements.husbandry.kill_axolotl_target.description": "Ṣe ẹgbẹ pẹlu Axolotl ki o ṣẹgun ija kan", "advancements.husbandry.kill_axolotl_target.title": "<PERSON><PERSON><PERSON> ti Ore!", "advancements.husbandry.leash_all_frog_variants.description": "Gba iyatọ Ọpọlọ kọọkan lori <PERSON> kan", "advancements.husbandry.leash_all_frog_variants.title": "When the Squad Hops into Town", "advancements.husbandry.make_a_sign_glow.description": "delas yod khale chosl", "advancements.husbandry.make_a_sign_glow.title": "Tàn k<PERSON>ès<PERSON>!", "advancements.husbandry.netherite_hoe.description": "Lo ingot Netherite lati ṣe igbesoke hoe kan, ati lẹhinna tun ṣe atunyẹwo awọn yiyan igbesi aye rẹ", "advancements.husbandry.netherite_hoe.title": "Ìyàsímímọ́ <PERSON><PERSON>", "advancements.husbandry.obtain_sniffer_egg.description": "Gba Ẹyin Sniffer", "advancements.husbandry.obtain_sniffer_egg.title": "<PERSON><PERSON><PERSON> a<PERSON>", "advancements.husbandry.place_dried_ghast_in_water.description": "Place a Dried Ghast block into water", "advancements.husbandry.place_dried_ghast_in_water.title": "Stay Hydrated!", "advancements.husbandry.plant_any_sniffer_seed.description": "<PERSON>bin irugbin <PERSON>", "advancements.husbandry.plant_any_sniffer_seed.title": "Gbingbin awọn ti o ti kọja", "advancements.husbandry.plant_seed.description": "Gbin irugbin kan ki o wo bi o ti n dagba", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON> kan", "advancements.husbandry.remove_wolf_armor.description": "Remove Wolf Armor from a Wolf using Shears", "advancements.husbandry.remove_wolf_armor.title": "Shear Brilliance", "advancements.husbandry.repair_wolf_armor.description": "Fully repair damaged Wolf Armor using Armadillo Scutes", "advancements.husbandry.repair_wolf_armor.title": "Good as New", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Gba sinu ọkọ oju omi kan ki o leefofo pẹlu Ewúrẹ", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON><PERSON><PERSON><PERSON> ti Atsfofo Ewurẹ Rẹ!", "advancements.husbandry.root.description": "Aye kun fun awọn ọrẹ ati ounjẹ", "advancements.husbandry.root.title": "Ọkọ", "advancements.husbandry.safely_harvest_honey.description": "Lo ina ibudó lati gba Oyin lati inu Ile-oyin kan nipa lilo Igo <PERSON> kan lai mu awọn oyin naa buru si", "advancements.husbandry.safely_harvest_honey.title": "Bee Alejo Wa", "advancements.husbandry.silk_touch_nest.description": "Gbe itẹ-ẹiyẹ <PERSON> kan, pẹlu awọn oyin mẹta ni inu, lilo ifọw<PERSON>kan siliki", "advancements.husbandry.silk_touch_nest.title": "Lapapọ Beelocation", "advancements.husbandry.tactical_fishing.description": "Mu ẹja kan ... laisi opa ipeja!", "advancements.husbandry.tactical_fishing.title": "<PERSON><PERSON>", "advancements.husbandry.tadpole_in_a_bucket.description": "Mu tadpole ninu garawa kan", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukkit Bukkit", "advancements.husbandry.tame_an_animal.description": "Tọ ẹranko kan", "advancements.husbandry.tame_an_animal.title": "Ore lailai", "advancements.husbandry.wax_off.description": "Fọ Epo-eti kuro ti Ejò kan!", "advancements.husbandry.wax_off.title": "Epo-eti Pa", "advancements.husbandry.wax_on.description": "Lo Honeycomb si apo idẹ!", "advancements.husbandry.wax_on.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.whole_pack.description": "Tame one of each Wolf variant", "advancements.husbandry.whole_pack.title": "The Whole Pack", "advancements.nether.all_effects.description": "Ṣe gbogbo ipa lo ni akoko kanna", "advancements.nether.all_effects.title": "Bawo ni A Ṣe Wa Nibi?", "advancements.nether.all_potions.description": "Jẹ ki gbogbo ipa ipa ni a lo ni akoko kanna", "advancements.nether.all_potions.title": "<PERSON><PERSON><PERSON> ibinu kan", "advancements.nether.brew_potion.description": "<PERSON><PERSON><PERSON> i<PERSON>ko kan", "advancements.nether.brew_potion.title": "Agbegbe Brewery", "advancements.nether.charge_respawn_anchor.description": "Gba agbara Oran Respawn si o pọju", "advancements.nether.charge_respawn_anchor.title": "<PERSON><PERSON> \"Mẹsan\"", "advancements.nether.create_beacon.description": "<PERSON><PERSON> ati gbe bekini kan", "advancements.nether.create_beacon.title": "<PERSON> wa si Ile", "advancements.nether.create_full_beacon.description": "<PERSON> si agbara ni kikun", "advancements.nether.create_full_beacon.title": "<PERSON><PERSON><PERSON>", "advancements.nether.distract_piglin.description": "<PERSON>n aw<PERSON><PERSON> pẹlu wura", "advancements.nether.distract_piglin.title": "Oh Shiny", "advancements.nether.explore_nether.description": "Ṣawari gbogbo awọn biomes ti Nether", "advancements.nether.explore_nether.title": "<PERSON><PERSON><PERSON>lo", "advancements.nether.fast_travel.description": "Lo Nether lati rin irin -ajo kilomita 7 ni Agbaye", "advancements.nether.fast_travel.title": "Subspace Bubble", "advancements.nether.find_bastion.description": "Tẹ Ajẹkù Bastion kan sii", "advancements.nether.find_bastion.title": "Awọn wọnyi ni Awọn Ọjọ", "advancements.nether.find_fortress.description": "Fọ ọna rẹ sinu odi ilu Nether", "advancements.nether.find_fortress.title": "Il<PERSON> -odi ti Ẹru", "advancements.nether.get_wither_skull.description": "<PERSON><PERSON> timole <PERSON>", "advancements.nether.get_wither_skull.title": "Spooky idẹruba Egungun", "advancements.nether.loot_bastion.description": "<PERSON><PERSON>gun okan ni o ku isanku", "advancements.nether.loot_bastion.title": "Awọn ẹlẹdẹ Ogun", "advancements.nether.netherite_armor.description": "Gba aṣọ kikun ti ihamọra Netherite", "advancements.nether.netherite_armor.title": "Bo <PERSON> ni I<PERSON>ti", "advancements.nether.obtain_ancient_debris.description": "Gba Idoti <PERSON>", "advancements.nether.obtain_ancient_debris.title": "<PERSON>ọ ninu <PERSON>n ijinlẹ", "advancements.nether.obtain_blaze_rod.description": "Ṣe igbona ina ti ọpa rẹ", "advancements.nether.obtain_blaze_rod.title": "<PERSON><PERSON>", "advancements.nether.obtain_crying_obsidian.description": "Gba Obsidian Ẹkún", "advancements.nether.obtain_crying_obsidian.title": "Tani O n ge Alubosa?", "advancements.nether.return_to_sender.description": "<PERSON> G<PERSON>t pẹlu b<PERSON><PERSON>lu ina kan", "advancements.nether.return_to_sender.title": "Pada si Oluranṣẹ", "advancements.nether.ride_strider.description": "<PERSON><PERSON>n Strider kan pẹlu Fungus ti o jin lori Stick kan", "advancements.nether.ride_strider.title": "Ọkọ̀ yìí ní ẹsẹ̀", "advancements.nether.ride_strider_in_overworld_lava.description": "Mu Strider kan fun gigun gigun lori adagun lava ni Overworld", "advancements.nether.ride_strider_in_overworld_lava.title": "O kan lara bi ile", "advancements.nether.root.description": "Mu awọn aṣọ igba ooru wa", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON>", "advancements.nether.summon_wither.title": "Aw<PERSON>n giga <PERSON>", "advancements.nether.uneasy_alliance.description": "<PERSON><PERSON><PERSON> kan la<PERSON>, mu wa lailewu si ile si <PERSON>... lẹhinna pa a", "advancements.nether.uneasy_alliance.title": "Uneasy Alliance", "advancements.nether.use_lodestone.description": "<PERSON> lori <PERSON> kan", "advancements.nether.use_lodestone.title": "Orilẹ-<PERSON><PERSON>, <PERSON><PERSON>", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Ṣe irẹwẹsi ati lẹhinna ṣe iwosan Zombie Villager kan", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "Deflect a project pẹlu asà kan", "advancements.story.deflect_arrow.title": "<PERSON><PERSON>, <PERSON> <PERSON><PERSON><PERSON>", "advancements.story.enchant_item.description": "Enchant ohun kan ni tabili Enchanting", "advancements.story.enchant_item.title": "Enchanter", "advancements.story.enter_the_end.description": "Tẹ Portal Ipari", "advancements.story.enter_the_end.title": "<PERSON><PERSON><PERSON>?", "advancements.story.enter_the_nether.description": "Kọ, ina ati tẹ Portal Nether", "advancements.story.enter_the_nether.title": "A nilo lati lọ jinle", "advancements.story.follow_ender_eye.description": "Tẹle <PERSON>ju ti Ender", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON>", "advancements.story.form_obsidian.description": "Gba bulọọki ti ifẹ", "advancements.story.form_obsidian.title": "Ice garawa Ipenija", "advancements.story.iron_tools.description": "Ṣe igbesoke Pickaxe rẹ", "advancements.story.iron_tools.title": "Ṣe kii ṣe Irin <PERSON>rin", "advancements.story.lava_bucket.description": "Fọwọsi garawa pẹlu lava", "advancements.story.lava_bucket.title": "Nkan to gbono", "advancements.story.mine_diamond.description": "Gba awọn okuta i<PERSON>ye", "advancements.story.mine_diamond.title": "Awọn okuta iye<PERSON>ye!", "advancements.story.mine_stone.description": "Okuta mi pẹlu pickaxe tuntun rẹ", "advancements.story.mine_stone.title": "Ọjọ ori <PERSON>uta", "advancements.story.obtain_armor.description": "Daabobo ararẹ pẹlu nkan ti ihamọra irin", "advancements.story.obtain_armor.title": "<PERSON><PERSON><PERSON> Up", "advancements.story.root.description": "Okan ati itan ti ere naa", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Ihamọra Diamond fi awọn ẹmi pamọ", "advancements.story.shiny_gear.title": "Cover Me with Diamonds", "advancements.story.smelt_iron.description": "<PERSON>ọ irin ingot kan", "advancements.story.smelt_iron.title": "Gba Hardware", "advancements.story.upgrade_tools.description": "<PERSON><PERSON> ikoko ti o dara julọ", "advancements.story.upgrade_tools.title": "Ngba Igbesoke", "advancements.toast.challenge": "<PERSON>pen<PERSON>!", "advancements.toast.goal": "Góńgó Àṣeyọri!", "advancements.toast.task": "Ilosiwaju Ti Ṣe!", "argument.anchor.invalid": "<PERSON><PERSON> oran ohun ti ko wulo %s", "argument.angle.incomplete": "Ti ko pe (igun 1 ti a reti)", "argument.angle.invalid": "<PERSON>gun ti ko fẹsẹmulẹ", "argument.block.id.invalid": "<PERSON><PERSON> aimọ '%s'", "argument.block.property.duplicate": "<PERSON>un -ini ' %s' le ṣee ṣeto lẹẹkan fun bulọki %s", "argument.block.property.invalid": "Dina %s ko gba ' %s' fun ohun -ini %s", "argument.block.property.novalue": "Iye ti a nireti fun ohun -ini ' %s' lori bulọki %s", "argument.block.property.unclosed": "Ipade ti o nireti] fun awọn ohun -ini ipinlẹ dina", "argument.block.property.unknown": "Dina %s ko ni ohun -ini ' %s'", "argument.block.tag.disallowed": "Awọn aami ko gba laaye ni<PERSON>, awọn bul<PERSON><PERSON><PERSON> gangan nikan", "argument.color.invalid": "Àìmọ̀ àwọ̀: '%s'", "argument.component.invalid": "Paati iwiregbe ti ko wulo: %s", "argument.criteria.invalid": "Ami ti a ko mọ '%s'", "argument.dimension.invalid": "Iwọn aimọ '%s'", "argument.double.big": "Meji ko gbọdọ ju %s lọ, ti a rii %s", "argument.double.low": "Meji ko gbọdọ dinku ju %s, ti a rii %s", "argument.entity.invalid": "Orukọ ti ko tọna tabi UUID", "argument.entity.notfound.entity": "Ko ri nkankan", "argument.entity.notfound.player": "Ko si ẹrọ orin kankan ti a rii", "argument.entity.options.advancements.description": "Awọn oṣere pẹlu awọn ilọsiwaju", "argument.entity.options.distance.description": "<PERSON><PERSON><PERSON> si nkan", "argument.entity.options.distance.negative": "<PERSON><PERSON><PERSON> ko le jẹ odi", "argument.entity.options.dx.description": "Aw<PERSON>n nkan laarin x ati x + dx", "argument.entity.options.dy.description": "Awọn nkan laarin y ati y + dy", "argument.entity.options.dz.description": "Awọn nkan laarin z ati z + dz", "argument.entity.options.gamemode.description": "Awọn ẹrọ orin pẹlu game mode", "argument.entity.options.inapplicable": "Aṣayan '%s' ko wulo nibi", "argument.entity.options.level.description": "<PERSON><PERSON><PERSON>", "argument.entity.options.level.negative": "<PERSON><PERSON>e ko yẹ ki o jẹ odi", "argument.entity.options.limit.description": "Nọmba ti o pọju ti awọn nkan lati pada", "argument.entity.options.limit.toosmall": "Iwọn gbọdọ jẹ o kere ju 1", "argument.entity.options.mode.invalid": "Ko tọ tabi ipo aimọ ere '%s'", "argument.entity.options.name.description": "Orukọ nkan", "argument.entity.options.nbt.description": "Awọn nkan pẹlu NBT", "argument.entity.options.predicate.description": "Asọtẹlẹ aṣa", "argument.entity.options.scores.description": "Awọn nkan pẹlu awọn ikun", "argument.entity.options.sort.description": "Too awọn nkan", "argument.entity.options.sort.irreversible": "<PERSON>r<PERSON> àìfẹsẹ̀múlẹ̀ tàbí àìmọ̀ irú '%s'", "argument.entity.options.tag.description": "A<PERSON><PERSON>n nkan pẹlu aami", "argument.entity.options.team.description": "Aw<PERSON>n nkan lori ẹgbẹ", "argument.entity.options.type.description": "<PERSON><PERSON><PERSON>n nkan ti iru", "argument.entity.options.type.invalid": "Invalid or unknown entity type '%s'", "argument.entity.options.unknown": "<PERSON><PERSON><PERSON><PERSON> aimọ '%s'", "argument.entity.options.unterminated": "O ti ṣe yẹ opin awọn aṣayan", "argument.entity.options.valueless": "Iye ti a nireti fun aṣayan '%s'", "argument.entity.options.x.description": "x ipo", "argument.entity.options.x_rotation.description": "Yiyi ti x nkankan", "argument.entity.options.y.description": "y ipo", "argument.entity.options.y_rotation.description": "Yiyi ti ẹya ti yiya", "argument.entity.options.z.description": "z ipo", "argument.entity.selector.allEntities": "Gbogbo awọn nkan", "argument.entity.selector.allPlayers": "Gbogbo awọn o<PERSON>", "argument.entity.selector.missing": "<PERSON><PERSON><PERSON>n yiyan ti o padanu", "argument.entity.selector.nearestEntity": "Nearest entity", "argument.entity.selector.nearestPlayer": "Ẹrọ orin to sunmọ", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON><PERSON> ko gba laaye", "argument.entity.selector.randomPlayer": "Id ẹrọ orin", "argument.entity.selector.self": "Nkan ti isiyi", "argument.entity.selector.unknown": "<PERSON><PERSON><PERSON><PERSON> aimọ iru '%s'", "argument.entity.toomany": "Ẹyọkan kan ṣoṣo ni a gba laaye, ṣug<PERSON><PERSON>n yiyan ti a pese ti gba laaye diẹ sii ju ọkan lọ", "argument.enum.invalid": "<PERSON><PERSON> a<PERSON><PERSON> \"%s\"", "argument.float.big": "Leefofo ko gbọdọ ju %s lọ, ti a rii %s", "argument.float.low": "Leefofo ko gbọdọ kere ju %s, ti a rii %s", "argument.gamemode.invalid": "<PERSON><PERSON> ere aimọ: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "ID ti ko wulo", "argument.id.unknown": "ID aimọ: %s", "argument.integer.big": "<PERSON><PERSON>yipada ko gbọdọ ju %s lọ, ti a rii %s", "argument.integer.low": "<PERSON>lujọpọ ko gbọdọ kere ju %s, ti a rii %s", "argument.item.id.invalid": "Nkan ti a ko mọ '%s'", "argument.item.tag.disallowed": "Awọn afi ko gba laaye ni<PERSON>, awọn ohun gangan nikan", "argument.literal.incorrect": "Ti ṣe yẹ gegebi %s", "argument.long.big": "Gigun ko gbọdọ ju %s lọ, ti a rii %s", "argument.long.low": "Gigun ko gbọdọ kere ju %s, ti a rii %s", "argument.message.too_long": "Chat message was too long (%s > maximum %s characters)", "argument.nbt.array.invalid": "Irú ìdìpọ̀ àìfẹsẹ̀múlẹ̀ '%s'", "argument.nbt.array.mixed": "Ko le fi %s sinu %s", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "<PERSON><PERSON><PERSON>i ti a nireti", "argument.nbt.expected.value": "O ti ṣe yẹ iye", "argument.nbt.list.mixed": "Ko le fi %s sinu atokọ %s", "argument.nbt.trailing": "Awọn data ipasẹ airotẹlẹ", "argument.player.entities": "Awọn oṣere nikan le ni ipa nipasẹ aṣẹ yii, ṣugb<PERSON>n yiyan ti a pese pẹlu awọn nkan", "argument.player.toomany": "Ẹrọ orin kan ṣoṣo ni a gba laaye, ṣug<PERSON><PERSON>n yiyan ti a pese ti gba laaye diẹ sii ju ọkan lọ", "argument.player.unknown": "Ẹrọ orin yẹn ko si", "argument.pos.missing.double": "O ti ṣe yẹ ipoidojuko kan", "argument.pos.missing.int": "O ti ṣe yẹ a Àkọsílẹ ipo", "argument.pos.mixed": "Ko le dapọ agbaye & awọn ipoido<PERSON> agbe<PERSON> (ohun gbogbo gbọdọ boya lo ^ tabi rara)", "argument.pos.outofbounds": "<PERSON><PERSON> yẹn wa ni ita awọn ifilelẹ ti a gba laaye.", "argument.pos.outofworld": "<PERSON><PERSON> yẹn ti jade ninu agbaye yii!", "argument.pos.unloaded": "<PERSON><PERSON> yẹn ko ni fifuye", "argument.pos2d.incomplete": "Ti ko pe (awọn ipoidojuko 2 ti a reti)", "argument.pos3d.incomplete": "Ti ko pe (awọn ipoidojuko 3 ti a reti)", "argument.range.empty": "O ti ṣe yẹ iye tabi ibiti awọn iye", "argument.range.ints": "Awọn nọmba gbogbo nikan ni o gba laaye, kii ṣe awọn eleemewa", "argument.range.swapped": "<PERSON> ko le tobi ju max lọ", "argument.resource.invalid_type": "Ano '%s' ni iru aṣiṣe '%s' (a reti '%s')", "argument.resource.not_found": "Ko le ri eroja '%s' ti iru '%s'", "argument.resource_or_id.failed_to_parse": "Failed to parse structure: %s", "argument.resource_or_id.invalid": "Invalid id or tag", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "Tag '%s' ni iru aṣiṣe '%s' (a reti '%s')", "argument.resource_tag.not_found": "Ko le ri tag '%s' ti iru '%s'", "argument.rotation.incomplete": "Ti ko pe (awọn ipoidojuko 2 ti a reti)", "argument.scoreHolder.empty": "Ko si awọn dimu Dimegilio ti o yẹ ti a le rii", "argument.scoreboardDisplaySlot.invalid": "Iho aimọ aimọ '%s'", "argument.style.invalid": "Invalid style: %s", "argument.time.invalid_tick_count": "Iye ami si gbọdọ jẹ ti kii-odi", "argument.time.invalid_unit": "Ìsopọ̀ tí kò fẹsẹ̀múlẹ̀", "argument.time.tick_count_too_low": "Iwọn ami ko gbọdọ kere ju %s, ti a rii %s", "argument.uuid.invalid": "UUID ti ko wulo", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "Àkọọ́lẹ̀ ìdè àìmọ̀ '%s'", "arguments.function.tag.unknown": "Aami tag iṣẹ aimọ '%s'", "arguments.function.unknown": "Iṣẹ aimọ %s", "arguments.item.component.expected": "Expected item component", "arguments.item.component.malformed": "Malformed '%s' component: '%s'", "arguments.item.component.repeated": "Item component '%s' was repeated, but only one value can be specified", "arguments.item.component.unknown": "Unknown item component '%s'", "arguments.item.malformed": "Malformed item: '%s'", "arguments.item.overstacked": "%s le ṣe akopọ nikan si %s", "arguments.item.predicate.malformed": "Malformed '%s' predicate: '%s'", "arguments.item.predicate.unknown": "Unknown item predicate '%s'", "arguments.item.tag.unknown": "<PERSON><PERSON> tag ohun kan ti a ko mọ '%s'", "arguments.nbtpath.node.invalid": "NBT ipa ọna ti ko tọna", "arguments.nbtpath.nothing_found": "Ko ri awọn eroja ti o baamu %s", "arguments.nbtpath.too_deep": "Abajade NBT ju jinna iteeye", "arguments.nbtpath.too_large": "Abajade NBT tobi ju", "arguments.objective.notFound": "<PERSON><PERSON> -afẹde aimọ '%s'", "arguments.objective.readonly": "Erongba ibi-afẹde '%s' jẹ kika-nikan", "arguments.operation.div0": "Ko le pin nipasẹ odo", "arguments.operation.invalid": "Isẹ ti ko wulo", "arguments.swizzle.invalid": "Swizzle ti ko wulo, apapọ ti a reti ti 'x', 'y' ati 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "Ìhámọ́ra", "attribute.name.armor_toughness": "<PERSON><PERSON><PERSON>", "attribute.name.attack_damage": "Attack Damage", "attribute.name.attack_knockback": "Attack Knockback", "attribute.name.attack_speed": "Attack Speed", "attribute.name.block_break_speed": "Block Break Speed", "attribute.name.block_interaction_range": "Block Interaction Range", "attribute.name.burning_time": "Burning Time", "attribute.name.camera_distance": "Camera Distance", "attribute.name.entity_interaction_range": "Entity Interaction Range", "attribute.name.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.flying_speed": "Flying Speed", "attribute.name.follow_range": "<PERSON>b <PERSON> Range", "attribute.name.generic.armor": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.armor_toughness": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_damage": "Ipalara Ikọlu", "attribute.name.generic.attack_knockback": "K<PERSON><PERSON> kolu", "attribute.name.generic.attack_speed": "Iyara Attack", "attribute.name.generic.block_interaction_range": "Block Interaction Range", "attribute.name.generic.burning_time": "Burning Time", "attribute.name.generic.entity_interaction_range": "Entity Interaction Range", "attribute.name.generic.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.generic.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.generic.flying_speed": "<PERSON><PERSON><PERSON>", "attribute.name.generic.follow_range": "Agbajo eniyan Tẹle Range", "attribute.name.generic.gravity": "Walẹ", "attribute.name.generic.jump_strength": "Jump Strength", "attribute.name.generic.knockback_resistance": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.luck": "Orire", "attribute.name.generic.max_absorption": "Max Absorption", "attribute.name.generic.max_health": "Ilera Max", "attribute.name.generic.movement_efficiency": "Movement Efficiency", "attribute.name.generic.movement_speed": "<PERSON><PERSON><PERSON>", "attribute.name.generic.oxygen_bonus": "Oxygen Bonus", "attribute.name.generic.safe_fall_distance": "Safe Fall Distance", "attribute.name.generic.scale": "<PERSON><PERSON><PERSON>", "attribute.name.generic.step_height": "Step Height", "attribute.name.generic.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.gravity": "Walẹ", "attribute.name.horse.jump_strength": "Agbara fifo Ẹṣin", "attribute.name.jump_strength": "Jump Strength", "attribute.name.knockback_resistance": "Knockback Resistance", "attribute.name.luck": "Orire", "attribute.name.max_absorption": "Max Absorption", "attribute.name.max_health": "Max Health", "attribute.name.mining_efficiency": "Mining Efficiency", "attribute.name.movement_efficiency": "Movement Efficiency", "attribute.name.movement_speed": "<PERSON><PERSON><PERSON>", "attribute.name.oxygen_bonus": "Oxygen Bonus", "attribute.name.player.block_break_speed": "Block Break Speed", "attribute.name.player.block_interaction_range": "Block Interaction Range", "attribute.name.player.entity_interaction_range": "Entity Interaction Range", "attribute.name.player.mining_efficiency": "Mining Efficiency", "attribute.name.player.sneaking_speed": "Sneaking Speed", "attribute.name.player.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.player.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.safe_fall_distance": "Safe Fall Distance", "attribute.name.scale": "<PERSON><PERSON><PERSON>", "attribute.name.sneaking_speed": "Sneaking Speed", "attribute.name.spawn_reinforcements": "Zombie Reinforcements", "attribute.name.step_height": "Step Height", "attribute.name.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.tempt_range": "Mob Tempt Range", "attribute.name.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "<PERSON><PERSON><PERSON><PERSON> atun<PERSON>e <PERSON>", "biome.minecraft.badlands": "Badlandsawọn agbegbe buburu", "biome.minecraft.bamboo_jungle": "Oparun Igbo", "biome.minecraft.basalt_deltas": "Basalt Deltas", "biome.minecraft.beach": "<PERSON><PERSON> okun", "biome.minecraft.birch_forest": "Igbo birch", "biome.minecraft.cherry_grove": "<PERSON><PERSON>", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.crimson_forest": "Igbo Crimson", "biome.minecraft.dark_forest": "<PERSON><PERSON><PERSON> dudu", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON>", "biome.minecraft.deep_dark": "<PERSON><PERSON> dudu", "biome.minecraft.deep_frozen_ocean": "<PERSON> tutuni<PERSON>", "biome.minecraft.deep_lukewarm_ocean": "<PERSON><PERSON> g<PERSON> nla", "biome.minecraft.deep_ocean": "<PERSON><PERSON>", "biome.minecraft.desert": "Agin<PERSON>", "biome.minecraft.dripstone_caves": "Awọn iho Dripstone", "biome.minecraft.end_barrens": "Aw<PERSON>n agan ti o pari", "biome.minecraft.end_highlands": "<PERSON><PERSON> oke", "biome.minecraft.end_midlands": "Ipari Midlands", "biome.minecraft.eroded_badlands": "Awọn ilẹ buburu ti o bajẹ", "biome.minecraft.flower_forest": "Igbo ododo", "biome.minecraft.forest": "Igbo", "biome.minecraft.frozen_ocean": "<PERSON><PERSON><PERSON>", "biome.minecraft.frozen_peaks": "<PERSON><PERSON><PERSON>n oke tio tutunini", "biome.minecraft.frozen_river": "<PERSON><PERSON><PERSON> o<PERSON>", "biome.minecraft.grove": "Ṣuuru", "biome.minecraft.ice_spikes": "Awọn Spikes Ice", "biome.minecraft.jagged_peaks": "Jagged tente oke", "biome.minecraft.jungle": "Igbo", "biome.minecraft.lukewarm_ocean": "Òkun tó gbóná", "biome.minecraft.lush_caves": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.mangrove_swamp": "Mangrove Swamp", "biome.minecraft.meadow": "Meadow", "biome.minecraft.mushroom_fields": "<PERSON><PERSON><PERSON><PERSON> aaye <PERSON>", "biome.minecraft.nether_wastes": "<PERSON><PERSON>", "biome.minecraft.ocean": "Òkun", "biome.minecraft.old_growth_birch_forest": "Igbo Idagba Atijọ Igbo", "biome.minecraft.old_growth_pine_taiga": "Idagba Atijọ Pine Taiga", "biome.minecraft.old_growth_spruce_taiga": "Growth Old Spruce Taiga", "biome.minecraft.pale_garden": "Pale Garden", "biome.minecraft.plains": "Pẹtẹlẹ", "biome.minecraft.river": "<PERSON><PERSON>", "biome.minecraft.savanna": "Savanna", "biome.minecraft.savanna_plateau": "Pẹtẹlẹ savanna", "biome.minecraft.small_end_islands": "<PERSON><PERSON><PERSON><PERSON> erek<PERSON>", "biome.minecraft.snowy_beach": "Sno eti okun", "biome.minecraft.snowy_plains": "Awọn pẹtẹlẹ Sno", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.snowy_taiga": "Snow <PERSON>", "biome.minecraft.soul_sand_valley": "Ọkàn <PERSON><PERSON><PERSON>", "biome.minecraft.sparse_jungle": "Igbo I<PERSON>le", "biome.minecraft.stony_peaks": "<PERSON><PERSON><PERSON><PERSON> Stony", "biome.minecraft.stony_shore": "Stony Shore", "biome.minecraft.sunflower_plains": "Pẹtẹlẹ sunflower", "biome.minecraft.swamp": "Swamp", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "<PERSON><PERSON><PERSON>", "biome.minecraft.the_void": "Awọn I<PERSON>ọ", "biome.minecraft.warm_ocean": "<PERSON><PERSON>", "biome.minecraft.warped_forest": "Igbo Igbin", "biome.minecraft.windswept_forest": "Igbo Igbo", "biome.minecraft.windswept_gravelly_hills": "<PERSON><PERSON><PERSON><PERSON> oke -nla wẹwẹ ti afẹfẹ fẹ", "biome.minecraft.windswept_hills": "<PERSON><PERSON><PERSON><PERSON> oke -nla ti afẹfẹ fẹ", "biome.minecraft.windswept_savanna": "Efufu savanna", "biome.minecraft.wooded_badlands": "Igbo badlands", "block.minecraft.acacia_button": "Bọtini Acacia", "block.minecraft.acacia_door": "Acacia enu", "block.minecraft.acacia_fence": "Acacia odi", "block.minecraft.acacia_fence_gate": "Acacia Ẹnubodè Pada", "block.minecraft.acacia_hanging_sign": "Acacia ikele Sign", "block.minecraft.acacia_leaves": "Acacia bunkun", "block.minecraft.acacia_log": "Acacia <PERSON>", "block.minecraft.acacia_planks": "Apẹrẹ acacia", "block.minecraft.acacia_pressure_plate": "Acacia I<PERSON>ju awo", "block.minecraft.acacia_sapling": "Ororoo igbá", "block.minecraft.acacia_sign": "Akara awo", "block.minecraft.acacia_slab": "Akara awo", "block.minecraft.acacia_stairs": "Acacia atẹgun", "block.minecraft.acacia_trapdoor": "Acacia niye", "block.minecraft.acacia_wall_hanging_sign": "Acacia Wall ikele Sign", "block.minecraft.acacia_wall_sign": "Acacia Wall Sign", "block.minecraft.acacia_wood": "Acacia igi", "block.minecraft.activator_rail": "<PERSON><PERSON><PERSON>", "block.minecraft.air": "Afẹfẹ", "block.minecraft.allium": "<PERSON>a", "block.minecraft.amethyst_block": "Àkọsílẹ ti Amethyst", "block.minecraft.amethyst_cluster": "<PERSON><PERSON><PERSON><PERSON>eth<PERSON>", "block.minecraft.ancient_debris": "Ibajẹ atijọ", "block.minecraft.andesite": "Andesite", "block.minecraft.andesite_slab": "Andesite Slab", "block.minecraft.andesite_stairs": "Awọn atẹgun Andesite", "block.minecraft.andesite_wall": "Andesite Wall", "block.minecraft.anvil": "An<PERSON>", "block.minecraft.attached_melon_stem": "Sọ Melon Stem", "block.minecraft.attached_pumpkin_stem": "Soo elegede tita", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "<PERSON><PERSON><PERSON><PERSON> ewe Azalea", "block.minecraft.azure_bluet": "Azure Bluet", "block.minecraft.bamboo": "Oparun", "block.minecraft.bamboo_block": "Oparun ti block", "block.minecraft.bamboo_button": "Oparu<PERSON>", "block.minecraft.bamboo_door": "Oparun ilẹkun", "block.minecraft.bamboo_fence": "<PERSON><PERSON>", "block.minecraft.bamboo_fence_gate": "Oparun Ẹnubodè Pada", "block.minecraft.bamboo_hanging_sign": "Oparun ikele Sign", "block.minecraft.bamboo_mosaic": "Oparu<PERSON>", "block.minecraft.bamboo_mosaic_slab": "Oparun <PERSON>", "block.minecraft.bamboo_mosaic_stairs": "Oparun Moseiki Atẹgun", "block.minecraft.bamboo_planks": "Oparun Igi", "block.minecraft.bamboo_pressure_plate": "Oparun Ipa Plate", "block.minecraft.bamboo_sapling": "Oparun Titu", "block.minecraft.bamboo_sign": "Oparun ami", "block.minecraft.bamboo_slab": "Oparun Slab", "block.minecraft.bamboo_stairs": "Oparun Atẹgun", "block.minecraft.bamboo_trapdoor": "Oparun Trapdoor", "block.minecraft.bamboo_wall_hanging_sign": "Oparun Wall ikele Sign", "block.minecraft.bamboo_wall_sign": "Oparun Wall Ami", "block.minecraft.banner.base.black": "<PERSON> kikun <PERSON>", "block.minecraft.banner.base.blue": "<PERSON><PERSON><PERSON> ni kikun", "block.minecraft.banner.base.brown": "<PERSON> kikun <PERSON>", "block.minecraft.banner.base.cyan": "<PERSON><PERSON><PERSON> kikun", "block.minecraft.banner.base.gray": "Field Grẹy Ni kikun", "block.minecraft.banner.base.green": "<PERSON> kikun <PERSON> Field", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON><PERSON>a Ni kikun", "block.minecraft.banner.base.light_gray": "<PERSON><PERSON><PERSON> Grẹy Imọlẹ Ni kikun", "block.minecraft.banner.base.lime": "<PERSON> kikun orombo aaye", "block.minecraft.banner.base.magenta": "<PERSON> kikun Field Magenta", "block.minecraft.banner.base.orange": "<PERSON><PERSON><PERSON> kikun", "block.minecraft.banner.base.pink": "<PERSON> kikun <PERSON>", "block.minecraft.banner.base.purple": "<PERSON><PERSON><PERSON> kikun", "block.minecraft.banner.base.red": "<PERSON> kikun Red Field", "block.minecraft.banner.base.white": "Ni kikun <PERSON>fun Funfun", "block.minecraft.banner.base.yellow": "<PERSON><PERSON><PERSON> kikun", "block.minecraft.banner.border.black": "Black Bordure", "block.minecraft.banner.border.blue": "Orombo Aala", "block.minecraft.banner.border.brown": "Aala Grey", "block.minecraft.banner.border.cyan": "<PERSON><PERSON>", "block.minecraft.banner.border.gray": "Aala Grey", "block.minecraft.banner.border.green": "Aala Grey", "block.minecraft.banner.border.light_blue": "Light Blue Bordure", "block.minecraft.banner.border.light_gray": "Imọlẹ Grey Bordure", "block.minecraft.banner.border.lime": "Orombo Aala", "block.minecraft.banner.border.magenta": "<PERSON><PERSON>", "block.minecraft.banner.border.orange": "<PERSON><PERSON>", "block.minecraft.banner.border.pink": "Aala Pink", "block.minecraft.banner.border.purple": "Aala eleyi ti", "block.minecraft.banner.border.red": "Red Bordure", "block.minecraft.banner.border.white": "Aala Funfun", "block.minecraft.banner.border.yellow": "Yellow Bordure", "block.minecraft.banner.bricks.black": "Black Field Masoned", "block.minecraft.banner.bricks.blue": "Blue Field Masoned", "block.minecraft.banner.bricks.brown": "<PERSON> Field Masoned", "block.minecraft.banner.bricks.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.gray": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.bricks.green": "<PERSON> Green Masoned", "block.minecraft.banner.bricks.light_blue": "Light Blue Field Masoned", "block.minecraft.banner.bricks.light_gray": "<PERSON><PERSON><PERSON> Imọlẹ <PERSON> Masoned", "block.minecraft.banner.bricks.lime": "Lime Field Masoned", "block.minecraft.banner.bricks.magenta": "<PERSON> <PERSON><PERSON>", "block.minecraft.banner.bricks.orange": "Orange Field Masoned", "block.minecraft.banner.bricks.pink": "<PERSON> Masoned", "block.minecraft.banner.bricks.purple": "<PERSON><PERSON>e eleyi ti <PERSON>ed", "block.minecraft.banner.bricks.red": "<PERSON> Field Masoned", "block.minecraft.banner.bricks.white": "Funfun Field Masoned", "block.minecraft.banner.bricks.yellow": "Field Yellow Masoned", "block.minecraft.banner.circle.black": "Black Roundel", "block.minecraft.banner.circle.blue": "Blue Roundel", "block.minecraft.banner.circle.brown": "<PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.circle.gray": "Grẹy Roundel", "block.minecraft.banner.circle.green": "Alawọ ewe <PERSON>el", "block.minecraft.banner.circle.light_blue": "Imọlẹ Blue Roundel", "block.minecraft.banner.circle.light_gray": "Imọlẹ Grẹy Roundel", "block.minecraft.banner.circle.lime": "Orombo Roundel", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "Orange Roundel", "block.minecraft.banner.circle.pink": "Pink Roundel", "block.minecraft.banner.circle.purple": "<PERSON><PERSON>", "block.minecraft.banner.circle.red": "Red Roundel", "block.minecraft.banner.circle.white": "White Roundel", "block.minecraft.banner.circle.yellow": "<PERSON><PERSON><PERSON><PERSON>el", "block.minecraft.banner.creeper.black": "Black Creeper agbara", "block.minecraft.banner.creeper.blue": "Blue Creeper agbara", "block.minecraft.banner.creeper.brown": "Brown Creeper agbara", "block.minecraft.banner.creeper.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.gray": "Grey Creeper Charge", "block.minecraft.banner.creeper.green": "Green Creeper agbara", "block.minecraft.banner.creeper.light_blue": "Light Blue Creeper agbara", "block.minecraft.banner.creeper.light_gray": "Light Grey Creeper agbara", "block.minecraft.banner.creeper.lime": "Lime Creeper idiyele", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON> a<PERSON>", "block.minecraft.banner.creeper.pink": "Pink Creeper agbara", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.creeper.red": "Red Creeper agbara", "block.minecraft.banner.creeper.white": "White Creeper agbara", "block.minecraft.banner.creeper.yellow": "Yellow Creeper agbara", "block.minecraft.banner.cross.black": "Saltire Dudu", "block.minecraft.banner.cross.blue": "Saltire Bulu", "block.minecraft.banner.cross.brown": "Brown Saltire", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.cross.gray": "Imọlẹ Gray Saltire", "block.minecraft.banner.cross.green": "Green Saltire", "block.minecraft.banner.cross.light_blue": "Ina Saltire Blue", "block.minecraft.banner.cross.light_gray": "Imọlẹ Grey Saltire", "block.minecraft.banner.cross.lime": "Orombo Saltire", "block.minecraft.banner.cross.magenta": "Magenta Saltire", "block.minecraft.banner.cross.orange": "Ọsan Saltire", "block.minecraft.banner.cross.pink": "Pink Saltire", "block.minecraft.banner.cross.purple": "Saltire eleyi ti", "block.minecraft.banner.cross.red": "Red Saltire", "block.minecraft.banner.cross.white": "White Saltire", "block.minecraft.banner.cross.yellow": "Saltire Yellow", "block.minecraft.banner.curly_border.black": "Black Bordure Indented", "block.minecraft.banner.curly_border.blue": "Blue Bordure Indented", "block.minecraft.banner.curly_border.brown": "Brown Bordure Indented", "block.minecraft.banner.curly_border.cyan": "<PERSON><PERSON> Indented", "block.minecraft.banner.curly_border.gray": "Grey Bordure Indented", "block.minecraft.banner.curly_border.green": "Green Bordure Indented", "block.minecraft.banner.curly_border.light_blue": "Light Blue Bordure Indented", "block.minecraft.banner.curly_border.light_gray": "Light Grey Bordure Indented", "block.minecraft.banner.curly_border.lime": "Orombo Bordure Indented", "block.minecraft.banner.curly_border.magenta": "Magenta Bordure Indented", "block.minecraft.banner.curly_border.orange": "Orange Bordure Indented", "block.minecraft.banner.curly_border.pink": "Pink Bordure Indented", "block.minecraft.banner.curly_border.purple": "Eleyii Bordure Indented", "block.minecraft.banner.curly_border.red": "Red Bordure Indented", "block.minecraft.banner.curly_border.white": "White Bordure Indented", "block.minecraft.banner.curly_border.yellow": "Yellow Bordure Indented", "block.minecraft.banner.diagonal_left.black": "Black Per tẹ Sinister", "block.minecraft.banner.diagonal_left.blue": "Blue Per tẹ Sinister", "block.minecraft.banner.diagonal_left.brown": "Brown Per tẹ Sinister", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON> tẹ Sinister", "block.minecraft.banner.diagonal_left.gray": "Grey Per Bend Sinister", "block.minecraft.banner.diagonal_left.green": "Green Per tẹ Sinister", "block.minecraft.banner.diagonal_left.light_blue": "Light Blue Per tẹ Sinister", "block.minecraft.banner.diagonal_left.light_gray": "Light Grey Per tẹ Sinister", "block.minecraft.banner.diagonal_left.lime": "Orombo Per Bend Sinister", "block.minecraft.banner.diagonal_left.magenta": "Magenta Per tẹ Sinister", "block.minecraft.banner.diagonal_left.orange": "Orange Per tẹ Sinister", "block.minecraft.banner.diagonal_left.pink": "Pink Per Bend Sinister", "block.minecraft.banner.diagonal_left.purple": "<PERSON><PERSON>i ti Fun tẹ Sinister", "block.minecraft.banner.diagonal_left.red": "Red Per tẹ Sinister", "block.minecraft.banner.diagonal_left.white": "Funfun Fun tẹ Sinister", "block.minecraft.banner.diagonal_left.yellow": "Yellow Per tẹ Sinister", "block.minecraft.banner.diagonal_right.black": "Black Per Tẹ", "block.minecraft.banner.diagonal_right.blue": "Blue Per Tẹ", "block.minecraft.banner.diagonal_right.brown": "Brown Fun tẹ", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.gray": "Grẹy Per Tẹ", "block.minecraft.banner.diagonal_right.green": "Green Fun tẹ", "block.minecraft.banner.diagonal_right.light_blue": "Light Blue Per Tẹ", "block.minecraft.banner.diagonal_right.light_gray": "Light Grey Per tẹ", "block.minecraft.banner.diagonal_right.lime": "Orombo Fun Tẹ", "block.minecraft.banner.diagonal_right.magenta": "Magenta Per tẹ", "block.minecraft.banner.diagonal_right.orange": "Orange Fun tẹ", "block.minecraft.banner.diagonal_right.pink": "Pink Fun tẹ", "block.minecraft.banner.diagonal_right.purple": "<PERSON><PERSON>i ti Per tẹ", "block.minecraft.banner.diagonal_right.red": "Red Per tẹ", "block.minecraft.banner.diagonal_right.white": "Funfun Fun Tẹ", "block.minecraft.banner.diagonal_right.yellow": "Yellow Per tẹ", "block.minecraft.banner.diagonal_up_left.black": "Black Per tẹ Inverted", "block.minecraft.banner.diagonal_up_left.blue": "Blue Per tẹ Sinister", "block.minecraft.banner.diagonal_up_left.brown": "Brown Per tẹ Inverted", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON> Per tẹ Inverted", "block.minecraft.banner.diagonal_up_left.gray": "Grẹy Per Tẹ Inverted", "block.minecraft.banner.diagonal_up_left.green": "Green Per tẹ Yiyipada", "block.minecraft.banner.diagonal_up_left.light_blue": "Light Blue Per tẹ Inverted", "block.minecraft.banner.diagonal_up_left.light_gray": "Light Grey Per tẹ Yiyipada", "block.minecraft.banner.diagonal_up_left.lime": "Orombo Per Fess Inverted", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta Per tẹ Inverted", "block.minecraft.banner.diagonal_up_left.orange": "Orange Per tẹ Inverted", "block.minecraft.banner.diagonal_up_left.pink": "Pink Per Tẹ Inverted", "block.minecraft.banner.diagonal_up_left.purple": "<PERSON><PERSON>i ti Per tẹ Inverted", "block.minecraft.banner.diagonal_up_left.red": "Red Per tẹ Yiyipada", "block.minecraft.banner.diagonal_up_left.white": "Funfun Fun tẹ Yiyipada", "block.minecraft.banner.diagonal_up_left.yellow": "Yellow Per Fess Ti Yiyi pada", "block.minecraft.banner.diagonal_up_right.black": "Black Per tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.blue": "Blue Per Tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.brown": "Brown Per tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.cyan": "<PERSON>an Fun Tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.gray": "Grey Per tẹ Sinister Ti yi pada", "block.minecraft.banner.diagonal_up_right.green": "Green Per tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.light_blue": "Ina Bulu Fun Tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.light_gray": "Imọlẹ Grey Fun Tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.lime": "Orombo Fun Tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.magenta": "Magenta Fun Tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.orange": "Magenta Fun Tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.pink": "Pink Per tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.purple": "<PERSON><PERSON>i ti Per tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.red": "Red Per Bend Sinister Inverted", "block.minecraft.banner.diagonal_up_right.white": "Funfun Fun Tẹ Sinister Inverted", "block.minecraft.banner.diagonal_up_right.yellow": "Yellow Per tẹ Sinister Inverted", "block.minecraft.banner.flow.black": "Black Flow", "block.minecraft.banner.flow.blue": "Blue Flow", "block.minecraft.banner.flow.brown": "Brown Flow", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flow.gray": "Gray Flow", "block.minecraft.banner.flow.green": "Green Flow", "block.minecraft.banner.flow.light_blue": "Light Blue Flow", "block.minecraft.banner.flow.light_gray": "Light Gray Flow", "block.minecraft.banner.flow.lime": "Lime Flow", "block.minecraft.banner.flow.magenta": "Magenta Flow", "block.minecraft.banner.flow.orange": "Orange Flow", "block.minecraft.banner.flow.pink": "Pink Flow", "block.minecraft.banner.flow.purple": "Purple Flow", "block.minecraft.banner.flow.red": "Red Flow", "block.minecraft.banner.flow.white": "White Flow", "block.minecraft.banner.flow.yellow": "Yellow Flow", "block.minecraft.banner.flower.black": "Black Flower agbara", "block.minecraft.banner.flower.blue": "Blue Flower agbara", "block.minecraft.banner.flower.brown": "<PERSON><PERSON>", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.gray": "Grey Flower agbara", "block.minecraft.banner.flower.green": "<PERSON><PERSON> ewe", "block.minecraft.banner.flower.light_blue": "Light Blue Flower agbara", "block.minecraft.banner.flower.light_gray": "Light Grey Flower agbara", "block.minecraft.banner.flower.lime": "Lime Flower agbara", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON>", "block.minecraft.banner.flower.orange": "Ododo Flower Orange", "block.minecraft.banner.flower.pink": "Pink Flower agbara", "block.minecraft.banner.flower.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.flower.red": "Red Flower agbara", "block.minecraft.banner.flower.white": "<PERSON><PERSON>", "block.minecraft.banner.flower.yellow": "Yellow Flower agbara", "block.minecraft.banner.globe.black": "Black Globe", "block.minecraft.banner.globe.blue": "Blue Globe", "block.minecraft.banner.globe.brown": "Brown Globe", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON>", "block.minecraft.banner.globe.gray": "Grẹy Globe", "block.minecraft.banner.globe.green": "Green Globe", "block.minecraft.banner.globe.light_blue": "Light Blue Globe", "block.minecraft.banner.globe.light_gray": "Imọlẹ Grey Globe", "block.minecraft.banner.globe.lime": "Orombo Globe", "block.minecraft.banner.globe.magenta": "Magenta Globe", "block.minecraft.banner.globe.orange": "Orange Globe", "block.minecraft.banner.globe.pink": "Pink Globe", "block.minecraft.banner.globe.purple": "Globe eleyi ti", "block.minecraft.banner.globe.red": "Red Globe", "block.minecraft.banner.globe.white": "Globe Funfun", "block.minecraft.banner.globe.yellow": "Yellow Globe", "block.minecraft.banner.gradient.black": "<PERSON><PERSON>", "block.minecraft.banner.gradient.blue": "Grad<PERSON> buluu", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.gray": "Gradient Grẹy", "block.minecraft.banner.gradient.green": "<PERSON><PERSON><PERSON>aw<PERSON> ewe", "block.minecraft.banner.gradient.light_blue": "Gradient Imọlẹ Ina", "block.minecraft.banner.gradient.light_gray": "Gradient Imọlẹ Grey", "block.minecraft.banner.gradient.lime": "Lime G<PERSON>ient", "block.minecraft.banner.gradient.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.gradient.pink": "Pink Gradient", "block.minecraft.banner.gradient.purple": "Gradient eleyi ti", "block.minecraft.banner.gradient.red": "Gradient pupa", "block.minecraft.banner.gradient.white": "Gradient funfun", "block.minecraft.banner.gradient.yellow": "Grad<PERSON> ofeefee", "block.minecraft.banner.gradient_up.black": "Gradient Ipilẹ Dudu", "block.minecraft.banner.gradient_up.blue": "Gradient Ipilẹ Buluu", "block.minecraft.banner.gradient_up.brown": "Grad<PERSON> Mi<PERSON>", "block.minecraft.banner.gradient_up.cyan": "Gradient Ipilẹ <PERSON><PERSON>", "block.minecraft.banner.gradient_up.gray": "Gradient Base Grẹy", "block.minecraft.banner.gradient_up.green": "Gradient Mi<PERSON>ọ Alawọ ewe", "block.minecraft.banner.gradient_up.light_blue": "Imọlẹ Blue Base Gradient", "block.minecraft.banner.gradient_up.light_gray": "Gradient Base Grey Light", "block.minecraft.banner.gradient_up.lime": "Lime Base Gradient", "block.minecraft.banner.gradient_up.magenta": "Graentaent Ipilẹ Magenta", "block.minecraft.banner.gradient_up.orange": "Gradient Ipilẹ Ọsan", "block.minecraft.banner.gradient_up.pink": "Pink Base Gradient", "block.minecraft.banner.gradient_up.purple": "Gradient Base Purple", "block.minecraft.banner.gradient_up.red": "Red Base Gradient", "block.minecraft.banner.gradient_up.white": "Gradient Ipilẹ White", "block.minecraft.banner.gradient_up.yellow": "Gradient Yellow Base", "block.minecraft.banner.guster.black": "Black Guster", "block.minecraft.banner.guster.blue": "Blue Guster", "block.minecraft.banner.guster.brown": "<PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON>", "block.minecraft.banner.guster.green": "<PERSON> Guster", "block.minecraft.banner.guster.light_blue": "Light Blue Guster", "block.minecraft.banner.guster.light_gray": "Light Gray Guster", "block.minecraft.banner.guster.lime": "<PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "Orange Guster", "block.minecraft.banner.guster.pink": "<PERSON> Guster", "block.minecraft.banner.guster.purple": "Purple Guster", "block.minecraft.banner.guster.red": "<PERSON> Gus<PERSON>", "block.minecraft.banner.guster.white": "White Guster", "block.minecraft.banner.guster.yellow": "Yellow Guster", "block.minecraft.banner.half_horizontal.black": "Black Per Fess", "block.minecraft.banner.half_horizontal.blue": "Blue Per Fess", "block.minecraft.banner.half_horizontal.brown": "<PERSON> Fun Fess", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "Grẹy Per Fess", "block.minecraft.banner.half_horizontal.green": "Green Fun Fess", "block.minecraft.banner.half_horizontal.light_blue": "Light Blue Per Fess", "block.minecraft.banner.half_horizontal.light_gray": "Imọlẹ Grey Per Fess", "block.minecraft.banner.half_horizontal.lime": "Orombo Fun Fess", "block.minecraft.banner.half_horizontal.magenta": "Magenta Fun Fess", "block.minecraft.banner.half_horizontal.orange": "Orange Fun Fess", "block.minecraft.banner.half_horizontal.pink": "Pink Fun Fess", "block.minecraft.banner.half_horizontal.purple": "El<PERSON>i ti <PERSON>", "block.minecraft.banner.half_horizontal.red": "Red Per Fess", "block.minecraft.banner.half_horizontal.white": "Funfun Fun Fess", "block.minecraft.banner.half_horizontal.yellow": "Yellow Per Fess", "block.minecraft.banner.half_horizontal_bottom.black": "Black Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.blue": "Bulu Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON> Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON> Inverted", "block.minecraft.banner.half_horizontal_bottom.gray": "Grey Per Fess Ti Yiyi pada", "block.minecraft.banner.half_horizontal_bottom.green": "Green Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Yellow Per Fess Ti Yiyi pada", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Light Grey Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.lime": "Orombo Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.magenta": "Magenta Per <PERSON>", "block.minecraft.banner.half_horizontal_bottom.orange": "<PERSON><PERSON> T<PERSON>", "block.minecraft.banner.half_horizontal_bottom.pink": "Pink Per Fess Ti yipada", "block.minecraft.banner.half_horizontal_bottom.purple": "Eleyi ti Per <PERSON> Inverted", "block.minecraft.banner.half_horizontal_bottom.red": "Pupa Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.white": "<PERSON><PERSON> T<PERSON>", "block.minecraft.banner.half_horizontal_bottom.yellow": "Yellow Per Fess Ti Yiyi pada", "block.minecraft.banner.half_vertical.black": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.blue": "Blue Fun bia", "block.minecraft.banner.half_vertical.brown": "Brown Fun bia", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.gray": "Grẹy Fun Pale", "block.minecraft.banner.half_vertical.green": "Green Fun bia", "block.minecraft.banner.half_vertical.light_blue": "Light Blue Per bia", "block.minecraft.banner.half_vertical.light_gray": "Light Grey Per bia", "block.minecraft.banner.half_vertical.lime": "Orombo Fun Awọn ṣọọbu", "block.minecraft.banner.half_vertical.magenta": "Magenta <PERSON> Pale", "block.minecraft.banner.half_vertical.orange": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.pink": "Pink Fun bia", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON>i ti <PERSON>", "block.minecraft.banner.half_vertical.red": "Pup<PERSON>", "block.minecraft.banner.half_vertical.white": "Funfun Fun Pa", "block.minecraft.banner.half_vertical.yellow": "Yellow Per bia", "block.minecraft.banner.half_vertical_right.black": "Black Per bia Inverted", "block.minecraft.banner.half_vertical_right.blue": "Blue Per bia Inverted", "block.minecraft.banner.half_vertical_right.brown": "Brown Per bia Inverted", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON>an <PERSON> bia Inverted", "block.minecraft.banner.half_vertical_right.gray": "Grẹy Per bia Inverted", "block.minecraft.banner.half_vertical_right.green": "Green Per bia Yi<PERSON>a", "block.minecraft.banner.half_vertical_right.light_blue": "Light Blue Per bia Inverted", "block.minecraft.banner.half_vertical_right.light_gray": "Light Gray Per bia Inverted", "block.minecraft.banner.half_vertical_right.lime": "Lime Per bia Inverted", "block.minecraft.banner.half_vertical_right.magenta": "Magenta Per <PERSON>", "block.minecraft.banner.half_vertical_right.orange": "Orange Per bia Inverted", "block.minecraft.banner.half_vertical_right.pink": "Pink Per bia Inverted", "block.minecraft.banner.half_vertical_right.purple": "Eleyi ti Per bia Inverted", "block.minecraft.banner.half_vertical_right.red": "Red Per bia Inverted", "block.minecraft.banner.half_vertical_right.white": "Funfun Fun Pale T<PERSON>yi", "block.minecraft.banner.half_vertical_right.yellow": "Yellow Per bia Inverted", "block.minecraft.banner.mojang.black": "<PERSON><PERSON> dudu", "block.minecraft.banner.mojang.blue": "<PERSON>un Blue", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON><PERSON> Grẹy", "block.minecraft.banner.mojang.green": "<PERSON><PERSON> ewe", "block.minecraft.banner.mojang.light_blue": "Light Blue Nkan", "block.minecraft.banner.mojang.light_gray": "<PERSON><PERSON>", "block.minecraft.banner.mojang.lime": "<PERSON><PERSON> orombo wewe", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON>", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON>", "block.minecraft.banner.mojang.purple": "<PERSON><PERSON>", "block.minecraft.banner.mojang.red": "<PERSON>un pupa", "block.minecraft.banner.mojang.white": "<PERSON><PERSON>", "block.minecraft.banner.mojang.yellow": "<PERSON><PERSON>", "block.minecraft.banner.piglin.black": "<PERSON><PERSON> imu dudu", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON> didan", "block.minecraft.banner.piglin.brown": "<PERSON><PERSON> brown", "block.minecraft.banner.piglin.cyan": "Ẹgbẹ turquoise", "block.minecraft.banner.piglin.gray": "<PERSON><PERSON><PERSON> grẹy", "block.minecraft.banner.piglin.green": "Ig<PERSON> ewe", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON> didan", "block.minecraft.banner.piglin.light_gray": "Ina grẹy ina", "block.minecraft.banner.piglin.lime": "Orombo orombo", "block.minecraft.banner.piglin.magenta": "Ẹmi mimu", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON><PERSON> r<PERSON>un osan", "block.minecraft.banner.piglin.pink": "Awọ pupa", "block.minecraft.banner.piglin.purple": "Ẹmi mimu", "block.minecraft.banner.piglin.red": "Iso pupa", "block.minecraft.banner.piglin.white": "Igba funfun", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.rhombus.black": "Black Lozenge", "block.minecraft.banner.rhombus.blue": "Blue Lozenge", "block.minecraft.banner.rhombus.brown": "Brown Lozenge", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "Grey Lozenge", "block.minecraft.banner.rhombus.green": "Green Lozenge", "block.minecraft.banner.rhombus.light_blue": "Imọlẹ Blue Lozenge", "block.minecraft.banner.rhombus.light_gray": "<PERSON><PERSON> Lozenge", "block.minecraft.banner.rhombus.lime": "Orombo Lozenge", "block.minecraft.banner.rhombus.magenta": "Magenta Lozenge", "block.minecraft.banner.rhombus.orange": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.pink": "Pink Lozenge", "block.minecraft.banner.rhombus.purple": "Eleyi ti Lozenge", "block.minecraft.banner.rhombus.red": "<PERSON>upa <PERSON>ge", "block.minecraft.banner.rhombus.white": "White Lozenge", "block.minecraft.banner.rhombus.yellow": "Yellow Lozenge", "block.minecraft.banner.skull.black": "Black Skull idiyele", "block.minecraft.banner.skull.blue": "Blue Skull idiyele", "block.minecraft.banner.skull.brown": "Brown Skull idiyele", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.gray": "Grey Skull Skrege", "block.minecraft.banner.skull.green": "Green Skull idiyele", "block.minecraft.banner.skull.light_blue": "Light Blue Skull Charge", "block.minecraft.banner.skull.light_gray": "Light Grey Skull idiyele", "block.minecraft.banner.skull.lime": "Lime Skull idiyele", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.orange": "Orange Skull idiyele", "block.minecraft.banner.skull.pink": "<PERSON> idiyele", "block.minecraft.banner.skull.purple": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.skull.red": "Red Skull idiyele", "block.minecraft.banner.skull.white": "White Skull idiyele", "block.minecraft.banner.skull.yellow": "Yellow Skull idiyele", "block.minecraft.banner.small_stripes.black": "<PERSON> Paly", "block.minecraft.banner.small_stripes.blue": "Blue Paly", "block.minecraft.banner.small_stripes.brown": "<PERSON>", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON> bia", "block.minecraft.banner.small_stripes.gray": "<PERSON>", "block.minecraft.banner.small_stripes.green": "Alawọ ewe <PERSON>ly", "block.minecraft.banner.small_stripes.light_blue": "Imọlẹ Blue Paly", "block.minecraft.banner.small_stripes.light_gray": "Imọlẹ Grey Paly", "block.minecraft.banner.small_stripes.lime": "Orombo Dun", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.orange": "Orange Paly", "block.minecraft.banner.small_stripes.pink": "<PERSON> Paly", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.white": "<PERSON>ly", "block.minecraft.banner.small_stripes.yellow": "Yellow Ṣiṣẹ", "block.minecraft.banner.square_bottom_left.black": "Black Mimọ Dexter Canton", "block.minecraft.banner.square_bottom_left.blue": "Blue Chief <PERSON>", "block.minecraft.banner.square_bottom_left.brown": "Brown Chief <PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.cyan": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.gray": "Grey Mimọ Dexter Canton", "block.minecraft.banner.square_bottom_left.green": "Green Chief <PERSON>", "block.minecraft.banner.square_bottom_left.light_blue": "Imọlẹ Blue Base Dexter Canton", "block.minecraft.banner.square_bottom_left.light_gray": "Light Gray Mimọ Dexter Canton", "block.minecraft.banner.square_bottom_left.lime": "Orombo Mimọ Dexter Canton", "block.minecraft.banner.square_bottom_left.magenta": "Magenta Mimọ Dexter Canton", "block.minecraft.banner.square_bottom_left.orange": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.pink": "Pink Mimọ Dexter Canton", "block.minecraft.banner.square_bottom_left.purple": "Purple Chief <PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.red": "Red Chief Sin<PERSON>", "block.minecraft.banner.square_bottom_left.white": "White Base Dexter Canton", "block.minecraft.banner.square_bottom_left.yellow": "Yellow Mimọ Dexter Canton", "block.minecraft.banner.square_bottom_right.black": "Black Mimọ Sinister Canton", "block.minecraft.banner.square_bottom_right.blue": "Blue Base Sinister Canton", "block.minecraft.banner.square_bottom_right.brown": "Brown Mimọ Sinister Canton", "block.minecraft.banner.square_bottom_right.cyan": "<PERSON>an <PERSON> Sinister Canton", "block.minecraft.banner.square_bottom_right.gray": "Gray Mimọ Sinister Canton", "block.minecraft.banner.square_bottom_right.green": "Green Mimọ Sinister Canton", "block.minecraft.banner.square_bottom_right.light_blue": "Imọlẹ Blue Base Sinister Canton", "block.minecraft.banner.square_bottom_right.light_gray": "Light Gray Mimọ Sinister Canton", "block.minecraft.banner.square_bottom_right.lime": "Orombo Mimọ Sinister Canton", "block.minecraft.banner.square_bottom_right.magenta": "Magenta Mimọ Sinister Canton", "block.minecraft.banner.square_bottom_right.orange": "<PERSON><PERSON> Sinister Canton", "block.minecraft.banner.square_bottom_right.pink": "Pink Mimọ Sinister Canton", "block.minecraft.banner.square_bottom_right.purple": "Eleyi ti Base Sinister Canton", "block.minecraft.banner.square_bottom_right.red": "Red Base Sinister Canton", "block.minecraft.banner.square_bottom_right.white": "White Base Sinister Canton", "block.minecraft.banner.square_bottom_right.yellow": "Yellow Mimọ Sinister Canton", "block.minecraft.banner.square_top_left.black": "Black Chief <PERSON>", "block.minecraft.banner.square_top_left.blue": "Blue Chief <PERSON>", "block.minecraft.banner.square_top_left.brown": "Brown Chief <PERSON>", "block.minecraft.banner.square_top_left.cyan": "Cyan Chief <PERSON>", "block.minecraft.banner.square_top_left.gray": "Grey Chief <PERSON>", "block.minecraft.banner.square_top_left.green": "Green Chief <PERSON>", "block.minecraft.banner.square_top_left.light_blue": "Imọlẹ Blue Chief <PERSON>", "block.minecraft.banner.square_top_left.light_gray": "<PERSON><PERSON><PERSON> Chief <PERSON>", "block.minecraft.banner.square_top_left.lime": "Orombo Olori Dexter Canton", "block.minecraft.banner.square_top_left.magenta": "Magenta Chief <PERSON>", "block.minecraft.banner.square_top_left.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_left.pink": "Pink Chief <PERSON>", "block.minecraft.banner.square_top_left.purple": "<PERSON><PERSON>i ti Chief <PERSON>", "block.minecraft.banner.square_top_left.red": "Red Chief <PERSON>", "block.minecraft.banner.square_top_left.white": "White Chief <PERSON>", "block.minecraft.banner.square_top_left.yellow": "Yellow Chief <PERSON>", "block.minecraft.banner.square_top_right.black": "Black Chief <PERSON>", "block.minecraft.banner.square_top_right.blue": "Blue Chief <PERSON>", "block.minecraft.banner.square_top_right.brown": "Brown Chief <PERSON><PERSON>", "block.minecraft.banner.square_top_right.cyan": "Cyan Chief <PERSON><PERSON>", "block.minecraft.banner.square_top_right.gray": "Grey Chief <PERSON>", "block.minecraft.banner.square_top_right.green": "Green Chief <PERSON>", "block.minecraft.banner.square_top_right.light_blue": "Imọlẹ Blue Chief Sinister Canton", "block.minecraft.banner.square_top_right.light_gray": "Light Gray Chief <PERSON>", "block.minecraft.banner.square_top_right.lime": "Orombo Olori Sinister Canton", "block.minecraft.banner.square_top_right.magenta": "Magenta Chief Sin<PERSON>", "block.minecraft.banner.square_top_right.orange": "<PERSON><PERSON>mi Chief Sin<PERSON> Canton", "block.minecraft.banner.square_top_right.pink": "Pink Chief <PERSON>", "block.minecraft.banner.square_top_right.purple": "<PERSON><PERSON>i ti Chief Sin<PERSON>", "block.minecraft.banner.square_top_right.red": "Red Chief Sin<PERSON>", "block.minecraft.banner.square_top_right.white": "White Chief <PERSON>ister <PERSON>", "block.minecraft.banner.square_top_right.yellow": "Yellow Chief Sinister <PERSON>", "block.minecraft.banner.straight_cross.black": "Black Cross", "block.minecraft.banner.straight_cross.blue": "Blue Cross", "block.minecraft.banner.straight_cross.brown": "<PERSON>", "block.minecraft.banner.straight_cross.cyan": "Cross Cyan", "block.minecraft.banner.straight_cross.gray": "Grẹy Fess", "block.minecraft.banner.straight_cross.green": "Green Cross", "block.minecraft.banner.straight_cross.light_blue": "Imọlẹ Blue Fess", "block.minecraft.banner.straight_cross.light_gray": "Imọlẹ Grẹy Fess", "block.minecraft.banner.straight_cross.lime": "Orombo Fess", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.pink": "Pink Fess", "block.minecraft.banner.straight_cross.purple": "Agbelebu Purple", "block.minecraft.banner.straight_cross.red": "Red Cross", "block.minecraft.banner.straight_cross.white": "Orombo Fess", "block.minecraft.banner.straight_cross.yellow": "Fess ofee<PERSON>e", "block.minecraft.banner.stripe_bottom.black": "Black Mimọ", "block.minecraft.banner.stripe_bottom.blue": "Ipilẹ Blue", "block.minecraft.banner.stripe_bottom.brown": "<PERSON> Ipilẹ", "block.minecraft.banner.stripe_bottom.cyan": "Ipilẹ <PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.gray": "Grẹy <PERSON>mọ", "block.minecraft.banner.stripe_bottom.green": "Ipilẹ Alawọ ewe", "block.minecraft.banner.stripe_bottom.light_blue": "Imọlẹ Blue Base", "block.minecraft.banner.stripe_bottom.light_gray": "Imọlẹ Grẹy Light", "block.minecraft.banner.stripe_bottom.lime": "Orombo Mimọ", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "Ipilẹ Pink", "block.minecraft.banner.stripe_bottom.purple": "Ipilẹ eleyi ti", "block.minecraft.banner.stripe_bottom.red": "Red Mi<PERSON>ọ", "block.minecraft.banner.stripe_bottom.white": "White Mimọ", "block.minecraft.banner.stripe_bottom.yellow": "Mimọ Yellow", "block.minecraft.banner.stripe_center.black": "Black Mimọ", "block.minecraft.banner.stripe_center.blue": "Blue bia", "block.minecraft.banner.stripe_center.brown": "Brown bia", "block.minecraft.banner.stripe_center.cyan": "<PERSON><PERSON> bia", "block.minecraft.banner.stripe_center.gray": "Grẹy bia", "block.minecraft.banner.stripe_center.green": "Imọlẹ Alawọ ewe", "block.minecraft.banner.stripe_center.light_blue": "Ina Bulu Ina", "block.minecraft.banner.stripe_center.light_gray": "Imọlẹ Grẹy Light", "block.minecraft.banner.stripe_center.lime": "Orombo bia", "block.minecraft.banner.stripe_center.magenta": "Magenta bia", "block.minecraft.banner.stripe_center.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.pink": "Pink bia", "block.minecraft.banner.stripe_center.purple": "<PERSON>ia eleyi ti", "block.minecraft.banner.stripe_center.red": "<PERSON>upa bia", "block.minecraft.banner.stripe_center.white": "White bia", "block.minecraft.banner.stripe_center.yellow": "<PERSON><PERSON><PERSON>eefee", "block.minecraft.banner.stripe_downleft.black": "Black tẹ Sinister", "block.minecraft.banner.stripe_downleft.blue": "Blue Tẹ Sinister", "block.minecraft.banner.stripe_downleft.brown": "<PERSON> tẹ Sinister", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON> Tẹ Sinister", "block.minecraft.banner.stripe_downleft.gray": "<PERSON> tẹ Sinister", "block.minecraft.banner.stripe_downleft.green": "Green tẹ Sinister", "block.minecraft.banner.stripe_downleft.light_blue": "Ina Blue tẹ Sinister", "block.minecraft.banner.stripe_downleft.light_gray": "Light Gray tẹ Sinister", "block.minecraft.banner.stripe_downleft.lime": "Orombo tẹ Sinister", "block.minecraft.banner.stripe_downleft.magenta": "Magenta Tẹ Sinister", "block.minecraft.banner.stripe_downleft.orange": "<PERSON><PERSON> Tẹ Sinister", "block.minecraft.banner.stripe_downleft.pink": "Pink tẹ Sinister", "block.minecraft.banner.stripe_downleft.purple": "<PERSON><PERSON>i ti tẹ Sinister", "block.minecraft.banner.stripe_downleft.red": "Red tẹ Sinister", "block.minecraft.banner.stripe_downleft.white": "Funfun Tẹ Sinister", "block.minecraft.banner.stripe_downleft.yellow": "Yellow tẹ Sinister", "block.minecraft.banner.stripe_downright.black": "Black tẹ", "block.minecraft.banner.stripe_downright.blue": "Bulu Tẹ", "block.minecraft.banner.stripe_downright.brown": "<PERSON> tẹ", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON> tẹ", "block.minecraft.banner.stripe_downright.gray": "Gray tẹ", "block.minecraft.banner.stripe_downright.green": "Green tẹ", "block.minecraft.banner.stripe_downright.light_blue": "Ina Blue Tẹ", "block.minecraft.banner.stripe_downright.light_gray": "<PERSON><PERSON> Tẹ", "block.minecraft.banner.stripe_downright.lime": "Lime tẹ", "block.minecraft.banner.stripe_downright.magenta": "Magenta Tẹ", "block.minecraft.banner.stripe_downright.orange": "Tẹ Orange", "block.minecraft.banner.stripe_downright.pink": "Pink tẹ", "block.minecraft.banner.stripe_downright.purple": "Tẹ eleyi ti", "block.minecraft.banner.stripe_downright.red": "Red tẹ", "block.minecraft.banner.stripe_downright.white": "Funfun Tẹ", "block.minecraft.banner.stripe_downright.yellow": "Yẹ tẹẹrẹ", "block.minecraft.banner.stripe_left.black": "Black bia Dexter", "block.minecraft.banner.stripe_left.blue": "<PERSON> Di<PERSON> Dexter", "block.minecraft.banner.stripe_left.brown": "<PERSON> bia Dexter", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON> bia <PERSON>", "block.minecraft.banner.stripe_left.gray": "Grey bia Dexter", "block.minecraft.banner.stripe_left.green": "Green bia Dexter", "block.minecraft.banner.stripe_left.light_blue": "Ina Dexter Bulu Onitẹlẹ", "block.minecraft.banner.stripe_left.light_gray": "<PERSON>a <PERSON> bia Dexter", "block.minecraft.banner.stripe_left.lime": "Orombo bia Dexter", "block.minecraft.banner.stripe_left.magenta": "Magenta bia Dexter", "block.minecraft.banner.stripe_left.orange": "<PERSON> bia", "block.minecraft.banner.stripe_left.pink": "Pink bia Dexter", "block.minecraft.banner.stripe_left.purple": "Dexter bia eleyi ti", "block.minecraft.banner.stripe_left.red": "Red bia Dexter", "block.minecraft.banner.stripe_left.white": "White bia Dexter", "block.minecraft.banner.stripe_left.yellow": "Dexter bia bia", "block.minecraft.banner.stripe_middle.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.blue": "Blue Fess", "block.minecraft.banner.stripe_middle.brown": "Brown Fess", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "Grẹy Fess", "block.minecraft.banner.stripe_middle.green": "Awọn <PERSON>", "block.minecraft.banner.stripe_middle.light_blue": "Imọlẹ Blue Fess", "block.minecraft.banner.stripe_middle.light_gray": "Imọlẹ Grẹy Fess", "block.minecraft.banner.stripe_middle.lime": "Orombo Fess", "block.minecraft.banner.stripe_middle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.pink": "Pink Fess", "block.minecraft.banner.stripe_middle.purple": "Fess eleyi ti", "block.minecraft.banner.stripe_middle.red": "Red Fess", "block.minecraft.banner.stripe_middle.white": "Funfun Fess", "block.minecraft.banner.stripe_middle.yellow": "Fess ofee<PERSON>e", "block.minecraft.banner.stripe_right.black": "Black bia Sinister", "block.minecraft.banner.stripe_right.blue": "Blue bia Sinister", "block.minecraft.banner.stripe_right.brown": "<PERSON> bia Sinister", "block.minecraft.banner.stripe_right.cyan": "<PERSON>an bia <PERSON>", "block.minecraft.banner.stripe_right.gray": "Grẹy bia ominous", "block.minecraft.banner.stripe_right.green": "Green bia Sinister", "block.minecraft.banner.stripe_right.light_blue": "Light Blue Pale Sinister", "block.minecraft.banner.stripe_right.light_gray": "Light Gray bia Sinister", "block.minecraft.banner.stripe_right.lime": "Orombo bia Sinister", "block.minecraft.banner.stripe_right.magenta": "Magenta bia Sinister", "block.minecraft.banner.stripe_right.orange": "<PERSON><PERSON> bia <PERSON>", "block.minecraft.banner.stripe_right.pink": "Pink bia Sinister", "block.minecraft.banner.stripe_right.purple": "Sinister bia", "block.minecraft.banner.stripe_right.red": "Red bia Sinister", "block.minecraft.banner.stripe_right.white": "White Pale Sinister", "block.minecraft.banner.stripe_right.yellow": "Yellow bia Sinister", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.blue": "<PERSON><PERSON><PERSON> buluu", "block.minecraft.banner.stripe_top.brown": "<PERSON> Chief", "block.minecraft.banner.stripe_top.cyan": "Chief <PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "Grey Chief", "block.minecraft.banner.stripe_top.green": "<PERSON><PERSON><PERSON>we", "block.minecraft.banner.stripe_top.light_blue": "Imọlẹ Blue Chief", "block.minecraft.banner.stripe_top.light_gray": "<PERSON><PERSON>i Grẹy Light", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "Magenta Chief", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.pink": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON><PERSON> el<PERSON>i", "block.minecraft.banner.stripe_top.red": "Red Chief", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "Yellow Chief", "block.minecraft.banner.triangle_bottom.black": "Black Chevron", "block.minecraft.banner.triangle_bottom.blue": "Blue Chevron", "block.minecraft.banner.triangle_bottom.brown": "<PERSON> Chevron", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON> Chevron", "block.minecraft.banner.triangle_bottom.green": "Alaw<PERSON> ewe <PERSON>", "block.minecraft.banner.triangle_bottom.light_blue": "Ina Blue Chevron", "block.minecraft.banner.triangle_bottom.light_gray": "Imọlẹ Grey Chevron", "block.minecraft.banner.triangle_bottom.lime": "Orombo Chevron", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON>ron", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.pink": "Pink Chevron", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON>i ti Chevron", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.white": "White Chevron", "block.minecraft.banner.triangle_bottom.yellow": "Yellow Chevron", "block.minecraft.banner.triangle_top.black": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.blue": "Bulu Inverted Chevron", "block.minecraft.banner.triangle_top.brown": "<PERSON> Inverted Chevron", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON> In<PERSON> Chevron", "block.minecraft.banner.triangle_top.gray": "Grẹy Inverted Chevron", "block.minecraft.banner.triangle_top.green": "Green Inverted Chevron", "block.minecraft.banner.triangle_top.light_blue": "Light Chevron <PERSON> Buluu", "block.minecraft.banner.triangle_top.light_gray": "Imọlẹ <PERSON>", "block.minecraft.banner.triangle_top.lime": "Orombo Inverted Chevron", "block.minecraft.banner.triangle_top.magenta": "Magenta Inverted Chevron", "block.minecraft.banner.triangle_top.orange": "<PERSON><PERSON>evron", "block.minecraft.banner.triangle_top.pink": "Pink Inverted Chevron", "block.minecraft.banner.triangle_top.purple": "Chevron eleyi ti <PERSON>yi", "block.minecraft.banner.triangle_top.red": "Red inverted Chevron", "block.minecraft.banner.triangle_top.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.yellow": "Yellow Inverted Chevron", "block.minecraft.banner.triangles_bottom.black": "Black mimọ Indented", "block.minecraft.banner.triangles_bottom.blue": "Blue Base Indented", "block.minecraft.banner.triangles_bottom.brown": "Brown mimọ Indented", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON> Indented", "block.minecraft.banner.triangles_bottom.gray": "<PERSON> Indented", "block.minecraft.banner.triangles_bottom.green": "Green mimọ Indented", "block.minecraft.banner.triangles_bottom.light_blue": "Yellow Mimọ Indented", "block.minecraft.banner.triangles_bottom.light_gray": "Imọlẹ Gray Mimọ Indented", "block.minecraft.banner.triangles_bottom.lime": "Orombo Mimọ Indented", "block.minecraft.banner.triangles_bottom.magenta": "Yellow Mimọ Indented", "block.minecraft.banner.triangles_bottom.orange": "Orange Mimọ Indented", "block.minecraft.banner.triangles_bottom.pink": "Pink Mimọ Indented", "block.minecraft.banner.triangles_bottom.purple": "Purple Mimọ Indented", "block.minecraft.banner.triangles_bottom.red": "Red mimọ Indented", "block.minecraft.banner.triangles_bottom.white": "White mimọ Indented", "block.minecraft.banner.triangles_bottom.yellow": "Yellow Mimọ Indented", "block.minecraft.banner.triangles_top.black": "Black Chief Indented", "block.minecraft.banner.triangles_top.blue": "Blue Chief Indented", "block.minecraft.banner.triangles_top.brown": "<PERSON> Chief Indented", "block.minecraft.banner.triangles_top.cyan": "Cyan Chief Indented", "block.minecraft.banner.triangles_top.gray": "Light Gray Chief Indented", "block.minecraft.banner.triangles_top.green": "Green Chief Indented", "block.minecraft.banner.triangles_top.light_blue": "<PERSON>a Blue Chief Indented", "block.minecraft.banner.triangles_top.light_gray": "Light Gray Chief Indented", "block.minecraft.banner.triangles_top.lime": "Orombo Oloye Indented", "block.minecraft.banner.triangles_top.magenta": "Magenta Chief Indented", "block.minecraft.banner.triangles_top.orange": "Oloye Oloye Indented", "block.minecraft.banner.triangles_top.pink": "Pink Chief Indented", "block.minecraft.banner.triangles_top.purple": "Purple Chief Indented", "block.minecraft.banner.triangles_top.red": "Pupa Chief Indented", "block.minecraft.banner.triangles_top.white": "Funfun Chief Indented", "block.minecraft.banner.triangles_top.yellow": "Yellow Chief Indented", "block.minecraft.barrel": "<PERSON><PERSON><PERSON>n ti a ko mọ", "block.minecraft.barrier": "Idankan", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "<PERSON>gbara <PERSON>", "block.minecraft.beacon.secondary": "<PERSON><PERSON><PERSON> Atẹle", "block.minecraft.bed.no_sleep": "O le sun nikan ni alẹ tabi lakoko iji ojo", "block.minecraft.bed.not_safe": "O le ma sinmi bayi; ohun ibanilẹru wa nitosi", "block.minecraft.bed.obstructed": "Ibo yii ti ni idena", "block.minecraft.bed.occupied": "<PERSON>bo yii ti tẹdo", "block.minecraft.bed.too_far_away": "O le ma sinmi bayi; i<PERSON>un naa jina ju lọ", "block.minecraft.bedrock": "Lya apata", "block.minecraft.bee_nest": "Bee itẹ-ẹiyẹ", "block.minecraft.beehive": "Bee", "block.minecraft.beetroots": "<PERSON><PERSON>", "block.minecraft.bell": "Bel<PERSON>", "block.minecraft.big_dripleaf": "Big Dripleaf", "block.minecraft.big_dripleaf_stem": "Big Dripleaf Stem", "block.minecraft.birch_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.birch_door": "Birch enu", "block.minecraft.birch_fence": "Birch odi", "block.minecraft.birch_fence_gate": "Birch Ẹnubodè Pada", "block.minecraft.birch_hanging_sign": "Birch ikele Sign", "block.minecraft.birch_leaves": "Birch bunkun", "block.minecraft.birch_log": "<PERSON>", "block.minecraft.birch_planks": "Ile-iṣẹ Birch", "block.minecraft.birch_pressure_plate": "Birch Ipa titẹ", "block.minecraft.birch_sapling": "Ororoo birch", "block.minecraft.birch_sign": "Birch awo", "block.minecraft.birch_slab": "Birch awo", "block.minecraft.birch_stairs": "<PERSON> atẹgun", "block.minecraft.birch_trapdoor": "Birch niye", "block.minecraft.birch_wall_hanging_sign": "Birch Wall ikele Sign", "block.minecraft.birch_wall_sign": "<PERSON> Sign", "block.minecraft.birch_wood": "Birch igi", "block.minecraft.black_banner": "Asia Dudu", "block.minecraft.black_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.black_candle": "Dudu Abẹla", "block.minecraft.black_candle_cake": "Akara oyinbo pẹlu dudu abẹla", "block.minecraft.black_carpet": "<PERSON><PERSON>", "block.minecraft.black_concrete": "<PERSON><PERSON>", "block.minecraft.black_concrete_powder": "<PERSON><PERSON> lulú fẹẹrẹ", "block.minecraft.black_glazed_terracotta": "<PERSON><PERSON> Glazed Terracotta", "block.minecraft.black_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.black_stained_glass": "<PERSON><PERSON> g<PERSON>", "block.minecraft.black_stained_glass_pane": "<PERSON><PERSON> g<PERSON>", "block.minecraft.black_terracotta": "<PERSON><PERSON> te<PERSON>", "block.minecraft.black_wool": "<PERSON><PERSON> k<PERSON> irun", "block.minecraft.blackstone": "<PERSON><PERSON> dudu", "block.minecraft.blackstone_slab": "Apẹrẹ gusu", "block.minecraft.blackstone_stairs": "<PERSON><PERSON> ogiri dudu", "block.minecraft.blackstone_wall": "<PERSON>du odi dudu", "block.minecraft.blast_furnace": "Blast Furnace", "block.minecraft.blue_banner": "Asia Blue", "block.minecraft.blue_bed": "Bulu Ibusun", "block.minecraft.blue_candle": "Bulu Abẹla", "block.minecraft.blue_candle_cake": "Akara oyinbo pẹlu Bulu abẹla", "block.minecraft.blue_carpet": "<PERSON><PERSON> carpeti", "block.minecraft.blue_concrete": "Imọlẹ Buluu", "block.minecraft.blue_concrete_powder": "Ipara lul<PERSON>", "block.minecraft.blue_glazed_terracotta": "Glazed Terracotta", "block.minecraft.blue_ice": "<PERSON>ulu yinyin", "block.minecraft.blue_orchid": "Bulu orchid", "block.minecraft.blue_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.blue_stained_glass": "Bulu gilasi", "block.minecraft.blue_stained_glass_pane": "Bulu gilasi", "block.minecraft.blue_terracotta": "Bulu terakota", "block.minecraft.blue_wool": "Bulu kìki irun", "block.minecraft.bone_block": "<PERSON><PERSON><PERSON><PERSON> Den<PERSON>", "block.minecraft.bookshelf": "<PERSON><PERSON> oju", "block.minecraft.brain_coral": "Coral Brain", "block.minecraft.brain_coral_block": "Ti Nmu Coral Block", "block.minecraft.brain_coral_fan": "A fọọmu ti agbada epo", "block.minecraft.brain_coral_wall_fan": "Odi ti coefal", "block.minecraft.brewing_stand": "<PERSON><PERSON>", "block.minecraft.brick_slab": "Brick awo", "block.minecraft.brick_stairs": "<PERSON>", "block.minecraft.brick_wall": "<PERSON><PERSON>", "block.minecraft.bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "<PERSON>", "block.minecraft.brown_bed": "<PERSON>un", "block.minecraft.brown_candle": "<PERSON><PERSON><PERSON><PERSON> Abẹla", "block.minecraft.brown_candle_cake": "Akara oyinbo pẹlu Brown abẹla", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_concrete": "<PERSON><PERSON><PERSON> brown", "block.minecraft.brown_concrete_powder": "Ipara lul<PERSON>", "block.minecraft.brown_glazed_terracotta": "Terracotta Brown", "block.minecraft.brown_mushroom": "<PERSON>", "block.minecraft.brown_mushroom_block": "<PERSON><PERSON> mycelium", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "Orukọ gilasi", "block.minecraft.brown_stained_glass_pane": "Orukọ gilasi", "block.minecraft.brown_terracotta": "Orukọ terakota", "block.minecraft.brown_wool": "Orukọ kìki irun", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "<PERSON><PERSON> buba", "block.minecraft.bubble_coral_block": "Block ti coral nkuta", "block.minecraft.bubble_coral_fan": "A àìpẹ ti iyọ iṣuu", "block.minecraft.bubble_coral_wall_fan": "Orilẹ-ọmọ ti o ni ẹba iṣuu", "block.minecraft.budding_amethyst": "Budding Amethyst", "block.minecraft.bush": "<PERSON>", "block.minecraft.cactus": "Ọrọ", "block.minecraft.cactus_flower": "Cactus Flower", "block.minecraft.cake": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.calcite": "Calcite", "block.minecraft.calibrated_sculk_sensor": "<PERSON><PERSON><PERSON> Sculk Calibrated", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>a mejo", "block.minecraft.candle": "Abẹla", "block.minecraft.candle_cake": "Abẹla Akara Oyinbo", "block.minecraft.carrots": "<PERSON><PERSON><PERSON>", "block.minecraft.cartography_table": "Atilẹyin <PERSON><PERSON><PERSON>n aworan", "block.minecraft.carved_pumpkin": "Ti gbe elegede", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "<PERSON><PERSON>", "block.minecraft.cave_vines": "<PERSON><PERSON>", "block.minecraft.cave_vines_plant": "Iho Ajara Plant", "block.minecraft.chain": "<PERSON><PERSON>", "block.minecraft.chain_command_block": "Ṣiṣẹ Ipa Ti Kii", "block.minecraft.cherry_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_door": "Àgbálùmo ilẹkun", "block.minecraft.cherry_fence": "Àgbálùmo Ẹnubodè", "block.minecraft.cherry_fence_gate": "Àgbálùmo Ẹnubodè Pada", "block.minecraft.cherry_hanging_sign": "Cherry ikele Sign", "block.minecraft.cherry_leaves": "<PERSON><PERSON>", "block.minecraft.cherry_log": "Àgb<PERSON>l<PERSON>mo ẹhin igi", "block.minecraft.cherry_planks": "Àgb<PERSON><PERSON><PERSON><PERSON> igi", "block.minecraft.cherry_pressure_plate": "Àgbálùmo I<PERSON> titẹ", "block.minecraft.cherry_sapling": "Àgb<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_sign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>", "block.minecraft.cherry_slab": "Àgbálùmo ami", "block.minecraft.cherry_stairs": "Àgbálùmo atẹgun", "block.minecraft.cherry_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cherry_wall_hanging_sign": "Cherry Wall ikele Sign", "block.minecraft.cherry_wall_sign": "Cherry Wall Sign", "block.minecraft.cherry_wood": "Àgb<PERSON><PERSON><PERSON><PERSON> igi", "block.minecraft.chest": "Àyà", "block.minecraft.chipped_anvil": "B<PERSON>tini ti a ti bajẹ pupọ", "block.minecraft.chiseled_bookshelf": "Chiseled Bookshelf", "block.minecraft.chiseled_copper": "Chiseled Copper", "block.minecraft.chiseled_deepslate": "Cobbled <PERSON><PERSON><PERSON>", "block.minecraft.chiseled_nether_bricks": "<PERSON><PERSON><PERSON><PERSON> biriki Nether biriki", "block.minecraft.chiseled_polished_blackstone": "<PERSON><PERSON> didan ti dudu didan", "block.minecraft.chiseled_quartz_block": "Chiseled Quatz Block", "block.minecraft.chiseled_red_sandstone": "Gba pupa Iyanrino<PERSON>ta", "block.minecraft.chiseled_resin_bricks": "Chiseled Resin Bricks", "block.minecraft.chiseled_sandstone": "Gbe Iyanrinokuta", "block.minecraft.chiseled_stone_bricks": "Awọn Bricks <PERSON><PERSON>", "block.minecraft.chiseled_tuff": "Chiseled <PERSON>", "block.minecraft.chiseled_tuff_bricks": "Chiseled Tuff Bricks", "block.minecraft.chorus_flower": "Egbe Flower", "block.minecraft.chorus_plant": "Ọkọ <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.clay": "<PERSON>o", "block.minecraft.closed_eyeblossom": "Closed Eyeblossom", "block.minecraft.coal_block": "Àkọsílẹ edu", "block.minecraft.coal_ore": "<PERSON><PERSON><PERSON> i<PERSON>", "block.minecraft.coarse_dirt": "Aye lile", "block.minecraft.cobbled_deepslate": "<PERSON><PERSON><PERSON><PERSON> Cobbled", "block.minecraft.cobbled_deepslate_slab": "Cobbled Deepslate Slab", "block.minecraft.cobbled_deepslate_stairs": "Cobbled Deepslate pẹtẹẹsì", "block.minecraft.cobbled_deepslate_wall": "Cobbled <PERSON><PERSON><PERSON>", "block.minecraft.cobblestone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cobblestone_slab": "Pèpéle gba", "block.minecraft.cobblestone_stairs": "<PERSON>è<PERSON><PERSON><PERSON> atẹgun", "block.minecraft.cobblestone_wall": "<PERSON><PERSON>", "block.minecraft.cobweb": "Ibuwọlu", "block.minecraft.cocoa": "<PERSON><PERSON>", "block.minecraft.command_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.comparator": "Afiwera Redstone", "block.minecraft.composter": "Composter", "block.minecraft.conduit": "The asiwaju", "block.minecraft.copper_block": "Àkọsílẹ ti Ejò", "block.minecraft.copper_bulb": "Copper Bulb", "block.minecraft.copper_door": "Copper Door", "block.minecraft.copper_grate": "Copper Grate", "block.minecraft.copper_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.copper_trapdoor": "Copper Trapdoor", "block.minecraft.cornflower": "<PERSON><PERSON>", "block.minecraft.cracked_deepslate_bricks": "Awọn biriki Ti a fọ", "block.minecraft.cracked_deepslate_tiles": "Awọn Tiles Deepslate Ti Fọ", "block.minecraft.cracked_nether_bricks": "Aw<PERSON>n biriki tẹtẹ", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON><PERSON><PERSON> okuta didan dudu pẹlu awọn dojuijako", "block.minecraft.cracked_stone_bricks": "Bricks Stone Bricks", "block.minecraft.crafter": "Crafter", "block.minecraft.crafting_table": "Ipele iṣẹ", "block.minecraft.creaking_heart": "Creaking Heart", "block.minecraft.creeper_head": "Creeper Orí", "block.minecraft.creeper_wall_head": "<PERSON>i ori <PERSON>", "block.minecraft.crimson_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.crimson_door": "<PERSON> ilekun", "block.minecraft.crimson_fence": "Crimson O<PERSON>", "block.minecraft.crimson_fence_gate": "Crimson Fence Gate", "block.minecraft.crimson_fungus": "Crimson Fungus", "block.minecraft.crimson_hanging_sign": "Crimson ikele Sign", "block.minecraft.crimson_hyphae": "Crimson Hyphae", "block.minecraft.crimson_nylium": "Pupa Nylium", "block.minecraft.crimson_planks": "Crimson Planks", "block.minecraft.crimson_pressure_plate": "Plateson Ipa Plate", "block.minecraft.crimson_roots": "Oloogun Roots", "block.minecraft.crimson_sign": "Crimson Ami", "block.minecraft.crimson_slab": "Crimson Slab", "block.minecraft.crimson_stairs": "Awọn atẹgun Crimson", "block.minecraft.crimson_stem": "<PERSON> Stalk", "block.minecraft.crimson_trapdoor": "Crimson Trapdoor", "block.minecraft.crimson_wall_hanging_sign": "Crimson Wall ikele Sign", "block.minecraft.crimson_wall_sign": "Crimson Wall Ami", "block.minecraft.crying_obsidian": "Wiko obsidian", "block.minecraft.cut_copper": "<PERSON><PERSON> E<PERSON>ò", "block.minecraft.cut_copper_slab": "Ge pẹlẹbẹ irin", "block.minecraft.cut_copper_stairs": "Ge Ejò pẹtẹẹsì", "block.minecraft.cut_red_sandstone": "<PERSON>e Igi <PERSON>-pupa", "block.minecraft.cut_red_sandstone_slab": "<PERSON><PERSON>-pupa", "block.minecraft.cut_sandstone": "Ge Gusu", "block.minecraft.cut_sandstone_slab": "Ge Igi I<PERSON>-<PERSON>uta", "block.minecraft.cyan_banner": "<PERSON><PERSON>", "block.minecraft.cyan_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_candle": "Alawọ Ewe Bulu Abẹla", "block.minecraft.cyan_candle_cake": "Akara oyinbo pẹlu cyan abẹla", "block.minecraft.cyan_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON>-<PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON><PERSON> lul<PERSON>", "block.minecraft.cyan_glazed_terracotta": "<PERSON><PERSON>zed Terracotta", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.cyan_terracotta": "Samawati terakota", "block.minecraft.cyan_wool": "<PERSON><PERSON><PERSON> k<PERSON> irun", "block.minecraft.damaged_anvil": "Ago ti a ti bajẹ", "block.minecraft.dandelion": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_oak_button": "<PERSON><PERSON><PERSON><PERSON> lati <PERSON>u dudu", "block.minecraft.dark_oak_door": "<PERSON><PERSON> dudu enu", "block.minecraft.dark_oak_fence": "<PERSON><PERSON>u odi", "block.minecraft.dark_oak_fence_gate": "<PERSON><PERSON>u Ẹnubodè Pada", "block.minecraft.dark_oak_hanging_sign": "Dudu Oak ikele Sign", "block.minecraft.dark_oak_leaves": "<PERSON><PERSON> dudu bunkun", "block.minecraft.dark_oak_log": "<PERSON><PERSON>", "block.minecraft.dark_oak_planks": "<PERSON><PERSON> igi <PERSON> dudu", "block.minecraft.dark_oak_pressure_plate": "<PERSON><PERSON>", "block.minecraft.dark_oak_sapling": "Ororoo oaku dudu", "block.minecraft.dark_oak_sign": "<PERSON><PERSON>", "block.minecraft.dark_oak_slab": "<PERSON>u ti oṣu dudu", "block.minecraft.dark_oak_stairs": "Pẹtẹẹsì oaku dudu", "block.minecraft.dark_oak_trapdoor": "<PERSON><PERSON><PERSON><PERSON><PERSON>n ti oṣu dudu", "block.minecraft.dark_oak_wall_hanging_sign": "Dark Oak Wall ikele Sign", "block.minecraft.dark_oak_wall_sign": "<PERSON><PERSON>", "block.minecraft.dark_oak_wood": "<PERSON><PERSON> dudu igi", "block.minecraft.dark_prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON> dudu pẹlu prismarine kan", "block.minecraft.dark_prismarine_stairs": "Pẹtẹẹsì dudu", "block.minecraft.daylight_detector": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_brain_coral": "Brakú ọpọlọ Coral", "block.minecraft.dead_brain_coral_block": "<PERSON>un amorindun ti ọpọlọ ikun ti o ku", "block.minecraft.dead_brain_coral_fan": "Òkú ọgbẹ àrùn", "block.minecraft.dead_brain_coral_wall_fan": "Agbegbe odi ti o ku ti ọra iṣọn", "block.minecraft.dead_bubble_coral": "Bkú Bubble Coral", "block.minecraft.dead_bubble_coral_block": "Block ti okú nyoju iyun", "block.minecraft.dead_bubble_coral_fan": "Afẹfẹ ti o ku ti ẹja iṣan", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON><PERSON><PERSON> odi ti o ku ni adun iṣan", "block.minecraft.dead_bush": "Igi ti o gbẹ", "block.minecraft.dead_fire_coral": "Coral Fire Coral", "block.minecraft.dead_fire_coral_block": "<PERSON>un Iku Akanku Iku", "block.minecraft.dead_fire_coral_fan": "<PERSON>un apanirun ti adun ti ina", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON><PERSON><PERSON> odi ti o ku ti adun ina", "block.minecraft.dead_horn_coral": "Hornkú Ikun Coral", "block.minecraft.dead_horn_coral_block": "<PERSON><PERSON> Ok<PERSON>n Igbẹ Agbẹ", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON> a<PERSON>un ti <PERSON>ra am<PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "Aja odi ti o ku ti adiye kan", "block.minecraft.dead_tube_coral": "<PERSON><PERSON><PERSON>", "block.minecraft.dead_tube_coral_block": "Block ti coral tubular apo", "block.minecraft.dead_tube_coral_fan": "Akan ti o ku ti adiye adiro", "block.minecraft.dead_tube_coral_wall_fan": "Aja odi ti o ku ni adun apo", "block.minecraft.decorated_pot": "I<PERSON>ko ti a ṣe ọṣọ", "block.minecraft.deepslate": "Deepslate", "block.minecraft.deepslate_brick_slab": "Deepslate Brick Slab", "block.minecraft.deepslate_brick_stairs": "Deepslate biriki pẹtẹẹsì", "block.minecraft.deepslate_brick_wall": "Deepslate biriki <PERSON>", "block.minecraft.deepslate_bricks": "Deepslate Awọn biriki", "block.minecraft.deepslate_coal_ore": "Deepslate Oédú Ore", "block.minecraft.deepslate_copper_ore": "Deepsla<PERSON>", "block.minecraft.deepslate_diamond_ore": "Deepslate Diamond irin", "block.minecraft.deepslate_emerald_ore": "Deepslate Emerald Ore", "block.minecraft.deepslate_gold_ore": "<PERSON><PERSON> ohun <PERSON> wú<PERSON>", "block.minecraft.deepslate_iron_ore": "Deepslate Irin irin", "block.minecraft.deepslate_lapis_ore": "Deepslate Lapis Lazuli Ore", "block.minecraft.deepslate_redstone_ore": "Deepslate Redstone Ore", "block.minecraft.deepslate_tile_slab": "Deepslate Tile Slab", "block.minecraft.deepslate_tile_stairs": "Deepslate Tile pẹtẹẹsì", "block.minecraft.deepslate_tile_wall": "Deepslate Tile <PERSON>", "block.minecraft.deepslate_tiles": "Awọn alẹmọ Deepslate", "block.minecraft.detector_rail": "Iṣinipopada pẹlu sensọ", "block.minecraft.diamond_block": "Àkọsílẹ okuta", "block.minecraft.diamond_ore": "Okuta Ẹsọ́", "block.minecraft.diorite": "Diorite", "block.minecraft.diorite_slab": "Diorite Slab", "block.minecraft.diorite_stairs": "Diorite pẹtẹẹsì", "block.minecraft.diorite_wall": "Diorite Wall", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "<PERSON><PERSON><PERSON>", "block.minecraft.dispenser": "Atokan", "block.minecraft.dragon_egg": "Dragon Ẹyin", "block.minecraft.dragon_head": "<PERSON><PERSON>", "block.minecraft.dragon_wall_head": "Oriiran Odi Oga", "block.minecraft.dried_ghast": "<PERSON><PERSON>", "block.minecraft.dried_kelp_block": "Ti gbin Kelp Block", "block.minecraft.dripstone_block": "<PERSON><PERSON>", "block.minecraft.dropper": "Ọgbẹ", "block.minecraft.emerald_block": "Àkọsílẹ Ti Emerald", "block.minecraft.emerald_ore": "Emerald Ore", "block.minecraft.enchanting_table": "Tọpeli tabili", "block.minecraft.end_gateway": "<PERSON><PERSON><PERSON>", "block.minecraft.end_portal": "<PERSON><PERSON>", "block.minecraft.end_portal_frame": "Eto Ilana End Portal", "block.minecraft.end_rod": "Oṣiṣẹ ipari", "block.minecraft.end_stone": "<PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "Opin <PERSON>", "block.minecraft.end_stone_brick_stairs": "Opin <PERSON>", "block.minecraft.end_stone_brick_wall": "<PERSON><PERSON>", "block.minecraft.end_stone_bricks": "<PERSON><PERSON><PERSON> bi<PERSON>", "block.minecraft.ender_chest": "Ender Ọpa", "block.minecraft.exposed_chiseled_copper": "Exposed Chiseled Copper", "block.minecraft.exposed_copper": "<PERSON><PERSON><PERSON> Han", "block.minecraft.exposed_copper_bulb": "Exposed Copper Bulb", "block.minecraft.exposed_copper_door": "Exposed Copper Door", "block.minecraft.exposed_copper_grate": "Exposed Copper Grate", "block.minecraft.exposed_copper_trapdoor": "Exposed Copper Trapdoor", "block.minecraft.exposed_cut_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_cut_copper_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.exposed_cut_copper_stairs": "Ṣe a<PERSON>han awọn pẹtẹẹsì idẹ", "block.minecraft.farmland": "Aaye ti a gbin", "block.minecraft.fern": "<PERSON><PERSON><PERSON> wẹ̀wẹ̀", "block.minecraft.fire": "<PERSON><PERSON>", "block.minecraft.fire_coral": "<PERSON><PERSON>an", "block.minecraft.fire_coral_block": "Igbẹ Aṣọ Ina", "block.minecraft.fire_coral_fan": "A fọọmu ti erun igbona", "block.minecraft.fire_coral_wall_fan": "Odi ti erun ina", "block.minecraft.firefly_bush": "Firefly Bush", "block.minecraft.fletching_table": "Fletching Table", "block.minecraft.flower_pot": "<PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea": "Aladodo Azalea", "block.minecraft.flowering_azalea_leaves": "Aladodo Azalea ewé", "block.minecraft.frogspawn": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.frosted_ice": "<PERSON><PERSON> y<PERSON>yin", "block.minecraft.furnace": "<PERSON><PERSON>", "block.minecraft.gilded_blackstone": "<PERSON><PERSON> dudu dudu", "block.minecraft.glass": "<PERSON><PERSON>", "block.minecraft.glass_pane": "<PERSON><PERSON>", "block.minecraft.glow_lichen": "Glow Lichen", "block.minecraft.glowstone": "Imọlẹ okuta", "block.minecraft.gold_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_ore": "<PERSON><PERSON> irin goolu", "block.minecraft.granite": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.granite_slab": "Granite Slab", "block.minecraft.granite_stairs": "Granite Awọn atẹgun", "block.minecraft.granite_wall": "Granite arabara", "block.minecraft.grass": "<PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "<PERSON><PERSON> koriko", "block.minecraft.gravel": "Wẹwẹ", "block.minecraft.gray_banner": "Banki <PERSON>", "block.minecraft.gray_bed": "Grẹy <PERSON><PERSON><PERSON>", "block.minecraft.gray_candle": "Grẹy Abẹla", "block.minecraft.gray_candle_cake": "Akara oyinbo pẹlu grẹy abẹla", "block.minecraft.gray_carpet": "Grẹy carpeti", "block.minecraft.gray_concrete": "<PERSON> nja", "block.minecraft.gray_concrete_powder": "Grey nja lulú", "block.minecraft.gray_glazed_terracotta": "Grey Glazed Terracotta", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "Grẹy gilasi", "block.minecraft.gray_stained_glass_pane": "Grẹy gilasi", "block.minecraft.gray_terracotta": "Grẹy terakota", "block.minecraft.gray_wool": "Grẹy kìki irun", "block.minecraft.green_banner": "<PERSON>", "block.minecraft.green_bed": "Alawọ Ewe <PERSON>", "block.minecraft.green_candle": "Alawọ ewe Abẹla", "block.minecraft.green_candle_cake": "Akara oyinbo pẹlu alawọ ewe abẹla", "block.minecraft.green_carpet": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.green_concrete": "Alawọ ewe alawọ ewe", "block.minecraft.green_concrete_powder": "<PERSON><PERSON><PERSON> nja alaw<PERSON>", "block.minecraft.green_glazed_terracotta": "Alawọ ewe Glazed Terracotta", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.green_stained_glass_pane": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.green_terracotta": "Aipọn terak<PERSON>", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> i<PERSON>", "block.minecraft.grindstone": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eefun", "block.minecraft.hanging_roots": "<PERSON>w<PERSON>n <PERSON>", "block.minecraft.hay_block": "<PERSON>gi ti koriko", "block.minecraft.heavy_core": "Heavy Core", "block.minecraft.heavy_weighted_pressure_plate": "Iwọn ipilẹ ti o ni irọra pípẹ", "block.minecraft.honey_block": "Dẹkun Ẹnu", "block.minecraft.honeycomb_block": "<PERSON>a ohun amorindun", "block.minecraft.hopper": "<PERSON>", "block.minecraft.horn_coral": "Coral ti a mu", "block.minecraft.horn_coral_block": "Ti Nmu Coral Block", "block.minecraft.horn_coral_fan": "A àìpẹ ti adiye aman", "block.minecraft.horn_coral_wall_fan": "Orilẹ-ib<PERSON>n ti <PERSON>ra iṣan", "block.minecraft.ice": "<PERSON><PERSON><PERSON>", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON><PERSON><PERSON> okuta ti a fi oju ṣe", "block.minecraft.infested_cobblestone": "Ti fi kún pèpéle", "block.minecraft.infested_cracked_stone_bricks": "<PERSON><PERSON>ki okuta ti a ti fi okuta ṣubu", "block.minecraft.infested_deepslate": "Awọn ẹlẹsẹ ti o ni ipalara", "block.minecraft.infested_mossy_stone_bricks": "<PERSON>iriki ti a fi okuta pa ti a da", "block.minecraft.infested_stone": "Okuta ti a da", "block.minecraft.infested_stone_bricks": "<PERSON><PERSON>ki okuta ti a ti fi ẹ si", "block.minecraft.iron_bars": "Iron Bars", "block.minecraft.iron_block": "Iwọn Iron", "block.minecraft.iron_door": "Iron enu", "block.minecraft.iron_ore": "Iron Ẹsọ́", "block.minecraft.iron_trapdoor": "Gbigbọn i<PERSON>", "block.minecraft.jack_o_lantern": "<PERSON>'<PERSON>", "block.minecraft.jigsaw": "Jigsaw Àkọsílẹ", "block.minecraft.jukebox": "A<PERSON><PERSON> ti ndun", "block.minecraft.jungle_button": "Bọtini Jungle", "block.minecraft.jungle_door": "Jungle enu", "block.minecraft.jungle_fence": "Jungle odi", "block.minecraft.jungle_fence_gate": "Jungle Ẹnubodè Pada", "block.minecraft.jungle_hanging_sign": "Ibugbe adiye <PERSON>", "block.minecraft.jungle_leaves": "Jungle bunkun", "block.minecraft.jungle_log": "Jungle Wọle", "block.minecraft.jungle_planks": "Ile-iṣẹ igbo", "block.minecraft.jungle_pressure_plate": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.jungle_sapling": "Ororoo igboro", "block.minecraft.jungle_sign": "Ipele i<PERSON>bo", "block.minecraft.jungle_slab": "Ipele i<PERSON>bo", "block.minecraft.jungle_stairs": "Atẹgun igbo", "block.minecraft.jungle_trapdoor": "Jungle niyeon", "block.minecraft.jungle_wall_hanging_sign": "Igbo odi i<PERSON>e Sign", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON>", "block.minecraft.jungle_wood": "Jungle igi", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "Ohun ọṣọ Ẹrọ", "block.minecraft.ladder": "Akaba", "block.minecraft.lantern": "<PERSON><PERSON><PERSON>", "block.minecraft.lapis_block": "Àkọsílẹ ti <PERSON>", "block.minecraft.lapis_ore": "<PERSON><PERSON> Ẹsọ́", "block.minecraft.large_amethyst_bud": "Amethyst Egbon Nla", "block.minecraft.large_fern": "<PERSON><PERSON> e<PERSON> wẹ̀wẹ̀", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> omi", "block.minecraft.leaf_litter": "Leaf Litter", "block.minecraft.lectern": "Lectern", "block.minecraft.lever": "<PERSON><PERSON>", "block.minecraft.light": "Imọlẹ", "block.minecraft.light_blue_banner": "Imọlẹ Blue Banner", "block.minecraft.light_blue_bed": "Ibusun Bulu ti o ni ina", "block.minecraft.light_blue_candle": "Bulu Didan Abẹla", "block.minecraft.light_blue_candle_cake": "Akara oyinbo pẹlu abẹla Imọlẹ Bulu", "block.minecraft.light_blue_carpet": "Imọlẹ bulu carpeti", "block.minecraft.light_blue_concrete": "Light bulu Nkan", "block.minecraft.light_blue_concrete_powder": "Pulu lulú Light bulu", "block.minecraft.light_blue_glazed_terracotta": "Light Blue Glazed Terracotta", "block.minecraft.light_blue_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.light_blue_stained_glass": "Imọlẹ bulu gilasi", "block.minecraft.light_blue_stained_glass_pane": "Imọlẹ bulu gilasi", "block.minecraft.light_blue_terracotta": "Imọlẹ bulu terakota", "block.minecraft.light_blue_wool": "Imọlẹ bulu kìki irun", "block.minecraft.light_gray_banner": "Imọlẹ Grey Light", "block.minecraft.light_gray_bed": "<PERSON><PERSON><PERSON> Grẹy", "block.minecraft.light_gray_candle": "Bulu ina Abẹla", "block.minecraft.light_gray_candle_cake": "Akara oyinbo pẹlu Grẹy Imọlẹ abẹla", "block.minecraft.light_gray_carpet": "Imọlẹ grẹy carpeti", "block.minecraft.light_gray_concrete": "Imọlẹ Grey Light", "block.minecraft.light_gray_concrete_powder": "Ina grẹy nja lulú", "block.minecraft.light_gray_glazed_terracotta": "Light Grey Glazed Terracotta", "block.minecraft.light_gray_shulker_box": "Apoti Imọlẹ <PERSON> Shulker", "block.minecraft.light_gray_stained_glass": "Imọlẹ grẹy gilasi", "block.minecraft.light_gray_stained_glass_pane": "Imọlẹ grẹy gilasi", "block.minecraft.light_gray_terracotta": "Imọlẹ grẹy terakota", "block.minecraft.light_gray_wool": "Imọlẹ grẹy kìki irun", "block.minecraft.light_weighted_pressure_plate": "<PERSON>w<PERSON><PERSON>", "block.minecraft.lightning_rod": "<PERSON>a <PERSON>anna", "block.minecraft.lilac": "<PERSON>", "block.minecraft.lily_of_the_valley": "<PERSON> ti afonifoji", "block.minecraft.lily_pad": "<PERSON>", "block.minecraft.lime_banner": "Asia orombo", "block.minecraft.lime_bed": "Orombo Wewe", "block.minecraft.lime_candle": "Orombo Wewe Abẹla", "block.minecraft.lime_candle_cake": "Akara oyinbo pẹlu orombo abẹla", "block.minecraft.lime_carpet": "Imọlẹ carpeti", "block.minecraft.lime_concrete": "Orombo to konge", "block.minecraft.lime_concrete_powder": "Orombo wewe lulú", "block.minecraft.lime_glazed_terracotta": "Orombo wewe Glazed Terracotta", "block.minecraft.lime_shulker_box": "Orombo wewe apoti", "block.minecraft.lime_stained_glass": "Imọlẹ aip<PERSON>n gilasi", "block.minecraft.lime_stained_glass_pane": "Imọlẹ aip<PERSON>n gilasi", "block.minecraft.lime_terracotta": "Imọlẹ terakota", "block.minecraft.lime_wool": "Imọlẹ kìki irun", "block.minecraft.lodestone": "O<PERSON>a", "block.minecraft.loom": "Loom", "block.minecraft.magenta_banner": "Asia Banner", "block.minecraft.magenta_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle": "Magenta Abẹla", "block.minecraft.magenta_candle_cake": "Akara oyinbo pẹlu Magenta abẹla", "block.minecraft.magenta_carpet": "<PERSON> carpeti", "block.minecraft.magenta_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.magenta_concrete_powder": "Magenta ṣoki lulú", "block.minecraft.magenta_glazed_terracotta": "Magenta Glazed Terracotta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON> gilasi", "block.minecraft.magenta_stained_glass_pane": "Àwo-pu<PERSON><PERSON><PERSON> g<PERSON>si", "block.minecraft.magenta_terracotta": "Crimson terakota", "block.minecraft.magenta_wool": "<PERSON> k<PERSON>ki irun", "block.minecraft.magma_block": "Dẹkun Magma", "block.minecraft.mangrove_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_door": "<PERSON><PERSON><PERSON>", "block.minecraft.mangrove_fence": "Mangrove odi", "block.minecraft.mangrove_fence_gate": "Mangrove odi Gate", "block.minecraft.mangrove_hanging_sign": "Mangrove ikele Sign", "block.minecraft.mangrove_leaves": "<PERSON><PERSON><PERSON><PERSON> ewe <PERSON>", "block.minecraft.mangrove_log": "Mangrove Wọle", "block.minecraft.mangrove_planks": "Mangrove Planks", "block.minecraft.mangrove_pressure_plate": "Mangrove Ipa Awo", "block.minecraft.mangrove_propagule": "Mangrove Propagule", "block.minecraft.mangrove_roots": "Awọn gbongbo Mangrove", "block.minecraft.mangrove_sign": "Mangrove Sign", "block.minecraft.mangrove_slab": "Mangrove pẹlẹbẹ", "block.minecraft.mangrove_stairs": "Mangrove pẹtẹẹsì", "block.minecraft.mangrove_trapdoor": "Mangrove Trapdoor", "block.minecraft.mangrove_wall_hanging_sign": "Mangrove Wall ikele Sign", "block.minecraft.mangrove_wall_sign": "Mangrove Wall Sign", "block.minecraft.mangrove_wood": "Igi mangrove", "block.minecraft.medium_amethyst_bud": "<PERSON><PERSON><PERSON><PERSON>eth<PERSON>", "block.minecraft.melon": "Melon", "block.minecraft.melon_stem": "<PERSON><PERSON> T<PERSON>", "block.minecraft.moss_block": "Iboju Moss", "block.minecraft.moss_carpet": "<PERSON>", "block.minecraft.mossy_cobblestone": "A apẹrẹ paati", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON> Slab", "block.minecraft.mossy_cobblestone_stairs": "<PERSON><PERSON> Awọn atẹgun", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON> Odi", "block.minecraft.mossy_stone_brick_slab": "A apẹrẹ okuta biriki", "block.minecraft.mossy_stone_brick_stairs": "Mossy Stone Brick Stairs", "block.minecraft.mossy_stone_brick_wall": "Mossy Stone Brick Wall", "block.minecraft.mossy_stone_bricks": "A apẹrẹ okuta biriki", "block.minecraft.moving_piston": "Gbigbe pisitini", "block.minecraft.mud": "Pẹtẹpẹtẹ", "block.minecraft.mud_brick_slab": "Pẹtẹpẹtẹ Biriki pẹlẹbẹ", "block.minecraft.mud_brick_stairs": "Pẹtẹpẹtẹ biriki pẹtẹẹsì", "block.minecraft.mud_brick_wall": "Pẹtẹpẹtẹ bi<PERSON>i <PERSON>", "block.minecraft.mud_bricks": "Aw<PERSON>n biriki pẹtẹpẹtẹ", "block.minecraft.muddy_mangrove_roots": "Mu<PERSON> wá", "block.minecraft.mushroom_stem": "<PERSON><PERSON><PERSON> je", "block.minecraft.mycelium": "Mycelium", "block.minecraft.nether_brick_fence": "Nether Brick Fence", "block.minecraft.nether_brick_slab": "Nether biriki gbu", "block.minecraft.nether_brick_stairs": "<PERSON><PERSON> Brick <PERSON>", "block.minecraft.nether_brick_wall": "<PERSON><PERSON><PERSON> a<PERSON>i", "block.minecraft.nether_bricks": "<PERSON><PERSON> biriki", "block.minecraft.nether_gold_ore": "Nether Ore Gold", "block.minecraft.nether_portal": "Lapapọ Èbúté", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON>", "block.minecraft.nether_sprouts": "Awọn Sprouts Netherlands", "block.minecraft.nether_wart": "Nether Wart", "block.minecraft.nether_wart_block": "Dẹkun Nether Wart", "block.minecraft.netherite_block": "Dẹkun Netherite", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Àkọsílẹ dun", "block.minecraft.oak_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.oak_door": "<PERSON><PERSON> enu", "block.minecraft.oak_fence": "<PERSON><PERSON> odi", "block.minecraft.oak_fence_gate": "Oaku Ẹnubodè Pada", "block.minecraft.oak_hanging_sign": "Oak ikele Sign", "block.minecraft.oak_leaves": "<PERSON><PERSON> bunkun", "block.minecraft.oak_log": "<PERSON><PERSON>", "block.minecraft.oak_planks": "<PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "Atilẹyin <PERSON><PERSON><PERSON>", "block.minecraft.oak_sapling": "Ororoo oaku", "block.minecraft.oak_sign": "<PERSON><PERSON>", "block.minecraft.oak_slab": "<PERSON><PERSON> awo", "block.minecraft.oak_stairs": "Pẹtẹẹsì oke", "block.minecraft.oak_trapdoor": "<PERSON> oṣuwọn", "block.minecraft.oak_wall_hanging_sign": "Oak Wall ikele Sign", "block.minecraft.oak_wall_sign": "<PERSON><PERSON>", "block.minecraft.oak_wood": "<PERSON><PERSON> igi", "block.minecraft.observer": "Oluwoye", "block.minecraft.obsidian": "Ojuju", "block.minecraft.ochre_froglight": "<PERSON>cher Froglight", "block.minecraft.ominous_banner": "Asia Ominous", "block.minecraft.open_eyeblossom": "Open Eyeblossom", "block.minecraft.orange_banner": "Asia asia", "block.minecraft.orange_bed": "<PERSON><PERSON>", "block.minecraft.orange_candle": "Ọsan Abẹla", "block.minecraft.orange_candle_cake": "Akara oyinbo pẹlu Okan abẹla Orange", "block.minecraft.orange_carpet": "<PERSON><PERSON>", "block.minecraft.orange_concrete": "Orange Nkan", "block.minecraft.orange_concrete_powder": "Orange nipon lulú", "block.minecraft.orange_glazed_terracotta": "<PERSON><PERSON> Terracotta", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.orange_stained_glass": "<PERSON><PERSON>", "block.minecraft.orange_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.orange_terracotta": "<PERSON><PERSON>", "block.minecraft.orange_tulip": "Osan tulip", "block.minecraft.orange_wool": "<PERSON><PERSON> k<PERSON>", "block.minecraft.oxeye_daisy": "<PERSON>ṣ<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_chiseled_copper": "Oxidized Chiseled Copper", "block.minecraft.oxidized_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.oxidized_copper_bulb": "Oxidized Copper Bulb", "block.minecraft.oxidized_copper_door": "Oxidized Copper Door", "block.minecraft.oxidized_copper_grate": "Oxidized Copper Grate", "block.minecraft.oxidized_copper_trapdoor": "Oxidized Copper Trapdoor", "block.minecraft.oxidized_cut_copper": "Oxidized Ge Ejò", "block.minecraft.oxidized_cut_copper_slab": "Oxidized Ge Ejò Slab", "block.minecraft.oxidized_cut_copper_stairs": "Oxidized Ge Ejò pẹtẹẹsì", "block.minecraft.packed_ice": "Iwapọ yinyin", "block.minecraft.packed_mud": "Papọ Pẹtẹpẹtẹ", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON>", "block.minecraft.pale_moss_block": "Pale Moss Block", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON> Moss Carpet", "block.minecraft.pale_oak_button": "Pale Oak Button", "block.minecraft.pale_oak_door": "Pale Oak Door", "block.minecraft.pale_oak_fence": "Pale Oak Fence", "block.minecraft.pale_oak_fence_gate": "Pale Oak Fence Gate", "block.minecraft.pale_oak_hanging_sign": "Pale Oak Hanging Sign", "block.minecraft.pale_oak_leaves": "Pale Oak Leaves", "block.minecraft.pale_oak_log": "Pale Oak Log", "block.minecraft.pale_oak_planks": "Pale Oak Planks", "block.minecraft.pale_oak_pressure_plate": "Pale Oak Pressure Plate", "block.minecraft.pale_oak_sapling": "Pale Oak Sapling", "block.minecraft.pale_oak_sign": "Pale Oak Sign", "block.minecraft.pale_oak_slab": "Pale Oak Slab", "block.minecraft.pale_oak_stairs": "Pale Oak Stairs", "block.minecraft.pale_oak_trapdoor": "Pale Oak Trapdoor", "block.minecraft.pale_oak_wall_hanging_sign": "Pale Oak Wall Hanging Sign", "block.minecraft.pale_oak_wall_sign": "Pale Oak Wall Sign", "block.minecraft.pale_oak_wood": "Pale Oak Wood", "block.minecraft.pearlescent_froglight": "Pearlescent Froglight", "block.minecraft.peony": "Peonia", "block.minecraft.petrified_oak_slab": "<PERSON>u igi oaku ti o ti gbilẹ", "block.minecraft.piglin_head": "<PERSON><PERSON> ori", "block.minecraft.piglin_wall_head": "<PERSON><PERSON> odi ori", "block.minecraft.pink_banner": "Asia Pink", "block.minecraft.pink_bed": "Pink Ibusun", "block.minecraft.pink_candle": "Pink Abẹla", "block.minecraft.pink_candle_cake": "Akara oyinbo pẹlu pink abẹla", "block.minecraft.pink_carpet": "Òdòdó carpeti", "block.minecraft.pink_concrete": "Alawọ ewe <PERSON>", "block.minecraft.pink_concrete_powder": "Pupa fẹẹrẹ lulú", "block.minecraft.pink_glazed_terracotta": "Pink Glazed Terracotta", "block.minecraft.pink_petals": "Pink Petals", "block.minecraft.pink_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.pink_stained_glass": "Òdòdó gilasi", "block.minecraft.pink_stained_glass_pane": "Òdòdó gilasi", "block.minecraft.pink_terracotta": "Òdòdó terakota", "block.minecraft.pink_tulip": "Òdòdó tulip", "block.minecraft.pink_wool": "Òdòdó kìki irun", "block.minecraft.piston": "<PERSON><PERSON>", "block.minecraft.piston_head": "Pisitini ori", "block.minecraft.pitcher_crop": "Pitcher <PERSON>", "block.minecraft.pitcher_plant": "<PERSON>cher <PERSON>", "block.minecraft.player_head": "Olusakoso akọ", "block.minecraft.player_head.named": "%s's <PERSON><PERSON>", "block.minecraft.player_wall_head": "Ori-ori Ẹrọ-ẹrọ", "block.minecraft.podzol": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pointed_dripstone": "Spiky drip", "block.minecraft.polished_andesite": "Pólándì andesite", "block.minecraft.polished_andesite_slab": "Agbara Andesite ti a gbin", "block.minecraft.polished_andesite_stairs": "Atẹgun Andesite ti a da didan", "block.minecraft.polished_basalt": "<PERSON><PERSON><PERSON> didan", "block.minecraft.polished_blackstone": "<PERSON>uta dudu ti a ni didan", "block.minecraft.polished_blackstone_brick_slab": "<PERSON><PERSON> dudu biriki <PERSON>", "block.minecraft.polished_blackstone_brick_stairs": "<PERSON><PERSON> okuta didan biriki", "block.minecraft.polished_blackstone_brick_wall": "<PERSON><PERSON> bi<PERSON>i dudu", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON> okuta biriki <PERSON>", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON><PERSON><PERSON> dudu", "block.minecraft.polished_blackstone_pressure_plate": "<PERSON><PERSON> didan <PERSON>", "block.minecraft.polished_blackstone_slab": "<PERSON><PERSON>", "block.minecraft.polished_blackstone_stairs": "<PERSON><PERSON> okuta okuta didan dudu", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON>", "block.minecraft.polished_deepslate": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> didan", "block.minecraft.polished_deepslate_slab": "<PERSON><PERSON>", "block.minecraft.polished_deepslate_stairs": "<PERSON><PERSON><PERSON><PERSON> pẹtẹẹsẹ <PERSON><PERSON> didan", "block.minecraft.polished_deepslate_wall": "<PERSON><PERSON>", "block.minecraft.polished_diorite": "Pólándì diorite", "block.minecraft.polished_diorite_slab": "Agbara Diorite <PERSON>ed", "block.minecraft.polished_diorite_stairs": "Awọn Diorite Poli ti o ni ẹgẹ", "block.minecraft.polished_granite": "Pólándì gira<PERSON>", "block.minecraft.polished_granite_slab": "Awọn Ẹlẹdẹ Granite Slab", "block.minecraft.polished_granite_stairs": "Awọn Igunna polished Granite", "block.minecraft.polished_tuff": "Polished <PERSON>", "block.minecraft.polished_tuff_slab": "Polished <PERSON><PERSON>", "block.minecraft.polished_tuff_stairs": "Polished <PERSON><PERSON>", "block.minecraft.polished_tuff_wall": "Polished <PERSON><PERSON>", "block.minecraft.poppy": "Irugbin", "block.minecraft.potatoes": "Poteto", "block.minecraft.potted_acacia_sapling": "Igi Acacia Oro", "block.minecraft.potted_allium": "Ti mu Allium", "block.minecraft.potted_azalea_bush": "Potted Azalea", "block.minecraft.potted_azure_bluet": "Ti mu Azure Bluet", "block.minecraft.potted_bamboo": "Ose <PERSON>", "block.minecraft.potted_birch_sapling": "<PERSON><PERSON>", "block.minecraft.potted_blue_orchid": "Ti mu bulu Orchid", "block.minecraft.potted_brown_mushroom": "Ti mu olu pupa", "block.minecraft.potted_cactus": "Kaadi ti o ti gbe", "block.minecraft.potted_cherry_sapling": "<PERSON><PERSON> ṣẹẹri Oro", "block.minecraft.potted_closed_eyeblossom": "Potted Closed Eyeblossom", "block.minecraft.potted_cornflower": "Potted Cornflower", "block.minecraft.potted_crimson_fungus": "Potted Crimson Fungus", "block.minecraft.potted_crimson_roots": "Gbongbo Pupa ni Ikoko", "block.minecraft.potted_dandelion": "Ti mu Dandelion", "block.minecraft.potted_dark_oak_sapling": "<PERSON><PERSON>", "block.minecraft.potted_dead_bush": "<PERSON><PERSON> Igbẹ Pupa", "block.minecraft.potted_fern": "Ti mu ewedò wẹ̀wẹ̀", "block.minecraft.potted_flowering_azalea_bush": "Potoko Aladodo Azalea", "block.minecraft.potted_jungle_sapling": "<PERSON>gi <PERSON>", "block.minecraft.potted_lily_of_the_valley": "<PERSON> Potted ti afonifoji", "block.minecraft.potted_mangrove_propagule": "Potted Mangrove Propagule", "block.minecraft.potted_oak_sapling": "<PERSON><PERSON>", "block.minecraft.potted_open_eyeblossom": "Potted Open Eyeblossom", "block.minecraft.potted_orange_tulip": "<PERSON>i mu <PERSON><PERSON>", "block.minecraft.potted_oxeye_daisy": "Ti mu Oxeye Daisy", "block.minecraft.potted_pale_oak_sapling": "Potted Pale Oak Sapling", "block.minecraft.potted_pink_tulip": "Ti mu <PERSON>", "block.minecraft.potted_poppy": "Ti mu Pop<PERSON>", "block.minecraft.potted_red_mushroom": "Ti mu olu pupa", "block.minecraft.potted_red_tulip": "Ti mu <PERSON>", "block.minecraft.potted_spruce_sapling": "<PERSON>gi <PERSON>", "block.minecraft.potted_torchflower": "Potted Torchflower", "block.minecraft.potted_warped_fungus": "Potted Warung Fungus", "block.minecraft.potted_warped_roots": "Awọn gbongbo ti a fọ", "block.minecraft.potted_white_tulip": "Ti mu funfun <PERSON>", "block.minecraft.potted_wither_rose": "We<PERSON> <PERSON><PERSON>", "block.minecraft.powder_snow": "Powder Snow", "block.minecraft.powder_snow_cauldron": "<PERSON><PERSON><PERSON> Cauldron", "block.minecraft.powered_rail": "<PERSON><PERSON><PERSON>", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "Brick awo pẹlu prismarine", "block.minecraft.prismarine_brick_stairs": "Prismarine brick atẹgun", "block.minecraft.prismarine_bricks": "<PERSON><PERSON><PERSON><PERSON> ohun amorindun Prismarine", "block.minecraft.prismarine_slab": "Plate pẹlu prismarine", "block.minecraft.prismarine_stairs": "Prismarine atẹgun", "block.minecraft.prismarine_wall": "<PERSON><PERSON> P<PERSON>arine", "block.minecraft.pumpkin": "Elegede", "block.minecraft.pumpkin_stem": "Elegede tita", "block.minecraft.purple_banner": "Asia asia", "block.minecraft.purple_bed": "<PERSON><PERSON><PERSON> ti", "block.minecraft.purple_candle": "<PERSON><PERSON>i ti Abẹla", "block.minecraft.purple_candle_cake": "<PERSON><PERSON> oyinbo pẹlu Eleyi ti abẹla", "block.minecraft.purple_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "P<PERSON>lu konge lulú", "block.minecraft.purple_glazed_terracotta": "Eleyi ti Glazed Terracotta", "block.minecraft.purple_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.purple_stained_glass": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.purple_terracotta": "Àluko terakota", "block.minecraft.purple_wool": "<PERSON><PERSON><PERSON> k<PERSON> i<PERSON>", "block.minecraft.purpur_block": "Purpur Block", "block.minecraft.purpur_pillar": "Purpur Pillar", "block.minecraft.purpur_slab": "elese pẹlẹbẹ", "block.minecraft.purpur_stairs": "Awọn pẹtẹẹsẹ Purpur", "block.minecraft.quartz_block": "Block Of Quartz", "block.minecraft.quartz_bricks": "<PERSON>wọ<PERSON> bi<PERSON>", "block.minecraft.quartz_pillar": "Ẹrọ Quartz", "block.minecraft.quartz_slab": "Kuotisi gbu", "block.minecraft.quartz_stairs": "Quartet Awọn atẹgun", "block.minecraft.rail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.raw_copper_block": "Àkọsílẹ ti <PERSON>", "block.minecraft.raw_gold_block": "Àkọsílẹ ti Raw Raw", "block.minecraft.raw_iron_block": "Àkọsílẹ ti Raw <PERSON>", "block.minecraft.red_banner": "Asia Pupa", "block.minecraft.red_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.red_candle": "Pupa Abẹla", "block.minecraft.red_candle_cake": "Akara oyinbo pẹlu pupa abẹla", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON>i", "block.minecraft.red_concrete": "<PERSON><PERSON>", "block.minecraft.red_concrete_powder": "Pupa ṣoki lulú", "block.minecraft.red_glazed_terracotta": "Red Glazed Terracotta", "block.minecraft.red_mushroom": "<PERSON><PERSON> pupa", "block.minecraft.red_mushroom_block": "Iwe-iṣiro pupa mycelium", "block.minecraft.red_nether_brick_slab": "Red Nether Brick Slab", "block.minecraft.red_nether_brick_stairs": "Nether Brick Stairs", "block.minecraft.red_nether_brick_wall": "Red Nether Brick Wall", "block.minecraft.red_nether_bricks": "<PERSON><PERSON><PERSON> pupa", "block.minecraft.red_sand": "<PERSON><PERSON><PERSON> pupa", "block.minecraft.red_sandstone": "<PERSON><PERSON><PERSON>", "block.minecraft.red_sandstone_slab": "Pupa apẹrẹ gusu", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON>a i<PERSON>rino<PERSON> atẹgun", "block.minecraft.red_sandstone_wall": "Odi Pupa Sandstone", "block.minecraft.red_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.red_stained_glass": "<PERSON><PERSON><PERSON> gila<PERSON>", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON><PERSON> gila<PERSON>", "block.minecraft.red_terracotta": "<PERSON><PERSON><PERSON> te<PERSON>", "block.minecraft.red_tulip": "Pupa tulip", "block.minecraft.red_wool": "<PERSON><PERSON><PERSON> k<PERSON> irun", "block.minecraft.redstone_block": "Àkọsílẹ Ti Redstone", "block.minecraft.redstone_lamp": "Redstone Atupa", "block.minecraft.redstone_ore": "Redstone irin", "block.minecraft.redstone_torch": "Redstone ògùṣọ", "block.minecraft.redstone_wall_torch": "Tèsi ogiri ti a fi okuta pupa ṣe", "block.minecraft.redstone_wire": "Redstone irin", "block.minecraft.reinforced_deepslate": "Fikun <PERSON>", "block.minecraft.repeater": "Atunṣe Redstone", "block.minecraft.repeating_command_block": "Tun <PERSON> Iṣẹ tun ṣe", "block.minecraft.resin_block": "Block of Resin", "block.minecraft.resin_brick_slab": "Resin Brick Slab", "block.minecraft.resin_brick_stairs": "Resin Brick Stairs", "block.minecraft.resin_brick_wall": "Resin Brick Wall", "block.minecraft.resin_bricks": "Resin Bricks", "block.minecraft.resin_clump": "<PERSON><PERSON>", "block.minecraft.respawn_anchor": "<PERSON><PERSON><PERSON> respawn", "block.minecraft.rooted_dirt": "Dirt fidimule", "block.minecraft.rose_bush": "Abo igbo", "block.minecraft.sand": "<PERSON><PERSON><PERSON>", "block.minecraft.sandstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.sandstone_slab": "Apẹrẹ gusu", "block.minecraft.sandstone_stairs": "Iyanrinokuta atẹgun", "block.minecraft.sandstone_wall": "Odi Sandstone", "block.minecraft.scaffolding": "Scaffolding", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "ayase sculk", "block.minecraft.sculk_sensor": "Sculk Sensọ", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk_vein": "<PERSON><PERSON><PERSON>", "block.minecraft.sea_lantern": "<PERSON><PERSON>", "block.minecraft.sea_pickle": "<PERSON><PERSON><PERSON>", "block.minecraft.seagrass": "Seagrass", "block.minecraft.set_spawn": "Ṣeto ipo ti a tunṣe", "block.minecraft.short_dry_grass": "Short Dry Grass", "block.minecraft.short_grass": "Short Grass", "block.minecraft.shroomlight": "Itankalẹ yara", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "A<PERSON>bon timole", "block.minecraft.skeleton_wall_skull": "<PERSON><PERSON>-<PERSON><PERSON>-ọpẹ", "block.minecraft.slime_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.small_amethyst_bud": "Bud Amethyst Egbon", "block.minecraft.small_dripleaf": "<PERSON><PERSON>", "block.minecraft.smithing_table": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smoker": "Smoker", "block.minecraft.smooth_basalt": "<PERSON>", "block.minecraft.smooth_quartz": "Dẹkun Quartz Dẹkun", "block.minecraft.smooth_quartz_slab": "Smooth Quartz Slab", "block.minecraft.smooth_quartz_stairs": "Smooth Quartz Awọn atẹgun", "block.minecraft.smooth_red_sandstone": "Dudu pupa gusu", "block.minecraft.smooth_red_sandstone_slab": "Awọn Ọgbọn Ikun Iyanrin pupa", "block.minecraft.smooth_red_sandstone_stairs": "Awọn Irẹrin Sandstone pẹtẹpẹtẹ", "block.minecraft.smooth_sandstone": "<PERSON><PERSON> gusu", "block.minecraft.smooth_sandstone_slab": "<PERSON><PERSON> gusu", "block.minecraft.smooth_sandstone_stairs": "Awọn I<PERSON> Awọn Irẹrin", "block.minecraft.smooth_stone": "<PERSON><PERSON>", "block.minecraft.smooth_stone_slab": "Awọn Ikọpọ Ọgbọn Ẹwa", "block.minecraft.sniffer_egg": "Sniffer ẹyin", "block.minecraft.snow": "Egbon", "block.minecraft.snow_block": "<PERSON><PERSON><PERSON> apẹrẹ", "block.minecraft.soul_campfire": "Nipa ina ti ẹmi", "block.minecraft.soul_fire": "Ọrun Iná", "block.minecraft.soul_lantern": "<PERSON><PERSON>", "block.minecraft.soul_sand": "<PERSON><PERSON><PERSON> ti <PERSON>", "block.minecraft.soul_soil": "Ọkàn", "block.minecraft.soul_torch": "Ọkàn Torch", "block.minecraft.soul_wall_torch": "Ọkàn odi chgùṣọ", "block.minecraft.spawn.not_valid": "O ko ni ibusun ile tabi oran-iṣẹ ti a tunṣe pada, tabi o ti ni idiwọ", "block.minecraft.spawner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spawner.desc1": "Ṣe ajọṣepọ pẹlu Ẹyin Spawn:", "block.minecraft.spawner.desc2": "Ṣeto Agbajo eniyan <PERSON>ru", "block.minecraft.sponge": "Kanrin<PERSON>", "block.minecraft.spore_blossom": "Spore Iruwe", "block.minecraft.spruce_button": "<PERSON><PERSON><PERSON>i Spruce", "block.minecraft.spruce_door": "Spruce enu", "block.minecraft.spruce_fence": "Spruce odi", "block.minecraft.spruce_fence_gate": "Spruce Ẹnubodè Pada", "block.minecraft.spruce_hanging_sign": "Spruce ikele Sign", "block.minecraft.spruce_leaves": "Spruce bunkun", "block.minecraft.spruce_log": "Spruce wọle", "block.minecraft.spruce_planks": "Ọkọ abẹrẹ", "block.minecraft.spruce_pressure_plate": "Iwọn Spruce Ipaju", "block.minecraft.spruce_sapling": "Ororoo dara", "block.minecraft.spruce_sign": "Egungun Spruce", "block.minecraft.spruce_slab": "Egungun Spruce", "block.minecraft.spruce_stairs": "Gbe atẹgun spruce", "block.minecraft.spruce_trapdoor": "Spruce Oṣuwọn", "block.minecraft.spruce_wall_hanging_sign": "Spruce Wall ikele Sign", "block.minecraft.spruce_wall_sign": "Spruce Wall Sign", "block.minecraft.spruce_wood": "Spruce igi", "block.minecraft.sticky_piston": "<PERSON><PERSON> al<PERSON>", "block.minecraft.stone": "<PERSON><PERSON>", "block.minecraft.stone_brick_slab": "<PERSON> awo okuta", "block.minecraft.stone_brick_stairs": "Okuta Brick Awọn atẹsẹ", "block.minecraft.stone_brick_wall": "Okuta Brick Stone", "block.minecraft.stone_bricks": "<PERSON><PERSON> biriki", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON> atilẹyin Itoju", "block.minecraft.stone_slab": "<PERSON><PERSON> awo", "block.minecraft.stone_stairs": "<PERSON><PERSON>", "block.minecraft.stonecutter": "Ṣiṣẹ Stone", "block.minecraft.stripped_acacia_log": "Pa Acacia <PERSON>", "block.minecraft.stripped_acacia_wood": "Igi A<PERSON> ti gbin", "block.minecraft.stripped_bamboo_block": "Àkọsílẹ ti ṣi kuro <PERSON>o", "block.minecraft.stripped_birch_log": "Ti pa Spruce Wọle", "block.minecraft.stripped_birch_wood": "<PERSON><PERSON> ti a lu", "block.minecraft.stripped_cherry_log": "Ti pa Àg<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_wood": "<PERSON><PERSON> ti a lu", "block.minecraft.stripped_crimson_hyphae": "<PERSON><PERSON><PERSON> Hyphae", "block.minecraft.stripped_crimson_stem": "Kọlu Crimson Stem", "block.minecraft.stripped_dark_oak_log": "<PERSON><PERSON><PERSON>u Wọle ti Dudu", "block.minecraft.stripped_dark_oak_wood": "<PERSON><PERSON><PERSON>u Wọle ti Dudu", "block.minecraft.stripped_jungle_log": "Ti pa Spruce Wọle", "block.minecraft.stripped_jungle_wood": "<PERSON>gi Igi ti a gbin", "block.minecraft.stripped_mangrove_log": "<PERSON> bọ Mangrove Wọle", "block.minecraft.stripped_mangrove_wood": "<PERSON> bọ Mangrove Wood", "block.minecraft.stripped_oak_log": "Ti pa <PERSON>a <PERSON>", "block.minecraft.stripped_oak_wood": "<PERSON><PERSON> ti a gbin", "block.minecraft.stripped_pale_oak_log": "Stripped Pale Oak Log", "block.minecraft.stripped_pale_oak_wood": "Stripped Pale Oak Wood", "block.minecraft.stripped_spruce_log": "Ti pa Spruce Wọle", "block.minecraft.stripped_spruce_wood": "<PERSON>gi Igi ti a lu", "block.minecraft.stripped_warped_hyphae": "<PERSON><PERSON><PERSON> Hyphae ti bajẹ", "block.minecraft.stripped_warped_stem": "<PERSON>g<PERSON><PERSON> Steped ti a ni nkan", "block.minecraft.structure_block": "Dẹkun igbekale", "block.minecraft.structure_void": "Ko si be", "block.minecraft.sugar_cane": "<PERSON><PERSON>", "block.minecraft.sunflower": "Oorun-ododo", "block.minecraft.suspicious_gravel": "<PERSON><PERSON>", "block.minecraft.suspicious_sand": "<PERSON><PERSON><PERSON> i<PERSON>", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON>", "block.minecraft.tall_dry_grass": "Tall Dry Grass", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.tall_seagrass": "Ga Seagrass", "block.minecraft.target": "Ibi-afẹde", "block.minecraft.terracotta": "Terakota", "block.minecraft.test_block": "Test Block", "block.minecraft.test_instance_block": "Test Instance Block", "block.minecraft.tinted_glass": "<PERSON><PERSON>", "block.minecraft.tnt": "Ìmúdàgba", "block.minecraft.tnt.disabled": "TNT explosions are disabled", "block.minecraft.torch": "Ògùṣọ", "block.minecraft.torchflower": "Ògùṣọ Flower", "block.minecraft.torchflower_crop": "Ògùṣọ Flower irugbin na", "block.minecraft.trapped_chest": "Àyà pakute", "block.minecraft.trial_spawner": "Trial Spawner", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "Tripwire <PERSON><PERSON>", "block.minecraft.tube_coral": "Coral Tube", "block.minecraft.tube_coral_block": "Akara Ideri <PERSON>", "block.minecraft.tube_coral_fan": "A àìpẹ ti coral tubular", "block.minecraft.tube_coral_wall_fan": "Orilẹ-ibẹrẹ oniṣan ti adan apo", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Tuff Brick Slab", "block.minecraft.tuff_brick_stairs": "Tuff Brick Stairs", "block.minecraft.tuff_brick_wall": "Tuff Brick Wall", "block.minecraft.tuff_bricks": "<PERSON>ff Bricks", "block.minecraft.tuff_slab": "<PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON> St<PERSON>s", "block.minecraft.tuff_wall": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "<PERSON>japa ẹyin", "block.minecraft.twisting_vines": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.twisting_vines_plant": "<PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.vault": "<PERSON><PERSON><PERSON>", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.vine": "<PERSON><PERSON><PERSON>", "block.minecraft.void_air": "Odo Air", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON> odi", "block.minecraft.warped_button": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_door": "<PERSON><PERSON><PERSON> Warped", "block.minecraft.warped_fence": "Ogun ti <PERSON>di", "block.minecraft.warped_fence_gate": "Ẹnu ọ̀nà Fence", "block.minecraft.warped_fungus": "<PERSON><PERSON>", "block.minecraft.warped_hanging_sign": "Àmì Ìsokọ́ Òkè", "block.minecraft.warped_hyphae": "Hyphae ti <PERSON>", "block.minecraft.warped_nylium": "Nylium ti ogun", "block.minecraft.warped_planks": "Planks Planed", "block.minecraft.warped_pressure_plate": "Plateau Ipa Ipa", "block.minecraft.warped_roots": "Ogun Roots", "block.minecraft.warped_sign": "<PERSON><PERSON>", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON> ogun", "block.minecraft.warped_stairs": "Awọn pẹtẹẹsì ogun", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_trapdoor": "Trapdoor ti Ogun", "block.minecraft.warped_wall_hanging_sign": "Àmì Ìsokọ́ Ògiri", "block.minecraft.warped_wall_sign": "<PERSON><PERSON> Warped", "block.minecraft.warped_wart_block": "Window Dena <PERSON>", "block.minecraft.water": "<PERSON><PERSON>", "block.minecraft.water_cauldron": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> omi", "block.minecraft.waxed_chiseled_copper": "Waxed Chiseled Copper", "block.minecraft.waxed_copper_block": "Epo-<PERSON><PERSON> ti <PERSON>ò", "block.minecraft.waxed_copper_bulb": "Waxed Copper Bulb", "block.minecraft.waxed_copper_door": "Waxed Copper Door", "block.minecraft.waxed_copper_grate": "Waxed Copper Grate", "block.minecraft.waxed_copper_trapdoor": "Waxed Copper Trapdoor", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_cut_copper_slab": "Waxed <PERSON><PERSON> slab", "block.minecraft.waxed_cut_copper_stairs": "Waxed <PERSON><PERSON> Ejò pẹtẹẹsì", "block.minecraft.waxed_exposed_chiseled_copper": "Waxed Exposed Chiseled Copper", "block.minecraft.waxed_exposed_copper": "<PERSON><PERSON><PERSON> Han", "block.minecraft.waxed_exposed_copper_bulb": "Waxed Exposed Copper Bulb", "block.minecraft.waxed_exposed_copper_door": "Waxed Exposed Copper Door", "block.minecraft.waxed_exposed_copper_grate": "Waxed Exposed Copper Grate", "block.minecraft.waxed_exposed_copper_trapdoor": "Waxed Exposed Copper Trapdoor", "block.minecraft.waxed_exposed_cut_copper": "Waxed Exposed Cut Copper", "block.minecraft.waxed_exposed_cut_copper_slab": "Waxed <PERSON><PERSON> Ge E<PERSON>", "block.minecraft.waxed_exposed_cut_copper_stairs": "Waxed fara han <PERSON>jò pẹtẹẹsì", "block.minecraft.waxed_oxidized_chiseled_copper": "Waxed Oxidized Chiseled Copper", "block.minecraft.waxed_oxidized_copper": "Ejò Oxidized Waxed", "block.minecraft.waxed_oxidized_copper_bulb": "Waxed Oxidized Copper Bulb", "block.minecraft.waxed_oxidized_copper_door": "Waxed Oxidized Copper Door", "block.minecraft.waxed_oxidized_copper_grate": "Waxed Oxidized Copper Grate", "block.minecraft.waxed_oxidized_copper_trapdoor": "Waxed Oxidized Copper Trapdoor", "block.minecraft.waxed_oxidized_cut_copper": "Waxed Oxidized Ge Ejò", "block.minecraft.waxed_oxidized_cut_copper_slab": "Waxed Oxidized Ge Ejò Slab", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Waxed Oxidized Ge Ejò pẹtẹẹsì", "block.minecraft.waxed_weathered_chiseled_copper": "Waxed Weathered Chiseled Copper", "block.minecraft.waxed_weathered_copper": "<PERSON><PERSON><PERSON> oju-ojo ti a fi epo-eti ṣe", "block.minecraft.waxed_weathered_copper_bulb": "Waxed Weathered Copper Bulb", "block.minecraft.waxed_weathered_copper_door": "Waxed Weathered Copper Door", "block.minecraft.waxed_weathered_copper_grate": "Waxed Weathered Copper Grate", "block.minecraft.waxed_weathered_copper_trapdoor": "Waxed Weathered Copper Trapdoor", "block.minecraft.waxed_weathered_cut_copper": "Waxed <PERSON>ed <PERSON><PERSON>", "block.minecraft.waxed_weathered_cut_copper_slab": "Waxed Weathered <PERSON><PERSON> E<PERSON>", "block.minecraft.waxed_weathered_cut_copper_stairs": "Waxed Weathered Ge Ejò pẹtẹẹsì", "block.minecraft.weathered_chiseled_copper": "Weathered Chiseled Copper", "block.minecraft.weathered_copper": "<PERSON><PERSON><PERSON>", "block.minecraft.weathered_copper_bulb": "Weathered Copper Bulb", "block.minecraft.weathered_copper_door": "Weathered Copper Door", "block.minecraft.weathered_copper_grate": "Weathered Copper Grate", "block.minecraft.weathered_copper_trapdoor": "Weathered Copper Trapdoor", "block.minecraft.weathered_cut_copper": "<PERSON><PERSON>", "block.minecraft.weathered_cut_copper_slab": "Weathered <PERSON><PERSON>", "block.minecraft.weathered_cut_copper_stairs": "Weathered Ge Ejò pẹtẹẹsì", "block.minecraft.weeping_vines": "Ẹkun <PERSON><PERSON>a", "block.minecraft.weeping_vines_plant": "<PERSON><PERSON>", "block.minecraft.wet_sponge": "<PERSON><PERSON> kan<PERSON>", "block.minecraft.wheat": "<PERSON><PERSON><PERSON> igi", "block.minecraft.white_banner": "Banner <PERSON>", "block.minecraft.white_bed": "Funfu<PERSON>", "block.minecraft.white_candle": "Funfun Abẹla", "block.minecraft.white_candle_cake": "Akara oyinbo pẹlu funfun abẹla", "block.minecraft.white_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.white_concrete": "Nkan funfun", "block.minecraft.white_concrete_powder": "Funfun ṣoki ti funfun", "block.minecraft.white_glazed_terracotta": "Funfun Glazed Terracotta", "block.minecraft.white_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON> gilasi", "block.minecraft.white_stained_glass_pane": "<PERSON><PERSON><PERSON> gilasi", "block.minecraft.white_terracotta": "Funfun teracota", "block.minecraft.white_tulip": "Funfun tulip", "block.minecraft.white_wool": "Funfun k<PERSON>ki irun", "block.minecraft.wildflowers": "Wildflowers", "block.minecraft.wither_rose": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Wither egun<PERSON> timole", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON>-<PERSON><PERSON>", "block.minecraft.yellow_banner": "Asia Yellow", "block.minecraft.yellow_bed": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_candle": "Ofeefee Abẹla", "block.minecraft.yellow_candle_cake": "Akara oyinbo pẹlu Ofeefee abẹla", "block.minecraft.yellow_carpet": "<PERSON><PERSON><PERSON> <PERSON>i", "block.minecraft.yellow_concrete": "<PERSON><PERSON> alaw<PERSON>", "block.minecraft.yellow_concrete_powder": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_glazed_terracotta": "Yellow Glazed Terracotta", "block.minecraft.yellow_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.yellow_stained_glass_pane": "<PERSON><PERSON><PERSON> g<PERSON>", "block.minecraft.yellow_terracotta": "Iyeye terakota", "block.minecraft.yellow_wool": "Iyeye kìki irun", "block.minecraft.zombie_head": "Zombie ori", "block.minecraft.zombie_wall_head": "<PERSON>ile-ori <PERSON>", "book.byAuthor": "nipa %1$s", "book.edit.title": "Book Edit Screen", "book.editTitle": "Tẹ akọle ti iwe naa:", "book.finalizeButton": "Wole ati sunm<PERSON>", "book.finalizeWarning": "Išọra! Lẹhin ti o ba wọle si iwe, iwọ kii yoo ni anfani lati ṣatunkọ rẹ.", "book.generation.0": "Atilẹba", "book.generation.1": "Ẹda ti atilẹba", "book.generation.2": "Daakọ ẹda naa", "book.generation.3": "Shred", "book.invalid.tag": "* Ti ko tọ ami iwe *", "book.pageIndicator": "Iwe %1$s lati %2$s", "book.page_button.next": "Next Page", "book.page_button.previous": "Previous Page", "book.sign.title": "Book Sign Screen", "book.sign.titlebox": "Title", "book.signButton": "Fi orukọ silẹ", "book.view.title": "Book View Screen", "build.tooHigh": "Ifilelẹ giga fun ile jẹ %s", "chat.cannotSend": "Ko le firanṣẹ ifiranṣẹ iwiregbe", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Tẹ lati teleport", "chat.copy": "Daak<PERSON> si Iwe itẹẹrẹ", "chat.copy.click": "Tẹ lati Daak<PERSON> sori <PERSON>kuru", "chat.deleted_marker": "Ifiranṣẹ iwiregbe yii ti paarẹ nipasẹ olupin naa.", "chat.disabled.chain_broken": "Iwiregbe alaabo nitori pq baje. Jọwọ gbiyanju atunsopọ.", "chat.disabled.expiredProfileKey": "Iwiregbe ni alaabo nitori bọtini gbangba profaili ti pari. Jọwọ gbiyanju atunsopọ.", "chat.disabled.invalid_command_signature": "Command had unexpected or missing command argument signatures.", "chat.disabled.invalid_signature": "<PERSON><PERSON> had an invalid signature. Please try reconnecting.", "chat.disabled.launcher": "Iwiregbe alaabo nipasẹ aṣayan ifilọlẹ. Ko le fi ifiranṣẹ ranṣẹ.", "chat.disabled.missingProfileKey": "Iwiregbe ni alaabo nitori bọtini profaili ti o padanu. Jọwọ gbiyanju atunsopọ.", "chat.disabled.options": "Iwiregbe alaabo ninu eto awọn a<PERSON>.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> received out-of-order. Did your system time change?", "chat.disabled.profile": "Iwiregbe ko gba laaye nipasẹ awọn eto akọọlẹ. Tẹ '%s' lẹẹkansi fun alaye diẹ sii.", "chat.disabled.profile.moreInfo": "Iwiregbe awọn eto iroyin. Ko le firanṣẹ tabi wo awọn ifiranṣẹ.", "chat.editBox": "Iwiregbe", "chat.filtered": "Filter nipa olupin.", "chat.filtered_full": "Awọn olupin ti pamọ ifiranṣẹ rẹ fun diẹ ninu awọn ẹrọ orin.", "chat.link.confirm": "Ṣe o da ọ loju pe o fẹ ṣii aaye ayelujara yii?", "chat.link.confirmTrusted": "Ṣe o fẹ ṣii ọna asopọ yii tabi daakọ si apẹrẹ igbasilẹ rẹ?", "chat.link.open": "Ṣi ni <PERSON><PERSON>usa", "chat.link.warning": "Ma ṣe ṣi awọn asopọ lati awọn eniyan ti iwọ ko gbẹkẹle!", "chat.queue": "[+%s laini isunm<PERSON><PERSON>i ni]", "chat.square_brackets": "[%s]", "chat.tag.error": "Server sent invalid message.", "chat.tag.modified": "Ifiranṣẹ ti yipada nipasẹ olupin. Atilẹba:", "chat.tag.not_secure": "Ifiranṣẹ ti a ko rii daju. Ko le ṣe ijabọ.", "chat.tag.system": "Ifiranṣẹ olupin. Ko le ṣe ijab<PERSON>.", "chat.tag.system_single_player": "Ifiranṣẹ olupin.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s ti pari ipenija %s", "chat.type.advancement.goal": "%s ti de ibi idojukọ %s", "chat.type.advancement.task": "%s ti ṣe ilosiwaju %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "Išẹ ifiranṣẹ", "chat.type.team.sent": "-> %s <%s> %s", "chat.type.team.text": "%s <%s> %s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s sọ %s", "chat.validation_error": "Chat validation error", "chat_screen.message": "Ifiranṣẹ lati firanṣẹ: %s", "chat_screen.title": "<PERSON><PERSON><PERSON>", "chat_screen.usage": "Ifiranṣẹ sii ki o tẹ Tẹ lati firanṣẹ", "chunk.toast.checkLog": "See log for more details", "chunk.toast.loadFailure": "Failed to load chunk at %s", "chunk.toast.lowDiskSpace": "Low disk space!", "chunk.toast.lowDiskSpace.description": "Might not be able to save the world.", "chunk.toast.saveFailure": "Failed to save chunk at %s", "clear.failed.multiple": "Ko si awọn ohun kan ti a rii lori awọn oṣere %s", "clear.failed.single": "Ko si awọn ohun kan ti a rii lori ẹrọ orin %s", "color.minecraft.black": "<PERSON><PERSON>", "color.minecraft.blue": "Búlù", "color.minecraft.brown": "<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "Grẹy", "color.minecraft.green": "<PERSON><PERSON> ewe", "color.minecraft.light_blue": "Imọlẹ Bulu", "color.minecraft.light_gray": "Grẹy Imọlẹ", "color.minecraft.lime": "Orombo wewe", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Ọsan", "color.minecraft.pink": "Pink", "color.minecraft.purple": "El<PERSON>i ti", "color.minecraft.red": "<PERSON><PERSON><PERSON>", "color.minecraft.white": "Funfun", "color.minecraft.yellow": "Ofeefee", "command.context.here": "<--[HERE]", "command.context.parse_error": "%s ni ipo %s: %s", "command.exception": "Kò le ṣàyẹ̀wò àṣẹ: %s", "command.expected.separator": "<PERSON><PERSON>e aaye ti a nireti lati pari ariya<PERSON>yan kan, ṣugbọn ri data itọpa", "command.failed": "Aṣiṣe airotẹlẹ kan ṣẹlẹ lati gbiyanju lati ṣiṣẹ aṣẹ yẹn", "command.forkLimit": "Maximum number of contexts (%s) reached", "command.unknown.argument": "Ti ko tọ ariyanjiyan fun pipaṣẹ", "command.unknown.command": "Aimọ tabi aṣẹ ti ko pe, wo isalẹ fun aṣiṣe", "commands.advancement.criterionNotFound": "Ilọsiwaju %1$s ko ni ami -ami ' %2$s'", "commands.advancement.grant.criterion.to.many.failure": "Ko le fun ami -ami ' %s' ti ilosiwaju %s si awọn oṣere %s bi wọn ti ni tẹlẹ", "commands.advancement.grant.criterion.to.many.success": "Idiyele ti a fun ni ' %s' ti ilosiwaju %s si awọn oṣere %s", "commands.advancement.grant.criterion.to.one.failure": "Ko le fun ami -ami ' %s' ti ilosiwaju %s si %s bi wọn ti ni tẹlẹ", "commands.advancement.grant.criterion.to.one.success": "I<PERSON><PERSON>le ti a fun ni ' %s' ti ilosiwaju %s si %s", "commands.advancement.grant.many.to.many.failure": "Ko le fun awọn ilosiwaju %s si awọn oṣere %s bi wọn ti ni wọn tẹlẹ", "commands.advancement.grant.many.to.many.success": "Ti fun ni ilosiwaju %s si awọn oṣere %s", "commands.advancement.grant.many.to.one.failure": "Ko le fun awọn ilosiwaju %s si %s bi wọn ti ni wọn tẹlẹ", "commands.advancement.grant.many.to.one.success": "Ti fun ni ilosiwaju %s si %s", "commands.advancement.grant.one.to.many.failure": "Ko le funni ni ilosiwaju %s si awọn oṣere %s bi wọn ti ni tẹlẹ", "commands.advancement.grant.one.to.many.success": "Ti fun ilosiwaju %s si awọn oṣere %s", "commands.advancement.grant.one.to.one.failure": "Ko le fun ilosiwaju %s si %s bi wọn ti ni tẹlẹ", "commands.advancement.grant.one.to.one.success": "Ti fun ilosiwaju %s si %s", "commands.advancement.revoke.criterion.to.many.failure": "Ko le fagilee ' %s' ti ilosiwaju %s lati awọn oṣere %s nitori wọn ko ni", "commands.advancement.revoke.criterion.to.many.success": "Iyatọ ti o fagile ' %s' ti ilosiwaju %s lati awọn oṣere %s", "commands.advancement.revoke.criterion.to.one.failure": "Ko le fagilee ' %s' ti ilosiwaju %s lati %s nitori wọn ko ni", "commands.advancement.revoke.criterion.to.one.success": "Ifagilee ifagile ' %s' ti ilosiwaju %s lati %s", "commands.advancement.revoke.many.to.many.failure": "Ko le fagilee ilosiwaju %s lati ọdọ awọn oṣere %s nitori wọn ko ni wọn", "commands.advancement.revoke.many.to.many.success": "Awọn ilọsiwaju %s fagilee lati awọn oṣere %s", "commands.advancement.revoke.many.to.one.failure": "Ko le fagilee ilosiwaju %s lati %s nitori wọn ko ni wọn", "commands.advancement.revoke.many.to.one.success": "Fagilee ilosiwaju %s lati %s", "commands.advancement.revoke.one.to.many.failure": "Ko le fagile ilosiwaju %s lati ọdọ awọn oṣere %s nitori wọn ko ni", "commands.advancement.revoke.one.to.many.success": "Fagile ilosiwaju %s lati awọn oṣere %s", "commands.advancement.revoke.one.to.one.failure": "Ko le fagile ilosiwaju %s lati %s nitori wọn ko ni", "commands.advancement.revoke.one.to.one.success": "Fagile ilosiwaju %s lati %s", "commands.attribute.base_value.get.success": "Iye ipilẹ ti ẹbun %s fun nkan %s jẹ %s", "commands.attribute.base_value.reset.success": "Base value for attribute %s for entity %s reset to default %s", "commands.attribute.base_value.set.success": "Iye ipilẹ fun ẹda %s fun nkankan %s ṣeto si %s", "commands.attribute.failed.entity": "%s kii ṣe nkan ti o wulo fun aṣẹ yii", "commands.attribute.failed.modifier_already_present": "Oluyipada %s ti wa tẹlẹ lori ẹda %s fun nkankan %s", "commands.attribute.failed.no_attribute": "Nkan %s ko ni ẹda kankan %s", "commands.attribute.failed.no_modifier": "Ẹyatọ %s fun nkankan %s ko ni oluyipada %s", "commands.attribute.modifier.add.success": "Oluyipada ti a ṣafikun %s lati ṣe ikaṣe %s fun nkan %s", "commands.attribute.modifier.remove.success": "Yiyọ aṣatunṣe %s kuro ninu ẹda %s fun nkan %s", "commands.attribute.modifier.value.get.success": "Iye ti aṣatunṣe %s lori ẹda %s fun nkankan %s jẹ %s", "commands.attribute.value.get.success": "Iye ti ẹda %s fun nkankan %s jẹ %s", "commands.ban.failed": "Ko si ohun ti o yipada. Ẹrọ orin ti ni idinamọ tẹlẹ", "commands.ban.success": "Ti fi ofin de %s: %s", "commands.banip.failed": "Ko si ohun ti o yipada. IP yẹn ti ni idinamọ tẹlẹ", "commands.banip.info": "Idinamọ yii kan %s ẹrọ orin (awọn): %s", "commands.banip.invalid": "Adirẹsi IP ti ko wulo tabi ẹrọ orin aimọ", "commands.banip.success": "Ti fi ofin de IP %s: %s", "commands.banlist.entry": "%s ti fi ofin de nipasẹ %s: %s", "commands.banlist.entry.unknown": "(Aimọ)", "commands.banlist.list": "(Awọn) Idinamọ %s wa:", "commands.banlist.none": "Ko si awọn wiwọle", "commands.bossbar.create.failed": "Pẹpẹ ọga tẹlẹ wa pẹlu ID '%s'", "commands.bossbar.create.success": "Ṣẹda ọga -iṣẹ aṣa %s", "commands.bossbar.get.max": "Aṣa ọga %%s ni o pọju %s", "commands.bossbar.get.players.none": "Aṣa bossbar %s ko ni awọn oṣere lọwọlọwọ lori a<PERSON>a", "commands.bossbar.get.players.some": "Ilera Oga aṣa %s ni awọn oṣere %s ti o wa lọwọlọwọ: %s", "commands.bossbar.get.value": "Aṣa <PERSON> %%s ni iye ti %s", "commands.bossbar.get.visible.hidden": "Custombar %%s ti wa ni ipamọ lọwọlọwọ", "commands.bossbar.get.visible.visible": "Aṣa bossbar %s ni a fihan lọwọlọwọ", "commands.bossbar.list.bars.none": "Nibẹ ni ko si aṣa bossbars lọwọ", "commands.bossbar.list.bars.some": "Awọn ilera aṣa aṣa %s n ṣiṣẹ: %s", "commands.bossbar.remove.success": "Ti yọ ọga -iṣẹ aṣa kuro %s", "commands.bossbar.set.color.success": "Aṣa bossbar %s ti yi awọ pada", "commands.bossbar.set.color.unchanged": "Ko si ohun ti o yipada. Iyẹn jẹ awọ tẹlẹ ti ọpa ọga yii", "commands.bossbar.set.max.success": "Aṣa <PERSON>ga -iṣẹ %s ti yipada ti o pọju si %s", "commands.bossbar.set.max.unchanged": "Ko si ohun ti o yipada. Iyẹn ti pọ julọ ti ọga iṣẹ yii tẹlẹ", "commands.bossbar.set.name.success": "Aṣa bossbar %s ti ni loruk<PERSON>i", "commands.bossbar.set.name.unchanged": "Ko si ohun ti o yipada. Iyẹn ni orukọ orukọ ọga yii tẹlẹ", "commands.bossbar.set.players.success.none": "Aṣa bossbar %s ko ni awọn oṣere kankan mọ", "commands.bossbar.set.players.success.some": "Ilera olori aṣa %s bayi ni awọn oṣere %s: %s", "commands.bossbar.set.players.unchanged": "Ko si ohun ti o yipada. Awọn oṣere yẹn ti wa tẹlẹ lori ọga pẹlu ko si ẹnikan lati ṣafikun tabi yọ kuro", "commands.bossbar.set.style.success": "Aṣa bossbar %s ti yi ara pada", "commands.bossbar.set.style.unchanged": "Ko si ohun ti o yipada. Iyẹn jẹ aṣa ti ọga ọga yii tẹlẹ", "commands.bossbar.set.value.success": "Aṣa <PERSON>ga -iṣẹ %s ti yi iye pada si %s", "commands.bossbar.set.value.unchanged": "Ko si ohun ti o yipada. Ti o ni tẹlẹ iye ti yi bossbar", "commands.bossbar.set.visibility.unchanged.hidden": "Ko si ohun ti o yipada. <PERSON><PERSON><PERSON>i ọga ti farapamọ tẹlẹ", "commands.bossbar.set.visibility.unchanged.visible": "Ko si ohun ti o yipada. Pẹpẹ ọga ti han tẹlẹ", "commands.bossbar.set.visible.success.hidden": "Custombar %%s ti wa ni pamọ nisinsinyi", "commands.bossbar.set.visible.success.visible": "A<PERSON>a <PERSON> -iṣẹ %s ti han ni<PERSON><PERSON>yi", "commands.bossbar.unknown": "<PERSON> -iṣẹ ti o wa pẹlu ID '%s'", "commands.clear.success.multiple": "Yiyọ %s ohun(s) lati %s awọn ẹrọ orin", "commands.clear.success.single": "Yọ awọn nkan kuro) lati awọn ẹrọ orin", "commands.clear.test.multiple": "Ri %s nkan(s) ti o baamu lori %s awọn ẹrọ orin", "commands.clear.test.single": "A ri %s awọn ohun ti o baamu) lori awọn ẹrọ orin", "commands.clone.failed": "Ko si ohun amorindun ti a ti cloned", "commands.clone.overlap": "<PERSON>wọn orisun ati awọn agbegbe opin irin ajo ko le ṣe idapọ", "commands.clone.success": "Awọn bulọọki %s ṣe ẹda ni aṣeyọri", "commands.clone.toobig": "Ọpọlọpọ awọn bulọọki ni agbegbe ti a ṣalaye (o pọju %s, pàtó %s)", "commands.damage.invulnerable": "Afojusun jẹ ailagbara si iru ibajẹ ti a fun", "commands.damage.success": "Lilo %s ibaje si %s", "commands.data.block.get": "%s lori bulọki %s, %s, %s lẹhin ipin iwọn ti %s jẹ %s", "commands.data.block.invalid": "Àk<PERSON><PERSON> ibi -afẹde kii ṣe nkan idena", "commands.data.block.modified": "Awọn data idena ti a tunṣe ti %s, %s, %s", "commands.data.block.query": "%s, %s, %s ni data idena wọnyi: %s", "commands.data.entity.get": "%s lori %s lẹhin ipin iwọn ti %s jẹ %s", "commands.data.entity.invalid": "Ko le ṣe atunṣe data ẹrọ orin", "commands.data.entity.modified": "Data nkan ti a tunṣe ti %s", "commands.data.entity.query": "%s ni data nkan wọnyi: %s", "commands.data.get.invalid": "Ko le gba %s; awọn aami nọmba nikan ni a gba laaye", "commands.data.get.multiple": "Ariyanjiyan yii gba iye NBT kan ṣoṣo", "commands.data.get.unknown": "Ko le gba %s; tag ko si", "commands.data.merge.failed": "Ko si ohun ti o yipada. Awọn ohun-ini pàtó ti ni awọn iye wọnyi", "commands.data.modify.expected_list": "Atokọ ti a nireti, ni: %s", "commands.data.modify.expected_object": "<PERSON>un ti a nireti, ni: %s", "commands.data.modify.expected_value": "Iye ti a nireti, ti gba: %s", "commands.data.modify.invalid_index": "Atọka akojọ ti ko tọna: %s", "commands.data.modify.invalid_substring": "Aw<PERSON>n atọka okun inu aiṣedeede: %s si %s", "commands.data.storage.get": "%s ni ibi ipamọ %s lẹhin ifosiwewe iwọn ti %s jẹ %s", "commands.data.storage.modified": "<PERSON><PERSON> ipa<PERSON> ti a tunṣe %s", "commands.data.storage.query": "<PERSON>bi ipamọ %s ni awọn akoonu wọnyi: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "Pack '%s' ko ṣiṣẹ!", "commands.datapack.disable.failed.feature": "Pack '%s' cannot be disabled, since it is part of an enabled flag!", "commands.datapack.enable.failed": "Pack '%s' ti ṣiṣẹ tẹlẹ!", "commands.datapack.enable.failed.no_flags": "Apo '%s' ko le muu ṣiṣẹ, niwọn igba ti awọn asia ti o nilo ko ṣiṣẹ ni agbaye yii: %s!", "commands.datapack.list.available.none": "Ko si awọn akopọ data diẹ sii ti o wa", "commands.datapack.list.available.success": "Awọn akopọ ẹya %s wa: %s", "commands.datapack.list.enabled.none": "Ko si awọn akopọ data ti o ṣiṣẹ", "commands.datapack.list.enabled.success": "Awọn idii ẹya %s wa ti a mu ṣiṣẹ: %s", "commands.datapack.modify.disable": "Mu abajọ data ṣiṣẹ %s", "commands.datapack.modify.enable": "Muu akopọ data ṣiṣẹ %s", "commands.datapack.unknown": "Apo data ti a ko mọ '%s'", "commands.debug.alreadyRunning": "A ti bẹrẹ profaili ti ami si", "commands.debug.function.noRecursion": "Ko le wa kakiri lati inu iṣẹ", "commands.debug.function.noReturnRun": "Tracing can't be used with return run", "commands.debug.function.success.multiple": "Wa kakiri awọn aṣẹ %s lati awọn iṣẹ %s si <PERSON>i o wu %s", "commands.debug.function.success.single": "Tọpasẹ %s pipaṣẹ(s) lati iṣẹ '%s' si faili ti njade %s", "commands.debug.function.traceFailed": "Kuna lati wa kakiri eyi", "commands.debug.notRunning": "Profaili ti ami si ko ti bẹrẹ", "commands.debug.started": "Bibẹrẹ ami ami profaili", "commands.debug.stopped": "Ti gbejade ami ami si lẹhin iṣẹju-aaya %s ati awọn ami-am i%s (%s awọn ami-ami fun iṣẹju-aaya kan)", "commands.defaultgamemode.success": "<PERSON><PERSON> ere aiyipada jẹ bayi %s", "commands.deop.failed": "Ko si ohun ti o yipada. Ẹrọ orin kii ṣe oniṣẹ ẹrọ kan", "commands.deop.success": "Ṣe %s ko si oniṣẹ olupin mọ", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "Iṣoro naa ko yipada; o ti ṣeto tẹlẹ si %s", "commands.difficulty.query": "Iṣoro naa jẹ %s", "commands.difficulty.success": "A ti ṣeto iṣoro naa si %s", "commands.drop.no_held_items": "Ẹya ko le mu awọn ohun kan mu", "commands.drop.no_loot_table": "Idahun %s ko ni tabili ikogun", "commands.drop.no_loot_table.block": "Block %s has no loot table", "commands.drop.success.multiple": "Awọn ohun %s silẹ", "commands.drop.success.multiple_with_table": "Awọn ohun %s silẹ lati tabili ikogun %s", "commands.drop.success.single": "Silẹ %s %s", "commands.drop.success.single_with_table": "Silẹ %s %s lati tabili ikogun %s", "commands.effect.clear.everything.failed": "Afojusun ko ni awọn ipa lati yọ kuro", "commands.effect.clear.everything.success.multiple": "Ti yọ gbogbo ipa kuro ninu awọn ibi -afẹde %s", "commands.effect.clear.everything.success.single": "Ti yọ gbogbo ipa kuro ninu %s", "commands.effect.clear.specific.failed": "Afojusun ko ni ipa ti a beere", "commands.effect.clear.specific.success.multiple": "Ipa ti yọ kuro %s lati awọn ibi -afẹde %s", "commands.effect.clear.specific.success.single": "Ipa ti yọ kuro %s lati %s", "commands.effect.give.failed": "Lagbara lati lo ipa yii (ibi -afẹde jẹ boya ko ni awọn ipa, tabi ni nkan ti o lagbara)", "commands.effect.give.success.multiple": "<PERSON>pa ti a lo %s si %s awọn ibi -afẹde", "commands.effect.give.success.single": "Ipa ti a lo %s si %s", "commands.enchant.failed": "Ko si ohun ti o yipada. Awọn ibi -afẹde boya ko ni ohun kan ni ọwọ wọn tabi a ko le lo idan naa", "commands.enchant.failed.entity": "%s kii ṣe nkan ti o wulo fun aṣẹ yii", "commands.enchant.failed.incompatible": "%s ko le ṣe atilẹyin iṣẹ idan naa", "commands.enchant.failed.itemless": "%s ko mu ohun kan mu", "commands.enchant.failed.level": "%s ga ju ipele ti o ga julọ ti %s ni atilẹyin nipasẹ iṣẹ idan naa", "commands.enchant.success.multiple": "Iṣaṣewadii ti a lo %s si %s fun nkan", "commands.enchant.success.single": "Iṣaṣe ti a lo %s si ohun %s", "commands.execute.blocks.toobig": "Ọpọlọpọ awọn bulọọki ni agbegbe ti a ṣalaye (o pọju %s, pàtó %s)", "commands.execute.conditional.fail": "Idanwo kuna", "commands.execute.conditional.fail_count": "Idanwo kuna, ka: %s", "commands.execute.conditional.pass": "Idanwo ti kọja", "commands.execute.conditional.pass_count": "Idanwo ti k<PERSON>ja, ka: %s", "commands.execute.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.experience.add.levels.success.multiple": "Fun awọn ipele iriri %s si awọn oṣere %s", "commands.experience.add.levels.success.single": "Ti fun awọn ipele iriri %s si %s", "commands.experience.add.points.success.multiple": "Ti fun ni iriri %s tọka si awọn oṣere %s", "commands.experience.add.points.success.single": "Ti fun awọn iriri iriri %s si %s", "commands.experience.query.levels": "%s ni awọn ipele iriri %s", "commands.experience.query.points": "%s ni awọn aaye iriri %s", "commands.experience.set.levels.success.multiple": "Ṣeto awọn ipele iriri %s lori awọn oṣere %s", "commands.experience.set.levels.success.single": "Ṣeto awọn ipele iriri %s lori %s", "commands.experience.set.points.invalid": "Ko le ṣeto awọn aaye iriri loke awọn aaye ti o pọju fun ipele ẹrọ orin lọwọlọwọ", "commands.experience.set.points.success.multiple": "Ṣeto awọn aaye i<PERSON>ri %s lori awọn oṣere %s", "commands.experience.set.points.success.single": "Ṣeto awọn aaye iriri %s lori %s", "commands.fill.failed": "Ko si awọn bulọọki ti o kun", "commands.fill.success": "Ni aṣeyọri kun awọn bulọọki %s", "commands.fill.toobig": "Ọpọlọpọ awọn bulọọki ni agbegbe ti a ṣalaye (o pọju %s, pàtó %s)", "commands.fillbiome.success": "Biomes ṣeto laarin %s, %s, %s ati %s, %s, %s", "commands.fillbiome.success.count": "%s biome titẹsi/awọn titẹ sii ṣeto laarin %s, %s, %s ati %s, %s, %s", "commands.fillbiome.toobig": "Ọpọlọpọ awọn bulọọki ni agbegbe ti a ṣalaye (o pọju %s, pàtó %s)", "commands.forceload.added.failure": "Ko si awọn chunks ti samisi fun ikojọpọ agbara", "commands.forceload.added.multiple": "Ti samisi %s chunks ni %s lati %s si %s lati fi agbara mu", "commands.forceload.added.none": "Ko si awọn ipa ipa ti kojọpọ ni a rii ni %s", "commands.forceload.added.single": "Ti samisi chunk %s ni %s lati fi agbara mu", "commands.forceload.list.multiple": "%s agbara ti a kojọpọ ni a rii ni %s ni: %s", "commands.forceload.list.single": "A ri agbara ti o kojọpọ ni %s ni: %s", "commands.forceload.query.failure": "Chunk ni %s ni %s ko ni samisi fun ikojọpọ agbara", "commands.forceload.query.success": "Chunk ni %s ni %s ni a samisi fun ikojọpọ agbara", "commands.forceload.removed.all": "Ko ṣe ami si gbogbo awọn ipa ti kojọpọ ni %s", "commands.forceload.removed.failure": "Ko si awọn ege ti a yọ kuro lati ikojọpọ agbara", "commands.forceload.removed.multiple": "Ko si aami %s chunks ni %s lati %s si %s fun ikojọpọ agbara", "commands.forceload.removed.single": "Ti ko ni aami chunk %s ni %s fun ikojọpọ agbara", "commands.forceload.toobig": "Ọpọlọpọ awọn ege ni agbegbe ti o sọ (o pọju %s, pàtó %s)", "commands.function.error.argument_not_compound": "Invalid argument type: %s, expected Compound", "commands.function.error.missing_argument": "Missing argument %2$s to function %1$s", "commands.function.error.missing_arguments": "Missing arguments to function %s", "commands.function.error.parse": "While instantiating macro %s: Command '%s' caused error: %s", "commands.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.function.result": "Function %s returned %s", "commands.function.scheduled.multiple": "Running functions %s", "commands.function.scheduled.no_functions": "Can't find any functions for name %s", "commands.function.scheduled.single": "Running function %s", "commands.function.success.multiple": "Ti ṣiṣẹ %s pipaṣẹ (s) lati awọn iṣẹ %s", "commands.function.success.multiple.result": "Ṣiṣẹ %s awọn iṣẹ", "commands.function.success.single": "Ti ṣiṣẹ %s pipaṣẹ(s) lati iṣẹ '%s'", "commands.function.success.single.result": "Iṣe '%2$s' da %1$s pada", "commands.gamemode.success.other": "Ṣeto ipo ere %s si %s", "commands.gamemode.success.self": "Ṣeto ipo ere tirẹ si %s", "commands.gamerule.query": "Gamerule %s ti ṣeto lọwọlọwọ si: %s", "commands.gamerule.set": "Gamerule %s ti ṣeto bayi si: %s", "commands.give.failed.toomanyitems": "Ko le fun diẹ ẹ sii ju %s ti %s", "commands.give.success.multiple": "Ti fun awọn oṣere %s %s si %s", "commands.give.success.single": "Ti fun %s %s si %s", "commands.help.failed": "Aṣẹ aimọ tabi awọn igbanilaaye ti ko to", "commands.item.block.set.success": "<PERSON><PERSON><PERSON> iho kan ni %s, %s, %s pẹlu %s", "commands.item.entity.set.success.multiple": "Rọpo iho lori %s awọn nkan pẹlu %s", "commands.item.entity.set.success.single": "Rọpo iho kan lori %s pẹlu %s", "commands.item.source.no_such_slot": "Orisun ko ni Iho %s", "commands.item.source.not_a_container": "Awọn ipo orisunb%s,%s,%s kii ṣe apoti", "commands.item.target.no_changed.known_item": "Ko si awọn ibi-afẹde ti a gba ohun %s sinu iho %s", "commands.item.target.no_changes": "Ko si awọn ibi-afẹde ti o gba sinu iho %s", "commands.item.target.no_such_slot": "Ibi-afẹde ko ni iho %s", "commands.item.target.not_a_container": "Awọn ipo idojukọ %s, %s, %s kii ṣe apoti", "commands.jfr.dump.failed": "<PERSON>na lati ju gbigbasilẹ JFR silẹ: %s", "commands.jfr.start.failed": "Ti kuna lati bẹrẹ profaili JFR", "commands.jfr.started": "Aworan JFR bẹrẹ", "commands.jfr.stopped": "Aworan JFR duro ati ju silẹ si %s", "commands.kick.owner.failed": "Cannot kick server owner in LAN game", "commands.kick.singleplayer.failed": "<PERSON><PERSON> kick in an offline singleplayer game", "commands.kick.success": "Ti gba %s: %s", "commands.kill.success.multiple": "Pa awọn nkan %s", "commands.kill.success.single": "Pa %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "%s ti max ti awọn ẹrọ orin %s wa lori a<PERSON>: %s", "commands.locate.biome.not_found": "Ko le ri iru-ara kan ti iru \"%s\" laarin ijinna to bojumu", "commands.locate.biome.success": "%s to sunmọ wa ni %s ( %s awọn bulọ<PERSON><PERSON> kuro)", "commands.locate.poi.not_found": "Ko le ri iru-ara kan ti iru \"%s\" laarin ijinna to bojumu", "commands.locate.poi.success": "%s to sunmọ wa ni %s ( %s awọn bulọ<PERSON><PERSON> kuro)", "commands.locate.structure.invalid": "Ko si eto pẹlu iru \"%s\"", "commands.locate.structure.not_found": "Ko le wa ọna kan ti iru \"%s\" nitosi", "commands.locate.structure.success": "%s to sunmọ wa ni %s ( %s awọn bulọ<PERSON><PERSON> kuro)", "commands.message.display.incoming": "%s n pariwo si ọ: %s", "commands.message.display.outgoing": "O pariwo si %s: %s", "commands.op.failed": "Ko si ohun ti o yipada. Ẹrọ orin tẹlẹ jẹ oniṣẹ ẹrọ kan", "commands.op.success": "Ṣe %s oniṣẹ ẹrọ", "commands.pardon.failed": "Ko si ohun ti o yipada. A ko fi ofin de ẹrọ orin naa", "commands.pardon.success": "Ainidi %s", "commands.pardonip.failed": "Ko si ohun ti o yipada. IP yẹn ko ni eewọ", "commands.pardonip.invalid": "Adirẹsi IP ti ko wulo", "commands.pardonip.success": "IP ti ko ni ofin %s", "commands.particle.failed": "Awọn patiku ko han fun ẹnikẹni", "commands.particle.success": "<PERSON><PERSON>an patiku %s", "commands.perf.alreadyRunning": "<PERSON><PERSON><PERSON>eto iṣẹ ti bẹrẹ tẹlẹ", "commands.perf.notRunning": "Olupese iṣẹ ko ti bẹrẹ", "commands.perf.reportFailed": "Kuna lati ṣẹda ijabọ yokokoro", "commands.perf.reportSaved": "Ti ṣẹda ijabọ atunkọ ni %s", "commands.perf.started": "Bibẹrẹ 10 ṣiṣe ṣiṣe profaili keji (lo '/ perf stop' lati da duro ni kutukutu)", "commands.perf.stopped": "<PERSON><PERSON><PERSON> iṣẹ ṣiṣe lẹhin %s iṣẹju-aaya ati %s ami(s) (%s ami(s) fun iṣẹju-aaya)", "commands.place.feature.failed": "<PERSON>na lati gbe ẹya ara ẹrọ", "commands.place.feature.invalid": "Ko si ẹya pẹlu iru \"%s\"", "commands.place.feature.success": "Fi \"%s\" si %s, %s, %s", "commands.place.jigsaw.failed": "<PERSON>na lati <PERSON>e ina jigsaw", "commands.place.jigsaw.invalid": "Ko si adagun awoṣe ti o ni iru \"%s\"", "commands.place.jigsaw.success": "Aruwo ti ipilẹṣẹ ni %s, %s, %s", "commands.place.structure.failed": "<PERSON>na lati gbe eto", "commands.place.structure.invalid": "Ko si eto pẹlu iru \"%s\"", "commands.place.structure.success": "Ti ipilẹṣẹ \"%s\" ni %s, %s, %s", "commands.place.template.failed": "<PERSON>na lati gbe awoṣe", "commands.place.template.invalid": "Ko si awoṣe pẹlu id \"%s\"", "commands.place.template.success": "Awoṣe ti kojọpọ \"%s\" ni %s, %s, %s", "commands.playsound.failed": "Ohùn naa ti jinna pupọ lati gbọ", "commands.playsound.success.multiple": "Ti dun ohun %s si %s awọn oṣere", "commands.playsound.success.single": "Ti dun ohun %s si %s", "commands.publish.alreadyPublished": "Ere pupọ pupọ ti gbalejo tẹlẹ lori ibudo %s", "commands.publish.failed": "Ko le gbalejo ere agbegbe", "commands.publish.started": "Ere ti agbegbe ti gbalejo lori ibudo %s", "commands.publish.success": "Ere elere pupọ ti gbalejo bayi lori ibudo %s", "commands.random.error.range_too_large": "The range of the random value must be at most 2147483646", "commands.random.error.range_too_small": "The range of the random value must be at least 2", "commands.random.reset.all.success": "Reset %s random sequence(s)", "commands.random.reset.success": "Reset random sequence %s", "commands.random.roll": "%s rolled %s (from %s to %s)", "commands.random.sample.success": "Randomized value: %s", "commands.recipe.give.failed": "Ko si awọn ilana tuntun ti a kẹkọọ", "commands.recipe.give.success.multiple": "Awọn ilana %s ṣiṣi silẹ fun awọn oṣere %s", "commands.recipe.give.success.single": "Awọn ilana %s ṣiṣi silẹ fun %s", "commands.recipe.take.failed": "Ko si awọn ilana ti o le gbagbe", "commands.recipe.take.success.multiple": "Mu awọn ilana %s lati ọdọ awọn oṣere %s", "commands.recipe.take.success.single": "Mu awọn ilana %s lati %s", "commands.reload.failure": "Ikojọpọ ti kuna; fifi atijọ data", "commands.reload.success": "Nmu lẹẹkansi!", "commands.ride.already_riding": "%s ti gun %s tẹlẹ", "commands.ride.dismount.success": "%s duro gigun %s", "commands.ride.mount.failure.cant_ride_players": "Awọn ẹrọ orin ko le gùn", "commands.ride.mount.failure.generic": "%s ko le bẹrẹ gigun %s", "commands.ride.mount.failure.loop": "Ko le gbe nkan kan sori ararẹ tabi eyikeyi ninu awọn ero inu rẹ", "commands.ride.mount.failure.wrong_dimension": "Ko le gbe nkan kan si oriṣiriṣi iwọn", "commands.ride.mount.success": "%s bẹrẹ gigun %s", "commands.ride.not_riding": "%s ko gun ọkọ kankan", "commands.rotate.success": "Rotated %s", "commands.save.alreadyOff": "Fipamọ ti wa ni pipa tẹlẹ", "commands.save.alreadyOn": "Fifipamọ ti wa ni titan tẹlẹ", "commands.save.disabled": "Fifipamọ aifọwọyi jẹ alaabo bayi", "commands.save.enabled": "Fifipamọ aifọwọyi ti ṣiṣẹ bayi", "commands.save.failed": "<PERSON> le ṣafipamọ ere naa (aaye disk to wa bi?)", "commands.save.saving": "Fifipamọ ere naa (eyi le gba iṣẹju diẹ!)", "commands.save.success": "Ti o ti fipamọ awọn ere", "commands.schedule.cleared.failure": "Ko si awọn iṣeto pẹlu id %s", "commands.schedule.cleared.success": "Yiyọ kuro iṣeto(s) %s pẹlu id %s", "commands.schedule.created.function": "Iṣeto iṣẹ '%s' ni %s ami(s) ni akoko ere %s", "commands.schedule.created.tag": "Atokun ti a seto ' %s' ni awọn ami %s ni gametime %s", "commands.schedule.macro": "Can't schedule a macro", "commands.schedule.same_tick": "Ko le ṣeto fun ami lọwọlọwọ", "commands.scoreboard.objectives.add.duplicate": "<PERSON>di kan ti wa tẹlẹ nipasẹ orukọ yẹn", "commands.scoreboard.objectives.add.success": "Ti ṣẹda ohun tuntun %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Ko si ohun ti o yipada. <PERSON><PERSON> ifihan yẹn ti ṣofo tẹlẹ", "commands.scoreboard.objectives.display.alreadySet": "Ko si ohun ti o yipada. <PERSON><PERSON><PERSON> iṣafihan yẹn ti ṣafihan ohun yẹn tẹlẹ", "commands.scoreboard.objectives.display.cleared": "Ko awọn ibi -afẹde eyikeyi kuro ninu ifihan iho %s", "commands.scoreboard.objectives.display.set": "Ṣeto iho ifihan %s lati <PERSON><PERSON><PERSON><PERSON> ohun -afẹde %s", "commands.scoreboard.objectives.list.empty": "Ko si awọn ibi -afẹde kan", "commands.scoreboard.objectives.list.success": "%s afojusun/afojusun wa: %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Enabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayname": "Yi orukọ ifihan ti %s pada si %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Cleared default number format of objective %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Changed default number format of objective %s", "commands.scoreboard.objectives.modify.rendertype": "Yi ayipada iru ohun ti o jẹ ete %s", "commands.scoreboard.objectives.remove.success": "Ohun ti a yọ kuro %s", "commands.scoreboard.players.add.success.multiple": "Ṣafikun %s si %s fun awọn nkan %s", "commands.scoreboard.players.add.success.single": "Ṣafikun %s si %s fun %s (ni bayi %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Cleared display name for %s entities in %s", "commands.scoreboard.players.display.name.clear.success.single": "Cleared display name for %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Changed display name to %s for %s entities in %s", "commands.scoreboard.players.display.name.set.success.single": "Changed display name to %s for %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Cleared number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Cleared number format for %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Changed number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Changed number format for %s in %s", "commands.scoreboard.players.enable.failed": "Ko si ohun ti o yipada. Ti nfa yẹn ti ṣiṣẹ tẹlẹ", "commands.scoreboard.players.enable.invalid": "Mu ṣiṣẹ ṣiṣẹ nikan lori awọn ibi-afẹde", "commands.scoreboard.players.enable.success.multiple": "Ṣiṣẹ ṣiṣẹ %s fun awọn nkan %s", "commands.scoreboard.players.enable.success.single": "Ṣiṣẹ ṣiṣẹ %s fun %s", "commands.scoreboard.players.get.null": "Ko le gba iye ti %s fun %s; ko si <PERSON>kan ti a ṣeto", "commands.scoreboard.players.get.success": "%s ni %s %s", "commands.scoreboard.players.list.empty": "Ko si awọn nkan ti o tọpinpin", "commands.scoreboard.players.list.entity.empty": "%s ko ni ikun lati fi han", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s ni awọn nọmba %s:", "commands.scoreboard.players.list.success": "Awọn nkan/awọn nkan ti a tọpinpin %s wa: %s", "commands.scoreboard.players.operation.success.multiple": "Ṣe imudojuiwọn %s fun awọn nkan %s", "commands.scoreboard.players.operation.success.single": "Ṣeto %s fun %s si %s", "commands.scoreboard.players.remove.success.multiple": "Ti yọ %s kuro ninu %s fun awọn nkan %s", "commands.scoreboard.players.remove.success.single": "Ti yọ %s kuro ninu %s fun %s (ni bayi %s)", "commands.scoreboard.players.reset.all.multiple": "Tun gbogbo awọn ikun pada fun awọn nkan %s", "commands.scoreboard.players.reset.all.single": "Tun gbogbo awọn ikun pada fun %s", "commands.scoreboard.players.reset.specific.multiple": "Tun %s pada fun awọn nkan %s", "commands.scoreboard.players.reset.specific.single": "Tun %s pada fun %s", "commands.scoreboard.players.set.success.multiple": "Ṣeto %s fun %s awọn nkan si %s", "commands.scoreboard.players.set.success.single": "Ṣeto %s fun %s si %s", "commands.seed.success": "Irugbin: %s", "commands.setblock.failed": "<PERSON> le <PERSON>eto bulọki naa", "commands.setblock.success": "Yi bulọki naa pada ni %s, %s, %s", "commands.setidletimeout.success": "Àkókò àìṣiṣẹ́ ẹ̀rọ náà ti di %s ìṣẹ́jú báyìí", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "Can only set the world spawn for overworld", "commands.setworldspawn.success": "Ṣeto aye spawn ojuami si %s, %s, %s, [%s]", "commands.spawnpoint.success.multiple": "Ṣeto aaye spawn si %s, %s, %s [%s] ni %s fun awọn oṣere %s", "commands.spawnpoint.success.single": "Ṣeto aaye spawn si %s, %s, %s [%s] ni %s fun %s", "commands.spectate.not_spectator": "%s ko si ni ipo oluwo", "commands.spectate.self": "Ko le wo ara rẹ", "commands.spectate.success.started": "Bayi wiwo %s", "commands.spectate.success.stopped": "Ko si ohun ti n wo nkankan kiri", "commands.spreadplayers.failed.entities": "<PERSON> le tan %s nkankan/ohun elo ni ayika %s, %s (ọpọlọpọ awọn ile-iṣẹ fun aaye – gbiyanju lilo itankale ni pupọ julọ %s)", "commands.spreadplayers.failed.invalid.height": "Ti ko tọ o pọju giga %s; ti a reti ga ju agbaye %s lọ", "commands.spreadplayers.failed.teams": "<PERSON> le tan %s egbe(s) ni ayika %s, %s (<PERSON>p<PERSON><PERSON><PERSON>p<PERSON> awọn ile-iṣẹ fun aaye - gbiyanju lati lo itankale ni pupọ julọ %s)", "commands.spreadplayers.success.entities": "Tan %s ẹrọ orin ni ayika %s, %s pẹlu aropin ijinna ti awọn bulọọki %s yato si", "commands.spreadplayers.success.teams": "Tan %s ẹgbẹ(s) ni ayika %s, %s pẹlu aropin ijinna ti awọn bulọọki %s yato si", "commands.stop.stopping": "<PERSON><PERSON><PERSON>", "commands.stopsound.success.source.any": "Da gbogbo awọn ohun '%s' duro", "commands.stopsound.success.source.sound": "<PERSON>ro ohun '%s' lori orisun '%s'", "commands.stopsound.success.sourceless.any": "Da gbogbo awọn ohun duro", "commands.stopsound.success.sourceless.sound": "Da ohun duro '%s'", "commands.summon.failed": "Ko le pe nkankan", "commands.summon.failed.uuid": "Lagbara lati pe nkankan nitori ẹda UUIDs meji", "commands.summon.invalidPosition": "Ipo ti ko wulo fun apejọ", "commands.summon.success": "Ti pe tuntun %s", "commands.tag.add.failed": "A<PERSON><PERSON>sun boya ti ni aami naa tẹlẹ tabi ni awọn aami pupọ pupọ", "commands.tag.add.success.multiple": "Fi aami kun ' %s' si awọn nkan %s", "commands.tag.add.success.single": "Fi aami kun ' %s' si %s", "commands.tag.list.multiple.empty": "Ko si awọn afi lori awọn nkan %s", "commands.tag.list.multiple.success": "Awọn nkan %s ni awọn afiwe afi %s: %s", "commands.tag.list.single.empty": "%s ko ni awọn afi", "commands.tag.list.single.success": "%s ni awọn afi %s: %s", "commands.tag.remove.failed": "Afojusun ko ni tag yii", "commands.tag.remove.success.multiple": "Atoka ti a yọ kuro ' %s' lati awọn nkan %s", "commands.tag.remove.success.single": "A ti yọ tag ' %s' kuro ninu %s", "commands.team.add.duplicate": "Ẹgbẹ kan ti wa tẹlẹ nipasẹ orukọ yẹn", "commands.team.add.success": "Ti ṣẹda ẹgbẹ %s", "commands.team.empty.success": "Ti yọ %s (àwọn) ọmọ ẹgbẹ́ kúrò nínú ẹgbẹ́ %s", "commands.team.empty.unchanged": "Ko si ohun ti o yipada. Ẹgbẹ yẹn ti ṣofo tẹlẹ", "commands.team.join.success.multiple": "Ṣafikun awọn ọmọ ẹgbẹ %s si ẹgbẹ %s", "commands.team.join.success.single": "Ṣafikun %s si ẹgbẹ %s", "commands.team.leave.success.multiple": "Ti yọ awọn ọmọ ẹgbẹ %s kuro ninu ẹgbẹ eyikeyi", "commands.team.leave.success.single": "Ti yọ %s kuro ninu ẹgbẹ eyikeyi", "commands.team.list.members.empty": "Ko si awọn ọmọ ẹgbẹ lori ẹgbẹ %s", "commands.team.list.members.success": "Ẹgbẹ́ %s ní %s (àwọn) ọmọ ẹgbẹ́:%s", "commands.team.list.teams.empty": "Ko si awọn ẹgbẹ", "commands.team.list.teams.success": "(Àwọn) %s ẹgbẹ́ wà: %s", "commands.team.option.collisionRule.success": "<PERSON><PERSON> fun ẹgbẹ %s jẹ bayi \" %s\"", "commands.team.option.collisionRule.unchanged": "Ko si ohun ti o yipada. <PERSON><PERSON> ijamba jẹ iye yẹn tẹlẹ", "commands.team.option.color.success": "Ṣe imudojuiwọn awọ fun ẹgbẹ %s si %s", "commands.team.option.color.unchanged": "Ko si ohun ti o yipada. Ẹgbẹ yẹn ti ni awọ yẹn tẹlẹ", "commands.team.option.deathMessageVisibility.success": "<PERSON>han ifiranṣẹ iku fun ẹgbẹ %s jẹ bayi \" %s\"", "commands.team.option.deathMessageVisibility.unchanged": "Ko si ohun ti o yipada. <PERSON>han ifiranṣẹ iku jẹ iye yẹn tẹlẹ", "commands.team.option.friendlyfire.alreadyDisabled": "Ko si ohun ti o yipada. Ina ọrẹ ti jẹ alaabo tẹlẹ fun ẹgbẹ yẹn", "commands.team.option.friendlyfire.alreadyEnabled": "Ko si ohun ti o yipada. Ina ọrẹ ti ṣiṣẹ tẹlẹ fun ẹgbẹ yẹn", "commands.team.option.friendlyfire.disabled": "Ina ọrẹ alaabo fun ẹgbẹ %s", "commands.team.option.friendlyfire.enabled": "Agbara ina ore fun egbe %s", "commands.team.option.name.success": "Ṣe imudojuiwọn orukọ ẹgbẹ %s", "commands.team.option.name.unchanged": "Ko si ohun ti o yipada. Ẹgbẹ yẹn ti ni orukọ yẹn tẹlẹ", "commands.team.option.nametagVisibility.success": "Hihan Nametag fun ẹgbẹ %s jẹ bayi \" %s\"", "commands.team.option.nametagVisibility.unchanged": "<PERSON> si ohun ti o yipada. Hihan Nametag jẹ iye yẹn tẹlẹ", "commands.team.option.prefix.success": "A ti ṣeto ìpele ẹgbẹ́ si %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Ko si ohun ti o yipada. Ẹgbẹ yẹn tẹlẹ ko le rii awọn ẹlẹgbẹ alaihan", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Ko si ohun ti o yipada. Ẹgbẹ yẹn le ti rii awọn ẹlẹgbẹ alaihan tẹlẹ", "commands.team.option.seeFriendlyInvisibles.disabled": "Ẹgbẹ %s ko le rii awọn ẹlẹgbẹ alaihan mọ", "commands.team.option.seeFriendlyInvisibles.enabled": "Ẹgbẹ %s le rii awọn ẹlẹgbẹ alaihan bayi", "commands.team.option.suffix.success": "A seto ifikun ẹgbẹ si %s", "commands.team.remove.success": "Ti yọ ẹgbẹ kuro %s", "commands.teammsg.failed.noteam": "O gbọdọ wa lori ẹgbẹ kan lati firanṣẹ ẹgbẹ rẹ", "commands.teleport.invalidPosition": "<PERSON><PERSON> ti ko fẹsẹmulẹ fun tẹlifoonu", "commands.teleport.success.entity.multiple": "Teleported %s awọn nkan si %s", "commands.teleport.success.entity.single": "Teleported %s si %s", "commands.teleport.success.location.multiple": "Teleported %s awọn nkan si %s, %s, %s", "commands.teleport.success.location.single": "Teleported %s si %s, %s, %s", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Click to copy to clipboard", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Percentiles: P50: %sms P95: %sms P99: %sms, sample: %s", "commands.tick.query.rate.running": "Target tick rate: %s per second.\nAverage time per tick: %sms (Target: %sms)", "commands.tick.query.rate.sprinting": "Target tick rate: %s per second (ignored, reference only).\nAverage time per tick: %sms", "commands.tick.rate.success": "Set the target tick rate to %s per second", "commands.tick.sprint.report": "Sprint completed with %s ticks per second, or %s ms per tick", "commands.tick.sprint.stop.fail": "No tick sprint in progress", "commands.tick.sprint.stop.success": "Interrupted the current tick sprint", "commands.tick.status.frozen": "The game is frozen", "commands.tick.status.lagging": "The game is running, but can't keep up with the target tick rate", "commands.tick.status.running": "The game is running normally", "commands.tick.status.sprinting": "The game is sprinting", "commands.tick.step.fail": "Unable to step the game - the game must be frozen first", "commands.tick.step.stop.fail": "No tick step in progress", "commands.tick.step.stop.success": "Interrupted the current tick step", "commands.tick.step.success": "Stepping %s tick(s)", "commands.time.query": "Akoko naa jẹ %s", "commands.time.set": "Ṣeto akoko si %s", "commands.title.cleared.multiple": "Awọn akọle ti a sọ di mimọ fun awọn oṣere %s", "commands.title.cleared.single": "Awọn akọle ti a sọ di mimọ fun %s", "commands.title.reset.multiple": "Tun awọn aṣayan akọle tun fun awọn oṣere %s", "commands.title.reset.single": "Tun awọn a<PERSON>ayan akọle tun fun %s", "commands.title.show.actionbar.multiple": "N ṣe afihan akọle iṣẹ iṣe tuntun fun awọn oṣere %s", "commands.title.show.actionbar.single": "N ṣe afihan ak<PERSON> iṣẹ iṣe tuntun fun %s", "commands.title.show.subtitle.multiple": "<PERSON><PERSON>han atunkọ tuntun fun awọn oṣere %s", "commands.title.show.subtitle.single": "<PERSON><PERSON><PERSON> atunk<PERSON> tuntun fun %s", "commands.title.show.title.multiple": "<PERSON><PERSON><PERSON> ak<PERSON>le tuntun fun awọn oṣere %s", "commands.title.show.title.single": "<PERSON><PERSON><PERSON> a<PERSON> tuntun fun %s", "commands.title.times.multiple": "Awọn akoko ifihan akọle ti yipada fun awọn oṣere %s", "commands.title.times.single": "Awọn akoko ifihan akọle ti yipada fun %s", "commands.transfer.error.no_players": "Must specify at least one player to transfer", "commands.transfer.success.multiple": "Transferring %s players to %s:%s", "commands.transfer.success.single": "Transferring %s to %s:%s", "commands.trigger.add.success": "Ti nfa %s (<PERSON><PERSON><PERSON><PERSON> %s si iye)", "commands.trigger.failed.invalid": "O le ma nfa awọn ibi -afẹde nikan ti o jẹ iru 'okunfa'", "commands.trigger.failed.unprimed": "O ko le ṣe okunfa ibi -afẹde yii sibẹsibẹ", "commands.trigger.set.success": "Ti nfa %s (iye ti a ṣeto si %s)", "commands.trigger.simple.success": "Ti ṣe okunfa %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "Ṣeto oju ojo lati ko", "commands.weather.set.rain": "Ṣeto oju ojo si ojo", "commands.weather.set.thunder": "Ṣeto oju ojo si ojo & <PERSON>ra", "commands.whitelist.add.failed": "Ẹrọ orin ti wa ni atokọ tẹlẹ", "commands.whitelist.add.success": "Ṣafikun %s si atokọ iwe -aṣẹ", "commands.whitelist.alreadyOff": "Whitelist ti wa ni pipa tẹlẹ", "commands.whitelist.alreadyOn": "Whitelist ti wa ni titan tẹlẹ", "commands.whitelist.disabled": "Whitelist ti wa ni pipa bayi", "commands.whitelist.enabled": "Whitelist ti wa ni titan", "commands.whitelist.list": "(Àwọn) akópa tí wọ́n %s ní ìgbàláàyè wà: %s", "commands.whitelist.none": "Ko si awọn oṣere funfun", "commands.whitelist.reloaded": "Ti tun ṣe atunto atokọ funfun", "commands.whitelist.remove.failed": "Ẹrọ orin ko ni iwe -aṣẹ funfun", "commands.whitelist.remove.success": "Ti yọ %s kuro ninu atok<PERSON> iwe -aṣẹ", "commands.worldborder.center.failed": "Ko si ohun ti o yipada. <PERSON>ala agbaye tẹlẹ ti dojukọ nibẹ", "commands.worldborder.center.success": "Ṣeto aarin aala agbaye si %s, %s", "commands.worldborder.damage.amount.failed": "Ko si ohun ti o yipada. Bibajẹ aala agbaye jẹ iye yẹn tẹlẹ", "commands.worldborder.damage.amount.success": "Ṣeto ibajẹ aala agbaye si %s fun bulọọki ni iṣẹju-aaya kọọkan", "commands.worldborder.damage.buffer.failed": "Ko si ohun ti o yipada. Ifarapa bibajẹ aala agbaye ti jẹ ijinna yẹn tẹlẹ", "commands.worldborder.damage.buffer.success": "Ṣeto ifipamọ ibajẹ aala agbaye si bulọọki %s", "commands.worldborder.get": "Aala agbaye lọwọlọwọ %s bulọọki fife", "commands.worldborder.set.failed.big": "Aala agbaye ko le tobi %s awọn bulọọki lọ jakejado", "commands.worldborder.set.failed.far": "Aala aye ko le wa siwaju ju awọn bulọọki %s lọ", "commands.worldborder.set.failed.nochange": "Ko si ohun ti o yipada. Aala agbaye jẹ iwọn yẹn tẹlẹ", "commands.worldborder.set.failed.small": "Aala agbaye ko le kere ju bulọọki 1 jakejado", "commands.worldborder.set.grow": "Dagba aala agbaye si awọn bulọọki %s jakejado ju iṣẹju -aaya %s lọ", "commands.worldborder.set.immediate": "Ṣeto aala aye si %s bulọọki(s) fife", "commands.worldborder.set.shrink": "Din aala agbaye si %s bulọọki(s) jakejado ju %s iṣẹju-aaya", "commands.worldborder.warning.distance.failed": "Ko si ohun ti o yipada. Ikilọ aala agbaye ti jẹ ijinna yẹn tẹlẹ", "commands.worldborder.warning.distance.success": "Ṣeto ijinna ikilọ aala agbaye si bulọọki %s", "commands.worldborder.warning.time.failed": "Ko si ohun ti o yipada. Ikilọ aala agbaye jẹ tẹlẹ iye akoko naa", "commands.worldborder.warning.time.success": "Ṣeto akoko ikilọ aala agbaye si %s iṣẹju-aaya", "compliance.playtime.greaterThan24Hours": "O ti nṣere fun o ju wakati 24 lọ", "compliance.playtime.hours": "O ti nṣere fun awọn wakati(s) %s", "compliance.playtime.message": "Awọn ere ti o pọju le dabaru pẹlu igbesi aye ojoojumọ deede", "connect.aborted": "Išẹ ti dena", "connect.authorizing": "<PERSON><PERSON><PERSON> ni...", "connect.connecting": "Nsopọ si olupin...", "connect.encrypting": "Atokunla...", "connect.failed": "Ti kùnà lati sopọ si olupin naa", "connect.failed.transfer": "Connection failed while transferring to the server", "connect.joining": "Ti darapọ mọ aye...", "connect.negotiating": "N ṣakoro...", "connect.reconfiging": "Reconfiguring...", "connect.reconfiguring": "Reconfiguring...", "connect.transferring": "Transferring to new server...", "container.barrel": "<PERSON>g", "container.beacon": "Bekini", "container.beehive.bees": "Bees: %s / %s", "container.beehive.honey": "Honey: %s / %s", "container.blast_furnace": "Blast Furnace", "container.brewing": "<PERSON><PERSON><PERSON><PERSON>", "container.cartography_table": "Tabili lati ṣẹda awọn maapu", "container.chest": "Àyà", "container.chestDouble": "Àyà nla", "container.crafter": "Crafter", "container.crafting": "Ṣiṣẹda", "container.creative": "<PERSON><PERSON><PERSON><PERSON>", "container.dispenser": "Olupilẹṣẹ", "container.dropper": "<PERSON><PERSON><PERSON><PERSON>", "container.enchant": "Enchant", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Lapis <PERSON>", "container.enchant.lapis.one": "1 Lapis Lazuli", "container.enchant.level.many": "%s Ipele Enchantment", "container.enchant.level.one": "1 Ipele Enchantment", "container.enchant.level.requirement": "Ipele ti a beere:%s", "container.enderchest": "<PERSON><PERSON>", "container.furnace": "Iler<PERSON>", "container.grindstone_title": "Titunṣe & Disenchant", "container.hopper": "<PERSON><PERSON> kan <PERSON>", "container.inventory": "Oja-<PERSON><PERSON>", "container.isLocked": "%s ti wa ni titiipa!", "container.lectern": "Lectern", "container.loom": "Loom", "container.repair": "Tunṣe & Orukọ", "container.repair.cost": "Iye owo Enchantment:%1$s", "container.repair.expensive": "Gbowo Giga!", "container.shulkerBox": "<PERSON><PERSON><PERSON>", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "ati %s siwaju sii...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Smoker", "container.spectatorCantOpen": "Lagbara lati ṣii. Ìkógun ko ti ipilẹṣẹ sibẹsibẹ.", "container.stonecutter": "<PERSON>uta <PERSON>", "container.upgrade": "<PERSON><PERSON> elo igbesoke", "container.upgrade.error_tooltip": "Nkan ko le <PERSON>e igbesoke ni ọna yii", "container.upgrade.missing_template_tooltip": "<PERSON>", "controls.keybinds": "<PERSON><PERSON><PERSON><PERSON>...", "controls.keybinds.duplicateKeybinds": "<PERSON><PERSON><PERSON>i yii tun jẹ lilo fun:\n%s", "controls.keybinds.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "controls.reset": "<PERSON><PERSON>", "controls.resetAll": "Tun gbogbo rẹ to", "controls.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.customize.buffet.biome": "Jọwọ yan biome kan", "createWorld.customize.buffet.title": "Buffet aye isọdi-ara", "createWorld.customize.flat.height": "Iga", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "Nën -%s", "createWorld.customize.flat.layer.top": "Top -%s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON>er", "createWorld.customize.flat.tile": "<PERSON><PERSON>", "createWorld.customize.flat.title": "Përshtatje Superflat", "createWorld.customize.presets": "<PERSON><PERSON><PERSON><PERSON> tito tẹlẹ", "createWorld.customize.presets.list": "<PERSON>a ana <PERSON>, këtu kemi disa që kemi bërë më herët!", "createWorld.customize.presets.select": "Përdorni paraprakisht", "createWorld.customize.presets.share": "Fẹ lati pin tito tẹlẹ rẹ pẹlu ẹnikan? Lo apoti ti o wa ni isalẹ!", "createWorld.customize.presets.title": "Përzgjidhni një parazgjedhje", "createWorld.preparing": "Ngbaradi fun ẹda agbaye...", "createWorld.tab.game.title": "<PERSON><PERSON>", "createWorld.tab.more.title": "Die e sii", "createWorld.tab.world.title": "<PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.attribution": "<PERSON><PERSON><PERSON>", "credits_and_attribution.button.credits": "<PERSON><PERSON><PERSON><PERSON>", "credits_and_attribution.button.licenses": "<PERSON>w<PERSON>n iwe-aṣẹ", "credits_and_attribution.screen.title": "Kirediti ati Attribution", "dataPack.bundle.description": "<PERSON> ohun kan <PERSON> adanwo ṣiṣẹ", "dataPack.bundle.name": "<PERSON><PERSON><PERSON>", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Improved movement for Minecarts", "dataPack.minecart_improvements.name": "Minecart Improvements", "dataPack.redstone_experiments.description": "Experimental Redstone changes", "dataPack.redstone_experiments.name": "Redstone Experiments", "dataPack.title": "<PERSON>", "dataPack.trade_rebalance.description": "Updated trades for Villagers", "dataPack.trade_rebalance.name": "Villager Trade Rebalance", "dataPack.update_1_20.description": "Awọn ẹya tuntun ati akoonu fun Minecraft 1.20", "dataPack.update_1_20.name": "Imudojuiwọn 1.20", "dataPack.update_1_21.description": "New features and content for Minecraft 1.21", "dataPack.update_1_21.name": "Update 1.21", "dataPack.validation.back": "Pada", "dataPack.validation.failed": "Iwe afọwọkọ data ti kuna!", "dataPack.validation.reset": "Tun si aiyipada", "dataPack.validation.working": "Ṣiṣẹtọ awọn akopọ data ti a yan...", "dataPack.vanilla.description": "Boṣewa data fun Minecraft", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "New features and content for the Winter Drop", "dataPack.winter_drop.name": "Winter Drop", "datapackFailure.safeMode": "<PERSON><PERSON>", "datapackFailure.safeMode.failed.description": "Aye yii ni data ipamọ ti ko tọ tabi ibaje ninu.", "datapackFailure.safeMode.failed.title": "<PERSON>na lati koj<PERSON> aye ni <PERSON>.", "datapackFailure.title": "Awọn aṣiṣe ninu awọn akopọ data ti a yan lọwọlọwọ ṣe idiwọ fun agbaye lati kojọpọ.\nO le gbiyanju lati ṣajọpọ pẹlu idii data fanila nikan (\"ipo ailewu\"), tabi pada si iboju akọle ki o ṣe atunṣe pẹlu ọwọ.", "death.attack.anvil": "%1$s ti jẹ papọ nipasẹ kokoro ti o ṣubu", "death.attack.anvil.player": "Ẹrọ orin %1$s ti balẹ nipasẹ gbigbọn apata nigbati o ba ja si %2$s", "death.attack.arrow": "%1$s ti ta nipasẹ %2$s", "death.attack.arrow.item": "%1$s ti ta nipasẹ %2$s lilo %3$s", "death.attack.badRespawnPoint.link": "Iṣẹ idiyele ninu ere", "death.attack.badRespawnPoint.message": "Ẹrọ orin %1$s ti pa nipasẹ ẹrọ orin %2$s", "death.attack.cactus": "%1$s ti ta lele si iku", "death.attack.cactus.player": "%1$s wọ inu cactus lakoko ti o n gbiyanju lati sa fun %2$s", "death.attack.cramming": "%1$s ti pọ ju pupọ", "death.attack.cramming.player": "Ẹrọ orin %1$s ti ni itemole nipasẹ %2$s", "death.attack.dragonBreath": "%1$s ni a sun ninu ẹmi dragoni", "death.attack.dragonBreath.player": "%1$s ni a sun ninu ẹmi dragoni nipasẹ %2$s", "death.attack.drown": "%1$s rì", "death.attack.drown.player": "%1$s rì lakoko ti o n gbiyanju lati sa %2$s", "death.attack.dryout": "%1$s s ku lati gbigbẹ", "death.attack.dryout.player": "%1$s ku lati gbigbẹ nigbati o n gbiyanju lati sa %2$s", "death.attack.even_more_magic": "%1$s pa nipasẹ idan diẹ sii", "death.attack.explosion": "%1$s fẹ", "death.attack.explosion.player": "%1$s ti fẹ nipasẹ %2$s", "death.attack.explosion.player.item": "Ẹrọ orin %1$s ti fẹrẹ soke nipasẹ %2$s lilo %3$s", "death.attack.fall": "%1$s lu ilẹ ju lile", "death.attack.fall.player": "Ẹrọ orin %1$s ti ṣe ibalẹ lile gbiyanju lati saaju ṣaaju ki o to %2$s", "death.attack.fallingBlock": "%1$s ti jẹ papọ nipasẹ bulọọki ja bo", "death.attack.fallingBlock.player": "Ẹrọ orin %1$s ti balẹ nipasẹ ọfà ti o ṣubu nigba ti o baja lodi si ẹrọ orin %2$s", "death.attack.fallingStalactite": "%1$s ti fọn nipasẹ stalactite ja bo", "death.attack.fallingStalactite.player": "%1$s ti wa ni titan nipasẹ fifọ stalactite lakoko ti o n ja %2$s", "death.attack.fireball": "Ti fi ina kun ina %1$s nipasẹ %2$s", "death.attack.fireball.item": "Ti fi ina kun ina %1$s nipasẹ %2$s lilo %3$s", "death.attack.fireworks": "%1$s lọ pẹlu ariwo", "death.attack.fireworks.item": "%1$s bu jade nitori awọn ina ti a ṣe nipasẹ%3$s nipasẹ olumulo%2$s", "death.attack.fireworks.player": "Ẹrọ orin %1$s ti lọ pẹlu bangi lakoko ti o ba lodi si ẹrọ orin %2$s", "death.attack.flyIntoWall": "%1$s ni agbara kainetik", "death.attack.flyIntoWall.player": "Ẹrọ orin %1$s ni iriri agbara agbara ti n gbiyanju lati sa lati %2$s", "death.attack.freeze": "%1$s di di iku", "death.attack.freeze.player": "%1$s ti tutunini si iku nipasẹ %2$s", "death.attack.generic": "%1$s ku", "death.attack.generic.player": "Ẹrọ orin %1$s ku nitori ti %2$s", "death.attack.genericKill": "%1$s ti a pa", "death.attack.genericKill.player": "%1$s ti pa nigba ija pẹlu %2$s", "death.attack.hotFloor": "Ẹrọ orin %1$s se awari pe papa jẹ ina", "death.attack.hotFloor.player": "%1$s rin sinu agbegbe ewu nitori %2$s", "death.attack.inFire": "%1$s lọ ninu ina", "death.attack.inFire.player": "%1$s rin sinu ina lakoko ti o n ja %2$s", "death.attack.inWall": "Ti pa %1$s ninu ogiri kan", "death.attack.inWall.player": "Ẹrọ orin %1$s wa ni odi nigba ti o baja %2$s", "death.attack.indirectMagic": "%1$s pa nipasẹ %2$s nipa lilo idan", "death.attack.indirectMagic.item": "%1$s pa nipasẹ %2$s lilo %3$s", "death.attack.lava": "%1$s gbiyanju lati we ninu lava", "death.attack.lava.player": "%1$s gbiyanju lati wẹ ninu lava lati sa fun %2$s", "death.attack.lightningBolt": "%1$s lu manamana", "death.attack.lightningBolt.player": "%1$s ti pa nipasẹ ina mimu nigba ti o njẹ %2$s", "death.attack.mace_smash": "%1$s was smashed by %2$s", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "%1$s ti pa nipa idan", "death.attack.magic.player": "%1$s ni a pa nipasẹ idan lakoko ti o n salọ%2$s", "death.attack.message_too_long": "<PERSON><PERSON><PERSON>, ifiranṣẹ naa ti gun ju lati fi jiṣẹ ni kikun. Ma binu! Eyi ni ẹya yiyọ kuro: %s", "death.attack.mob": "%1$s ti pa nipasẹ %2$s", "death.attack.mob.item": "%1$s ti pa nipasẹ %2$s lilo %3$s", "death.attack.onFire": "%1$s sun si iku", "death.attack.onFire.item": "%1$s Ti sun si agaran lakoko ija %2$s lilo %3$s", "death.attack.onFire.player": "%1$s si agaran lakoko ti o n ja %2$s", "death.attack.outOfWorld": "%1$s subu kuro ni agbaye", "death.attack.outOfWorld.player": "Ẹrọ orin %1$s ko fẹ lati gbe ni aye kanna bi %2$s", "death.attack.outsideBorder": "%1$s fi opin si aye yi", "death.attack.outsideBorder.player": "%1$s fi opin si aye yi nigba ija %2$s", "death.attack.player": "%1$s ti pa nipasẹ %2$s", "death.attack.player.item": "%1$s ti pa nipasẹ %2$s lilo %3$s", "death.attack.sonic_boom": "%1$s ti parẹ nipasẹ igbe ẹsun kan ti o gba agbara-ọrọ", "death.attack.sonic_boom.item": "%1$s ti parẹ nipasẹ igbe ẹsun kan ti o nfi ẹsun ti ara rẹ jẹ nigba ti o ngbiyanju lati sa fun %2$s lilo %3$s", "death.attack.sonic_boom.player": "%1$s ti parẹ nipasẹ igbe ẹsun kan ti a fi ẹsun kan ti o ngbiyanju lati sa fun %2$s", "death.attack.stalagmite": "%1$s ti kan mọgi lori stalagmite kan", "death.attack.stalagmite.player": "%1$s ti kan mọgi lori iduro kan nigbati o ja %2$s", "death.attack.starve": "%1$s ebi npa ku", "death.attack.starve.player": "Ẹrọ orin %1$s ku fun ebi npa nigba ti ija %2$s", "death.attack.sting": "%1$s ta ku", "death.attack.sting.item": "%1$s was stung to death by %2$s using %3$s", "death.attack.sting.player": "%1$s ti ta si iku nipa %2$s", "death.attack.sweetBerryBush": "%1$s ti pa mọ nipasẹ iku igbo dun kan", "death.attack.sweetBerryBush.player": "%1$s ni a pa mọ ku nipasẹ igbo berry ti o dun lakoko ig<PERSON>yan<PERSON> lati sa fun %2$s", "death.attack.thorns": "Ti pa %1$s ni igbiyanju lati ṣe ipalara %2$s", "death.attack.thorns.item": "Ẹrọ orin %1$s ti pa nipasẹ ẹrọ orin %3$s gbiyanju lati ṣe ipalara oloro %2$s", "death.attack.thrown": "%1$s ti ṣajọ nipasẹ %2$s", "death.attack.thrown.item": "%1$s ti ṣajọ nipasẹ %2$s lilo %3$s", "death.attack.trident": "Ẹrọ orin %1$s ti gun nipasẹ orin %2$s", "death.attack.trident.item": "Ẹrọ orin %1$s ti gun nipasẹ orin %2$s pẹlu: %3$s", "death.attack.wither": "%1$s rọ", "death.attack.wither.player": "Ẹrọ orin %1$s ṣan ni igba ija si %2$s", "death.attack.witherSkull": "%1$s lu nipa timole lati%2$s", "death.attack.witherSkull.item": "%1$s ni a ta nipasẹ agbárí lati %2$s ni lilo %3$s", "death.fell.accident.generic": "%1$s ṣubu lati ibi giga kan", "death.fell.accident.ladder": "%1$s ṣubu lulẹ akaba kan", "death.fell.accident.other_climbable": "%1$s <PERSON>ubu lakoko ti ngun", "death.fell.accident.scaffolding": "%1$s ṣubu kuro ni abawọn ijuwe", "death.fell.accident.twisting_vines": "%1$s ṣubu kuro ni diẹ ninu awọn àjara lilọ kiri", "death.fell.accident.vines": "%1$s subu diẹ ninu awọn <PERSON>a", "death.fell.accident.weeping_vines": "%1$s ṣubu diẹ ninu awọn ajara omije", "death.fell.assist": "%1$s ti ni ijakule lati ṣubu nipasẹ %2$s", "death.fell.assist.item": "%1$s ni ijakule lati ṣubu nipasẹ %2$s nipa lilo %3$s", "death.fell.finish": "%1$s <PERSON>ubu ti o jinna o si pari nipa %2$s", "death.fell.finish.item": "%1$s ṣubu ti o jinna o si pari nipa %2$s nipa lilo %3$s", "death.fell.killer": "%1$s ms ti ni ijakule lati <PERSON>u", "deathScreen.quit.confirm": "Ṣe o da ọ loju pe o fẹ dawọ duro?", "deathScreen.respawn": "<PERSON><PERSON><PERSON>e", "deathScreen.score": "O wole", "deathScreen.score.value": "Score: %s", "deathScreen.spectate": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.title": "O ku!", "deathScreen.title.hardcore": "Ere pari!", "deathScreen.titleScreen": "<PERSON><PERSON><PERSON>", "debug.advanced_tooltips.help": "F3 + H = Awọn irinṣẹ irinṣẹ ilọsiwaju", "debug.advanced_tooltips.off": "Awọn imọran irinṣẹ ilọsiwaju: farapamọ", "debug.advanced_tooltips.on": "<PERSON><PERSON><PERSON><PERSON> imọran irinṣẹ ilọsiwaju: ti han", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON> awọn aala chunk", "debug.chunk_boundaries.off": "Awọn aala Chunk: farapamọ", "debug.chunk_boundaries.on": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> Chun<PERSON>: ti han", "debug.clear_chat.help": "F3 + D = Ko iwiregbe nu", "debug.copy_location.help": "F3 + C = Didakọ ipo naa bi aṣẹ /tp, mu F3 + C lati idorikodo ere naa", "debug.copy_location.message": "Ipo ti a dakọ si paali", "debug.crash.message": "Apapo F3 + C ti wa ni titẹ. Ere naa yoo ni idorikodo ti o ko ba tu bọtini.", "debug.crash.warning": "Puro fun fun %s...", "debug.creative_spectator.error": "Ko le yipada ipo ere; ko si igban<PERSON>aye", "debug.creative_spectator.help": "F3 + N = Yi ipo ere iṣaaju <-> o<PERSON><PERSON><PERSON>", "debug.dump_dynamic_textures": "Fipamọ awọn awoara ti o ni agbara si %s", "debug.dump_dynamic_textures.help": "F3 + S = <PERSON><PERSON><PERSON> awọn awoara <PERSON>úd<PERSON>", "debug.gamemodes.error": "<PERSON> le ṣii switcher ipo ere; ko si igban<PERSON>e", "debug.gamemodes.help": "F3 + F4 = Ṣii switcher ipo ere", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Itele", "debug.help.help": "F3 + Q = Fi akojọ yii han", "debug.help.message": "<PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON> b<PERSON><PERSON>:", "debug.inspect.client.block": "Ti dakọ data idena ẹgbẹ-alabara si <PERSON>kuru", "debug.inspect.client.entity": "Ti dakọ data ẹda ẹgbẹ-alabara si <PERSON>kuru", "debug.inspect.help": "F3 + I = Daakọ nkan kan tabi dina data si agekuru", "debug.inspect.server.block": "Ti dakọ data idena ẹgbẹ-ẹgbẹ si agekuru", "debug.inspect.server.entity": "Ti dakọ data nkan ẹgbẹ olupin si agekuru", "debug.pause.help": "F3 + Esc = <PERSON><PERSON> la<PERSON> a<PERSON> a<PERSON>ayan idaduro (ti idaduro ba ṣeeṣe)", "debug.pause_focus.help": "F3 + P = Sinmi lori idojuk<PERSON> ti sọnu", "debug.pause_focus.off": "Sinmi lori idojukọ ti o sọnu: alaabo", "debug.pause_focus.on": "Sinmi lori idojukọ ti o sọnu: mu ṣiṣẹ", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Ibẹrẹ / da profaili duro", "debug.profiling.start": "Profaili bẹrẹ fun %s aaya. Lo F3 + L lati da duro ni kutukutu", "debug.profiling.stop": "Profaili pari. Awọn esi ti a fipamọ si %s", "debug.reload_chunks.help": "F3 + A = reloading chunks", "debug.reload_chunks.message": "Reloading gbogbo chunks", "debug.reload_resourcepacks.help": "F3 + T = Tun gbe awọn akopọ orisun pada", "debug.reload_resourcepacks.message": "Awọn idii awọn orisun ti tun gbejade", "debug.show_hitboxes.help": "F3 + B = Ṣafihan awọn apoti apoti", "debug.show_hitboxes.off": "Hitboxes: pam<PERSON>", "debug.show_hitboxes.on": "<PERSON>wọn apoti i<PERSON>: ti fihan", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "Yi demo yoo ṣiṣe ni ọjọ marun ọjọ. Ṣe ohun ti o dara julọ!", "demo.day.2": "Ọjọ meji", "demo.day.3": "Ọjọ mẹta", "demo.day.4": "Ọjọ Mẹrin", "demo.day.5": "<PERSON>yi ni ọjọ ikẹhin rẹ!", "demo.day.6": "O ti kọja ọjọ karun rẹ. Lo %s lati fipamọ sikirinifoto ti ẹda rẹ.", "demo.day.warning": "A<PERSON>ko rẹ ti fẹrẹ pẹ!", "demo.demoExpired": "<PERSON>ago Demo soke!", "demo.help.buy": "Ra Bayi!", "demo.help.fullWrapped": "Ṣe demo yii yoo ṣiṣe ni awọn ọjọ 5 in-game (nipa wakati 1 ati iṣẹju 40 ti akoko gidi). Ṣayẹwo awọn ilọsiwaju fun awọn tanilolobo! Gba dun!", "demo.help.inventory": "Lo bọtini %1$s lati ṣii oja rẹ", "demo.help.jump": "Jump nipa titẹ bọtini %1$s", "demo.help.later": "Tẹsiwaju Ṣiṣẹ!", "demo.help.movement": "Lo awọn %1$s,%2$s,%3$s,%4$s awọn bọtini ati awọn Asin lati gbe ni ayika", "demo.help.movementMouse": "Wo ni ayika lilo <PERSON>", "demo.help.movementShort": "Gbe nipasẹ titẹ %1$s,%2$s,%3$s,%4$s awọn b<PERSON><PERSON>i", "demo.help.title": "Minecraft Demo Ipo", "demo.remainingTime": "Akoko ti o pẹ: %s", "demo.reminder": "<PERSON><PERSON><PERSON> demo ti pari. Ra ere lati tẹsiwaju tabi bẹrẹ agbaye tuntun!", "difficulty.lock.question": "Ṣe o da ọ loju pe o fẹ lati ṣii iṣoro ti aye yii? Eyi yoo ṣeto aye yii lati ma jẹ %1$s nigbagbogbo, ati pe iwọ kii yoo ni anfani lati yi pada lẹẹkansi.", "difficulty.lock.title": "Tiiipa Agbaye Titan", "disconnect.endOfStream": "<PERSON><PERSON><PERSON>", "disconnect.exceeded_packet_rate": "Gba fun iye ti o pọ ju iwọn soso lọ", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "<PERSON><PERSON><PERSON><PERSON> ibeere ipo", "disconnect.loginFailedInfo": "Ti kùnà lati wọle: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Pupọ ṣiṣẹ ni alaabo. Jọwọ ṣayẹwo awọn eto akọọlẹ Microsoft rẹ.\n", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON><PERSON> (Gbiyanju tun bẹrẹ iṣẹ rẹ ati o<PERSON>)", "disconnect.loginFailedInfo.serversUnavailable": "\"Awọn olupin ìfàṣẹsí ko ṣe de ọdọ lọwọlọwọ. Jọwọ gbiyanju agai\".", "disconnect.loginFailedInfo.userBanned": "O ti wa ni idinamọ lati mu online", "disconnect.lost": "As<PERSON><PERSON> ti sọnu", "disconnect.packetError": "Network Protocol Error", "disconnect.spam": "Ti da jade fun àwúrú<PERSON>", "disconnect.timeout": "Tita jade", "disconnect.transfer": "Transferred to another server", "disconnect.unknownHost": "Alejo ti a ko mo", "download.pack.failed": "%s out of %s pack(s) failed to download", "download.pack.progress.bytes": "Progress: %s (total size unknown)", "download.pack.progress.percent": "Progress: %s%%", "download.pack.title": "Downloading resource pack %s/%s", "editGamerule.default": "Aiyipada: %s", "editGamerule.title": "Satunkọ awọn ofin ere", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.bad_omen": "Buburu Omen", "effect.minecraft.blindness": "Afọju", "effect.minecraft.conduit_power": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.darkness": "Òkunkun", "effect.minecraft.dolphins_grace": "I<PERSON> to wulo", "effect.minecraft.fire_resistance": "<PERSON><PERSON>", "effect.minecraft.glowing": "<PERSON><PERSON>", "effect.minecraft.haste": "Kánkán", "effect.minecraft.health_boost": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON><PERSON> ti <PERSON>", "effect.minecraft.hunger": "<PERSON><PERSON>", "effect.minecraft.infested": "Infested", "effect.minecraft.instant_damage": "Ibajẹ lẹsẹkẹsẹ", "effect.minecraft.instant_health": "Ilera lẹsẹkẹsẹ", "effect.minecraft.invisibility": "<PERSON><PERSON><PERSON>", "effect.minecraft.jump_boost": "<PERSON><PERSON> didn", "effect.minecraft.levitation": "<PERSON><PERSON><PERSON>", "effect.minecraft.luck": "Orire", "effect.minecraft.mining_fatigue": "Iwakusa rirẹ", "effect.minecraft.nausea": "<PERSON>u rirun", "effect.minecraft.night_vision": "Alẹ Iran", "effect.minecraft.oozing": "Oozing", "effect.minecraft.poison": "<PERSON><PERSON>", "effect.minecraft.raid_omen": "Raid Omen", "effect.minecraft.regeneration": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.resistance": "Atako", "effect.minecraft.saturation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.slow_falling": "<PERSON><PERSON><PERSON>", "effect.minecraft.slowness": "O lọra", "effect.minecraft.speed": "<PERSON><PERSON><PERSON>", "effect.minecraft.strength": "<PERSON><PERSON><PERSON>", "effect.minecraft.trial_omen": "Trial Omen", "effect.minecraft.unluck": "Oriburuku", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON>", "effect.minecraft.weakness": "<PERSON><PERSON>a", "effect.minecraft.weaving": "Weaving", "effect.minecraft.wind_charged": "Wind Charged", "effect.minecraft.wither": "<PERSON>er", "effect.none": "<PERSON> ipa", "enchantment.level.1": "I", "enchantment.level.10": "X", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "VII", "enchantment.level.8": "VIII", "enchantment.level.9": "IX", "enchantment.minecraft.aqua_affinity": "<PERSON><PERSON>", "enchantment.minecraft.bane_of_arthropods": "Bane ti Arthropods", "enchantment.minecraft.binding_curse": "Eg<PERSON> ti <PERSON>", "enchantment.minecraft.blast_protection": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "Density", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON> Strider", "enchantment.minecraft.efficiency": "Ṣiṣe", "enchantment.minecraft.feather_falling": "Iyẹ Silẹ Iye", "enchantment.minecraft.fire_aspect": "<PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Ida<PERSON><PERSON><PERSON>", "enchantment.minecraft.flame": "<PERSON><PERSON>", "enchantment.minecraft.fortune": "Fortune", "enchantment.minecraft.frost_walker": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.impaling": "Puncture", "enchantment.minecraft.infinity": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.knockback": "Ẹkunkun", "enchantment.minecraft.looting": "Ikogun", "enchantment.minecraft.loyalty": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.luck_of_the_sea": "<PERSON><PERSON> ti <PERSON>", "enchantment.minecraft.lure": "<PERSON><PERSON>", "enchantment.minecraft.mending": "Fifiranṣẹ", "enchantment.minecraft.multishot": "Multishot", "enchantment.minecraft.piercing": "<PERSON><PERSON>", "enchantment.minecraft.power": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Idaabobo Projectile", "enchantment.minecraft.protection": "Idaabobo", "enchantment.minecraft.punch": "Punch", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "Torped<PERSON>", "enchantment.minecraft.sharpness": "Didasilẹ", "enchantment.minecraft.silk_touch": "Ifọwọkan Siliki", "enchantment.minecraft.smite": "<PERSON>", "enchantment.minecraft.soul_speed": "Ọkàn iyara", "enchantment.minecraft.sweeping": "Gbigba Eti", "enchantment.minecraft.sweeping_edge": "Sweeping Edge", "enchantment.minecraft.swift_sneak": "<PERSON> ajiwo", "enchantment.minecraft.thorns": "Ẹgún", "enchantment.minecraft.unbreaking": "<PERSON><PERSON>", "enchantment.minecraft.vanishing_curse": "Egún ti Ṣegbé", "enchantment.minecraft.wind_burst": "<PERSON> Burst", "entity.minecraft.acacia_boat": "Acacia Boat", "entity.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "<PERSON>un ti adalu", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Ihamọra Imurasilẹ", "entity.minecraft.arrow": "Ọfà", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "entity.minecraft.bamboo_raft": "Bamboo Raft", "entity.minecraft.bat": "Adan", "entity.minecraft.bee": "Bee", "entity.minecraft.birch_boat": "<PERSON> Boat", "entity.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Àkọsílẹ <PERSON><PERSON><PERSON>", "entity.minecraft.boat": "Boat", "entity.minecraft.bogged": "Bogged", "entity.minecraft.breeze": "<PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Wind Charge", "entity.minecraft.camel": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.cat": "Ologbo", "entity.minecraft.cave_spider": "<PERSON><PERSON>", "entity.minecraft.cherry_boat": "Cherry Boat", "entity.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "Minecart pẹlu àya", "entity.minecraft.chicken": "Adiẹ", "entity.minecraft.cod": "Cod", "entity.minecraft.command_block_minecart": "Minecart pẹlu Àṣẹ Àkọsílẹ", "entity.minecraft.cow": "<PERSON><PERSON><PERSON>", "entity.minecraft.creaking": "Creaking", "entity.minecraft.creaking_transient": "Creaking", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Dark Oak Boat", "entity.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "entity.minecraft.dolphin": "Ẹja", "entity.minecraft.donkey": "Kẹtẹkẹtẹ", "entity.minecraft.dragon_fireball": "Fireball dragoni", "entity.minecraft.drowned": "Ti danu", "entity.minecraft.egg": "Jọ ẹyin", "entity.minecraft.elder_guardian": "Alagbato agba", "entity.minecraft.end_crystal": "<PERSON>", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Evoker", "entity.minecraft.evoker_fangs": "<PERSON><PERSON><PERSON><PERSON> oluwa naa", "entity.minecraft.experience_bottle": "Ṣe igo kan siga", "entity.minecraft.experience_orb": "<PERSON><PERSON><PERSON>", "entity.minecraft.eye_of_ender": "<PERSON><PERSON> ti <PERSON>", "entity.minecraft.falling_block": "Àkọsílẹ Ti kuna", "entity.minecraft.falling_block_type": "Falling %s", "entity.minecraft.fireball": "Bọọlu ina", "entity.minecraft.firework_rocket": "Ise ina Rocket", "entity.minecraft.fishing_bobber": "Leefofo", "entity.minecraft.fox": "Akata", "entity.minecraft.frog": "Ọpọlọ", "entity.minecraft.furnace_minecart": "Minecart pẹlu Ileru", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON>", "entity.minecraft.glow_squid": "Alábá Squid", "entity.minecraft.goat": "Ewúrẹ", "entity.minecraft.guardian": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.happy_ghast": "<PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Minecart pẹlu Hopper", "entity.minecraft.horse": "Ẹṣin", "entity.minecraft.husk": "Ọkọ", "entity.minecraft.illusioner": "Oluranju", "entity.minecraft.interaction": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.iron_golem": "<PERSON><PERSON>", "entity.minecraft.item": "<PERSON><PERSON>", "entity.minecraft.item_display": "<PERSON><PERSON><PERSON>", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "Jungle Boat", "entity.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "entity.minecraft.killer_bunny": "<PERSON><PERSON>", "entity.minecraft.leash_knot": "<PERSON><PERSON> wiwun", "entity.minecraft.lightning_bolt": "Monomono", "entity.minecraft.lingering_potion": "Lingering Potion", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.magma_cube": "Magma Kuubu", "entity.minecraft.mangrove_boat": "Mangrove Boat", "entity.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "entity.minecraft.marker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Minecart", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Ìbaaka", "entity.minecraft.oak_boat": "Oak Boat", "entity.minecraft.oak_chest_boat": "Oak Boat with Chest", "entity.minecraft.ocelot": "Ocelot", "entity.minecraft.ominous_item_spawner": "Ominous <PERSON>em <PERSON>wner", "entity.minecraft.painting": "<PERSON><PERSON><PERSON>", "entity.minecraft.pale_oak_boat": "Pale Oak Boat", "entity.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "<PERSON><PERSON><PERSON>", "entity.minecraft.pig": "Ẹlẹdẹ", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "Iyat<PERSON>", "entity.minecraft.pillager": "Ikogun", "entity.minecraft.player": "Orin", "entity.minecraft.polar_bear": "Pola Agbateru", "entity.minecraft.potion": "Ikun", "entity.minecraft.pufferfish": "Pufferfish", "entity.minecraft.rabbit": "Ehor<PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Ẹja", "entity.minecraft.sheep": "Agutan", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Ṣiṣiriṣi <PERSON><PERSON><PERSON>", "entity.minecraft.silverfish": "Silverfish", "entity.minecraft.skeleton": "<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton_horse": "<PERSON><PERSON><PERSON>", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "Bọọlu ina kekere", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Egbon Golem", "entity.minecraft.snowball": "Bọọlu afẹsẹgba", "entity.minecraft.spawner_minecart": "Wagon pẹlu kan Monster Spawner", "entity.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON>", "entity.minecraft.spider": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "Splash Potion", "entity.minecraft.spruce_boat": "Spruce Boat", "entity.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "entity.minecraft.squid": "Ti ipilẹ aimọ", "entity.minecraft.stray": "Ṣáko", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "Tadpole", "entity.minecraft.text_display": "<PERSON><PERSON><PERSON>", "entity.minecraft.tnt": "Ologun TNT", "entity.minecraft.tnt_minecart": "Minecart pẹlu TNT", "entity.minecraft.trader_llama": "Oniṣowo Llama", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "Tropical Eja", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "Black Tang", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "Orilẹ-<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.12": "<PERSON><PERSON> parrot", "entity.minecraft.tropical_fish.predefined.13": "Ayaba Angelfish", "entity.minecraft.tropical_fish.predefined.14": "Pupa <PERSON>", "entity.minecraft.tropical_fish.predefined.15": "Pupa ti a sọ blenny", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.17": "Threadfin", "entity.minecraft.tropical_fish.predefined.18": "Tomati Clownfish", "entity.minecraft.tropical_fish.predefined.19": "Ẹja eja", "entity.minecraft.tropical_fish.predefined.2": "Blue Tang", "entity.minecraft.tropical_fish.predefined.20": "Yellowtail Parrotfish", "entity.minecraft.tropical_fish.predefined.21": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.4": "Cichlid", "entity.minecraft.tropical_fish.predefined.5": "Clownfish", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Emperor <PERSON>", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON> e<PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Clayfish", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "Iranran", "entity.minecraft.tropical_fish.type.stripey": "Ṣiṣan", "entity.minecraft.tropical_fish.type.sunstreak": "Sunstreak", "entity.minecraft.turtle": "Ẹkuru", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "<PERSON><PERSON>", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cartographer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.cleric": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.farmer": "Agbẹ", "entity.minecraft.villager.fisherman": "Apeja", "entity.minecraft.villager.fletcher": "<PERSON>", "entity.minecraft.villager.leatherworker": "A<PERSON>ọ <PERSON>", "entity.minecraft.villager.librarian": "Ikawe", "entity.minecraft.villager.mason": "Oluwa", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "<PERSON><PERSON>", "entity.minecraft.villager.shepherd": "Oluṣọ-agutan", "entity.minecraft.villager.toolsmith": "Irinṣẹ", "entity.minecraft.villager.weaponsmith": "Oniṣẹ ohun ija", "entity.minecraft.vindicator": "Olùdásílẹ̀", "entity.minecraft.wandering_trader": "Oniṣow<PERSON> On<PERSON>", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Wind Charge", "entity.minecraft.witch": "<PERSON><PERSON>", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON><PERSON> ti <PERSON>", "entity.minecraft.wolf": "<PERSON><PERSON><PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie_horse": "Ẹṣin <PERSON><PERSON>a", "entity.minecraft.zombie_villager": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombified_piglin": "<PERSON><PERSON><PERSON>", "entity.not_summonable": "Ko le pe nkankan ti iru %s", "event.minecraft.raid": "Igbogun ti", "event.minecraft.raid.defeat": "<PERSON><PERSON><PERSON>", "event.minecraft.raid.defeat.full": "Raid - Defeat", "event.minecraft.raid.raiders_remaining": "Awọn a<PERSON>ọni<PERSON> ti osi:%s", "event.minecraft.raid.victory": "Iṣẹgun", "event.minecraft.raid.victory.full": "Raid - Victory", "filled_map.buried_treasure": "Maapu si iṣura ti a pamọ", "filled_map.explorer_jungle": "Jungle Explorer Map", "filled_map.explorer_swamp": "Swamp Explorer Map", "filled_map.id": "No. #%s", "filled_map.level": "(Ipele %s/%s)", "filled_map.locked": "Titii pa", "filled_map.mansion": "Woodland Oluwakiri Maapu", "filled_map.monument": "Ocean Oluwakiri Ma<PERSON>u", "filled_map.scale": "Iwọn ni 1:%s", "filled_map.trial_chambers": "Trial Explorer Map", "filled_map.unknown": "Aimọ Maapu", "filled_map.village_desert": "Desert Village Map", "filled_map.village_plains": "Plains Village Map", "filled_map.village_savanna": "Savanna Village Map", "filled_map.village_snowy": "Snowy Village Map", "filled_map.village_taiga": "Taiga Village Map", "flat_world_preset.minecraft.bottomless_pit": "<PERSON><PERSON><PERSON> laisi isalẹ", "flat_world_preset.minecraft.classic_flat": "Classic Flat", "flat_world_preset.minecraft.desert": "Agin<PERSON>", "flat_world_preset.minecraft.overworld": "Agbegbe", "flat_world_preset.minecraft.redstone_ready": "Ṣetan okuta pupa", "flat_world_preset.minecraft.snowy_kingdom": "Ìjọba <PERSON><PERSON><PERSON>e", "flat_world_preset.minecraft.the_void": "Awọn I<PERSON>ọ", "flat_world_preset.minecraft.tunnelers_dream": "<PERSON><PERSON> al<PERSON>n", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON>", "flat_world_preset.unknown": "???", "gameMode.adventure": "<PERSON><PERSON>", "gameMode.changed": "Ipo ti ere rẹ ti ni imudojuiwọn si %s", "gameMode.creative": "<PERSON><PERSON>", "gameMode.hardcore": "<PERSON><PERSON>!", "gameMode.spectator": "<PERSON><PERSON> wiwo", "gameMode.survival": "<PERSON><PERSON>", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "<PERSON><PERSON> a<PERSON> il<PERSON>", "gamerule.blockExplosionDropDecay": "Ni awọn bug<PERSON>mu <PERSON>, diẹ ninu awọn bulọọki kii yoo ju ikogun wọn silẹ", "gamerule.blockExplosionDropDecay.description": "Diẹ ninu awọn silė lati awọn bulọọki ti o parun nipasẹ awọn bugbamu ti o ṣẹlẹ nipasẹ awọn ibaraenisepo Àkọsílẹ ti sọnu ninu bugbamu naa.", "gamerule.category.chat": "Iwiregbe", "gamerule.category.drops": "Silė", "gamerule.category.misc": "<PERSON><PERSON><PERSON><PERSON>", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "Player", "gamerule.category.spawning": "Fifẹ", "gamerule.category.updates": "<PERSON><PERSON><PERSON><PERSON> imudoju<PERSON><PERSON><PERSON><PERSON> a<PERSON>e", "gamerule.commandBlockOutput": "<PERSON><PERSON><PERSON> idena pipaṣẹ igbohun<PERSON>fe", "gamerule.commandModificationBlockLimit": "Òfin Iyipada Àkọsílẹ iye", "gamerule.commandModificationBlockLimit.description": "Nọmba awọn bulọọki ti o le yipada ni ẹẹkan nipasẹ aṣẹ kan, gẹgẹbi kikun tabi oniye.", "gamerule.disableElytraMovementCheck": "Mu iṣipopada elytra ṣiṣẹ", "gamerule.disablePlayerMovementCheck": "Disable player movement check", "gamerule.disableRaids": "Mu Raids", "gamerule.doDaylightCycle": "Ilosiwaju akoko ti <PERSON>", "gamerule.doEntityDrops": "<PERSON> ohun elo ẹrọ silẹ", "gamerule.doEntityDrops.description": "Awọn iṣakoso ṣubu lati awọn ọkọ-akọọlẹ (pẹlu awọn atokọ), awọn <PERSON><PERSON>u ohun kan, awọn ọkọ oju omi, ati bẹbẹ lọ.", "gamerule.doFireTick": "I<PERSON><PERSON><PERSON><PERSON>w<PERSON><PERSON> ina", "gamerule.doImmediateRespawn": "Tun ṣe atunṣe lẹsẹkẹsẹ", "gamerule.doInsomnia": "Spant phantoms", "gamerule.doLimitedCrafting": "<PERSON><PERSON> oh<PERSON>lo fun iṣẹ ọwọ", "gamerule.doLimitedCrafting.description": "Ti o ba mu ṣiṣẹ, awọn oṣere yoo ni anfani lati ṣe awọn ilana ṣiṣi silẹ nikan.", "gamerule.doMobLoot": "<PERSON> i<PERSON>gun eniyan", "gamerule.doMobLoot.description": "Ṣakoso awọn orisun silẹ lati awọn agbajo eniyan, pẹlu awọn orbs iriri.", "gamerule.doMobSpawning": "Spawn agbajo eniyan", "gamerule.doMobSpawning.description": "Diẹ ninu awọn ile-iṣẹ le ni awọn ofin lọtọ.", "gamerule.doPatrolSpawning": "Ibisi pillager patrols", "gamerule.doTileDrops": "<PERSON>wọn bulọọki silẹ", "gamerule.doTileDrops.description": "Ṣakoso awọn orisun orisun silẹ lati awọn bulọọki, pẹlu awọn orbs iriri.", "gamerule.doTraderSpawning": "Spawn rin kakiri awọn on<PERSON>", "gamerule.doVinesSpread": "<PERSON><PERSON><PERSON> tan", "gamerule.doVinesSpread.description": "Awọn iṣakoso boya tabi kii ṣe bulọki Vines ntan laileto si awọn bulọọki ti o wa nitosi. Ko kan iru awọn bulọọki ajara miiran gẹgẹbi Awọn Ajara Ẹkun, <PERSON><PERSON>ọ<PERSON><PERSON>, ati bẹbẹ lọ.", "gamerule.doWardenSpawning": "Spawn Wardens", "gamerule.doWeatherCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> oju ojo", "gamerule.drowningDamage": "Ṣe ibajẹ riru omi", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> Ender <PERSON> vanish on death", "gamerule.enderPearlsVanishOnDeath.description": "Whether Ender <PERSON> thrown by a player vanish when that player dies.", "gamerule.entitiesWithPassengersCanUsePortals": "Entities with passengers can use portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Allow entities with passengers to teleport through Nether Portals, End Portals, and End Gateways.", "gamerule.fallDamage": "Ṣe ibajẹ isubu", "gamerule.fireDamage": "Ṣe ibajẹ ina", "gamerule.forgiveDeadPlayers": "<PERSON><PERSON> awọn ẹrọ orin ti o ku", "gamerule.forgiveDeadPlayers.description": "Awọn agba didoju ti ibinu binu da ibinu nigbati ẹrọ orin ti o fojusi naa ku nitosi.", "gamerule.freezeDamage": "Ṣe ibajẹ ina", "gamerule.globalSoundEvents": "Awọn iṣẹlẹ ohun agbaye", "gamerule.globalSoundEvents.description": "Nig<PERSON>i awọn iṣẹlẹ ere kan ba ṣẹlẹ, bii ag<PERSON><PERSON> ọ<PERSON>, a gbọ ohun naa nibi gbogbo.", "gamerule.keepInventory": "<PERSON><PERSON><PERSON> atokọ lẹhin iku", "gamerule.lavaSourceConversion": "Lava yipada si orisun", "gamerule.lavaSourceConversion.description": "Nigbati lava ti nṣàn ti yika ni ẹgbẹ meji nipasẹ awọn orisun lava o yipada si orisun kan.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "Igbohunsafefe admin ase", "gamerule.maxCommandChainLength": "Opin iwọn pq pipaṣẹ", "gamerule.maxCommandChainLength.description": "Kan si pipaṣẹ awọn ẹwọn Àkọsílẹ ati awọn iṣẹ.", "gamerule.maxCommandForkCount": "Command context limit", "gamerule.maxCommandForkCount.description": "Maximum number of contexts that can be used by commands like 'execute as'.", "gamerule.maxEntityCramming": "Ẹnu-ilẹ cramming iloro", "gamerule.minecartMaxSpeed": "Minecart max speed", "gamerule.minecartMaxSpeed.description": "Maximum default speed of a moving Minecart on land.", "gamerule.mobExplosionDropDecay": "Ninu awọn bugbamu a<PERSON>, diẹ ninu awọn bulọọki kii yoo ju ikogun wọn silẹ", "gamerule.mobExplosionDropDecay.description": "Diẹ ninu awọn silė lati awọn bulọọki ti o bajẹ nipasẹ awọn bugbamu ti o ṣẹlẹ nipasẹ awọn agbajo eniyan ti sọnu ninu bugbamu naa.", "gamerule.mobGriefing": "Gba awọn iṣe agbajo eniyan iparun", "gamerule.naturalRegeneration": "Ṣe atunṣe ilera", "gamerule.playersNetherPortalCreativeDelay": "Player's Nether portal delay in creative mode", "gamerule.playersNetherPortalCreativeDelay.description": "Time (in ticks) that a creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersNetherPortalDefaultDelay": "Player's Nether portal delay in non-creative mode", "gamerule.playersNetherPortalDefaultDelay.description": "Time (in ticks) that a non-creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersSleepingPercentage": "Iye awọn eniyan ti n sun", "gamerule.playersSleepingPercentage.description": "Iwọn ọgọrun ti awọn oṣere ti o gbọdọ sùn lati foju alẹ.", "gamerule.projectilesCanBreakBlocks": "Projectiles can break blocks", "gamerule.projectilesCanBreakBlocks.description": "Controls whether impact projectiles will destroy blocks that are destructible by them.", "gamerule.randomTickSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON>n ami ami iyara", "gamerule.reducedDebugInfo": "Din yokokoro info", "gamerule.reducedDebugInfo.description": "Aw<PERSON>n akoonu ifilelẹ ti iboju yokokoro.", "gamerule.sendCommandFeedback": "Firanṣẹ esi esi", "gamerule.showDeathMessages": "Ṣe afihan awọn ifiranṣẹ iku", "gamerule.snowAccumulationHeight": "Egbon ikojọpọ iga", "gamerule.snowAccumulationHeight.description": "<PERSON>g<PERSON><PERSON> o ba n ṣan, awọn ipele ti egbon n dagba lori ilẹ titi di pupọ julọ nọmba awọn ipele yii.", "gamerule.spawnChunkRadius": "Spawn chunk radius", "gamerule.spawnChunkRadius.description": "Amount of chunks that stay loaded around the overworld spawn position.", "gamerule.spawnRadius": "Tun rediosi ipo", "gamerule.spawnRadius.description": "Controls the size of the area around the spawn point that players can spawn in.", "gamerule.spectatorsGenerateChunks": "Gba awọn oluwo laaye lati ṣe aaye ilẹ", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "Ni awọn bugbamu TNT, diẹ ninu awọn bulọọki kii yoo ju ikogun wọn silẹ", "gamerule.tntExplosionDropDecay.description": "Diẹ ninu awọn silė lati awọn bulọọki ti o bajẹ nipasẹ awọn bugbamu ti o ṣẹlẹ nipasẹ TNT ti sọnu ninu bugbamu naa.", "gamerule.universalAnger": "<PERSON><PERSON><PERSON> g<PERSON> a<PERSON>e", "gamerule.universalAnger.description": "Awọn agbajọ didoju binu binu eyikeyi oṣere to wa nitosi, kii ṣe oṣere ti o binu wọn nikan. Ṣiṣẹ dara julọ ti o ba forgiveDeadPlayers jẹ alaabo.", "gamerule.waterSourceConversion": "<PERSON>mi yipada si orisun", "gamerule.waterSourceConversion.description": "Nig<PERSON>i omi ṣiṣan ti yika ni ẹgbẹ meji nipasẹ awọn orisun omi o yipada si orisun kan.", "generator.custom": "<PERSON><PERSON>a", "generator.customized": "<PERSON>i <PERSON><PERSON>a <PERSON>", "generator.minecraft.amplified": "TI O DARA", "generator.minecraft.amplified.info": "Akiyesi: Kan fun igbadun! <PERSON><PERSON> k<PERSON> kọn<PERSON>a.", "generator.minecraft.debug_all_block_states": "<PERSON><PERSON>", "generator.minecraft.flat": "<PERSON><PERSON><PERSON> pup<PERSON>", "generator.minecraft.large_biomes": "<PERSON><PERSON><PERSON><PERSON> ohun elo giga", "generator.minecraft.normal": "<PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.single_biome_surface": "<PERSON><PERSON>", "generator.single_biome_caves": "Ẹṣọ", "generator.single_biome_floating_islands": "<PERSON><PERSON><PERSON><PERSON> erekusu loju omi", "gui.abuseReport.attestation": "By submitting this report, you confirm that the information you have provided is accurate and complete to the best of your knowledge.", "gui.abuseReport.comments": "Comments", "gui.abuseReport.describe": "Sharing details will help us make a well-informed decision.", "gui.abuseReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.abuseReport.discard.discard": "Leave and Discard Report", "gui.abuseReport.discard.draft": "Save as Draft", "gui.abuseReport.discard.return": "Continue Editing", "gui.abuseReport.discard.title": "Discard report and comments?", "gui.abuseReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.abuseReport.draft.discard": "Discard", "gui.abuseReport.draft.edit": "Continue Editing", "gui.abuseReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.abuseReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.abuseReport.draft.title": "Edit draft chat report?", "gui.abuseReport.error.title": "Iṣoro fifiranṣẹ ijabọ rẹ", "gui.abuseReport.message": "Where did you observe the bad behavior?\nThis will help us in researching your case.", "gui.abuseReport.more_comments": "Please describe what happened:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "You are reporting \"%s\".", "gui.abuseReport.name.title": "Report Inappropriate Player Name", "gui.abuseReport.observed_what": "Why are you reporting this?", "gui.abuseReport.read_info": "Learn About Reporting", "gui.abuseReport.reason.alcohol_tobacco_drugs": "<PERSON>òg<PERSON>n tabi oti", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "Ẹnikan n gba awọn miiran niyanju lati ṣe alabapin ninu awọn iṣe ti o jọmọ oogun ti ko tọ tabi ni iyanju mimu mimu ti ko dagba.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "Ibalopọ ọmọ tabi ilokulo", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "Ẹnikan n sọrọ nipa tabi bibẹẹkọ n ṣe igbega iwa aiṣedeede ti o kan awọn ọmọde.", "gui.abuseReport.reason.defamation_impersonation_false_information": "Ibajẹ, a<PERSON><PERSON>e, tabi alaye eke", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "Ẹnì kan ń ba orúkọ ẹlòmíì jẹ́, ó ń díbọ́n pé òun jẹ́ ẹnì kan tí kì í ṣe ẹnì kan, tàbí tí ń pín ìsọfúnni èké ní ṣíṣe pẹ̀lú ète láti ṣàníyàn tàbí ṣi àwọn ẹlòmíràn lọ́nà.", "gui.abuseReport.reason.description": "Apejuwe:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON><PERSON> eke", "gui.abuseReport.reason.generic": "I want to report them", "gui.abuseReport.reason.generic.description": "I'm annoyed with them / they have done something I do not like.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON>palara tabi i<PERSON>ilaya", "gui.abuseReport.reason.harassment_or_bullying.description": "Ẹnikan n tiju, <PERSON><PERSON><PERSON><PERSON>, tabi ipanilaya iwọ tabi ẹlomiran. Eyi pẹlu nigbati ẹnikan n gbiyanju leralera lati kan si ọ tabi ẹlomiiran laisi igbanilaaye tabi fifiranṣẹ alaye ti ara ẹni nipa rẹ tabi ẹlomiiran laisi aṣẹ (“doxing”).", "gui.abuseReport.reason.hate_speech": "Ọrọ ikorira", "gui.abuseReport.reason.hate_speech.description": "Ẹnikan n kọlu ọ tabi oṣere miiran ti o da lori awọn abuda idanimọ wọn, bii ẹsin, ije, tabi i<PERSON>.", "gui.abuseReport.reason.imminent_harm": "Ipalara ti o sunmọ - <PERSON>rokeke lati ṣe ipalara fun awọn miiran", "gui.abuseReport.reason.imminent_harm.description": "Ẹnikan n halẹ lati ṣe ipalara fun ọ tabi ẹlomiran ni igbesi aye gidi.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "<PERSON><PERSON><PERSON> timotimo ti kii ṣe ifọkanbalẹ", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "Ẹnikan n sọrọ nipa, pinpin, tabi bibẹẹkọ igbega awọn aworan ikọkọ ati timotimo.", "gui.abuseReport.reason.self_harm_or_suicide": "Ipalara ti o sunmọ - ipalara ti ara ẹni tabi igbẹmi ara ẹni", "gui.abuseReport.reason.self_harm_or_suicide.description": "Ẹnikan n halẹ lati ṣe ipalara fun ara wọn ni igbesi aye gidi tabi sọrọ nipa ipalara fun ara wọn ni igbesi aye gidi.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "Ipanilaya tabi iwa-ipa extremism", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "Ẹnikan n sọrọ nipa, <PERSON><PERSON><PERSON><PERSON>, tabi halẹ lati ṣe awọn iṣe ipanilaya tabi ipanilaya iwa-ipa fun iṣelu, ẹsin, imọ-jinlẹ, tabi awọn idi miiran.", "gui.abuseReport.reason.title": "<PERSON> Ẹka <PERSON><PERSON>in", "gui.abuseReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.abuseReport.select_reason": "Select Report Category", "gui.abuseReport.send": "Send Report", "gui.abuseReport.send.comment_too_long": "Please shorten the comment", "gui.abuseReport.send.error_message": "Aṣiṣe kan ti da pada lakoko fifiranṣẹ ijabọ rẹ:", "gui.abuseReport.send.generic_error": "Ibapade aṣiṣe airotẹlẹ lakoko fifiranṣẹ ijabọ rẹ.", "gui.abuseReport.send.http_error": "Aṣiṣe HTTP airotẹlẹ waye lakoko fifiranṣẹ ijabọ rẹ.", "gui.abuseReport.send.json_error": "<PERSON><PERSON><PERSON><PERSON> fifuye isanwo aiṣede<PERSON> lakoko fifiranṣẹ ijabọ rẹ.", "gui.abuseReport.send.no_reason": "Please select a report category", "gui.abuseReport.send.not_attested": "Please read the text above and tick the checkbox to be able to send the report", "gui.abuseReport.send.service_unavailable": "<PERSON> le de ọdọ iṣẹ Ijabọ Abuse. Jọwọ rii daju pe o ti sopọ si intanẹẹti ki o tun gbiyanju lẹẹkansi.", "gui.abuseReport.sending.title": "Fifiranṣẹ ijabọ rẹ...", "gui.abuseReport.sent.title": "<PERSON><PERSON><PERSON>", "gui.abuseReport.skin.title": "Report Player Skin", "gui.abuseReport.title": "Report Player", "gui.abuseReport.type.chat": "Chat Messages", "gui.abuseReport.type.name": "Player Name", "gui.abuseReport.type.skin": "Player Skin", "gui.acknowledge": "Jẹwọ", "gui.advancements": "<PERSON><PERSON><PERSON><PERSON>", "gui.all": "<PERSON><PERSON>", "gui.back": "Pada", "gui.banned.description": "%s\n\n%s\n\nKọ ẹkọ diẹ sii ni ọna asopọ atẹle: %s", "gui.banned.description.permanent": "A ti fi ofin de akọọlẹ rẹ patapata, eyiti o tumọ si pe o ko le ṣere lori ayelujara tabi darapọ mọ Realms.", "gui.banned.description.reason": "Laipẹ a gba ijabọ fun ihuwasi buburu nipasẹ akọọlẹ rẹ. Awọn oniwontunniwonsi ti ṣe atunyẹwo ọran rẹ ni bayi ati ṣe idanimọ rẹ bi %s, eyiti o lodi si Awọn Ilana Agbegbe Minecraft.", "gui.banned.description.reason_id": "Koodu: %s", "gui.banned.description.reason_id_message": "Koodu: %s - %s", "gui.banned.description.temporary": "%s <PERSON>iti di igba naa, o ko le ṣere lori ayelujara tabi darapọ mọ Realms.", "gui.banned.description.temporary.duration": "Iwe akọọlẹ rẹ ti daduro fun igba diẹ ati pe yoo tun mu ṣiṣẹ ni %s.", "gui.banned.description.unknownreason": "Laipẹ a gba ijabọ fun ihuwasi buburu nipasẹ akọọlẹ rẹ. Awọn oniwontunniwonsi ti ṣe atunyẹwo ọran rẹ ni bayi ati ṣe idanimọ pe o lodi si Awọn ajohunše Agbegbe Minecraft.", "gui.banned.name.description": "Your current name - \"%s\" - violates our Community Standards. You can play singleplayer, but will need to change your name to play online.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.name.title": "Name Not Allowed in Multiplayer", "gui.banned.reason.defamation_impersonation_false_information": "Impersonation tabi pinpin alaye lati lo nilokulo tabi ṣi awọn miiran lọna", "gui.banned.reason.drugs": "Itọkasi si arufin oloro", "gui.banned.reason.extreme_violence_or_gore": "Awọn apejuwe ti gidi-aye iwa-ipa ti o pọju tabi gore", "gui.banned.reason.false_reporting": "Pup<PERSON> eke tabi awọn ijab<PERSON> aiṣedeede", "gui.banned.reason.fraud": "Gbigba arekereke tabi lilo akoonu", "gui.banned.reason.generic_violation": "Titako Awọn <PERSON>", "gui.banned.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON> tí a lò ní ìdarí, ọ̀nà <PERSON>pal<PERSON>ra", "gui.banned.reason.hate_speech": "Ọrọ i<PERSON><PERSON> tabi iyasoto", "gui.banned.reason.hate_terrorism_notorious_figure": "Awọn itọkasi si awọn ẹgbẹ i<PERSON>ira, awọn ẹgbẹ apanilaya, tabi awọn eeya o<PERSON>ki", "gui.banned.reason.imminent_harm_to_person_or_property": "<PERSON><PERSON><PERSON> lati fa ipalara gidi-aye si eniyan tabi ohun-ini", "gui.banned.reason.nudity_or_pornography": "Ṣafihan awọn ohun elo onihoho tabi onihoho", "gui.banned.reason.sexually_inappropriate": "<PERSON><PERSON><PERSON><PERSON> koko-ọr<PERSON> tabi akoonu ti iseda ibalop<PERSON>", "gui.banned.reason.spam_or_advertising": "Spam tabi i<PERSON>o", "gui.banned.skin.description": "Your current skin violates our Community Standards. You can still play with a default skin, or select a new one.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.skin.title": "Skin Not Allowed", "gui.banned.title.permanent": "Ti gbesele akọọlẹ patapata", "gui.banned.title.temporary": "Akọọlẹ ti daduro fun igba diẹ", "gui.cancel": "F<PERSON><PERSON>", "gui.chatReport.comments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.describe": "<PERSON><PERSON> awọn alaye yoo ran wa lọwọ lati ṣe ipinnu ti o ni oye daradara.", "gui.chatReport.discard.content": "Ti o ba lọ kuro, iwọ yoo padanu ijabọ yii ati awọn asọye rẹ.\nṢe o da ọ loju pe o fẹ lọ kuro?", "gui.chatReport.discard.discard": "Fi ki o si jabọ Iroyin", "gui.chatReport.discard.draft": "Fipamọ Bi Akọpamọ", "gui.chatReport.discard.return": "Tesiwaju Ṣiṣatunṣe", "gui.chatReport.discard.title": "<PERSON><PERSON><PERSON> ijabọ ati awọn asọye bi?", "gui.chatReport.draft.content": "Ṣe iwọ yoo fẹ lati tẹsiwaju ṣiṣatunṣe ijabọ ti o wa tẹlẹ tabi sọ ọ nù ki o ṣẹda ọkan tuntun?", "gui.chatReport.draft.discard": "Discard", "gui.chatReport.draft.edit": "Tesiwaju Ṣiṣatunṣe", "gui.chatReport.draft.quittotitle.content": "Ṣe iwọ yoo fẹ lati tẹsiwaju ṣiṣatunṣe rẹ tabi sọ ọ nù?", "gui.chatReport.draft.quittotitle.title": "O ni ijabọ iwiregbe kikọ ti yoo sọnu ti o ba dawọ silẹ", "gui.chatReport.draft.title": "Ṣatunkọ iroyin iwiregbe osere?", "gui.chatReport.more_comments": "Jọwọ ṣapejuwe ohun ti o ṣẹlẹ:", "gui.chatReport.observed_what": "Kini idi ti o fi royin eyi?", "gui.chatReport.read_info": "Kọ ẹkọ <PERSON>", "gui.chatReport.report_sent_msg": "A ti gba ijabọ rẹ ni aṣeyọri. E dupe!\n\nẸgbẹ wa yoo ṣe ayẹwo rẹ ni kete bi o ti ṣee.", "gui.chatReport.select_chat": "<PERSON>n ifiranṣẹ Iwiregbe lati jabo", "gui.chatReport.select_reason": "Select Report Category", "gui.chatReport.selected_chat": "%s Ifiranṣẹ (awọn) iwiregbe ti yan lati jabo", "gui.chatReport.send": "Firanṣẹ <PERSON><PERSON><PERSON>", "gui.chatReport.send.comments_too_long": "Jọwọ kuru ọrọ as<PERSON><PERSON> naa", "gui.chatReport.send.no_reason": "Jọw<PERSON> yan ẹka ijabọ kan", "gui.chatReport.send.no_reported_messages": "Jọw<PERSON> yan o kere ju ifiranṣẹ iwiregbe kan lati jabo", "gui.chatReport.send.too_many_messages": "G<PERSON>yan<PERSON> lati <PERSON><PERSON> ọpọlọpọ awọn ifiranṣẹ sinu ijabọ naa", "gui.chatReport.title": "Player <PERSON><PERSON><PERSON>", "gui.chatSelection.context": "Awọn ifiranšẹ agbegbe yiyan yii yoo wa pẹlu lati pese afikun ọrọ-ọrọ", "gui.chatSelection.fold": "%s ifiranṣẹ(s) pamọ", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s da<PERSON><PERSON> mọ iwiregbe naa", "gui.chatSelection.message.narrate": "%s sọ pé: %s ni %s", "gui.chatSelection.selected": "%s/%s ifiranṣẹ(awọn) ti yan", "gui.chatSelection.title": "<PERSON>n ifiranṣẹ Iwiregbe lati jabo", "gui.continue": "Tesiwaju", "gui.copy_link_to_clipboard": "Daakọ Ọna asopọ si <PERSON>ru", "gui.days": "%s ọj<PERSON> (awọn)", "gui.done": "Me see", "gui.down": "Si isalẹ", "gui.entity_tooltip.type": "A oriṣi:%s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rejected %s files", "gui.fileDropFailure.title": "Failed to add files", "gui.hours": "%s wakati", "gui.loadingMinecraft": "Loading Minecraft", "gui.minutes": "%s iseju", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s b<PERSON><PERSON><PERSON>", "gui.narrate.editBox": "%s ṣatunkọ apoti: %s", "gui.narrate.slider": "%s <PERSON><PERSON><PERSON><PERSON>", "gui.narrate.tab": "%s tab", "gui.no": "<PERSON><PERSON>", "gui.none": "ոչ", "gui.ok": "Ok", "gui.open_report_dir": "Open Report Directory", "gui.proceed": "Tesiwaju", "gui.recipebook.moreRecipes": "Ọtun tẹ fun alaye diẹ sii", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Ṣewadii...", "gui.recipebook.toggleRecipes.all": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.blastable": "<PERSON><PERSON><PERSON><PERSON> ilana ilana <PERSON>", "gui.recipebook.toggleRecipes.craftable": "<PERSON><PERSON><PERSON> Craftable", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smokable": "<PERSON><PERSON><PERSON><PERSON> ilana ẹfin", "gui.report_to_server": "Report To Server", "gui.socialInteractions.blocking_hint": "Ṣakoso pẹlu akọọlẹ Microsoft", "gui.socialInteractions.empty_blocked": "Ko si awọn ẹrọ orin ti a dina ni iwiregbe", "gui.socialInteractions.empty_hidden": "Ko si awọn ẹrọ orin ti o farapamọ ni iwiregbe", "gui.socialInteractions.hidden_in_chat": "Iwiregbe awọn ifiranṣẹ lati %s yoo farasin", "gui.socialInteractions.hide": "<PERSON><PERSON><PERSON> ni <PERSON>", "gui.socialInteractions.narration.hide": "Tọju awọn ifiranṣẹ lati %s", "gui.socialInteractions.narration.report": "Iroyin player %s", "gui.socialInteractions.narration.show": "Ṣe afihan awọn ifiranṣẹ lati %s", "gui.socialInteractions.report": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.search_empty": "Ko le ri awọn oṣere kankan pẹlu orukọ yẹn", "gui.socialInteractions.search_hint": "Wa...", "gui.socialInteractions.server_label.multiple": "Awọn ẹrọ orin%s -%s", "gui.socialInteractions.server_label.single": "Ẹrọ orin%s -%s", "gui.socialInteractions.show": "Ṣe a<PERSON>han ni <PERSON>", "gui.socialInteractions.shown_in_chat": "Iwiregbe awọn ifiranṣẹ %s lati yoo han", "gui.socialInteractions.status_blocked": "<PERSON><PERSON>", "gui.socialInteractions.status_blocked_offline": "<PERSON>i dina mọ - <PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.status_hidden_offline": "Farasin - <PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.status_offline": "<PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_all": "Mee at", "gui.socialInteractions.tab_blocked": "Ti dina mọ", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON><PERSON>", "gui.socialInteractions.title": "Awọn ibaraẹnisọrọ Awujọ", "gui.socialInteractions.tooltip.hide": "<PERSON><PERSON><PERSON> awọn ifiranṣẹ", "gui.socialInteractions.tooltip.report": "Iroyin player", "gui.socialInteractions.tooltip.report.disabled": "Iṣẹ ijabọ ko si", "gui.socialInteractions.tooltip.report.no_messages": "Ko si awọn ifiranṣẹ iroyin lati ọdọ ẹrọ orin %s", "gui.socialInteractions.tooltip.report.not_reportable": "Ẹrọ orin yii ko le ṣe ijabọ, nitori awọn ifiranṣẹ iwiregbe wọn ko le rii daju lori o<PERSON>pin yii", "gui.socialInteractions.tooltip.show": "Ṣe a<PERSON>han awọn ifiranṣẹ", "gui.stats": "<PERSON><PERSON><PERSON><PERSON>", "gui.toMenu": "Pada si atokọ olupin rẹ", "gui.toRealms": "Pada si Akojọ Realms", "gui.toTitle": "Pada wa\nsi iboju akọọlẹ", "gui.toWorld": "<PERSON>da si <PERSON>", "gui.togglable_slot": "Click to disable slot", "gui.up": "Soke", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "Bẹẹni", "hanging_sign.edit": "Ṣatunkọ Ifiranṣẹ <PERSON><PERSON>", "instrument.minecraft.admire_goat_horn": "Ṣe akiyesi", "instrument.minecraft.call_goat_horn": "<PERSON>pe", "instrument.minecraft.dream_goat_horn": "Ala", "instrument.minecraft.feel_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Wá", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON>", "inventory.binSlot": "<PERSON> kan", "inventory.hotbarInfo": "Fi awọn ohun elo pamọ pẹlu lilo%1$s +%2$s", "inventory.hotbarSaved": "Awọn irinṣẹ ti a fipamọ (mu pada -%1$s +%2$s)", "item.canBreak": "Le fọ:", "item.canPlace": "Le ṣee gbe sori:", "item.canUse.unknown": "Unknown", "item.color": "Awọ: %s", "item.components": "%s component(s)", "item.disabled": "<PERSON><PERSON>", "item.durability": "Agbara: %s / %s", "item.dyed": "Dyed", "item.minecraft.acacia_boat": "Okọ akasia", "item.minecraft.acacia_chest_boat": "Acacia ọkọ oju-omi kekere pẹlu àyà", "item.minecraft.allay_spawn_egg": "Allay Ẹyin Spawn", "item.minecraft.amethyst_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.angler_pottery_shard": "<PERSON><PERSON> o <PERSON>", "item.minecraft.angler_pottery_sherd": "<PERSON><PERSON> a<PERSON> o <PERSON>", "item.minecraft.apple": "<PERSON><PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "<PERSON> o <PERSON>", "item.minecraft.archer_pottery_sherd": "<PERSON> a<PERSON> o <PERSON>", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON>", "item.minecraft.armadillo_spawn_egg": "Armadillo Spawn Egg", "item.minecraft.armor_stand": "Ihamọra Imurasilẹ", "item.minecraft.arms_up_pottery_shard": "Arms Up apadì o Shard", "item.minecraft.arms_up_pottery_sherd": "Arms Up apadì o Sherd", "item.minecraft.arrow": "Ọfa", "item.minecraft.axolotl_bucket": "Garawa ti Axolotl", "item.minecraft.axolotl_spawn_egg": "Axolotl Spawn Ẹyin", "item.minecraft.baked_potato": "<PERSON><PERSON> Ọdunkun", "item.minecraft.bamboo_chest_raft": "Bamboo Raft pẹlu àyà", "item.minecraft.bamboo_raft": "Oparun Raft", "item.minecraft.bat_spawn_egg": "Ẹyin pe pipe", "item.minecraft.bee_spawn_egg": "Ẹyin Bee Spawn", "item.minecraft.beef": "<PERSON><PERSON>", "item.minecraft.beetroot": "Beetroot", "item.minecraft.beetroot_seeds": "Beetroot Irugbin", "item.minecraft.beetroot_soup": "<PERSON><PERSON>", "item.minecraft.birch_boat": "Oko birch", "item.minecraft.birch_chest_boat": "Ọkọ oju omi Birch pẹlu àyà", "item.minecraft.black_bundle": "Black Bundle", "item.minecraft.black_dye": "Dudu dai", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "<PERSON> a<PERSON> o <PERSON>hard", "item.minecraft.blade_pottery_sherd": "<PERSON> apad<PERSON> o <PERSON>rd", "item.minecraft.blaze_powder": "Blaze lulú", "item.minecraft.blaze_rod": "Blaze ọ<PERSON>", "item.minecraft.blaze_spawn_egg": "Ẹyin pe kan ina", "item.minecraft.blue_bundle": "Blue Bundle", "item.minecraft.blue_dye": "Awọ bulu", "item.minecraft.blue_egg": "Blue Egg", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "Bogged Spawn Egg", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolt Armor Trim", "item.minecraft.bone": "<PERSON><PERSON><PERSON>", "item.minecraft.bone_meal": "Ounjẹ <PERSON><PERSON>gun", "item.minecraft.book": "<PERSON>we", "item.minecraft.bordure_indented_banner_pattern": "Bordure Indented Banner Pattern", "item.minecraft.bow": "Teriba", "item.minecraft.bowl": "<PERSON><PERSON><PERSON>", "item.minecraft.bread": "Búrẹ́dì", "item.minecraft.breeze_rod": "<PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "Breeze Spawn Egg", "item.minecraft.brewer_pottery_shard": "<PERSON> a<PERSON> o <PERSON>", "item.minecraft.brewer_pottery_sherd": "<PERSON> a<PERSON> o <PERSON>", "item.minecraft.brewing_stand": "<PERSON><PERSON><PERSON><PERSON> imurasilẹ", "item.minecraft.brick": "<PERSON><PERSON>", "item.minecraft.brown_bundle": "<PERSON> Bundle", "item.minecraft.brown_dye": "Awọ dai", "item.minecraft.brown_egg": "<PERSON>", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle": "<PERSON><PERSON><PERSON>", "item.minecraft.bundle.empty": "Empty", "item.minecraft.bundle.empty.description": "Can hold a mixed stack of items", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "<PERSON><PERSON> apadì o <PERSON>", "item.minecraft.burn_pottery_sherd": "Iná apadì o <PERSON>", "item.minecraft.camel_spawn_egg": "Rakunmi Spawn Ẹyin", "item.minecraft.carrot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Karooti lori Ọpá kan", "item.minecraft.cat_spawn_egg": "Ṣẹda o nran kan", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Ẹyin pipe ni agbọn egungun kan", "item.minecraft.chainmail_boots": "Bata pq", "item.minecraft.chainmail_chestplate": "Ipa-<PERSON><PERSON><PERSON>", "item.minecraft.chainmail_helmet": "<PERSON><PERSON><PERSON> i<PERSON>i", "item.minecraft.chainmail_leggings": "<PERSON>un elo gbigbe", "item.minecraft.charcoal": "Eedu", "item.minecraft.cherry_boat": "Ọkọ oju o<PERSON>", "item.minecraft.cherry_chest_boat": "Àgbálùmo ọkọ oju-omi kekere pẹlu àyà", "item.minecraft.chest_minecart": "Minecart pẹlu àya", "item.minecraft.chicken": "<PERSON><PERSON>", "item.minecraft.chicken_spawn_egg": "Ẹyin pipe kan gboo", "item.minecraft.chorus_fruit": "Chorus Eso", "item.minecraft.clay_ball": "<PERSON><PERSON><PERSON>", "item.minecraft.clock": "Aago", "item.minecraft.coal": "Alóró", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.coast_armor_trim_smithing_template.new": "Coast Armor Trim", "item.minecraft.cocoa_beans": "<PERSON><PERSON>", "item.minecraft.cod": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.cod_bucket": "Garawa pẹlu cod", "item.minecraft.cod_spawn_egg": "Ẹyin pipe fun cod", "item.minecraft.command_block_minecart": "Minecart pẹlu Àṣẹ Àkọsílẹ", "item.minecraft.compass": "K<PERSON><PERSON><PERSON>", "item.minecraft.cooked_beef": "Steak", "item.minecraft.cooked_chicken": "<PERSON><PERSON> jinna", "item.minecraft.cooked_cod": "Ṣunna ẹja kan", "item.minecraft.cooked_mutton": "<PERSON><PERSON> jinna", "item.minecraft.cooked_porkchop": "<PERSON><PERSON> ẹlẹdẹ", "item.minecraft.cooked_rabbit": "<PERSON><PERSON><PERSON> jinna", "item.minecraft.cooked_salmon": "<PERSON>i ti a jinna", "item.minecraft.cookie": "<PERSON><PERSON>", "item.minecraft.copper_ingot": "<PERSON><PERSON><PERSON>", "item.minecraft.cow_spawn_egg": "Ẹyin pipe kan Maalu", "item.minecraft.creaking_spawn_egg": "Creaking Spawn Egg", "item.minecraft.creeper_banner_pattern": "Apẹrẹ Apẹrẹ", "item.minecraft.creeper_banner_pattern.desc": "Gbigba a<PERSON>bara <PERSON>", "item.minecraft.creeper_banner_pattern.new": "Creeper Charge Banner Pattern", "item.minecraft.creeper_spawn_egg": "Ẹyin pipe Creeper", "item.minecraft.crossbow": "Crossbow", "item.minecraft.crossbow.projectile": "Ise agbese:", "item.minecraft.crossbow.projectile.multiple": "Projectile: %s x %s", "item.minecraft.crossbow.projectile.single": "Projectile: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON>", "item.minecraft.cyan_dye": "<PERSON><PERSON><PERSON>", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "<PERSON><PERSON> a<PERSON> o <PERSON>", "item.minecraft.danger_pottery_sherd": "<PERSON><PERSON> a<PERSON> o <PERSON>", "item.minecraft.dark_oak_boat": "<PERSON><PERSON> oju omi <PERSON>u dudu", "item.minecraft.dark_oak_chest_boat": "Egbe igi oak dudu pẹlu àyà", "item.minecraft.debug_stick": "Ọpa alaburo", "item.minecraft.debug_stick.empty": "%s ko ni ohun-ini kan", "item.minecraft.debug_stick.select": "ti a ti yan \"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" ṣeto si %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "Okuta Ẹsọ́", "item.minecraft.diamond_boots": "<PERSON><PERSON>", "item.minecraft.diamond_chestplate": "<PERSON>uta Iyebiye", "item.minecraft.diamond_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_hoe": "Hoe Iyebiye", "item.minecraft.diamond_horse_armor": "Ihamọra ẹṣin iyebiye", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "Apata Diamondka", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "Idẹ Diamond", "item.minecraft.disc_fragment_5": "<PERSON><PERSON><PERSON> dissi", "item.minecraft.disc_fragment_5.desc": "Diski Orin - 5", "item.minecraft.dolphin_spawn_egg": "Ẹyin pe ẹja kan", "item.minecraft.donkey_spawn_egg": "Ẹyin pe ọmọ kẹtẹkẹtẹ kan", "item.minecraft.dragon_breath": "Ìmí ti Dragon", "item.minecraft.dried_kelp": "Omi omi ti o gbẹ", "item.minecraft.drowned_spawn_egg": "Ẹyin pe olutọru", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON>ne Armor <PERSON>", "item.minecraft.echo_shard": "<PERSON><PERSON><PERSON> shard", "item.minecraft.egg": "Ẹyin", "item.minecraft.elder_guardian_spawn_egg": "Ẹyin pe aṣoju alagba", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "Emerald", "item.minecraft.enchanted_book": "<PERSON><PERSON>", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON>", "item.minecraft.end_crystal": "End gara", "item.minecraft.ender_dragon_spawn_egg": "Ẹyin pe <PERSON>er <PERSON>", "item.minecraft.ender_eye": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.ender_pearl": "<PERSON><PERSON> parili", "item.minecraft.enderman_spawn_egg": "Ẹyin pe <PERSON><PERSON>", "item.minecraft.endermite_spawn_egg": "Ẹyin pe Endermite", "item.minecraft.evoker_spawn_egg": "Ẹyin ti o pe olupe naa", "item.minecraft.experience_bottle": "<PERSON><PERSON>", "item.minecraft.explorer_pottery_shard": "Explorer <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.explorer_pottery_sherd": "Explorer <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.eye_armor_trim_smithing_template.new": "Eye Armor Trim", "item.minecraft.feather": "Ẹyẹ", "item.minecraft.fermented_spider_eye": "Fermented Oju <PERSON>", "item.minecraft.field_masoned_banner_pattern": "Field Masoned Banner Pattern", "item.minecraft.filled_map": "Maap<PERSON>", "item.minecraft.fire_charge": "<PERSON><PERSON>", "item.minecraft.firework_rocket": "Ise ina Rocket", "item.minecraft.firework_rocket.flight": "<PERSON><PERSON><PERSON>:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "<PERSON><PERSON> ina Irawo", "item.minecraft.firework_star.black": "<PERSON><PERSON>", "item.minecraft.firework_star.blue": "Bulu", "item.minecraft.firework_star.brown": "<PERSON>", "item.minecraft.firework_star.custom_color": "<PERSON><PERSON>a", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "Fade si", "item.minecraft.firework_star.flicker": "<PERSON><PERSON>", "item.minecraft.firework_star.gray": "Grẹy", "item.minecraft.firework_star.green": "Alawọ ewe", "item.minecraft.firework_star.light_blue": "Imọlẹ Bulu", "item.minecraft.firework_star.light_gray": "Grẹy Imọlẹ", "item.minecraft.firework_star.lime": "Orombo wewe", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "Ọsan", "item.minecraft.firework_star.pink": "Pink", "item.minecraft.firework_star.purple": "El<PERSON>i ti", "item.minecraft.firework_star.red": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape": "Apẹrẹ Aimọ", "item.minecraft.firework_star.shape.burst": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.creeper": "<PERSON><PERSON>e ti <PERSON>", "item.minecraft.firework_star.shape.large_ball": "Bọọlu Nla", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "<PERSON><PERSON>", "item.minecraft.firework_star.trail": "Itọpa", "item.minecraft.firework_star.white": "Funfun", "item.minecraft.firework_star.yellow": "Ofeefee", "item.minecraft.fishing_rod": "<PERSON><PERSON> ipẹja", "item.minecraft.flint": "<PERSON><PERSON>", "item.minecraft.flint_and_steel": "<PERSON> ati Irin", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.flow_armor_trim_smithing_template.new": "Flow Armor Trim", "item.minecraft.flow_banner_pattern": "<PERSON>", "item.minecraft.flow_banner_pattern.desc": "Flow", "item.minecraft.flow_banner_pattern.new": "<PERSON> <PERSON> Pattern", "item.minecraft.flow_pottery_sherd": "Flow Pottery Sherd", "item.minecraft.flower_banner_pattern": "Apẹẹrẹ asia", "item.minecraft.flower_banner_pattern.desc": "Gbigba O<PERSON>do", "item.minecraft.flower_banner_pattern.new": "Flower Charge Banner Pattern", "item.minecraft.flower_pot": "<PERSON><PERSON><PERSON>", "item.minecraft.fox_spawn_egg": "<PERSON>", "item.minecraft.friend_pottery_shard": "Ọrẹ <PERSON><PERSON> Shard", "item.minecraft.friend_pottery_sherd": "Ọrẹ <PERSON><PERSON> Sherd", "item.minecraft.frog_spawn_egg": "Ọpọlọ Spawn Ẹyin", "item.minecraft.furnace_minecart": "Minecart pẹlu ileru", "item.minecraft.ghast_spawn_egg": "Ẹyin pe <PERSON>t", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON> o<PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Giṣẹ Melon Ibẹbẹ", "item.minecraft.globe_banner_pattern": "Apẹẹrẹ asia", "item.minecraft.globe_banner_pattern.desc": "Agbaiye", "item.minecraft.globe_banner_pattern.new": "Globe Banner Pattern", "item.minecraft.glow_berries": "Alábá Berries", "item.minecraft.glow_ink_sac": "Alábá Inki Sac", "item.minecraft.glow_item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.glow_squid_spawn_egg": "Alábá Squid Spawn Ẹyin", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON>", "item.minecraft.goat_horn": "Iwo ewurẹ", "item.minecraft.goat_spawn_egg": "Ewure Spawn Egg", "item.minecraft.gold_ingot": "<PERSON><PERSON><PERSON>", "item.minecraft.gold_nugget": "<PERSON><PERSON> Go<PERSON>", "item.minecraft.golden_apple": "<PERSON><PERSON><PERSON> w<PERSON>", "item.minecraft.golden_axe": "Axórí. fin", "item.minecraft.golden_boots": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_carrot": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_chestplate": "<PERSON><PERSON><PERSON><PERSON> Ẹlẹsẹ wura", "item.minecraft.golden_helmet": "Àṣí<PERSON><PERSON>", "item.minecraft.golden_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_horse_armor": "Golden ẹṣin ihamọra", "item.minecraft.golden_leggings": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.golden_pickaxe": "Ẹwẹ Pickaxe", "item.minecraft.golden_shovel": "Ṣẹfulu ti wura", "item.minecraft.golden_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.gray_bundle": "<PERSON>", "item.minecraft.gray_dye": "Grẹy Dai", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "Green Bundle", "item.minecraft.green_dye": "Awo Alawọ", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "Ẹyin pe olutọju", "item.minecraft.gunpowder": "<PERSON><PERSON>", "item.minecraft.guster_banner_pattern": "<PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON>y <PERSON>", "item.minecraft.happy_ghast_spawn_egg": "Happy Ghast Spawn Egg", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "Okan ti okun", "item.minecraft.heart_pottery_shard": "<PERSON><PERSON> o <PERSON>", "item.minecraft.heart_pottery_sherd": "<PERSON><PERSON> o <PERSON>", "item.minecraft.heartbreak_pottery_shard": "Heartbreak <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.heartbreak_pottery_sherd": "Heartbreak <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.hoglin_spawn_egg": "Ẹyin <PERSON><PERSON><PERSON>", "item.minecraft.honey_bottle": "<PERSON><PERSON>", "item.minecraft.honeycomb": "<PERSON><PERSON><PERSON> oyin", "item.minecraft.hopper_minecart": "Minecart pẹlu Hopper", "item.minecraft.horse_spawn_egg": "Ẹyin pe ẹṣin", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.host_armor_trim_smithing_template.new": "Host <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "Howl <PERSON><PERSON><PERSON> o <PERSON>", "item.minecraft.howl_pottery_sherd": "Howl a<PERSON><PERSON> o <PERSON>", "item.minecraft.husk_spawn_egg": "Ẹyin pipe fun ogbele", "item.minecraft.ink_sac": "Inki Sac", "item.minecraft.iron_axe": "Axe Iron", "item.minecraft.iron_boots": "<PERSON><PERSON>", "item.minecraft.iron_chestplate": "<PERSON><PERSON>", "item.minecraft.iron_golem_spawn_egg": "Iron Golem Spawn Ẹyin", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.iron_hoe": "<PERSON><PERSON>", "item.minecraft.iron_horse_armor": "Ihamọra ẹṣin irin", "item.minecraft.iron_ingot": "Iron Ingot", "item.minecraft.iron_leggings": "<PERSON><PERSON>", "item.minecraft.iron_nugget": "<PERSON><PERSON>", "item.minecraft.iron_pickaxe": "Iron Pickaxe", "item.minecraft.iron_shovel": "<PERSON><PERSON>", "item.minecraft.iron_sword": "<PERSON><PERSON>à <PERSON>", "item.minecraft.item_frame": "<PERSON><PERSON><PERSON>", "item.minecraft.jungle_boat": "Ok<PERSON> oju omi igbo", "item.minecraft.jungle_chest_boat": "Gungle <PERSON>k<PERSON> oju omi pẹlu àyà", "item.minecraft.knowledge_book": "<PERSON><PERSON>", "item.minecraft.lapis_lazuli": "<PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON>", "item.minecraft.lead": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.leather": "Awọ", "item.minecraft.leather_boots": "Alawọ orunkun", "item.minecraft.leather_chestplate": "Alawọ tunic", "item.minecraft.leather_helmet": "Alawọ fila", "item.minecraft.leather_horse_armor": "Ologun ẹṣin ẹṣin", "item.minecraft.leather_leggings": "<PERSON><PERSON><PERSON><PERSON> sokoto alaw<PERSON>", "item.minecraft.light_blue_bundle": "Light Blue Bundle", "item.minecraft.light_blue_dye": "<PERSON><PERSON><PERSON> bulu didan", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "Light Gray Bundle", "item.minecraft.light_gray_dye": "<PERSON>a Grẹy", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "Lime Bundle", "item.minecraft.lime_dye": "Orombo Wewe", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "Pẹ <PERSON>gun", "item.minecraft.lingering_potion.effect.awkward": "Irẹwẹsi <PERSON><PERSON>", "item.minecraft.lingering_potion.effect.empty": "Lingering Uncraftable Iwon", "item.minecraft.lingering_potion.effect.fire_resistance": "Lingering Iwon ti Ina Atako", "item.minecraft.lingering_potion.effect.harming": "Lingering Iwon ti <PERSON>ara", "item.minecraft.lingering_potion.effect.healing": "Lingering Iwon ti <PERSON>san", "item.minecraft.lingering_potion.effect.infested": "Lingering Potion of Infestation", "item.minecraft.lingering_potion.effect.invisibility": "Lingering Iwon ti <PERSON>han", "item.minecraft.lingering_potion.effect.leaping": "Lingering Iwon ti Fifo", "item.minecraft.lingering_potion.effect.levitation": "<PERSON>kun <PERSON>kun ti <PERSON>", "item.minecraft.lingering_potion.effect.luck": "Lingering Iwon ti Orire", "item.minecraft.lingering_potion.effect.mundane": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.night_vision": "Lingering Iwon ti Alẹ Iran", "item.minecraft.lingering_potion.effect.oozing": "Lingering Potion of Oozing", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "Ẹdun ti <PERSON>", "item.minecraft.lingering_potion.effect.slow_falling": "<PERSON><PERSON><PERSON><PERSON> afẹfẹ ti isinku r<PERSON>ra", "item.minecraft.lingering_potion.effect.slowness": "Lingering Iwon ti Fifalẹ", "item.minecraft.lingering_potion.effect.strength": "Lingering Iwon ti <PERSON>bara", "item.minecraft.lingering_potion.effect.swiftness": "Lingering Iwon ti <PERSON>", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "Adalu pipẹ ti oluwa erupẹ", "item.minecraft.lingering_potion.effect.water": "Lingering <PERSON><PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "Lingering Iwon ti <PERSON>", "item.minecraft.lingering_potion.effect.weakness": "<PERSON>kun <PERSON>kun ti <PERSON>a", "item.minecraft.lingering_potion.effect.weaving": "Lingering Potion of Weaving", "item.minecraft.lingering_potion.effect.wind_charged": "Lingering Potion of Wind Charging", "item.minecraft.llama_spawn_egg": "Ẹyin pe laini", "item.minecraft.lodestone_compass": "O<PERSON>a", "item.minecraft.mace": "Mace", "item.minecraft.magenta_bundle": "Magenta Bundle", "item.minecraft.magenta_dye": "Magenta dai", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Ipara Magma", "item.minecraft.magma_cube_spawn_egg": "Ẹyin ti n pe kọnisi magma", "item.minecraft.mangrove_boat": "Ọkọ oju omi mangove", "item.minecraft.mangrove_chest_boat": "Ọkọ oju omi kekere pẹlu àyà", "item.minecraft.map": "<PERSON><PERSON>", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "A nkan ti elegede", "item.minecraft.milk_bucket": "<PERSON><PERSON> garawa", "item.minecraft.minecart": "Minecart", "item.minecraft.miner_pottery_shard": "Miner <PERSON><PERSON>", "item.minecraft.miner_pottery_sherd": "Miner <PERSON>", "item.minecraft.mojang_banner_pattern": "Apẹẹrẹ asia", "item.minecraft.mojang_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.mojang_banner_pattern.new": "<PERSON> Banner <PERSON>", "item.minecraft.mooshroom_spawn_egg": "Ẹyin pe eran Maalu kan", "item.minecraft.mourner_pottery_shard": "<PERSON><PERSON><PERSON> o <PERSON>", "item.minecraft.mourner_pottery_sherd": "<PERSON><PERSON><PERSON> a<PERSON> o <PERSON>", "item.minecraft.mule_spawn_egg": "Ẹyin pe ikun kan", "item.minecraft.mushroom_stew": "Ipẹtẹ Olu", "item.minecraft.music_disc_11": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_11.desc": "C418 - 11", "item.minecraft.music_disc_13": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Music Disc", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Music Disc", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_mellohi.desc": "C418 - me<PERSON><PERSON>\n", "item.minecraft.music_disc_otherside": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_otherside.desc": "<PERSON>", "item.minecraft.music_disc_pigstep": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_pigstep.desc": "<PERSON><PERSON>- Pigstep", "item.minecraft.music_disc_precipice": "Music Disc", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_stal.desc": "C418 - stal", "item.minecraft.music_disc_strad": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_strad.desc": "C418 - strad\n", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "<PERSON><PERSON> Orin", "item.minecraft.music_disc_ward.desc": "C418 - ward", "item.minecraft.mutton": "<PERSON><PERSON>", "item.minecraft.name_tag": "Orukọ Tag", "item.minecraft.nautilus_shell": "<PERSON><PERSON><PERSON> ti a nautilus", "item.minecraft.nether_brick": "Nether Brick", "item.minecraft.nether_star": "<PERSON><PERSON> Irawọ", "item.minecraft.nether_wart": "Nether Wart", "item.minecraft.netherite_axe": "Ax ti Netherite", "item.minecraft.netherite_boots": "<PERSON><PERSON><PERSON><PERSON> bata <PERSON>", "item.minecraft.netherite_chestplate": "Chestpiece Netherite", "item.minecraft.netherite_helmet": "Helmiteite", "item.minecraft.netherite_hoe": "<PERSON><PERSON>", "item.minecraft.netherite_ingot": "Netherite Ingot", "item.minecraft.netherite_leggings": "Leggings Netherite", "item.minecraft.netherite_pickaxe": "Pickaxe ti Netherite", "item.minecraft.netherite_scrap": "Alokuirin Netherite", "item.minecraft.netherite_shovel": "Ṣẹgun Netherite", "item.minecraft.netherite_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite Upgrade", "item.minecraft.oak_boat": "<PERSON><PERSON> oju omi <PERSON>u", "item.minecraft.oak_chest_boat": "Egbe igi oak pẹlu àyà", "item.minecraft.ocelot_spawn_egg": "Ẹyin pe Ocelot", "item.minecraft.ominous_bottle": "Ominous <PERSON>", "item.minecraft.ominous_trial_key": "Ominous Trial Key", "item.minecraft.orange_bundle": "Orange Bundle", "item.minecraft.orange_dye": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "<PERSON><PERSON><PERSON>", "item.minecraft.pale_oak_boat": "Pale Oak Boat", "item.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "item.minecraft.panda_spawn_egg": "Ṣiṣẹda panda", "item.minecraft.paper": "<PERSON>we", "item.minecraft.parrot_spawn_egg": "Ẹyin pe ikẹjọ kan", "item.minecraft.phantom_membrane": "Awọ<PERSON><PERSON>ra koriko", "item.minecraft.phantom_spawn_egg": "Ẹyin pe pipe kan", "item.minecraft.pig_spawn_egg": "Ẹyin pipe kan ẹlẹdẹ", "item.minecraft.piglin_banner_pattern": "Apẹẹrẹ asia", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Snout Banner Pattern", "item.minecraft.piglin_brute_spawn_egg": "<PERSON><PERSON><PERSON>", "item.minecraft.piglin_spawn_egg": "Ẹjẹ <PERSON><PERSON>", "item.minecraft.pillager_spawn_egg": "Ṣiṣe buburu", "item.minecraft.pink_bundle": "Pink Bundle", "item.minecraft.pink_dye": "Pink Dai", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "<PERSON>cher <PERSON>", "item.minecraft.pitcher_pod": "Pitcher Labẹ", "item.minecraft.plenty_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.plenty_pottery_sherd": "<PERSON><PERSON><PERSON>", "item.minecraft.poisonous_potato": "<PERSON><PERSON> ti Ọdunkun", "item.minecraft.polar_bear_spawn_egg": "Ẹyin ti o pe ni agbọn pola kan", "item.minecraft.popped_chorus_fruit": "Ti jade Chorus Eso", "item.minecraft.porkchop": "Aise ẹlẹdẹ ẹlẹdẹ", "item.minecraft.potato": "Ọdunkun", "item.minecraft.potion": "Ikoko", "item.minecraft.potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.empty": "Ikoko Ti ko ṣee gbe", "item.minecraft.potion.effect.fire_resistance": "<PERSON><PERSON> ti <PERSON>a Atako", "item.minecraft.potion.effect.harming": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.healing": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.infested": "Potion of Infestation", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON> ti <PERSON>fo", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.luck": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.night_vision": "<PERSON>won ti Alẹ Iran", "item.minecraft.potion.effect.oozing": "Potion of Oozing", "item.minecraft.potion.effect.poison": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.regeneration": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.slow_falling": "Ẹdun ti ilọkuro sisẹ", "item.minecraft.potion.effect.slowness": "<PERSON>won ti Fifalẹ", "item.minecraft.potion.effect.strength": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.turtle_master": "Ẹkuru kan ti o ni erupẹ", "item.minecraft.potion.effect.water": "<PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "<PERSON><PERSON> ti <PERSON><PERSON>", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON> ti <PERSON>", "item.minecraft.potion.effect.weaving": "Potion of Weaving", "item.minecraft.potion.effect.wind_charged": "Potion of Wind Charging", "item.minecraft.pottery_shard_archer": "<PERSON> o <PERSON>", "item.minecraft.pottery_shard_arms_up": "Arms Up apadì o Shard", "item.minecraft.pottery_shard_prize": "Prize <PERSON><PERSON> Shard", "item.minecraft.pottery_shard_skull": "<PERSON><PERSON> o <PERSON>", "item.minecraft.powder_snow_bucket": "Powder Egbon Garawa", "item.minecraft.prismarine_crystals": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.prize_pottery_shard": "Prize <PERSON><PERSON> Shard", "item.minecraft.prize_pottery_sherd": "Prize <PERSON><PERSON> Sherd", "item.minecraft.pufferfish": "Pufferfish", "item.minecraft.pufferfish_bucket": "Garawa pẹlu fifaju", "item.minecraft.pufferfish_spawn_egg": "Ẹyin pe pipe kan", "item.minecraft.pumpkin_pie": "Elegede Paii", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_bundle": "Purple Bundle", "item.minecraft.purple_dye": "<PERSON><PERSON>", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "<PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON><PERSON> a<PERSON>", "item.minecraft.rabbit_foot": "Ẹsẹ Ehoro", "item.minecraft.rabbit_hide": "Ìbòmọlẹ Ehoro", "item.minecraft.rabbit_spawn_egg": "Ẹyin pe ehoro", "item.minecraft.rabbit_stew": "Ehoro Ipẹtẹ", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Armor Trim", "item.minecraft.ravager_spawn_egg": "Ṣẹda akọmalu", "item.minecraft.raw_copper": "<PERSON><PERSON><PERSON>", "item.minecraft.raw_gold": "Raw Gold", "item.minecraft.raw_iron": "<PERSON><PERSON>", "item.minecraft.recovery_compass": "Kompasi imularada", "item.minecraft.red_bundle": "Red Bundle", "item.minecraft.red_dye": "Red lulú", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "Redstone lulú", "item.minecraft.resin_brick": "Resin Brick", "item.minecraft.resin_clump": "<PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "<PERSON>n run", "item.minecraft.saddle": "Gàárì", "item.minecraft.salmon": "<PERSON><PERSON>", "item.minecraft.salmon_bucket": "Akara pẹlu iru ẹja nla kan", "item.minecraft.salmon_spawn_egg": "Ẹyin pipe fun iru ẹja nla kan", "item.minecraft.scrape_pottery_sherd": "Scrape Pottery Sherd", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.sentry_armor_trim_smithing_template.new": "Sentry Armor Trim", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> Armor <PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.shears": "Shears", "item.minecraft.sheep_spawn_egg": "Ẹyin pe agutan", "item.minecraft.shelter_pottery_shard": "<PERSON><PERSON><PERSON>", "item.minecraft.shelter_pottery_sherd": "<PERSON><PERSON><PERSON> o <PERSON>", "item.minecraft.shield": "Apata", "item.minecraft.shield.black": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.blue": "Awọ Blue", "item.minecraft.shield.brown": "Shield Brown", "item.minecraft.shield.cyan": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.gray": "Awọ Grẹy", "item.minecraft.shield.green": "Shield Alawọ ewe", "item.minecraft.shield.light_blue": "Awọ Blue Shield", "item.minecraft.shield.light_gray": "<PERSON><PERSON>ọ Grey Grey", "item.minecraft.shield.lime": "Apamọwọ Orombo wewe", "item.minecraft.shield.magenta": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.orange": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.pink": "Awọ Pink", "item.minecraft.shield.purple": "Shield eleyi", "item.minecraft.shield.red": "<PERSON><PERSON><PERSON>", "item.minecraft.shield.white": "<PERSON>ṣọ funfun", "item.minecraft.shield.yellow": "Shield Shield", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON>", "item.minecraft.shulker_spawn_egg": "Ẹyin pe <PERSON>er", "item.minecraft.sign": "<PERSON><PERSON>", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.silence_armor_trim_smithing_template.new": "Silence Armor Trim", "item.minecraft.silverfish_spawn_egg": "Ẹyin pe ẹja iha", "item.minecraft.skeleton_horse_spawn_egg": "Ẹyin pe ọmọ ẹṣin egungun kan", "item.minecraft.skeleton_spawn_egg": "Ẹyin ti n pe egungun", "item.minecraft.skull_banner_pattern": "Apẹẹrẹ asia", "item.minecraft.skull_banner_pattern.desc": "<PERSON><PERSON><PERSON>", "item.minecraft.skull_banner_pattern.new": "Skull Charge Banner Pattern", "item.minecraft.skull_pottery_shard": "<PERSON><PERSON> o <PERSON>", "item.minecraft.skull_pottery_sherd": "<PERSON><PERSON> o <PERSON>", "item.minecraft.slime_ball": "Slimeball", "item.minecraft.slime_spawn_egg": "Ẹyin pe Slime", "item.minecraft.smithing_template": "<PERSON><PERSON>", "item.minecraft.smithing_template.applies_to": "Kan si:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Fi ingot tabi gara", "item.minecraft.smithing_template.armor_trim.applies_to": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Fi kan nkan ti iham<PERSON>ra", "item.minecraft.smithing_template.armor_trim.ingredients": "Ingots & Kirisita", "item.minecraft.smithing_template.ingredients": "Eroja:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Fi Netherite Ingot kun", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamond Equipment", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Ṣafikun iham<PERSON><PERSON> diamond, ohun ija, tabi irinṣẹ", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherite Ingot", "item.minecraft.smithing_template.upgrade": "Igbesoke: ", "item.minecraft.sniffer_spawn_egg": "Ẹyin pipe kan Sniffer", "item.minecraft.snort_pottery_shard": "<PERSON>nort a<PERSON><PERSON> o <PERSON>", "item.minecraft.snort_pottery_sherd": "Snort a<PERSON><PERSON> o <PERSON>rd", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "Ẹyin pipe kan <PERSON>bon Golem", "item.minecraft.snowball": "Bọọlu afẹsẹgba", "item.minecraft.spectral_arrow": "<PERSON><PERSON><PERSON>", "item.minecraft.spider_eye": "<PERSON><PERSON>", "item.minecraft.spider_spawn_egg": "Ẹyin pipe kan Spider", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.spire_armor_trim_smithing_template.new": "Spire Arm<PERSON>", "item.minecraft.splash_potion": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.awkward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.fire_resistance": "<PERSON><PERSON><PERSON><PERSON> ti Ina Atako", "item.minecraft.splash_potion.effect.harming": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.healing": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.infested": "Splash Potion of Infestation", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON> ti Fifo", "item.minecraft.splash_potion.effect.levitation": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.mundane": "<PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.night_vision": "<PERSON><PERSON><PERSON><PERSON> iwon ti Alẹ Iran", "item.minecraft.splash_potion.effect.oozing": "Splash Potion of Oozing", "item.minecraft.splash_potion.effect.poison": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.regeneration": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.slow_falling": "Mu fifọ pọ simẹnti", "item.minecraft.splash_potion.effect.slowness": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.strength": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.swiftness": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.thick": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "Isọpọ ikoko ti oludari erupẹ", "item.minecraft.splash_potion.effect.water": "Asọ Omi Asọ", "item.minecraft.splash_potion.effect.water_breathing": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON> ti <PERSON>", "item.minecraft.splash_potion.effect.weaving": "Splash Potion of Weaving", "item.minecraft.splash_potion.effect.wind_charged": "Splash Potion of Wind Charging", "item.minecraft.spruce_boat": "Okọ oju omi spruce", "item.minecraft.spruce_chest_boat": "Spruce Boat pẹlu àyà", "item.minecraft.spyglass": "Spyglass", "item.minecraft.squid_spawn_egg": "Ẹyin pe pipe kan", "item.minecraft.stick": "Lẹẹmọ", "item.minecraft.stone_axe": "Apata <PERSON>", "item.minecraft.stone_hoe": "Hoe Stone", "item.minecraft.stone_pickaxe": "<PERSON><PERSON>", "item.minecraft.stone_shovel": "<PERSON><PERSON>", "item.minecraft.stone_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.stray_spawn_egg": "Ẹyin ti o pe ni aṣiṣe naa", "item.minecraft.strider_spawn_egg": "Onje itaja Summon Egg", "item.minecraft.string": "<PERSON><PERSON>", "item.minecraft.sugar": "Suga", "item.minecraft.suspicious_stew": "Ipanu ifura", "item.minecraft.sweet_berries": "Sweet eso", "item.minecraft.tadpole_bucket": "Garawa ti Tadpole", "item.minecraft.tadpole_spawn_egg": "Tadpole Spawn Ẹyin", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.tide_armor_trim_smithing_template.new": "Tide Armor Trim", "item.minecraft.tipped_arrow": "Arrow olorun", "item.minecraft.tipped_arrow.effect.awkward": "Tifa Ọfa", "item.minecraft.tipped_arrow.effect.empty": "Ọfa Ti a ko le gbe jade", "item.minecraft.tipped_arrow.effect.fire_resistance": "Ọfa ti Ina Atako", "item.minecraft.tipped_arrow.effect.harming": "Ọfa ti <PERSON>palara", "item.minecraft.tipped_arrow.effect.healing": "Ọfa ti <PERSON><PERSON>san", "item.minecraft.tipped_arrow.effect.infested": "Arrow of Infestation", "item.minecraft.tipped_arrow.effect.invisibility": "Ọfà ti <PERSON>han", "item.minecraft.tipped_arrow.effect.leaping": "Ọfa ti Fifo", "item.minecraft.tipped_arrow.effect.levitation": "Ọfa ti <PERSON>ti", "item.minecraft.tipped_arrow.effect.luck": "Ọfa ti Orire", "item.minecraft.tipped_arrow.effect.mundane": "Tifa Ọfa", "item.minecraft.tipped_arrow.effect.night_vision": "Ọfa ti Alẹ Iran", "item.minecraft.tipped_arrow.effect.oozing": "Arrow of Oozing", "item.minecraft.tipped_arrow.effect.poison": "Ọfa ti <PERSON>", "item.minecraft.tipped_arrow.effect.regeneration": "Ọfa ti <PERSON>", "item.minecraft.tipped_arrow.effect.slow_falling": "Orilẹ-ede ti isunku fifọ", "item.minecraft.tipped_arrow.effect.slowness": "Arrow of Fifalẹ", "item.minecraft.tipped_arrow.effect.strength": "Ọfa ti <PERSON>bara", "item.minecraft.tipped_arrow.effect.swiftness": "Ọfa ti <PERSON>", "item.minecraft.tipped_arrow.effect.thick": "Tifa Ọfa", "item.minecraft.tipped_arrow.effect.turtle_master": "A shot kan ti Ẹkuru titunto si", "item.minecraft.tipped_arrow.effect.water": "Ọfà ti Splashing", "item.minecraft.tipped_arrow.effect.water_breathing": "Ọfa ti <PERSON><PERSON>", "item.minecraft.tipped_arrow.effect.weakness": "Ọfa ti <PERSON>a", "item.minecraft.tipped_arrow.effect.weaving": "Arrow of Weaving", "item.minecraft.tipped_arrow.effect.wind_charged": "Arrow of Wind Charging", "item.minecraft.tnt_minecart": "Minecart pẹlu TNT", "item.minecraft.torchflower_seeds": "Ògùṣọ Flower irugbin na", "item.minecraft.totem_of_undying": "Totem ti Iku", "item.minecraft.trader_llama_spawn_egg": "Ṣẹda onisowo oniṣowo kan", "item.minecraft.trial_key": "Trial Key", "item.minecraft.trident": "Trident", "item.minecraft.tropical_fish": "<PERSON><PERSON> <PERSON>na oorun eja", "item.minecraft.tropical_fish_bucket": "<PERSON>go kan pẹlu ẹja ti oorun", "item.minecraft.tropical_fish_spawn_egg": "Ẹyin pe pipe eja ti nwaye", "item.minecraft.turtle_helmet": "<PERSON><PERSON><PERSON>", "item.minecraft.turtle_scute": "<PERSON>", "item.minecraft.turtle_spawn_egg": "Ẹyin pe ikoko kan", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.vex_armor_trim_smithing_template.new": "Vex Armor Trim", "item.minecraft.vex_spawn_egg": "Ẹyin pe ikorira", "item.minecraft.villager_spawn_egg": "Ẹyin pe olutọju naa", "item.minecraft.vindicator_spawn_egg": "Ẹyin pe pipe kan", "item.minecraft.wandering_trader_spawn_egg": "Ṣẹda onikan iṣowo", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON>", "item.minecraft.warden_spawn_egg": "Warden Spawn Ẹyin", "item.minecraft.warped_fungus_on_a_stick": "Ipeja Olu Olu Ipeja", "item.minecraft.water_bucket": "<PERSON><PERSON><PERSON>", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wayfinder Armor Trim", "item.minecraft.wheat": "<PERSON><PERSON><PERSON>", "item.minecraft.wheat_seeds": "<PERSON><PERSON><PERSON>", "item.minecraft.white_bundle": "White Bundle", "item.minecraft.white_dye": "<PERSON><PERSON>n dai", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.wild_armor_trim_smithing_template.new": "Wild Armor Trim", "item.minecraft.wind_charge": "Wind Charge", "item.minecraft.witch_spawn_egg": "Ẹyin pe ọrẹ kan", "item.minecraft.wither_skeleton_spawn_egg": "Ẹyin ti n pe Wither egungun", "item.minecraft.wither_spawn_egg": "Wither Spawn Ẹyin", "item.minecraft.wolf_armor": "<PERSON>or", "item.minecraft.wolf_spawn_egg": "Ẹyin pe ikoko kan", "item.minecraft.wooden_axe": "Axe onigi", "item.minecraft.wooden_hoe": "<PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON>", "item.minecraft.wooden_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.writable_book": "<PERSON>we ati ki o Quill", "item.minecraft.written_book": "Iwe ti a <PERSON>", "item.minecraft.yellow_bundle": "Yellow Bundle", "item.minecraft.yellow_dye": "A<PERSON>ọ Ofeefee", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "<PERSON><PERSON><PERSON> Summ<PERSON>", "item.minecraft.zombie_horse_spawn_egg": "Ẹyin pe Zombie ẹṣin", "item.minecraft.zombie_spawn_egg": "Ẹyin pe Zombie", "item.minecraft.zombie_villager_spawn_egg": "Ẹyin pe olutọju Zombie naa", "item.minecraft.zombified_piglin_spawn_egg": "Zombie Piglin Summon <PERSON>ba", "item.modifiers.any": "When equipped:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "When equipped:", "item.modifiers.chest": "Nigbati o ba wọ:", "item.modifiers.feet": "Nig<PERSON>i o ba wa ni ẹsẹ rẹ:", "item.modifiers.hand": "When held:", "item.modifiers.head": "Nigbati o ba wọ si ori:", "item.modifiers.legs": "Nigbati o ba fi si ẹsẹ rẹ:", "item.modifiers.mainhand": "Nigbati ni akọkọ ọwọ:", "item.modifiers.offhand": "Nigbati ni ọw<PERSON> osi:", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NBT: %s tag(s)", "item.op_block_warning.line1": "Warning:", "item.op_block_warning.line2": "Use of this item might lead to command execution", "item.op_block_warning.line3": "Do not use unless you know the exact contents!", "item.unbreakable": "Ko le fọ", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON><PERSON> bul<PERSON><PERSON><PERSON> ile", "itemGroup.coloredBlocks": "Awọ<PERSON> bulọ<PERSON><PERSON> awọ", "itemGroup.combat": "<PERSON><PERSON>", "itemGroup.consumables": "<PERSON><PERSON><PERSON><PERSON> ohun elo", "itemGroup.crafting": "Ṣiṣẹda", "itemGroup.foodAndDrink": "<PERSON><PERSON><PERSON> ati ohun mimu", "itemGroup.functional": "<PERSON>wọn bulọọki iṣẹ", "itemGroup.hotbar": "<PERSON><PERSON><PERSON><PERSON> elo <PERSON>", "itemGroup.ingredients": "<PERSON><PERSON><PERSON>", "itemGroup.inventory": "<PERSON><PERSON>", "itemGroup.natural": "<PERSON><PERSON><PERSON> ohun amorindun", "itemGroup.op": "<PERSON><PERSON><PERSON><PERSON> ohun elo onišẹ", "itemGroup.redstone": "Redstone ohun amorindun", "itemGroup.search": "Search Awọn ohun kan", "itemGroup.spawnEggs": "Awọn Ẹyin ti a pa", "itemGroup.tools": "Awọn irinṣẹ & Awọn ohun elo", "item_modifier.unknown": "Atunse aimọ: %s", "jigsaw_block.final_state": "<PERSON><PERSON><PERSON> si:", "jigsaw_block.generate": "<PERSON><PERSON>", "jigsaw_block.joint.aligned": "Alẹ", "jigsaw_block.joint.rollable": "Ti nka", "jigsaw_block.joint_label": "<PERSON><PERSON> isẹpo:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON><PERSON><PERSON> nkan iruju ti <PERSON>aw", "jigsaw_block.levels": "Awọn ipele: %s", "jigsaw_block.name": "Orukọ:", "jigsaw_block.placement_priority": "Placement Priority:", "jigsaw_block.placement_priority.tooltip": "When this Jigsaw block connects to a piece, this is the order in which that piece is processed for connections in the wider structure.\n\nPieces will be processed in descending priority with insertion order breaking ties.", "jigsaw_block.pool": "Pool Ifojusi:", "jigsaw_block.selection_priority": "Selection Priority:", "jigsaw_block.selection_priority.tooltip": "When the parent piece is being processed for connections, this is the order in which this Jigsaw block attempts to connect to its target piece.\n\nJigsaws will be processed in descending priority with random ordering breaking ties.", "jigsaw_block.target": "Orukọ ibi-afẹde:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "<PERSON><PERSON><PERSON><PERSON>", "key.attack": "Kolu/run", "key.back": "Lọ pada", "key.categories.creative": "<PERSON><PERSON> iseda", "key.categories.gameplay": "<PERSON><PERSON><PERSON><PERSON> ori k<PERSON>", "key.categories.inventory": "<PERSON><PERSON>", "key.categories.misc": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.movement": "Agbegbe", "key.categories.multiplayer": "<PERSON><PERSON> a<PERSON>", "key.categories.ui": "<PERSON><PERSON> ere", "key.chat": "Ṣiṣe iwiregbe", "key.command": "Titẹ aṣẹ", "key.drop": "Sọ ohun kan ti a yan", "key.forward": "Lọ siwaju", "key.fullscreen": "Oni balu ni kikun", "key.hotbar.1": "Ibuwe Gbigbọn 1", "key.hotbar.2": "Ibuwe Gbigbọn 2", "key.hotbar.3": "Ibuwe Gbigbọn 3", "key.hotbar.4": "Ibuwe Gbigbọn 4", "key.hotbar.5": "Ibuwe Gbigbọn 5", "key.hotbar.6": "Ibuwe Gbigbọn 6", "key.hotbar.7": "Ibuwe Gbigbọn 7", "key.hotbar.8": "Ibuwe Gbigbọn 8", "key.hotbar.9": "Ibuwe Gbigbọn 9", "key.inventory": "Ṣiṣe ayẹwo/<PERSON><PERSON><PERSON> oja", "key.jump": "Sí", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.keyboard.caps.lock": "<PERSON><PERSON><PERSON> ise leta nla", "key.keyboard.comma": ",", "key.keyboard.delete": "Paarẹ", "key.keyboard.down": "Isọ isalẹ", "key.keyboard.end": "<PERSON><PERSON><PERSON>", "key.keyboard.enter": "Tẹ", "key.keyboard.equal": "=", "key.keyboard.escape": "Pamọ", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Ile", "key.keyboard.insert": "Fi sii", "key.keyboard.keypad.0": "Ọpa abuja 0", "key.keyboard.keypad.1": "Ọpa abuja 1", "key.keyboard.keypad.2": "Ọpa abuja 2", "key.keyboard.keypad.3": "Ọpa abuja 3", "key.keyboard.keypad.4": "Ọpa abuja 4", "key.keyboard.keypad.5": "Ọpa abuja 5", "key.keyboard.keypad.6": "Ọpa abuja 6", "key.keyboard.keypad.7": "Ọpa abuja 7", "key.keyboard.keypad.8": "Ọpa abuja 8", "key.keyboard.keypad.9": "Ọpa abuja 9", "key.keyboard.keypad.add": "Ọpa abuja +", "key.keyboard.keypad.decimal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>i oriṣi", "key.keyboard.keypad.divide": "Ọpa abuja /", "key.keyboard.keypad.enter": "<PERSON><PERSON><PERSON><PERSON> sii Tẹ", "key.keyboard.keypad.equal": "Ọpa abuja =", "key.keyboard.keypad.multiply": "Ọpa abuja *", "key.keyboard.keypad.subtract": "Ọpa abuja -", "key.keyboard.left": "Ori-apa osi", "key.keyboard.left.alt": "<PERSON>si alt", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "Isakoso ti osi", "key.keyboard.left.shift": "<PERSON>sọ-ti osi", "key.keyboard.left.win": "<PERSON><PERSON>", "key.keyboard.menu": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.page.down": "<PERSON><PERSON><PERSON><PERSON><PERSON> si isalẹ", "key.keyboard.page.up": "<PERSON><PERSON> iwe soke", "key.keyboard.pause": "<PERSON><PERSON>", "key.keyboard.period": ".", "key.keyboard.print.screen": "<PERSON><PERSON>", "key.keyboard.right": "Apa-<PERSON><PERSON>", "key.keyboard.right.alt": "Ọtun Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.right.shift": "<PERSON><PERSON>", "key.keyboard.right.win": "Ọtun Gba", "key.keyboard.scroll.lock": "<PERSON><PERSON><PERSON><PERSON>itiipa", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON><PERSON>", "key.keyboard.tab": "<PERSON><PERSON><PERSON>", "key.keyboard.unknown": "<PERSON> dè", "key.keyboard.up": "<PERSON><PERSON>", "key.keyboard.world.1": "Aye 1", "key.keyboard.world.2": "Aye 2", "key.left": "<PERSON><PERSON> si osi", "key.loadToolbarActivator": "Ẹru Gbigbe Hotbar", "key.mouse": "Bọtini %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>w<PERSON>", "key.mouse.middle": "<PERSON><PERSON><PERSON>", "key.mouse.right": "<PERSON><PERSON><PERSON><PERSON>", "key.pickItem": "<PERSON> àkọsílẹ", "key.playerlist": "Akojọ ẹrọ orin", "key.quickActions": "Quick Actions", "key.right": "<PERSON><PERSON>", "key.saveToolbarActivator": "Fipamọ Imuduro <PERSON> ṣiṣẹ", "key.screenshot": "<PERSON> aworan si<PERSON>", "key.smoothCamera": "<PERSON><PERSON><PERSON> ipo kamẹra", "key.sneak": "<PERSON> <PERSON>", "key.socialInteractions": "<PERSON><PERSON><PERSON> ibaraẹnisọrọ <PERSON>", "key.spectatorOutlines": "Ṣiṣẹ Awọn ẹrọ orin (Awọn oluranran)", "key.sprint": "Ṣẹṣẹ", "key.swapOffhand": "Yi ohun kan pẹlu Offhand", "key.togglePerspective": "<PERSON><PERSON><PERSON> i<PERSON>i", "key.use": "Lo apo ohun/ibi", "known_server_link.announcements": "Announcements", "known_server_link.community": "Community", "known_server_link.community_guidelines": "Community Guidelines", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Forums", "known_server_link.news": "News", "known_server_link.report_bug": "Report Server Bug", "known_server_link.status": "Status", "known_server_link.support": "Support", "known_server_link.website": "Website", "lanServer.otherPlayers": "Awọn eto fun Awọn ẹrọ orin miiran", "lanServer.port": "Nọmba Port", "lanServer.port.invalid": "<PERSON> wulo ibudo.\nFi apoti ṣatunkọ silẹ ni ofo tabi tẹ nọmba sii laarin 1024 ati 65535.", "lanServer.port.invalid.new": "<PERSON> wulo ibudo.\nFi apoti ṣatunkọ silẹ ni ofo tabi tẹ nọmba sii laarin %s ati %s.", "lanServer.port.unavailable": "Port ko si.\nFi apoti atunṣe silẹ ni ofo tabi tẹ nọmba ti o yatọ sii laarin 1024 ati 65535.", "lanServer.port.unavailable.new": "Port ko si.\nFi apoti atunṣe silẹ ni ofo tabi tẹ nọmba ti o yatọ sii laarin %s ati %s.", "lanServer.scanning": "Ṣiṣayẹwo fun awọn ere lori nẹtiwọki agbegbe rẹ", "lanServer.start": "Bẹrẹ LAN aye", "lanServer.title": "LAN aye", "language.code": "yor_NG", "language.name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language.region": "Nàìjíríà", "lectern.take_book": "<PERSON> <PERSON>we kan", "loading.progress": "%s%%", "mco.account.privacy.info": "Ka diẹ sii nipa Mojang ati awọn ofin aṣiri", "mco.account.privacy.info.button": "Read more about GDPR", "mco.account.privacy.information": "Mojang implements certain procedures to help protect children and their privacy including complying with the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to obtain parental consent before accessing your Realms account.", "mco.account.privacyinfo": "Mojang ṣe awọn ilana kan lati ṣe iranlọwọ lati daabobo awọn ọmọde ati aṣiri wọn pẹlu ibamu pẹlu ibamu pẹlu Ofin Idaabobo Idaabobo Ayelujara ti Awọn ọmọde (COPPA) ati Ilana Idaabobo Gbogbogbo Data (GDPR).\n\nO le nilo lati gba ifọwọsi obi ṣaaju ki o to wọle si akọọlẹ Realms rẹ.\n\nTi o ba ni iroyin Minecraft ti o dagba (ti o wọle pẹlu orukọ olumulo rẹ), o nilo lati jade iwe akọọlẹ naa si iwe Mojang kan lati le wọle si Realms.", "mco.account.update": "<PERSON><PERSON><PERSON><PERSON>", "mco.activity.noactivity": "Ko si iṣẹ ṣiṣe fun awọn ọjọ %s sẹhin", "mco.activity.title": "<PERSON>re aṣayan iṣẹ-ṣiṣe", "mco.backup.button.download": "Ṣe igbasilẹ tuntun", "mco.backup.button.reset": "Tun aye", "mco.backup.button.restore": "Mu pada", "mco.backup.button.upload": "Po si agbaye", "mco.backup.changes.tooltip": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry": "Backup (%s)", "mco.backup.entry.description": "Apejuwe", "mco.backup.entry.enabledPack": "Enabled Pack", "mco.backup.entry.gameDifficulty": "<PERSON><PERSON>", "mco.backup.entry.gameMode": "<PERSON><PERSON>", "mco.backup.entry.gameServerVersion": "Ere Server Version", "mco.backup.entry.name": "Oruk<PERSON>", "mco.backup.entry.seed": "Irugbin", "mco.backup.entry.templateName": "Orukọ Awoṣe", "mco.backup.entry.undefined": "<PERSON><PERSON><PERSON><PERSON>", "mco.backup.entry.uploaded": "Uploaded", "mco.backup.entry.worldType": "<PERSON><PERSON>", "mco.backup.generate.world": "Ina aye", "mco.backup.info.title": "<PERSON><PERSON><PERSON><PERSON> lati kẹhin afẹyinti", "mco.backup.narration": "Backup from %s", "mco.backup.nobackups": "Agbegbe yi ko ni awọn backu lọwọlọwọ lọwọlọwọ.", "mco.backup.restoring": "Mimu-pada sipo ibugbe yin", "mco.backup.unknown": "ÀÌMỌ̀", "mco.brokenworld.download": "Ṣe igbasilẹ", "mco.brokenworld.downloaded": "Ṣe igbasilẹ", "mco.brokenworld.message.line1": "Jọwọ tun bẹrẹ tabi yan aye miiran.", "mco.brokenworld.message.line2": "O tun le yan lati ṣe igbasilẹ agbaye si singleplayer.", "mco.brokenworld.minigame.title": "A ko ni atilẹyin minigame yii mọ", "mco.brokenworld.nonowner.error": "Jọwọ duro fun eni ti o le da aye pada", "mco.brokenworld.nonowner.title": "Aye ko ti lo", "mco.brokenworld.play": "<PERSON> ṣiṣẹ", "mco.brokenworld.reset": "<PERSON><PERSON>", "mco.brokenworld.title": "Aye rẹ lọwọlọwọ ko ni atilẹyin", "mco.client.incompatible.msg.line1": "Onibara rẹ ko ni ibamu pẹlu Realms.", "mco.client.incompatible.msg.line2": "Jọwọ lo ẹya tuntun julọ ti Minecraft.", "mco.client.incompatible.msg.line3": "Awọn ohun-ini gidi ko ni ibamu pẹlu awọn ẹya itẹlera aworan.", "mco.client.incompatible.title": "<PERSON><PERSON><PERSON>!", "mco.client.outdated.stable.version": "Your client version (%s) is not compatible with Realms.\n\nPlease use the most recent version of Minecraft.", "mco.client.unsupported.snapshot.version": "Your client version (%s) is not compatible with Realms.\n\nRealms is not available for this snapshot version.", "mco.compatibility.downgrade": "Downgrade", "mco.compatibility.downgrade.description": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work.\n\nA backup of your world will be saved under \"World Backups\". Please restore your world if needed.", "mco.compatibility.incompatible.popup.title": "Incompatible version", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are trying to join is incompatible with the version you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in version %s; you are on version %s.\n\nThese series are not compatible with each other. A new world is needed to play on this version.", "mco.compatibility.unverifiable.message": "The version this world was last played in could not be verified. If the world gets upgraded or downgraded, a backup will be automatically created and saved under \"World Backups\".", "mco.compatibility.unverifiable.title": "Compatibility not verifiable", "mco.compatibility.upgrade": "Upgrade", "mco.compatibility.upgrade.description": "This world was last played in version %s; you are on version %s.\n\nA backup of your world will be saved under \"World Backups\".\n\nPlease restore your world if needed.", "mco.compatibility.upgrade.friend.description": "This world was last played in version %s; you are on version %s.\n\nA backup of the world will be saved under \"World Backups\".\n\nThe owner of the Realm can restore the world if needed.", "mco.compatibility.upgrade.title": "Do you really want to upgrade this world?", "mco.configure.current.minigame": "Lọwọlọwọ", "mco.configure.world.activityfeed.disabled": "<PERSON><PERSON><PERSON><PERSON> ifunni onisẹmu igba die alaabo", "mco.configure.world.backup": "Awọn afẹyinti agbaye", "mco.configure.world.buttons.activity": "Ẹrọ oniṣẹ", "mco.configure.world.buttons.close": "Palẹ ibugbe", "mco.configure.world.buttons.delete": "Paarẹ", "mco.configure.world.buttons.done": "Ṣe", "mco.configure.world.buttons.edit": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.invite": "Pe ẹrọ orin", "mco.configure.world.buttons.moreoptions": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ayan diẹ ẹ sii", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Ṣii ij<PERSON>ba", "mco.configure.world.buttons.options": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ayan aye", "mco.configure.world.buttons.players": "Awọn ẹrọ orin", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "Tun aye tun", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "<PERSON><PERSON><PERSON>", "mco.configure.world.buttons.subscription": "Subscription", "mco.configure.world.buttons.switchminigame": "<PERSON><PERSON><PERSON> a<PERSON>a", "mco.configure.world.close.question.line1": "<PERSON>j<PERSON>ba rẹ yoo di ko ṣeeṣe.", "mco.configure.world.close.question.line2": "Ṣe o da ọ loju pe o fẹ tẹsiwaju?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "Tii awọn ijọba naa...", "mco.configure.world.commandBlocks": "<PERSON>wọn bulọọki aṣẹ", "mco.configure.world.delete.button": "Pa ibugbe", "mco.configure.world.delete.question.line1": "<PERSON>u rẹ yoo paarẹ patapata", "mco.configure.world.delete.question.line2": "Ṣe o da ọ loju pe o fẹ tẹsiwaju?", "mco.configure.world.description": "<PERSON><PERSON><PERSON>", "mco.configure.world.edit.slot.name": "Orukọ agbaye", "mco.configure.world.edit.subscreen.adventuremap": "Diẹ ninu awọn eto jẹ alaabo niwon agbaye lọwọlọwọ rẹ jẹ ìrìn", "mco.configure.world.edit.subscreen.experience": "Diẹ ninu awọn eto jẹ alaabo niwon agbaye lọwọlọwọ rẹ jẹ iriri", "mco.configure.world.edit.subscreen.inspiration": "Diẹ ninu awọn eto jẹ alaabo niwon agbaye lọwọlọwọ rẹ jẹ awokose", "mco.configure.world.forceGameMode": "<PERSON><PERSON> ere agbara", "mco.configure.world.invite.narration": "O ni %s titun ifiwepe", "mco.configure.world.invite.profile.name": "Oruk<PERSON>", "mco.configure.world.invited": "O pe", "mco.configure.world.invited.number": "Iye ti a pe (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON><PERSON><PERSON>", "mco.configure.world.invites.ops.tooltip": "Oniṣẹ", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON> kuro", "mco.configure.world.leave.question.line1": "Ti o ba kuro ni ibugbe yii iwọ kii yoo rii ayafi ti a ba pe ọ lẹẹkansi", "mco.configure.world.leave.question.line2": "Ṣe o da ọ loju pe o fẹ tẹsiwaju?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "<PERSON>po", "mco.configure.world.minigame": "Lọwọlọwọ: %s", "mco.configure.world.name": "Orukọ ibugbe", "mco.configure.world.opening": "Ṣiṣe awọn ijọba naa...", "mco.configure.world.players.error": "Ẹrọ orin pẹlu orukọ ti a pese ko si tẹlẹ", "mco.configure.world.players.inviting": "Oṣere ifiwepe...", "mco.configure.world.players.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.pvp": "PVP", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Aye rẹ yoo di atunbere ati pe agbaye rẹ lọwọlọwọ yoo sọnu", "mco.configure.world.reset.question.line2": "Ṣe o da ọ loju pe o fẹ tẹsiwaju?", "mco.configure.world.resourcepack.question": "You need a custom resource pack to play on this Realm\n\nDo you want to download it and play?", "mco.configure.world.resourcepack.question.line1": "O nilo apo idii ti aṣa lati mu ṣiṣẹ lori ilẹ-ọba yii", "mco.configure.world.resourcepack.question.line2": "Ṣe o fẹ lati gba lati ayelujara ki o mu ṣiṣẹ?", "mco.configure.world.restore.download.question.line1": "Aye yoo ṣe igbasilẹ ati fi kun si awọn agbaye player rẹ kan.", "mco.configure.world.restore.download.question.line2": "Ṣe o fẹ lati tẹsiwaju?", "mco.configure.world.restore.question.line1": "Aye rẹ yoo pada si ọjọ '%s' (%s)", "mco.configure.world.restore.question.line2": "Ṣe o da ọ loju pe o fẹ tẹsiwaju?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "<PERSON><PERSON><PERSON>", "mco.configure.world.slot": "Aye %s", "mco.configure.world.slot.empty": "O dara", "mco.configure.world.slot.switch.question.line1": "Rẹ yoo ilẹ yipada si aye miiran", "mco.configure.world.slot.switch.question.line2": "Ṣe o da ọ loju pe o fẹ tẹsiwaju?", "mco.configure.world.slot.tooltip": "<PERSON><PERSON><PERSON> si aye", "mco.configure.world.slot.tooltip.active": "Dapọ", "mco.configure.world.slot.tooltip.minigame": "Yipada si minigame", "mco.configure.world.spawnAnimals": "Awọn ẹranko ti a dapọ", "mco.configure.world.spawnMonsters": "<PERSON><PERSON><PERSON><PERSON> ohun ibanilẹru titobi", "mco.configure.world.spawnNPCs": "Spawn NPCs", "mco.configure.world.spawnProtection": "Spawn protection", "mco.configure.world.spawn_toggle.message": "<PERSON>pa aṣayan yii yoo yọ gbogbo awọn nkan ti o wa tẹlẹ ti iru naa kuro", "mco.configure.world.spawn_toggle.message.npc": "<PERSON><PERSON> aṣayan yii yoo mu gbogbo awọn nkan ti o wa tẹlẹ kuro, bii Awọn ara abule", "mco.configure.world.spawn_toggle.title": "Ikilọ!", "mco.configure.world.status": "<PERSON>po", "mco.configure.world.subscription.day": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.days": "<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.expired": "<PERSON><PERSON>", "mco.configure.world.subscription.extend": "Gba alabapin sii", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON> ju <PERSON>j<PERSON> kan lọ", "mco.configure.world.subscription.month": "osù", "mco.configure.world.subscription.months": "osu", "mco.configure.world.subscription.recurring.daysleft": "Ṣe atunṣe laifọwọyi ni", "mco.configure.world.subscription.recurring.info": "Awọn iyipada ti a ṣe si ṣiṣe alabapin Realms rẹ gẹgẹbi akoko iṣakojọpọ tabi pipa ìdíyelé loorekoore kii yoo ṣe afihan titi ọjọ-iṣiro-owo rẹ ti nbọ.", "mco.configure.world.subscription.remaining.days": "%1$s ọjọ (awọn)", "mco.configure.world.subscription.remaining.months": "%1$s Awọn oṣu (awọn)", "mco.configure.world.subscription.remaining.months.days": "%1$s osu(s), %2$s ọjọ(s)", "mco.configure.world.subscription.start": "Ọjọ ibẹrẹ", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "<PERSON>iko ti o seku", "mco.configure.world.subscription.title": "Ṣiṣe alabapin rẹ", "mco.configure.world.subscription.unknown": "Aimọ", "mco.configure.world.switch.slot": "Ṣẹda aye", "mco.configure.world.switch.slot.subtitle": "Aye yii jẹ asan, yan bi o ṣe le ṣẹda aye rẹ", "mco.configure.world.title": "Ṣe atunto ibugbe:", "mco.configure.world.uninvite.player": "Ṣe o da ọ loju pe o fẹ lati pe '%s' bi?", "mco.configure.world.uninvite.question": "Ṣe o dajudaju pe o fẹ lati mu uninvite", "mco.configure.worlds.title": "<PERSON><PERSON><PERSON><PERSON>", "mco.connect.authorizing": "W<PERSON>le sinu...", "mco.connect.connecting": "Sisopọ si ibugbe...", "mco.connect.failed": "<PERSON>na lati sopọ si ibugbe", "mco.connect.region": "Server region: %s", "mco.connect.success": "Ṣe", "mco.create.world": "Ṣẹda", "mco.create.world.error": "O gbọdọ tẹ orukọ sii!", "mco.create.world.failed": "Failed to create world!", "mco.create.world.reset.title": "Ṣiṣẹda aye...", "mco.create.world.skip": "Foo", "mco.create.world.subtitle": "Ti o ba yan, yan kini aye lati fi si ijọba rẹ", "mco.create.world.wait": "Ṣiṣẹda awọn ijọba...", "mco.download.cancelled": "Gbigba agbaye tuntun", "mco.download.confirmation.line1": "Aye ti o nlọ lati ṣe igbasilẹ tobi ju%s", "mco.download.confirmation.line2": "O ko ni anfani lati gbe agbaye yii si ibugbe rẹ lẹẹkansi", "mco.download.confirmation.oversized": "The world you are going to download is larger than %s\n\nYou won't be able to upload this world to your Realm again", "mco.download.done": "Ṣe igbasilẹ ṣe", "mco.download.downloading": "Gbigba", "mco.download.extracting": "Sisẹ", "mco.download.failed": "Ṣe igbasilẹ kùnà", "mco.download.percent": "%s %%", "mco.download.preparing": "<PERSON><PERSON><PERSON> g<PERSON>ba lati a<PERSON>", "mco.download.resourcePack.fail": "Kuna lati ṣe igbasilẹ idii orisun!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Gbigba agbaye tuntun", "mco.error.invalid.session.message": "Jọwọ gbiyanju tun bẹrẹ Minecraft", "mco.error.invalid.session.title": "<PERSON><PERSON>j<PERSON> ti ko tọna", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON> ti igba atij<PERSON>", "mco.errorMessage.6002": "<PERSON>w<PERSON>n ofin iṣẹ ko gba", "mco.errorMessage.6003": "Download iye to de", "mco.errorMessage.6004": "Ifilelẹ gbejade", "mco.errorMessage.6005": "World locked", "mco.errorMessage.6006": "World is out of date", "mco.errorMessage.6007": "User in too many Realms", "mco.errorMessage.6008": "Invalid Realm name", "mco.errorMessage.6009": "Invalid Realm description", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON><PERSON><PERSON> kan waye, <PERSON><PERSON><PERSON><PERSON> gbiyanju lẹẹkansi nigbamii.", "mco.errorMessage.generic": "An error occurred: ", "mco.errorMessage.initialize.failed": "Failed to initialize Realm", "mco.errorMessage.noDetails": "No error details provided", "mco.errorMessage.realmsService": "An error occurred (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Could not connect to Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Could not check compatible version, got response: %s", "mco.errorMessage.retry": "Retry operation", "mco.errorMessage.serviceBusy": "Awọn ibugbe nšišẹ ni akoko.\nJọwọ gbiyanju lati sopọ si Ijọba rẹ lẹẹkansi ni iṣẹju diẹ", "mco.gui.button": "<PERSON><PERSON><PERSON><PERSON>", "mco.gui.ok": "O dara", "mco.info": "Alaye!", "mco.invited.player.narration": "Invited player %s", "mco.invites.button.accept": "Gba", "mco.invites.button.reject": "Gba", "mco.invites.nopending": "Ko si awọn ipe isunm<PERSON>!", "mco.invites.pending": "Awọn ifiwepe tuntun!", "mco.invites.title": "Ni idaduro awọn ifiwepe", "mco.minigame.world.changeButton": "Yan minigame miiran", "mco.minigame.world.info.line1": "<PERSON><PERSON> yoo r<PERSON><PERSON> aye rẹ fun igba diẹ pẹlu minigame!", "mco.minigame.world.info.line2": "O le pada wa si aye atilẹba rẹ laisi pipadanu ohunkohun.", "mco.minigame.world.noSelection": "Jọwọ ṣe yiyan", "mco.minigame.world.restore": "Ipari minigame...", "mco.minigame.world.restore.question.line1": "Minigame yoo pari ati ijọba rẹ yoo pada.", "mco.minigame.world.restore.question.line2": "Ṣe o da ọ loju pe o fẹ tẹsiwaju?", "mco.minigame.world.selected": "Ti a yan minigame:", "mco.minigame.world.slot.screen.title": "<PERSON><PERSON>a aye...", "mco.minigame.world.startButton": "<PERSON><PERSON><PERSON>", "mco.minigame.world.starting.screen.title": "Bibẹrẹ iṣẹ minigame...", "mco.minigame.world.stopButton": "Opin minigame", "mco.minigame.world.switch.new": "Yan minigame miiran?", "mco.minigame.world.switch.title": "<PERSON><PERSON><PERSON> a<PERSON>a", "mco.minigame.world.title": "Yipada ijọba si minigame", "mco.news": "<PERSON><PERSON><PERSON>n iroyin <PERSON>s", "mco.notification.dismiss": "Kọ silẹ", "mco.notification.transferSubscription.buttonText": "Transfer Now", "mco.notification.transferSubscription.message": "Java Realms subscriptions are moving to the Microsoft Store. Do not let your subscription expire!\nTransfer now and get 30 days of Realms for free.\nGo to Profile on minecraft.net to transfer your subscription.", "mco.notification.visitUrl.buttonText.default": "Ṣii <PERSON>na asop<PERSON>", "mco.notification.visitUrl.message.default": "Jọwọ ṣabẹwo si ọna asopọ ni isalẹ", "mco.onlinePlayers": "Online Players", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "<PERSON><PERSON><PERSON>", "mco.reset.world.adventure": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.experience": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.generate": "New world", "mco.reset.world.inspiration": "Inspiration", "mco.reset.world.resetting.screen.title": "<PERSON>tun aye...", "mco.reset.world.seed": "<PERSON><PERSON><PERSON> (<PERSON>yi je eyi ko je)", "mco.reset.world.template": "<PERSON><PERSON><PERSON><PERSON> awo<PERSON> a<PERSON>e", "mco.reset.world.title": "Tun aye tun", "mco.reset.world.upload": "Gbe si aye", "mco.reset.world.warning": "<PERSON><PERSON> yoo r<PERSON><PERSON> aye ti isiyi ti ijọba rẹ", "mco.selectServer.buy": "Ra ijọba kan!", "mco.selectServer.close": "Pa", "mco.selectServer.closed": "Ile-ilẹ ti o ti pari", "mco.selectServer.closeserver": "Ile-ilẹ ti o ti pari", "mco.selectServer.configure": "Ṣeto iṣakoso ij<PERSON>ba", "mco.selectServer.configureRealm": "Ṣeto iṣakoso ij<PERSON>ba", "mco.selectServer.create": "Ṣẹda ij<PERSON>ba", "mco.selectServer.create.subtitle": "Select what world to put on your new Realm", "mco.selectServer.expired": "Ipinle ti pari", "mco.selectServer.expiredList": "Ṣiṣe alabapin rẹ ti pari", "mco.selectServer.expiredRenew": "Tunse", "mco.selectServer.expiredSubscribe": "Alabapin", "mco.selectServer.expiredTrial": "Iwadii rẹ ti pari", "mco.selectServer.expires.day": "Pa ni <PERSON>j<PERSON> kan", "mco.selectServer.expires.days": "Ṣiṣẹ ni awọn ọjọ %s", "mco.selectServer.expires.soon": "Ṣiṣẹ laipe", "mco.selectServer.leave": "Fi ibugbe silẹ", "mco.selectServer.loading": "Loading Realms List", "mco.selectServer.mapOnlySupportedForVersion": "Yi map ti wa ni laisi ni %s", "mco.selectServer.minigame": "Minigame:", "mco.selectServer.minigameName": "Minigame: %s", "mco.selectServer.minigameNotSupportedInVersion": "Ko le ṣe mu minigame yi ni %s", "mco.selectServer.noRealms": "You don't seem to have a Realm. Add a Realm to play together with your friends.", "mco.selectServer.note": "Akiyesi:", "mco.selectServer.open": "Ṣii ij<PERSON>ba", "mco.selectServer.openserver": "Ṣii ij<PERSON>ba", "mco.selectServer.play": "<PERSON> ṣiṣẹ", "mco.selectServer.popup": "Awọn Imọlẹ jẹ a<PERSON>wu, ọna ti o rọrun lati gbadun aye ere Amiriki kan pẹlu awọn ọrẹ mẹwa ni akoko kan. O ṣe atilẹyin awọn eru ti minigames ati ọpọlọpọ awọn aye aṣa! Nikan ni o ni ibugbe naa nilo lati sanwo.", "mco.selectServer.purchase": "Fi Ijọba kun", "mco.selectServer.trial": "Gba idanwo!", "mco.selectServer.uninitialized": "Tẹ lati bẹrẹ ijọba titun rẹ!", "mco.snapshot.createSnapshotPopup.text": "You are about to create a free Snapshot Realm that will be paired with your paid Realms subscription. This new Snapshot Realm will be accessible for as long as the paid subscription is active. Your paid Realm will not be affected.", "mco.snapshot.createSnapshotPopup.title": "Create Snapshot Realm?", "mco.snapshot.creating": "Creating Snapshot Realm...", "mco.snapshot.description": "Paired with \"%s\"", "mco.snapshot.friendsRealm.downgrade": "You need to be on version %s to join this Realm", "mco.snapshot.friendsRealm.upgrade": "%s needs to upgrade their Realm before you can play from this version", "mco.snapshot.paired": "This Snapshot Realm is paired with \"%s\"", "mco.snapshot.parent.tooltip": "Use the latest release of Minecraft to play on this Realm", "mco.snapshot.start": "Start free Snapshot Realm", "mco.snapshot.subscription.info": "This is a Snapshot Realm that is paired to the subscription of your Realm '%s'. It will stay active for as long as its paired Realm is.", "mco.snapshot.tooltip": "Use Snapshot Realms to get a sneak peek at upcoming versions of Minecraft, which might include new features and other changes.\n\nYou can find your normal Realms in the release version of the game.", "mco.snapshotRealmsPopup.message": "Realms are now available in Snapshots starting with Snapshot 23w41a. Every Realms subscription comes with a free Snapshot Realm that is separate from your normal Java Realm!", "mco.snapshotRealmsPopup.title": "Realms now available in Snapshots", "mco.snapshotRealmsPopup.urlText": "Learn More", "mco.template.button.publisher": "Oludasile", "mco.template.button.select": "Yan", "mco.template.button.trailer": "Trailer", "mco.template.default.name": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>e", "mco.template.info.tooltip": "Wẹẹbu atẹjade", "mco.template.name": "Awose", "mco.template.select.failure": "A ko le ṣe gba atokọ akoonu fun ẹya yii.\nJọwọ ṣayẹwo asopọ intanẹẹti rẹ, tabi tun gbiyanju nigba<PERSON>i.", "mco.template.select.narrate.authors": "Awọ<PERSON> on<PERSON>:%s", "mco.template.select.narrate.version": "ẹya %s", "mco.template.select.none": "<PERSON><PERSON>, o dabi pe akoonu akoonu yii jẹ ofo lọwọlọwọ.\nJọwọ ṣayẹwo pada nigbamii fun akoonu tuntun, tabi ti o ba jẹ <PERSON><PERSON> kan,\n%s.", "mco.template.select.none.linkTitle": "ro pe gbigbe ohunkan funrararẹ", "mco.template.title": "<PERSON><PERSON><PERSON><PERSON> awo<PERSON> a<PERSON>e", "mco.template.title.minigame": "Minigames", "mco.template.trailer.tooltip": "Map trailer", "mco.terms.buttons.agree": "Gba", "mco.terms.buttons.disagree": "Ma ṣe gba", "mco.terms.sentence.1": "Mo ti gba awọn Ile-iṣẹ Minecraft", "mco.terms.sentence.2": "Aw<PERSON>n ofin ti iṣẹ", "mco.terms.title": "<PERSON><PERSON><PERSON><PERSON> ofin imulo <PERSON> ti Iṣẹ", "mco.time.daysAgo": "%1$s day(s) ago", "mco.time.hoursAgo": "%1$s hour(s) ago", "mco.time.minutesAgo": "%1$s minute(s) ago", "mco.time.now": "ni bayi", "mco.time.secondsAgo": "%1$s second(s) ago", "mco.trial.message.line1": "Ṣe o fẹ gba ijọba tirẹ?", "mco.trial.message.line2": "Tẹ ibi fun alaye diẹ sii!", "mco.upload.button.name": "Po si", "mco.upload.cancelled": "Po si pawonre", "mco.upload.close.failure": "Ko le pa ijọba rẹ de, jọwọ gbiyanju lẹẹkansii", "mco.upload.done": "Po si ti ṣetan", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Ikojọpọ kuna! (%s)", "mco.upload.failed.too_big.description": "The selected world is too big. The maximum allowed size is %s.", "mco.upload.failed.too_big.title": "World too big", "mco.upload.hardcore": "Aye ogbontarigi ko le ṣe gbejade!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "<PERSON><PERSON><PERSON> a<PERSON> rẹ", "mco.upload.select.world.none": "Ko si awọn aye ẹlẹyọ kan ti a rii!", "mco.upload.select.world.subtitle": "Jọwọ yan aye ẹyọ kan lati gbejade", "mco.upload.select.world.title": "Po si agbaye", "mco.upload.size.failure.line1": "'%s' ti tobi ju!", "mco.upload.size.failure.line2": "O jẹ%s. Iwọn ti o pọju ti a gba laaye jẹ%s.", "mco.upload.uploading": "Gbigbe '%s'", "mco.upload.verifying": "<PERSON><PERSON><PERSON> a<PERSON> rẹ", "mco.version": "Version: %s", "mco.warning": "<PERSON><PERSON><PERSON>!", "mco.worldSlot.minigame": "Minigame", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "<PERSON><PERSON>", "menu.feedback": "Feedback...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Akopọ Ere", "menu.modded": " (Modded)", "menu.multiplayer": "<PERSON><PERSON><PERSON> pup<PERSON>", "menu.online": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>", "menu.options": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>...", "menu.paused": "Ere ti duro duro bayi", "menu.playdemo": "<PERSON><PERSON>", "menu.playerReporting": "<PERSON><PERSON><PERSON>", "menu.preparingSpawn": "Ngbaradi agbegbe agbegbe:%s %%", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "<PERSON><PERSON>", "menu.reportBugs": "<PERSON><PERSON> asise kan", "menu.resetdemo": "<PERSON><PERSON>", "menu.returnToGame": "<PERSON>da si <PERSON>re", "menu.returnToMenu": "Fipamọ ki o si lọ si Akọle", "menu.savingChunks": "Fifipamọ Chunk", "menu.savingLevel": "Fipamọ aye", "menu.sendFeedback": "<PERSON> idahun han", "menu.server_links": "Server Links...", "menu.server_links.title": "Server Links", "menu.shareToLan": "Šii si LAN", "menu.singleplayer": "<PERSON><PERSON>e", "menu.working": "Ṣiṣẹ...", "merchant.deprecated": "Awọn alagbeja tun pada si igba meji fun ọjọ kan.", "merchant.level.1": "<PERSON><PERSON><PERSON>", "merchant.level.2": "Ibẹkọ", "merchant.level.3": "Oludaduro", "merchant.level.4": "Iwé", "merchant.level.5": "Titunto si", "merchant.title": "%s - %s", "merchant.trades": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>o", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "|", "mount.onboard": "Tẹ%1$s lati kuro", "multiplayer.applyingPack": "<PERSON><PERSON> or<PERSON>un rẹ kun", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "<PERSON>w<PERSON><PERSON> olupin asiri ti wa ni isalẹ. Jọwọ gbiyanju lẹẹkansi nigbamii, binu!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "O ti gba ọ laaye lati ọdọ olupin yii", "multiplayer.disconnect.banned.expiration": "\nIfagile rẹ yoo kuro lori %s", "multiplayer.disconnect.banned.reason": "O ti gba ọ laaye lati ọdọ olupin yii.\nIdi: %s", "multiplayer.disconnect.banned_ip.expiration": "Idinamọ rẹ yoo y<PERSON><PERSON><PERSON> lori %s", "multiplayer.disconnect.banned_ip.reason": "A ko fi adiresi IP rẹ silẹ lati ọdọ olupin yii.\nIdi: %s", "multiplayer.disconnect.chat_validation_failed": "Ikuna ifisilẹ ifiranṣẹ iwiregbe", "multiplayer.disconnect.duplicate_login": "O wọle lati ibi miiran", "multiplayer.disconnect.expired_public_key": "Bọtini gbangba profaili ti pari. Ṣayẹwo pe akoko eto rẹ ti ṣiṣẹpọ, ki o gbiyanju tun bẹrẹ ere rẹ.", "multiplayer.disconnect.flying": "Fò ní ko ṣiṣẹ lori olupin yii", "multiplayer.disconnect.generic": "Ti ge asopọ", "multiplayer.disconnect.idling": "O ti jẹ aṣiṣe fun gun ju!", "multiplayer.disconnect.illegal_characters": "Awọn lẹta ti ko tọ si ni ibaraẹnisọrọ", "multiplayer.disconnect.incompatible": "Onibara ti ko ni ibamu! Jọwọ lo %s", "multiplayer.disconnect.invalid_entity_attacked": "Ṣiyanju lati kolu ohun ti ko tọ", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON><PERSON> naa fi apo ti ko wulo ranṣẹ", "multiplayer.disconnect.invalid_player_data": "Data ẹrọ orin ti ko fẹ", "multiplayer.disconnect.invalid_player_movement": "Packet ẹrọ orin alaiṣẹ ti ko gba", "multiplayer.disconnect.invalid_public_key_signature": "alley monomono", "multiplayer.disconnect.invalid_public_key_signature.new": "Ibuwọlu ti ko tọ fun bọtini gbogbogbo profaili.\nGbiyanju lati tun bẹrẹ ere rẹ.", "multiplayer.disconnect.invalid_vehicle_movement": "Aṣiṣe ọkọ ayọkẹlẹ ti ko tọ", "multiplayer.disconnect.ip_banned": "O ti ni ipese IP lati ọdọ olupin yii", "multiplayer.disconnect.kicked": "Ti gba nipasẹ onišẹ ẹrọ", "multiplayer.disconnect.missing_tags": "Eto ti ko ni awọn ami ti a gba lati ọdọ olupin.\nJọwọ kan si oniṣẹ olupin.", "multiplayer.disconnect.name_taken": "Orukọ naa ti wa tẹlẹ", "multiplayer.disconnect.not_whitelisted": "O ko funfun-akojọ lori olupin yii!", "multiplayer.disconnect.out_of_order_chat": "Jade-ibere-ibere ti a gba. Ṣe eto eto rẹ yipada?", "multiplayer.disconnect.outdated_client": "Onibara aibaramu! Jọwọ lo %s", "multiplayer.disconnect.outdated_server": "Onibara aibaramu! Jọwọ lo %s", "multiplayer.disconnect.server_full": "<PERSON>lu<PERSON>e naa kun!", "multiplayer.disconnect.server_shutdown": "<PERSON><PERSON><PERSON> olupin", "multiplayer.disconnect.slow_login": "Ti gbe gun ju lati wọle", "multiplayer.disconnect.too_many_pending_chats": "Pupọ awọn ifiranṣẹ iwiregbe ti a ko gba", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "Aṣa aṣa lairotẹlẹ lati alabara", "multiplayer.disconnect.unsigned_chat": "Pakẹti iwiregbe ti o gba pẹlu sonu tabi ibuwọlu ti ko tọ.", "multiplayer.disconnect.unverified_username": "Ko kùn lati ṣayẹwo orukọ olumulo!", "multiplayer.downloadingStats": "N gba awọn iṣiro pada...", "multiplayer.downloadingTerrain": "Ilẹ igbimọ...", "multiplayer.lan.server_found": "A ri olupin titun: %s", "multiplayer.message_not_delivered": "<PERSON> le firanṣẹ iwiregbe, ṣayẹwo awọn àkọọlẹ olupin: %s", "multiplayer.player.joined": "%s darapo ere naa", "multiplayer.player.joined.renamed": "%s (eyi ti a mọ ni %s) darapọ mọ ere naa", "multiplayer.player.left": "%s fi ere naa silẹ", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Awọn ẹrọ orin ori a<PERSON>: %s", "multiplayer.requiredTexturePrompt.disconnect": "<PERSON><PERSON><PERSON> nilo idii awọn orisun aṣa", "multiplayer.requiredTexturePrompt.line1": "<PERSON><PERSON><PERSON> yii nilo lilo idii orisun orisun aṣa.", "multiplayer.requiredTexturePrompt.line2": "Iwọ yoo ge asopọ lati ọdọ olupin yii ti o ko ba lo ṣeto ẹya yii.", "multiplayer.socialInteractions.not_available": "Awọn ibaraẹnisọrọ Awujọ wa nikan ni awọn aye pupọ", "multiplayer.status.and_more": "... ati %s siwaju sii ...", "multiplayer.status.cancelled": "Ti fagile", "multiplayer.status.cannot_connect": "Agbara lati sopọ si olupin naa", "multiplayer.status.cannot_resolve": "<PERSON> le yanju or<PERSON> o<PERSON>", "multiplayer.status.finished": "<PERSON><PERSON>", "multiplayer.status.incompatible": "Ẹya ti ko ni ibamu!", "multiplayer.status.motd.narration": "Ifiranṣẹ ti ọjọ naa: %s", "multiplayer.status.no_connection": "(ko si asop<PERSON>)", "multiplayer.status.old": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s awọn iṣẹju-aaya", "multiplayer.status.pinging": "Pinging...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s jade ti %s awọn ẹrọ orin lori a<PERSON>a", "multiplayer.status.quitting": "<PERSON><PERSON>", "multiplayer.status.request_handled": "<PERSON><PERSON>re ipo ti wa ni <PERSON>w<PERSON>", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Ti gba ipo ti a ko beere", "multiplayer.status.version.narration": "Ẹya olupin: %s", "multiplayer.stopSleeping": "Fi Ibugbe silẹ", "multiplayer.texturePrompt.failure.line1": "A ko le lo package ohun elo olupin naa", "multiplayer.texturePrompt.failure.line2": "Iṣẹ eyikeyi ti o nilo awọn orisun aṣa le ma ṣiṣẹ bi o ti ṣe yẹ", "multiplayer.texturePrompt.line1": "Olupese yii ṣe iṣeduro lilo ti aṣejọ iṣowo.", "multiplayer.texturePrompt.line2": "Ṣe o fẹ lati gba lati ayelujara ki o fi sori ẹrọ ti o ni idojuk<PERSON>?", "multiplayer.texturePrompt.serverPrompt": "%s\nIfiranṣẹ lati ọdọ olupin:\n%s", "multiplayer.title": "<PERSON> ṣiṣẹ lori pupọ", "multiplayer.unsecureserver.toast": "Awọn ifiranṣẹ ti a firanṣẹ sori olupin yii le jẹ atunṣe ati pe o le ma ṣe afihan ifiranṣẹ atilẹba naa", "multiplayer.unsecureserver.toast.title": "Awọn ifiranṣẹ iwiregbe ko le jẹri", "multiplayerWarning.check": "Maṣe han iboju yii lẹẹkansi", "multiplayerWarning.header": "<PERSON><PERSON><PERSON><PERSON>: Play-Party Online Party ti ẹnikẹta", "multiplayerWarning.message": "Akiyesi: <PERSON><PERSON> ori a<PERSON> jẹ eyiti a funni nipasẹ awọn olupin ẹnikẹta ti ko ni ohun ini, ti o ṣiṣẹ tabi iṣakoso nipasẹ Mojang Studios tabi Microsoft. Nigbati o nṣire lori ayelujar<PERSON>, o le ba pade awọn ifiranṣẹ iwiregbe ailorukọ tabi awọn oriṣi miiran ti akoonu ti ipilẹṣẹ olumulo ti o le ma dara fun gbogbo eniyan.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Bọtini %s", "narration.button.usage.focused": "Tẹ Enter lati muu ṣiṣẹ", "narration.button.usage.hovered": "<PERSON><PERSON> tẹ lati muu ṣiṣẹ", "narration.checkbox": "Apoti: %s", "narration.checkbox.usage.focused": "Tẹ Tẹ lati yipada", "narration.checkbox.usage.hovered": "<PERSON><PERSON> tẹ lati yipada", "narration.component_list.usage": "Tẹ Tab lati lilö kiri si eroja atẹle", "narration.cycle_button.usage.focused": "Tẹ Enter lati yipada si %s", "narration.cycle_button.usage.hovered": "<PERSON>si tẹ lati yipada si", "narration.edit_box": "Apoti ṣiṣatunk<PERSON>: %s", "narration.item": "Item: %s", "narration.recipe": "Ohun èlò fún %s", "narration.recipe.usage": "<PERSON><PERSON> tẹ lati yan", "narration.recipe.usage.more": "Ọtun tẹ lati fihan awọn ilana diẹ sii", "narration.selection.usage": "Tẹ bọtini isalẹ ati isalẹ lati gbe si titẹsi miiran", "narration.slider.usage.focused": "Tẹ awọn bọ<PERSON>i itẹwe osi tabi ọtun lati yi iye pada", "narration.slider.usage.hovered": "Fa esun lati yi iye pada", "narration.suggestion": "Àṣìṣe tí a yàn %s sí %s: %s", "narration.suggestion.tooltip": "Àṣìṣe tí a yàn %s sí %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Press Tab to cycle to the next suggestion", "narration.suggestion.usage.cycle.hidable": "Press Tab to cycle to the next suggestion, or Escape to leave suggestions", "narration.suggestion.usage.fill.fixed": "Press Tab to use suggestion", "narration.suggestion.usage.fill.hidable": "Press Tab to use suggestion, or Escape to leave suggestions", "narration.tab_navigation.usage": "Tẹ Kont<PERSON>lu ati Taabu lati yipada laarin awọn taabu", "narrator.button.accessibility": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.difficulty_lock": "Tiipa isoro", "narrator.button.difficulty_lock.locked": "Titii pa", "narrator.button.difficulty_lock.unlocked": "Ṣiṣi silẹ", "narrator.button.language": "<PERSON><PERSON>", "narrator.controls.bound": "%s ti wa ni owun si %s", "narrator.controls.reset": "Tun b<PERSON><PERSON>i sita %s", "narrator.controls.unbound": "%s ko ni owun", "narrator.joining": "Didapọ", "narrator.loading": "Ikojọpọ: %s", "narrator.loading.done": "<PERSON>i <PERSON><PERSON>", "narrator.position.list": "Ti a yan akojọ ti", "narrator.position.object_list": "Ti a yan kana jade ninu", "narrator.position.screen": "<PERSON>boju iboju kuro ninu", "narrator.position.tab": "Ti yan taabu %s jade ninu %s", "narrator.ready_to_play": "<PERSON>an lati mu", "narrator.screen.title": "<PERSON><PERSON><PERSON>", "narrator.screen.usage": "Lo k<PERSON>sọ <PERSON>in tabi bọtini taabu lati yan ano", "narrator.select": "Ti yan: %s", "narrator.select.world": "Ti a yan %s, ṣiṣe ni o kẹhin %s, %s, %s, version %s", "narrator.select.world_info": "Selected %s, last played: %s, %s", "narrator.toast.disabled": "<PERSON><PERSON><PERSON><PERSON>", "narrator.toast.enabled": "Oluṣeto Ti n ṣatunṣe", "optimizeWorld.confirm.description": "Aye rẹ yoo wa ni iṣapeye, rii daju pe gbogbo data ti wa ni ipamọ ni ọna kika titun. O le gba igba pipọ ti o da lori aye rẹ. Lọgan ti pari, o le ṣiṣe ni kiakia, ṣugbọn kii yoo ni ibamu pẹlu ẹya agbalagba ti ere naa. Ṣe o da ọ loju pe o fẹ tẹsiwaju?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "Mu aye kun", "optimizeWorld.info.converted": "Ilọsiwaju ti chunks: %s", "optimizeWorld.info.skipped": "Omitted chunks: %s", "optimizeWorld.info.total": "Gbogbo chunks: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Tika chunks...", "optimizeWorld.stage.failed": "Ikuna! :(", "optimizeWorld.stage.finished": "Finishing up...", "optimizeWorld.stage.finished.chunks": "Finishing up upgrading chunks...", "optimizeWorld.stage.finished.entities": "Finishing up upgrading entities...", "optimizeWorld.stage.finished.poi": "Finishing up upgrading points of interest...", "optimizeWorld.stage.upgrading": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.chunks": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.entities": "Upgrading all entities...", "optimizeWorld.stage.upgrading.poi": "Upgrading all points of interest...", "optimizeWorld.title": "Ṣiṣayẹwo aye \"%s\"", "options.accessibility": "Accessibility Settings...", "options.accessibility.high_contrast": "Iyat<PERSON> giga", "options.accessibility.high_contrast.error.tooltip": "<PERSON><PERSON><PERSON> ohun elo Itansan giga ko si", "options.accessibility.high_contrast.tooltip": "Ṣe ilọsiwaju iyatọ ti awọn eroja UI", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "<PERSON><PERSON><PERSON><PERSON><PERSON> Wiw<PERSON>le", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON> Blur", "options.accessibility.menu_background_blurriness.tooltip": "Changes the blurriness of menu backgrounds.", "options.accessibility.narrator_hotkey": "Narrator <PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Allows the Narrator to be toggled on and off with 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Allows the Narrator to be toggled on and off with 'Ctrl+B'.", "options.accessibility.panorama_speed": "<PERSON>yara Yi lọ Panorama", "options.accessibility.text_background": "Ọrọ ti abẹlẹ", "options.accessibility.text_background.chat": "Iwiregbe", "options.accessibility.text_background.everywhere": "Nigbagbogbo", "options.accessibility.text_background_opacity": "Text Isale Opacity", "options.accessibility.title": "<PERSON><PERSON> ...", "options.allowServerListing": "Gba Awọn ako<PERSON> o<PERSON> la<PERSON>e", "options.allowServerListing.tooltip": "Awọn olupin le ṣe atokọ awọn oṣere ori ayelujara gẹgẹbi apakan ti ipo gbogbo eniyan.\nPẹlu aṣayan yi pa orukọ rẹ yoo ko han soke ni iru awọn akojọ.", "options.ao": "<PERSON>m<PERSON> ina", "options.ao.max": "<PERSON><PERSON><PERSON><PERSON>", "options.ao.min": "<PERSON><PERSON>", "options.ao.off": "PA", "options.attack.crosshair": "-<PERSON><PERSON>", "options.attack.hotbar": "Ọpa irinṣe", "options.attackIndicator": "<PERSON>w<PERSON><PERSON> i<PERSON>lu", "options.audioDevice": "Ẹrọ", "options.audioDevice.default": "Aiyipada <PERSON>", "options.autoJump": "Idojukọ-<PERSON><PERSON><PERSON>", "options.autoSuggestCommands": "Abajade aṣẹ", "options.autosaveIndicator": "Atọka fifipamọ aifọwọyi", "options.biomeBlendRadius": "Biome Blend", "options.biomeBlendRadius.1": "PA (Sare ju)", "options.biomeBlendRadius.11": "11x11 (Gigaju)", "options.biomeBlendRadius.13": "13x13 (Showoff)", "options.biomeBlendRadius.15": "15x15 (<PERSON>)", "options.biomeBlendRadius.3": "3x3 (<PERSON><PERSON>)", "options.biomeBlendRadius.5": "5x5 (<PERSON><PERSON>)", "options.biomeBlendRadius.7": "7x7 (Ga)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON><PERSON> Ga julọ)", "options.chat": "Cha<PERSON>s...", "options.chat.color": "<PERSON>w<PERSON><PERSON> awọ", "options.chat.delay": "<PERSON><PERSON><PERSON> i<PERSON>: %s awọn aaya", "options.chat.delay_none": "<PERSON><PERSON><PERSON>: <PERSON><PERSON> si", "options.chat.height.focused": "Igi ti a fiyesi", "options.chat.height.unfocused": "<PERSON><PERSON>", "options.chat.line_spacing": "Spacing Line", "options.chat.links": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>iwe <PERSON>", "options.chat.links.prompt": "<PERSON><PERSON>n lori <PERSON> isopọ", "options.chat.opacity": "Opacity Oro Iwadi", "options.chat.scale": "Iwọn Text Awo", "options.chat.title": "Awọn eto iwiregbe...", "options.chat.visibility": "Iwiregbe", "options.chat.visibility.full": "Han", "options.chat.visibility.hidden": "<PERSON><PERSON><PERSON>", "options.chat.visibility.system": "<PERSON><PERSON><PERSON><PERSON> ase nikan", "options.chat.width": "<PERSON><PERSON><PERSON><PERSON>", "options.chunks": "%s baṣi ẹran", "options.clouds.fancy": "Fancy", "options.clouds.fast": "<PERSON><PERSON>", "options.controls": "<PERSON><PERSON><PERSON><PERSON> iṣak<PERSON>...", "options.credits_and_attribution": "Awọn Kirẹditi & Itọkasi...", "options.damageTiltStrength": "Ibajẹ Pulọọgi", "options.damageTiltStrength.tooltip": "Iwọn gbigbọn kamẹra ti o ṣẹlẹ nipasẹ ipalara.", "options.darkMojangStudiosBackgroundColor": "Monochrome Logo", "options.darkMojangStudiosBackgroundColor.tooltip": "<PERSON>w<PERSON>n ile-iṣẹ Mojang ṣe ayipada abẹlẹ ti iboju si dudu.", "options.darknessEffectScale": "<PERSON>unkun pulsering", "options.darknessEffectScale.tooltip": "Iṣakoso bi o ṣe ṣe ipa awọn pulses nigbati a ba ri ile-ẹwọn kan tabi Sculk Shrieker fun ọ.", "options.difficulty": "Roro", "options.difficulty.easy": "<PERSON><PERSON><PERSON>", "options.difficulty.easy.info": "ṣodi mobs spawn sugbon mu kere bibajẹ. Ebi bar depletes ati drains ilera si isalẹ lati 5 ọkàn.", "options.difficulty.hard": "<PERSON><PERSON>", "options.difficulty.hard.info": "ṣodi mobs spawn ati ki o mu diẹ bibajẹ. Ebi bar depletes ati drains gbogbo ilera.", "options.difficulty.hardcore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.difficulty.normal": "<PERSON><PERSON>", "options.difficulty.normal.info": "ṣodi mobs spawn ati ki o mu boṣewa bibajẹ. Igi ebi npa ati ki o fa ilera silẹ si idaji ọkan.", "options.difficulty.online": "<PERSON><PERSON> o<PERSON>", "options.difficulty.peaceful": "Alaafia", "options.difficulty.peaceful.info": "Ko si ṣodi mobs ati ki o nikan diẹ ninu awọn didoju mobs spawn. Pẹpẹ ebi ko dinku ati pe ilera n kun lori akoko.", "options.directionalAudio": "Audio", "options.directionalAudio.off.tooltip": "Classic Sitẹrio ohun", "options.directionalAudio.on.tooltip": "Nlo ohun afetigbọ itọnisọna orisun HRTF lati mu kikopa ohun 3D dara si. Nilo ohun elo ohun afetigbọ ibaramu HRTF, ati pe o ni iriri ti o dara julọ pẹlu agbekọri.", "options.discrete_mouse_scroll": "Iyatọ Ti o Dara", "options.entityDistanceScaling": "<PERSON><PERSON><PERSON> si nkankan", "options.entityShadows": "<PERSON><PERSON><PERSON><PERSON> Ẹrọ", "options.font": "Font Settings...", "options.font.title": "Font Settings", "options.forceUnicodeFont": "Fi agbara mu Ẹrọ Unicode", "options.fov": "FOV", "options.fov.max": "Quake Pro", "options.fov.min": "<PERSON><PERSON>", "options.fovEffectScale": "Awọn ipa FOV", "options.fovEffectScale.tooltip": "Ṣakoso elo ni aaye wiwo le yipada pẹlu awọn ipa imuṣere.", "options.framerate": "%s Awọn fireemu fun keji", "options.framerateLimit": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>u to pọ<PERSON> fun keji", "options.framerateLimit.max": "<PERSON><PERSON><PERSON>", "options.fullscreen": "Gbogbo si<PERSON>", "options.fullscreen.current": "Lọwọlọwọ", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "Iwọn iboju kikun", "options.fullscreen.unavailable": "Eto ko si", "options.gamma": "Imọlẹ", "options.gamma.default": "<PERSON><PERSON><PERSON><PERSON>", "options.gamma.max": "Imọlẹ", "options.gamma.min": "Irẹwẹsi", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON><PERSON>", "options.glintSpeed.tooltip": "Ṣakoso bi o ṣe yara awọn glint wiwo ti n ta kaakiri awọn ohun kan ti o wuyi.", "options.glintStrength": "<PERSON><PERSON>", "options.glintStrength.tooltip": "Ṣakoso bi o ṣe han gbangba glint wiwo wa lori awọn ohun kan ti o wuyi.", "options.graphics": "<PERSON><PERSON>", "options.graphics.fabulous": "Ayeye!", "options.graphics.fabulous.tooltip": "%s awọn apẹẹrẹ nlo awọn ifa iboju fun ọj<PERSON>, a<PERSON><PERSON><PERSON><PERSON>, ati awọn patikulu lẹhin awọn bulọọki translucent ati omi.\nEyi le ni ikolu ti o nira pupọ fun awọn ẹrọ to ṣee gbe ati awọn ifihan 4K.", "options.graphics.fancy": "Fancy", "options.graphics.fancy.tooltip": "Awọn eya aworan Fancy ṣe iwọn iṣẹ ati didara fun ọpọlọpọ awọn ero.\n<PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON>, ati awọn patikulu le ma han lẹhin awọn bulọọki translucent tabi omi.", "options.graphics.fast": "<PERSON><PERSON>", "options.graphics.fast.tooltip": "Быстрая\" графика снижает количество видимого снега и дождя.\nЭффект прозрачности выключен для некоторых блоков типа листвы.", "options.graphics.warning.accept": "Continue Without Support", "options.graphics.warning.cancel": "Pada", "options.graphics.warning.message": "A ṣe awari ẹrọ awọn ohun elo rẹ bi ko ṣe atilẹyin fun aṣayan awọn apẹẹrẹ%s.\n\nO le foju eyi ki o tẹsiwaju, sibẹsibẹ atilẹyin fun ẹrọ rẹ kii yoo pese ti o ba yan lati lo awọn aworan iyasọtọ ti %s.", "options.graphics.warning.renderer": "Renderer ri: [%s]", "options.graphics.warning.title": "Ẹrọ Ẹya-ara yii ko ni atilẹyin", "options.graphics.warning.vendor": "Olupese ti ri: [%s]", "options.graphics.warning.version": "Ẹya OpenGL ti a rii: [%s]", "options.guiScale": "Ilana GAII", "options.guiScale.auto": "Idojukọ", "options.hidden": "<PERSON><PERSON><PERSON>", "options.hideLightningFlashes": "<PERSON><PERSON><PERSON> Imọlẹ Itanna", "options.hideLightningFlashes.tooltip": "Prevents Lightning Bolts from making the sky flash. The bolts themselves will still be visible.", "options.hideMatchedNames": "<PERSON><PERSON><PERSON> orukọ ti o baamu", "options.hideMatchedNames.tooltip": "3rd-party Servers may send chat messages in non-standard formats.\nWith this option on, hidden players will be matched based on chat sender names.", "options.hideSplashTexts": "Hide Splash Texts", "options.hideSplashTexts.tooltip": "Hides the yellow splash text in the main menu.", "options.inactivityFpsLimit": "Reduce FPS when", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimized", "options.inactivityFpsLimit.minimized.tooltip": "Limits framerate only when the game window is minimized.", "options.invertMouse": "Invert <PERSON>in", "options.japaneseGlyphVariants": "Japanese Glyph Variants", "options.japaneseGlyphVariants.tooltip": "Uses Japanese variants of CJK characters in the default font.", "options.key.hold": "Mu", "options.key.toggle": "<PERSON><PERSON><PERSON>", "options.language": "Ede...", "options.language.title": "Language", "options.languageAccuracyWarning": "(Language translations may not be 100%% accurate)", "options.languageWarning": "Awọn itọnisọna ede le ma ni pipe 100%%", "options.mainHand": "Akọkọ Ọwọ", "options.mainHand.left": "<PERSON><PERSON>", "options.mainHand.right": "Ọtun", "options.mipmapLevels": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.cape": "<PERSON><PERSON>", "options.modelPart.hat": "<PERSON><PERSON><PERSON><PERSON>", "options.modelPart.jacket": "<PERSON><PERSON>", "options.modelPart.left_pants_leg": "Orisun Ẹsẹ-sosi", "options.modelPart.left_sleeve": "Ogbon apa osi", "options.modelPart.right_pants_leg": "Ọpa Ẹsẹ Ọtun", "options.modelPart.right_sleeve": "Ọwọ Ọtun", "options.mouseWheelSensitivity": "Sensitivity lilọ kiri", "options.mouse_settings": "<PERSON><PERSON><PERSON><PERSON>...", "options.mouse_settings.title": "<PERSON><PERSON><PERSON><PERSON>", "options.multiplayer.title": "Eto Awọn Ọpọlọpọ...", "options.multiplier": "%sx", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "Oluwaworan", "options.narrator.all": "N Sọ Gbogbo Ẹ", "options.narrator.chat": "Ntọrọ iwiregbe", "options.narrator.notavailable": "<PERSON> si", "options.narrator.off": "PA", "options.narrator.system": "Nisalẹ ẹrọ", "options.notifications.display_time": "<PERSON><PERSON><PERSON>", "options.notifications.display_time.tooltip": "Ni ipa lori ipari akoko ti gbogbo awọn iwifunni duro han loju iboju.", "options.off": "PA", "options.off.composed": "%s: PA", "options.on": "TAN", "options.on.composed": "%s: LATI", "options.online": "lóníforíkorí...", "options.online.title": "lónífor<PERSON><PERSON><PERSON> a<PERSON>n a<PERSON>n", "options.onlyShowSecureChat": "Ṣe a<PERSON>han iwiregbe aabo nikan", "options.onlyShowSecureChat.tooltip": "<PERSON><PERSON><PERSON>an awọn ifiranṣẹ lati awọn ẹrọ orin miiran ti o le rii daju lati firanṣẹ nipasẹ ẹrọ orin yẹn, ati pe ko yipada.", "options.operatorItemsTab": "Onišẹ <PERSON><PERSON><PERSON><PERSON> ohun <PERSON>b", "options.particles": "Patik<PERSON>", "options.particles.all": "Gbogbo", "options.particles.decreased": "Din ku", "options.particles.minimal": "Pọọku", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %s%%", "options.prioritizeChunkUpdates": "kéé<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Diẹ ninu awọn iṣe laarin chunk kan yoo tun ṣajọpọ chunk naa lẹsẹkẹsẹ. Eyi pẹlu idinamọ gbigbe & iparun.", "options.prioritizeChunkUpdates.nearby": "Dina ni kikun", "options.prioritizeChunkUpdates.nearby.tooltip": "Awọn ege to wa nitosi nigbagbogbo ni akopọ lẹsẹkẹsẹ. Eyi le ni ipa lori iṣẹ ṣiṣe ere nigbati awọn bulọọki gbe tabi parun.", "options.prioritizeChunkUpdates.none": "Asapo", "options.prioritizeChunkUpdates.none.tooltip": "Awọn ege ti o wa nitosi jẹ akojọpọ ni awọn okun to jọra. Eleyi le ja si ni finifini visual ihò nigba ti ohun amorindun ti wa nirun", "options.rawMouseInput": "Aise input", "options.realmsNotifications": "Realms News & ifiwepe", "options.realmsNotifications.tooltip": "Fetches Realms news and invites in the title screen and displays their respective icon on the Realms button.", "options.reducedDebugInfo": "<PERSON>", "options.renderClouds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "<PERSON><PERSON>na Ṣe atunṣe", "options.resourcepack": "Awọn akopọ Iṣura...", "options.rotateWithMinecart": "Rotate with Minecarts", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "Awọn ipa ipa<PERSON>", "options.screenEffectScale.tooltip": "Ipa inu riru ati awọn ipa iparo ti iboju oju ọna Netheral Nether.\nAwọn iye isalẹ rọpo ipa rirẹ nipasẹ superimposing alawọ ewe loju iboju.", "options.sensitivity": "Ifam<PERSON>", "options.sensitivity.max": "GBẸHIN LILE!!!", "options.sensitivity.min": "*paade*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "Fi awọn akọkọ silẹ", "options.simulationDistance": "<PERSON><PERSON><PERSON>", "options.skinCustomisation": "Aṣaṣe ti ara...", "options.skinCustomisation.title": "Aṣaṣe ti ara", "options.sounds": "Orin ati <PERSON>n ohùn...", "options.sounds.title": "Orin & Awọn a<PERSON> ohun", "options.telemetry": "Data Telemetry...", "options.telemetry.button": "Gbigba data", "options.telemetry.button.tooltip": "\"%s\" pẹlu nikan data ti a beere.\n\"%s\" pẹlu iyan, bakanna bi data ti a beere.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON><PERSON> is disabled.", "options.telemetry.state.all": "Gbogbo", "options.telemetry.state.minimal": "<PERSON><PERSON><PERSON>", "options.telemetry.state.none": "<PERSON> si", "options.title": "<PERSON><PERSON><PERSON><PERSON>", "options.touchscreen": "<PERSON><PERSON>", "options.video": "Awọn eto fidio...", "options.videoTitle": "Awọn eto fidio", "options.viewBobbing": "Wiwo ti nrin", "options.visible": "Han", "options.vsync": "Amuṣiṣẹpọ inaro", "outOfMemory.message": "Minecraft ti pari iranti.\n\nEyi le ṣẹlẹ nipasẹ kokoro kan ninu ere tabi nipasẹ Ẹrọ Foju Java ti a ko pin si iranti to.\n\nLati ṣe idiwọ ibajẹ ipele, ere lọwọlọwọ ti jáwọ. A ti gbiyanju lati gba iranti laaye lati jẹ ki o pada si akojọ aṣayan akọkọ ati pada si ṣiṣiṣẹ, ṣugbọn eyi le ma ṣiṣẹ.\n\nJọwọ tun bẹrẹ ere ti o ba tun ri ifiranṣẹ yii lẹẹkansi.", "outOfMemory.title": "Jade ti iranti!", "pack.available.title": "Доступен", "pack.copyFailure": "O kuna lati daakọ awọn akopọ", "pack.dropConfirm": "Ṣe o fẹ lati ṣ<PERSON>ikun awọn akopọ wọnyi si Minecraft?", "pack.dropInfo": "Fa ati ju silẹ awọn faili sinu window yii lati ṣafikun awọn akopọ", "pack.dropRejected.message": "The following entries were not valid packs and were not copied:\n %s", "pack.dropRejected.title": "Non-pack entries", "pack.folderInfo": "(Gbe awọn faili idii nibi)", "pack.incompatible": "<PERSON><PERSON><PERSON>", "pack.incompatible.confirm.new": "Ti ṣe idii yii fun ikede tuntun ti Minecraft ati pe o le ma ṣiṣẹ daradara.", "pack.incompatible.confirm.old": "A ṣe idii yii fun ẹya atijọ ti Minecraft ati pe o le ma ṣiṣẹ daradara.", "pack.incompatible.confirm.title": "Вы уверены, что хотите загрузить этот пак?", "pack.incompatible.new": "(Ṣeda fun ẹya tuntun ti Minecraft)", "pack.incompatible.old": "(Ṣeda fun ẹya ti atijọ ti Minecraft)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "Ṣii <PERSON>a <PERSON>olda", "pack.selected.title": "Выбрано", "pack.source.builtin": "-itumọ ti ni", "pack.source.feature": "ẹya-ara", "pack.source.local": "agbegbe", "pack.source.server": "olupin", "pack.source.world": "a<PERSON><PERSON>e", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Ede Albania", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "<PERSON><PERSON>", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "<PERSON>g<PERSON><PERSON>", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Hello Ọgbẹni Courtbet", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "<PERSON><PERSON>", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab pẹlu mẹta pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "<PERSON><PERSON>", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Etikun", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Òkú Òkú", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "<PERSON><PERSON> ati <PERSON>", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "Ipele ti Ṣeto", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "Iw<PERSON><PERSON><PERSON>_ipon", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "ofo ni", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "Alarinkiri", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Ilẹ̀ ahoro", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "<PERSON><PERSON>", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Afẹfẹ", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "Yíyọ", "painting.random": "<PERSON>ya<PERSON><PERSON> laileto", "parsing.bool.expected": "Boolean ti o nireti", "parsing.bool.invalid": "Boolean ti ko wulo, o ti ṣe yẹ 'otitọ' tabi 'eke' ṣugbọn o ri '%s'", "parsing.double.expected": "O ti ṣe yẹ ilọpo meji", "parsing.double.invalid": "Ìlọ́po méjì '%s'", "parsing.expected": "O ti ṣe yẹ '%s'", "parsing.float.expected": "Ofofo ti o ti ṣe yẹ", "parsing.float.invalid": "Leefofo loju omi ti ko wulo '%s'", "parsing.int.expected": "O ti ṣe yẹ odidi", "parsing.int.invalid": "Nọmba odidi ti ko tọna '%s'", "parsing.long.expected": "Ti ṣe yẹ gun", "parsing.long.invalid": "Gígùn tí kò fẹsẹ̀múlẹ̀ '%s'", "parsing.quote.escape": "Ọ̀nà àbájáde àìfẹsẹ̀múlẹ̀ '\\%s' nínú okùn tí a ṣàyọlò", "parsing.quote.expected.end": "<PERSON><PERSON> agbasọ ti a ko sọ", "parsing.quote.expected.start": "Ti ṣe yẹ agbasọ lati bẹrẹ okun kan", "particle.invalidOptions": "Can't parse particle options: %s", "particle.notFound": "Patiku ti a ko mọ: %s", "permissions.requires.entity": "A nilo nkan lati ṣiṣẹ aṣẹ yii nibi", "permissions.requires.player": "O nilo ẹrọ orin lati ṣiṣẹ aṣẹ yii nibi", "potion.potency.1": "II", "potion.potency.2": "III", "potion.potency.3": "IV", "potion.potency.4": "V", "potion.potency.5": "VI", "potion.whenDrank": "Nigbati o ba lo:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "Aimọ asọtẹlẹ:%s", "quickplay.error.invalid_identifier": "Ko le ri aye pẹlu idamo ti a pese", "quickplay.error.realm_connect": "Ko le sopọ si Ijọba", "quickplay.error.realm_permission": "Aini igbanilaaye lati sopọ si Ijọba yii", "quickplay.error.title": "Ti kuna lati Ṣiṣere kiakia", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "Imudojuiwọn ti ko ni atilẹyin ni titẹsi bayi", "recipe.notFound": "<PERSON><PERSON>lo aimọ: %s", "recipe.toast.description": "Ṣayẹwo iwe ohunelo rẹ", "recipe.toast.title": "Awọn ilana titun Ṣiṣi silẹ!", "record.nowPlaying": "Bayi ndun:%s", "recover_world.bug_tracker": "Report a Bug", "recover_world.button": "Attempt to Recover", "recover_world.done.failed": "Failed to recover from previous state.", "recover_world.done.success": "Recovery was successful!", "recover_world.done.title": "Recovery done", "recover_world.issue.missing_file": "Missing file", "recover_world.issue.none": "No issues", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "Attempt to Restore", "recover_world.restoring": "Attempting to restore world...", "recover_world.state_entry": "State from %s: ", "recover_world.state_entry.unknown": "unknown", "recover_world.title": "Failed to load world", "recover_world.warning": "Failed to load world summary", "resourcePack.broken_assets": "TI ṢE AWARI OHUN-INI TI A ṢE", "resourcePack.high_contrast.name": "Iyat<PERSON> giga", "resourcePack.load_fail": "Gbigbawọle awọn olu <PERSON>adi kuna", "resourcePack.programmer_art.name": "Olupilẹṣẹ Art", "resourcePack.runtime_failure": "Resource pack error detected", "resourcePack.server.name": "Oro Pataki Agbaye", "resourcePack.title": "<PERSON> ohun elo", "resourcePack.vanilla.description": "Wiwo aiyipada ati rilara ti Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON><PERSON>", "resourcepack.downloading": "Gbigba Pack Resource", "resourcepack.progress": "Gbigba faili (%s MB)...", "resourcepack.requesting": "Ṣiṣe ibeere...", "screenshot.failure": "Ko le fi sikirinifoto pamọ: %s", "screenshot.success": "Sikirinifoto ti o fipamọ bi %s", "selectServer.add": "Fi Asopọ sii", "selectServer.defaultName": "<PERSON><PERSON><PERSON>craft", "selectServer.delete": "Paarẹ", "selectServer.deleteButton": "Paarẹ", "selectServer.deleteQuestion": "Ṣe o da ọ loju pe o fẹ yọ iru olupin yii kuro?", "selectServer.deleteWarning": "\"%s\" yoo sọnu lailai! (fun igba pipe pupọ!)", "selectServer.direct": "As<PERSON><PERSON> taara", "selectServer.edit": "Ṣatunkọ", "selectServer.hiddenAddress": "(<PERSON><PERSON><PERSON>)", "selectServer.refresh": "<PERSON><PERSON>", "selectServer.select": "Darapọ mọ olupin", "selectWorld.access_failure": "<PERSON>na lati wọle si agbaye", "selectWorld.allowCommands": "Gba Iyanjẹ", "selectWorld.allowCommands.info": "Aṣẹ bi /gamemode, /experience", "selectWorld.allowCommands.new": "Allow Commands", "selectWorld.backupEraseCache": "Pa data ti a kuro", "selectWorld.backupJoinConfirmButton": "Ṣẹda afẹyinti ati fifuye", "selectWorld.backupJoinSkipButton": "Mo mọ ohun ti Mo n ṣe!", "selectWorld.backupQuestion.customized": "Aye ti ara ẹni ko ni atilẹyin", "selectWorld.backupQuestion.downgrade": "Yi<PERSON><PERSON> aye kan ko ni atilẹyin", "selectWorld.backupQuestion.experimental": "Awọn agbaye lilo Awọn Eto Idanwo ko ni atilẹyin", "selectWorld.backupQuestion.snapshot": "Ṣe o fẹ gaan lati fifuye aye yii?", "selectWorld.backupWarning.customized": "<PERSON><PERSON>, a ko ṣe atilẹyin fun aye ti ara ẹni ni ẹya ti Minecraft. A tun le fifuye aye yii ki o si pa ohun gbogbo mọ bi o ti jẹ, ṣugbọn aaye ti a gbejade titun ni a ko le ṣe deede. A tọrọ gafara fun nkan inira naa!", "selectWorld.backupWarning.downgrade": "Aye yii ti dun kẹhin ni ẹya%s; o wa lori ẹya%s. Idoju aye kan le fa ibajẹ - a ko le ṣe idaniloju pe yoo kojọpọ tabi ṣiṣẹ. Ti o ba tun fẹ tẹsiwaju, jọwọ ṣe afẹyinti!", "selectWorld.backupWarning.experimental": "Aiye yi nlo esiperimenta eto ti o le da ṣiṣẹ ni eyikeyi akoko. A ko le ṣe iṣeduro pe yoo fifuye tabi ṣiṣẹ. Jọwọ jẹ ṣọra!", "selectWorld.backupWarning.snapshot": "Aye yi gbẹyin dun ni version %s; o wa lori ẹya %s. Jọwọ ṣe afẹyinti ni irú ti o ba ni iriri awọn ibajẹ aye!", "selectWorld.bonusItems": "Ẹya ajeseku", "selectWorld.cheats": "Iyanjẹ", "selectWorld.commands": "Commands", "selectWorld.conversion": "Gbọdọ wa ni iyipada!", "selectWorld.conversion.tooltip": "Aye yii gbọdọ wa ni ṣiṣi ni ẹya agbalagba (bii 1.6.4) lati ṣe iyipada lailewu", "selectWorld.create": "Ṣẹda Aye tuntun", "selectWorld.customizeType": "Ṣe akanṣe", "selectWorld.dataPacks": "Awọn a<PERSON>", "selectWorld.data_read": "Kika data agbaye...", "selectWorld.delete": "Paarẹ", "selectWorld.deleteButton": "Paarẹ", "selectWorld.deleteQuestion": "Ṣe o da ọ loju pe o fẹ pa aye yii run?", "selectWorld.deleteWarning": "Aye \"%s\" yoo padanu lailai! (o gan gun!)", "selectWorld.delete_failure": "<PERSON><PERSON> lati <PERSON><PERSON>", "selectWorld.edit": "Ṣatunkọ", "selectWorld.edit.backup": "Ṣe afẹyinti", "selectWorld.edit.backupCreated": "Ni atileyin: %s", "selectWorld.edit.backupFailed": "<PERSON>yipad<PERSON> afẹyinti", "selectWorld.edit.backupFolder": "Ṣii folda Backups", "selectWorld.edit.backupSize": "iwọn: %s MB", "selectWorld.edit.export_worldgen_settings": "<PERSON> okeere <PERSON>to Iran-Jade", "selectWorld.edit.export_worldgen_settings.failure": "<PERSON><PERSON><PERSON> ilu okeere kuna", "selectWorld.edit.export_worldgen_settings.success": "Ti ilẹ okeere", "selectWorld.edit.openFolder": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWorld.edit.optimize": "Je ki World", "selectWorld.edit.resetIcon": "<PERSON><PERSON><PERSON>", "selectWorld.edit.save": "Fipamọ", "selectWorld.edit.title": "Ṣatunkọ Aye", "selectWorld.enterName": "Orukọ agbaye", "selectWorld.enterSeed": "Irugbin fun agbaye monomono", "selectWorld.experimental": "Idanwo", "selectWorld.experimental.details": "<PERSON><PERSON><PERSON><PERSON> alaye", "selectWorld.experimental.details.entry": "Awọn ẹya idanwo ti o nilo: %s", "selectWorld.experimental.details.title": "Esiperimenta ẹya awọn ibeere", "selectWorld.experimental.message": "Ṣọra!\nIṣeto ni yii nilo awọn ẹya ti o tun wa labẹ idagbasoke. Aye rẹ le jamba, fọ, tabi ko ṣiṣẹ pẹlu awọn imudojuiwọn ọjọ iwaju.", "selectWorld.experimental.title": "Esiperimenta Awọn ẹya ara ẹrọ Ikilọ", "selectWorld.experiments": "<PERSON><PERSON><PERSON><PERSON> idan<PERSON>", "selectWorld.experiments.info": "Awọn idanwo jẹ awọn ẹya tuntun ti o pọju. Ṣọra bi awọn nkan ṣe le bajẹ. Awọn idanwo ko le wa ni pipa lẹhin ẹda agbaye.", "selectWorld.futureworld.error.text": "Nkankan kan ti ko tọ si nigba ti o n gbiyanju lati ṣafikun aye lati ẹya ikede iwaju. O jẹ ilana ti o lewu lati bẹrẹ pẹlu wa a gafara, o ko ṣiṣẹ.", "selectWorld.futureworld.error.title": "<PERSON><PERSON> sele!", "selectWorld.gameMode": "<PERSON><PERSON>", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "<PERSON><PERSON> bi <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> awọn bulọ<PERSON><PERSON> ko le ṣafikun tabi yọ<PERSON>ro.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON> bi <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> awọn bulọ<PERSON><PERSON> ko le", "selectWorld.gameMode.adventure.line2": "jẹ afikun tabi yọ kuro", "selectWorld.gameMode.creative": "Creative", "selectWorld.gameMode.creative.info": "Ṣẹda, k<PERSON>, ati ṣawari laisi awọn opin. O le fo, ni awọn ohun elo ailopin, ati pe ko le ṣe ipalara nipasẹ awọn ohun ibanilẹru.", "selectWorld.gameMode.creative.line1": "<PERSON><PERSON><PERSON> aw<PERSON>n ohun elo, fifa fifa ati", "selectWorld.gameMode.creative.line2": "pa awọn ohun amorindun lesekese", "selectWorld.gameMode.hardcore": "Ogbon", "selectWorld.gameMode.hardcore.info": "<PERSON><PERSON> i<PERSON>aye ni titiipa si iṣoro 'Lile'. O ko le respawn ti o ba ti o ba kú.", "selectWorld.gameMode.hardcore.line1": "<PERSON>nna bi <PERSON>, tiipa ni nira julọ", "selectWorld.gameMode.hardcore.line2": "i<PERSON><PERSON>, ati igbesi aye kan nikan", "selectWorld.gameMode.spectator": "Spectator", "selectWorld.gameMode.spectator.info": "O le wo ṣugbọn maṣe fi ọwọ kan.", "selectWorld.gameMode.spectator.line1": "O le wo ṣugbọn maṣe fi ọwọ kan", "selectWorld.gameMode.survival": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.info": "Ṣawari aye aramada nibiti o ti kọ, gba, iṣẹ ọwọ ati ja awọn ohun ibanilẹru titobi ju.", "selectWorld.gameMode.survival.line1": "Wa fun awọn or<PERSON>, iṣẹ ọwọ, ere", "selectWorld.gameMode.survival.line2": "ipele, ilera ati ebi", "selectWorld.gameRules": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.import_worldgen_settings": "<PERSON><PERSON><PERSON><PERSON> eto wọle", "selectWorld.import_worldgen_settings.failure": "Aṣiṣe agbewọle awọn eto wọle", "selectWorld.import_worldgen_settings.select_file": "<PERSON> faili eto (.json)", "selectWorld.incompatible.description": "This world cannot be opened in this version.\nIt was last played in version %s.", "selectWorld.incompatible.info": "Incompatible version: %s", "selectWorld.incompatible.title": "Incompatible version", "selectWorld.incompatible.tooltip": "This world cannot be opened because it was created by an incompatible version.", "selectWorld.incompatible_series": "Ti a ṣẹda nipasẹ ẹya ti ko ni ibamu", "selectWorld.load_folder_access": "E pamundur për të lexuar ose për të hyrë në dosje ku botët e lojës janë ruajtur!", "selectWorld.loading_list": "Monomono alagbato\n", "selectWorld.locked": "Ni titiipa nipasẹ apeere miiran ti Minecraft", "selectWorld.mapFeatures": "<PERSON>a aw<PERSON>n ilana", "selectWorld.mapFeatures.info": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>, <PERSON><PERSON><PERSON><PERSON> oju-o<PERSON> kekere, ati bẹbẹ lọ.", "selectWorld.mapType": "<PERSON><PERSON>", "selectWorld.mapType.normal": "<PERSON><PERSON>", "selectWorld.moreWorldOptions": "<PERSON><PERSON><PERSON><PERSON> diẹ sii...", "selectWorld.newWorld": "Aye tuntun", "selectWorld.recreate": "Tun-Ṣẹda", "selectWorld.recreate.customized.text": "Ṣiṣe atunṣe aye ko ni atilẹyin ni ikede ti Minecraft. A le gbiyanju lati tun ṣe pẹlu iru ọkà ati ohun-ini kanna, ṣugb<PERSON>n eyikeyi isọdi ile-iṣẹ ni yoo sọnu. A tọrọ gafara fun nkan inira naa!", "selectWorld.recreate.customized.title": "Awọn aye adani ko ni atilẹyin mọ", "selectWorld.recreate.error.text": "Nkankan kan ti ko tọ si nigba ti o n gbiyanju lati ṣa aye pada.", "selectWorld.recreate.error.title": "<PERSON><PERSON> sele!", "selectWorld.resource_load": "Preparing Resources...", "selectWorld.resultFolder": "Yoo wa ni fipamọ ni:", "selectWorld.search": "ri asise kan", "selectWorld.seedInfo": "<PERSON><PERSON><PERSON><PERSON> bosh për një farë të rastit", "selectWorld.select": "<PERSON><PERSON> yan", "selectWorld.targetFolder": "Fipamọ: %s", "selectWorld.title": "Yan <PERSON>", "selectWorld.tooltip.fromNewerVersion1": "Bota u ruajt në një version më të ri,", "selectWorld.tooltip.fromNewerVersion2": "ngarkimi i kësaj bote mund të shkaktojë probleme!", "selectWorld.tooltip.snapshot1": "Maṣe gbagbe lati ṣe afẹyinti aye yii", "selectWorld.tooltip.snapshot2": "para se ta ngarkoni në këtë fotografi.", "selectWorld.unable_to_load": "Nuk mund të ngarkoni botët", "selectWorld.version": "Version:", "selectWorld.versionJoinButton": "Ṣiṣe agbara ni gbogbo ọna", "selectWorld.versionQuestion": "Njẹ o fẹ lati ṣaju aye yii?", "selectWorld.versionUnknown": "aimọ", "selectWorld.versionWarning": "A ṣe igbesi aye yii kẹhin ni ikede %s ati ikojọpọ rẹ ni ikede yii le fa ibajẹ!", "selectWorld.warning.deprecated.question": "Diẹ ninu awọn ẹya ti a lo ti wa ni idaduro ati pe yoo da iṣẹ duro ni ọjọ iwaju. Ṣe o fẹ lati tẹsiwaju?", "selectWorld.warning.deprecated.title": "Ikilọ! Awọn eto wọnyi nlo awọn ẹya ti a ti parẹ", "selectWorld.warning.experimental.question": "Awọn eto wọnyi jẹ idanwo ati pe o le da iṣẹ duro ni ọjọ kan. Ṣe o fẹ lati tẹsiwaju?", "selectWorld.warning.experimental.title": "Ikilọ! Awọn eto wọnyi nlo awọn ẹya idanwo", "selectWorld.warning.lowDiskSpace.description": "There is not much space left on your device.\nRunning out of disk space while in game can lead to your world being damaged.", "selectWorld.warning.lowDiskSpace.title": "Warning! Low disk space!", "selectWorld.world": "Aye", "sign.edit": "Satunkọ akoonu ti awo naa", "sleep.not_possible": "Ko si iye isinmi ti o le kọja ni alẹ yii", "sleep.players_sleeping": "%s %s awọn ẹrọ orin sisun", "sleep.skipping_night": "<PERSON>sun nipasẹ alẹ yii", "slot.only_single_allowed": "Only single slots allowed, got '%s'", "slot.unknown": "Unknown Iho '%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Ibaramu/Ayika", "soundCategory.block": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "soundCategory.hostile": "Aw<PERSON>n ẹda ailewu", "soundCategory.master": "Titunto si <PERSON>wọn didun", "soundCategory.music": "Orin", "soundCategory.neutral": "Awọn ore eda", "soundCategory.player": "Awọn ẹrọ orin", "soundCategory.record": "Jukebox/bulọọki Akọsilẹ", "soundCategory.ui": "UI", "soundCategory.voice": "Ohùn/Ọrọ", "soundCategory.weather": "<PERSON><PERSON>", "spectatorMenu.close": "Pa akojọ aṣayan naa", "spectatorMenu.next_page": "<PERSON><PERSON>-iwe ti o tẹle", "spectatorMenu.previous_page": "<PERSON><PERSON>-iwe ti tẹlẹ", "spectatorMenu.root.prompt": "Tẹ bọtini lati yan aṣẹ, ati lẹẹkansi lati lo.", "spectatorMenu.team_teleport": "Teleport si Team ẹgbẹ", "spectatorMenu.team_teleport.prompt": "Yan egbe lati teleport si", "spectatorMenu.teleport": "Teleport si ẹrọ orin", "spectatorMenu.teleport.prompt": "Yan orin lati teleport si", "stat.generalButton": "Gbogbogbo", "stat.itemsButton": "Items", "stat.minecraft.animals_bred": "<PERSON><PERSON>", "stat.minecraft.aviate_one_cm": "<PERSON><PERSON><PERSON> nipasẹ Elytra", "stat.minecraft.bell_ring": "Agogo n Lu", "stat.minecraft.boat_one_cm": "<PERSON><PERSON><PERSON> Ọkọ <PERSON><PERSON>", "stat.minecraft.clean_armor": "Ihamọra <PERSON>", "stat.minecraft.clean_banner": "Awọn Asia ti Mọtoto", "stat.minecraft.clean_shulker_box": "<PERSON><PERSON><PERSON><PERSON> apoti <PERSON> ti <PERSON>", "stat.minecraft.climb_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON><PERSON><PERSON> ti o tẹ", "stat.minecraft.damage_absorbed": "Bibajẹ Ti Gba", "stat.minecraft.damage_blocked_by_shield": "Bibajẹ bulọki nipasẹ Shield", "stat.minecraft.damage_dealt": "Ibajẹ ṣe", "stat.minecraft.damage_dealt_absorbed": "Ibajẹ ṣe (Gba ara)", "stat.minecraft.damage_dealt_resisted": "Ibajẹ Ibajẹ", "stat.minecraft.damage_resisted": "Bibajẹ Ti koju", "stat.minecraft.damage_taken": "Bibajẹ Ti Ya", "stat.minecraft.deaths": "Nọmba ti Awọn iku", "stat.minecraft.drop": "<PERSON><PERSON><PERSON><PERSON> ohun silẹ", "stat.minecraft.eat_cake_slice": "Oyinbo ege Je", "stat.minecraft.enchant_item": "Enchanted awọn ohun kan", "stat.minecraft.fall_one_cm": "<PERSON><PERSON><PERSON> kuna", "stat.minecraft.fill_cauldron": "Cauldrons Kún", "stat.minecraft.fish_caught": "<PERSON>ja mu", "stat.minecraft.fly_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "<PERSON><PERSON><PERSON> nipasẹ Ẹṣin", "stat.minecraft.inspect_dispenser": "Dispensers Wa\n", "stat.minecraft.inspect_dropper": "Droppers Wiwa", "stat.minecraft.inspect_hopper": "Hoppers Wiwa", "stat.minecraft.interact_with_anvil": "<PERSON><PERSON><PERSON><PERSON> pẹlu Anvil", "stat.minecraft.interact_with_beacon": "Awọn ibaraẹnisọrọ pẹlu Beakoni", "stat.minecraft.interact_with_blast_furnace": "Awọn ibaraẹnisọrọ pẹlu Burn Furnace", "stat.minecraft.interact_with_brewingstand": "Awọn ibaraẹnisọrọ pẹlu <PERSON><PERSON><PERSON>", "stat.minecraft.interact_with_campfire": "Awọn ibaraẹnisọrọ pẹlu Firefire", "stat.minecraft.interact_with_cartography_table": "Awọn ibaraẹnisọrọ pẹlu Table Cartography", "stat.minecraft.interact_with_crafting_table": "<PERSON><PERSON><PERSON><PERSON> pẹlu tabili Ṣiṣẹda", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON><PERSON><PERSON> pẹlu Ileru", "stat.minecraft.interact_with_grindstone": "Awọn i<PERSON> pẹlu Grindstone", "stat.minecraft.interact_with_lectern": "Awọn ibaraẹnisọrọ pẹlu Lectern", "stat.minecraft.interact_with_loom": "Awọn ibaraẹnisọrọ pẹlu Loom", "stat.minecraft.interact_with_smithing_table": "Ibaraṣepọ pẹlu tabili alagbẹdẹ", "stat.minecraft.interact_with_smoker": "<PERSON><PERSON><PERSON><PERSON> pẹlu Ẹmu", "stat.minecraft.interact_with_stonecutter": "Awọn ibaraẹnisọrọ pẹlu Stonecutter", "stat.minecraft.jump": "Awọn fo", "stat.minecraft.leave_game": "Awọn ere Awọn olodun-", "stat.minecraft.minecart_one_cm": "<PERSON>jinna nipasẹ Minecart", "stat.minecraft.mob_kills": "<PERSON><PERSON><PERSON><PERSON> agbajo eniyan pa", "stat.minecraft.open_barrel": "Barrels Ṣi", "stat.minecraft.open_chest": "Awọn apoti ti ṣii", "stat.minecraft.open_enderchest": "<PERSON><PERSON><PERSON><PERSON> apoti <PERSON> Ṣi", "stat.minecraft.open_shulker_box": "<PERSON><PERSON><PERSON><PERSON> Ṣi", "stat.minecraft.pig_one_cm": "<PERSON><PERSON><PERSON> nipasẹ Ẹlẹdẹ", "stat.minecraft.play_noteblock": "A<PERSON><PERSON><PERSON> bulọọki Akọsilẹ dun", "stat.minecraft.play_record": "Orin Cd nṣiṣẹ orin", "stat.minecraft.play_time": "<PERSON><PERSON><PERSON>", "stat.minecraft.player_kills": "Player pa", "stat.minecraft.pot_flower": "Eweko Potted", "stat.minecraft.raid_trigger": "Awọn Raids Nfa", "stat.minecraft.raid_win": "Awọn igbogun ti Gba", "stat.minecraft.sleep_in_bed": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>n ninu <PERSON> kan", "stat.minecraft.sneak_time": "Agogo Sneak", "stat.minecraft.sprint_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.strider_one_cm": "Ṣẹgun nipa gigun lori ijoko kan", "stat.minecraft.swim_one_cm": "<PERSON><PERSON>", "stat.minecraft.talked_to_villager": "Ti ba Awọn ara abule sọrọ", "stat.minecraft.target_hit": "<PERSON>w<PERSON>n ibi-afẹde fojusi", "stat.minecraft.time_since_death": "<PERSON><PERSON>ko lati iku ti o kẹhin", "stat.minecraft.time_since_rest": "Akoko g<PERSON>", "stat.minecraft.total_world_time": "Akoko pẹlu World Open", "stat.minecraft.traded_with_villager": "Ti ta pẹlu Awọn abule", "stat.minecraft.trigger_trapped_chest": "Awọn àyà tí a há mọ́", "stat.minecraft.tune_noteblock": "Awọn bulọọki Akiyesi a<PERSON>wy", "stat.minecraft.use_cauldron": "<PERSON><PERSON>", "stat.minecraft.walk_on_water_one_cm": "<PERSON><PERSON><PERSON> l<PERSON> si ẹsẹ lori omi", "stat.minecraft.walk_one_cm": "<PERSON><PERSON><PERSON>", "stat.minecraft.walk_under_water_one_cm": "<PERSON><PERSON><PERSON> rin irin ajo labẹ omi", "stat.mobsButton": "<PERSON><PERSON>", "stat_type.minecraft.broken": "A<PERSON>ko ti o bajẹ", "stat_type.minecraft.crafted": "Igba Tiase", "stat_type.minecraft.dropped": "Silẹ", "stat_type.minecraft.killed": "O pa %s %s", "stat_type.minecraft.killed.none": "Iwọ ko ti pa %s", "stat_type.minecraft.killed_by": "%s pa ọ %s aago (igba)", "stat_type.minecraft.killed_by.none": "Iwọ ko tii pa nipasẹ %s", "stat_type.minecraft.mined": "Igba Iwakusa", "stat_type.minecraft.picked_up": "Ti gbe", "stat_type.minecraft.used": "Igba ti a lo", "stats.none": "-", "structure_block.button.detect_size": "ṢETỌ", "structure_block.button.load": "FIFUYE", "structure_block.button.save": "FIPAMỌ", "structure_block.custom_data": "Aṣa data Tag Orukọ", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "Igun: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Fifuye: %s", "structure_block.hover.save": "Fipamọ: %s", "structure_block.include_entities": "Include Entities:", "structure_block.integrity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Ẹya ati irugbin", "structure_block.integrity.integrity": "Iyege eto", "structure_block.integrity.seed": "<PERSON><PERSON><PERSON>", "structure_block.invalid_structure_name": "Orukọ ti ko ni idiyele: '%s'", "structure_block.load_not_found": "Igbekale '%s' ko si", "structure_block.load_prepare": "Ipilẹ ipo '%s' ti pese", "structure_block.load_success": "Be ti kojọpọ lati '%s'", "structure_block.mode.corner": "<PERSON><PERSON>", "structure_block.mode.data": "Data", "structure_block.mode.load": "<PERSON><PERSON><PERSON>", "structure_block.mode.save": "Fipamọ", "structure_block.mode_info.corner": "<PERSON><PERSON> - as<PERSON> fun ibi ati iwọn", "structure_block.mode_info.data": "Ipo data - <PERSON><PERSON> Logic Game", "structure_block.mode_info.load": "<PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON>p<PERSON> lati faili", "structure_block.mode_info.save": "Fipamọ ipo - kọ si faili", "structure_block.position": "<PERSON><PERSON>", "structure_block.position.x": "oju<PERSON>o I<PERSON> x", "structure_block.position.y": "ipo <PERSON> y", "structure_block.position.z": "ojulumo ipo z", "structure_block.save_failure": "Lagbara lati fi eto pamọ '%s'", "structure_block.save_success": "Eto ti fipamọ bi '%s'", "structure_block.show_air": "Ṣafihan awọn bulọ<PERSON><PERSON> al<PERSON>han:", "structure_block.show_boundingbox": "Fi iṣafihan si ikeji:", "structure_block.size": "Iwọn be", "structure_block.size.x": "iwọn be x", "structure_block.size.y": "iwọn be y", "structure_block.size.z": "iwọn be z", "structure_block.size_failure": "Iwọn ti ọna ko ṣee wa-ri. Fi igun naa kun pẹlu orukọ isọmọ tuntun", "structure_block.size_success": "A rii iwọn ni aṣeyọri fun '%s'", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "Orukọ Eto", "subtitles.ambient.cave": "<PERSON><PERSON> a<PERSON>", "subtitles.ambient.sound": "Eerie noise", "subtitles.block.amethyst_block.chime": "Amethyst chimes", "subtitles.block.amethyst_block.resonate": "Amethyst <PERSON>oki", "subtitles.block.anvil.destroy": "Anvil ti parun", "subtitles.block.anvil.land": "Anvil gbe ilẹ", "subtitles.block.anvil.use": "Anvil ti lo", "subtitles.block.barrel.close": "<PERSON><PERSON>", "subtitles.block.barrel.open": "<PERSON><PERSON>", "subtitles.block.beacon.activate": "Ile ina ti n ṣiṣẹ", "subtitles.block.beacon.ambient": "Маяк гудит", "subtitles.block.beacon.deactivate": "Ile ina naa wa ni pipa", "subtitles.block.beacon.power_select": "Bekoni agbara ti fi sori ẹrọ", "subtitles.block.beehive.drip": "Oyin yo", "subtitles.block.beehive.enter": "Bee ti nwọ Ile Agbon", "subtitles.block.beehive.exit": "Bee fi oju Agbon", "subtitles.block.beehive.shear": "<PERSON><PERSON> i<PERSON> ori", "subtitles.block.beehive.work": "Oyin ṣiṣẹ", "subtitles.block.bell.resonate": "Bell tun pada", "subtitles.block.bell.use": "<PERSON>wọ<PERSON> ago<PERSON>", "subtitles.block.big_dripleaf.tilt_down": "Dripleaf tẹ mọlẹ", "subtitles.block.big_dripleaf.tilt_up": "Dripleaf tẹ", "subtitles.block.blastfurnace.fire_crackle": "Ina adiro ina", "subtitles.block.brewing_stand.brew": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bubble_column.bubble_pop": "<PERSON><PERSON>an ti adehun", "subtitles.block.bubble_column.upwards_ambient": "<PERSON><PERSON>an ti n <PERSON>àn", "subtitles.block.bubble_column.upwards_inside": "Iṣan ti n fo", "subtitles.block.bubble_column.whirlpool_ambient": "<PERSON><PERSON>an ti ntan", "subtitles.block.bubble_column.whirlpool_inside": "I<PERSON>an ti idibajẹ kuna", "subtitles.block.button.click": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> tite", "subtitles.block.cake.add_candle": "<PERSON><PERSON><PERSON><PERSON> akara oyinbo oyinbo", "subtitles.block.campfire.crackle": "Campfire crackles", "subtitles.block.candle.crackle": "Candles crackles", "subtitles.block.candle.extinguish": "Candle extinguishes", "subtitles.block.chest.close": "Chest closes", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON>a <PERSON>", "subtitles.block.chest.open": "Àyà ṣi", "subtitles.block.chorus_flower.death": "Egbe Ododo rọ", "subtitles.block.chorus_flower.grow": "Ododo Chorus gbooro", "subtitles.block.comparator.click": "<PERSON><PERSON>wer<PERSON> tẹ", "subtitles.block.composter.empty": "Apoti apoti ti ṣofo", "subtitles.block.composter.fill": "Compost apoti kun", "subtitles.block.composter.ready": "Compost apoti compost", "subtitles.block.conduit.activate": "<PERSON><PERSON><PERSON> omi tan-an", "subtitles.block.conduit.ambient": "<PERSON><PERSON><PERSON> okun pulsates", "subtitles.block.conduit.attack.target": "<PERSON><PERSON><PERSON><PERSON> orisun orisun okun", "subtitles.block.conduit.deactivate": "<PERSON><PERSON><PERSON> omi okun wa ni pipa", "subtitles.block.copper_bulb.turn_off": "Copper Bulb turns off", "subtitles.block.copper_bulb.turn_on": "Copper Bulb turns on", "subtitles.block.copper_trapdoor.close": "Trapdoor closes", "subtitles.block.copper_trapdoor.open": "Trapdoor opens", "subtitles.block.crafter.craft": "Crafter crafts", "subtitles.block.crafter.fail": "Crafter fails crafting", "subtitles.block.creaking_heart.hurt": "Creaking Heart grumbles", "subtitles.block.creaking_heart.idle": "Eerie noise", "subtitles.block.creaking_heart.spawn": "Creaking Heart awakens", "subtitles.block.deadbush.idle": "Dry sounds", "subtitles.block.decorated_pot.insert": "Decorated Pot fills", "subtitles.block.decorated_pot.insert_fail": "Decorated Pot wobbles", "subtitles.block.decorated_pot.shatter": "<PERSON><PERSON><PERSON> shatters", "subtitles.block.dispenser.dispense": "<PERSON>un elo ti a pin", "subtitles.block.dispenser.fail": "Dispenser kuna", "subtitles.block.door.toggle": "Ilẹkun creaks", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "Tabili Arcane ti lo", "subtitles.block.end_portal.spawn": "Portal si Ipari wa ni sisi", "subtitles.block.end_portal_frame.fill": "Oju ti Ender ti fi sii", "subtitles.block.eyeblossom.close": "Eyeblossom closes", "subtitles.block.eyeblossom.idle": "Eyeblossom whispers", "subtitles.block.eyeblossom.open": "Eyeblossom opens", "subtitles.block.fence_gate.toggle": "Crafts Gate creaks", "subtitles.block.fire.ambient": "Ina crackles", "subtitles.block.fire.extinguish": "<PERSON><PERSON> kú", "subtitles.block.firefly_bush.idle": "Fireflies buzz", "subtitles.block.frogspawn.hatch": "Tad<PERSON> Hatches", "subtitles.block.furnace.fire_crackle": "Furnace crackles", "subtitles.block.generic.break": "Dina ti bajẹ", "subtitles.block.generic.fall": "Something falls on a block", "subtitles.block.generic.footsteps": "Awọn ipasẹ", "subtitles.block.generic.hit": "Dẹkun fifọ", "subtitles.block.generic.place": "Dina ti gbe", "subtitles.block.grindstone.use": "Atunkọ. block. grindstone. use", "subtitles.block.growing_plant.crop": "<PERSON><PERSON><PERSON>", "subtitles.block.hanging_sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.honey_block.slide": "<PERSON><PERSON> isalẹ ohun amorindun ti oyin", "subtitles.block.iron_trapdoor.close": "Awọn atunkọ. block. iron_trapdoor. close", "subtitles.block.iron_trapdoor.open": "awọn atunkọ.block.iron_trapdoor.open", "subtitles.block.lava.ambient": "Awọn atunkọ. block. lava. ambient", "subtitles.block.lava.extinguish": "atunkọ.block.lava.extinguish", "subtitles.block.lever.click": "A tẹ efa", "subtitles.block.note_block.note": "Àkọsílẹ akọsilẹ ti ndun", "subtitles.block.pale_hanging_moss.idle": "Eerie noise", "subtitles.block.piston.move": "Pisitini n gbe", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> n <PERSON>an", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava rọ sinu Cauldron", "subtitles.block.pointed_dripstone.drip_water": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON>mi rọ sinu Cauldron", "subtitles.block.pointed_dripstone.land": "Stalactite kọlu", "subtitles.block.portal.ambient": "awọn atunkọ.block.portal.ambient", "subtitles.block.portal.travel": "Awọn ariwo Portal ti n lọ", "subtitles.block.portal.trigger": "Awọn ariwo Portal fẹẹrẹ", "subtitles.block.pressure_plate.click": "awọn atunkọ.block.pressure_plate.click", "subtitles.block.pumpkin.carve": "Scissors ge", "subtitles.block.redstone_torch.burnout": "awọn atunkọ.block.redstone_torch.burnout\n", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON> whooshes", "subtitles.block.respawn_anchor.charge": "<PERSON><PERSON><PERSON> atij<PERSON> oran ti a tunṣe pada", "subtitles.block.respawn_anchor.deplete": "<PERSON><PERSON><PERSON><PERSON> oran depletes", "subtitles.block.respawn_anchor.set_spawn": "Irin-iṣẹ iṣatunṣe atunṣe", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "Sculk nyoju", "subtitles.block.sculk.spread": "Sculk ti nran", "subtitles.block.sculk_catalyst.bloom": "Sculk ayase blooms", "subtitles.block.sculk_sensor.clicking": "Sensọ Sculk bẹrẹ titẹ", "subtitles.block.sculk_sensor.clicking_stop": "<PERSON><PERSON><PERSON> duro tite", "subtitles.block.sculk_shrieker.shriek": "Sculk Shriek<PERSON> kigbe", "subtitles.block.shulker_box.close": "awọn atunkọ.block.shulker_box.close\n", "subtitles.block.shulker_box.open": "awọn atunkọ.block.shulker_box.open", "subtitles.block.sign.waxed_interact_fail": "Wobbles wole", "subtitles.block.smithing_table.use": "Tabili ti alawodudu ti lo", "subtitles.block.smoker.smoke": "awọn atunkọ.block.smoker.smoke", "subtitles.block.sniffer_egg.crack": "Sniffer Ẹyin dojuijako", "subtitles.block.sniffer_egg.hatch": "Sniffer Ẹyin hatches", "subtitles.block.sniffer_egg.plop": "Sniffer plops", "subtitles.block.sponge.absorb": "Sponge sucks", "subtitles.block.sweet_berry_bush.pick_berries": "Berries agbejade", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "awọn atunkọ.block.trapdoor.toggle", "subtitles.block.trial_spawner.about_to_spawn_item": "Ominous item prepares", "subtitles.block.trial_spawner.ambient": "Trial Spawner crackles", "subtitles.block.trial_spawner.ambient_charged": "Ominous crackling", "subtitles.block.trial_spawner.ambient_ominous": "Ominous crackling", "subtitles.block.trial_spawner.charge_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.close_shutter": "Trial Spawner closes", "subtitles.block.trial_spawner.detect_player": "Trial Spawner charges up", "subtitles.block.trial_spawner.eject_item": "Trial Spawner ejects items", "subtitles.block.trial_spawner.ominous_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.open_shutter": "Trial Spawner opens", "subtitles.block.trial_spawner.spawn_item": "Ominous item drops", "subtitles.block.trial_spawner.spawn_item_begin": "Ominous item appears", "subtitles.block.trial_spawner.spawn_mob": "Trial Spawner spawns a mob", "subtitles.block.tripwire.attach": "awọn atunkọ.block.tripwire.attach", "subtitles.block.tripwire.click": "<PERSON><PERSON><PERSON><PERSON> jinna <PERSON>", "subtitles.block.tripwire.detach": "Tripwire yapa", "subtitles.block.vault.activate": "Vault ignites", "subtitles.block.vault.ambient": "Vault crackles", "subtitles.block.vault.close_shutter": "Vault closes", "subtitles.block.vault.deactivate": "<PERSON>ault extinguishes", "subtitles.block.vault.eject_item": "Vault ejects item", "subtitles.block.vault.insert_item": "<PERSON><PERSON> unlocks", "subtitles.block.vault.insert_item_fail": "Vault rejects item", "subtitles.block.vault.open_shutter": "Vault opens", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> rejects player", "subtitles.block.water.ambient": "<PERSON><PERSON>", "subtitles.block.wet_sponge.dries": "Sponge dries", "subtitles.chiseled_bookshelf.insert": "Iwe ti a gbe", "subtitles.chiseled_bookshelf.insert_enchanted": "Enchanted Book placed", "subtitles.chiseled_bookshelf.take": "Iwe ti o gba", "subtitles.chiseled_bookshelf.take_enchanted": "Enchanted Book taken", "subtitles.enchant.thorns.hit": "Ẹgún prick", "subtitles.entity.allay.ambient_with_item": "<PERSON>ay wá", "subtitles.entity.allay.ambient_without_item": "Allay nfe", "subtitles.entity.allay.death": "Allay k<PERSON>", "subtitles.entity.allay.hurt": "Allay dun", "subtitles.entity.allay.item_given": "<PERSON><PERSON> chortles", "subtitles.entity.allay.item_taken": "Always", "subtitles.entity.allay.item_thrown": "<PERSON>ay s<PERSON>", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> grunts", "subtitles.entity.armadillo.brush": "<PERSON>ute is brushed off", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.armadillo.hurt_reduced": "Armadillo shields itself", "subtitles.entity.armadillo.land": "Armadillo lands", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armadillo.roll": "Armadillo rolls up", "subtitles.entity.armadillo.scute_drop": "Armadillo sheds scute", "subtitles.entity.armadillo.unroll_finish": "Armadillo unrolls", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armor_stand.fall": "<PERSON><PERSON><PERSON>", "subtitles.entity.arrow.hit": "Ọfà deba", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON><PERSON> lu", "subtitles.entity.arrow.shoot": "Ọfa ti a yọ", "subtitles.entity.axolotl.attack": "Awọn kolu Axolotl", "subtitles.entity.axolotl.death": "Axolotl ku", "subtitles.entity.axolotl.hurt": "Axolotl dun", "subtitles.entity.axolotl.idle_air": "Axolotl chirps", "subtitles.entity.axolotl.idle_water": "Axolotl chirps", "subtitles.entity.axolotl.splash": "<PERSON><PERSON><PERSON><PERSON> itanna <PERSON>", "subtitles.entity.axolotl.swim": "Axolotl we", "subtitles.entity.bat.ambient": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON> adan", "subtitles.entity.bat.death": "Bat ku", "subtitles.entity.bat.hurt": "Adan dun", "subtitles.entity.bat.takeoff": "Bat gba kuro", "subtitles.entity.bee.ambient": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bee.death": "<PERSON>yin ku", "subtitles.entity.bee.hurt": "Bee farapa", "subtitles.entity.bee.loop": "Bee buzzes", "subtitles.entity.bee.loop_aggressive": "Bee buzzes ni ibinu", "subtitles.entity.bee.pollinate": "Bee buzzes inudidun", "subtitles.entity.bee.sting": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.blaze.ambient": "Blaze nmi", "subtitles.entity.blaze.burn": "<PERSON> crackles", "subtitles.entity.blaze.death": "<PERSON> ku", "subtitles.entity.blaze.hurt": "<PERSON> dun", "subtitles.entity.blaze.shoot": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.boat.paddle_land": "Oars kana", "subtitles.entity.boat.paddle_water": "Gbigbe ọkọ", "subtitles.entity.bogged.ambient": "Bogged rattles", "subtitles.entity.bogged.death": "Bogged dies", "subtitles.entity.bogged.hurt": "Bogged hurts", "subtitles.entity.breeze.charge": "Breeze charges", "subtitles.entity.breeze.death": "<PERSON><PERSON> dies", "subtitles.entity.breeze.deflect": "<PERSON><PERSON> deflects", "subtitles.entity.breeze.hurt": "Breeze hurts", "subtitles.entity.breeze.idle_air": "Breeze flies", "subtitles.entity.breeze.idle_ground": "Breeze whirs", "subtitles.entity.breeze.inhale": "Breeze inhales", "subtitles.entity.breeze.jump": "<PERSON><PERSON> jumps", "subtitles.entity.breeze.land": "Breeze lands", "subtitles.entity.breeze.shoot": "Breeze shoots", "subtitles.entity.breeze.slide": "Breeze slides", "subtitles.entity.breeze.whirl": "Breeze whirls", "subtitles.entity.breeze.wind_burst": "Wind Charge bursts", "subtitles.entity.camel.ambient": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.entity.camel.dash": "Rakunmi yeets", "subtitles.entity.camel.dash_ready": "<PERSON><PERSON><PERSON><PERSON> pada", "subtitles.entity.camel.death": "<PERSON><PERSON><PERSON><PERSON> ku", "subtitles.entity.camel.eat": "Rakunmi jẹun", "subtitles.entity.camel.hurt": "<PERSON><PERSON><PERSON><PERSON> dun", "subtitles.entity.camel.saddle": "Gàárì, equips", "subtitles.entity.camel.sit": "<PERSON><PERSON><PERSON><PERSON> joko", "subtitles.entity.camel.stand": "<PERSON><PERSON><PERSON><PERSON> duro soke", "subtitles.entity.camel.step": "Awọn igbesẹ ti ibakasiẹ", "subtitles.entity.camel.step_sand": "<PERSON><PERSON><PERSON> ibakasiẹ", "subtitles.entity.cat.ambient": "<PERSON> meows", "subtitles.entity.cat.beg_for_food": "Cat begs fun ounje", "subtitles.entity.cat.death": "Ologbo ku", "subtitles.entity.cat.eat": "Cat njẹ", "subtitles.entity.cat.hiss": "Cat hisses", "subtitles.entity.cat.hurt": "Cat dun", "subtitles.entity.cat.purr": "Cat purrs", "subtitles.entity.chicken.ambient": "<PERSON><PERSON> clucks", "subtitles.entity.chicken.death": "<PERSON><PERSON> ku", "subtitles.entity.chicken.egg": "<PERSON><PERSON> plops", "subtitles.entity.chicken.hurt": "<PERSON><PERSON> dun", "subtitles.entity.cod.death": "Filafẹlẹ ti ku", "subtitles.entity.cod.flop": "Filafẹlẹ awọ", "subtitles.entity.cod.hurt": "Filafẹlẹ jẹ ipalara", "subtitles.entity.cow.ambient": "<PERSON><PERSON><PERSON> malu", "subtitles.entity.cow.death": "Maalu ku", "subtitles.entity.cow.hurt": "<PERSON><PERSON><PERSON> dun", "subtitles.entity.cow.milk": "Maalu ti ni wara", "subtitles.entity.creaking.activate": "Creaking watches", "subtitles.entity.creaking.ambient": "Creaking creaks", "subtitles.entity.creaking.attack": "Creaking attacks", "subtitles.entity.creaking.deactivate": "Creaking calms", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Creaking stops", "subtitles.entity.creaking.spawn": "Creaking manifests", "subtitles.entity.creaking.sway": "Creaking is hit", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Creaking moves", "subtitles.entity.creeper.death": "C<PERSON>per ku", "subtitles.entity.creeper.hurt": "C<PERSON>per dun", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> hisses", "subtitles.entity.dolphin.ambient": "Ẹja twitter", "subtitles.entity.dolphin.ambient_water": "Ẹja nla", "subtitles.entity.dolphin.attack": "Ẹja ku", "subtitles.entity.dolphin.death": "Ẹja na ku", "subtitles.entity.dolphin.eat": "Ẹja jẹ wọn", "subtitles.entity.dolphin.hurt": "Filafẹlẹ naa ni iyara", "subtitles.entity.dolphin.jump": "Filafẹlẹ na n fo", "subtitles.entity.dolphin.play": "Filafẹlẹ na n rẹrin", "subtitles.entity.dolphin.splash": "Filafẹlẹ <PERSON><PERSON>", "subtitles.entity.dolphin.swim": "Filafẹlẹ Iru naa n wọ", "subtitles.entity.donkey.ambient": "Kẹtẹkẹtẹ hee-haws", "subtitles.entity.donkey.angry": "Kẹtẹkẹtẹ adugbo", "subtitles.entity.donkey.chest": "Kẹtẹkẹtẹ Chest equips", "subtitles.entity.donkey.death": "Ke<PERSON>kete ku", "subtitles.entity.donkey.eat": "Kẹtẹkẹtẹ njẹun", "subtitles.entity.donkey.hurt": "Kẹtẹkẹtẹ ṣe ipalara", "subtitles.entity.donkey.jump": "<PERSON><PERSON> jumps", "subtitles.entity.drowned.ambient": "Ti danu idinku", "subtitles.entity.drowned.ambient_water": "Ti danu idinku", "subtitles.entity.drowned.death": "Ti danu ku", "subtitles.entity.drowned.hurt": "Ti danu jìya", "subtitles.entity.drowned.shoot": "Ti danu ṣabọ ifọrọwọrọ kan", "subtitles.entity.drowned.step": "Ti danu ti o ba wa", "subtitles.entity.drowned.swim": "<PERSON>i danu fifó", "subtitles.entity.egg.throw": "Ẹyin fo", "subtitles.entity.elder_guardian.ambient": "Alagba Alagba moans", "subtitles.entity.elder_guardian.ambient_land": "Alagba Alagba moans", "subtitles.entity.elder_guardian.curse": "<PERSON><PERSON>un Alagba Alagba", "subtitles.entity.elder_guardian.death": "Alàgbà Alàgbà kú", "subtitles.entity.elder_guardian.flop": "Alagba Guardian flops", "subtitles.entity.elder_guardian.hurt": "Alagba Alagba dun", "subtitles.entity.ender_dragon.ambient": "<PERSON><PERSON> kigbe", "subtitles.entity.ender_dragon.death": "<PERSON>i ku", "subtitles.entity.ender_dragon.flap": "<PERSON><PERSON> kigbe", "subtitles.entity.ender_dragon.growl": "<PERSON><PERSON> kigbe", "subtitles.entity.ender_dragon.hurt": "Dragon dun", "subtitles.entity.ender_dragon.shoot": "Dragon dun", "subtitles.entity.ender_eye.death": "<PERSON><PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON><PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> fo", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> vwoops", "subtitles.entity.enderman.death": "<PERSON><PERSON> ku", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> <PERSON>e i<PERSON>ara", "subtitles.entity.enderman.scream": "<PERSON><PERSON> kigbe", "subtitles.entity.enderman.stare": "<PERSON><PERSON> kigbe", "subtitles.entity.enderman.teleport": "<PERSON><PERSON><PERSON><PERSON> tẹlifoonu <PERSON>erman", "subtitles.entity.endermite.ambient": "Awọn i<PERSON>", "subtitles.entity.endermite.death": "<PERSON><PERSON> ku", "subtitles.entity.endermite.hurt": "<PERSON><PERSON> <PERSON>e i<PERSON>ara", "subtitles.entity.evoker.ambient": "Evoker nk<PERSON>n", "subtitles.entity.evoker.cast_spell": "Evoker n ṣa<PERSON>a l<PERSON>", "subtitles.entity.evoker.celebrate": "Evoker <PERSON><PERSON> idunnu", "subtitles.entity.evoker.death": "Evoker ku", "subtitles.entity.evoker.hurt": "Evoker <PERSON><PERSON>", "subtitles.entity.evoker.prepare_attack": "Evoker <PERSON><PERSON><PERSON>", "subtitles.entity.evoker.prepare_summon": "Evoker ṣetan pipe si", "subtitles.entity.evoker.prepare_wololo": "Evoker <PERSON><PERSON><PERSON> pele", "subtitles.entity.evoker_fangs.attack": "<PERSON><PERSON> im<PERSON>a", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON> ti gba", "subtitles.entity.firework_rocket.blast": "Aw<PERSON>n ina ina", "subtitles.entity.firework_rocket.launch": "Awọn ifilọlẹ iṣẹ ina", "subtitles.entity.firework_rocket.twinkle": "<PERSON>e ina seju", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "A leefofo loju omi", "subtitles.entity.fishing_bobber.splash": "Iyẹfun fifun ni", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON> s<PERSON>", "subtitles.entity.fox.aggro": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.fox.ambient": "Akata fo", "subtitles.entity.fox.bite": "Fox geje", "subtitles.entity.fox.death": "Fox geje", "subtitles.entity.fox.eat": "Akata njẹ", "subtitles.entity.fox.hurt": "Akata dun", "subtitles.entity.fox.screech": "<PERSON> screeches", "subtitles.entity.fox.sleep": "Akata fo", "subtitles.entity.fox.sniff": "Akata fo", "subtitles.entity.fox.spit": "Fox tutọ", "subtitles.entity.fox.teleport": "Fox teleports", "subtitles.entity.frog.ambient": "Ọpọlọ croaks", "subtitles.entity.frog.death": "Ọpọlọ ku", "subtitles.entity.frog.eat": "Ọpọlọ jẹun", "subtitles.entity.frog.hurt": "Ọpọlọ dun", "subtitles.entity.frog.lay_spawn": "Ọpọlọ lays spawn", "subtitles.entity.frog.long_jump": "Ọpọlọ fo", "subtitles.entity.generic.big_fall": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.burn": "<PERSON><PERSON>", "subtitles.entity.generic.death": "ku", "subtitles.entity.generic.drink": "Sipping\n", "subtitles.entity.generic.eat": "njẹ", "subtitles.entity.generic.explode": "Bug<PERSON><PERSON>", "subtitles.entity.generic.extinguish_fire": "<PERSON><PERSON><PERSON><PERSON> ina", "subtitles.entity.generic.hurt": "Nkankan dun", "subtitles.entity.generic.small_fall": "<PERSON><PERSON> kan kọsẹ", "subtitles.entity.generic.splash": "Ṣiṣan", "subtitles.entity.generic.swim": "<PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Wind Charge bursts", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> kigbe", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> ku", "subtitles.entity.ghast.hurt": "G<PERSON>t dun", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON><PERSON> kan <PERSON>emu kun", "subtitles.entity.glow_item_frame.break": "<PERSON><PERSON> baje", "subtitles.entity.glow_item_frame.place": "Alábá <PERSON> kan <PERSON>emu gbe", "subtitles.entity.glow_item_frame.remove_item": "<PERSON><PERSON><PERSON> kan <PERSON>a", "subtitles.entity.glow_item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON> ohun elo Ina Alay<PERSON> n tẹ", "subtitles.entity.glow_squid.ambient": "Alábá Squid we", "subtitles.entity.glow_squid.death": "Alábá Squid kú", "subtitles.entity.glow_squid.hurt": "Alábá Squid n dun", "subtitles.entity.glow_squid.squirt": "Alábá Squid abereyo inki", "subtitles.entity.goat.ambient": "<PERSON><PERSON><PERSON> ewurẹ", "subtitles.entity.goat.death": "Ewúrẹ ku", "subtitles.entity.goat.eat": "<PERSON><PERSON><PERSON> je", "subtitles.entity.goat.horn_break": "Ewúrẹ Horn fi opin si pa", "subtitles.entity.goat.hurt": "<PERSON><PERSON><PERSON> dun", "subtitles.entity.goat.long_jump": "Ewúrẹ fò", "subtitles.entity.goat.milk": "<PERSON><PERSON>re ti wa ni miliki", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> e<PERSON>re", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON> ewurẹ", "subtitles.entity.goat.step": "Awọn igbesẹ ewurẹ", "subtitles.entity.guardian.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> moans", "subtitles.entity.guardian.ambient_land": "Flaps <PERSON>", "subtitles.entity.guardian.attack": "Olutọju a<PERSON>eyo", "subtitles.entity.guardian.death": "Olutọju ku", "subtitles.entity.guardian.flop": "Olutọju flops", "subtitles.entity.guardian.hurt": "Olutọju dun", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "<PERSON><PERSON><PERSON> kigbe", "subtitles.entity.hoglin.angry": "<PERSON><PERSON>n dagba ni ibinu", "subtitles.entity.hoglin.attack": "<PERSON><PERSON>n ku", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> yipada sinu zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON>n ku", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON>n ti farapa", "subtitles.entity.hoglin.retreat": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.step": "Awọn igbesẹ ti Hoglin", "subtitles.entity.horse.ambient": "Awọn ẹṣin ẹṣin", "subtitles.entity.horse.angry": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>bo ẹṣin", "subtitles.entity.horse.armor": "<PERSON><PERSON><PERSON><PERSON> ihamọra ihamọra ẹṣin", "subtitles.entity.horse.breathe": "Ẹṣin nmí", "subtitles.entity.horse.death": "Ẹṣin kú", "subtitles.entity.horse.eat": "Ẹṣin jẹ", "subtitles.entity.horse.gallop": "Ẹṣin gigun", "subtitles.entity.horse.hurt": "Ẹṣin farapa", "subtitles.entity.horse.jump": "Fo ẹṣin", "subtitles.entity.horse.saddle": "Gàárì, equips", "subtitles.entity.husk.ambient": "Husk kérora", "subtitles.entity.husk.converted_to_zombie": "Awọn cadaver yipada sinu Zombie kan", "subtitles.entity.husk.death": "Husk ku", "subtitles.entity.husk.hurt": "Husk farapa", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.cast_spell": "Illusioner simẹnti", "subtitles.entity.illusioner.death": "Alaroye ku", "subtitles.entity.illusioner.hurt": "Alaroye dun", "subtitles.entity.illusioner.mirror_move": "Alaroye <PERSON>", "subtitles.entity.illusioner.prepare_blindness": "Alaroye šetan ifọju", "subtitles.entity.illusioner.prepare_mirror": "Alaroye šetan digi aworan", "subtitles.entity.iron_golem.attack": "Awọn ikọlu Iron Golem", "subtitles.entity.iron_golem.damage": "Iron golem fi opin si", "subtitles.entity.iron_golem.death": "Iron Golem ku", "subtitles.entity.iron_golem.hurt": "Iron Golem ṣe ipalara", "subtitles.entity.iron_golem.repair": "Iron Golem tunṣe", "subtitles.entity.item.break": "<PERSON><PERSON><PERSON><PERSON> nkan fifọ", "subtitles.entity.item.pickup": "<PERSON><PERSON><PERSON><PERSON> nkan isere", "subtitles.entity.item_frame.add_item": "<PERSON><PERSON><PERSON> kun", "subtitles.entity.item_frame.break": "<PERSON>emu <PERSON> fi opin si", "subtitles.entity.item_frame.place": "<PERSON><PERSON>u <PERSON> ti a gbe", "subtitles.entity.item_frame.remove_item": "<PERSON><PERSON><PERSON> kan <PERSON>ofo", "subtitles.entity.item_frame.rotate_item": "<PERSON><PERSON><PERSON><PERSON><PERSON>u <PERSON> tẹ", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "Mànàmáná k<PERSON>lu", "subtitles.entity.lightning_bolt.thunder": "<PERSON><PERSON><PERSON> hó", "subtitles.entity.llama.ambient": "Llama n lu", "subtitles.entity.llama.angry": "<PERSON> jẹ kikorò pẹlu ibinu", "subtitles.entity.llama.chest": "<PERSON><PERSON>a Chest awọn ohun elo", "subtitles.entity.llama.death": "<PERSON><PERSON>a ku", "subtitles.entity.llama.eat": "Llama n lu", "subtitles.entity.llama.hurt": "<PERSON><PERSON>a dun", "subtitles.entity.llama.spit": "Llama spits", "subtitles.entity.llama.step": "Llama spits", "subtitles.entity.llama.swag": "Llama ti ṣe ọṣọ", "subtitles.entity.magma_cube.death": "Magma Cube ku", "subtitles.entity.magma_cube.hurt": "Magma Cube ku", "subtitles.entity.magma_cube.squish": "Magma kuubu squishes", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "<PERSON><PERSON>", "subtitles.entity.mooshroom.convert": "Mooshroom yipada", "subtitles.entity.mooshroom.eat": "Mooshroom jẹun", "subtitles.entity.mooshroom.milk": "Mooshroom n ni wara", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom n gba ifunwara ni ifura", "subtitles.entity.mule.ambient": "Mule hee-haws", "subtitles.entity.mule.angry": "Ileke mule", "subtitles.entity.mule.chest": "Mule àya equips", "subtitles.entity.mule.death": "<PERSON><PERSON> ku", "subtitles.entity.mule.eat": "<PERSON><PERSON> njẹun", "subtitles.entity.mule.hurt": "Ìbaaka máa ń dunni", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumps", "subtitles.entity.painting.break": "Awọn fifọ kikun", "subtitles.entity.painting.place": "<PERSON>kun ti a gbe", "subtitles.entity.panda.aggressive_ambient": "Panda huffs", "subtitles.entity.panda.ambient": "Panda sokoto", "subtitles.entity.panda.bite": "<PERSON>da geje", "subtitles.entity.panda.cant_breed": "Panda n pariwo", "subtitles.entity.panda.death": "Panda ku", "subtitles.entity.panda.eat": "Panda jẹun", "subtitles.entity.panda.hurt": "Panda dun", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON><PERSON><PERSON> imu imu <PERSON>", "subtitles.entity.panda.sneeze": "<PERSON>da sinmi", "subtitles.entity.panda.step": "Awọn igbesẹ Panda", "subtitles.entity.panda.worried_ambient": "Panda whimpers", "subtitles.entity.parrot.ambient": "Parrot wí pé", "subtitles.entity.parrot.death": "Parrot ku", "subtitles.entity.parrot.eats": "Parrot jẹun", "subtitles.entity.parrot.fly": "Awọn eso aloku ti parro", "subtitles.entity.parrot.hurts": "Parrot dun", "subtitles.entity.parrot.imitate.blaze": "Parrot nmí", "subtitles.entity.parrot.imitate.bogged": "Parrot rattles", "subtitles.entity.parrot.imitate.breeze": "Parrot whirs", "subtitles.entity.parrot.imitate.creaking": "Parrot creaks", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.parrot.imitate.drowned": "Ag<PERSON><PERSON>n ti wa ni idinku", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON> moans", "subtitles.entity.parrot.imitate.ender_dragon": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.parrot.imitate.endermite": "Parrot scuttles", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON><PERSON> sunkún", "subtitles.entity.parrot.imitate.guardian": "Parrot kerora", "subtitles.entity.parrot.imitate.hoglin": "<PERSON><PERSON><PERSON><PERSON> koriko ododo", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> ké<PERSON>ra", "subtitles.entity.parrot.imitate.illusioner": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.parrot.imitate.magma_cube": "Parrot squishes", "subtitles.entity.parrot.imitate.phantom": "Ag<PERSON><PERSON>n jẹ screeching", "subtitles.entity.parrot.imitate.piglin": "Awọn iṣapẹẹrẹ Parrot", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> snort", "subtitles.entity.parrot.imitate.pillager": "<PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.parrot.imitate.ravager": "Parrot grunts", "subtitles.entity.parrot.imitate.shulker": "Àkùkọ lururu", "subtitles.entity.parrot.imitate.silverfish": "Parrot hisses", "subtitles.entity.parrot.imitate.skeleton": "Àkùkọ lja", "subtitles.entity.parrot.imitate.slime": "Parrot squishes", "subtitles.entity.parrot.imitate.spider": "Parrot hisses", "subtitles.entity.parrot.imitate.stray": "Parrot rattles", "subtitles.entity.parrot.imitate.vex": "Àkùkọ awọn ipọnju", "subtitles.entity.parrot.imitate.vindicator": "Àkùkọ kikoro", "subtitles.entity.parrot.imitate.warden": "<PERSON><PERSON><PERSON> k<PERSON>n", "subtitles.entity.parrot.imitate.witch": "Àkùkọ rẹrin", "subtitles.entity.parrot.imitate.wither": "Àkùkọ ibinu", "subtitles.entity.parrot.imitate.wither_skeleton": "Parrot rattles", "subtitles.entity.parrot.imitate.zoglin": "<PERSON><PERSON><PERSON> kegbe", "subtitles.entity.parrot.imitate.zombie": "Parrot kerora", "subtitles.entity.parrot.imitate.zombie_villager": "Parrot kerora", "subtitles.entity.phantom.ambient": "<PERSON><PERSON><PERSON><PERSON> screeches", "subtitles.entity.phantom.bite": "Alakikanju ajẹku", "subtitles.entity.phantom.death": "Alakikanju k<PERSON>", "subtitles.entity.phantom.flap": "<PERSON><PERSON><PERSON><PERSON> naa n <PERSON>aja", "subtitles.entity.phantom.hurt": "<PERSON><PERSON><PERSON><PERSON> jiya", "subtitles.entity.phantom.swoop": "Alakikanju naa n gbe soke", "subtitles.entity.pig.ambient": "Ẹlẹdẹ oinks", "subtitles.entity.pig.death": "Ẹlẹdẹ ku", "subtitles.entity.pig.hurt": "Ẹlẹdẹ dun", "subtitles.entity.pig.saddle": "Gàárì, equips", "subtitles.entity.piglin.admiring_item": "<PERSON>lin fẹran nkan yii", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> snorts", "subtitles.entity.piglin.angry": "<PERSON><PERSON> snorts ni ibinu", "subtitles.entity.piglin.celebrate": "Piglin ṣe ayẹyẹ", "subtitles.entity.piglin.converted_to_zombified": "<PERSON>lin yipada sinu ẹlẹdẹ Zombie kan", "subtitles.entity.piglin.death": "<PERSON><PERSON> ti ku ni bayi", "subtitles.entity.piglin.hurt": "<PERSON><PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> snorts ilara", "subtitles.entity.piglin.retreat": "<PERSON><PERSON>", "subtitles.entity.piglin.step": "Awọn igbesẹ <PERSON>lin", "subtitles.entity.piglin_brute.ambient": "<PERSON><PERSON><PERSON> snorts", "subtitles.entity.piglin_brute.angry": "<PERSON><PERSON><PERSON> snorts ni ibinu", "subtitles.entity.piglin_brute.converted_to_zombified": "Brutal piglin <PERSON>a", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON><PERSON> ku", "subtitles.entity.piglin_brute.hurt": "<PERSON><PERSON><PERSON> ti farapa", "subtitles.entity.piglin_brute.step": "Awọn igbesẹ ti <PERSON><PERSON><PERSON>", "subtitles.entity.pillager.ambient": "<PERSON><PERSON><PERSON> k<PERSON>n", "subtitles.entity.pillager.celebrate": "<PERSON><PERSON><PERSON><PERSON> hails", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.pillager.hurt": "<PERSON><PERSON><PERSON> naa dunni", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON> lu", "subtitles.entity.player.attack.knockback": "<PERSON><PERSON><PERSON> fe", "subtitles.entity.player.attack.strong": "Ra", "subtitles.entity.player.attack.sweep": "<PERSON><PERSON>", "subtitles.entity.player.attack.weak": "<PERSON><PERSON><PERSON> fifun", "subtitles.entity.player.burp": "Belching", "subtitles.entity.player.death": "Ẹrọ orin naa ku", "subtitles.entity.player.freeze_hurt": "Di ẹrọ orin", "subtitles.entity.player.hurt": "Ẹrọ orin dun", "subtitles.entity.player.hurt_drown": "Ẹrọ orin n bomi", "subtitles.entity.player.hurt_on_fire": "Ẹrọ orin naa wa ni ina", "subtitles.entity.player.levelup": "Ẹrọ orin n dun", "subtitles.entity.player.teleport": "Player teleports", "subtitles.entity.polar_bear.ambient": "Pola agbateru kérora", "subtitles.entity.polar_bear.ambient_baby": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> mutters", "subtitles.entity.polar_bear.death": "Pola agbateru naa ku", "subtitles.entity.polar_bear.hurt": "O ṣe ipalara agbateru pola", "subtitles.entity.polar_bear.warning": "Pola agbateru igbe", "subtitles.entity.potion.splash": "Igo naa n fọ", "subtitles.entity.potion.throw": "Igo ti a ju", "subtitles.entity.puffer_fish.blow_out": "Oṣupa ti o nfa", "subtitles.entity.puffer_fish.blow_up": "Oṣupa fa soke", "subtitles.entity.puffer_fish.death": "Oṣupa jẹ ku", "subtitles.entity.puffer_fish.flop": "Oṣupa ni fifọ", "subtitles.entity.puffer_fish.hurt": "<PERSON><PERSON><PERSON><PERSON> jiya", "subtitles.entity.puffer_fish.sting": "O<PERSON>upa ami si", "subtitles.entity.rabbit.ambient": "Ehoro nṣẹlẹ", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON> lu", "subtitles.entity.rabbit.death": "<PERSON><PERSON><PERSON> ku", "subtitles.entity.rabbit.hurt": "<PERSON><PERSON>o ni irora", "subtitles.entity.rabbit.jump": "Ehoro n fo", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ra", "subtitles.entity.ravager.attack": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.ravager.celebrate": "<PERSON><PERSON><PERSON><PERSON> hails", "subtitles.entity.ravager.death": "<PERSON><PERSON><PERSON><PERSON> ku", "subtitles.entity.ravager.hurt": "<PERSON><PERSON><PERSON><PERSON> dun", "subtitles.entity.ravager.roar": "<PERSON><PERSON><PERSON><PERSON> kigbe", "subtitles.entity.ravager.step": "<PERSON><PERSON><PERSON><PERSON> nbọ", "subtitles.entity.ravager.stunned": "<PERSON><PERSON><PERSON><PERSON> ya<PERSON>", "subtitles.entity.salmon.death": "Salmon ku", "subtitles.entity.salmon.flop": "<PERSON>", "subtitles.entity.salmon.hurt": "Salmon jiya", "subtitles.entity.sheep.ambient": "Agutan n lu", "subtitles.entity.sheep.death": "<PERSON>g<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON> dun", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON>n", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> ku", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON>", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> a<PERSON>o", "subtitles.entity.shulker.teleport": "<PERSON><PERSON><PERSON><PERSON> tẹlifoonu Shulker", "subtitles.entity.shulker_bullet.hit": "<PERSON><PERSON><PERSON> ib<PERSON>n ti nwaye", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> ibọn fi opin si", "subtitles.entity.silverfish.ambient": "<PERSON><PERSON><PERSON><PERSON> erin <PERSON>", "subtitles.entity.silverfish.death": "Silverfish ku", "subtitles.entity.silverfish.hurt": "Silverfish dun", "subtitles.entity.skeleton.ambient": "Egungun rattles", "subtitles.entity.skeleton.converted_to_stray": "<PERSON><PERSON><PERSON> lṣáko", "subtitles.entity.skeleton.death": "<PERSON><PERSON><PERSON> ku", "subtitles.entity.skeleton.hurt": "<PERSON><PERSON><PERSON> dun", "subtitles.entity.skeleton.shoot": "Egun<PERSON> egungun", "subtitles.entity.skeleton_horse.ambient": "<PERSON><PERSON><PERSON> kigbe", "subtitles.entity.skeleton_horse.death": "<PERSON><PERSON><PERSON> ku", "subtitles.entity.skeleton_horse.hurt": "Ẹṣin egungun dun", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "<PERSON><PERSON><PERSON> ẹṣin ti nṣàn", "subtitles.entity.slime.attack": "Slug lu", "subtitles.entity.slime.death": "Slug ku", "subtitles.entity.slime.hurt": "Slime dun", "subtitles.entity.slime.squish": "Slug squish", "subtitles.entity.sniffer.death": "<PERSON>niffer ku", "subtitles.entity.sniffer.digging": "Sniffer digs", "subtitles.entity.sniffer.digging_stop": "Sniffer duro soke", "subtitles.entity.sniffer.drop_seed": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.eat": "Sniffer jẹun", "subtitles.entity.sniffer.egg_crack": "Sniffer Egg cracks", "subtitles.entity.sniffer.egg_hatch": "Sniffer Egg hatches", "subtitles.entity.sniffer.happy": "<PERSON><PERSON><PERSON> d<PERSON>n", "subtitles.entity.sniffer.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.idle": "<PERSON><PERSON><PERSON> k<PERSON>n", "subtitles.entity.sniffer.scenting": "<PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.searching": "<PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.sniffer.sniffing": "Sniffer nmi", "subtitles.entity.sniffer.step": "<PERSON><PERSON><PERSON> awọn igbesẹ", "subtitles.entity.snow_golem.death": "Golem egbon ku", "subtitles.entity.snow_golem.hurt": "Golem egbon dun", "subtitles.entity.snowball.throw": "Еgbon n fo", "subtitles.entity.spider.ambient": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "subtitles.entity.spider.death": "Alan<PERSON><PERSON>n ku", "subtitles.entity.spider.hurt": "Alantakun dun", "subtitles.entity.squid.ambient": "Ẹja ẹlẹsẹ mẹjọ nfofo", "subtitles.entity.squid.death": "Ẹja ẹlẹsẹ mẹjọ kú", "subtitles.entity.squid.hurt": "Ẹja ẹlẹsẹ mẹjọ dun", "subtitles.entity.squid.squirt": "Squid yoo fun ni inki", "subtitles.entity.stray.ambient": "Awọn ariwo ti o sọnu", "subtitles.entity.stray.death": "<PERSON>ray ku", "subtitles.entity.stray.hurt": "Stray dun", "subtitles.entity.strider.death": "Lavomerka kú", "subtitles.entity.strider.eat": "Lavomer jẹun", "subtitles.entity.strider.happy": "Lavomerka kọrin", "subtitles.entity.strider.hurt": "<PERSON><PERSON><PERSON>", "subtitles.entity.strider.idle": "<PERSON><PERSON><PERSON><PERSON> idap<PERSON>ọra Strider", "subtitles.entity.strider.retreat": "<PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.death": "Tadpole ku", "subtitles.entity.tadpole.flop": "Tadpole flops", "subtitles.entity.tadpole.grow_up": "Tadpole dagba", "subtitles.entity.tadpole.hurt": "Tadpole dun", "subtitles.entity.tnt.primed": "TNT pariwo", "subtitles.entity.tropical_fish.death": "Ẹja Ikun yii n ku", "subtitles.entity.tropical_fish.flop": "Tropical ẹja fifa", "subtitles.entity.tropical_fish.hurt": "Ẹja ti ẹja nla farapa", "subtitles.entity.turtle.ambient_land": "Ẹkuru ni orin", "subtitles.entity.turtle.death": "Ẹkuru ku", "subtitles.entity.turtle.death_baby": "Ẹkuru Ẹyẹ ku", "subtitles.entity.turtle.egg_break": "Turtle ẹyin fifọ", "subtitles.entity.turtle.egg_crack": "Turtle ẹyin fọ", "subtitles.entity.turtle.egg_hatch": "Turtle hatched", "subtitles.entity.turtle.hurt": "Ẹkuru jiya", "subtitles.entity.turtle.hurt_baby": "Ẹkuru ẹyẹ jẹ ijiya", "subtitles.entity.turtle.lay_egg": "Ẹkuru ẹiyẹ mu ẹyin", "subtitles.entity.turtle.shamble": "<PERSON><PERSON><PERSON>", "subtitles.entity.turtle.shamble_baby": "<PERSON><PERSON><PERSON> kek<PERSON>", "subtitles.entity.turtle.swim": "Ijapa we", "subtitles.entity.vex.ambient": "Ipalara ibanuje", "subtitles.entity.vex.charge": "<PERSON><PERSON><PERSON> squeals", "subtitles.entity.vex.death": "<PERSON>palara ku", "subtitles.entity.vex.hurt": "Ipalara dun", "subtitles.entity.villager.ambient": "<PERSON> ilu kikoro", "subtitles.entity.villager.celebrate": "<PERSON> ilu ka<PERSON>", "subtitles.entity.villager.death": "Ara ilu ku", "subtitles.entity.villager.hurt": "Ara ilu dun", "subtitles.entity.villager.no": "Ara ilu koo", "subtitles.entity.villager.trade": "<PERSON> ilu paar<PERSON>", "subtitles.entity.villager.work_armorer": "Awọn gunmith ṣiṣẹ", "subtitles.entity.villager.work_butcher": "Olupa ṣiṣẹ", "subtitles.entity.villager.work_cartographer": "Oluyaworan n ṣiṣẹ", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON> ṣiṣẹ", "subtitles.entity.villager.work_farmer": "Agbe n ṣiṣẹ", "subtitles.entity.villager.work_fisherman": "Awọn apeja ṣiṣẹ", "subtitles.entity.villager.work_fletcher": "Tafàtafà ń ṣiṣẹ́", "subtitles.entity.villager.work_leatherworker": "<PERSON> ṣiṣẹ", "subtitles.entity.villager.work_librarian": "Ikawe ṣiṣẹ", "subtitles.entity.villager.work_mason": "<PERSON> ṣiṣẹ", "subtitles.entity.villager.work_shepherd": "Oluṣọ -agutan n ṣiṣẹ", "subtitles.entity.villager.work_toolsmith": "Onisẹ ẹrọ ṣiṣẹ", "subtitles.entity.villager.work_weaponsmith": "Awọn gunmith ṣiṣẹ", "subtitles.entity.villager.yes": "Ara ilu gba", "subtitles.entity.vindicator.ambient": "<PERSON><PERSON><PERSON><PERSON><PERSON> kikoro", "subtitles.entity.vindicator.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.death": "<PERSON><PERSON><PERSON><PERSON><PERSON> ku", "subtitles.entity.vindicator.hurt": "<PERSON><PERSON><PERSON><PERSON><PERSON> dun", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON><PERSON> ayo kikoro", "subtitles.entity.wandering_trader.death": "<PERSON>lugbe ayo ku", "subtitles.entity.wandering_trader.disappeared": "Oniṣowo itine naa parẹ", "subtitles.entity.wandering_trader.drink_milk": "Oniṣowo itine jẹ mimu wara", "subtitles.entity.wandering_trader.drink_potion": "Oniṣòwo ti n rin kiri n mu ọra kan", "subtitles.entity.wandering_trader.hurt": "Alarinkiri Onisowo ṣe ipalara", "subtitles.entity.wandering_trader.no": "Alarinkiri Onisowo ko gba", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON><PERSON><PERSON><PERSON> irin-ajo kan ti han", "subtitles.entity.wandering_trader.trade": "<PERSON><PERSON><PERSON><PERSON> on<PERSON>o Onija<PERSON> kakiri", "subtitles.entity.wandering_trader.yes": "Alarinkiri Oniṣowo gba", "subtitles.entity.warden.agitated": "Warden kerora ni ibinu", "subtitles.entity.warden.ambient": "<PERSON> k<PERSON>n", "subtitles.entity.warden.angry": "<PERSON> <PERSON>", "subtitles.entity.warden.attack_impact": "<PERSON><PERSON><PERSON><PERSON> ilẹ Warden lu", "subtitles.entity.warden.death": "Warden ku", "subtitles.entity.warden.dig": "Warden digs", "subtitles.entity.warden.emerge": "Warden <PERSON>", "subtitles.entity.warden.heartbeat": "Ọkàn Warden n lu", "subtitles.entity.warden.hurt": "Warden dun", "subtitles.entity.warden.listening": "Warden gba a<PERSON>i", "subtitles.entity.warden.listening_angry": "Warden gba akiyesi ni ibinu", "subtitles.entity.warden.nearby_close": "Warden yo<PERSON>o", "subtitles.entity.warden.nearby_closer": "Warden a<PERSON><PERSON><PERSON>", "subtitles.entity.warden.nearby_closest": "<PERSON> fa sunmo", "subtitles.entity.warden.roar": "Warden roars", "subtitles.entity.warden.sniff": "Warden sniffs", "subtitles.entity.warden.sonic_boom": "<PERSON> a<PERSON><PERSON>", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON><PERSON> id<PERSON>", "subtitles.entity.warden.step": "<PERSON><PERSON><PERSON><PERSON> igbesẹ Warden", "subtitles.entity.warden.tendril_clicks": "Awọn tendrils Warden tẹ", "subtitles.entity.wind_charge.throw": "Wind Charge flies", "subtitles.entity.wind_charge.wind_burst": "Wind Charge bursts", "subtitles.entity.witch.ambient": "<PERSON><PERSON>", "subtitles.entity.witch.celebrate": "Aje ni idunnu", "subtitles.entity.witch.death": "<PERSON>je ku", "subtitles.entity.witch.drink": "<PERSON><PERSON> mimu", "subtitles.entity.witch.hurt": "<PERSON><PERSON> dun", "subtitles.entity.witch.throw": "<PERSON><PERSON>", "subtitles.entity.wither.ambient": "<PERSON><PERSON> binu", "subtitles.entity.wither.death": "<PERSON>je ku", "subtitles.entity.wither.hurt": "<PERSON>er <PERSON>", "subtitles.entity.wither.shoot": "Awọn i<PERSON> ijaya", "subtitles.entity.wither.spawn": "Ti tu silẹ", "subtitles.entity.wither_skeleton.ambient": "Wither Skeleton rattles", "subtitles.entity.wither_skeleton.death": "Wither <PERSON> ku", "subtitles.entity.wither_skeleton.hurt": "Wither S<PERSON>eton dun", "subtitles.entity.wolf.ambient": "<PERSON><PERSON><PERSON><PERSON> so<PERSON>", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "<PERSON> k<PERSON>", "subtitles.entity.wolf.growl": "<PERSON><PERSON><PERSON>", "subtitles.entity.wolf.hurt": "<PERSON><PERSON><PERSON> dun", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "<PERSON><PERSON><PERSON> gbon", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "<PERSON>oglin roars", "subtitles.entity.zoglin.angry": "<PERSON><PERSON><PERSON> dagba ni ibinu", "subtitles.entity.zoglin.attack": "Awọn i<PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.entity.zoglin.hurt": "\"Zoglin ń díẹ̀.\"", "subtitles.entity.zoglin.step": "\"Àwọn pílò Zoglin\"", "subtitles.entity.zombie.ambient": "Zombie kérora", "subtitles.entity.zombie.attack_wooden_door": "'Yí dúrọ́wà'", "subtitles.entity.zombie.break_wooden_door": "‘’Bẹ́ẹ̀ iṣẹ́ náà‘’", "subtitles.entity.zombie.converted_to_drowned": "‘’Àwon àyọkà ń gbọgbọ́n àyọkà òké‘’", "subtitles.entity.zombie.death": "Zombie ku", "subtitles.entity.zombie.destroy_egg": "“”Àwon ojú-ìjá tí a pín“”", "subtitles.entity.zombie.hurt": "Zombie dun", "subtitles.entity.zombie.infect": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombie_horse.ambient": "Ẹkun Zombie kigbe", "subtitles.entity.zombie_horse.death": "Ẹṣin Zombie ku", "subtitles.entity.zombie_horse.hurt": "Ẹṣin Zombie ṣe ipalara", "subtitles.entity.zombie_villager.ambient": "Zombie Villager kérora", "subtitles.entity.zombie_villager.converted": "“”Zombie Villager vociferates“”", "subtitles.entity.zombie_villager.cure": "“”Sọ́fẹ́ àwon ọgbọ́à Zombie“”", "subtitles.entity.zombie_villager.death": "Zombie Villager ku", "subtitles.entity.zombie_villager.hurt": "Zombie Villager <PERSON><PERSON>", "subtitles.entity.zombified_piglin.ambient": "”Àwọn grunts <PERSON><PERSON> Zombified“", "subtitles.entity.zombified_piglin.angry": "”Pá àwọn kólá Piglin jẹ́ alátagbà‘’", "subtitles.entity.zombified_piglin.death": "Mankú ènìyàn ẹlẹdẹ ku", "subtitles.entity.zombified_piglin.hurt": "‘’Irúfẹ́ Piglin àyọkà Ìgbárà‘’", "subtitles.event.mob_effect.bad_omen": "Omen takes hold", "subtitles.event.mob_effect.raid_omen": "Raid looms nearby", "subtitles.event.mob_effect.trial_omen": "Ominous trial looms nearby", "subtitles.event.raid.horn": "Ìwo búburú ń dún", "subtitles.item.armor.equip": "Yọ kuro ni ẹhin", "subtitles.item.armor.equip_chain": "Jingles ihamọra pq", "subtitles.item.armor.equip_diamond": "Diamond ihamọra clangs", "subtitles.item.armor.equip_elytra": "Rustling ti elytra", "subtitles.item.armor.equip_gold": "<PERSON><PERSON><PERSON><PERSON> ihamọra ihamọra goolu", "subtitles.item.armor.equip_iron": "<PERSON><PERSON><PERSON><PERSON> idile i<PERSON> irin", "subtitles.item.armor.equip_leather": "<PERSON><PERSON><PERSON>", "subtitles.item.armor.equip_netherite": "‘’Gbéjà èlé ìsàlẹ̀ ìsàlẹ̀‘’", "subtitles.item.armor.equip_turtle": "<PERSON><PERSON><PERSON> i<PERSON>", "subtitles.item.armor.equip_wolf": "Wolf Armor is fastened", "subtitles.item.armor.unequip_wolf": "Wolf Armor snips away", "subtitles.item.axe.scrape": "Aku a<PERSON>ku", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON><PERSON> ila <PERSON>", "subtitles.item.axe.wax_off": "<PERSON>po pa", "subtitles.item.bone_meal.use": "<PERSON><PERSON><PERSON> Ounjẹ crinkles", "subtitles.item.book.page_turn": "<PERSON><PERSON><PERSON> o<PERSON> -iwe", "subtitles.item.book.put": "<PERSON><PERSON><PERSON><PERSON> kika iwe", "subtitles.item.bottle.empty": "<PERSON><PERSON> ofo", "subtitles.item.bottle.fill": "<PERSON><PERSON> kun", "subtitles.item.brush.brushing.generic": "Fẹlẹfẹlẹ", "subtitles.item.brush.brushing.gravel": "Fifọ wẹwẹ", "subtitles.item.brush.brushing.gravel.complete": "Brushing Gravel ti pari", "subtitles.item.brush.brushing.sand": "<PERSON><PERSON><PERSON> brushing", "subtitles.item.brush.brushing.sand.complete": "<PERSON><PERSON><PERSON> ti pari", "subtitles.item.bucket.empty": "Garawa ṣ'ofo", "subtitles.item.bucket.fill": "Garawa kún", "subtitles.item.bucket.fill_axolotl": "Axolotl ṣe ayẹyẹ", "subtitles.item.bucket.fill_fish": "<PERSON><PERSON> mú", "subtitles.item.bucket.fill_tadpole": "Tadpole sile", "subtitles.item.bundle.drop_contents": "<PERSON><PERSON><PERSON>", "subtitles.item.bundle.insert": "Nkan ti koj<PERSON>", "subtitles.item.bundle.insert_fail": "Bundle full", "subtitles.item.bundle.remove_one": "Nkan ti ko ni idi", "subtitles.item.chorus_fruit.teleport": "Awọn ẹrọ orin teleports", "subtitles.item.crop.plant": "Irugbin gbin", "subtitles.item.crossbow.charge": "Crossbow gba agbara soke", "subtitles.item.crossbow.hit": "Ọfà deba", "subtitles.item.crossbow.load": "Awọn ẹru Crossbow", "subtitles.item.crossbow.shoot": "<PERSON><PERSON><PERSON><PERSON> ina Crossbow", "subtitles.item.dye.use": "Awọn abawọn awọ", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Fireball whooshes", "subtitles.item.flintandsteel.use": "<PERSON> ati Irin tẹ", "subtitles.item.glow_ink_sac.use": "Alábá Inki Sac splotches", "subtitles.item.goat_horn.play": "Ewúrẹ Horn awọn ere", "subtitles.item.hoe.till": "Hoe tills", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.honeycomb.wax_on": "Epo-eti lori", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Ink Sac splotches", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Awọn titiipa kọmpasi Lodestone pẹlẹpẹlẹ si Lodestone", "subtitles.item.mace.smash_air": "<PERSON> smashes", "subtitles.item.mace.smash_ground": "<PERSON> smashes", "subtitles.item.nether_wart.plant": "Irugbin gbin", "subtitles.item.ominous_bottle.dispose": "Bottle breaks", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "Shears tẹ", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "Awọn bulọọki Shield", "subtitles.item.shovel.flatten": "Ṣọṣọ flattens", "subtitles.item.spyglass.stop_using": "Awọn retracts Spyglass", "subtitles.item.spyglass.use": "Spyglass gbooro", "subtitles.item.totem.use": "Totem ṣiṣẹ", "subtitles.item.trident.hit": "<PERSON>wọn itọsi Trident", "subtitles.item.trident.hit_ground": "Trident gbọn", "subtitles.item.trident.return": "Trident pada", "subtitles.item.trident.riptide": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.trident.throw": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.item.trident.thunder": "Trident <PERSON><PERSON>", "subtitles.item.wolf_armor.break": "<PERSON>or breaks", "subtitles.item.wolf_armor.crack": "Wolf Armor cracks", "subtitles.item.wolf_armor.damage": "Wolf Armor takes damage", "subtitles.item.wolf_armor.repair": "Wolf Armor is repaired", "subtitles.particle.soul_escape": "Ọkàn sa", "subtitles.ui.cartography_table.take_result": "<PERSON><PERSON><PERSON> kale", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "Loom lo", "subtitles.ui.stonecutter.take_result": "<PERSON>ige <PERSON>uta ti lo", "subtitles.weather.rain": "<PERSON><PERSON>", "symlink_warning.message": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.pack": "Loading packs with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.more_info": "More Information", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.title.pack": "Added pack(s) contain(s) symbolic links", "symlink_warning.title.world": "The world folder contains symbolic links", "team.collision.always": "Nigbagbogbo", "team.collision.never": "<PERSON><PERSON>", "team.collision.pushOtherTeams": "<PERSON><PERSON><PERSON> aw<PERSON>n ẹgbẹ miiran", "team.collision.pushOwnTeam": "T<PERSON>ri ẹgbẹ tirẹ", "team.notFound": "Ẹgbẹ ti a ko mọ '%s'", "team.visibility.always": "Nigbagbogbo", "team.visibility.hideForOtherTeams": "Tọju fun awọn ẹgbẹ miiran", "team.visibility.hideForOwnTeam": "Tọju fun ẹgbẹ tirẹ", "team.visibility.never": "<PERSON><PERSON>", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Ilosiwaju Ti Ṣe", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (<PERSON><PERSON><PERSON><PERSON>)", "telemetry.event.optional.disabled": "%s (Optional) - Disabled", "telemetry.event.performance_metrics.description": "Mọ profaili iṣẹ gbogbogbo ti Minecraft ṣe iranlọwọ fun wa lati tune ati mu ere naa pọ si fun ọpọlọpọ awọn pato ẹrọ ati awọn ọna ṣiṣe.\nẸya ere wa pẹlu lati ṣe iranlọwọ fun wa lati ṣe afiwe profaili iṣẹ fun awọn ẹya tuntun ti Minecraft.", "telemetry.event.performance_metrics.title": "<PERSON><PERSON><PERSON><PERSON> Iṣẹ", "telemetry.event.required": "%s (Beere)", "telemetry.event.world_load_times.description": "O ṣe pataki fun wa lati ni oye bi o ṣe pẹ to lati darapọ mọ agbaye kan, ati bii iyẹn ṣe yipada lori akoko. Fun apẹẹrẹ, nigba ti a ba ṣafikun awọn ẹya tuntun tabi ṣe awọn ayipada imọ-ẹrọ nla, a nilo lati rii iru ipa ti o ni lori awọn akoko fifuye.", "telemetry.event.world_load_times.title": "World fifuye Times", "telemetry.event.world_loaded.description": "Mọ bi awọn ẹrọ orin ṣe mu Minecraft (gẹgẹbi <PERSON><PERSON>, <PERSON><PERSON> tabi olupin modded, ati ẹya ere) gba wa laaye lati dojukọ awọn imudojuiwọn ere lati mu awọn agbegbe ti awọn oṣere bikita julọ.\nIṣẹlẹ Ti kojọpọ Agbaye jẹ so pọ pẹlu iṣẹlẹ ti ko gbejade agbaye lati ṣe iṣiro bawo ni igba ere naa ti pẹ to.", "telemetry.event.world_loaded.title": "Aye ti koj<PERSON><PERSON>ọ", "telemetry.event.world_unloaded.description": "Iṣẹlẹ yii jẹ idapọ pẹlu iṣẹlẹ Ti kojọpọ Agbaye lati ṣe iṣiro bii igba ti igba aye ti pẹ to.\nIye akoko naa (ni awọn iṣẹju-aaya ati awọn ami-ami) jẹ wiwọn nigbati igba aye ba ti pari (fil<PERSON> si ak<PERSON>, gige asop<PERSON> lati <PERSON>).", "telemetry.event.world_unloaded.title": "Aye Unloaded", "telemetry.property.advancement_game_time.title": "Game Time (Ticks)", "telemetry.property.advancement_id.title": "Ilọsiwaju ID", "telemetry.property.client_id.title": "ID onibara", "telemetry.property.client_modded.title": "<PERSON><PERSON><PERSON>", "telemetry.property.dedicated_memory_kb.title": "<PERSON>ti <PERSON> (kB)", "telemetry.property.event_timestamp_utc.title": "Akoko Iṣẹlẹ (UTC)", "telemetry.property.frame_rate_samples.title": "Awọn ayẹwo Iwọn fireemu (FPS)", "telemetry.property.game_mode.title": "<PERSON><PERSON>", "telemetry.property.game_version.title": "Ere Version", "telemetry.property.launcher_name.title": "Launcher or<PERSON>o", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "Minecraft Ikoni ID", "telemetry.property.new_world.title": "<PERSON>", "telemetry.property.number_of_samples.title": "<PERSON><PERSON><PERSON> ayẹwo", "telemetry.property.operating_system.title": "<PERSON>to is<PERSON>e", "telemetry.property.opt_in.title": "<PERSON>", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "<PERSON><PERSON><PERSON>", "telemetry.property.render_time_samples.title": "Mu Time Awọn ayẹwo", "telemetry.property.seconds_since_load.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ìrùsókè (aaya)", "telemetry.property.server_modded.title": "Server Modded", "telemetry.property.server_type.title": "<PERSON><PERSON> o<PERSON>", "telemetry.property.ticks_since_load.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ìrùsókè (Ticks)", "telemetry.property.used_memory_samples.title": "Lo ID Access Memory", "telemetry.property.user_id.title": "Idanimọ olumulo", "telemetry.property.world_load_time_ms.title": "<PERSON>koko <PERSON> (Milliseconds)", "telemetry.property.world_session_id.title": "ID Ikoni Agbaye", "telemetry_info.button.give_feedback": "<PERSON> idahun han", "telemetry_info.button.privacy_statement": "Privacy Statement", "telemetry_info.button.show_data": "Ṣi Data Mia", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "Data to wa", "telemetry_info.screen.description": "Gbigba data yii ṣe iranlọwọ fun wa ni ilọsiwaju Minecraft nipa didari wa ni awọn itọsọna ti o ṣe pataki si awọn oṣere wa.\nO tun le firanṣẹ ni awọn esi afikun lati ṣe iranlọwọ fun wa lati ni ilọsiwaju Minecraft.", "telemetry_info.screen.title": "Telemetry Data Gbigba", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "Eto 32-bit ti a rii: eyi le ṣe idiwọ fun ọ lati ṣere ni ọjọ iwaju nitori eto 64-bit yoo nilo!", "title.32bit.deprecation.realms": "Minecraft yoo nilo eto 64-bit laipẹ, eyi ti yoo ṣe idiwọ fun ọ lati ṣiṣẹ tabi lo awọn ibugbe lori ẹrọ yii. Iwọ yoo nilo lati fagilee ṣiṣe alabapin awọn ibugbe eyikeyi pẹlu ọwọ.", "title.32bit.deprecation.realms.check": "Maṣe fi iboju yii han lẹẹkansi", "title.32bit.deprecation.realms.header": "32-bit eto ri", "title.credits": "Copyright Mojang AB. Do not distribute!", "title.multiplayer.disabled": "Pupọ jẹ alaabo. Jọwọ ṣayẹwo awọn eto akọọlẹ Microsoft rẹ.", "title.multiplayer.disabled.banned.name": "You must change your name before you can play online", "title.multiplayer.disabled.banned.permanent": "Akọọlẹ rẹ ti daduro fun igba pipẹ lati ere ori ayelujara", "title.multiplayer.disabled.banned.temporary": "Akọọlẹ rẹ ti daduro fun igba diẹ lati ere ori ayelujara", "title.multiplayer.lan": "<PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON> pupọ (<PERSON>lu<PERSON> ẹgbẹ kẹta)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "nikan player", "translation.test.args": "%s %s", "translation.test.complex": "Ipilẹṣẹ,%s%2$s lẹẹkansi %s ati %1$s ni pipe %s ati tun %1$s lẹẹkansi!", "translation.test.escape": "%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "e ǹlẹ́ %", "translation.test.invalid2": "e ǹlẹ́ %s", "translation.test.none": "E ǹlẹ́, ayé!", "translation.test.world": "ayé", "trim_material.minecraft.amethyst": "<PERSON><PERSON>", "trim_material.minecraft.copper": "<PERSON>un èlò idẹ", "trim_material.minecraft.diamond": "<PERSON>un èlò Ẹ̀yẹ́", "trim_material.minecraft.emerald": "<PERSON><PERSON>", "trim_material.minecraft.gold": "<PERSON><PERSON>", "trim_material.minecraft.iron": "<PERSON><PERSON>", "trim_material.minecraft.lapis": "<PERSON><PERSON>", "trim_material.minecraft.netherite": "<PERSON><PERSON>", "trim_material.minecraft.quartz": "<PERSON><PERSON>", "trim_material.minecraft.redstone": "<PERSON><PERSON> è<PERSON>ò Redstone", "trim_material.minecraft.resin": "<PERSON><PERSON>", "trim_pattern.minecraft.bolt": "Bolt Armor Trim", "trim_pattern.minecraft.coast": "Coast Armor Gee", "trim_pattern.minecraft.dune": "<PERSON>ne Armor Gee", "trim_pattern.minecraft.eye": "<PERSON><PERSON>", "trim_pattern.minecraft.flow": "Flow Armor Trim", "trim_pattern.minecraft.host": "Ogun Armor Gee", "trim_pattern.minecraft.raiser": "Raiser Armor Gee", "trim_pattern.minecraft.rib": "<PERSON><PERSON>", "trim_pattern.minecraft.sentry": "Sentry Armor Gee", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON>", "trim_pattern.minecraft.silence": "Ipalọlọ Armor Gee", "trim_pattern.minecraft.snout": "Snout Armor Gee", "trim_pattern.minecraft.spire": "Spire Armor <PERSON>", "trim_pattern.minecraft.tide": "Tide Armor Gee", "trim_pattern.minecraft.vex": "Vex Armor Gee", "trim_pattern.minecraft.ward": "<PERSON>", "trim_pattern.minecraft.wayfinder": "Wayfinder Armor Gee", "trim_pattern.minecraft.wild": "Wild Armor Gee", "tutorial.bundleInsert.description": "Ọtun Tẹ lati fi awọn ohun kan kun", "tutorial.bundleInsert.title": "Օգտագործեք Պարկ", "tutorial.craft_planks.description": "<PERSON>we ohunelo le <PERSON>e i<PERSON>lọw<PERSON>", "tutorial.craft_planks.title": "<PERSON>w<PERSON><PERSON> igi onigi iṣẹ ọwọ", "tutorial.find_tree.description": "Punch rẹ lati gba igi", "tutorial.find_tree.title": "Wa igi kan", "tutorial.look.description": "Lo asin rẹ lati yipada", "tutorial.look.title": "Wo ni ayika", "tutorial.move.description": "Lọ pẹlu %s", "tutorial.move.title": "Gbe pẹlu %s, %s, %s ati %s", "tutorial.open_inventory.description": "Tẹ %s", "tutorial.open_inventory.title": "Ṣii akojo oja rẹ", "tutorial.punch_tree.description": "Duro %s", "tutorial.punch_tree.title": "Pa igi naa run", "tutorial.socialInteractions.description": "Tẹ %s lati ṣii", "tutorial.socialInteractions.title": "Ibaṣ<PERSON><PERSON>", "upgrade.minecraft.netherite_upgrade": "Netherite Igbesoke"}