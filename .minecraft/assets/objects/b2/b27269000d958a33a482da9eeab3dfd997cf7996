{"accessibility.onboarding.accessibility.button": "Ntọala nnweta...", "accessibility.onboarding.screen.narrator": "<PERSON><PERSON><PERSON> tinye iji mee ka onye na-agụ akụkọ nwee ike", "accessibility.onboarding.screen.title": "Nabata na Minecraft! Ọ ga-amasị gị ịme ka onye na-akọ akụkọ ma ọ bụ gaa na Ntọala nnweta?", "addServer.add": "<PERSON><PERSON><PERSON><PERSON>", "addServer.enterIp": "Adreesị sava", "addServer.enterName": "Aha Server", "addServer.resourcePack": "Mkpesa sava Server", "addServer.resourcePack.disabled": "Nwere nkwar<PERSON>", "addServer.resourcePack.enabled": "Kwadoro", "addServer.resourcePack.prompt": "<PERSON><PERSON>", "addServer.title": "<PERSON><PERSON>", "advMode.command": "Njikwa njikwa", "advMode.mode": "Mode", "advMode.mode.auto": "Tinyegharịa", "advMode.mode.autoexec.bat": "Nọ n'ọrụ mgbe niile", "advMode.mode.conditional": "Ọnọdụ", "advMode.mode.redstone": "\n\nMk<PERSON><PERSON>", "advMode.mode.redstoneTriggered": "Mkpa Redstone", "advMode.mode.sequence": "<PERSON><PERSON>", "advMode.mode.unconditional": "Ọnọdụ", "advMode.notAllowed": "G<PERSON><PERSON><PERSON><PERSON><PERSON> onye <PERSON>ụkpọ opped na <PERSON>n<PERSON><PERSON><PERSON> okike", "advMode.notEnabled": "<PERSON><PERSON><PERSON><PERSON> ike inye iwu iwu na sava a", "advMode.previousOutput": "Mbu<PERSON> gara aga", "advMode.setCommand": "<PERSON><PERSON><PERSON> n<PERSON>kwa maka Block", "advMode.setCommand.success": "<PERSON><PERSON> nyere: %s", "advMode.trackOutput": "<PERSON><PERSON><PERSON>", "advMode.triggering": "Na -akpali akpali", "advMode.type": "<PERSON><PERSON><PERSON><PERSON>", "advancement.advancementNotFound": "Mgbagote amaghị ama:%s", "advancements.adventure.adventuring_time.description": "Chọpụta biome ọ bụla", "advancements.adventure.adventuring_time.title": "Oge na -atọ ụtọ", "advancements.adventure.arbalistic.description": "<PERSON><PERSON> were gbuo iwe mmadụ ise pụrụ iche", "advancements.adventure.arbalistic.title": "<PERSON><PERSON><PERSON><PERSON> injin agha", "advancements.adventure.avoid_vibration.description": "Mibaa na-ak<PERSON>kụ ihe mmetụta skulk ma ọ bụ wodin wee ghanahụ ya Ichọpụta gị", "advancements.adventure.avoid_vibration.title": "Nkeji 100", "advancements.adventure.blowback.description": "Gbuo ikuku site na chaja ikuku gbara ikuku", "advancements.adventure.blowback.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.brush_armadillo.description": "Nweta Armadillo Scutes site na Armadillo site na iji ahịhịa", "advancements.adventure.brush_armadillo.title": "Ọ bụgh<PERSON> Scute?", "advancements.adventure.bullseye.description": "Kụọ anya ehi nke ihe mgbochi iche site na opekata mpe mita iri atọ n'idị anya", "advancements.adventure.bullseye.title": "Bullseye", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Mee ite a chọrọ mma n'ime ụlọ anọ anọ", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Mwegh<PERSON> nke <PERSON>", "advancements.adventure.crafters_crafting_crafters.description": "<PERSON><PERSON><PERSON><PERSON> nso onye nka mgbe ọ na-arụ ọrụ nka", "advancements.adventure.crafters_crafting_crafters.title": "<PERSON>dị na-a<PERSON><PERSON>", "advancements.adventure.fall_from_world_height.description": "Free ọdịda si n'elu <PERSON>wa (ewu ók<PERSON>) na ala nke <PERSON>wa na-adị ndụ", "advancements.adventure.fall_from_world_height.title": "Caves & Cliffs", "advancements.adventure.heart_transplanter.description": "Place a Creaking Heart with the correct alignment between two Pale Oak Log blocks", "advancements.adventure.heart_transplanter.title": "Heart Transplanter", "advancements.adventure.hero_of_the_village.description": "Na -agbachitere obodo nke ọma site na mwakpo", "advancements.adventure.hero_of_the_village.title": "<PERSON>ke obodo", "advancements.adventure.honey_block_slide.description": "Banye na Mmanụ a Honeyụ na-egbochi imebi ọdịda gị", "advancements.adventure.honey_block_slide.title": "Ọnọdụ siri ike", "advancements.adventure.kill_a_mob.description": "Gbuo n<PERSON>wu an<PERSON>j<PERSON> bụla", "advancements.adventure.kill_a_mob.title": "<PERSON><PERSON><PERSON><PERSON> an<PERSON>", "advancements.adventure.kill_all_mobs.description": "Gbuo otu n'ime nnukwu anụ ọjọọ ọ bụla", "advancements.adventure.kill_all_mobs.title": "<PERSON><PERSON><PERSON><PERSON>", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Gbuo igwe mmadụ n'akụkụ ihe na-akpali skulk", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "O na-agbasa", "advancements.adventure.lighten_up.description": "<PERSON><PERSON><PERSON> k<PERSON> ka <PERSON> na-egbuke egbuke", "advancements.adventure.lighten_up.title": "Mee elu", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Gbo<PERSON>e onye Obodo n'aka ụj<PERSON> achọghị achọ na-amunyeghị ọkụ", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Anya e<PERSON>be", "advancements.adventure.minecraft_trials_edition.description": "Kwụpụ <PERSON> n'ụlọ ikpe", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: <PERSON><PERSON><PERSON><PERSON><PERSON> (s) nnwale", "advancements.adventure.ol_betsy.description": "Gbaa uta", "advancements.adventure.ol_betsy.title": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "advancements.adventure.overoverkill.description": "<PERSON><PERSON> obi 50 nke mmebi n'otu ihe ọkụkụ site na iji Mace", "advancements.adventure.overoverkill.title": "Over-Overkill", "advancements.adventure.play_jukebox_in_meadows.description": "Me<PERSON> ka ala ahịhịa juru dị ndụ site na ụda egwu sitere na akpati juke", "advancements.adventure.play_jukebox_in_meadows.title": "Ụda Egwu", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Read the power signal of a Chiseled Bookshelf using a Comparator", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "The Power of Books", "advancements.adventure.revaulting.description": "Unlock an Ominous Vault with an Ominous Trial Key", "advancements.adventure.revaulting.title": "Revaulting", "advancements.adventure.root.description": "Adventure, nyocha na ọgụ", "advancements.adventure.root.title": "<PERSON><PERSON><PERSON> njem", "advancements.adventure.salvage_sherd.description": "Brush a Suspicious block to obtain a Pottery Sherd", "advancements.adventure.salvage_sherd.title": "Respecting the Remnants", "advancements.adventure.shoot_arrow.description": "Were àkù gbaa ihe", "advancements.adventure.shoot_arrow.title": "<PERSON>", "advancements.adventure.sleep_in_bed.description": "<PERSON><PERSON><PERSON><PERSON> were gbanwee ọnụ ntụgharị gị", "advancements.adventure.sleep_in_bed.title": "Ezigbo ụra", "advancements.adventure.sniper_duel.description": "Gbuo Skeleton ma ọ d<PERSON>kar<PERSON>a ala mita 50", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.spyglass_at_dragon.description": "<PERSON><PERSON> Ender dragọn site na enyo onye nledo", "advancements.adventure.spyglass_at_dragon.title": "Ọ bụ ụgbọ elu?", "advancements.adventure.spyglass_at_ghast.description": "Lee anya egwu site na enyo onye nledo", "advancements.adventure.spyglass_at_ghast.title": "Ọ bụ balloon?", "advancements.adventure.spyglass_at_parrot.description": "Lee icheoku anya site na enyo onye nledo", "advancements.adventure.spyglass_at_parrot.title": "Ọ bụ <PERSON>ụ?", "advancements.adventure.summon_iron_golem.description": "Kpọkọta Iron Golem iji nyere aka chebe obodo", "advancements.adventure.summon_iron_golem.title": "Enyemaka e goro", "advancements.adventure.throw_trident.description": "<PERSON><PERSON><PERSON><PERSON> Trident na ihe. Mara: Ịtụfu naanị ngwa ọgụ gị abụghị ezigbo echiche.", "advancements.adventure.throw_trident.title": "Ọchị egwu<PERSON><PERSON><PERSON>fu", "advancements.adventure.totem_of_undying.description": "<PERSON><PERSON> nke na -ekwegh<PERSON> ekwe ghọgbuo ọnwụ", "advancements.adventure.totem_of_undying.title": "Ọnwụ anwụ", "advancements.adventure.trade.description": "<PERSON><PERSON> onye ime obodo <PERSON> ah<PERSON>a nke <PERSON>", "advancements.adventure.trade.title": "Nso Edinam N̄kpọ!", "advancements.adventure.trade_at_world_height.description": "<PERSON><PERSON><PERSON> ahịa n'aka onye Obodo n'ogo njedebe nke nwube", "advancements.adventure.trade_at_world_height.title": "Onye na-ere ah<PERSON>a <PERSON>", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Apply these smithing templates at least once: <PERSON><PERSON>, <PERSON><PERSON>ut, R<PERSON>, Ward, Silence, Vex, Tide, Wayfinder", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Ịkụ ọlụ igwe n'ụdị", "advancements.adventure.trim_with_any_armor_pattern.description": "Craft trimmed armor at a Smithing Table", "advancements.adventure.trim_with_any_armor_pattern.title": "Crafting a New Look", "advancements.adventure.two_birds_one_arrow.description": "<PERSON><PERSON> a<PERSON> na-apu gbuo Phantoms abụọ", "advancements.adventure.two_birds_one_arrow.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>, o<PERSON> a<PERSON>", "advancements.adventure.under_lock_and_key.description": "Unlock a Vault with a Trial Key", "advancements.adventure.under_lock_and_key.title": "Under Lock and Key", "advancements.adventure.use_lodestone.description": "Use a Compass on a Lodestone", "advancements.adventure.use_lodestone.title": "Country Lode, Take Me Home", "advancements.adventure.very_very_frightening.description": "<PERSON><PERSON> tigbuo onye bi n'ime obodo", "advancements.adventure.very_very_frightening.title": "Nnọọ <PERSON>", "advancements.adventure.voluntary_exile.description": "Gbuo onye agha wakporo.\nMa eleghị anya tụlee <PERSON>n<PERSON>pụ n'obodo nta maka oge a...", "advancements.adventure.voluntary_exile.title": "Ọpụpụ afọ ofufo", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Walk on Powder Snow... without sinking in it", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Ìhè dị ka oke bekee", "advancements.adventure.who_needs_rockets.description": "Use a Wind Charge to launch yourself upward 8 blocks", "advancements.adventure.who_needs_rockets.title": "Who Needs Rockets?", "advancements.adventure.whos_the_pillager_now.description": "<PERSON>ye Pillager ụtọ nke ọgwụ nke ha", "advancements.adventure.whos_the_pillager_now.title": "'<PERSON><PERSON><PERSON> b<PERSON>ger ugbu a?", "advancements.empty": "O yighị ka ihe ọ bụla ebe a...", "advancements.end.dragon_breath.description": "Chik<PERSON><PERSON> nkumume dragọn n'ime otu karama enyo", "advancements.end.dragon_breath.title": "I chọrọ otu minti", "advancements.end.dragon_egg.description": "<PERSON><PERSON> a<PERSON>", "advancements.end.dragon_egg.title": "Ọgbọ nke <PERSON>z<PERSON>", "advancements.end.elytra.description": "<PERSON><PERSON><PERSON>", "advancements.end.elytra.title": "<PERSON><PERSON><PERSON><PERSON> bụ ebe nje<PERSON>be", "advancements.end.enter_end_gateway.description": "Gbana<PERSON>ụ agwaetiti ah<PERSON>", "advancements.end.enter_end_gateway.title": "Njepụ ime ime", "advancements.end.find_end_city.description": "<PERSON><PERSON>, kedu ihe nwere ike ime?", "advancements.end.find_end_city.title": "Obodo dị na ngwụcha egwuregwu", "advancements.end.kill_dragon.description": "<PERSON><PERSON> ike", "advancements.end.kill_dragon.title": "Ha<PERSON><PERSON> Ọgwụgwụ", "advancements.end.levitate.description": "Chekwaa ihe mgbochi 50 site na mwakpo nke Shulker", "advancements.end.levitate.title": "Nnukwu Echiche Site Ebe a", "advancements.end.respawn_dragon.description": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "advancements.end.respawn_dragon.title": "Ọgwụgwụ... Ọzọ...", "advancements.end.root.description": "Ma <PERSON> bụ mmalite?", "advancements.end.root.title": "Njedebe", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "<PERSON>wee ka alee dobe achịcha na ngongo ndetu", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "<PERSON><PERSON><PERSON>", "advancements.husbandry.allay_deliver_item_to_player.description": "<PERSON>wee ka alee wetara gị ihe", "advancements.husbandry.allay_deliver_item_to_player.title": "I nweela enyi n'ime m", "advancements.husbandry.axolotl_in_a_bucket.description": "Jide Axolotl na bọket", "advancements.husbandry.axolotl_in_a_bucket.title": "<PERSON><PERSON> kachasị egbu egbu", "advancements.husbandry.balanced_diet.description": "<PERSON><PERSON> ihe <PERSON> bụla a ga -eri, <PERSON> bụrụgodị na ọ dịghị gị mma", "advancements.husbandry.balanced_diet.title": "<PERSON><PERSON>", "advancements.husbandry.breed_all_animals.description": "Zụlite anụ<PERSON>!", "advancements.husbandry.breed_all_animals.title": "Abụọ na Abụọ", "advancements.husbandry.breed_an_animal.description": "Zụlite anụ<PERSON> abụ<PERSON>", "advancements.husbandry.breed_an_animal.title": "Parrots na ụ<PERSON>ụ", "advancements.husbandry.complete_catalogue.description": "<PERSON>ek<PERSON><PERSON>d<PERSON> Cat <PERSON>!", "advancements.husbandry.complete_catalogue.title": "Ndepụta Ndepụ<PERSON> zuru ezu", "advancements.husbandry.feed_snifflet.description": "Feed a Snifflet", "advancements.husbandry.feed_snifflet.title": "Little Sniffs", "advancements.husbandry.fishy_business.description": "<PERSON>we<PERSON><PERSON><PERSON> a<PERSON>", "advancements.husbandry.fishy_business.title": "Azụ̀ Azụ̀", "advancements.husbandry.froglights.description": "Nwee Froglight niile na ngwa ahịa gị", "advancements.husbandry.froglights.title": "Ejikọtara ike anyị!", "advancements.husbandry.kill_axolotl_target.description": "Jikọ<PERSON> Axolotl wee merie ọgụ", "advancements.husbandry.kill_axolotl_target.title": "Ike Ịgwọ Ọrịa nke <PERSON>!", "advancements.husbandry.leash_all_frog_variants.description": "Get each frog variant on a lead", "advancements.husbandry.leash_all_frog_variants.title": "Mgbe Squad na-abanye n'ime obodo", "advancements.husbandry.make_a_sign_glow.description": "<PERSON><PERSON> ka ederede nke ụdị akara ọ bụla na-egbuke egbuke", "advancements.husbandry.make_a_sign_glow.title": "Na -enwu enwu ma lee!", "advancements.husbandry.netherite_hoe.description": "<PERSON><PERSON> il<PERSON>hara anya k<PERSON>, wee t<PERSON><PERSON><PERSON><PERSON> nh<PERSON>r<PERSON> ndụ gị", "advancements.husbandry.netherite_hoe.title": "<PERSON>aran<PERSON> siri ike", "advancements.husbandry.obtain_sniffer_egg.description": "Obtain a Sniffer Egg", "advancements.husbandry.obtain_sniffer_egg.title": "Smells Interesting", "advancements.husbandry.place_dried_ghast_in_water.description": "Place a Dried Ghast block into water", "advancements.husbandry.place_dried_ghast_in_water.title": "Stay Hydrated!", "advancements.husbandry.plant_any_sniffer_seed.description": "Plant any Sniffer seed", "advancements.husbandry.plant_any_sniffer_seed.title": "Planting the Past", "advancements.husbandry.plant_seed.description": "<PERSON><PERSON><PERSON> mkp<PERSON><PERSON><PERSON> ma hụ ka ọ na -eto", "advancements.husbandry.plant_seed.title": "<PERSON><PERSON>", "advancements.husbandry.remove_wolf_armor.description": "Remove Wolf Armor from a Wolf using Shears", "advancements.husbandry.remove_wolf_armor.title": "Shear Brilliance", "advancements.husbandry.repair_wolf_armor.description": "Fully repair damaged Wolf Armor using Armadillo Scutes", "advancements.husbandry.repair_wolf_armor.title": "Ọma dị ka <PERSON>hụrụ", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Banye n'ụgbọ mmiri ma were ewu na -ese n'elu mmiri", "advancements.husbandry.ride_a_boat_with_a_goat.title": "<PERSON>he <PERSON> bụla na -ese ewu gị!", "advancements.husbandry.root.description": "Ụwa juputara na ndị enyi na nri", "advancements.husbandry.root.title": "Ọrụ di", "advancements.husbandry.safely_harvest_honey.description": "Use a Campfire to collect Honey from a Beehive using a Glass Bottle without aggravating the Bees", "advancements.husbandry.safely_harvest_honey.title": "<PERSON>", "advancements.husbandry.silk_touch_nest.description": "Wegharia akwụ aṅụ, nwere aṅụ atọ n'ime ya, site na iji mmetu silku", "advancements.husbandry.silk_touch_nest.title": "Bee Beeationation", "advancements.husbandry.tactical_fishing.description": "<PERSON><PERSON>... na-enweghị mkpanaka <PERSON>kụ azụ!", "advancements.husbandry.tactical_fishing.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.tadpole_in_a_bucket.description": "<PERSON><PERSON> n'ime bọket", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukit Bukit", "advancements.husbandry.tame_an_animal.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.husbandry.tame_an_animal.title": "<PERSON>yi kacha mma ruo mgbe ebighị ebi", "advancements.husbandry.wax_off.description": "Kpochapu wax site na ngọngọ ọla kọpa!", "advancements.husbandry.wax_off.title": "<PERSON><PERSON><PERSON><PERSON> waksị", "advancements.husbandry.wax_on.description": "Tinye mmanụ a<PERSON>ụ na ngọngọ ọ<PERSON> k<PERSON>!", "advancements.husbandry.wax_on.title": "Tinye na waks", "advancements.husbandry.whole_pack.description": "Tame one of each Wolf variant", "advancements.husbandry.whole_pack.title": "The Whole Pack", "advancements.nether.all_effects.description": "Mee ka etinyere mmetụta ọ bụla n'otu oge", "advancements.nether.all_effects.title": "Kedu ka anyị siri rute ebe a?", "advancements.nether.all_potions.description": "Mee ka mmetụta ọgwụ ọ bụla tinye n'otu oge", "advancements.nether.all_potions.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.nether.brew_potion.description": "Were otù uzọ mee biya", "advancements.nether.brew_potion.title": "O<PERSON>do ebe omume biya", "advancements.nether.charge_respawn_anchor.description": "<PERSON><PERSON><PERSON><PERSON> nkwụghachi <PERSON>gw<PERSON> kacha", "advancements.nether.charge_respawn_anchor.title": "Ọ bụghị “Nd<PERSON> itoolu”", "advancements.nether.create_beacon.description": "Ruputa ma debe mgbaàmà ọkụ", "advancements.nether.create_beacon.title": "Weta mg<PERSON>", "advancements.nether.create_full_beacon.description": "<PERSON>e <PERSON> ọkụ ka o nwee ike zuru ezu", "advancements.nether.create_full_beacon.title": "Mgbama mg<PERSON>ma", "advancements.nether.distract_piglin.description": "Dọpụ uche Piglins na ọla edo", "advancements.nether.distract_piglin.title": "O mgbuke", "advancements.nether.explore_nether.description": "<PERSON><PERSON><PERSON><PERSON> ihe omumu ihe omumu nile nke Netherland", "advancements.nether.explore_nether.title": "<PERSON><PERSON> n<PERSON> anya na -e<PERSON>", "advancements.nether.fast_travel.description": "<PERSON><PERSON> mee njem kilomita asaa na Overworld", "advancements.nether.fast_travel.title": "Subspace Afọ", "advancements.nether.find_bastion.description": "Tinye Ihe Nwepu Bastion", "advancements.nether.find_bastion.title": "<PERSON><PERSON> nd<PERSON> ah<PERSON> b<PERSON>", "advancements.nether.find_fortress.description": "Kwụs<PERSON>z<PERSON> gị n'ime ebe e wusiri ike nke Netherland", "advancements.nether.find_fortress.title": "<PERSON>be e wusiri ike dị egwu", "advancements.nether.get_wither_skull.description": "Nweta okpokoro isi nke Wither Skeleton", "advancements.nether.get_wither_skull.title": "Okpụkpụ na atụ ụjọ", "advancements.nether.loot_bastion.description": "<PERSON><PERSON><PERSON><PERSON> igbe n'ime ihe fọdụ<PERSON>ụ n'ime ala", "advancements.nether.loot_bastion.title": "<PERSON><PERSON>a", "advancements.nether.netherite_armor.description": "<PERSON><PERSON><PERSON> uwe agha Netherite zuru oke", "advancements.nether.netherite_armor.title": "Kpuchie m na mpekere", "advancements.nether.obtain_ancient_debris.description": "<PERSON><PERSON><PERSON> mpekere ochie", "advancements.nether.obtain_ancient_debris.title": "Zoro Ezo na Omimi", "advancements.nether.obtain_blaze_rod.description": "<PERSON><PERSON><PERSON> ka ọkụ nke mkpanaka ya kwụsị", "advancements.nether.obtain_blaze_rod.title": "N'ime Ọkụ", "advancements.nether.obtain_crying_obsidian.description": "Nweta Obsidian na -ebe akwa", "advancements.nether.obtain_crying_obsidian.title": "Kedu onye na -egbutu ya<PERSON>?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON><PERSON> bibie <PERSON>", "advancements.nether.return_to_sender.title": "Laghachi na onye na -ezi ozi", "advancements.nether.ride_strider.description": "Were ahịhịa na -efe efe na mkpisi jiri mkpị", "advancements.nether.ride_strider.title": "Ụgbọ mmiri a nwere ụkwụ", "advancements.nether.ride_strider_in_overworld_lava.description": "Were Strider maka ogologo njem n'elu ọdọ mmiri dị na Overworld", "advancements.nether.ride_strider_in_overworld_lava.title": "<PERSON> <PERSON><PERSON> ka <PERSON>", "advancements.nether.root.description": "<PERSON>a uwe <PERSON>", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON><PERSON>", "advancements.nether.summon_wither.title": "<PERSON><PERSON><PERSON>", "advancements.nether.uneasy_alliance.description": "Napụta Ghast site na Netherland, weta ya n'ụlọ na Overworld n'enweghị nsogbu... wee gbuo ya", "advancements.nether.uneasy_alliance.title": "Njikọ Uneasy", "advancements.nether.use_lodestone.description": "<PERSON><PERSON> were mezie iberibe mag<PERSON>", "advancements.nether.use_lodestone.title": "<PERSON><PERSON><PERSON>, <PERSON> m laa", "advancements.progress": "%s/%s", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "<PERSON><PERSON>wụ wee gwọọ onye Zombie Villager", "advancements.story.cure_zombie_villager.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "advancements.story.deflect_arrow.description": "<PERSON><PERSON> <PERSON><PERSON> proje<PERSON>", "advancements.story.deflect_arrow.title": "Ọ b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "advancements.story.enchant_item.description": "<PERSON>e ihe na tebụl enchanting", "advancements.story.enchant_item.title": "<PERSON><PERSON>ụ<PERSON>", "advancements.story.enter_the_end.description": "Tinye Portal Ọgwụgwụ", "advancements.story.enter_the_end.title": "Njedebe?", "advancements.story.enter_the_nether.description": "<PERSON><PERSON><PERSON>, gbanye ma <PERSON>e <PERSON> Nether", "advancements.story.enter_the_nether.title": "<PERSON><PERSON> k<PERSON>rị ime omimi", "advancements.story.follow_ender_eye.description": "<PERSON><PERSON> n<PERSON>", "advancements.story.follow_ender_eye.title": "<PERSON><PERSON><PERSON>", "advancements.story.form_obsidian.description": "<PERSON><PERSON><PERSON> ngongo nke ihe na-adịghi mma", "advancements.story.form_obsidian.title": "Ịma Aka <PERSON> ịwụ", "advancements.story.iron_tools.description": "Kwalite pickaxe gị", "advancements.story.iron_tools.title": "Ọ b<PERSON>gh<PERSON> ya Iron Pick", "advancements.story.lava_bucket.description": "Were lava kpojuo bukeetị gị", "advancements.story.lava_bucket.title": "<PERSON><PERSON> <PERSON>", "advancements.story.mine_diamond.description": "Nweta diamond", "advancements.story.mine_diamond.title": "Ọla diamond!", "advancements.story.mine_stone.description": "Were pickaxe gị gwute okwute", "advancements.story.mine_stone.title": "Afọ Nkume", "advancements.story.obtain_armor.description": "Chebe onwe gị site na otu ihe agha nke igwe", "advancements.story.obtain_armor.title": "Kwadoro", "advancements.story.root.description": "Obi na akụkọ egwuregwu", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "<PERSON>he agha diamond na -azọpụta ndụ", "advancements.story.shiny_gear.title": "Were ola diamond kpuchie m", "advancements.story.smelt_iron.description": "Gbazee ígwè ingotu", "advancements.story.smelt_iron.title": "<PERSON><PERSON><PERSON>", "advancements.story.upgrade_tools.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> pickaxe ka mma", "advancements.story.upgrade_tools.title": "Inweta nkwalite", "advancements.toast.challenge": "Ihe ngema Aka zuru ezu!", "advancements.toast.goal": "Ihe <PERSON>baru Ọsọ eruwo!", "advancements.toast.task": "Ọganihu Mere!", "argument.anchor.invalid": "Ọnọdụ ngudo otu abaghị uru%s", "argument.angle.incomplete": "<PERSON><PERSON><PERSON><PERSON> oke ( at<PERSON><PERSON><PERSON> anya ofu akụkụ)", "argument.angle.invalid": "Akụkụ e<PERSON>hi ezi", "argument.block.id.invalid": "Ụdị ngongo amaghị ama'%s'", "argument.block.property.duplicate": "Njirimara '%s' nwere ike idozi nanị otu ugboro maka ngongo %s", "argument.block.property.invalid": "Ngongo %s an<PERSON><PERSON> an<PERSON> '%s' maka %s n<PERSON><PERSON><PERSON>", "argument.block.property.novalue": "<PERSON><PERSON> at<PERSON><PERSON><PERSON> anya maka n<PERSON> '%s' na na ngongo%s", "argument.block.property.unclosed": "<PERSON><PERSON><PERSON> at<PERSON><PERSON><PERSON> anya] maka nji<PERSON>ra ọnọ<PERSON><PERSON> ngongo", "argument.block.property.unknown": "Ngongo %s enwegh<PERSON> n<PERSON> '%s'", "argument.block.tag.disallowed": "<PERSON><PERSON><PERSON> an<PERSON> m<PERSON>o e<PERSON>, na<PERSON><PERSON> ngongo n'<PERSON>wa ya", "argument.color.invalid": "Agba amaghị '*%s'", "argument.component.invalid": "<PERSON><PERSON> mkparita <PERSON>ka abaghi uru:%s", "argument.criteria.invalid": "<PERSON>ye amaghi ama '%s'", "argument.dimension.invalid": "Dimension amaghị ama%s", "argument.double.big": "Abụọ agaghị akarịnata %s, afụrụ %s", "argument.double.low": "Abụọ agaghị emekarị %s, afụrụ %s", "argument.entity.invalid": "<PERSON>a abaghi uru ma <PERSON> bụ UUID", "argument.entity.notfound.entity": "<PERSON><PERSON><PERSON><PERSON> a <PERSON>", "argument.entity.notfound.player": "<PERSON><PERSON><PERSON><PERSON> onye eg<PERSON> a hụr<PERSON>", "argument.entity.options.advancements.description": "Ndị na -ere ahịa nwere nkwalite", "argument.entity.options.distance.description": "Ogologo ya na ụlọ ọrụ", "argument.entity.options.distance.negative": "Ịdị anya enweghị ike <PERSON>d<PERSON> na njụ", "argument.entity.options.dx.description": "Ụlọ dị n'etiti x na x + dx", "argument.entity.options.dy.description": "Ụlọ ọrụ dị n'etiti y na y + dy", "argument.entity.options.dz.description": "Ngwa dị n'etiti z na z + dz", "argument.entity.options.gamemode.description": "Players with game mode", "argument.entity.options.inapplicable": "Agaghị etinyenwu nhọrọ'%s' ebea", "argument.entity.options.level.description": "Ọkwa ah<PERSON>m<PERSON>he", "argument.entity.options.level.negative": "<PERSON><PERSON> na njụ", "argument.entity.options.limit.description": "Ọnụ ọgụgụ kacha nke ụlọ ọrụ <PERSON>", "argument.entity.options.limit.toosmall": "Njedebe ga-aburiri opekata mpe otu", "argument.entity.options.mode.invalid": "Ọnọdụ egwuregwu abaghi uru ma ọ bụ nke amaghị ama'%s'", "argument.entity.options.name.description": "<PERSON><PERSON> n<PERSON><PERSON>", "argument.entity.options.nbt.description": "Ụlọ ọrụ nwere NBT", "argument.entity.options.predicate.description": "Omenala nke ahia", "argument.entity.options.scores.description": "Ụlọ ọr<PERSON> nwere akara", "argument.entity.options.sort.description": "<PERSON><PERSON>", "argument.entity.options.sort.irreversible": "Ụdị ina-oke abaghị uru ma ọ bụ nke amaghị ama'%s'", "argument.entity.options.tag.description": "Ngalaba nwere mkpado", "argument.entity.options.team.description": "Ndị otu nọ n'otu", "argument.entity.options.type.description": "Ngalaba nke <PERSON>", "argument.entity.options.type.invalid": "Ụdị otu abaghị uru ma ọ bụ nke amaghị ama'%s'", "argument.entity.options.unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON> amaghị ama'%s'", "argument.entity.options.unterminated": "<PERSON><PERSON><PERSON> at<PERSON><PERSON><PERSON> anya maka nh<PERSON>", "argument.entity.options.valueless": "<PERSON><PERSON> at<PERSON><PERSON><PERSON> anya maka nhọrọ'%s'", "argument.entity.options.x.description": "x <PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.x_rotation.description": "Ntụgharị ihe nke ụlọ ọrụ", "argument.entity.options.y.description": "y <PERSON><PERSON><PERSON><PERSON>", "argument.entity.options.y_rotation.description": "<PERSON><PERSON><PERSON><PERSON> nke y", "argument.entity.options.z.description": "<PERSON> <PERSON><PERSON><PERSON>", "argument.entity.selector.allEntities": "Ụlọ ọr<PERSON> niile", "argument.entity.selector.allPlayers": "<PERSON><PERSON><PERSON> e<PERSON> niile", "argument.entity.selector.missing": "Ụdị ihe nhọrọ na- efu efu", "argument.entity.selector.nearestEntity": "Nearest entity", "argument.entity.selector.nearestPlayer": "Onye ọkpụkpọ kacha nso", "argument.entity.selector.not_allowed": "<PERSON><PERSON><PERSON><PERSON>he <PERSON>h<PERSON>", "argument.entity.selector.randomPlayer": "Onye ọkpụkpọ Random", "argument.entity.selector.self": "<PERSON><PERSON> dị ugbu a", "argument.entity.selector.unknown": "Ụdị ihe nhọrọ amaghị ama'%s'", "argument.entity.toomany": "<PERSON><PERSON><PERSON> ofu otu ka e nyere ikike, mana ihe nhọrọ ewetara nyere ikike inabata karịa ofu otu", "argument.enum.invalid": "Uru na-abaghi uru %s", "argument.float.big": "Ise elu agaghị akarịnata %s, afụrụ %s", "argument.float.low": "Ise elu agaghị akarịnata %s, afụrụ %s", "argument.gamemode.invalid": "Unknown game mode: %s", "argument.hexcolor.invalid": "Invalid hex color code '%s'", "argument.id.invalid": "Njirimara na abaghị uru", "argument.id.unknown": "ID amaghị ama:%s", "argument.integer.big": "Ogbe ọnụọgụ<PERSON>ụ agaghị akarịnata %s, afụrụ %s", "argument.integer.low": "Ogbe ọnụọgụgụ agaghị epekarịnata %s, afụrụ %s", "argument.item.id.invalid": "<PERSON><PERSON> amaghị ama'%s'", "argument.item.tag.disallowed": "Anabataghi mkparị ebe a, naani ihega na onwe ha", "argument.literal.incorrect": "<PERSON><PERSON><PERSON> at<PERSON><PERSON><PERSON> anya %s", "argument.long.big": "Ogologo agaghị akarịnata %s, afụrụ %s", "argument.long.low": "Ogologo agaghị epekarịnata %s, afụrụ%s", "argument.message.too_long": "Chat message was too long (%s > maximum %s characters)", "argument.nbt.array.invalid": "Ụdị array abaghi uru'%s'", "argument.nbt.array.mixed": "Enweghị ike itinye%s n'ime%s", "argument.nbt.expected.compound": "Expected compound tag", "argument.nbt.expected.key": "<PERSON><PERSON><PERSON> at<PERSON><PERSON><PERSON>a", "argument.nbt.expected.value": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "argument.nbt.list.mixed": "Enweghị ike itinye%s n'ime ihe nguputa*%s", "argument.nbt.trailing": "Data nsosnso <PERSON> anya", "argument.player.entities": "<PERSON><PERSON><PERSON> ndị egwuregwu ka iwu a nwere ike <PERSON>, mana ihe nhọrọ ewetara ritere otu dị iche iche", "argument.player.toomany": "<PERSON><PERSON><PERSON> otu onye egwuruegwu ka enyere ikike, mana ihe nhọrọ ewetara nyere ikike maka ihe karịrị ofu otu", "argument.player.unknown": "<PERSON><PERSON> e<PERSON> ad<PERSON>", "argument.pos.missing.double": "<PERSON><PERSON><PERSON><PERSON> anya o<PERSON> ah<PERSON>", "argument.pos.missing.int": "At<PERSON><PERSON><PERSON> anya otu <PERSON> ngongo", "argument.pos.mixed": "<PERSON><PERSON><PERSON><PERSON> agwakotanwu ahịrị <PERSON>wa na nke ndinime (ihe nile ga-e<PERSON><PERSON>ri ma <PERSON> ^ ọ bụrụ mba)", "argument.pos.outofbounds": "Ọnọdụ ahụ dị n'è<PERSON>í <PERSON> e<PERSON>.", "argument.pos.outofworld": "Ọkwa ahụ adịghị adị n'ụwa a!", "argument.pos.unloaded": "Ekponyeghi ihe n'ọkwa ahu", "argument.pos2d.incomplete": "<PERSON><PERSON><PERSON><PERSON><PERSON>zu (nchịkọta 2 atụ<PERSON><PERSON> anya ya)", "argument.pos3d.incomplete": "<PERSON><PERSON><PERSON><PERSON> oke ( at<PERSON><PERSON><PERSON> anya ah<PERSON> atọ)", "argument.range.empty": "<PERSON><PERSON> at<PERSON>r<PERSON> anya ma <PERSON> bụ oke nke uru", "argument.range.ints": "<PERSON><PERSON><PERSON> ogbe ọn<PERSON>ọgụgụ ka a chọrọ, ach<PERSON><PERSON><PERSON> nkèji irí", "argument.range.swapped": "Opekata mpe agagh<PERSON> akarinwu obukata ibu", "argument.resource.invalid_type": "Element '%s' has wrong type '%s' (expected '%s')", "argument.resource.not_found": "Can't find element '%s' of type '%s'", "argument.resource_or_id.failed_to_parse": "Failed to parse structure: %s", "argument.resource_or_id.invalid": "Invalid id or tag", "argument.resource_or_id.no_such_element": "Can't find element '%s' in registry '%s'", "argument.resource_selector.not_found": "No matches for selector '%s' of type '%s'", "argument.resource_tag.invalid_type": "Mkpado '%s' nwere ụdị '%s' na-e<PERSON>hi ezi (a tụrụ anya '%s')", "argument.resource_tag.not_found": "Can't find tag '%s' of type '%s'", "argument.rotation.incomplete": "<PERSON><PERSON><PERSON><PERSON> oke ( atụr<PERSON> anya ahiri abụ<PERSON>)", "argument.scoreHolder.empty": "<PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON><PERSON><PERSON> dị mkpa ahụr<PERSON>", "argument.scoreboardDisplaySlot.invalid": "<PERSON><PERSON>ere ngosi amaghị ama'%s'", "argument.style.invalid": "Ụdị ezighi ezi: %s", "argument.time.invalid_tick_count": "Ọnụọgụgụ ákàrà ga-aburiri nju", "argument.time.invalid_unit": "Ot<PERSON> n'abaghi uru", "argument.time.tick_count_too_low": "The tick count must not be less than %s, found %s", "argument.uuid.invalid": "UUID na ezighi ezi", "argument.waypoint.invalid": "Selected entity is not a waypoint", "arguments.block.tag.unknown": "<PERSON><PERSON> mkpado ngongo amaghị ama '%s'", "arguments.function.tag.unknown": "Mkpado <PERSON><PERSON> amaghị ama'%s'", "arguments.function.unknown": "Ọrụ amaghị%s", "arguments.item.component.expected": "Expected item component", "arguments.item.component.malformed": "Malformed '%s' component: '%s'", "arguments.item.component.repeated": "Item component '%s' was repeated, but only one value can be specified", "arguments.item.component.unknown": "Unknown item component '%s'", "arguments.item.malformed": "Malformed item: '%s'", "arguments.item.overstacked": "%s naanị nwere ike ịchikota ọnụ ruo '%s", "arguments.item.predicate.malformed": "Malformed '%s' predicate: '%s'", "arguments.item.predicate.unknown": "Unknown item predicate '%s'", "arguments.item.tag.unknown": "<PERSON>he mkpado amaghị ama'%s'", "arguments.nbtpath.node.invalid": "Mmewere ụzọ NBT na abaghị uru", "arguments.nbtpath.nothing_found": "Onwere mmewere ahụrụ dabara na %s", "arguments.nbtpath.too_deep": "Resulting NBT too deeply nested", "arguments.nbtpath.too_large": "Resulting NBT too large", "arguments.objective.notFound": "<PERSON><PERSON> nh<PERSON>rọ mgbamputara amaghị ama '%s'", "arguments.objective.readonly": "<PERSON><PERSON> <PERSON>h<PERSON>rọ mgbamputara'%s' bụ naan<PERSON>", "arguments.operation.div0": "<PERSON><PERSON><PERSON><PERSON> ejinwu efu were kee ya", "arguments.operation.invalid": "Ọrụ na ezighi ezi", "arguments.swizzle.invalid": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> uru, ng<PERSON><PERSON><PERSON> at<PERSON>rụ anya maka 'x', 'Y' and 'z'", "attribute.modifier.equals.0": "%s %s", "attribute.modifier.equals.1": "%s%% %s", "attribute.modifier.equals.2": "%s%% %s", "attribute.modifier.plus.0": "+%s %s", "attribute.modifier.plus.1": "+%s%% %s", "attribute.modifier.plus.2": "+%s%% %s", "attribute.modifier.take.0": "-%s %s", "attribute.modifier.take.1": "-%s%% %s", "attribute.modifier.take.2": "-%s%% %s", "attribute.name.armor": "<PERSON><PERSON> nchebe", "attribute.name.armor_toughness": "<PERSON><PERSON>", "attribute.name.attack_damage": "Attack Damage", "attribute.name.attack_knockback": "Attack Knockback", "attribute.name.attack_speed": "Attack Speed", "attribute.name.block_break_speed": "Block Break Speed", "attribute.name.block_interaction_range": "Block Interaction Range", "attribute.name.burning_time": "Burning Time", "attribute.name.camera_distance": "Camera Distance", "attribute.name.entity_interaction_range": "Entity Interaction Range", "attribute.name.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.flying_speed": "Ọsọ na-efe efe", "attribute.name.follow_range": "<PERSON>b <PERSON> Range", "attribute.name.generic.armor": "Ekike agha", "attribute.name.generic.armor_toughness": "<PERSON><PERSON>", "attribute.name.generic.attack_damage": "M<PERSON>bi <PERSON>", "attribute.name.generic.attack_knockback": "<PERSON><PERSON><PERSON><PERSON>", "attribute.name.generic.attack_speed": "Mwakpo Ọsọ", "attribute.name.generic.block_interaction_range": "Block Interaction Range", "attribute.name.generic.burning_time": "Burning Time", "attribute.name.generic.entity_interaction_range": "Entity Interaction Range", "attribute.name.generic.explosion_knockback_resistance": "Explosion Knockback Resistance", "attribute.name.generic.fall_damage_multiplier": "Fall Damage Multiplier", "attribute.name.generic.flying_speed": "Speedgba ọsọ", "attribute.name.generic.follow_range": "Ìgwè Nd<PERSON> Na-eso <PERSON>", "attribute.name.generic.gravity": "Gravity", "attribute.name.generic.jump_strength": "Jump Strength", "attribute.name.generic.knockback_resistance": "<PERSON>sog<PERSON>", "attribute.name.generic.luck": "Luck", "attribute.name.generic.max_absorption": "Max Absorption", "attribute.name.generic.max_health": "<PERSON><PERSON><PERSON>", "attribute.name.generic.movement_efficiency": "Movement Efficiency", "attribute.name.generic.movement_speed": "Ọsọ", "attribute.name.generic.oxygen_bonus": "Oxygen Bonus", "attribute.name.generic.safe_fall_distance": "Safe Fall Distance", "attribute.name.generic.scale": "Scale", "attribute.name.generic.step_height": "Step Height", "attribute.name.generic.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.gravity": "Gravity", "attribute.name.horse.jump_strength": "<PERSON><PERSON>in<PERSON>", "attribute.name.jump_strength": "Jump Strength", "attribute.name.knockback_resistance": "Knockback Resistance", "attribute.name.luck": "Luck", "attribute.name.max_absorption": "Max Absorption", "attribute.name.max_health": "Max Health", "attribute.name.mining_efficiency": "Mining Efficiency", "attribute.name.movement_efficiency": "Movement Efficiency", "attribute.name.movement_speed": "Ọsọ", "attribute.name.oxygen_bonus": "Oxygen Bonus", "attribute.name.player.block_break_speed": "Block Break Speed", "attribute.name.player.block_interaction_range": "Block Interaction Range", "attribute.name.player.entity_interaction_range": "Entity Interaction Range", "attribute.name.player.mining_efficiency": "Mining Efficiency", "attribute.name.player.sneaking_speed": "Sneaking Speed", "attribute.name.player.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.player.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.safe_fall_distance": "Safe Fall Distance", "attribute.name.scale": "Scale", "attribute.name.sneaking_speed": "Sneaking Speed", "attribute.name.spawn_reinforcements": "Zombie Reinforcements", "attribute.name.step_height": "Step Height", "attribute.name.submerged_mining_speed": "Submerged Mining Speed", "attribute.name.sweeping_damage_ratio": "Sweeping Damage Ratio", "attribute.name.tempt_range": "Mob Tempt Range", "attribute.name.water_movement_efficiency": "Water Movement Efficiency", "attribute.name.waypoint_receive_range": "Waypoint Receive Range", "attribute.name.waypoint_transmit_range": "Waypoint Transmit Range", "attribute.name.zombie.spawn_reinforcements": "M<PERSON>zi Zombie", "biome.minecraft.badlands": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.bamboo_jungle": "<PERSON><PERSON><PERSON>", "biome.minecraft.basalt_deltas": "Delta ndị basalt", "biome.minecraft.beach": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.birch_forest": "Ọhịa birch", "biome.minecraft.cherry_grove": "Cherry Grove", "biome.minecraft.cold_ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.crimson_forest": "Crimson igbo", "biome.minecraft.dark_forest": "Ọhịa gbara <PERSON>", "biome.minecraft.deep_cold_ocean": "<PERSON><PERSON><PERSON><PERSON> miri emi juru oyi", "biome.minecraft.deep_dark": "<PERSON>i emi <PERSON>", "biome.minecraft.deep_frozen_ocean": "Osimiri mmiri emi kpukoro akpụkọ", "biome.minecraft.deep_lukewarm_ocean": "Osimiri miri emi dị ñari ñari", "biome.minecraft.deep_ocean": "<PERSON><PERSON><PERSON><PERSON> miri emi", "biome.minecraft.desert": "Ọzara", "biome.minecraft.dripstone_caves": "Ọgba Dripstone", "biome.minecraft.end_barrens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.end_highlands": "Kwụsị alaa dị elu", "biome.minecraft.end_midlands": "Kwụsị ala etiti", "biome.minecraft.eroded_badlands": "<PERSON><PERSON> mbuze megoro", "biome.minecraft.flower_forest": "Ọhịa okooko", "biome.minecraft.forest": "Oh<PERSON>", "biome.minecraft.frozen_ocean": "Oshimiri kpukoro akpụkọ", "biome.minecraft.frozen_peaks": "Ọnụ kacha kpọnw<PERSON>rụ akpọnwụ", "biome.minecraft.frozen_river": "Mmírí <PERSON> a<PERSON>pụk<PERSON>", "biome.minecraft.grove": "Grove", "biome.minecraft.ice_spikes": "Akpuru spaiks", "biome.minecraft.jagged_peaks": "Ọnụ kacha elu", "biome.minecraft.jungle": "<PERSON><PERSON>", "biome.minecraft.lukewarm_ocean": "Oshimiri dị ñari ñari", "biome.minecraft.lush_caves": "Lush Caves", "biome.minecraft.mangrove_swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON> mangroove", "biome.minecraft.meadow": "<PERSON><PERSON>", "biome.minecraft.mushroom_fields": "<PERSON><PERSON> ero", "biome.minecraft.nether_wastes": "<PERSON><PERSON> W ụtọ", "biome.minecraft.ocean": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.old_growth_birch_forest": "<PERSON><PERSON> O<PERSON>", "biome.minecraft.old_growth_pine_taiga": "Ochie Uto Pine Taiga", "biome.minecraft.old_growth_spruce_taiga": "Ochie Uto Spruce Taiga", "biome.minecraft.pale_garden": "Pale Garden", "biome.minecraft.plains": "<PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.river": "Ọdọ mmiri", "biome.minecraft.savanna": "<PERSON><PERSON> <PERSON> ndu", "biome.minecraft.savanna_plateau": "<PERSON><PERSON>", "biome.minecraft.small_end_islands": "Agwaetiti nwere obere ngwucha", "biome.minecraft.snowy_beach": "Osimiri kpukoro akpụkọ", "biome.minecraft.snowy_plains": "<PERSON><PERSON> oyi na-aw<PERSON> oyi", "biome.minecraft.snowy_slopes": "<PERSON><PERSON><PERSON><PERSON> snow", "biome.minecraft.snowy_taiga": "Taiga kpukoro akpụkọ", "biome.minecraft.soul_sand_valley": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.sparse_jungle": "<PERSON><PERSON>a pere mpe", "biome.minecraft.stony_peaks": "<PERSON><PERSON> nkume", "biome.minecraft.stony_shore": "Stone Shore", "biome.minecraft.sunflower_plains": "<PERSON><PERSON><PERSON><PERSON> ok<PERSON>", "biome.minecraft.swamp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "biome.minecraft.taiga": "Taiga", "biome.minecraft.the_end": "The End", "biome.minecraft.the_void": "The Void", "biome.minecraft.warm_ocean": "Osi<PERSON>i dị ñari ñari", "biome.minecraft.warped_forest": "Oke ohia", "biome.minecraft.windswept_forest": "<PERSON>e ọ<PERSON>ịa na-efe efe", "biome.minecraft.windswept_gravelly_hills": "Windwept Gravelly Hills", "biome.minecraft.windswept_hills": "<PERSON>gwu Windswept", "biome.minecraft.windswept_savanna": "Ikuku Savanna", "biome.minecraft.wooded_badlands": "Badlands osisi", "block.minecraft.acacia_button": "Acacia <PERSON>", "block.minecraft.acacia_door": "Acacia <PERSON>", "block.minecraft.acacia_fence": "Acacia ngere", "block.minecraft.acacia_fence_gate": "Ọnụ ụzọ ámá Acacia", "block.minecraft.acacia_hanging_sign": "Acacia Hanging Sign", "block.minecraft.acacia_leaves": "Acacia doo", "block.minecraft.acacia_log": "Abanye na Acacia", "block.minecraft.acacia_planks": "Acacia <PERSON>", "block.minecraft.acacia_pressure_plate": "Plate Strike Acacia", "block.minecraft.acacia_sapling": "Acacia Sapling", "block.minecraft.acacia_sign": "Ama Acacia", "block.minecraft.acacia_slab": "Acacia <PERSON>b", "block.minecraft.acacia_stairs": "Acacia <PERSON>", "block.minecraft.acacia_trapdoor": "Acacia <PERSON>ia", "block.minecraft.acacia_wall_hanging_sign": "Acacia Wall Hanging Sign", "block.minecraft.acacia_wall_sign": "Ihe acacia acacia", "block.minecraft.acacia_wood": "Osisi Acacia", "block.minecraft.activator_rail": "Atorzọ Njikwa", "block.minecraft.air": "<PERSON><PERSON><PERSON>", "block.minecraft.allium": "Allium", "block.minecraft.amethyst_block": "<PERSON>he mgbochi nke amethyst", "block.minecraft.amethyst_cluster": "Ụyọkọ Amethyst", "block.minecraft.ancient_debris": "Nkpọr<PERSON> o<PERSON>e", "block.minecraft.andesite": "Andesite", "block.minecraft.andesite_slab": "Okpokoro Andesite", "block.minecraft.andesite_stairs": "Steepụ Andesite", "block.minecraft.andesite_wall": "Mgbidi Andesite", "block.minecraft.anvil": "An<PERSON>", "block.minecraft.attached_melon_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.attached_pumpkin_stem": "Akpokọta Ugu", "block.minecraft.azalea": "Azalea", "block.minecraft.azalea_leaves": "Akwụkwọ Azalea", "block.minecraft.azure_bluet": "Azure Bluet", "block.minecraft.bamboo": "<PERSON><PERSON><PERSON>", "block.minecraft.bamboo_block": "Ngọngọ nke achara", "block.minecraft.bamboo_button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.bamboo_door": "Ambgba égbè", "block.minecraft.bamboo_fence": "<PERSON><PERSON>", "block.minecraft.bamboo_fence_gate": "Ọnụ <PERSON>z<PERSON> ngere achara", "block.minecraft.bamboo_hanging_sign": "Bamboo Hanging Sign", "block.minecraft.bamboo_mosaic": "Bamboo Mosaic", "block.minecraft.bamboo_mosaic_slab": "Bamboo Mosaic Slab", "block.minecraft.bamboo_mosaic_stairs": "Bamboo Mosaic Stairs", "block.minecraft.bamboo_planks": "Bamboo Planks", "block.minecraft.bamboo_pressure_plate": "Bamboo Pressure Plate", "block.minecraft.bamboo_sapling": "Ambgba égbè", "block.minecraft.bamboo_sign": "Bamboo Sign", "block.minecraft.bamboo_slab": "Bamboo Slab", "block.minecraft.bamboo_stairs": "Bamboo Stairs", "block.minecraft.bamboo_trapdoor": "Bamboo Trapdoor", "block.minecraft.bamboo_wall_hanging_sign": "Bamboo Wall Hanging Sign", "block.minecraft.bamboo_wall_sign": "Bamboo Wall Sign", "block.minecraft.banner.base.black": "<PERSON><PERSON> ojii zuru oke", "block.minecraft.banner.base.blue": "<PERSON><PERSON> bluu zuru oke", "block.minecraft.banner.base.brown": "<PERSON><PERSON> brawu zuru oke", "block.minecraft.banner.base.cyan": "<PERSON><PERSON> cian zuru oke", "block.minecraft.banner.base.gray": "<PERSON><PERSON> zuru oke", "block.minecraft.banner.base.green": "<PERSON><PERSON> griin zuru oke", "block.minecraft.banner.base.light_blue": "<PERSON><PERSON> acha anụ<PERSON> anụnụ nke <PERSON>", "block.minecraft.banner.base.light_gray": "Ubi ọkụ <PERSON> zuru oke", "block.minecraft.banner.base.lime": "Ubi Lime zuru oke", "block.minecraft.banner.base.magenta": "<PERSON>bi magenta nke <PERSON>ma", "block.minecraft.banner.base.orange": "<PERSON><PERSON> epe nke <PERSON>", "block.minecraft.banner.base.pink": "<PERSON>bi Pink zuru oke", "block.minecraft.banner.base.purple": "Ubi populu zuru oke", "block.minecraft.banner.base.red": "<PERSON>bi Uh<PERSON> zuru ezu", "block.minecraft.banner.base.white": "<PERSON><PERSON> n<PERSON>", "block.minecraft.banner.base.yellow": "Ubi edo edo zuru oke", "block.minecraft.banner.border.black": "<PERSON><PERSON><PERSON><PERSON><PERSON> ojii", "block.minecraft.banner.border.blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON><PERSON> an<PERSON>n<PERSON>", "block.minecraft.banner.border.brown": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.banner.border.cyan": "<PERSON><PERSON>-ala ntụ ntụ", "block.minecraft.banner.border.gray": "<PERSON><PERSON>-ala ntụ ntụ", "block.minecraft.banner.border.green": "Oke-ala akwụkwọ ndụ akwụkwọ ndụ", "block.minecraft.banner.border.light_blue": "Oke-ala anụn<PERSON> anụn<PERSON>", "block.minecraft.banner.border.light_gray": "<PERSON>e-ala ntụ ntụ <PERSON>è", "block.minecraft.banner.border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> or<PERSON>", "block.minecraft.banner.border.magenta": "<PERSON><PERSON><PERSON><PERSON>a uh<PERSON>o", "block.minecraft.banner.border.orange": "<PERSON><PERSON><PERSON><PERSON><PERSON> uh<PERSON>o", "block.minecraft.banner.border.pink": "<PERSON>e-ala uhie ocha", "block.minecraft.banner.border.purple": "Oke-ala odo odo", "block.minecraft.banner.border.red": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.border.white": "<PERSON><PERSON>-ala ocha", "block.minecraft.banner.border.yellow": "Oke-ala edo edo", "block.minecraft.banner.bricks.black": "Etechiela <PERSON> o<PERSON>", "block.minecraft.banner.bricks.blue": "Etechiela ubi an<PERSON> an<PERSON>", "block.minecraft.banner.bricks.brown": "Etechiela ubi aja aja", "block.minecraft.banner.bricks.cyan": "Etechiela ubi ntụ ntụ", "block.minecraft.banner.bricks.gray": "Etechiela ubi ntụ ntụ", "block.minecraft.banner.bricks.green": "Etechiela ubi akwụkwọ ndụ akwụkwọ ndụ", "block.minecraft.banner.bricks.light_blue": "Etechiela ubi anụ<PERSON> anụn<PERSON>", "block.minecraft.banner.bricks.light_gray": "Etechiela ubi ntụ ntụ <PERSON>è", "block.minecraft.banner.bricks.lime": "Etech<PERSON><PERSON> ubi or<PERSON> n<PERSON>", "block.minecraft.banner.bricks.magenta": "Etechiela ubi uh<PERSON>odo", "block.minecraft.banner.bricks.orange": "Etechiela ubi <PERSON>o", "block.minecraft.banner.bricks.pink": "Etechiela ubi uh<PERSON>", "block.minecraft.banner.bricks.purple": "Etechiela ubi odo odo", "block.minecraft.banner.bricks.red": "Etechiela ubi <PERSON>", "block.minecraft.banner.bricks.white": "Etechiela ubi <PERSON>", "block.minecraft.banner.bricks.yellow": "Etechiela ubi edo edo", "block.minecraft.banner.circle.black": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.blue": "Blue Roundel", "block.minecraft.banner.circle.brown": "<PERSON>", "block.minecraft.banner.circle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.circle.gray": "<PERSON> Roundel", "block.minecraft.banner.circle.green": "block.minecraft.banner.circle.green", "block.minecraft.banner.circle.light_blue": "Ìhè Bulu Roundel", "block.minecraft.banner.circle.light_gray": "<PERSON> <PERSON>", "block.minecraft.banner.circle.lime": "<PERSON><PERSON>", "block.minecraft.banner.circle.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.circle.orange": "<PERSON><PERSON>", "block.minecraft.banner.circle.pink": "Pink Roundel", "block.minecraft.banner.circle.purple": "Purple Roundel", "block.minecraft.banner.circle.red": "<PERSON>el uhie", "block.minecraft.banner.circle.white": "Ọcha Roundel", "block.minecraft.banner.circle.yellow": "Yellow Roundel", "block.minecraft.banner.creeper.black": "<PERSON><PERSON><PERSON> elu ojii", "block.minecraft.banner.creeper.blue": "<PERSON><PERSON><PERSON> anụnụ anụnụ na-akpụ akpụ", "block.minecraft.banner.creeper.brown": "Chaajị aja aja na-akpụ akpụ", "block.minecraft.banner.creeper.cyan": "Chaajị ntụ ntụ na-akpụ akpụ", "block.minecraft.banner.creeper.gray": "Ụgwọ creeper isi awọ", "block.minecraft.banner.creeper.green": "<PERSON><PERSON>ị akwụkwọ ndụ akwụkwọ ndụ na-akpụ akpụ", "block.minecraft.banner.creeper.light_blue": "<PERSON><PERSON><PERSON> anụnụ anụnụ ìhè na-akpụ akpụ", "block.minecraft.banner.creeper.light_gray": "<PERSON><PERSON> elu isi awọ", "block.minecraft.banner.creeper.lime": "Chaajị oroma n<PERSON>i na-akpụ akpụ", "block.minecraft.banner.creeper.magenta": "<PERSON><PERSON><PERSON> uh<PERSON>odo na-akpụ akpụ", "block.minecraft.banner.creeper.orange": "<PERSON><PERSON><PERSON> uhiedo na-akpụ akpụ", "block.minecraft.banner.creeper.pink": "<PERSON><PERSON><PERSON> elu pink", "block.minecraft.banner.creeper.purple": "<PERSON><PERSON><PERSON> odo odo na-akpụ akpụ", "block.minecraft.banner.creeper.red": "<PERSON><PERSON><PERSON> na- akpụ akpụ", "block.minecraft.banner.creeper.white": "<PERSON><PERSON><PERSON>cha na-akpụ akpụ", "block.minecraft.banner.creeper.yellow": "Chaajị ede edo na-akpụ akpụ", "block.minecraft.banner.cross.black": "<PERSON><PERSON>", "block.minecraft.banner.cross.blue": "B<PERSON> Nnu", "block.minecraft.banner.cross.brown": "<PERSON><PERSON><PERSON> a<PERSON>", "block.minecraft.banner.cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.cross.gray": "<PERSON>", "block.minecraft.banner.cross.green": "Agba akwụkwọ ndụ <PERSON>", "block.minecraft.banner.cross.light_blue": "<PERSON><PERSON>", "block.minecraft.banner.cross.light_gray": "Ìhè Agba ntụ <PERSON>nu", "block.minecraft.banner.cross.lime": "<PERSON><PERSON>", "block.minecraft.banner.cross.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.orange": "<PERSON><PERSON>", "block.minecraft.banner.cross.pink": "<PERSON>", "block.minecraft.banner.cross.purple": "P<PERSON><PERSON><PERSON><PERSON> Salt", "block.minecraft.banner.cross.red": "<PERSON><PERSON>", "block.minecraft.banner.cross.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.cross.yellow": "Edo edo edo", "block.minecraft.banner.curly_border.black": "<PERSON><PERSON><PERSON><PERSON>a ojii a<PERSON> uru", "block.minecraft.banner.curly_border.blue": "<PERSON>e-<PERSON>a an<PERSON>n<PERSON> anụnụ abaghị uru", "block.minecraft.banner.curly_border.brown": "<PERSON><PERSON>-<PERSON><PERSON> <PERSON><PERSON><PERSON> a<PERSON> uru", "block.minecraft.banner.curly_border.cyan": "<PERSON>e-ala ntụ ntụ abaghị uru", "block.minecraft.banner.curly_border.gray": "<PERSON>e-ala ntụ ntụ abaghị uru", "block.minecraft.banner.curly_border.green": "Oke-<PERSON>a akwụkwọ ndụ akwụkwọ ndụ abaghị uru", "block.minecraft.banner.curly_border.light_blue": "Oke-ala anụn<PERSON> anụnụ ìhè abaghị uru", "block.minecraft.banner.curly_border.light_gray": "Oke-ala ntụ ntụ ìhè abaghị uru", "block.minecraft.banner.curly_border.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON> or<PERSON> n<PERSON><PERSON><PERSON> abaghị uru", "block.minecraft.banner.curly_border.magenta": "<PERSON>e-ala uh<PERSON><PERSON>o a<PERSON> uru", "block.minecraft.banner.curly_border.orange": "<PERSON><PERSON>-<PERSON>a uh<PERSON>o a<PERSON> uru", "block.minecraft.banner.curly_border.pink": "<PERSON><PERSON>-<PERSON>a <PERSON><PERSON> a<PERSON> uru", "block.minecraft.banner.curly_border.purple": "Oke-ala odo odo abag<PERSON> uru", "block.minecraft.banner.curly_border.red": "<PERSON>e-<PERSON>a <PERSON><PERSON> a<PERSON> uru", "block.minecraft.banner.curly_border.white": "<PERSON><PERSON> a<PERSON> uru", "block.minecraft.banner.curly_border.yellow": "Oke-ala edo edo abaghị uru", "block.minecraft.banner.diagonal_left.black": "Black Per Bend Sinister", "block.minecraft.banner.diagonal_left.blue": "Blue Per Bend Sinister", "block.minecraft.banner.diagonal_left.brown": "<PERSON> Sinister", "block.minecraft.banner.diagonal_left.cyan": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_left.gray": "Grey Per Bend Sinister", "block.minecraft.banner.diagonal_left.green": "Green Per Bend Sinister", "block.minecraft.banner.diagonal_left.light_blue": "Ìhè Blue Kwa Bend Sinister", "block.minecraft.banner.diagonal_left.light_gray": "Ìhè Grey Per Bend Sinister", "block.minecraft.banner.diagonal_left.lime": "Lime kwa Bend Sinister", "block.minecraft.banner.diagonal_left.magenta": "Ma<PERSON><PERSON> Per <PERSON>", "block.minecraft.banner.diagonal_left.orange": "Orange Kwa Bend Sinister", "block.minecraft.banner.diagonal_left.pink": "Pink Per Bend Sinister", "block.minecraft.banner.diagonal_left.purple": "Purple Per Bend Sinister", "block.minecraft.banner.diagonal_left.red": "Red Per Bend Sinister", "block.minecraft.banner.diagonal_left.white": "White Per Bend Sinister", "block.minecraft.banner.diagonal_left.yellow": "Yellow Kwa Bend Sinister", "block.minecraft.banner.diagonal_right.black": "Black kwa band", "block.minecraft.banner.diagonal_right.blue": "Blue kwa band", "block.minecraft.banner.diagonal_right.brown": "Brown kwa band", "block.minecraft.banner.diagonal_right.cyan": "<PERSON><PERSON>", "block.minecraft.banner.diagonal_right.gray": "Awo kwa Bend", "block.minecraft.banner.diagonal_right.green": "Green kwa band", "block.minecraft.banner.diagonal_right.light_blue": "Oroma nkịrịsị Kwa Bend", "block.minecraft.banner.diagonal_right.light_gray": "Ìhè isi awọ kwa Bend", "block.minecraft.banner.diagonal_right.lime": "Oroma nkịrịsị Kwa Bend", "block.minecraft.banner.diagonal_right.magenta": "Magenta Kwa Bend", "block.minecraft.banner.diagonal_right.orange": "Oroma Kwa Bend", "block.minecraft.banner.diagonal_right.pink": "Pink Kwa Bend", "block.minecraft.banner.diagonal_right.purple": "Purple Kwa Bend", "block.minecraft.banner.diagonal_right.red": "Uhie kwa band", "block.minecraft.banner.diagonal_right.white": "Ọcha Kwa Bend", "block.minecraft.banner.diagonal_right.yellow": "Yelloe Kwa Bend", "block.minecraft.banner.diagonal_up_left.black": "Black Kwa Bend gbanwere", "block.minecraft.banner.diagonal_up_left.blue": "Blue Per Bend gbanwere", "block.minecraft.banner.diagonal_up_left.brown": "Agba agba agba agba aja aja agbajiri agbaji", "block.minecraft.banner.diagonal_up_left.cyan": "<PERSON><PERSON> na-agbanwe", "block.minecraft.banner.diagonal_up_left.gray": "Agba Grey gbagọrọ agbagọ", "block.minecraft.banner.diagonal_up_left.green": "Green Kwa Bend gbanwere", "block.minecraft.banner.diagonal_up_left.light_blue": "Ìhè Blue kwa Bend gbanwere", "block.minecraft.banner.diagonal_up_left.light_gray": "Ìhè Agba Agba Per Bendver", "block.minecraft.banner.diagonal_up_left.lime": "Wayo Kwa Bend gbanwere", "block.minecraft.banner.diagonal_up_left.magenta": "Magenta Kwa gbagọrọ agbagọ", "block.minecraft.banner.diagonal_up_left.orange": "Oroma Kwa Bend", "block.minecraft.banner.diagonal_up_left.pink": "Pink Kwa Bend gbanwere", "block.minecraft.banner.diagonal_up_left.purple": "A gbanwere Purple Per Bend", "block.minecraft.banner.diagonal_up_left.red": "Red Per Bend gbanwere", "block.minecraft.banner.diagonal_up_left.white": "White Kwa Bend gbanwere", "block.minecraft.banner.diagonal_up_left.yellow": "Yellow Nke Bend gbanwere", "block.minecraft.banner.diagonal_up_right.black": "Black Kwa Bend Sinister Agbanwe Agbanwe", "block.minecraft.banner.diagonal_up_right.blue": "> block.minecraft.banner.diagonal_up_right.blue", "block.minecraft.banner.diagonal_up_right.brown": "\nblock.minecraft.banner.diagonal_up_right.brown", "block.minecraft.banner.diagonal_up_right.cyan": "block.minecraft.banner.diagonal_up_right.cyan", "block.minecraft.banner.diagonal_up_right.gray": "block.minecraft.banner.diagonal_up_right.gray", "block.minecraft.banner.diagonal_up_right.green": "Akwụkwọ ndụ Kwa Bend Agbagharala mmehie", "block.minecraft.banner.diagonal_up_right.light_blue": "block.minecraft.banner.diagonal_up_right.light_blue", "block.minecraft.banner.diagonal_up_right.light_gray": "block.minecraft.banner.diagonal_up_right.light_gray", "block.minecraft.banner.diagonal_up_right.lime": "block.minecraft.banner.diagonal_up_right.lime", "block.minecraft.banner.diagonal_up_right.magenta": "block.minecraft.banner.diagonal_up_right.magenta", "block.minecraft.banner.diagonal_up_right.orange": "block.minecraft.banner.diagonal_up_right.orange", "block.minecraft.banner.diagonal_up_right.pink": "block.minecraft.banner.diagonal_up_right.pink", "block.minecraft.banner.diagonal_up_right.purple": "\n block.minecraft.banner.diagonal_up_right.purple", "block.minecraft.banner.diagonal_up_right.red": "Uhie Kwa Bend Agbagharala mmehie", "block.minecraft.banner.diagonal_up_right.white": "block.minecraft.banner.diagonal_up_right.white", "block.minecraft.banner.diagonal_up_right.yellow": " block.minecraft.banner.diagonal_up_right.yellow", "block.minecraft.banner.flow.black": "Black Flow", "block.minecraft.banner.flow.blue": "Blue Flow", "block.minecraft.banner.flow.brown": "Brown Flow", "block.minecraft.banner.flow.cyan": "<PERSON><PERSON>", "block.minecraft.banner.flow.gray": "Gray Flow", "block.minecraft.banner.flow.green": "Green Flow", "block.minecraft.banner.flow.light_blue": "Light Blue Flow", "block.minecraft.banner.flow.light_gray": "Light Gray Flow", "block.minecraft.banner.flow.lime": "Lime Flow", "block.minecraft.banner.flow.magenta": "Magenta Flow", "block.minecraft.banner.flow.orange": "Orange Flow", "block.minecraft.banner.flow.pink": "Pink Flow", "block.minecraft.banner.flow.purple": "Purple Flow", "block.minecraft.banner.flow.red": "Red Flow", "block.minecraft.banner.flow.white": "White Flow", "block.minecraft.banner.flow.yellow": "Yellow Flow", "block.minecraft.banner.flower.black": "Oji Ifuru Chaji", "block.minecraft.banner.flower.blue": "<PERSON><PERSON><PERSON> ok<PERSON> an<PERSON> anụn<PERSON>", "block.minecraft.banner.flower.brown": "<PERSON><PERSON><PERSON> okooko aja aja", "block.minecraft.banner.flower.cyan": "<PERSON><PERSON><PERSON> okooko ntụ ntụ", "block.minecraft.banner.flower.gray": "<PERSON><PERSON><PERSON> okooko ntụ ntụ", "block.minecraft.banner.flower.green": "Nke-nke Ifuru Chaji", "block.minecraft.banner.flower.light_blue": "<PERSON><PERSON><PERSON> okooko an<PERSON> anụn<PERSON>", "block.minecraft.banner.flower.light_gray": "<PERSON><PERSON><PERSON> okooko ntụ ntụ <PERSON>", "block.minecraft.banner.flower.lime": "<PERSON><PERSON><PERSON> okooko or<PERSON> nk<PERSON>", "block.minecraft.banner.flower.magenta": "<PERSON><PERSON><PERSON> okooko uh<PERSON>o", "block.minecraft.banner.flower.orange": "<PERSON><PERSON><PERSON> ok<PERSON>o", "block.minecraft.banner.flower.pink": "<PERSON><PERSON><PERSON> ok<PERSON>o", "block.minecraft.banner.flower.purple": "<PERSON><PERSON><PERSON> okooko odo odo", "block.minecraft.banner.flower.red": "Mme mme Ifuru <PERSON>ji", "block.minecraft.banner.flower.white": "<PERSON><PERSON><PERSON> ok<PERSON>", "block.minecraft.banner.flower.yellow": "<PERSON><PERSON><PERSON> okooko edo edo", "block.minecraft.banner.globe.black": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.blue": "Bulu ụwa", "block.minecraft.banner.globe.brown": "<PERSON><PERSON>", "block.minecraft.banner.globe.cyan": "<PERSON><PERSON>", "block.minecraft.banner.globe.gray": "Grey ụwa", "block.minecraft.banner.globe.green": "Nke-n<PERSON>", "block.minecraft.banner.globe.light_blue": "Ìhè B<PERSON> ụwa", "block.minecraft.banner.globe.light_gray": "Ìhè Grey ụwa", "block.minecraft.banner.globe.lime": "Ìhè <PERSON><PERSON>-nke <PERSON>wa", "block.minecraft.banner.globe.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.orange": "Oroma <PERSON>", "block.minecraft.banner.globe.pink": "<PERSON><PERSON>", "block.minecraft.banner.globe.purple": "Acha ozo ozo ụwa", "block.minecraft.banner.globe.red": "Mme mme <PERSON>wa", "block.minecraft.banner.globe.white": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.globe.yellow": "<PERSON>", "block.minecraft.banner.gradient.black": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.gradient.blue": "Bulu grajentị", "block.minecraft.banner.gradient.brown": "<PERSON><PERSON>", "block.minecraft.banner.gradient.cyan": "<PERSON><PERSON>", "block.minecraft.banner.gradient.gray": "<PERSON> grajentị", "block.minecraft.banner.gradient.green": "<PERSON>du-ndu grajent<PERSON>", "block.minecraft.banner.gradient.light_blue": "Ìhè bulu grajentị", "block.minecraft.banner.gradient.light_gray": "Ìhè grey grajentị", "block.minecraft.banner.gradient.lime": "Ìhè Nke-nke grajentị", "block.minecraft.banner.gradient.magenta": "Magenta grajent<PERSON>", "block.minecraft.banner.gradient.orange": "Oroma grajent<PERSON>", "block.minecraft.banner.gradient.pink": "<PERSON><PERSON> ocha grajent<PERSON>", "block.minecraft.banner.gradient.purple": "<PERSON>cha odo odo grajent<PERSON>", "block.minecraft.banner.gradient.red": "<PERSON><PERSON>", "block.minecraft.banner.gradient.white": "<PERSON><PERSON>", "block.minecraft.banner.gradient.yellow": "<PERSON> grajent<PERSON>", "block.minecraft.banner.gradient_up.black": "<PERSON><PERSON> g<PERSON>", "block.minecraft.banner.gradient_up.blue": "Bulu Isi grajentị", "block.minecraft.banner.gradient_up.brown": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.cyan": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.gray": "<PERSON> grajent<PERSON>", "block.minecraft.banner.gradient_up.green": "<PERSON>ke-nke <PERSON> graj<PERSON>", "block.minecraft.banner.gradient_up.light_blue": "Ìhè Bulu Isi grajentị", "block.minecraft.banner.gradient_up.light_gray": "Ìhè <PERSON> <PERSON>i grajentị", "block.minecraft.banner.gradient_up.lime": "Ìhè Nke-nke <PERSON> grajent<PERSON>", "block.minecraft.banner.gradient_up.magenta": "Magenta Is<PERSON> grajent<PERSON>", "block.minecraft.banner.gradient_up.orange": "Oroma <PERSON> graj<PERSON>", "block.minecraft.banner.gradient_up.pink": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.purple": "Acha ozo ozo Isi grajent<PERSON>", "block.minecraft.banner.gradient_up.red": "Mme mme <PERSON> graj<PERSON>", "block.minecraft.banner.gradient_up.white": "<PERSON><PERSON>", "block.minecraft.banner.gradient_up.yellow": "<PERSON> g<PERSON>", "block.minecraft.banner.guster.black": "Black Guster", "block.minecraft.banner.guster.blue": "Blue Guster", "block.minecraft.banner.guster.brown": "<PERSON>", "block.minecraft.banner.guster.cyan": "<PERSON><PERSON>", "block.minecraft.banner.guster.gray": "<PERSON>", "block.minecraft.banner.guster.green": "<PERSON> Guster", "block.minecraft.banner.guster.light_blue": "Light Blue Guster", "block.minecraft.banner.guster.light_gray": "Light Gray Guster", "block.minecraft.banner.guster.lime": "<PERSON><PERSON>", "block.minecraft.banner.guster.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.guster.orange": "Orange Guster", "block.minecraft.banner.guster.pink": "<PERSON> Guster", "block.minecraft.banner.guster.purple": "Purple Guster", "block.minecraft.banner.guster.red": "<PERSON> Gus<PERSON>", "block.minecraft.banner.guster.white": "White Guster", "block.minecraft.banner.guster.yellow": "Yellow Guster", "block.minecraft.banner.half_horizontal.black": "Nwa Per Per", "block.minecraft.banner.half_horizontal.blue": "Bulu Kwa Fess", "block.minecraft.banner.half_horizontal.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.gray": "Grey Kwa Fess", "block.minecraft.banner.half_horizontal.green": "Green Per Fess", "block.minecraft.banner.half_horizontal.light_blue": "Ìhè Bulu Kwa Fess", "block.minecraft.banner.half_horizontal.light_gray": "Ìhè Grey Kwa Fess", "block.minecraft.banner.half_horizontal.lime": "Ìhè Ndu-ndu <PERSON> Fess", "block.minecraft.banner.half_horizontal.magenta": "Magenta Kwa Fess", "block.minecraft.banner.half_horizontal.orange": "Oroma Kwa Fess", "block.minecraft.banner.half_horizontal.pink": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.purple": "<PERSON><PERSON> odo odo <PERSON>", "block.minecraft.banner.half_horizontal.red": "Red Per Fess", "block.minecraft.banner.half_horizontal.white": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal.yellow": "Edo Kwa Fess", "block.minecraft.banner.half_horizontal_bottom.black": "Black Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.blue": "<PERSON><PERSON> an<PERSON> anụnụ maka ntụgharị Fess", "block.minecraft.banner.half_horizontal_bottom.brown": "<PERSON> Maka Fess tụgh<PERSON>a", "block.minecraft.banner.half_horizontal_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_horizontal_bottom.gray": "Atụgharịrị <PERSON>", "block.minecraft.banner.half_horizontal_bottom.green": "Green Maka Fess tụghar<PERSON>a", "block.minecraft.banner.half_horizontal_bottom.light_blue": "Atụgharịrị na-acha anụnụ anụnụ kwa Fess", "block.minecraft.banner.half_horizontal_bottom.light_gray": "Agbanyụrụ agba ntụ kwa Fess", "block.minecraft.banner.half_horizontal_bottom.lime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wayo kwa <PERSON>", "block.minecraft.banner.half_horizontal_bottom.magenta": "Magenta kwa Fess tụgharịrị", "block.minecraft.banner.half_horizontal_bottom.orange": "Oroma Kwa Fess tụgharịa", "block.minecraft.banner.half_horizontal_bottom.pink": "Pink kwa Fess tụgharịrị", "block.minecraft.banner.half_horizontal_bottom.purple": "A tụgharịala odo odo kwa nkọ", "block.minecraft.banner.half_horizontal_bottom.red": "Red Per Fess Inverted", "block.minecraft.banner.half_horizontal_bottom.white": "Atụgharịrị na-acha <PERSON>cha kwa Fess", "block.minecraft.banner.half_horizontal_bottom.yellow": "Agbarụrụ Yellow Kwa Fess", "block.minecraft.banner.half_vertical.black": "Oji Kwa Icha", "block.minecraft.banner.half_vertical.blue": "Bulu Kwa Icha", "block.minecraft.banner.half_vertical.brown": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.cyan": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.gray": "Grey Kwa Icha", "block.minecraft.banner.half_vertical.green": "Ndu-ndu Kwa Icha", "block.minecraft.banner.half_vertical.light_blue": "<PERSON> na -acha ọcha na -acha", "block.minecraft.banner.half_vertical.light_gray": "Ìhè Grey Kwa Icha", "block.minecraft.banner.half_vertical.lime": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.magenta": "Ma<PERSON><PERSON>", "block.minecraft.banner.half_vertical.orange": "Orange Kwa Icha", "block.minecraft.banner.half_vertical.pink": "Pink Kwa Pale", "block.minecraft.banner.half_vertical.purple": "<PERSON><PERSON> odo odo Kwa Icha", "block.minecraft.banner.half_vertical.red": "<PERSON><PERSON>", "block.minecraft.banner.half_vertical.white": "White Kwa Icha", "block.minecraft.banner.half_vertical.yellow": "Yellow Kwa Pale", "block.minecraft.banner.half_vertical_right.black": "Oji Kwa Icha Nke ozo", "block.minecraft.banner.half_vertical_right.blue": "Bulu Kwa Icha Nke ozo", "block.minecraft.banner.half_vertical_right.brown": "<PERSON><PERSON> N<PERSON>o", "block.minecraft.banner.half_vertical_right.cyan": "<PERSON><PERSON>cha N<PERSON>o", "block.minecraft.banner.half_vertical_right.gray": "<PERSON> Inverted", "block.minecraft.banner.half_vertical_right.green": "Ndu-ndu Kwa Icha N<PERSON>o", "block.minecraft.banner.half_vertical_right.light_blue": "Light Blue Per Pale Inverted", "block.minecraft.banner.half_vertical_right.light_gray": "Ìhè Grey Kwa Icha Nke ozo", "block.minecraft.banner.half_vertical_right.lime": "Wayo kwa icha mmirimmiri t<PERSON>", "block.minecraft.banner.half_vertical_right.magenta": "Magenta Per <PERSON> Inverted", "block.minecraft.banner.half_vertical_right.orange": "Orange Per Pale Inverted", "block.minecraft.banner.half_vertical_right.pink": "Pink kwa Icha mmirimmiri t<PERSON>a", "block.minecraft.banner.half_vertical_right.purple": "<PERSON><PERSON> odo odo Kwa Icha Nke ozo", "block.minecraft.banner.half_vertical_right.red": "<PERSON><PERSON> K<PERSON> Icha Nke ozo", "block.minecraft.banner.half_vertical_right.white": "White Per Pale Inverted", "block.minecraft.banner.half_vertical_right.yellow": "Agbarụrụ Yellow Kwa Icha mmirimmiri", "block.minecraft.banner.mojang.black": "<PERSON><PERSON>", "block.minecraft.banner.mojang.blue": "Bulu apụl", "block.minecraft.banner.mojang.brown": "<PERSON><PERSON>", "block.minecraft.banner.mojang.cyan": "<PERSON><PERSON>", "block.minecraft.banner.mojang.gray": "<PERSON> ap<PERSON>l", "block.minecraft.banner.mojang.green": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.light_blue": "Ìhè Bulu apụl", "block.minecraft.banner.mojang.light_gray": "Ìhè Grey apụl", "block.minecraft.banner.mojang.lime": "Ìhè Nke-nke ap<PERSON>l", "block.minecraft.banner.mojang.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.mojang.orange": "<PERSON><PERSON>", "block.minecraft.banner.mojang.pink": "<PERSON><PERSON> ocha a<PERSON>", "block.minecraft.banner.mojang.purple": "Acha ozo ozo", "block.minecraft.banner.mojang.red": "Mme mme ap<PERSON>l", "block.minecraft.banner.mojang.white": "<PERSON><PERSON>", "block.minecraft.banner.mojang.yellow": "Edo Ìhè Grey apụl", "block.minecraft.banner.piglin.black": "<PERSON><PERSON>", "block.minecraft.banner.piglin.blue": "<PERSON><PERSON> imi", "block.minecraft.banner.piglin.brown": "Sun<PERSON>ụ<PERSON> aja aja", "block.minecraft.banner.piglin.cyan": "Sun<PERSON>ụ<PERSON><PERSON> ntụ ntụ", "block.minecraft.banner.piglin.gray": "Sun<PERSON>ụ<PERSON><PERSON> ntụ ntụ", "block.minecraft.banner.piglin.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> akwụkwọ ndụ akwụkwọ ndụ", "block.minecraft.banner.piglin.light_blue": "<PERSON><PERSON><PERSON><PERSON><PERSON> anụnụ anụnụ <PERSON>", "block.minecraft.banner.piglin.light_gray": "Sunaụtụ ntụ ntụ <PERSON>è", "block.minecraft.banner.piglin.lime": "<PERSON><PERSON><PERSON> or<PERSON> n<PERSON>", "block.minecraft.banner.piglin.magenta": "<PERSON><PERSON><PERSON><PERSON>o", "block.minecraft.banner.piglin.orange": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.pink": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.purple": "<PERSON><PERSON><PERSON><PERSON> odo odo", "block.minecraft.banner.piglin.red": "<PERSON><PERSON> imi", "block.minecraft.banner.piglin.white": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.piglin.yellow": "<PERSON><PERSON><PERSON> imi", "block.minecraft.banner.rhombus.black": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.blue": "Blue Lozenge", "block.minecraft.banner.rhombus.brown": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.cyan": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.gray": "Gray Lozenge", "block.minecraft.banner.rhombus.green": "Ndu-ndu <PERSON>ge", "block.minecraft.banner.rhombus.light_blue": "Ìhè Bulu Lozenge", "block.minecraft.banner.rhombus.light_gray": "Ìhè Grey Lozenge", "block.minecraft.banner.rhombus.lime": "Lime Lozenge", "block.minecraft.banner.rhombus.magenta": "Magenta Lozenge", "block.minecraft.banner.rhombus.orange": "Oroma lozenge", "block.minecraft.banner.rhombus.pink": "<PERSON><PERSON> o<PERSON>", "block.minecraft.banner.rhombus.purple": "Purple Lozenge", "block.minecraft.banner.rhombus.red": "Red Lozenge", "block.minecraft.banner.rhombus.white": "<PERSON><PERSON>", "block.minecraft.banner.rhombus.yellow": "Yellow Lozenge", "block.minecraft.banner.skull.black": "<PERSON><PERSON> isi <PERSON>", "block.minecraft.banner.skull.blue": "Bulu Okpokoro isi Chaji", "block.minecraft.banner.skull.brown": "<PERSON><PERSON> is<PERSON>", "block.minecraft.banner.skull.cyan": "<PERSON><PERSON> isi <PERSON>", "block.minecraft.banner.skull.gray": "<PERSON> isi <PERSON>", "block.minecraft.banner.skull.green": "<PERSON><PERSON><PERSON><PERSON><PERSON> isi <PERSON>", "block.minecraft.banner.skull.light_blue": "Ìhè Bulu Okpokoro isi Chaji", "block.minecraft.banner.skull.light_gray": "Ìhè Grey Okpokoro isi <PERSON>ji", "block.minecraft.banner.skull.lime": "Ìhè Nke-nke <PERSON> isi <PERSON>", "block.minecraft.banner.skull.magenta": "<PERSON><PERSON><PERSON> isi <PERSON>", "block.minecraft.banner.skull.orange": "<PERSON><PERSON> isi <PERSON>", "block.minecraft.banner.skull.pink": "<PERSON><PERSON> isi <PERSON>", "block.minecraft.banner.skull.purple": "Acha ozo ozo Okpokoro isi <PERSON>", "block.minecraft.banner.skull.red": "Mme mme Okpokoro isi Cha<PERSON>", "block.minecraft.banner.skull.white": "<PERSON><PERSON> isi <PERSON>", "block.minecraft.banner.skull.yellow": "<PERSON> isi <PERSON>", "block.minecraft.banner.small_stripes.black": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.blue": "Bulu Paly", "block.minecraft.banner.small_stripes.brown": "Agba aja aja", "block.minecraft.banner.small_stripes.cyan": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.gray": "<PERSON>", "block.minecraft.banner.small_stripes.green": "Agba akwụkwọ ndụ <PERSON>", "block.minecraft.banner.small_stripes.light_blue": "Ìhè Bulu Paly", "block.minecraft.banner.small_stripes.light_gray": "Ìhè Grey Paly", "block.minecraft.banner.small_stripes.lime": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.orange": "Oro<PERSON>", "block.minecraft.banner.small_stripes.pink": "<PERSON> Paly", "block.minecraft.banner.small_stripes.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.red": "<PERSON><PERSON>", "block.minecraft.banner.small_stripes.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.small_stripes.yellow": "Edo-<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.black": "Nwa <PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.blue": "Bulu Isi <PERSON>", "block.minecraft.banner.square_bottom_left.brown": "Agba aja aja <PERSON>", "block.minecraft.banner.square_bottom_left.cyan": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.gray": "Agba ntụ ntụ <PERSON>", "block.minecraft.banner.square_bottom_left.green": "Agba akwụkwọ ndụ <PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.light_blue": "Ìhè Bulu Base Dexter Kantọn", "block.minecraft.banner.square_bottom_left.light_gray": "Ìhè Agba ntụ <PERSON>i <PERSON>", "block.minecraft.banner.square_bottom_left.lime": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.orange": "<PERSON><PERSON> Is<PERSON>", "block.minecraft.banner.square_bottom_left.pink": "<PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.red": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.white": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_bottom_left.yellow": "<PERSON>-<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.black": "Nwa <PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.blue": "Bulu Isi <PERSON>", "block.minecraft.banner.square_bottom_right.brown": "<PERSON><PERSON>ba aja isi <PERSON>", "block.minecraft.banner.square_bottom_right.cyan": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.gray": "<PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.green": "Agba akwụkwọ ndụ Isi Sinister <PERSON>", "block.minecraft.banner.square_bottom_right.light_blue": "Ìhè Bulu Isi <PERSON>", "block.minecraft.banner.square_bottom_right.light_gray": "Ìhè Agba ntụ Isi <PERSON>", "block.minecraft.banner.square_bottom_right.lime": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.magenta": "<PERSON><PERSON><PERSON> Is<PERSON>", "block.minecraft.banner.square_bottom_right.orange": "<PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.pink": "<PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.purple": "Pledị Ihe O<PERSON> Dị Mpụ na Pọpụl", "block.minecraft.banner.square_bottom_right.red": "<PERSON><PERSON> <PERSON>", "block.minecraft.banner.square_bottom_right.white": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_bottom_right.yellow": "Edo-ed<PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_top_left.black": "<PERSON>ye isi ojii <PERSON>", "block.minecraft.banner.square_top_left.blue": "Bulu Onyeisi Dexter <PERSON>", "block.minecraft.banner.square_top_left.brown": "Agba aja Onyeisi <PERSON>", "block.minecraft.banner.square_top_left.cyan": "<PERSON><PERSON>", "block.minecraft.banner.square_top_left.gray": "Agba ntụ Onyeisi <PERSON>", "block.minecraft.banner.square_top_left.green": "Agba akwụkwọ ndụ Onyeisi <PERSON>", "block.minecraft.banner.square_top_left.light_blue": "Ìhè <PERSON>ulu Onyeisi <PERSON>", "block.minecraft.banner.square_top_left.light_gray": "Ìhè Agba ntụ Onyeisi <PERSON>", "block.minecraft.banner.square_top_left.lime": "<PERSON><PERSON>", "block.minecraft.banner.square_top_left.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_left.orange": "<PERSON><PERSON>", "block.minecraft.banner.square_top_left.pink": "Pink Onyeisi <PERSON>", "block.minecraft.banner.square_top_left.purple": "Onye na-acha odo odo <PERSON><PERSON>", "block.minecraft.banner.square_top_left.red": "<PERSON><PERSON>", "block.minecraft.banner.square_top_left.white": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_top_left.yellow": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.square_top_right.black": "Onye isi ojii <PERSON>", "block.minecraft.banner.square_top_right.blue": "Bulu Onyeisi Sinister Kantọn", "block.minecraft.banner.square_top_right.brown": "Agba aja Onyeisi Sinister <PERSON>", "block.minecraft.banner.square_top_right.cyan": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.gray": "Agba ntụ Onyeisi Sinister Kantọn", "block.minecraft.banner.square_top_right.green": "Agba akwụkwọ ndụ Onyeisi Sinister Kantọn", "block.minecraft.banner.square_top_right.light_blue": "Ìhè Bulu Onyeisi Sinister Kantọn", "block.minecraft.banner.square_top_right.light_gray": "Ìhè Agba ntụ Onyeisi Sinister Kantọn", "block.minecraft.banner.square_top_right.lime": "<PERSON><PERSON> On<PERSON>", "block.minecraft.banner.square_top_right.magenta": "Magenta Onyeisi Sin<PERSON>", "block.minecraft.banner.square_top_right.orange": "Oroma Onyeisi Sinister <PERSON>", "block.minecraft.banner.square_top_right.pink": "Pink Onyeisi Sinister <PERSON>", "block.minecraft.banner.square_top_right.purple": "Pọpụl Onyeisi Sinister <PERSON>", "block.minecraft.banner.square_top_right.red": "<PERSON><PERSON>", "block.minecraft.banner.square_top_right.white": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.square_top_right.yellow": "Edo-edo Onyeisi <PERSON>", "block.minecraft.banner.straight_cross.black": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.blue": "Bulu Obe", "block.minecraft.banner.straight_cross.brown": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.cyan": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.gray": "<PERSON>", "block.minecraft.banner.straight_cross.green": "Nke-<PERSON><PERSON>", "block.minecraft.banner.straight_cross.light_blue": "Ìhè Bulu Obe", "block.minecraft.banner.straight_cross.light_gray": "Ìhè Grey Obe", "block.minecraft.banner.straight_cross.lime": "Ìhè <PERSON><PERSON>-nke <PERSON>be", "block.minecraft.banner.straight_cross.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.straight_cross.orange": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.pink": "Mme mme Obe", "block.minecraft.banner.straight_cross.purple": "Acha ozo ozo Obe", "block.minecraft.banner.straight_cross.red": "Mme mme Obe", "block.minecraft.banner.straight_cross.white": "<PERSON><PERSON>", "block.minecraft.banner.straight_cross.yellow": "<PERSON>", "block.minecraft.banner.stripe_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.blue": "Isi Bulu", "block.minecraft.banner.stripe_bottom.brown": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.green": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.light_blue": "Ìhè Bulu Isi", "block.minecraft.banner.stripe_bottom.light_gray": "Ìhè <PERSON><PERSON> awọ", "block.minecraft.banner.stripe_bottom.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.orange": "<PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.pink": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.purple": "Ntọala Pọpụl", "block.minecraft.banner.stripe_bottom.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_bottom.white": "<PERSON><PERSON><PERSON><PERSON>cha isi", "block.minecraft.banner.stripe_bottom.yellow": "<PERSON><PERSON>o", "block.minecraft.banner.stripe_center.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.blue": "<PERSON><PERSON> an<PERSON>", "block.minecraft.banner.stripe_center.brown": "Agba aja aja", "block.minecraft.banner.stripe_center.cyan": "<PERSON>an <PERSON>cha mm<PERSON>", "block.minecraft.banner.stripe_center.gray": "Agba Grey", "block.minecraft.banner.stripe_center.green": "Agba akwụkwọ ndụ icha mmirimmiri", "block.minecraft.banner.stripe_center.light_blue": "Ìhè Bulu icha mmirimmiri", "block.minecraft.banner.stripe_center.light_gray": "Ìhè Agba Grey", "block.minecraft.banner.stripe_center.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_center.magenta": "Magenta icha mmirimmiri", "block.minecraft.banner.stripe_center.orange": "Oroma icha mmirimmiri", "block.minecraft.banner.stripe_center.pink": "Pink icha mmirimmiri", "block.minecraft.banner.stripe_center.purple": "Agba odo", "block.minecraft.banner.stripe_center.red": "<PERSON>ie uhie", "block.minecraft.banner.stripe_center.white": "Na<PERSON><PERSON><PERSON>cha <PERSON> mm<PERSON>", "block.minecraft.banner.stripe_center.yellow": "<PERSON>cha odo odo", "block.minecraft.banner.stripe_downleft.black": "Nwa gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.blue": "Bulu Gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.brown": "Agba aja Gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.cyan": "<PERSON><PERSON> agbagọ Sinister", "block.minecraft.banner.stripe_downleft.gray": "Grey Gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.green": "Agba akwụkwọ ndụ Gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.light_blue": "Ìhè Bulu Gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.light_gray": "Ìhè Grey gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.lime": "Wayo Gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.magenta": "Magenta Gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.orange": "Oroma Gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.pink": "Pink Gbagọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.purple": "Pọp<PERSON><PERSON> gbag<PERSON>rọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.red": "<PERSON><PERSON>ọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>rọ agbagọ Sinister", "block.minecraft.banner.stripe_downleft.yellow": "Edo-edo G<PERSON>ọrọ agbagọ Sinister", "block.minecraft.banner.stripe_downright.black": "Nwa gbagọrọ agbagọ", "block.minecraft.banner.stripe_downright.blue": "Agba Gbagọrọ agbagọ", "block.minecraft.banner.stripe_downright.brown": "Agba a<PERSON>bag<PERSON>", "block.minecraft.banner.stripe_downright.cyan": "<PERSON><PERSON> a<PERSON>", "block.minecraft.banner.stripe_downright.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.green": "Agba akwụkwọ ndụ Gbagọrọ agbagọ", "block.minecraft.banner.stripe_downright.light_blue": "Ìhè Bulu Gbagọrọ agbagọ", "block.minecraft.banner.stripe_downright.light_gray": "Ìhè Grey gbagọrọ agbagọ", "block.minecraft.banner.stripe_downright.lime": "Wayo gbagọrọ agbagọ", "block.minecraft.banner.stripe_downright.magenta": "Magenta Gbagọrọ agbagọ", "block.minecraft.banner.stripe_downright.orange": "Oroma Gbagọrọ agbagọ", "block.minecraft.banner.stripe_downright.pink": "Pink gbagọọ agbagọ", "block.minecraft.banner.stripe_downright.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>bag<PERSON>", "block.minecraft.banner.stripe_downright.red": "<PERSON><PERSON>", "block.minecraft.banner.stripe_downright.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>bag<PERSON>", "block.minecraft.banner.stripe_downright.yellow": "Edo edo edo", "block.minecraft.banner.stripe_left.black": "Nwa icha mmirimmiri <PERSON>", "block.minecraft.banner.stripe_left.blue": "<PERSON><PERSON> an<PERSON>", "block.minecraft.banner.stripe_left.brown": "Agba aja Icha mm<PERSON>", "block.minecraft.banner.stripe_left.cyan": "<PERSON><PERSON> icha <PERSON>", "block.minecraft.banner.stripe_left.gray": "Agba ntụ Na-acha odo odo", "block.minecraft.banner.stripe_left.green": "Agba akwụkwọ ndụ Icha mmirimmiri Dexter", "block.minecraft.banner.stripe_left.light_blue": "Ìhè Bulu icha mmirimmiri Dexter", "block.minecraft.banner.stripe_left.light_gray": "Ìhè <PERSON> Ichapu Dexter", "block.minecraft.banner.stripe_left.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_left.magenta": "Magenta icha mmirimmiri <PERSON>", "block.minecraft.banner.stripe_left.orange": "Oroma icha mmirimmiri <PERSON>", "block.minecraft.banner.stripe_left.pink": "Pink icha mmirimmiri Dexter", "block.minecraft.banner.stripe_left.purple": "Acha acha odo odo", "block.minecraft.banner.stripe_left.red": "<PERSON>ie <PERSON>-acha uhie uhie", "block.minecraft.banner.stripe_left.white": "Ọcha <PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_left.yellow": "Edo-edo icha mmirimmiri <PERSON>", "block.minecraft.banner.stripe_middle.black": "Nwa Nwa", "block.minecraft.banner.stripe_middle.blue": "Bulu Fes", "block.minecraft.banner.stripe_middle.brown": "Agba aja aja", "block.minecraft.banner.stripe_middle.cyan": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.green": "Esgwọ Agba akwụkwọ ndụ", "block.minecraft.banner.stripe_middle.light_blue": "Ìhè Bulu Fes", "block.minecraft.banner.stripe_middle.light_gray": "Ìhè <PERSON><PERSON> awọ", "block.minecraft.banner.stripe_middle.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_middle.magenta": "Magenta <PERSON>s", "block.minecraft.banner.stripe_middle.orange": "Oroma Fes", "block.minecraft.banner.stripe_middle.pink": "Pink agba", "block.minecraft.banner.stripe_middle.purple": "Akwukwo Udo", "block.minecraft.banner.stripe_middle.red": "<PERSON>ie uhie", "block.minecraft.banner.stripe_middle.white": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_middle.yellow": "Odo odo", "block.minecraft.banner.stripe_right.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_right.blue": "Bulu Icha mmirimmiri <PERSON>", "block.minecraft.banner.stripe_right.brown": "Agba aja Icha mmirimm<PERSON>", "block.minecraft.banner.stripe_right.cyan": "<PERSON><PERSON> mmiri<PERSON>", "block.minecraft.banner.stripe_right.gray": "Agba Grey Icha mmirimmiri <PERSON>", "block.minecraft.banner.stripe_right.green": "Agba akwụkwọ ndụ Bilie Sinister", "block.minecraft.banner.stripe_right.light_blue": "Ìhè Bulu icha mmirimmiri Sinister", "block.minecraft.banner.stripe_right.light_gray": "Ìhè Na-acha uhie uhie Ihie", "block.minecraft.banner.stripe_right.lime": "Way<PERSON>", "block.minecraft.banner.stripe_right.magenta": "Magenta icha mmirimmiri <PERSON>", "block.minecraft.banner.stripe_right.orange": "Oroma icha mmirimmiri <PERSON>", "block.minecraft.banner.stripe_right.pink": "Pink icha mmirimmiri <PERSON>", "block.minecraft.banner.stripe_right.purple": "<PERSON><PERSON><PERSON><PERSON>acha odo odo", "block.minecraft.banner.stripe_right.red": "<PERSON>ie <PERSON>-acha uhie uhie", "block.minecraft.banner.stripe_right.white": "Ọcha Na-acha Ọcha", "block.minecraft.banner.stripe_right.yellow": "Onye na-acha odo odo", "block.minecraft.banner.stripe_top.black": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.blue": "Bulu Onyeisi", "block.minecraft.banner.stripe_top.brown": "Agba aja Onyeisi", "block.minecraft.banner.stripe_top.cyan": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.gray": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.green": "Agba akwụkwọ ndụ Onyeisi", "block.minecraft.banner.stripe_top.light_blue": "Ìhè B<PERSON> Onyeisi", "block.minecraft.banner.stripe_top.light_gray": "Ìhè <PERSON><PERSON> awọ", "block.minecraft.banner.stripe_top.lime": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.magenta": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.orange": "<PERSON><PERSON>", "block.minecraft.banner.stripe_top.pink": "Pink Onyeisi", "block.minecraft.banner.stripe_top.purple": "<PERSON><PERSON><PERSON>", "block.minecraft.banner.stripe_top.red": "Onyeisi Uhie", "block.minecraft.banner.stripe_top.white": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.stripe_top.yellow": "Onye Isi Edo-edo", "block.minecraft.banner.triangle_bottom.black": "Nwa Chevron", "block.minecraft.banner.triangle_bottom.blue": "Bulu Chevron", "block.minecraft.banner.triangle_bottom.brown": "Agba a<PERSON>", "block.minecraft.banner.triangle_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.gray": "<PERSON> Chevron", "block.minecraft.banner.triangle_bottom.green": "Agba akwụkwọ ndụ Chevron", "block.minecraft.banner.triangle_bottom.light_blue": "Ìhè Bulu Chevron", "block.minecraft.banner.triangle_bottom.light_gray": "Ìhè Agba ntụ Chevron", "block.minecraft.banner.triangle_bottom.lime": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.magenta": "<PERSON><PERSON><PERSON>ron", "block.minecraft.banner.triangle_bottom.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.pink": "Pink Chevron", "block.minecraft.banner.triangle_bottom.purple": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.red": "<PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.white": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.banner.triangle_bottom.yellow": "<PERSON>-<PERSON><PERSON> Chevron", "block.minecraft.banner.triangle_top.black": "Nwa Chegharịrị Nwa", "block.minecraft.banner.triangle_top.blue": "Bulu Chegharịr<PERSON> Ch<PERSON>ron", "block.minecraft.banner.triangle_top.brown": "Agba aja gbanwere Chevron", "block.minecraft.banner.triangle_top.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.gray": "Agba ntụ n<PERSON><PERSON><PERSON>", "block.minecraft.banner.triangle_top.green": "Agba akwụkwọ ndụ nọdụ Chevron", "block.minecraft.banner.triangle_top.light_blue": "Ìhè Blue Agbanwee Chevron", "block.minecraft.banner.triangle_top.light_gray": "Ìhè Na-agbanwe agbanwe Chevron", "block.minecraft.banner.triangle_top.lime": "Wayo na-agbanwe agbanwe Chevron", "block.minecraft.banner.triangle_top.magenta": "Agba akwụkwọ ndụ nọdụ Chevron", "block.minecraft.banner.triangle_top.orange": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.pink": "Pink nọchiri <PERSON>ron", "block.minecraft.banner.triangle_top.purple": "Chevron na-acha odo odo", "block.minecraft.banner.triangle_top.red": "<PERSON><PERSON>", "block.minecraft.banner.triangle_top.white": "Wayo na-agbanwe agbanwe Chevron", "block.minecraft.banner.triangle_top.yellow": "Nwa Chegharịrị Nwa", "block.minecraft.banner.triangles_bottom.black": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.blue": "<PERSON>i <PERSON>he na-acha an<PERSON> an<PERSON>n<PERSON>", "block.minecraft.banner.triangles_bottom.brown": "<PERSON>i ala agba aja aja", "block.minecraft.banner.triangles_bottom.cyan": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.gray": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.green": "Ejiri Green Base", "block.minecraft.banner.triangles_bottom.light_blue": "Ìhè Bulu icha mmirimmiri Dexter", "block.minecraft.banner.triangles_bottom.light_gray": "Light isi awọ Base Indented", "block.minecraft.banner.triangles_bottom.lime": "<PERSON><PERSON> isi ala", "block.minecraft.banner.triangles_bottom.magenta": "Magenta Isi Indented", "block.minecraft.banner.triangles_bottom.orange": "Orange Isi Indented", "block.minecraft.banner.triangles_bottom.pink": "Pink Isi Indented", "block.minecraft.banner.triangles_bottom.purple": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.red": "<PERSON><PERSON>", "block.minecraft.banner.triangles_bottom.white": "<PERSON>i ihe eji acha <PERSON>cha", "block.minecraft.banner.triangles_bottom.yellow": "Isi Yellow Base", "block.minecraft.banner.triangles_top.black": "<PERSON><PERSON>", "block.minecraft.banner.triangles_top.blue": "Onye isi na -acha an<PERSON> anụnụ", "block.minecraft.banner.triangles_top.brown": "<PERSON>ye isi <PERSON> a<PERSON>wu<PERSON>", "block.minecraft.banner.triangles_top.cyan": "<PERSON><PERSON> isi <PERSON>", "block.minecraft.banner.triangles_top.gray": "A nabatara onye isi awọ", "block.minecraft.banner.triangles_top.green": "Green Chief Indented", "block.minecraft.banner.triangles_top.light_blue": "Etinyere Blue Light", "block.minecraft.banner.triangles_top.light_gray": "A nabatara onye isi ọkụ ọ<PERSON>ụ", "block.minecraft.banner.triangles_top.lime": "Wayo Chief Indented", "block.minecraft.banner.triangles_top.magenta": "Onye isi Ma<PERSON>a a<PERSON>ala", "block.minecraft.banner.triangles_top.orange": "Orange Chief Indented", "block.minecraft.banner.triangles_top.pink": "Onye isi Pink agbakwunyere", "block.minecraft.banner.triangles_top.purple": "A <PERSON> <PERSON><PERSON><PERSON>ye <PERSON> Chief", "block.minecraft.banner.triangles_top.red": "<PERSON><PERSON> Is<PERSON>", "block.minecraft.banner.triangles_top.white": "A nabatara White Chief", "block.minecraft.banner.triangles_top.yellow": "Onye isi odo na -abata", "block.minecraft.barrel": "Gbọmgbọm", "block.minecraft.barrier": "<PERSON><PERSON>", "block.minecraft.basalt": "Basalt", "block.minecraft.beacon": "<PERSON><PERSON><PERSON>", "block.minecraft.beacon.primary": "<PERSON><PERSON>", "block.minecraft.beacon.secondary": "Secondary Ike", "block.minecraft.bed.no_sleep": "Can nwere ike ihi <PERSON>ra n’abalị ma ọ bụ n’oge oke ikuku", "block.minecraft.bed.not_safe": "Ị nwere ike izu ike ugbu a; e nwere nnukwu anụ dị nso", "block.minecraft.bed.obstructed": "Ihe ndina a mechiri emechi", "block.minecraft.bed.occupied": "<PERSON><PERSON> ndina a ka ji", "block.minecraft.bed.too_far_away": "Ị nwere ike izu ike ugbu a; ihe ndina dị nnọọ anya", "block.minecraft.bedrock": "Bedrock", "block.minecraft.bee_nest": "Bee akwu", "block.minecraft.beehive": "Bee", "block.minecraft.beetroots": "Beetroots", "block.minecraft.bell": "Mgb<PERSON><PERSON><PERSON>", "block.minecraft.big_dripleaf": "Nnukwu Dripleaf", "block.minecraft.big_dripleaf_stem": "Nnukwu Stepleaf Stem", "block.minecraft.birch_button": "<PERSON>", "block.minecraft.birch_door": "Ọrịa Birch", "block.minecraft.birch_fence": "<PERSON> ngere", "block.minecraft.birch_fence_gate": "Ọnụ encezọ encemá Birch", "block.minecraft.birch_hanging_sign": "<PERSON> Hanging Sign", "block.minecraft.birch_leaves": "<PERSON><PERSON><PERSON><PERSON> Birch", "block.minecraft.birch_log": "Abanye na Birch", "block.minecraft.birch_planks": "<PERSON>", "block.minecraft.birch_pressure_plate": "Plate Strip Press", "block.minecraft.birch_sapling": "Birch Sapling", "block.minecraft.birch_sign": "Akara Birch", "block.minecraft.birch_slab": "<PERSON><PERSON>", "block.minecraft.birch_stairs": "<PERSON>", "block.minecraft.birch_trapdoor": "<PERSON>", "block.minecraft.birch_wall_hanging_sign": "Birch Wall Hanging Sign", "block.minecraft.birch_wall_sign": "<PERSON><PERSON>", "block.minecraft.birch_wood": "<PERSON><PERSON><PERSON>", "block.minecraft.black_banner": "Nwa <PERSON>ụ<PERSON>", "block.minecraft.black_bed": "Nwa ndina", "block.minecraft.black_candle": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>i", "block.minecraft.black_candle_cake": "<PERSON><PERSON>cha na <PERSON> kandụl", "block.minecraft.black_carpet": "<PERSON><PERSON><PERSON> ojii", "block.minecraft.black_concrete": "Nwa <PERSON>he", "block.minecraft.black_concrete_powder": "Nwa lhe ntụ ntụ", "block.minecraft.black_glazed_terracotta": "Black na-egbuke egbuke Terracotta", "block.minecraft.black_shulker_box": "Igbe Black Shulker", "block.minecraft.black_stained_glass": "<PERSON><PERSON> ojii", "block.minecraft.black_stained_glass_pane": "Nwa Agba Glass", "block.minecraft.black_terracotta": "Nwa Terracotta", "block.minecraft.black_wool": "Nwa Nwa ojii", "block.minecraft.blackstone": "<PERSON><PERSON> ojii", "block.minecraft.blackstone_slab": "Blackstone Slab", "block.minecraft.blackstone_stairs": "<PERSON><PERSON> ojii", "block.minecraft.blackstone_wall": "Nwa Nkịtị", "block.minecraft.blast_furnace": "Gbawara Ọkụ", "block.minecraft.blue_banner": "Bulu Pụr<PERSON>", "block.minecraft.blue_bed": "Bulu bed", "block.minecraft.blue_candle": "Kand<PERSON><PERSON> na -acha an<PERSON> an<PERSON>n<PERSON>", "block.minecraft.blue_candle_cake": "Achịcha na kandụl na -acha anụn<PERSON> anụn<PERSON>", "block.minecraft.blue_carpet": "<PERSON>peep<PERSON> na-a<PERSON> an<PERSON> an<PERSON>n<PERSON>", "block.minecraft.blue_concrete": "Bulu Concrete", "block.minecraft.blue_concrete_powder": "Bulu lhe Ntụ ntụ", "block.minecraft.blue_glazed_terracotta": "Blue Glazed Terracotta", "block.minecraft.blue_ice": "Blue Ice", "block.minecraft.blue_orchid": "Blue Orchid", "block.minecraft.blue_shulker_box": "Igbe Blue Shulker", "block.minecraft.blue_stained_glass": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> an<PERSON> an<PERSON>n<PERSON>", "block.minecraft.blue_stained_glass_pane": "<PERSON>g<PERSON> eji agba agba agba aja aja", "block.minecraft.blue_terracotta": "Blue Terracotta", "block.minecraft.blue_wool": "Blue ajị", "block.minecraft.bone_block": "Ọkpụkpụ Block", "block.minecraft.bookshelf": "Akwụkwọ akwụkwọ", "block.minecraft.brain_coral": "<PERSON>", "block.minecraft.brain_coral_block": "<PERSON><PERSON><PERSON> nke <PERSON>", "block.minecraft.brain_coral_fan": "A na-enwe <PERSON>", "block.minecraft.brain_coral_wall_fan": "Inbụrụ Coral M<PERSON>bidi Fan", "block.minecraft.brewing_stand": "<PERSON><PERSON>", "block.minecraft.brick_slab": "Biriiki Slab", "block.minecraft.brick_stairs": "<PERSON><PERSON>", "block.minecraft.brick_wall": "<PERSON>g<PERSON><PERSON> brik\n", "block.minecraft.bricks": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.brown_banner": "Agba a<PERSON>", "block.minecraft.brown_bed": "Agba aja aja", "block.minecraft.brown_candle": "<PERSON><PERSON>", "block.minecraft.brown_candle_cake": "Achịcha na kandụl aja aja", "block.minecraft.brown_carpet": "<PERSON><PERSON><PERSON> aja aja", "block.minecraft.brown_concrete": "Agba a<PERSON>", "block.minecraft.brown_concrete_powder": "Agba aja lhu Ntụ ntụ", "block.minecraft.brown_glazed_terracotta": "Agba aja aja Glazed Terracotta", "block.minecraft.brown_mushroom": "<PERSON>", "block.minecraft.brown_mushroom_block": "<PERSON>", "block.minecraft.brown_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.brown_stained_glass": "<PERSON><PERSON>", "block.minecraft.brown_stained_glass_pane": "Agba aja aja gbara agba", "block.minecraft.brown_terracotta": "Brown Terracotta", "block.minecraft.brown_wool": "Blue ajị", "block.minecraft.bubble_column": "<PERSON><PERSON><PERSON>", "block.minecraft.bubble_coral": "<PERSON>", "block.minecraft.bubble_coral_block": "<PERSON><PERSON>ere nke coral egosi<PERSON>ta", "block.minecraft.bubble_coral_fan": "A na-enwe coral egosi<PERSON>", "block.minecraft.bubble_coral_wall_fan": "Afọ Coral Wall Fan", "block.minecraft.budding_amethyst": "Amethyst na -agba agba", "block.minecraft.bush": "<PERSON>", "block.minecraft.cactus": "<PERSON><PERSON><PERSON>", "block.minecraft.cactus_flower": "Cactus Flower", "block.minecraft.cake": "<PERSON><PERSON><PERSON>", "block.minecraft.calcite": "Gbakọọ", "block.minecraft.calibrated_sculk_sensor": "Calibrated Sculk Sensor", "block.minecraft.campfire": "<PERSON><PERSON><PERSON>", "block.minecraft.candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.candle_cake": "A<PERSON>cha na kandụl", "block.minecraft.carrots": "Carọt", "block.minecraft.cartography_table": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.carved_pumpkin": "Ebube a kụrụ akpụ", "block.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_air": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_vines": "<PERSON><PERSON><PERSON>", "block.minecraft.cave_vines_plant": "Osisi Osisi Cave", "block.minecraft.chain": "Chain", "block.minecraft.chain_command_block": "Chain Command Block", "block.minecraft.cherry_button": "<PERSON>", "block.minecraft.cherry_door": "Cherry Door", "block.minecraft.cherry_fence": "<PERSON>", "block.minecraft.cherry_fence_gate": "Cherry Fence Gate", "block.minecraft.cherry_hanging_sign": "Cherry Hanging Sign", "block.minecraft.cherry_leaves": "Cherry Leaves", "block.minecraft.cherry_log": "Cherry Log", "block.minecraft.cherry_planks": "Cherry Planks", "block.minecraft.cherry_pressure_plate": "Cherry Pressure Plate", "block.minecraft.cherry_sapling": "Cherry Sapling", "block.minecraft.cherry_sign": "Cherry Sign", "block.minecraft.cherry_slab": "Cherry Slab", "block.minecraft.cherry_stairs": "<PERSON> Stairs", "block.minecraft.cherry_trapdoor": "Cherry Trapdoor", "block.minecraft.cherry_wall_hanging_sign": "Cherry Wall Hanging Sign", "block.minecraft.cherry_wall_sign": "Cherry Wall Sign", "block.minecraft.cherry_wood": "<PERSON>", "block.minecraft.chest": "Igbe", "block.minecraft.chipped_anvil": "<PERSON><PERSON>", "block.minecraft.chiseled_bookshelf": "Chiseled Bookshelf", "block.minecraft.chiseled_copper": "Chiseled Copper", "block.minecraft.chiseled_deepslate": "Deepzọ nke Chiseled", "block.minecraft.chiseled_nether_bricks": "Chiseled Nether Brick", "block.minecraft.chiseled_polished_blackstone": "Chiseled Polished Blackstone", "block.minecraft.chiseled_quartz_block": "Chiseled Quartz Block", "block.minecraft.chiseled_red_sandstone": "Chiseled Red Sandstone", "block.minecraft.chiseled_resin_bricks": "Chiseled Resin Bricks", "block.minecraft.chiseled_sandstone": "Chiseled Sandstone", "block.minecraft.chiseled_stone_bricks": "Chiseled N<PERSON>me brik", "block.minecraft.chiseled_tuff": "Chiseled <PERSON>", "block.minecraft.chiseled_tuff_bricks": "Chiseled Tuff Bricks", "block.minecraft.chorus_flower": "Chorus Ifuru", "block.minecraft.chorus_plant": "Osisi Chorus", "block.minecraft.clay": "<PERSON><PERSON><PERSON>", "block.minecraft.closed_eyeblossom": "Closed Eyeblossom", "block.minecraft.coal_block": "<PERSON><PERSON> mg<PERSON>chi nke <PERSON>", "block.minecraft.coal_ore": "Coal Ore", "block.minecraft.coarse_dirt": "<PERSON><PERSON>", "block.minecraft.cobbled_deepslate": "Cobled miri emi nkume", "block.minecraft.cobbled_deepslate_slab": "Cobled miri emi nkume m<PERSON>ba", "block.minecraft.cobbled_deepslate_stairs": "Cobled miri emi nkume steepụ", "block.minecraft.cobbled_deepslate_wall": "Mgbidi Deepslate Cobbled", "block.minecraft.cobblestone": "Nkịtị", "block.minecraft.cobblestone_slab": "Okpokor<PERSON> Nkume", "block.minecraft.cobblestone_stairs": "<PERSON><PERSON>", "block.minecraft.cobblestone_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.cobweb": "Cobweb", "block.minecraft.cocoa": "<PERSON><PERSON>", "block.minecraft.command_block": "Iwu Block", "block.minecraft.comparator": "Onye na-acho<PERSON>", "block.minecraft.composter": "Ngwak<PERSON><PERSON>", "block.minecraft.conduit": "Ndiniime", "block.minecraft.copper_block": "Ngọng<PERSON>", "block.minecraft.copper_bulb": "Copper Bulb", "block.minecraft.copper_door": "Copper Door", "block.minecraft.copper_grate": "Copper Grate", "block.minecraft.copper_ore": "Ọla kọpa", "block.minecraft.copper_trapdoor": "Copper Trapdoor", "block.minecraft.cornflower": "Ọka", "block.minecraft.cracked_deepslate_bricks": "<PERSON><PERSON> nd<PERSON> g<PERSON>", "block.minecraft.cracked_deepslate_tiles": "Taịlị Deepslate gbawara agbawa", "block.minecraft.cracked_nether_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.cracked_polished_blackstone_bricks": "<PERSON><PERSON> brọsh ojii ejirila ya mee", "block.minecraft.cracked_stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.crafter": "Crafter", "block.minecraft.crafting_table": "Isiokwu Crafting", "block.minecraft.creaking_heart": "Creaking Heart", "block.minecraft.creeper_head": "Isi na-ak<PERSON>ụ akpụ", "block.minecraft.creeper_wall_head": "Isi mgbidi <PERSON>", "block.minecraft.crimson_button": "<PERSON> b<PERSON><PERSON>", "block.minecraft.crimson_door": "Crimson Door", "block.minecraft.crimson_fence": "<PERSON> Fence", "block.minecraft.crimson_fence_gate": "Crimson Fence Gate", "block.minecraft.crimson_fungus": "Crimson Fungus", "block.minecraft.crimson_hanging_sign": "Crimson Hanging Sign", "block.minecraft.crimson_hyphae": "Crimson Hyphae", "block.minecraft.crimson_nylium": "Crimson Nylium", "block.minecraft.crimson_planks": "Crimson Planks", "block.minecraft.crimson_pressure_plate": "Crimson nrụgide Plate", "block.minecraft.crimson_roots": "Crimson Roots", "block.minecraft.crimson_sign": "<PERSON> ama", "block.minecraft.crimson_slab": "Crimson Slab", "block.minecraft.crimson_stairs": "Ọkpụkpụ Crimson", "block.minecraft.crimson_stem": "<PERSON>", "block.minecraft.crimson_trapdoor": "Crimson Trapdoor", "block.minecraft.crimson_wall_hanging_sign": "Crimson Wall Hanging Sign", "block.minecraft.crimson_wall_sign": "Crimson Wall Sign", "block.minecraft.crying_obsidian": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.cut_copper": "<PERSON> <PERSON>", "block.minecraft.cut_copper_slab": "<PERSON> Okpokoro Ọla kọpa", "block.minecraft.cut_copper_stairs": "Bee steepụ <PERSON>", "block.minecraft.cut_red_sandstone": "<PERSON>", "block.minecraft.cut_red_sandstone_slab": "<PERSON> <PERSON>", "block.minecraft.cut_sandstone": "<PERSON> nkume isi", "block.minecraft.cut_sandstone_slab": "<PERSON> nkume isi nkume", "block.minecraft.cyan_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_bed": "<PERSON><PERSON>", "block.minecraft.cyan_candle": "<PERSON><PERSON><PERSON><PERSON> cyan", "block.minecraft.cyan_candle_cake": "Achịcha na kandụl cyan", "block.minecraft.cyan_carpet": "<PERSON><PERSON>", "block.minecraft.cyan_concrete": "<PERSON><PERSON>", "block.minecraft.cyan_concrete_powder": "<PERSON><PERSON> ntụ", "block.minecraft.cyan_glazed_terracotta": "Cyan na-egbuke egbuke Terracotta", "block.minecraft.cyan_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.cyan_stained_glass": "<PERSON><PERSON>", "block.minecraft.cyan_stained_glass_pane": "<PERSON><PERSON> Stained Glass Pane", "block.minecraft.cyan_terracotta": "<PERSON><PERSON>", "block.minecraft.cyan_wool": "<PERSON><PERSON>", "block.minecraft.damaged_anvil": "<PERSON><PERSON>", "block.minecraft.dandelion": "Dandelion", "block.minecraft.dark_oak_button": "<PERSON><PERSON>", "block.minecraft.dark_oak_door": "Ọchịchịrị Oak", "block.minecraft.dark_oak_fence": "Ọchịchịrị Oak nsu", "block.minecraft.dark_oak_fence_gate": "Ọchịchịrị Oak nsu nsu", "block.minecraft.dark_oak_hanging_sign": "Dark Oak Hanging Sign", "block.minecraft.dark_oak_leaves": "<PERSON> gbara <PERSON>", "block.minecraft.dark_oak_log": "Itiri A<PERSON>ban<PERSON>", "block.minecraft.dark_oak_planks": "Itiri <PERSON>", "block.minecraft.dark_oak_pressure_plate": "Itiri Akpu Okpokoro", "block.minecraft.dark_oak_sapling": "Ọchịchịrị Oak Sapling", "block.minecraft.dark_oak_sign": "Ọgba Ọchịchịrị Ọchịchịrị", "block.minecraft.dark_oak_slab": "Itiri Akpu Slab", "block.minecraft.dark_oak_stairs": "Itiri Akpu Nzọụkwụ", "block.minecraft.dark_oak_trapdoor": "Itiri <PERSON>", "block.minecraft.dark_oak_wall_hanging_sign": "Dark Oak Wall Hanging Sign", "block.minecraft.dark_oak_wall_sign": "Ọchịchịrị Oak gbara <PERSON>ch<PERSON>chịrị", "block.minecraft.dark_oak_wood": "Osisi Oak gbara <PERSON>", "block.minecraft.dark_prismarine": "Ọchịchịrị <PERSON><PERSON>arine", "block.minecraft.dark_prismarine_slab": "<PERSON><PERSON>", "block.minecraft.dark_prismarine_stairs": "<PERSON><PERSON>", "block.minecraft.daylight_detector": "<PERSON><PERSON> n<PERSON><PERSON>", "block.minecraft.dead_brain_coral": "nw<PERSON><PERSON><PERSON> anw<PERSON>", "block.minecraft.dead_brain_coral_block": "Ugbo nke coral oku <PERSON>", "block.minecraft.dead_brain_coral_fan": "Brain Nkume coral nw<PERSON><PERSON><PERSON> anwụ", "block.minecraft.dead_brain_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON> Coral Wall Fan", "block.minecraft.dead_bubble_coral": "Nw<PERSON><PERSON><PERSON>fọ Coral ", "block.minecraft.dead_bubble_coral_block": "Ugbo nke coral oku ego<PERSON><PERSON>", "block.minecraft.dead_bubble_coral_fan": "Ọnwụ Afọ Coral Fan", "block.minecraft.dead_bubble_coral_wall_fan": "<PERSON>w<PERSON><PERSON><PERSON> anw<PERSON><PERSON><PERSON> A<PERSON>ụ Coral Wall Fan", "block.minecraft.dead_bush": "<PERSON> anwụ anwụ", "block.minecraft.dead_fire_coral": "Coral Nwụr<PERSON> nw<PERSON>", "block.minecraft.dead_fire_coral_block": "Ugbo nke coral oku ọ<PERSON>", "block.minecraft.dead_fire_coral_fan": "Nw<PERSON><PERSON><PERSON> Ọkụ Nkume coral", "block.minecraft.dead_fire_coral_wall_fan": "<PERSON>w<PERSON><PERSON><PERSON> Fire Coral Wall Fan", "block.minecraft.dead_horn_coral": "Coral nw<PERSON><PERSON><PERSON> anwụ nwụr<PERSON>", "block.minecraft.dead_horn_coral_block": "Ugbo nke coral oku nwere mpi", "block.minecraft.dead_horn_coral_fan": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dead_horn_coral_wall_fan": "<PERSON><PERSON><PERSON><PERSON> Horn Coral Wall Fan", "block.minecraft.dead_tube_coral": "Coral nw<PERSON><PERSON><PERSON> anwụ nwụr<PERSON>", "block.minecraft.dead_tube_coral_block": "Ugbo nke coral tubular", "block.minecraft.dead_tube_coral_fan": "<PERSON><PERSON><PERSON><PERSON><PERSON> tube Nkume coral", "block.minecraft.dead_tube_coral_wall_fan": "Nw<PERSON><PERSON><PERSON> tube Coral Wall Fan", "block.minecraft.decorated_pot": "Decorated Pot", "block.minecraft.deepslate": "<PERSON><PERSON><PERSON> miri emi", "block.minecraft.deepslate_brick_slab": "Mbadamba nkume brik", "block.minecraft.deepslate_brick_stairs": "Deepslate Brick steepụ", "block.minecraft.deepslate_brick_wall": "Mgbidi brik dị omimi", "block.minecraft.deepslate_bricks": "<PERSON><PERSON> nd<PERSON> g<PERSON>", "block.minecraft.deepslate_coal_ore": "Ngwurug<PERSON>", "block.minecraft.deepslate_copper_ore": "Deepslate Ọla kọpa", "block.minecraft.deepslate_diamond_ore": "Deepslate ọla edo", "block.minecraft.deepslate_emerald_ore": "Okwute miri emi emerald ore", "block.minecraft.deepslate_gold_ore": "Igbochi. minecraft. okwa_nwachukwu_nke", "block.minecraft.deepslate_iron_ore": "Igbochi. minecraft. okwa_nwagbara", "block.minecraft.deepslate_lapis_ore": "Okwute miri emi lapis lazuli ore", "block.minecraft.deepslate_redstone_ore": "Deepslate Redstone Ore", "block.minecraft.deepslate_tile_slab": "Mbadamba Tile nke Deepslate", "block.minecraft.deepslate_tile_stairs": "Akwa steepụ dị omimi", "block.minecraft.deepslate_tile_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.deepslate_tiles": "Taịlị Deepslate", "block.minecraft.detector_rail": "Zọ nchọpụta Detector", "block.minecraft.diamond_block": "Ngọngọ nke Diamond", "block.minecraft.diamond_ore": "Diamond Ore", "block.minecraft.diorite": "Diorite", "block.minecraft.diorite_slab": "Diorite slab", "block.minecraft.diorite_stairs": "Diorite steepụ", "block.minecraft.diorite_wall": "Mgbidi Diorite", "block.minecraft.dirt": "<PERSON><PERSON>", "block.minecraft.dirt_path": "Ụzọ unyi", "block.minecraft.dispenser": "Dispensin", "block.minecraft.dragon_egg": "Akwa Egg", "block.minecraft.dragon_head": "<PERSON><PERSON>", "block.minecraft.dragon_wall_head": "Isi mgbidi Dragon", "block.minecraft.dried_ghast": "<PERSON><PERSON>", "block.minecraft.dried_kelp_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.dripstone_block": "Oghere Dripstone", "block.minecraft.dropper": "<PERSON><PERSON><PERSON>", "block.minecraft.emerald_block": "Ngọngọ nke Emerald", "block.minecraft.emerald_ore": "Emerald Ore", "block.minecraft.enchanting_table": "<PERSON><PERSON>", "block.minecraft.end_gateway": "Ọnụ <PERSON>z<PERSON> nke ọgwụgwụ", "block.minecraft.end_portal": "<PERSON><PERSON><PERSON> Ọgwụgwụ", "block.minecraft.end_portal_frame": "Njedebe Portal Ọgwụgwụ", "block.minecraft.end_rod": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.end_stone": "Ọgwụgwụ <PERSON><PERSON><PERSON>", "block.minecraft.end_stone_brick_slab": "End Stone brik slab", "block.minecraft.end_stone_brick_stairs": "End Stone brik steepụ", "block.minecraft.end_stone_brick_wall": "Ọgwụgwụ <PERSON><PERSON><PERSON> brik", "block.minecraft.end_stone_bricks": "Ọgwụgwụ <PERSON><PERSON>me brik", "block.minecraft.ender_chest": "<PERSON><PERSON>", "block.minecraft.exposed_chiseled_copper": "Exposed Chiseled Copper", "block.minecraft.exposed_copper": "Ọla kọpa kpughere", "block.minecraft.exposed_copper_bulb": "Exposed Copper Bulb", "block.minecraft.exposed_copper_door": "Exposed Copper Door", "block.minecraft.exposed_copper_grate": "Exposed Copper Grate", "block.minecraft.exposed_copper_trapdoor": "Exposed Copper Trapdoor", "block.minecraft.exposed_cut_copper": "Ekpughere Ọla kọpa", "block.minecraft.exposed_cut_copper_slab": "Egosipụtara Cut Okpokoro Ọla kọpa", "block.minecraft.exposed_cut_copper_stairs": "Egosipụtara Cut steepụ <PERSON> k<PERSON>", "block.minecraft.farmland": "<PERSON>a oru ubi", "block.minecraft.fern": "Fern", "block.minecraft.fire": "Ọkụ", "block.minecraft.fire_coral": "<PERSON>", "block.minecraft.fire_coral_block": "<PERSON><PERSON>ere nke coral ọ<PERSON>", "block.minecraft.fire_coral_fan": "A na-enwe coral <PERSON>ụ", "block.minecraft.fire_coral_wall_fan": "Fire Coral Mgbidi Fan", "block.minecraft.firefly_bush": "Firefly Bush", "block.minecraft.fletching_table": "Isiokwu Fletching", "block.minecraft.flower_pot": "Ite ifuru", "block.minecraft.flowering_azalea": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.flowering_azalea_leaves": "Ifuru Azalea Ifuru", "block.minecraft.frogspawn": "Elespan", "block.minecraft.frosted_ice": "Frosted Ice", "block.minecraft.furnace": "Furnace", "block.minecraft.gilded_blackstone": "<PERSON><PERSON> ojii gilded", "block.minecraft.glass": "<PERSON><PERSON>", "block.minecraft.glass_pane": "<PERSON><PERSON>", "block.minecraft.glow_lichen": "Glow Lichen d<PERSON>cha", "block.minecraft.glowstone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gold_block": "Ngọngọ nke Gold", "block.minecraft.gold_ore": "Gold ọlaedo", "block.minecraft.granite": "Granite", "block.minecraft.granite_slab": "Granite slab", "block.minecraft.granite_stairs": "Granite steepụ", "block.minecraft.granite_wall": "Mgbidi Granite", "block.minecraft.grass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.grass_block": "<PERSON><PERSON><PERSON>", "block.minecraft.gravel": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_banner": "Agba ntụ Pụ<PERSON>", "block.minecraft.gray_bed": "Agba ntụ Ndina", "block.minecraft.gray_candle": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.gray_candle_cake": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "block.minecraft.gray_carpet": "Kapeeti agba ntụ", "block.minecraft.gray_concrete": "Agba ntụ lhe", "block.minecraft.gray_concrete_powder": "Agba ntụ lhe Ntụ ntụ", "block.minecraft.gray_glazed_terracotta": "Grey Glazed Terracotta", "block.minecraft.gray_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.gray_stained_glass": "<PERSON><PERSON> na agba", "block.minecraft.gray_stained_glass_pane": "Grey stained Glass Pane", "block.minecraft.gray_terracotta": "Grey Terracotta", "block.minecraft.gray_wool": "<PERSON><PERSON> a<PERSON>", "block.minecraft.green_banner": "Ọkọlọtọ Agba akwụkwọ ndụ", "block.minecraft.green_bed": "Ndina Agba akwụkwọ ndụ", "block.minecraft.green_candle": "<PERSON>nd<PERSON><PERSON>", "block.minecraft.green_candle_cake": "<PERSON><PERSON><PERSON> na <PERSON> kand<PERSON>l", "block.minecraft.green_carpet": "Kapeeti Green", "block.minecraft.green_concrete": "Agba akwụkwọ ndụ <PERSON>he", "block.minecraft.green_concrete_powder": "Agba akwụkwọ ndụ lhe Ntụ ntụ", "block.minecraft.green_glazed_terracotta": "Green gbara agba Terracotta", "block.minecraft.green_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.green_stained_glass": "Gilasi <PERSON> na-agba agba", "block.minecraft.green_stained_glass_pane": "Green kwadoro Glass ebi ndụ", "block.minecraft.green_terracotta": "Green Terracotta", "block.minecraft.green_wool": "<PERSON><PERSON><PERSON>", "block.minecraft.grindstone": "Okpokoro", "block.minecraft.hanging_roots": "Mgbukpọ Eko", "block.minecraft.hay_block": "<PERSON>", "block.minecraft.heavy_core": "Heavy Core", "block.minecraft.heavy_weighted_pressure_plate": "Plate Predictive Ebu Arọ Juru", "block.minecraft.honey_block": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.honeycomb_block": "Ngọngọ mmanụ a <PERSON>ụ", "block.minecraft.hopper": "<PERSON>", "block.minecraft.horn_coral": "Coral nwere mpi", "block.minecraft.horn_coral_block": "Ugbo nke coral oku nwere mpi", "block.minecraft.horn_coral_fan": "A na-enwe coral mpi", "block.minecraft.horn_coral_wall_fan": "<PERSON><PERSON> <PERSON>", "block.minecraft.ice": "Plain Ice", "block.minecraft.infested_chiseled_stone_bricks": "<PERSON>k<PERSON><PERSON> nkume a pịrị apị", "block.minecraft.infested_cobblestone": "Pavement Awara", "block.minecraft.infested_cracked_stone_bricks": "Akpụ nkume ndị gbawara ag<PERSON>wa", "block.minecraft.infested_deepslate": "Deepslate jup<PERSON>", "block.minecraft.infested_mossy_stone_bricks": "<PERSON><PERSON><PERSON><PERSON> brik bred na-<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.infested_stone": "<PERSON><PERSON><PERSON>", "block.minecraft.infested_stone_bricks": "Akpụ brik na-agbazi", "block.minecraft.iron_bars": "<PERSON><PERSON><PERSON>", "block.minecraft.iron_block": "Ngọngọ nke Iron", "block.minecraft.iron_door": "N'Ọnụ Ironzọ", "block.minecraft.iron_ore": "Ígwè ore", "block.minecraft.iron_trapdoor": "Tragwè Trapdoor", "block.minecraft.jack_o_lantern": "<PERSON>'<PERSON>", "block.minecraft.jigsaw": "Ngọngọ Jigsaw", "block.minecraft.jukebox": "Jukebox", "block.minecraft.jungle_button": "<PERSON><PERSON>", "block.minecraft.jungle_door": "Ogige Jungle", "block.minecraft.jungle_fence": "<PERSON> ngere", "block.minecraft.jungle_fence_gate": "Ọnụ Fzọ encemá Ọhịa", "block.minecraft.jungle_hanging_sign": "Jungle Hanging Sign", "block.minecraft.jungle_leaves": "<PERSON><PERSON> Oke Osimiri", "block.minecraft.jungle_log": "Abanye na Ubi", "block.minecraft.jungle_planks": "<PERSON><PERSON>", "block.minecraft.jungle_pressure_plate": "Plate Strike Ubi", "block.minecraft.jungle_sapling": "Sapling Jungle", "block.minecraft.jungle_sign": "Nbanye Ala", "block.minecraft.jungle_slab": "<PERSON><PERSON>", "block.minecraft.jungle_stairs": "<PERSON><PERSON>", "block.minecraft.jungle_trapdoor": "<PERSON><PERSON>", "block.minecraft.jungle_wall_hanging_sign": "Jungle Wall Hanging Sign", "block.minecraft.jungle_wall_sign": "<PERSON><PERSON> ama nke <PERSON>", "block.minecraft.jungle_wood": "Osisi Jungle", "block.minecraft.kelp": "<PERSON><PERSON><PERSON>", "block.minecraft.kelp_plant": "<PERSON><PERSON><PERSON>", "block.minecraft.ladder": "Ladder", "block.minecraft.lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.lapis_block": "<PERSON><PERSON> nke <PERSON>", "block.minecraft.lapis_ore": "Lapis <PERSON> Ore", "block.minecraft.large_amethyst_bud": "Nnukwu Amethyst Bud", "block.minecraft.large_fern": "Nnukwu Fern", "block.minecraft.lava": "<PERSON><PERSON>", "block.minecraft.lava_cauldron": "<PERSON><PERSON>", "block.minecraft.leaf_litter": "Leaf Litter", "block.minecraft.lectern": "<PERSON><PERSON><PERSON>", "block.minecraft.lever": "Lever", "block.minecraft.light": "Mgbochi. minecraft. ọkụ", "block.minecraft.light_blue_banner": "Ìhè <PERSON><PERSON> Pụ<PERSON>", "block.minecraft.light_blue_bed": "Ìhè Bulu Ndina", "block.minecraft.light_blue_candle": "Light Blue kandụl", "block.minecraft.light_blue_candle_cake": "Achịcha na kandụl na -acha anụn<PERSON> anụn<PERSON>", "block.minecraft.light_blue_carpet": "<PERSON><PERSON><PERSON> na-acha odo odo", "block.minecraft.light_blue_concrete": "Ìhè Blue lhe", "block.minecraft.light_blue_concrete_powder": "Ìhè Blue lhe Ntụ ntụ", "block.minecraft.light_blue_glazed_terracotta": "Magenta Glazed Terracotta", "block.minecraft.light_blue_shulker_box": "Ìhè <PERSON> Shulker Igbe", "block.minecraft.light_blue_stained_glass": "Glass Lightined Glass", "block.minecraft.light_blue_stained_glass_pane": "Light Light stained Glass Pane", "block.minecraft.light_blue_terracotta": "Ìhè Blue Terracotta", "block.minecraft.light_blue_wool": "Uwe Blue Blue", "block.minecraft.light_gray_banner": "Ìhè <PERSON>", "block.minecraft.light_gray_bed": "Ìhè Agba ntụ Ndina", "block.minecraft.light_gray_candle": "Light <PERSON> kandụl", "block.minecraft.light_gray_candle_cake": "Achịcha na kandụl ọkụ ọ<PERSON>ụ", "block.minecraft.light_gray_carpet": "Carpet Light", "block.minecraft.light_gray_concrete": "Ìhè A<PERSON><PERSON>", "block.minecraft.light_gray_concrete_powder": "Ìhè Agba hle Ntụ ntụ", "block.minecraft.light_gray_glazed_terracotta": "Ìhè Agba Glazed Terracotta", "block.minecraft.light_gray_shulker_box": "Ìhè <PERSON>be", "block.minecraft.light_gray_stained_glass": "Glass nke agba agba agba", "block.minecraft.light_gray_stained_glass_pane": "Light Grey stained Glass Pane", "block.minecraft.light_gray_terracotta": "Ìhè Gray Terracotta", "block.minecraft.light_gray_wool": "Ìhè Gray ajị", "block.minecraft.light_weighted_pressure_plate": "Plate Predictive Light", "block.minecraft.lightning_rod": "<PERSON><PERSON><PERSON>", "block.minecraft.lilac": "Lilac", "block.minecraft.lily_of_the_valley": "<PERSON> nke n<PERSON>", "block.minecraft.lily_pad": "<PERSON>", "block.minecraft.lime_banner": "<PERSON><PERSON>", "block.minecraft.lime_bed": "Way<PERSON>", "block.minecraft.lime_candle": "<PERSON><PERSON>", "block.minecraft.lime_candle_cake": "Achịcha na kandụl Wayo", "block.minecraft.lime_carpet": "<PERSON><PERSON>", "block.minecraft.lime_concrete": "Oroma <PERSON><PERSON>", "block.minecraft.lime_concrete_powder": "Oroma nkịrịsị lhe Ntụ ntụ", "block.minecraft.lime_glazed_terracotta": "Wayo Glazed Terracotta", "block.minecraft.lime_shulker_box": "<PERSON><PERSON>", "block.minecraft.lime_stained_glass": "<PERSON><PERSON>", "block.minecraft.lime_stained_glass_pane": "<PERSON><PERSON>", "block.minecraft.lime_terracotta": "Wayo Terracotta", "block.minecraft.lime_wool": "<PERSON><PERSON>", "block.minecraft.lodestone": "Lodestone", "block.minecraft.loom": "Loom", "block.minecraft.magenta_banner": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_bed": "Magenta Ndina", "block.minecraft.magenta_candle": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_candle_cake": "Achịcha na kandụl Magenta", "block.minecraft.magenta_carpet": "Ka<PERSON><PERSON>", "block.minecraft.magenta_concrete": "<PERSON><PERSON>a lhu", "block.minecraft.magenta_concrete_powder": "Magenta lhe Ntụ ntụ", "block.minecraft.magenta_glazed_terracotta": "Magenta Glazed Terracotta", "block.minecraft.magenta_shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.magenta_stained_glass": "<PERSON><PERSON> n<PERSON>", "block.minecraft.magenta_stained_glass_pane": "<PERSON><PERSON><PERSON> akara e<PERSON>", "block.minecraft.magenta_terracotta": "Magenta Terracotta", "block.minecraft.magenta_wool": "Magenta Wool", "block.minecraft.magma_block": "Magma Ngọngọ", "block.minecraft.mangrove_button": "<PERSON>ọ<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_door": "Ọnụ ụzọ Mangrove", "block.minecraft.mangrove_fence": "Mgbidi nke Mangrove", "block.minecraft.mangrove_fence_gate": "Ọnụ <PERSON>z<PERSON> ngere Mangrove", "block.minecraft.mangrove_hanging_sign": "Mangrove Hanging Sign", "block.minecraft.mangrove_leaves": "Akwụkwọ osisi Mangrove", "block.minecraft.mangrove_log": "Akwụkwọ akụkọ Mangrove", "block.minecraft.mangrove_planks": "Osisi Mangrove", "block.minecraft.mangrove_pressure_plate": "<PERSON><PERSON><PERSON>ụ<PERSON>", "block.minecraft.mangrove_propagule": "Mangrove Propagule", "block.minecraft.mangrove_roots": "Mgb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.mangrove_sign": "Akara Mangrove", "block.minecraft.mangrove_slab": "Osisi Mangrove", "block.minecraft.mangrove_stairs": "Mangrove steepụ", "block.minecraft.mangrove_trapdoor": "Ọnụ ụzọ Mangrove", "block.minecraft.mangrove_wall_hanging_sign": "Mangrove Wall Hanging Sign", "block.minecraft.mangrove_wall_sign": "Akara mgbidi Mangrove", "block.minecraft.mangrove_wood": "Osisi Mangrove", "block.minecraft.medium_amethyst_bud": "Ọkara Amethyst Bud", "block.minecraft.melon": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.melon_stem": "<PERSON><PERSON><PERSON>", "block.minecraft.moss_block": "Akpaetu ngọngọ", "block.minecraft.moss_carpet": "<PERSON>", "block.minecraft.mossy_cobblestone": "<PERSON><PERSON>ment", "block.minecraft.mossy_cobblestone_slab": "<PERSON><PERSON> Slab", "block.minecraft.mossy_cobblestone_stairs": "Mossy Cobb<PERSON>tone steepụ", "block.minecraft.mossy_cobblestone_wall": "<PERSON><PERSON>", "block.minecraft.mossy_stone_brick_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_brick_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_brick_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.mossy_stone_bricks": "<PERSON><PERSON><PERSON>", "block.minecraft.moving_piston": "Ogba Aghara <PERSON>", "block.minecraft.mud": "<PERSON><PERSON>", "block.minecraft.mud_brick_slab": "<PERSON>i brik ap<PERSON>", "block.minecraft.mud_brick_stairs": "Ugboro steepụ brik", "block.minecraft.mud_brick_wall": "<PERSON><PERSON><PERSON><PERSON> brik <PERSON>", "block.minecraft.mud_bricks": "<PERSON><PERSON> <PERSON>", "block.minecraft.muddy_mangrove_roots": "Mgb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Mangrove Muddy", "block.minecraft.mushroom_stem": "<PERSON><PERSON>", "block.minecraft.mycelium": "Mycelium", "block.minecraft.nether_brick_fence": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_brick_slab": "Nether Brick Slab", "block.minecraft.nether_brick_stairs": "Ebube Brick Nether", "block.minecraft.nether_brick_wall": "<PERSON><PERSON> brik", "block.minecraft.nether_bricks": "Nether Bricks", "block.minecraft.nether_gold_ore": "Nether Gold Ore", "block.minecraft.nether_portal": "Nether Portal", "block.minecraft.nether_quartz_ore": "<PERSON><PERSON><PERSON>", "block.minecraft.nether_sprouts": "Nkọwapụta Netherland", "block.minecraft.nether_wart": "Nether Wart", "block.minecraft.nether_wart_block": "Ebe Wart Block", "block.minecraft.netherite_block": "Ogwe nke Netherite", "block.minecraft.netherrack": "Netherrack", "block.minecraft.note_block": "Rịba ama Block", "block.minecraft.oak_button": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_door": "Oak Orzọ", "block.minecraft.oak_fence": "Oak nsu", "block.minecraft.oak_fence_gate": "Ọnụ ụzọ ámá Oak", "block.minecraft.oak_hanging_sign": "Oak Hanging Sign", "block.minecraft.oak_leaves": "Oak doo", "block.minecraft.oak_log": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_planks": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_pressure_plate": "Akpu Plate Stress", "block.minecraft.oak_sapling": "Oak Sapling", "block.minecraft.oak_sign": "Oak akara", "block.minecraft.oak_slab": "Akpu Slab", "block.minecraft.oak_stairs": "Akpu <PERSON>", "block.minecraft.oak_trapdoor": "<PERSON><PERSON><PERSON>", "block.minecraft.oak_wall_hanging_sign": "Oak Wall Hanging Sign", "block.minecraft.oak_wall_sign": "Oak Wall Sign", "block.minecraft.oak_wood": "Osisi Oak", "block.minecraft.observer": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.obsidian": "Obsidian", "block.minecraft.ochre_froglight": "<PERSON><PERSON>", "block.minecraft.ominous_banner": "Akpata Pụ<PERSON>", "block.minecraft.open_eyeblossom": "Open Eyeblossom", "block.minecraft.orange_banner": "Ọkọlọtọ oroma", "block.minecraft.orange_bed": "Ndina oroma", "block.minecraft.orange_candle": "<PERSON><PERSON><PERSON><PERSON> or<PERSON>", "block.minecraft.orange_candle_cake": "Achịcha na kandụl oroma", "block.minecraft.orange_carpet": "<PERSON><PERSON><PERSON> oroma", "block.minecraft.orange_concrete": "Oroma lhe ", "block.minecraft.orange_concrete_powder": "or<PERSON> l<PERSON>tụ ntụ", "block.minecraft.orange_glazed_terracotta": "Oroma Glazed Terracotta", "block.minecraft.orange_shulker_box": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.orange_stained_glass": "Iko na agba oroma", "block.minecraft.orange_stained_glass_pane": "Glass Pane Oroma", "block.minecraft.orange_terracotta": "Orange Terracotta", "block.minecraft.orange_tulip": "<PERSON><PERSON>", "block.minecraft.orange_wool": "Oroma Oroma", "block.minecraft.oxeye_daisy": "Oxeye Daisy", "block.minecraft.oxidized_chiseled_copper": "Oxidized Chiseled Copper", "block.minecraft.oxidized_copper": "Ọla kọpa Oxidized", "block.minecraft.oxidized_copper_bulb": "Oxidized Copper Bulb", "block.minecraft.oxidized_copper_door": "Oxidized Copper Door", "block.minecraft.oxidized_copper_grate": "Oxidized Copper Grate", "block.minecraft.oxidized_copper_trapdoor": "Oxidized Copper Trapdoor", "block.minecraft.oxidized_cut_copper": "Ọla kọpa Oxidized", "block.minecraft.oxidized_cut_copper_slab": "Okpukpo <PERSON> a kpụrụ akpụ", "block.minecraft.oxidized_cut_copper_stairs": "Oxidized Bee ọla kọpa steepụ", "block.minecraft.packed_ice": "Packed Ice", "block.minecraft.packed_mud": "Mud juru n'ọn<PERSON>", "block.minecraft.pale_hanging_moss": "<PERSON><PERSON>", "block.minecraft.pale_moss_block": "Pale Moss Block", "block.minecraft.pale_moss_carpet": "<PERSON><PERSON> Moss Carpet", "block.minecraft.pale_oak_button": "Pale Oak Button", "block.minecraft.pale_oak_door": "Pale Oak Door", "block.minecraft.pale_oak_fence": "Pale Oak Fence", "block.minecraft.pale_oak_fence_gate": "Pale Oak Fence Gate", "block.minecraft.pale_oak_hanging_sign": "Pale Oak Hanging Sign", "block.minecraft.pale_oak_leaves": "Pale Oak Leaves", "block.minecraft.pale_oak_log": "Pale Oak Log", "block.minecraft.pale_oak_planks": "Pale Oak Planks", "block.minecraft.pale_oak_pressure_plate": "Pale Oak Pressure Plate", "block.minecraft.pale_oak_sapling": "Pale Oak Sapling", "block.minecraft.pale_oak_sign": "Pale Oak Sign", "block.minecraft.pale_oak_slab": "Pale Oak Slab", "block.minecraft.pale_oak_stairs": "Pale Oak Stairs", "block.minecraft.pale_oak_trapdoor": "Pale Oak Trapdoor", "block.minecraft.pale_oak_wall_hanging_sign": "Pale Oak Wall Hanging Sign", "block.minecraft.pale_oak_wall_sign": "Pale Oak Wall Sign", "block.minecraft.pale_oak_wood": "Pale Oak Wood", "block.minecraft.pearlescent_froglight": "<PERSON><PERSON><PERSON> ele ih<PERSON>", "block.minecraft.peony": "Peony", "block.minecraft.petrified_oak_slab": "<PERSON>-ama ji<PERSON><PERSON>", "block.minecraft.piglin_head": "<PERSON><PERSON>", "block.minecraft.piglin_wall_head": "<PERSON><PERSON>", "block.minecraft.pink_banner": "Pink Pụ<PERSON>", "block.minecraft.pink_bed": "Pink Ndina", "block.minecraft.pink_candle": "Kand<PERSON><PERSON>", "block.minecraft.pink_candle_cake": "Achịcha na kandụl Pink", "block.minecraft.pink_carpet": "Kapeeti Pink", "block.minecraft.pink_concrete": "Pink Concrete lhe", "block.minecraft.pink_concrete_powder": "Pink lhe Ntụ ntụ", "block.minecraft.pink_glazed_terracotta": "Wayo Glazed Terracotta", "block.minecraft.pink_petals": "Pink Petals", "block.minecraft.pink_shulker_box": "<PERSON> Shulker <PERSON>be", "block.minecraft.pink_stained_glass": "<PERSON><PERSON>", "block.minecraft.pink_stained_glass_pane": "Pink Stained Glass Pane", "block.minecraft.pink_terracotta": "Pink Terracotta", "block.minecraft.pink_tulip": "<PERSON> Tulip", "block.minecraft.pink_wool": "<PERSON> aj<PERSON>", "block.minecraft.piston": "<PERSON><PERSON><PERSON>", "block.minecraft.piston_head": "Piston isi", "block.minecraft.pitcher_crop": "Pitcher C<PERSON>", "block.minecraft.pitcher_plant": "Pitcher Plant", "block.minecraft.player_head": "Isi player", "block.minecraft.player_head.named": "%s's <PERSON><PERSON>", "block.minecraft.player_wall_head": "Okpokoro player", "block.minecraft.podzol": "Podzol", "block.minecraft.pointed_dripstone": "Akara Dripstone", "block.minecraft.polished_andesite": "Andesite a na-egbu egbu", "block.minecraft.polished_andesite_slab": "Nkume Polish Andesite", "block.minecraft.polished_andesite_stairs": "Ebube a na-egbu mara mma nke Andesite", "block.minecraft.polished_basalt": "Basalt Polured", "block.minecraft.polished_blackstone": "<PERSON><PERSON> ojii", "block.minecraft.polished_blackstone_brick_slab": "Blackstone brick Slab", "block.minecraft.polished_blackstone_brick_stairs": "Ntọala Blackstone Brick", "block.minecraft.polished_blackstone_brick_wall": "Nnukwu mg<PERSON>i ojii", "block.minecraft.polished_blackstone_bricks": "<PERSON><PERSON><PERSON><PERSON><PERSON> ojii", "block.minecraft.polished_blackstone_button": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>i", "block.minecraft.polished_blackstone_pressure_plate": "Ọkpụkpụ <PERSON><PERSON>ri Blackstone rụpụtara", "block.minecraft.polished_blackstone_slab": "Blackstone Slab", "block.minecraft.polished_blackstone_stairs": "Nzọ ọkwa Blackstone", "block.minecraft.polished_blackstone_wall": "<PERSON><PERSON><PERSON><PERSON> ojii ojii", "block.minecraft.polished_deepslate": "Deepslate na -egbu maramara", "block.minecraft.polished_deepslate_slab": "Nkịtị Deepslate", "block.minecraft.polished_deepslate_stairs": "Steepụ Deepslate mara mma", "block.minecraft.polished_deepslate_wall": "Mgbidi Deepslate mara mma", "block.minecraft.polished_diorite": "Diorite gbagoro", "block.minecraft.polished_diorite_slab": "Egbu maramara Diorite slab", "block.minecraft.polished_diorite_stairs": "Ebube Diorite steepụ", "block.minecraft.polished_granite": "Granite mara mma", "block.minecraft.polished_granite_slab": "Ebube Granite Slab", "block.minecraft.polished_granite_stairs": "Ebube Granite steepụ", "block.minecraft.polished_tuff": "Polished <PERSON>", "block.minecraft.polished_tuff_slab": "Polished <PERSON><PERSON>", "block.minecraft.polished_tuff_stairs": "Polished <PERSON><PERSON>", "block.minecraft.polished_tuff_wall": "Polished <PERSON><PERSON>", "block.minecraft.poppy": "<PERSON><PERSON>", "block.minecraft.potatoes": "<PERSON><PERSON><PERSON>", "block.minecraft.potted_acacia_sapling": "Acacia na-amị mkpụrụ na ite", "block.minecraft.potted_allium": "Garlic n'ime ite", "block.minecraft.potted_azalea_bush": "Azalea g<PERSON>", "block.minecraft.potted_azure_bluet": "Blue Houstonia n'ime ite", "block.minecraft.potted_bamboo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.potted_birch_sapling": "Osisi ọka birch n'ime ite", "block.minecraft.potted_blue_orchid": "Blue orchid na ite", "block.minecraft.potted_brown_mushroom": "Aja aja toadstool n'ime ite", "block.minecraft.potted_cactus": "Cactus n'ime ite", "block.minecraft.potted_cherry_sapling": "Potted Cherry Sapling", "block.minecraft.potted_closed_eyeblossom": "Potted Closed Eyeblossom", "block.minecraft.potted_cornflower": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ọka", "block.minecraft.potted_crimson_fungus": "Potted Crimson Fungus", "block.minecraft.potted_crimson_roots": "Potted Crimson mgb<PERSON><PERSON>", "block.minecraft.potted_dandelion": "Dandelion n'ime ite", "block.minecraft.potted_dark_oak_sapling": "Okpu Oak seedling n'ime ite", "block.minecraft.potted_dead_bush": "Osisi a kpọrọ nkụ n'ime ite", "block.minecraft.potted_fern": "<PERSON>uru n'ime ite", "block.minecraft.potted_flowering_azalea_bush": "<PERSON><PERSON><PERSON> osisi Azalea", "block.minecraft.potted_jungle_sapling": "<PERSON><PERSON> ahihia n'ime ite", "block.minecraft.potted_lily_of_the_valley": "Kpụrụ <PERSON>", "block.minecraft.potted_mangrove_propagule": "Osisi Mangrove Propagule", "block.minecraft.potted_oak_sapling": "Oak seedling n'ime ite", "block.minecraft.potted_open_eyeblossom": "Potted Open Eyeblossom", "block.minecraft.potted_orange_tulip": "Orange tulip n'ime ite", "block.minecraft.potted_oxeye_daisy": "<PERSON><PERSON> n'ime ite", "block.minecraft.potted_pale_oak_sapling": "Potted Pale Oak Sapling", "block.minecraft.potted_pink_tulip": "Pink tulip n'ime ite", "block.minecraft.potted_poppy": "Poppy n'ime ite", "block.minecraft.potted_red_mushroom": "Toadstool n'ime ite", "block.minecraft.potted_red_tulip": "Utu tulip n'ime ite", "block.minecraft.potted_spruce_sapling": "Ghaa mkpụ<PERSON><PERSON> osisi na ite", "block.minecraft.potted_torchflower": "Potted Torchflower", "block.minecraft.potted_warped_fungus": "Fungus ak<PERSON> akp<PERSON>", "block.minecraft.potted_warped_roots": "Mgburugwu Akpọrọ akpụ", "block.minecraft.potted_white_tulip": "White tulip na ite", "block.minecraft.potted_wither_rose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.powder_snow": "<PERSON>tụ ntụ Snow", "block.minecraft.powder_snow_cauldron": "Ntụ ntụ Snow Cauldron", "block.minecraft.powered_rail": "Ilgbọ okporo ígwè kwadoro", "block.minecraft.prismarine": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.prismarine_brick_stairs": "Biriiki P<PERSON>ari<PERSON>", "block.minecraft.prismarine_bricks": "<PERSON><PERSON>", "block.minecraft.prismarine_slab": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_stairs": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.prismarine_wall": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.pumpkin": "Ugu", "block.minecraft.pumpkin_stem": "<PERSON><PERSON>", "block.minecraft.purple_banner": "Pụrụ Iche Pụrụ Iche", "block.minecraft.purple_bed": "<PERSON><PERSON>", "block.minecraft.purple_candle": "Kand<PERSON>l na -acha odo odo", "block.minecraft.purple_candle_cake": "<PERSON><PERSON><PERSON> na -acha odo odo kand<PERSON>l", "block.minecraft.purple_carpet": "Purple Carpet", "block.minecraft.purple_concrete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.purple_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON> lhe Ntụ ntụ", "block.minecraft.purple_glazed_terracotta": "<PERSON><PERSON><PERSON><PERSON><PERSON> Glazed Terracotta", "block.minecraft.purple_shulker_box": "<PERSON>gbe <PERSON>", "block.minecraft.purple_stained_glass": "<PERSON><PERSON> nke Nwere Ifuru", "block.minecraft.purple_stained_glass_pane": "<PERSON><PERSON><PERSON> nke Pụ<PERSON>", "block.minecraft.purple_terracotta": "<PERSON>ọ<PERSON><PERSON>l <PERSON>cott<PERSON>", "block.minecraft.purple_wool": "<PERSON><PERSON>", "block.minecraft.purpur_block": "Block Purpur", "block.minecraft.purpur_pillar": "Purpur Ogidi", "block.minecraft.purpur_slab": "Pur<PERSON> slab", "block.minecraft.purpur_stairs": "Steepụ Purpur", "block.minecraft.quartz_block": "Ngọngọ nke Quartz", "block.minecraft.quartz_bricks": "Nkịt<PERSON>", "block.minecraft.quartz_pillar": "Ogidi quartz", "block.minecraft.quartz_slab": "Quartz Slab", "block.minecraft.quartz_stairs": "Quartz steepụ", "block.minecraft.rail": "<PERSON><PERSON><PERSON> elu", "block.minecraft.raw_copper_block": "Ngọngọ nke <PERSON>", "block.minecraft.raw_gold_block": "Ngọngọ nke ọla edo Raw", "block.minecraft.raw_iron_block": "Mkpọ<PERSON> nke Irongwè", "block.minecraft.red_banner": "<PERSON><PERSON>", "block.minecraft.red_bed": "<PERSON><PERSON>", "block.minecraft.red_candle": "<PERSON><PERSON><PERSON><PERSON> uhie", "block.minecraft.red_candle_cake": "Achicha na Red kandụl", "block.minecraft.red_carpet": "<PERSON><PERSON><PERSON>", "block.minecraft.red_concrete": "uhie uhie ihe", "block.minecraft.red_concrete_powder": "<PERSON><PERSON> lhe Ntụ ntụ", "block.minecraft.red_glazed_terracotta": "Green gbara agba Terracotta", "block.minecraft.red_mushroom": "Toadstool", "block.minecraft.red_mushroom_block": "Red Ero <PERSON>", "block.minecraft.red_nether_brick_slab": "<PERSON>ie uhie Nether Brick Slab", "block.minecraft.red_nether_brick_stairs": "<PERSON><PERSON> brik", "block.minecraft.red_nether_brick_wall": "<PERSON><PERSON> brik <PERSON>", "block.minecraft.red_nether_bricks": "<PERSON>", "block.minecraft.red_sand": "<PERSON>ie uhie", "block.minecraft.red_sandstone": "Red Sandstone", "block.minecraft.red_sandstone_slab": "Nkume Red Sandstone", "block.minecraft.red_sandstone_stairs": "<PERSON><PERSON>", "block.minecraft.red_sandstone_wall": "Red Sandstone Mgbidi", "block.minecraft.red_shulker_box": "<PERSON> Shulker <PERSON>be", "block.minecraft.red_stained_glass": "<PERSON><PERSON> na-acha uhie uhie", "block.minecraft.red_stained_glass_pane": "<PERSON><PERSON>dor<PERSON>", "block.minecraft.red_terracotta": "<PERSON><PERSON>", "block.minecraft.red_tulip": "<PERSON><PERSON>", "block.minecraft.red_wool": "<PERSON><PERSON> uwe an<PERSON>", "block.minecraft.redstone_block": "Ngọngọ nke Emerald", "block.minecraft.redstone_lamp": "Redstone Oriọna", "block.minecraft.redstone_ore": "Redstone Ore", "block.minecraft.redstone_torch": "Osimiri Redstone", "block.minecraft.redstone_wall_torch": "<PERSON><PERSON> Mgbidi", "block.minecraft.redstone_wire": "Redstone Waya", "block.minecraft.reinforced_deepslate": "Deepslate jup<PERSON>", "block.minecraft.repeater": "<PERSON><PERSON><PERSON>", "block.minecraft.repeating_command_block": "Ikwugharị Block iwu", "block.minecraft.resin_block": "Block of Resin", "block.minecraft.resin_brick_slab": "Resin Brick Slab", "block.minecraft.resin_brick_stairs": "Resin Brick Stairs", "block.minecraft.resin_brick_wall": "Resin Brick Wall", "block.minecraft.resin_bricks": "Resin Bricks", "block.minecraft.resin_clump": "<PERSON><PERSON>", "block.minecraft.respawn_anchor": "<PERSON><PERSON><PERSON><PERSON>aw<PERSON>", "block.minecraft.rooted_dirt": "Unyi gbanyere mkpọr<PERSON>w<PERSON>", "block.minecraft.rose_bush": "<PERSON>", "block.minecraft.sand": "<PERSON><PERSON>", "block.minecraft.sandstone": "<PERSON><PERSON><PERSON> a<PERSON>", "block.minecraft.sandstone_slab": "Okwute Sandstone", "block.minecraft.sandstone_stairs": "Ulo Sandstone", "block.minecraft.sandstone_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.scaffolding": "<PERSON><PERSON><PERSON><PERSON> ihu", "block.minecraft.sculk": "Sculk", "block.minecraft.sculk_catalyst": "Ọkpụkpọ anụ", "block.minecraft.sculk_sensor": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk_shrieker": "<PERSON><PERSON><PERSON>", "block.minecraft.sculk_vein": "Ọkpụkpụ akwara", "block.minecraft.sea_lantern": "<PERSON><PERSON><PERSON>", "block.minecraft.sea_pickle": "<PERSON><PERSON>", "block.minecraft.seagrass": "<PERSON><PERSON>", "block.minecraft.set_spawn": "Ntọala nrụpụta", "block.minecraft.short_dry_grass": "Short Dry Grass", "block.minecraft.short_grass": "Short Grass", "block.minecraft.shroomlight": "Ìhè", "block.minecraft.shulker_box": "<PERSON><PERSON><PERSON>", "block.minecraft.skeleton_skull": "Ọkpụkpụ okpokoro isi", "block.minecraft.skeleton_wall_skull": "Okpokoro skeleton", "block.minecraft.slime_block": "Mpempe slime", "block.minecraft.small_amethyst_bud": "Obere amethyst Bud", "block.minecraft.small_dripleaf": "<PERSON><PERSON><PERSON>", "block.minecraft.smithing_table": "Isiok<PERSON>", "block.minecraft.smoker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.smooth_basalt": "Basalt dị nro", "block.minecraft.smooth_quartz": "G<PERSON><PERSON>e <PERSON>z dị nro", "block.minecraft.smooth_quartz_slab": "Ezigbo Quartz Slab", "block.minecraft.smooth_quartz_stairs": "Ezigbo Quartz steepụ", "block.minecraft.smooth_red_sandstone": "Ezigbo Red Sandstone", "block.minecraft.smooth_red_sandstone_slab": "Ezigbo uhie uhie Sandstone Slab", "block.minecraft.smooth_red_sandstone_stairs": "Ezigbo Red Sandstone steepụ", "block.minecraft.smooth_sandstone": "Ezigbo Sandstone", "block.minecraft.smooth_sandstone_slab": "Ezigbo Sandstone Slab", "block.minecraft.smooth_sandstone_stairs": "Ezigbo Sandstone steepụ", "block.minecraft.smooth_stone": "Ezigbo Okute", "block.minecraft.smooth_stone_slab": "Nkume Na-agba Ọcha", "block.minecraft.sniffer_egg": "Sniffer Egg", "block.minecraft.snow": "Snoo", "block.minecraft.snow_block": "Ugbo snow", "block.minecraft.soul_campfire": "Mk<PERSON><PERSON><PERSON><PERSON> Campfire", "block.minecraft.soul_fire": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ọkụ", "block.minecraft.soul_lantern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_sand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_soil": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.soul_wall_torch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "block.minecraft.spawn.not_valid": "<PERSON>we<PERSON><PERSON> akwa akwa ma ọ bụ ebubo ar<PERSON><PERSON> a<PERSON>, ma <PERSON> bụ gbochie ya", "block.minecraft.spawner": "Monster Spawner", "block.minecraft.spawner.desc1": "Interact with Spawn Egg:", "block.minecraft.spawner.desc2": "Sets Mob Type", "block.minecraft.sponge": "Ogbo", "block.minecraft.spore_blossom": "<PERSON><PERSON><PERSON> o<PERSON>", "block.minecraft.spruce_button": "Spruce Bọtị<PERSON>ụ", "block.minecraft.spruce_door": "Spruce n'Ọnụ", "block.minecraft.spruce_fence": "Ogige Spruce", "block.minecraft.spruce_fence_gate": "Spruce nsu nsu", "block.minecraft.spruce_hanging_sign": "Spruce Hanging Sign", "block.minecraft.spruce_leaves": "Spruce Ahapụ", "block.minecraft.spruce_log": "Abanye na Spruce", "block.minecraft.spruce_planks": "<PERSON><PERSON><PERSON>", "block.minecraft.spruce_pressure_plate": "Mpempe akwụkwọ Mgbochi", "block.minecraft.spruce_sapling": "spruce sapling", "block.minecraft.spruce_sign": "<PERSON><PERSON><PERSON><PERSON> ihe", "block.minecraft.spruce_slab": "<PERSON>ke<PERSON>", "block.minecraft.spruce_stairs": "Ekepu <PERSON>", "block.minecraft.spruce_trapdoor": "Eke<PERSON>", "block.minecraft.spruce_wall_hanging_sign": "Spruce Wall Hanging Sign", "block.minecraft.spruce_wall_sign": "<PERSON><PERSON> ngosi Wall Spruce", "block.minecraft.spruce_wood": "Spruce <PERSON>i", "block.minecraft.sticky_piston": "Nkwụnye pistin", "block.minecraft.stone": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_slab": "Biriiki Okute Slab", "block.minecraft.stone_brick_stairs": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_brick_wall": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_bricks": "<PERSON><PERSON><PERSON> brik", "block.minecraft.stone_button": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_pressure_plate": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_slab": "<PERSON><PERSON><PERSON>", "block.minecraft.stone_stairs": "Nkume steepụ", "block.minecraft.stonecutter": "<PERSON><PERSON><PERSON>", "block.minecraft.stripped_acacia_log": "Akwụsịla A<PERSON>", "block.minecraft.stripped_acacia_wood": "Akwụsịla Itiri A<PERSON>", "block.minecraft.stripped_bamboo_block": "Block of Stripped Bamboo", "block.minecraft.stripped_birch_log": "Akwụ<PERSON><PERSON><PERSON>", "block.minecraft.stripped_birch_wood": "Akw<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_cherry_log": "Stripped Cherry Log", "block.minecraft.stripped_cherry_wood": "Stripped Cherry Wood", "block.minecraft.stripped_crimson_hyphae": "Stickened Crimson Hyphae", "block.minecraft.stripped_crimson_stem": "<PERSON><PERSON>", "block.minecraft.stripped_dark_oak_log": "Akwụsịla Itiri A<PERSON>", "block.minecraft.stripped_dark_oak_wood": "Akwụsịla Itiri A<PERSON>", "block.minecraft.stripped_jungle_log": "Akw<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_jungle_wood": "Akw<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.stripped_mangrove_log": "<PERSON>he ndekọ Mangrove gbawara agbawa", "block.minecraft.stripped_mangrove_wood": "Osisi Mangrove gbawara agbawa", "block.minecraft.stripped_oak_log": "Akwụ<PERSON><PERSON><PERSON>", "block.minecraft.stripped_oak_wood": "Akwụ<PERSON><PERSON><PERSON>", "block.minecraft.stripped_pale_oak_log": "Stripped Pale Oak Log", "block.minecraft.stripped_pale_oak_wood": "Stripped Pale Oak Wood", "block.minecraft.stripped_spruce_log": "Akwụ<PERSON><PERSON><PERSON>", "block.minecraft.stripped_spruce_wood": "Akwụ<PERSON><PERSON><PERSON>", "block.minecraft.stripped_warped_hyphae": "Hypae Stpedted Warped", "block.minecraft.stripped_warped_stem": "Pedzọ Gbara Ọgbọ Afọ", "block.minecraft.structure_block": "<PERSON><PERSON><PERSON> usoro", "block.minecraft.structure_void": "<PERSON><PERSON><PERSON> a<PERSON> uru", "block.minecraft.sugar_cane": "Okpete", "block.minecraft.sunflower": "Sunflower", "block.minecraft.suspicious_gravel": "Suspicious Gravel", "block.minecraft.suspicious_sand": "Suspicious Sand", "block.minecraft.sweet_berry_bush": "<PERSON><PERSON>", "block.minecraft.tall_dry_grass": "Tall Dry Grass", "block.minecraft.tall_grass": "<PERSON><PERSON><PERSON>", "block.minecraft.tall_seagrass": "<PERSON><PERSON><PERSON>", "block.minecraft.target": "Ezubere", "block.minecraft.terracotta": "Terracotta", "block.minecraft.test_block": "Test Block", "block.minecraft.test_instance_block": "Test Instance Block", "block.minecraft.tinted_glass": "<PERSON><PERSON> a t<PERSON> atụ", "block.minecraft.tnt": "Dynamite", "block.minecraft.tnt.disabled": "TNT explosions are disabled", "block.minecraft.torch": "Ọwa", "block.minecraft.torchflower": "Torch<PERSON>", "block.minecraft.torchflower_crop": "Torchflower <PERSON>", "block.minecraft.trapped_chest": "Chest tọrọ atọ", "block.minecraft.trial_spawner": "Trial Spawner", "block.minecraft.tripwire": "<PERSON><PERSON><PERSON>", "block.minecraft.tripwire_hook": "<PERSON><PERSON> nke <PERSON>wire", "block.minecraft.tube_coral": "Coral tubular", "block.minecraft.tube_coral_block": "<PERSON><PERSON>ere nke coral tubular", "block.minecraft.tube_coral_fan": "A na-enwe coral tubular", "block.minecraft.tube_coral_wall_fan": "Mgbidi Fan Coral tub", "block.minecraft.tuff": "<PERSON><PERSON>", "block.minecraft.tuff_brick_slab": "Tuff Brick Slab", "block.minecraft.tuff_brick_stairs": "Tuff Brick Stairs", "block.minecraft.tuff_brick_wall": "Tuff Brick Wall", "block.minecraft.tuff_bricks": "<PERSON>ff Bricks", "block.minecraft.tuff_slab": "<PERSON><PERSON>", "block.minecraft.tuff_stairs": "<PERSON><PERSON> St<PERSON>s", "block.minecraft.tuff_wall": "<PERSON><PERSON>", "block.minecraft.turtle_egg": "Mbe <PERSON>", "block.minecraft.twisting_vines": "<PERSON><PERSON> Twist", "block.minecraft.twisting_vines_plant": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "block.minecraft.vault": "<PERSON><PERSON>", "block.minecraft.verdant_froglight": "<PERSON><PERSON><PERSON>", "block.minecraft.vine": "Vine", "block.minecraft.void_air": "Agụụ ikuku", "block.minecraft.wall_torch": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.warped_button": "Bọtị Warped", "block.minecraft.warped_door": "Ọnụ <PERSON>z<PERSON> ọgụ", "block.minecraft.warped_fence": "Nsu agha kpụ ọkụ n'ọnụ", "block.minecraft.warped_fence_gate": "Ọnụ <PERSON><PERSON><PERSON>", "block.minecraft.warped_fungus": "Fungus nke g<PERSON>ara agbacha", "block.minecraft.warped_hanging_sign": "Warped Hanging Sign", "block.minecraft.warped_hyphae": "Hypae a lụr<PERSON> agha", "block.minecraft.warped_nylium": "Nylium <PERSON>", "block.minecraft.warped_planks": "Planks Warped", "block.minecraft.warped_pressure_plate": "Gburugburu <PERSON>", "block.minecraft.warped_roots": "Mgbada Ọgụ", "block.minecraft.warped_sign": "Warped Sign", "block.minecraft.warped_slab": "<PERSON><PERSON><PERSON> a<PERSON>a", "block.minecraft.warped_stairs": "Nrị<PERSON>", "block.minecraft.warped_stem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "block.minecraft.warped_trapdoor": "Plọ njem dị warara", "block.minecraft.warped_wall_hanging_sign": "Warped Wall Hanging Sign", "block.minecraft.warped_wall_sign": "<PERSON><PERSON>", "block.minecraft.warped_wart_block": "Ngọngọ nke Wart", "block.minecraft.water": "<PERSON><PERSON><PERSON>", "block.minecraft.water_cauldron": "<PERSON><PERSON><PERSON>", "block.minecraft.waxed_chiseled_copper": "Waxed Chiseled Copper", "block.minecraft.waxed_copper_block": "<PERSON><PERSON> m<PERSON><PERSON>", "block.minecraft.waxed_copper_bulb": "Waxed Copper Bulb", "block.minecraft.waxed_copper_door": "Waxed Copper Door", "block.minecraft.waxed_copper_grate": "Waxed Copper Grate", "block.minecraft.waxed_copper_trapdoor": "Waxed Copper Trapdoor", "block.minecraft.waxed_cut_copper": "<PERSON><PERSON> m<PERSON><PERSON>", "block.minecraft.waxed_cut_copper_slab": "Waxed <PERSON>", "block.minecraft.waxed_cut_copper_stairs": "Waxed Cut Copper steepụ", "block.minecraft.waxed_exposed_chiseled_copper": "Waxed Exposed Chiseled Copper", "block.minecraft.waxed_exposed_copper": "Ọla kọpa e kpughere ekpuchi", "block.minecraft.waxed_exposed_copper_bulb": "Waxed Exposed Copper Bulb", "block.minecraft.waxed_exposed_copper_door": "Waxed Exposed Copper Door", "block.minecraft.waxed_exposed_copper_grate": "Waxed Exposed Copper Grate", "block.minecraft.waxed_exposed_copper_trapdoor": "Waxed Exposed Copper Trapdoor", "block.minecraft.waxed_exposed_cut_copper": "<PERSON><PERSON> m<PERSON>puchi <PERSON> k<PERSON> e<PERSON>", "block.minecraft.waxed_exposed_cut_copper_slab": "<PERSON><PERSON><PERSON><PERSON><PERSON> ahu ekpu<PERSON>ere n<PERSON>o <PERSON>", "block.minecraft.waxed_exposed_cut_copper_stairs": "<PERSON><PERSON> mkpuchi a kpachapụ<PERSON><PERSON> ag<PERSON>ụ steepụ <PERSON> kọpa", "block.minecraft.waxed_oxidized_chiseled_copper": "Waxed Oxidized Chiseled Copper", "block.minecraft.waxed_oxidized_copper": "Ọla kọpa a kpụrụ akpụ", "block.minecraft.waxed_oxidized_copper_bulb": "Waxed Oxidized Copper Bulb", "block.minecraft.waxed_oxidized_copper_door": "Waxed Oxidized Copper Door", "block.minecraft.waxed_oxidized_copper_grate": "Waxed Oxidized Copper Grate", "block.minecraft.waxed_oxidized_copper_trapdoor": "Waxed Oxidized Copper Trapdoor", "block.minecraft.waxed_oxidized_cut_copper": "Waksị oxidized cot coper", "block.minecraft.waxed_oxidized_cut_copper_slab": "Waxed Oxidized Bee <PERSON>", "block.minecraft.waxed_oxidized_cut_copper_stairs": "Waxed Oxidized Bee steepụ <PERSON>", "block.minecraft.waxed_weathered_chiseled_copper": "Waxed Weathered Chiseled Copper", "block.minecraft.waxed_weathered_copper": "Ọla kọpa a kpụrụ akpụ", "block.minecraft.waxed_weathered_copper_bulb": "Waxed Weathered Copper Bulb", "block.minecraft.waxed_weathered_copper_door": "Waxed Weathered Copper Door", "block.minecraft.waxed_weathered_copper_grate": "Waxed Weathered Copper Grate", "block.minecraft.waxed_weathered_copper_trapdoor": "Waxed Weathered Copper Trapdoor", "block.minecraft.waxed_weathered_cut_copper": "<PERSON><PERSON><PERSON>ed <PERSON>", "block.minecraft.waxed_weathered_cut_copper_slab": "Waxed Weathered Cut Cut Copper Slab", "block.minecraft.waxed_weathered_cut_copper_stairs": "Waxed Weathered Cut Cut Copper steepụ", "block.minecraft.weathered_chiseled_copper": "Weathered Chiseled Copper", "block.minecraft.weathered_copper": "Ọla <PERSON><PERSON><PERSON> Weathered", "block.minecraft.weathered_copper_bulb": "Weathered Copper Bulb", "block.minecraft.weathered_copper_door": "Weathered Copper Door", "block.minecraft.weathered_copper_grate": "Weathered Copper Grate", "block.minecraft.weathered_copper_trapdoor": "Weathered Copper Trapdoor", "block.minecraft.weathered_cut_copper": "Ọla <PERSON><PERSON><PERSON> Weathered", "block.minecraft.weathered_cut_copper_slab": "Egosipụtara Cut Okpokoro Ọla kọpa", "block.minecraft.weathered_cut_copper_stairs": "Okpokoro <PERSON>", "block.minecraft.weeping_vines": "Ikwa Vine", "block.minecraft.weeping_vines_plant": "<PERSON><PERSON><PERSON>i", "block.minecraft.wet_sponge": "Wet Wet", "block.minecraft.wheat": "Okpokoro Ugbo", "block.minecraft.white_banner": "Ọkọlọtọ <PERSON>-acha <PERSON>cha", "block.minecraft.white_bed": "<PERSON><PERSON> ndina <PERSON>", "block.minecraft.white_candle": "Kand<PERSON>l Ọcha", "block.minecraft.white_candle_cake": "<PERSON><PERSON><PERSON> na <PERSON> kand<PERSON>l", "block.minecraft.white_carpet": "White kapeeti", "block.minecraft.white_concrete": "<PERSON><PERSON><PERSON><PERSON>", "block.minecraft.white_concrete_powder": "<PERSON><PERSON><PERSON><PERSON><PERSON> lhe Ntụ ntụ", "block.minecraft.white_glazed_terracotta": "White gbara agba Terracotta", "block.minecraft.white_shulker_box": "Igbe White Shulker", "block.minecraft.white_stained_glass": "<PERSON><PERSON><PERSON>a<PERSON>", "block.minecraft.white_stained_glass_pane": "Agba ntụpọ", "block.minecraft.white_terracotta": "White Terracotta", "block.minecraft.white_tulip": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "block.minecraft.white_wool": "White ajị", "block.minecraft.wildflowers": "Wildflowers", "block.minecraft.wither_rose": "<PERSON><PERSON>", "block.minecraft.wither_skeleton_skull": "Okpokoro Isi Ọkpụkpụ okpokoro isi", "block.minecraft.wither_skeleton_wall_skull": "<PERSON><PERSON><PERSON>o wither skeleton", "block.minecraft.yellow_banner": "Ọkọlọtọ Edo-edo", "block.minecraft.yellow_bed": "Ihe ndina edo edo", "block.minecraft.yellow_candle": "Kand<PERSON>l na -acha odo odo", "block.minecraft.yellow_candle_cake": "Achịcha na kandụl odo", "block.minecraft.yellow_carpet": "Ka<PERSON><PERSON> edo edo", "block.minecraft.yellow_concrete": "Edo-edo lhe", "block.minecraft.yellow_concrete_powder": "Edo-edo lhe Ntụ ntụ", "block.minecraft.yellow_glazed_terracotta": "Yellow Glazed Terracotta", "block.minecraft.yellow_shulker_box": "<PERSON>gbe <PERSON><PERSON>", "block.minecraft.yellow_stained_glass": "<PERSON><PERSON> odo acha odo", "block.minecraft.yellow_stained_glass_pane": "Agba ntụpọ Yellow", "block.minecraft.yellow_terracotta": "Edo-edo Terracotta", "block.minecraft.yellow_wool": "<PERSON><PERSON><PERSON> an<PERSON> edo edo", "block.minecraft.zombie_head": "<PERSON><PERSON>", "block.minecraft.zombie_wall_head": "Ok<PERSON>koro zombie", "book.byAuthor": "site %1$s", "book.edit.title": "Book Edit Screen", "book.editTitle": "Tinye Aha Akwụkwọ:", "book.finalizeButton": "<PERSON>ye ma mechie", "book.finalizeWarning": "Mara! <PERSON>g<PERSON> <PERSON> b<PERSON> aka n'akwụkwọ ahụ, ọ gakwaghị enwe ike dezie ya.", "book.generation.0": "<PERSON><PERSON> mb<PERSON>", "book.generation.1": "<PERSON>uo nke mbụ", "book.generation.2": "Mpempe akwụkwọ nnomi", "book.generation.3": "<PERSON><PERSON><PERSON>", "book.invalid.tag": "* Akwụkwọ mkpado e<PERSON>hi ezi *", "book.pageIndicator": "Peeji %1$s nke %2$s", "book.page_button.next": "Next Page", "book.page_button.previous": "Previous Page", "book.sign.title": "Book Sign Screen", "book.sign.titlebox": "Title", "book.signButton": "Banye", "book.view.title": "Book View Screen", "build.tooHigh": "Oke maka owuwu bụ %s", "chat.cannotSend": "Enweghị ike izipu ozi nkata", "chat.coordinates": "%s, %s, %s", "chat.coordinates.tooltip": "Pịa teleport", "chat.copy": "Detuo na Clipboard", "chat.copy.click": "Pịa ka Detuo na Clipboard", "chat.deleted_marker": "This chat message has been deleted by the server.", "chat.disabled.chain_broken": "<PERSON><PERSON> disabled due to broken chain. Please try reconnecting.", "chat.disabled.expiredProfileKey": "Agbanyụ<PERSON><PERSON> nkata n'ihi igodo <PERSON>ha profaị<PERSON> emebiela. Biko nwaa ijikọ ọzọ.", "chat.disabled.invalid_command_signature": "Command had unexpected or missing command argument signatures.", "chat.disabled.invalid_signature": "<PERSON><PERSON> had an invalid signature. Please try reconnecting.", "chat.disabled.launcher": "Chat disabled by launcher option. Cannot send message.", "chat.disabled.missingProfileKey": "<PERSON><PERSON> disabled due to missing profile public key. Please try reconnecting.", "chat.disabled.options": "Chat disabled in client options.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> received out-of-order. Did your system time change?", "chat.disabled.profile": "Chat is not allowed by account settings. Press '%s' again for more information.", "chat.disabled.profile.moreInfo": "Ntọala aka<PERSON><PERSON><PERSON> an<PERSON>ị nkata. Enweghị ike izipu ma ọ bụ lelee ozi.", "chat.editBox": "nkata", "chat.filtered": "Filtered by the server.", "chat.filtered_full": "<PERSON><PERSON> nkesa ahụ ezoola ozi gị maka ụfọdụ ndị egwure<PERSON>wu.", "chat.link.confirm": "Ì ji n'aka na ịchọrọ imepe weebụsaịtị a?", "chat.link.confirmTrusted": "You chọrọ imeghe njikọ a ma ọ bụ detuo ya na bọọdụ mbadamba gị?", "chat.link.open": "Mepee na Nchọgharị", "chat.link.warning": "Emekwala uzo obula site n’aka ndi I dighi atukwasi obi!", "chat.queue": "[+%s kwụ n'ahịrị]", "chat.square_brackets": "[%s]", "chat.tag.error": "Server sent invalid message.", "chat.tag.modified": "Message modified by the server. Original:", "chat.tag.not_secure": "Unverified message. Cannot be reported.", "chat.tag.system": "Server message. Cannot be reported.", "chat.tag.system_single_player": "Server message.", "chat.type.admin": "[%s: %s]", "chat.type.advancement.challenge": "%s emech<PERSON><PERSON> aka ahụ %s", "chat.type.advancement.goal": "%s erutela ebum<PERSON>che ahụ %s", "chat.type.advancement.task": "%s em<PERSON>a ka og<PERSON>hu %s", "chat.type.announcement": "[%s] %s", "chat.type.emote": "* %s %s", "chat.type.team.hover": "<PERSON><PERSON>", "chat.type.team.sent": "->%s<%s> %s", "chat.type.team.text": "%s<%s>%s", "chat.type.text": "<%s> %s", "chat.type.text.narrate": "%s kwuru %s", "chat.validation_error": "Chat validation error", "chat_screen.message": "Ozi aga eziga: %s", "chat_screen.title": "<PERSON><PERSON> ihuenyo", "chat_screen.usage": "<PERSON><PERSON> ozi mbata wee pịa banye ka izipu", "chunk.toast.checkLog": "See log for more details", "chunk.toast.loadFailure": "Failed to load chunk at %s", "chunk.toast.lowDiskSpace": "Low disk space!", "chunk.toast.lowDiskSpace.description": "Might not be able to save the world.", "chunk.toast.saveFailure": "Failed to save chunk at %s", "clear.failed.multiple": "<PERSON><PERSON><PERSON> na onye egwuregwu%s", "clear.failed.single": "Onweghi ihe ahụrụ na onye egwuregwu %s", "color.minecraft.black": "<PERSON><PERSON><PERSON>", "color.minecraft.blue": "Blue", "color.minecraft.brown": "<PERSON>", "color.minecraft.cyan": "<PERSON><PERSON>", "color.minecraft.gray": "<PERSON>", "color.minecraft.green": "Akwụkwọ Ndụ", "color.minecraft.light_blue": "Light Blue", "color.minecraft.light_gray": "Light Gray", "color.minecraft.lime": "Lime", "color.minecraft.magenta": "Ma<PERSON><PERSON>", "color.minecraft.orange": "Orange", "color.minecraft.pink": "Pink", "color.minecraft.purple": "Purple", "color.minecraft.red": "Red", "color.minecraft.white": "Ọcha", "color.minecraft.yellow": "Yellow", "command.context.here": "<--[HERE]\n", "command.context.parse_error": "%s nọ n'ọnọdụ %s: %s", "command.exception": "Enweghị ike iza iwu: %s", "command.expected.separator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON><PERSON><PERSON> anya iji mechie otu <PERSON>, mana afụrụ data nsosnso", "command.failed": "<PERSON><PERSON>i a na -atụghị anya ya na -eme mgbe a na -agbalị ime iwu ahụ", "command.forkLimit": "Maximum number of contexts (%s) reached", "command.unknown.argument": "Mgbawa nke iwu na-<PERSON>i", "command.unknown.command": "<PERSON><PERSON> amaghi ma obu nke ezughi oke, lee n'ok<PERSON>u maka njehie", "commands.advancement.criterionNotFound": "Ọganihu %1$s enweghị ọkwa ' %2$s'", "commands.advancement.grant.criterion.to.many.failure": "Enweghị ike inye nkọwa '%s' nke ọganihu %s na %s egwuregwu ka ha nwere ya", "commands.advancement.grant.criterion.to.many.success": "Ezigbo nkatọ '%s' nke ọganihu %s na %s egwuregwu", "commands.advancement.grant.criterion.to.one.failure": "Enweghị ike inye nkọwa '%s' nke ọganihu %s na %s ka ha nwere ya", "commands.advancement.grant.criterion.to.one.success": "Ezigbo nkatọ '%s' nke ọganihu %s na %s", "commands.advancement.grant.many.to.many.failure": "Enwenwughi ike inye ndị egwuregwu %s nkwalite %s dịka ha nwere ha", "commands.advancement.grant.many.to.many.success": "Enyere <PERSON> %s na ndị egwuregwu %s", "commands.advancement.grant.many.to.one.failure": "Enwenwughi ike ịnye nkwalite %s ka %s dịka ha nwere ha", "commands.advancement.grant.many.to.one.success": "Enyere <PERSON> %s ka <PERSON> b<PERSON> %s", "commands.advancement.grant.one.to.many.failure": "Enwenwughi ike inye ndị egwuregwu %s nkwalite %s ka ha nwere ya", "commands.advancement.grant.one.to.many.success": "<PERSON><PERSON><PERSON> %s ruo %s ndị egwuregwu", "commands.advancement.grant.one.to.one.failure": "Enwenwughi ike ịnye ọganihu %s ka %s dịka ha nwere ya", "commands.advancement.grant.one.to.one.success": "Enyere <PERSON> %s ka %s", "commands.advancement.revoke.criterion.to.many.failure": "Enweghi ike iwepu criterion '%s' nke ọganihu %s si %s egwuregwu ka ha enweghị ya", "commands.advancement.revoke.criterion.to.many.success": "Nkọwa a kwụsịrị '%s' nke ọganihu %s si na %s", "commands.advancement.revoke.criterion.to.one.failure": "O nweghị ike iwepu criterion '%s' nke ọganihu %s si %s ka ha enweghị ya", "commands.advancement.revoke.criterion.to.one.success": "Nkọwa weghaara '%s' nke ọganihu %s si %s", "commands.advancement.revoke.many.to.many.failure": "Enweghi ike iwepu mmepe %s site na %s egwuregwu ka ha enweghị ha", "commands.advancement.revoke.many.to.many.success": "Wepụ %s n'ihu site na %s egwuregwu", "commands.advancement.revoke.many.to.one.failure": "Enweghi ike iwepu %s n'ihu site na %s ka ha enweghị ha", "commands.advancement.revoke.many.to.one.success": "Wepụ %s n'ihu %s", "commands.advancement.revoke.one.to.many.failure": "Enweghi ike iwepu ọganihu %s si %s egwuregwu ka ha enweghị ya", "commands.advancement.revoke.one.to.many.success": "Gbanyere ọganihu %s si na sistemụ %s", "commands.advancement.revoke.one.to.one.failure": "Enweghi ike iwepu ọganihu %s si na %s ka ha enweghị ya", "commands.advancement.revoke.one.to.one.success": "Weghaara ọganihu %s si %s", "commands.attribute.base_value.get.success": "Uru ntọala nke agwa %s maka ihe %s bụ %s", "commands.attribute.base_value.reset.success": "Base value for attribute %s for entity %s reset to default %s", "commands.attribute.base_value.set.success": "Ọnụ ego dị mkpa maka agwa %s maka ngalaba %s atọrọ ka %s", "commands.attribute.failed.entity": "%s abụgh<PERSON> ihe dị adị maka iwu a", "commands.attribute.failed.modifier_already_present": "Ihe mmezi %s adịlarị na agwa %s maka ihe %s", "commands.attribute.failed.no_attribute": "Entity %s enweghị agwa %s", "commands.attribute.failed.no_modifier": "Agwa agwa %s maka ihe %s enwegh<PERSON> onye nrụpụta %s", "commands.attribute.modifier.add.success": "Agbakwunyere ihe ngbanwe %s ka o were kwuo %s maka ihe %s", "commands.attribute.modifier.remove.success": "Ewepụrụ ihe nrụzi %s site na agwa %s maka ihe %s", "commands.attribute.modifier.value.get.success": "Uru nke ihe nrụzi %s na agwa %s maka ihe %s bụ %s", "commands.attribute.value.get.success": "<PERSON><PERSON> nji<PERSON> %s maka ihe %s bụ %s", "commands.ban.failed": "Onwe<PERSON><PERSON> ihe gbanwere. <PERSON><PERSON><PERSON> onye egwu<PERSON>wu", "commands.ban.success": "Amachiela %s: %s", "commands.banip.failed": "Onweghi ihe gbanwere. Amachiela IP ahụ", "commands.banip.info": "This ban affects %s player(s): %s", "commands.banip.invalid": "Adreesi IP n'abaghi uru ma <PERSON> bụ onye egwuregwu amaghị ama", "commands.banip.success": "Amachibidoro IP %s: %s", "commands.banlist.entry": "A machibidoro %s site %s: %s", "commands.banlist.entry.unknown": "(Unknown)", "commands.banlist.list": "There are %s ban(s):", "commands.banlist.none": "<PERSON>we<PERSON><PERSON> mmachibido iwu", "commands.bossbar.create.failed": "Enweghị bossbar dinu nwere ID '%s'", "commands.bossbar.create.success": "Emepụtara akara ngosi omenala %s", "commands.bossbar.get.max": "Ogwe nchịkwa ndị ahịa %s nwere nke kacha nke %s", "commands.bossbar.get.players.none": "Onye isi nchịkwa %s enweghị ndị egwuregwu ugbu a n'ịntanetị", "commands.bossbar.get.players.some": "Custom bossbar %s has %s player(s) currently online: %s", "commands.bossbar.get.value": "Ogwe nchịkwa omenala %s nwere uru %s", "commands.bossbar.get.visible.hidden": "A na -ezobe akara nchịkwa %s ugbu a", "commands.bossbar.get.visible.visible": "A na -egosi ọkwa ihu ahịa %s ugbu a", "commands.bossbar.list.bars.none": "Enweghị omenala bossbars nọ n'ọrụ", "commands.bossbar.list.bars.some": "There are %s custom bossbar(s) active: %s", "commands.bossbar.remove.success": "Wepu akara ngosi omenala %s", "commands.bossbar.set.color.success": "Ogwe nchịkwa omenala %s agbanweela agba", "commands.bossbar.set.color.unchanged": "Ọ dịghị ihe gbanwere. <PERSON><PERSON> ahụ bụ ụcha nke bossbar a", "commands.bossbar.set.max.success": "Ogwe nchịkwa ndị ahịa %s agbanweela kacha ka ọ bụrụ %s", "commands.bossbar.set.max.unchanged": "Ọ dịghị ihe gbanwere. Nke a bụ nke kachasị nke bossbar a", "commands.bossbar.set.name.success": "E degharịrị aha ọba ọrụ %s aha <PERSON>zọ", "commands.bossbar.set.name.unchanged": "Ọ dịghị ihe gbanwere. <PERSON><PERSON> ahụ bụ aha aha a", "commands.bossbar.set.players.success.none": "Ogwe njikwa omenala %s enweghịzi ndị egwuregwu", "commands.bossbar.set.players.success.some": "Custom bossbar %s now has %s player(s): %s", "commands.bossbar.set.players.unchanged": "Onweghi ihe gbanwere. N<PERSON><PERSON> egwuregwu ahụ anodugo na bossbar na-enweghi onye aga-etinye ma ọ bụ wepụ", "commands.bossbar.set.style.success": "Omenala blọọgụ %s agbanweela ụdị", "commands.bossbar.set.style.unchanged": "Ọ dịghị ihe gbanwere. Nke ahu bu ugbua nke bossbar a", "commands.bossbar.set.value.success": "Ogwe nchịkwa omenala %s agbanweela uru ka <PERSON> bụrụ %s", "commands.bossbar.set.value.unchanged": "Ọ dịghị ihe gbanwere. Nke ahụ abụrụ uru nke bossbar", "commands.bossbar.set.visibility.unchanged.hidden": "Ọ dịghị ihe gbanwere. Etubela bossbar", "commands.bossbar.set.visibility.unchanged.visible": "Ọ dịghị ihe gbanwere. <PERSON><PERSON>ela ike <PERSON>hụ ọkpụkpụ", "commands.bossbar.set.visible.success.hidden": "Ugbu a, a na -ezobe onye isi nchịkwa %s", "commands.bossbar.set.visible.success.visible": "A na -<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> omenala %s ugbu a", "commands.bossbar.unknown": "Onweghi bossbar dinu na ID '%s'", "commands.clear.success.multiple": "Removed %s item(s) from %s players", "commands.clear.success.single": "Removed %s item(s) from player %s", "commands.clear.test.multiple": "Found %s matching item(s) on %s players", "commands.clear.test.single": "Found %s matching item(s) on player %s", "commands.clone.failed": "Enweghị ihe mg<PERSON>ju cloned", "commands.clone.overlap": "Ebe isi na ebe ozo enweghi ike i<PERSON>ari", "commands.clone.success": "Successfully cloned %s block(s)", "commands.clone.toobig": "Ọtụtụ ngongo na ebe ar<PERSON> aka (okarichaa'%s aururu aka %s)", "commands.damage.invulnerable": "Target is invulnerable to the given damage type", "commands.damage.success": "Applied %s damage to %s", "commands.data.block.get": "%s na ngongo %s, %s, %s agachaa ihe ọnụọgụ nke %s is %s", "commands.data.block.invalid": "<PERSON>ongo a na-atụ anya ab<PERSON>gh<PERSON> ngongo otu", "commands.data.block.modified": "Data ngongo edozighariri nke %s, %s, %s", "commands.data.block.query": "%s, %s, %s has the following block data: %s\nnwere data ngongo ndị a", "commands.data.entity.get": "%s na %s agachaa ihe ọnụọgụgụ nke %s bụ %s", "commands.data.entity.invalid": "Enweghị ike idozi data onye egwuregwu", "commands.data.entity.modified": "Data otu edozighariri maka%s", "commands.data.entity.query": "%s nwere data otu ndị a: %s", "commands.data.get.invalid": "Enweghị ike inweta%s; naan<PERSON> mkpado ọ<PERSON> ka enyere ikike", "commands.data.get.multiple": "Mgbagha nkea na-anabata otu uru NBT", "commands.data.get.unknown": "Enweghị ike inweta%s; mk<PERSON>o adighi", "commands.data.merge.failed": "Ọ dịghị ihe gbanwere. Ngwongwo akọwapụtara nwerelarị ụkpụrụ ndị a", "commands.data.modify.expected_list": "<PERSON><PERSON> nguputa at<PERSON><PERSON><PERSON> anya ya nwetara:%s", "commands.data.modify.expected_object": "<PERSON><PERSON> at<PERSON><PERSON> anya ya, nwetara: %s", "commands.data.modify.expected_value": "Expected value, got: %s", "commands.data.modify.invalid_index": "Indeksi ihe nguputa abaghị uru%s", "commands.data.modify.invalid_substring": "Invalid substring indices: %s to %s", "commands.data.storage.get": "%s na ebe nchekwa %s mgbe ihe banyere %s bụ %s", "commands.data.storage.modified": "Echekwa echekwara %s", "commands.data.storage.query": "Nchekwa %s nwere ihe ndia: %s", "commands.datapack.create.already_exists": "Pack with name '%s' already exists", "commands.datapack.create.invalid_full_name": "Invalid new pack name '%s'", "commands.datapack.create.invalid_name": "Invalid characters in new pack name '%s'", "commands.datapack.create.io_failure": "Can't create pack with name '%s', check logs", "commands.datapack.create.metadata_encode_failure": "Failed to encode metadata for pack with name '%s': %s", "commands.datapack.create.success": "Created new empty pack with name '%s'", "commands.datapack.disable.failed": "<PERSON><PERSON><PERSON><PERSON> ngwugwu '%s'!", "commands.datapack.disable.failed.feature": "Pack '%s' cannot be disabled, since it is part of an enabled flag!", "commands.datapack.enable.failed": "<PERSON><PERSON><PERSON><PERSON> ngwugwu '%s'!", "commands.datapack.enable.failed.no_flags": "Pack '%s' cannot be enabled, since required flags are not enabled in this world: %s!", "commands.datapack.list.available.none": "<PERSON><PERSON><PERSON><PERSON><PERSON> ngwugwu data ọ<PERSON><PERSON><PERSON>", "commands.datapack.list.available.success": "There are %s data pack(s) available: %s", "commands.datapack.list.enabled.none": "Enwegh<PERSON> ngwugwu data emeghere", "commands.datapack.list.enabled.success": "There are %s data pack(s) enabled: %s", "commands.datapack.modify.disable": "Mmechi ngwugwu data %s", "commands.datapack.modify.enable": "Mmeghe ngwu<PERSON>wu data %s", "commands.datapack.unknown": "<PERSON><PERSON><PERSON><PERSON> amaghị ama'%s'", "commands.debug.alreadyRunning": "Amalitela <PERSON>", "commands.debug.function.noRecursion": "Enweghị ike <PERSON> n'ime ọr<PERSON>", "commands.debug.function.noReturnRun": "Tracing can't be used with return run", "commands.debug.function.success.multiple": "Traced %s command(s) from %s functions to output file %s", "commands.debug.function.success.single": "Traced %s command(s) from function '%s' to output file %s", "commands.debug.function.traceFailed": "Ịchọta <PERSON><PERSON><PERSON> agagh<PERSON>", "commands.debug.notRunning": "Onye na -eme nchọ<PERSON><PERSON>ta akọ<PERSON> amalitebeghị", "commands.debug.started": "Amalitere pro<PERSON>", "commands.debug.stopped": "Kwụsịr<PERSON> imepụta akọrọ mgbe %s sekọnd na akọrọ %s ( %s akọr<PERSON> kwa nkeji)", "commands.defaultgamemode.success": "Ndabere egwuregwu mode ugbu a %s", "commands.deop.failed": "Onweghi ihe gbanwere. Onye egwuregwu abụghị onye oru", "commands.deop.success": "E mere %s obughizi onye ọrụ ihe nkesa", "commands.dialog.clear.multiple": "Cleared dialog for %s players", "commands.dialog.clear.single": "Cleared dialog for %s", "commands.dialog.show.multiple": "Displayed dialog to %s players", "commands.dialog.show.single": "Displayed dialog to %s", "commands.difficulty.failure": "<PERSON><PERSON> nhia ahụ agbanweghị; etinye go ya na %s", "commands.difficulty.query": "<PERSON>he isi ike bụ %s", "commands.difficulty.success": "Enyere nso<PERSON> ah<PERSON> na %s", "commands.drop.no_held_items": "Akurungwa enweghị ike ijide ihe <PERSON>bụla", "commands.drop.no_loot_table": "Akurungwa %s enwegh<PERSON> okpokoro nwepụ <PERSON>", "commands.drop.no_loot_table.block": "Block %s has no loot table", "commands.drop.success.multiple": "Dobere %s ihe", "commands.drop.success.multiple_with_table": "Dobere %s ihe site na okpokoro nwepụ %s", "commands.drop.success.single": "Ama esịn %s %s", "commands.drop.success.single_with_table": "Ama esịn %s %s site na tebụl loot %s", "commands.effect.clear.everything.failed": "<PERSON>he ana-achọ enweghị mmeta ọbụla a ga ewepu", "commands.effect.clear.everything.success.multiple": "Wepụ ihe ọ bụla site n'aka %s", "commands.effect.clear.everything.success.single": "Wepụ ihe ọ bụla sitere %s", "commands.effect.clear.specific.failed": "<PERSON><PERSON> ana-achọ enweghị mmeta arịọr<PERSON>", "commands.effect.clear.specific.success.multiple": "Mmetụta wepụrụ %s si na sistemụ %s", "commands.effect.clear.specific.success.single": "Mmetụta wepụ %s si %s", "commands.effect.give.failed": "Enweghị ike itinye ihe mmeta ( onwere ike bụrụ na ihe ana-achọ nwere mgbochi n'ebe ihe mmeta dị ma ọ bụ na o nwere ihe sịkarịrị ike)", "commands.effect.give.success.multiple": "<PERSON><PERSON> %s gaa %s lekwas<PERSON><PERSON><PERSON> anya", "commands.effect.give.success.single": "<PERSON>jiri mmet<PERSON> %s gaa %s", "commands.enchant.failed": "Onweghi ihe gbanwere. Onwere ike bụrụ na ihe ana-achọ enweghị ihe dị ha n'aka ma ọ bụ na enweghị ike itinye ihe itunanya", "commands.enchant.failed.entity": "%s is not a valid entity for this command", "commands.enchant.failed.incompatible": "'%s enweghị ike ikwado enchantmenti ahụ", "commands.enchant.failed.itemless": "%s anagh<PERSON> ejide ihe <PERSON>", "commands.enchant.failed.level": "%s kar<PERSON>r<PERSON> nke %s nke enchantmenti ah<PERSON> kwadoro", "commands.enchant.success.multiple": "Ejiri Enchantment %s tinye na %s entities", "commands.enchant.success.single": "Enchantment %s na-ar<PERSON>ụ %s", "commands.execute.blocks.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.execute.conditional.fail": "Adaala nnwale", "commands.execute.conditional.fail_count": "Adaala n<PERSON>, gụọ: %s", "commands.execute.conditional.pass": "Agafeela nnwale", "commands.execute.conditional.pass_count": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>, gụọ: %s", "commands.execute.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.experience.add.levels.success.multiple": "Nye %s ah<PERSON><PERSON>ụ <PERSON> na %s egwuregwu", "commands.experience.add.levels.success.single": "Nye %s ahụmahụ <PERSON> na %s", "commands.experience.add.points.success.multiple": "Ihe nrite %s na-egosi na egwuregwu %s", "commands.experience.add.points.success.single": "Ihe mmetuta %s kwuru na %s", "commands.experience.query.levels": "%s nwere %s sistemụ sistemụ", "commands.experience.query.points": "%s nwere %s isi ihe nwere sistemụ", "commands.experience.set.levels.success.multiple": "Debe ọkwa %s na %s egwuregwu", "commands.experience.set.levels.success.single": "Debe ọkwa %s na %s", "commands.experience.set.points.invalid": "Enweghị ike itinye <PERSON> n'elu Po<PERSON>ị okarichaa maka ọnọdụ ndịugbua nke onye egwuregwu", "commands.experience.set.points.success.multiple": "Setịpụ sistemụ %s na arụ ọrụ %s", "commands.experience.set.points.success.single": "Dee ihe omimi %s na %s", "commands.fill.failed": "Onweghi ngongo e kpojuru", "commands.fill.success": "Successfully filled %s block(s)", "commands.fill.toobig": "Too many blocks in the specified area (maximum %s, specified %s)", "commands.fillbiome.success": "Biomes set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.success.count": "%s biome entry/entries set between %s, %s, %s and %s, %s, %s", "commands.fillbiome.toobig": "Too many blocks in the specified volume (maximum %s, specified %s)", "commands.forceload.added.failure": "Enwegh<PERSON> akara ibe akara maka <PERSON>ye ike", "commands.forceload.added.multiple": "Akara%s nchịkọta na %s site na %s ruo na %s ka ewere were ike bunye ya ibu", "commands.forceload.added.none": "Onweghi nchịkọta eji ike bunye ibu ah<PERSON>ụ na %s", "commands.forceload.added.single": "Akara chunk %s n'ime %s ka a ga -amanye ya ibu", "commands.forceload.list.multiple": "Achọtara iberibe %s nwere %s na: %s", "commands.forceload.list.single": "Achọtara mpekere dị ike n'ime %s na: %s", "commands.forceload.query.failure": "Akara akara na %s n'ime %s maka akara ike", "commands.forceload.query.success": "Akara akara na %s n'ime %s maka akara ike", "commands.forceload.removed.all": "Wepụrụ akara nchịkọta nile eji ike bu ye ibu na %s", "commands.forceload.removed.failure": "Onweghi nchịkọta ewepụta na mkpoju n'ike", "commands.forceload.removed.multiple": "Nwepụ ákàrà%s chunks in %s from %s to %s maka mkpoju n'ike", "commands.forceload.removed.single": "Nchịkọta ewepụru <PERSON> %s na %s maka mkpoju n'ike", "commands.forceload.toobig": "Ọtụtụ nchịkọta na ebe egosiri kpọmkwem\n (ọmarịcha %s, kpọmkwem %s)", "commands.function.error.argument_not_compound": "Invalid argument type: %s, expected Compound", "commands.function.error.missing_argument": "Missing argument %2$s to function %1$s", "commands.function.error.missing_arguments": "Missing arguments to function %s", "commands.function.error.parse": "While instantiating macro %s: Command '%s' caused error: %s", "commands.function.instantiationFailure": "Failed to instantiate function %s: %s", "commands.function.result": "Function %s returned %s", "commands.function.scheduled.multiple": "Running functions %s", "commands.function.scheduled.no_functions": "Can't find any functions for name %s", "commands.function.scheduled.single": "Running function %s", "commands.function.success.multiple": "Executed %s command(s) from %s functions", "commands.function.success.multiple.result": "Executed %s functions", "commands.function.success.single": "Executed %s command(s) from function '%s'", "commands.function.success.single.result": "Function '%2$s' returned %1$s", "commands.gamemode.success.other": "Tinye %s's <PERSON><PERSON><PERSON><PERSON><PERSON> egwuregwu na %s", "commands.gamemode.success.self": "<PERSON><PERSON> ọnọ<PERSON><PERSON> egwuregwu nke onwe na %s", "commands.gamerule.query": "Etinyela iwuegwuregwu %s na %s", "commands.gamerule.set": "Etinyela iwuegwuregwu%s na %s", "commands.give.failed.toomanyitems": "Enweghị ike inye ihe karịrị %s nke %s", "commands.give.success.multiple": "Nyefee %s %s na %s egwuregwu", "commands.give.success.single": "Nye %s %s aka %s", "commands.help.failed": "<PERSON><PERSON> amaghị ama ma ọ bụ ikike ezughi ezu", "commands.item.block.set.success": "Jiri %s dochie oghere na %s, %s, %s", "commands.item.entity.set.success.multiple": "Jiri %s dochie oghere na %s", "commands.item.entity.set.success.single": "Jiri %s dochie oghere na %s", "commands.item.source.no_such_slot": "Isi mmalite enweghị oghere %s", "commands.item.source.not_a_container": "Ọnọdụ isi mmalite %s, %s, %s abụghị akpa", "commands.item.target.no_changed.known_item": "Enweghị ebumnuche anabatara %s n'ime oghere %s", "commands.item.target.no_changes": "Enweghị ebumnuche anabatara n'ime oghere %s", "commands.item.target.no_such_slot": "Ebumnuche enweghị oghere %s", "commands.item.target.not_a_container": "Ọnọdụ ebumnuche %s, %s, %s abụghị akpa", "commands.jfr.dump.failed": "Etufughị ndekọ JFR: %s", "commands.jfr.start.failed": "Ịmalite profaịlụ JFR dara", "commands.jfr.started": "Mmalite profaịlụ JFR", "commands.jfr.stopped": "Profaịlụ JFR kwụsịrị wee tụfuo ya na %s", "commands.kick.owner.failed": "Cannot kick server owner in LAN game", "commands.kick.singleplayer.failed": "<PERSON><PERSON> kick in an offline singleplayer game", "commands.kick.success": "Agbaala ukwu %s: %s", "commands.kill.success.multiple": "Egbuola %s otu", "commands.kill.success.single": "Egbuola %s", "commands.list.nameAndId": "%s (%s)", "commands.list.players": "Enwere %s nke kacha %s egwuregwu online: %s", "commands.locate.biome.not_found": "Enweghị ike I chọta ebe ahịhịa nke ụdị \"%s\" na ebe dị nso", "commands.locate.biome.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.poi.not_found": "Enweghị ike <PERSON>ch<PERSON>ta ebe mmasị nke ụdị %s na ebe dị nso", "commands.locate.poi.success": "The nearest %s is at %s (%s blocks away)", "commands.locate.structure.invalid": "Enwe<PERSON><PERSON> nhazi <PERSON>b<PERSON>la dị na ụdị \"%s\"", "commands.locate.structure.not_found": "Enweghị ike <PERSON> nke <PERSON>ị \"%s\" dị nso", "commands.locate.structure.success": "%s kacha nso dị na %s ( ngongo %s n'id<PERSON> anya)", "commands.message.display.incoming": "%s tamuru nye gị %s", "commands.message.display.outgoing": "I tamuru nye %s: %s", "commands.op.failed": "Onweghi ihe gbanwere. Onye egwuregwu aburula onye oru", "commands.op.success": "E mere %s onye ọrụ ihe nkesa", "commands.pardon.failed": "Onweghi ihe gbanwere. A machighi onye egwuregwu", "commands.pardon.success": "Amachighi %s", "commands.pardonip.failed": "Onweghi ihe gbanwere. Amaghị IP ahụ", "commands.pardonip.invalid": "<PERSON><PERSON><PERSON> abaghị uru", "commands.pardonip.success": "Akwụsịghị IP %s", "commands.particle.failed": "Urughuru ya adighiri onye ọbụla ị<PERSON>", "commands.particle.success": "Ngosi <PERSON> %s", "commands.perf.alreadyRunning": "Amalitelarị <PERSON><PERSON>", "commands.perf.notRunning": "Prof<PERSON><PERSON><PERSON><PERSON>", "commands.perf.reportFailed": "Ịmepụta mkpesa mperi agaghị eme", "commands.perf.reportSaved": "Emepụtara mkpesa nbibi na %s", "commands.perf.started": "<PERSON><PERSON><PERSON> ọsọ profaịlụ arụmọrụ nke abụọ nke abụọ (jiri '/perf stop' kwụsị n'isi)", "commands.perf.stopped": "Stopped performance profiling after %s second(s) and %s tick(s) (%s tick(s) per second)", "commands.place.feature.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> et<PERSON> n<PERSON>", "commands.place.feature.invalid": "Enwegh<PERSON> n<PERSON>ra <PERSON> dị na ụdị%s", "commands.place.feature.success": "Etinyela\"%s\" na %s, %s, %s", "commands.place.jigsaw.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> emep<PERSON> jigs<PERSON>", "commands.place.jigsaw.invalid": "Enweghị ọdọ ndebiri nke ụdị%s", "commands.place.jigsaw.success": "Emeputara jigsọọ na %s, %s, %s", "commands.place.structure.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> et<PERSON>ye n<PERSON>zi", "commands.place.structure.invalid": "There is no structure with type \"%s\"", "commands.place.structure.success": "Nhazi emeputara \"%s\" na %s, %s, %s", "commands.place.template.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> etinye n<PERSON>i", "commands.place.template.invalid": "Enweghị nde iri nke ụdị id%s", "commands.place.template.success": "Ekponyego ndebiri \"%s\" na %s, %s, %s", "commands.playsound.failed": "Ụda ahụ dị ányá ịnụ nu", "commands.playsound.success.multiple": "Egwuru ụda %s na %s egwuregwu", "commands.playsound.success.single": "Egwuru ụda %s na %s", "commands.publish.alreadyPublished": "Anabata la egwuregwu odininigwe na pootu %s", "commands.publish.failed": "<PERSON><PERSON><PERSON><PERSON> <PERSON>ke <PERSON> e<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>ara", "commands.publish.started": "A nabatara egwuregwu mpaghara n'ọdụ ụgbọ mmiri %s", "commands.publish.success": "Enweela egwuregwu ọtụtụ ngwukorita na pọọtụ %s", "commands.random.error.range_too_large": "The range of the random value must be at most 2147483646", "commands.random.error.range_too_small": "The range of the random value must be at least 2", "commands.random.reset.all.success": "Reset %s random sequence(s)", "commands.random.reset.success": "Reset random sequence %s", "commands.random.roll": "%s rolled %s (from %s to %s)", "commands.random.sample.success": "Randomized value: %s", "commands.recipe.give.failed": "Onweghi ngwa nri ọ<PERSON>ụ<PERSON>", "commands.recipe.give.success.multiple": "Emegheri %s usoro nri maka ndị egwuregwu %s", "commands.recipe.give.success.single": "Ezi nri %s akpọchiri maka %s", "commands.recipe.take.failed": "Onweghi ngwa nri a ga-echefunwu", "commands.recipe.take.success.multiple": "Were usoro s %site na ndị egwuregwu %s", "commands.recipe.take.success.single": "Ewere usoro %s site na %s", "commands.reload.failure": "Ntinyeghari ekweghi; ndebe data ochie", "commands.reload.success": "Bubatagharịa!", "commands.ride.already_riding": "%s is already riding %s", "commands.ride.dismount.success": "%s stopped riding %s", "commands.ride.mount.failure.cant_ride_players": "Players can't be ridden", "commands.ride.mount.failure.generic": "%s couldn't start riding %s", "commands.ride.mount.failure.loop": "Can't mount entity on itself or any of its passengers", "commands.ride.mount.failure.wrong_dimension": "Can't mount entity in different dimension", "commands.ride.mount.success": "%s started riding %s", "commands.ride.not_riding": "%s is not riding any vehicle", "commands.rotate.success": "Rotated %s", "commands.save.alreadyOff": "Agbanyuola nchekwa", "commands.save.alreadyOn": "Agbanyela nchekwa", "commands.save.disabled": "Ewe<PERSON><PERSON>la nchekwa ak<PERSON>ka ug<PERSON>a", "commands.save.enabled": "<PERSON>g<PERSON><PERSON> emeg<PERSON> n<PERSON> a<PERSON>", "commands.save.failed": "Enweghị ike ichekwa egwuregwu ahụ (enwere oghere efere zuru okè?)", "commands.save.saving": "Nchekwa nke egwuregwu ( nkea gnwere ike iwe nwa oge)", "commands.save.success": "Echekwara egwu<PERSON><PERSON><PERSON> ah<PERSON>", "commands.schedule.cleared.failure": "Enweghị usoro na id %s", "commands.schedule.cleared.success": "Removed %s schedule(s) with id %s", "commands.schedule.created.function": "Scheduled function '%s' in %s tick(s) at gametime %s", "commands.schedule.created.tag": "Mkpado kwadoro %s na %s na-akụ na oge egwuregwu %s", "commands.schedule.macro": "Can't schedule a macro", "commands.schedule.same_tick": "Enweghị ike ikwado maka akara nke ugbua", "commands.scoreboard.objectives.add.duplicate": "Enwegh<PERSON> ihe nhọrọ dị na aha ahụ", "commands.scoreboard.objectives.add.success": "Ekeputara nhọrọ ọhụrụ %s", "commands.scoreboard.objectives.display.alreadyEmpty": "Enweghị ihe gbanwere. Onweghizi ihe dị na oghere ngosi ahụ", "commands.scoreboard.objectives.display.alreadySet": "Onweghi ihe gbanwere. Oghere ihe ngosi ahụ na-egosizi ihe nhọrọ ahụ", "commands.scoreboard.objectives.display.cleared": "Ehichapugo ihe nhọrọ ọbụna dị na oghere ihe ngosi %s", "commands.scoreboard.objectives.display.set": "Tinye oghere ihe ngosi %s iji gosi ihe nhọrọ %s", "commands.scoreboard.objectives.list.empty": "Enwegh<PERSON> ihe nhọ<PERSON>", "commands.scoreboard.objectives.list.success": "There are %s objective(s): %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Enabled display auto-update for objective %s", "commands.scoreboard.objectives.modify.displayname": "Gbanwere ngosi ngosi nke %s ka %s", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Cleared default number format of objective %s", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Changed default number format of objective %s", "commands.scoreboard.objectives.modify.rendertype": "Gbanwee ụdị ebumnuche ahụ %s", "commands.scoreboard.objectives.remove.success": "Ewepụta ihe nhọrọ %s", "commands.scoreboard.players.add.success.multiple": "Agbakwunyere %s na %s maka %s otu", "commands.scoreboard.players.add.success.single": "Agbakwunyere%s na %s maka %s (ugbua %s)", "commands.scoreboard.players.display.name.clear.success.multiple": "Cleared display name for %s entities in %s", "commands.scoreboard.players.display.name.clear.success.single": "Cleared display name for %s in %s", "commands.scoreboard.players.display.name.set.success.multiple": "Changed display name to %s for %s entities in %s", "commands.scoreboard.players.display.name.set.success.single": "Changed display name to %s for %s in %s", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Cleared number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Cleared number format for %s in %s", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Changed number format for %s entities in %s", "commands.scoreboard.players.display.numberFormat.set.success.single": "Changed number format for %s in %s", "commands.scoreboard.players.enable.failed": "Onweghi ihe gbanwere. E megheela ihe mkpalite ahụ", "commands.scoreboard.players.enable.invalid": "<PERSON><PERSON> naan<PERSON>ụ dị na mkpalite ihe nhọrọ", "commands.scoreboard.players.enable.success.multiple": "Emegheela ihe mkpalite%s maka %s otu", "commands.scoreboard.players.enable.success.single": "Emegheela ihe mkpalite %s maka %s", "commands.scoreboard.players.get.null": "Enweghị ike inweta uru nke %s for %s; onweghi nka digoro nkwadobe", "commands.scoreboard.players.get.success": "%s nwere %s %s", "commands.scoreboard.players.list.empty": "Enweghị otú ndị esoro", "commands.scoreboard.players.list.entity.empty": "%s enweghị mp<PERSON><PERSON>", "commands.scoreboard.players.list.entity.entry": "%s: %s", "commands.scoreboard.players.list.entity.success": "%s has %s score(s):", "commands.scoreboard.players.list.success": "There are %s tracked entity/entities: %s", "commands.scoreboard.players.operation.success.multiple": "Akwalitela %s maka %s otu", "commands.scoreboard.players.operation.success.single": "Set %s for %s to %s", "commands.scoreboard.players.remove.success.multiple": "Ewepụta %s site na %s maka %s otu", "commands.scoreboard.players.remove.success.single": "Ewepuru%s site na %s maka %s (ugbua %s)", "commands.scoreboard.players.reset.all.multiple": "Dozigharia mpụtara nile maka %s otu", "commands.scoreboard.players.reset.all.single": "Tinyegharia mpụtara nile maka %s", "commands.scoreboard.players.reset.specific.multiple": "Dozigharia%s maka %s otu", "commands.scoreboard.players.reset.specific.single": "Dozigharia%s maka %s", "commands.scoreboard.players.set.success.multiple": "Etinyere%s maka %s otu na %s", "commands.scoreboard.players.set.success.single": "Etinyere%s maka %s na %s", "commands.seed.success": "Mkpụrụ %s", "commands.setblock.failed": "Enweghị ike idozi ihe ngongo", "commands.setblock.success": "Gbanwere ngọngọ na %s, %s, %s", "commands.setidletimeout.success": "The player idle timeout is now %s minute(s)", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "Can only set the world spawn for overworld", "commands.setworldspawn.success": "Tọọ ntụpọ ụwa ka %s, %s, %s [%s]", "commands.spawnpoint.success.multiple": "Tọọ ntụpọ spawn na %s, %s, %s [%s] na %s maka %s ndị egwuregwu", "commands.spawnpoint.success.single": "Debe ntụpọ na %s, %s, %s [%s] na %s maka %s", "commands.spectate.not_spectator": "%s anọ<PERSON><PERSON> n'ọnọdụ onye nkiri", "commands.spectate.self": "Enwegh<PERSON> ike ile onwe gị anya", "commands.spectate.success.started": "<PERSON><PERSON><PERSON> a dị egwu %s", "commands.spectate.success.stopped": "<PERSON><PERSON><PERSON><PERSON> elele ihe dị adị anya", "commands.spreadplayers.failed.entities": "Could not spread %s entity/entities around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.failed.invalid.height": "<PERSON><PERSON>ogo ok<PERSON> a<PERSON>ị uru %s; atụrụ anya karịa opekata mpe nke ụwa %s", "commands.spreadplayers.failed.teams": "Could not spread %s team(s) around %s, %s (too many entities for space - try using spread of at most %s)", "commands.spreadplayers.success.entities": "Spread %s entity/entities around %s, %s with an average distance of %s block(s) apart", "commands.spreadplayers.success.teams": "Spread %s team(s) around %s, %s with an average distance of %s block(s) apart", "commands.stop.stopping": "<PERSON><PERSON><PERSON><PERSON> ihe nkesa", "commands.stopsound.success.source.any": "Kwụsị ụ<PERSON> '%s' niile", "commands.stopsound.success.source.sound": "Akwụsịla <PERSON> '%s' na isi mmalite '%s'", "commands.stopsound.success.sourceless.any": "Akwụ<PERSON><PERSON><PERSON>", "commands.stopsound.success.sourceless.sound": "Akwụsịla <PERSON> '%s'", "commands.summon.failed": "Enweghị ike i<PERSON>ko otu", "commands.summon.failed.uuid": "Enweghị ike i<PERSON>ko otu", "commands.summon.invalidPosition": "Ọnọdụ na-e<PERSON>hi ezi maka <PERSON>", "commands.summon.success": "Akpọkọtara <PERSON>ụ<PERSON>ụ %s", "commands.tag.add.failed": "Ọ nwere ike bụrụ na ihe nlegagara anya enwego ihe mkpado ma ọ bu na o nwere ihe mkpado rie nne", "commands.tag.add.success.multiple": "Mkpado agbakwunyere'%s' na %s otu", "commands.tag.add.success.single": "<PERSON><PERSON><PERSON> a<PERSON> '%s' to %s", "commands.tag.list.multiple.empty": "Enweghị mkpado obula na %s otu ndia", "commands.tag.list.multiple.success": "Otu %s nwere %s mkpado: %s", "commands.tag.list.single.empty": "%s enwegh<PERSON> mk<PERSON>o <PERSON>", "commands.tag.list.single.success": "%s nwere %s mkpado: %s", "commands.tag.remove.failed": "Ihe nlegara anya enweghị ihe mkpado nkea", "commands.tag.remove.success.multiple": "Mkpado ewepụrụ'%s' site na %s otu", "commands.tag.remove.success.single": "Mkpado ewepụrụ '%s' site na %s", "commands.team.add.duplicate": "<PERSON>wego otu nwere aha ahụ", "commands.team.add.success": "Otú ekewaputa %s", "commands.team.empty.success": "Removed %s member(s) from team %s", "commands.team.empty.unchanged": "Onweghi ihe gbanwere. Onweghizi ihe dị na otú ahu", "commands.team.join.success.multiple": "Agbakwunyere %s ndị òtù na ime otu %s", "commands.team.join.success.single": "Agbakwunyere%s na otu %s", "commands.team.leave.success.multiple": "Ewepụta %s ndị òtù n'ime otu", "commands.team.leave.success.single": "Ewepuru%s n'ime otu obula", "commands.team.list.members.empty": "Enweghị ndị nọ ná otu %s", "commands.team.list.members.success": "Team %s has %s member(s): %s", "commands.team.list.teams.empty": "Enwegh<PERSON> otú obula", "commands.team.list.teams.success": "There are %s team(s): %s", "commands.team.option.collisionRule.success": "<PERSON><PERSON> nkwe<PERSON><PERSON><PERSON><PERSON><PERSON> maka otu %s buzi \"%s\"", "commands.team.option.collisionRule.unchanged": "Onweghi ihe gbanwere. <PERSON><PERSON> n<PERSON>ta enwego uru ah<PERSON>", "commands.team.option.color.success": "Ewelitere agba ahụ maka otu %s to %s", "commands.team.option.color.unchanged": "Onweghi ihe gbanwere. Ot<PERSON> ahụ enwego agba", "commands.team.option.deathMessageVisibility.success": "Ịfụ anya ozi ọnwụ maka otu %s buzi %s", "commands.team.option.deathMessageVisibility.unchanged": "Onweghi ihe gbanwere. <PERSON><PERSON><PERSON> ozi ọnwụ enwego uru ahụ", "commands.team.option.friendlyfire.alreadyDisabled": "Onweghi ihe gbanwere. Ewepụla <PERSON> enyi maka \notu ahu", "commands.team.option.friendlyfire.alreadyEnabled": "Onweghi ihe gbanwere. <PERSON><PERSON><PERSON>la <PERSON> enyi maka otu ahụ", "commands.team.option.friendlyfire.disabled": "Emechiela <PERSON> ọyị maka otu %s", "commands.team.option.friendlyfire.enabled": "Emegheela <PERSON> ọ<PERSON>ị maka otu %s", "commands.team.option.name.success": "Emelitere aha otu %s", "commands.team.option.name.unchanged": "Onweghi ihe gbanwere. Ot<PERSON> ahụ enwego aha ahu", "commands.team.option.nametagVisibility.success": "Ịfụ anya aham<PERSON><PERSON>o maka otu %s buzi %s", "commands.team.option.nametagVisibility.unchanged": "Onweghi ihe gbanwere. <PERSON><PERSON><PERSON> mkpado enwego uru ah<PERSON>", "commands.team.option.prefix.success": "Etin<PERSON>la ndin<PERSON>hu otu na %s", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Onweghi ihe gbanwere. Otú ahụ enweghị ike ịhụ ndị òtù ha anaghị ahụ anya", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Onweghi ihe gbanwere. Ot<PERSON> ahụ nwere ike ịhụ ndị òtù ha anaghị ahụ anya", "commands.team.option.seeFriendlyInvisibles.disabled": "Otú %s enwegh<PERSON>zi ike <PERSON>ụ ndị òtù anaghị afụ anya", "commands.team.option.seeFriendlyInvisibles.enabled": "Otú %s nwe<PERSON>ri ike <PERSON>ụ ndị òtù anaghị afụ anya", "commands.team.option.suffix.success": "Etinyela Ndịnaazụ otu na %s", "commands.team.remove.success": "Otú ewepugoro %s", "commands.teammsg.failed.noteam": "I ga-anoriri na ofu otu iji zie otu gị ozi", "commands.teleport.invalidPosition": "Ọnọdụ ezighi ezi maka teleport", "commands.teleport.success.entity.multiple": "Telepọted %s otu na %s", "commands.teleport.success.entity.single": "Ebuputara %s ka %s", "commands.teleport.success.location.multiple": "Telepọted %s otu na %s, %s,%s", "commands.teleport.success.location.single": "Telepọted %s na %s, %s,%s", "commands.test.batch.starting": "Starting environment %s batch %s", "commands.test.clear.error.no_tests": "Could not find any tests to clear", "commands.test.clear.success": "Cleared %s structure(s)", "commands.test.coordinates": "%s, %s, %s", "commands.test.coordinates.copy": "Click to copy to clipboard", "commands.test.create.success": "Created test setup for test %s", "commands.test.error.no_test_containing_pos": "Can't find a test instance that contains %s, %s, %s", "commands.test.error.no_test_instances": "Found no test instances", "commands.test.error.non_existant_test": "Test %s could not be found", "commands.test.error.structure_not_found": "Test structure %s could not be found", "commands.test.error.test_instance_not_found": "Test instance block entity could not be found", "commands.test.error.test_instance_not_found.position": "Test instance block entity could not be found for test at %s, %s, %s", "commands.test.error.too_large": "The structure size must be less than %s blocks along each axis", "commands.test.locate.done": "Finished locating, found %s structure(s)", "commands.test.locate.found": "Found structure at: %s (distance: %s)", "commands.test.locate.started": "Started locating test structures, this might take a while...", "commands.test.no_tests": "No tests to run", "commands.test.relative_position": "Position relative to %s: %s", "commands.test.reset.error.no_tests": "Could not find any tests to reset", "commands.test.reset.success": "Reset %s structure(s)", "commands.test.run.no_tests": "No tests found", "commands.test.run.running": "Running %s test(s)...", "commands.test.summary": "Game Test complete! %s test(s) were run", "commands.test.summary.all_required_passed": "All required tests passed :)", "commands.test.summary.failed": "%s required test(s) failed :(", "commands.test.summary.optional_failed": "%s optional test(s) failed", "commands.tick.query.percentiles": "Percentiles: P50: %sms P95: %sms P99: %sms, sample: %s", "commands.tick.query.rate.running": "Target tick rate: %s per second.\nAverage time per tick: %sms (Target: %sms)", "commands.tick.query.rate.sprinting": "Target tick rate: %s per second (ignored, reference only).\nAverage time per tick: %sms", "commands.tick.rate.success": "Set the target tick rate to %s per second", "commands.tick.sprint.report": "Sprint completed with %s ticks per second, or %s ms per tick", "commands.tick.sprint.stop.fail": "No tick sprint in progress", "commands.tick.sprint.stop.success": "Interrupted the current tick sprint", "commands.tick.status.frozen": "The game is frozen", "commands.tick.status.lagging": "The game is running, but can't keep up with the target tick rate", "commands.tick.status.running": "The game is running normally", "commands.tick.status.sprinting": "The game is sprinting", "commands.tick.step.fail": "Unable to step the game - the game must be frozen first", "commands.tick.step.stop.fail": "No tick step in progress", "commands.tick.step.stop.success": "Interrupted the current tick step", "commands.tick.step.success": "Stepping %s tick(s)", "commands.time.query": "<PERSON>he na-aku bụ %s", "commands.time.set": "Tinye oge na %s", "commands.title.cleared.multiple": "Aha Ehichapuru maka ndị egwuregwu %s", "commands.title.cleared.single": "Aha Ehichapuru maka %s", "commands.title.reset.multiple": "<PERSON><PERSON> n<PERSON><PERSON> aha maka%s nd<PERSON> egwuregwu", "commands.title.reset.single": "<PERSON><PERSON> n<PERSON> aha maka %s", "commands.title.show.actionbar.multiple": "<PERSON><PERSON><PERSON> aha igwe ihe omume ọhụrụ maka %s ndị egwure<PERSON>wu", "commands.title.show.actionbar.single": "<PERSON>osi aha igwe ihe omume ọhụrụ maka %s", "commands.title.show.subtitle.multiple": "<PERSON><PERSON><PERSON> maka%s ndị egwure<PERSON>wu", "commands.title.show.subtitle.single": "<PERSON><PERSON><PERSON>ụ<PERSON>ụ maka %s", "commands.title.show.title.multiple": "<PERSON><PERSON><PERSON> aha <PERSON><PERSON> maka %s ndị egwuregwu", "commands.title.show.title.single": "Ngosi aha <PERSON> maka %s", "commands.title.times.multiple": "Gbanwere oge ngosi aha maka %s ndị egwuregwu", "commands.title.times.single": "Gbanwere oge ngosi aha maka %s", "commands.transfer.error.no_players": "Must specify at least one player to transfer", "commands.transfer.success.multiple": "Transferring %s players to %s:%s", "commands.transfer.success.single": "Transferring %s to %s:%s", "commands.trigger.add.success": "Kpalitere %s ( agbakwunyere %s na <PERSON><PERSON><PERSON> ah<PERSON>a)", "commands.trigger.failed.invalid": "I nwere ike ikpalite naanị ihe achomachọ ndị nke bụ ụdị enwere ike ikpalite", "commands.trigger.failed.unprimed": "Inweghi ike ikpalite ihe achomachọ nkea", "commands.trigger.set.success": "Kpalitere %s ( etinyere ọnụ ahịa na %s)", "commands.trigger.simple.success": "Kpalitere %s", "commands.version.build_time": "build_time = %s", "commands.version.data": "data = %s", "commands.version.header": "Server version info:", "commands.version.id": "id = %s", "commands.version.name": "name = %s", "commands.version.pack.data": "pack_data = %s", "commands.version.pack.resource": "pack_resource = %s", "commands.version.protocol": "protocol = %s (%s)", "commands.version.series": "series = %s", "commands.version.stable.no": "stable = no", "commands.version.stable.yes": "stable = yes", "commands.waypoint.list.empty": "No waypoints in %s", "commands.waypoint.list.success": "%s waypoint(s) in %s: %s", "commands.waypoint.modify.color": "Waypoint color is now %s", "commands.waypoint.modify.color.reset": "Reset waypoint color", "commands.waypoint.modify.style": "Waypoint style changed", "commands.weather.set.clear": "<PERSON><PERSON> ihu igwe i<PERSON>", "commands.weather.set.rain": "Tinye ihu igwe ka mmiri zoo", "commands.weather.set.thunder": "Tinye ihu igwe na mmiri ozuzo na égbè eluigwe", "commands.whitelist.add.failed": "Agunyego onye egwuregwu na ihe nguputa ocha", "commands.whitelist.add.success": "Agbakwunyere %s na ndepụta aha", "commands.whitelist.alreadyOff": "Agbanyuola ihe nguputa ocha", "commands.whitelist.alreadyOn": "Agbanyego Ihe nguputa ocha", "commands.whitelist.disabled": "A gbanyụrụ whitelist ugbu a", "commands.whitelist.enabled": "A gbanyelarị whitelist", "commands.whitelist.list": "There are %s whitelisted player(s): %s", "commands.whitelist.none": "<PERSON>we<PERSON><PERSON> ndị egwure<PERSON>wu <PERSON>", "commands.whitelist.reloaded": "<PERSON><PERSON><PERSON><PERSON><PERSON> aha nd<PERSON>", "commands.whitelist.remove.failed": "Agunyego onye egwuregwu na ihe nguputa ocha", "commands.whitelist.remove.success": "Ewepụrụ %s na ndepụta aha", "commands.worldborder.center.failed": "Onweghi ihe gbanwere. N'otu <PERSON>wa nile agbakoola ebe ahụ", "commands.worldborder.center.success": "Tinye etiti ogige ụwa na %s, %s", "commands.worldborder.damage.amount.failed": "Onweghi ihe gbanwere. Ihe mmebi <PERSON>wa nile aburula maka idi anya ah<PERSON>", "commands.worldborder.damage.amount.success": "Nweta mmebi nke ụwa %s kwa mgbochi ọ bụla", "commands.worldborder.damage.buffer.failed": "Onweghi ihe gbanwere. Efere mmebi nke nsọtu <PERSON>wa nile aburula nya bụ <PERSON> anya", "commands.worldborder.damage.buffer.success": "Set the world border damage buffer to %s block(s)", "commands.worldborder.get": "The world border is currently %s block(s) wide", "commands.worldborder.set.failed.big": "Oke ụwa enweghị ike ibu karịa %s blocks n'obosara", "commands.worldborder.set.failed.far": "Oke oke ụwa enweghị ike <PERSON> karịa ngọngọ %s", "commands.worldborder.set.failed.nochange": "Were Akwụkwọ", "commands.worldborder.set.failed.small": "Ókèala ụwa enweghị ike <PERSON> ihe na-erughị otu ngọng<PERSON> n'o<PERSON>ara", "commands.worldborder.set.grow": "Izụlite ogige ụwa na %s ngongo n'ibu karịa %s sek<PERSON>nd", "commands.worldborder.set.immediate": "Set the world border to %s block(s) wide", "commands.worldborder.set.shrink": "Shrinking the world border to %s block(s) wide over %s second(s)", "commands.worldborder.warning.distance.failed": "Onwegh<PERSON> ihe gbanwere. <PERSON><PERSON> aka na nti nke nsọtu <PERSON>wa nile aburula <PERSON> anya", "commands.worldborder.warning.distance.success": "Set the world border warning distance to %s block(s)", "commands.worldborder.warning.time.failed": "Onwegh<PERSON> ihe gbanwere. Idọ aka na ntị nke nsọtu <PERSON>wa nile aburula maka oge ahụ", "commands.worldborder.warning.time.success": "Set the world border warning time to %s second(s)", "compliance.playtime.greaterThan24Hours": "<PERSON><PERSON><PERSON><PERSON> egwu ihe karịrị awa iri abụọ na anọ", "compliance.playtime.hours": "Igwuola egwu %s awa (s)", "compliance.playtime.message": "Oke egwuregwu nwere ike ịmet<PERSON>ta ihe omume kwa Ụbọchị", "connect.aborted": "<PERSON>g<PERSON><PERSON>", "connect.authorizing": "Na-abanye...", "connect.connecting": "Ijikọ na sava ahụ...", "connect.encrypting": "Encrypting...", "connect.failed": "Ijikọtaghị na sava ahụ", "connect.failed.transfer": "Connection failed while transferring to the server", "connect.joining": "Ịbanye ụwa...", "connect.negotiating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "connect.reconfiging": "Reconfiguring...", "connect.reconfiguring": "Reconfiguring...", "connect.transferring": "Transferring to new server...", "container.barrel": "Barrel", "container.beacon": "Beacon", "container.beehive.bees": "Bees: %s / %s", "container.beehive.honey": "Honey: %s / %s", "container.blast_furnace": "Blast Furnace", "container.brewing": "Brewing Stand", "container.cartography_table": "Cartography Table", "container.chest": "Chest", "container.chestDouble": "<PERSON><PERSON><PERSON><PERSON>", "container.crafter": "Crafter", "container.crafting": "Nkà", "container.creative": "Nhọrọ <PERSON>kebi", "container.dispenser": "Dispenser", "container.dropper": "Dropper", "container.enchant": "Enchant", "container.enchant.clue": "%s . . . ?", "container.enchant.lapis.many": "%s Lapis <PERSON>\n", "container.enchant.lapis.one": "1 Lapis Lazuli", "container.enchant.level.many": "%s Enchantment Etoju", "container.enchant.level.one": "1 Enchantment Ọkwa", "container.enchant.level.requirement": "Ọkwa chọrọ: %s", "container.enderchest": "<PERSON><PERSON> Chest", "container.furnace": "Furnace", "container.grindstone_title": "Mmezi & Disenchant", "container.hopper": "<PERSON><PERSON>", "container.inventory": "Inventory", "container.isLocked": "%s akpọ<PERSON>ri!", "container.lectern": "Lectern", "container.loom": "Loom", "container.repair": "Mmezi & Aha", "container.repair.cost": "Ọnụ Enchantment: %1$s", "container.repair.expensive": "Oke ọnụ!", "container.shulkerBox": "Shulker Box", "container.shulkerBox.itemCount": "%s x%s", "container.shulkerBox.more": "na %s Ọzọ...", "container.shulkerBox.unknownContents": "???????", "container.smoker": "Smoker", "container.spectatorCantOpen": "<PERSON>weghi ike imeghe. A ga-ebunye ya ọzọ.", "container.stonecutter": "<PERSON><PERSON><PERSON>", "container.upgrade": "Kwalite Gear", "container.upgrade.error_tooltip": "Item can't be upgraded this way", "container.upgrade.missing_template_tooltip": "Add <PERSON> Template", "controls.keybinds": "Key njikọ...", "controls.keybinds.duplicateKeybinds": "This key is also used for:\n%s", "controls.keybinds.title": "Key njik<PERSON>", "controls.reset": "Tọgharia", "controls.resetAll": "Tọgharia igodo", "controls.title": "Njikwa", "createWorld.customize.buffet.biome": "Biko <PERSON> biome", "createWorld.customize.buffet.title": "Buffet ụwa customization", "createWorld.customize.flat.height": "<PERSON><PERSON>", "createWorld.customize.flat.layer": "%s", "createWorld.customize.flat.layer.bottom": "N'ala - %s", "createWorld.customize.flat.layer.top": "N’elu - %s", "createWorld.customize.flat.removeLayer": "<PERSON><PERSON> oyi akwa", "createWorld.customize.flat.tile": "<PERSON><PERSON>", "createWorld.customize.flat.title": "Nhazi Superflat", "createWorld.customize.presets": "Preset", "createWorld.customize.presets.list": "Ọzọ, lee <PERSON><PERSON><PERSON><PERSON><PERSON> anyị mere na mbụ!", "createWorld.customize.presets.select": "<PERSON><PERSON>", "createWorld.customize.presets.share": "\n<PERSON><PERSON><PERSON><PERSON> <PERSON>re onye gị na onye na-ere ahịa? <PERSON>ri igbe dị n'okpuru!", "createWorld.customize.presets.title": "<PERSON><PERSON><PERSON><PERSON>", "createWorld.preparing": "Nkwado maka okike <PERSON>wa...", "createWorld.tab.game.title": "Game", "createWorld.tab.more.title": "More", "createWorld.tab.world.title": "Ụwa", "credits_and_attribution.button.attribution": "Attribution", "credits_and_attribution.button.credits": "Credits", "credits_and_attribution.button.licenses": "Licenses", "credits_and_attribution.screen.title": "Credits and Attribution", "dataPack.bundle.description": "Enables experimental Bundle item", "dataPack.bundle.name": "Bundles", "dataPack.locator_bar.description": "Show the direction of other players in multiplayer", "dataPack.locator_bar.name": "Locator Bar", "dataPack.minecart_improvements.description": "Improved movement for Minecarts", "dataPack.minecart_improvements.name": "Minecart Improvements", "dataPack.redstone_experiments.description": "Experimental Redstone changes", "dataPack.redstone_experiments.name": "Redstone Experiments", "dataPack.title": "<PERSON><PERSON><PERSON><PERSON> ngwu<PERSON>wu data", "dataPack.trade_rebalance.description": "Updated trades for Villagers", "dataPack.trade_rebalance.name": "Villager Trade Rebalance", "dataPack.update_1_20.description": "New features and content for Minecraft 1.20", "dataPack.update_1_20.name": "Update 1.20", "dataPack.update_1_21.description": "New features and content for Minecraft 1.21", "dataPack.update_1_21.name": "Update 1.21", "dataPack.validation.back": "Laghachi azụ", "dataPack.validation.failed": "Nkwado data ezughị ezu!", "dataPack.validation.reset": "Tọgharia ka Ndi an-kpọ", "dataPack.validation.working": "Na-akwado nkwado data ndị a họpụtara...", "dataPack.vanilla.description": "Data ndabere maka Minecraft", "dataPack.vanilla.name": "<PERSON><PERSON><PERSON>", "dataPack.winter_drop.description": "New features and content for the Winter Drop", "dataPack.winter_drop.name": "Winter Drop", "datapackFailure.safeMode": "Ọnọdụ Nchek<PERSON>", "datapackFailure.safeMode.failed.description": "This world contains invalid or corrupted save data.", "datapackFailure.safeMode.failed.title": "Failed to load world in Safe Mode.", "datapackFailure.title": "Errors in currently selected data packs prevented the world from loading.\nYou can either try to load it with only the vanilla data pack (\"safe mode\"), or go back to the title screen and fix it manually.", "death.attack.anvil": "%1$s e jiri n<PERSON> dara daa", "death.attack.anvil.player": "%1$s bụ onye na-ada ada ebe ọ na-alụ ọ<PERSON>ụ %2$s", "death.attack.arrow": "%1$s gbagburu ya %2$s", "death.attack.arrow.item": "%1$s gbagburu ya %2$s eji %3$s", "death.attack.badRespawnPoint.link": "Ebumnuche Egwuregwu", "death.attack.badRespawnPoint.message": "%1$s gburu site na %2$s", "death.attack.cactus": "%1$s eworu ọnwụ", "death.attack.cactus.player": "%1$s batara n'ime kaktus ebe ọ na-achọ <PERSON>ụ %2$s", "death.attack.cramming": "%1$s bya agbakash<PERSON>hu", "death.attack.cramming.player": "%1$s gbue egbugbu %2$s", "death.attack.dragonBreath": "%1$s was roasted in dragon's breath", "death.attack.dragonBreath.player": "%1$s was roasted in dragon's breath by %2$s", "death.attack.drown": "%1$s rie mmiri", "death.attack.drown.player": "%1$s mmiri riri ebe ị na-achọ ịgbapụ %2$s", "death.attack.dryout": "%1$s nwụr<PERSON> n'ihi akpịrị ịkpọ nkụ", "death.attack.dryout.player": "%1$s nwụr<PERSON> n'ihi akpịrị ịkpọ nkụ ebe ọ na -achọ <PERSON>ụ %2$s", "death.attack.even_more_magic": "%1$s gburu <PERSON><PERSON><PERSON>na majik karịa", "death.attack.explosion": "%1$s gbawara", "death.attack.explosion.player": "%1$s gbawara site %2$s", "death.attack.explosion.player.item": "%1$s gbawara site %2$s eji %3$s", "death.attack.fall": "%1$s see n’ala n’ike nke ukwuu", "death.attack.fall.player": "%1$s see n'ala ike nke ukwuu mgbe ị na-achọ <PERSON>ụ %2$s", "death.attack.fallingBlock": "%1$s bya egbushia mkpọla-ọchaa", "death.attack.fallingBlock.player": "%1$s bụụr<PERSON> onye na-ada ada ebe ọ<PERSON>ụ %2$s", "death.attack.fallingStalactite": "%1$s ka stalactite na -ada ada k<PERSON>dara ya", "death.attack.fallingStalactite.player": "%1$s ka stalactite dara mgbe ọ na -alụ ọ<PERSON>ụ %2$s", "death.attack.fireball": "%1$s gbagburu ya %2$s", "death.attack.fireball.item": "%1$s gbagburu ya %2$s eji %3$s", "death.attack.fireworks": "%1$s pụọ na a Bang", "death.attack.fireworks.item": "%1$s wee pụọ na a Bang n'ihi na ezimezi oku ọ<PERSON>ụ si %3$s site na %2$s", "death.attack.fireworks.player": "%1$s pụọ na mkpọtụ mgbe ị na-alụ ọ<PERSON>ụ %2$s", "death.attack.flyIntoWall": "%1$s enweta ume ike", "death.attack.flyIntoWall.player": "%1$s enwe ume ike mgbe <PERSON> na-achọ <PERSON>ụ %2$s", "death.attack.freeze": "%1$s froze ruo ọnwụ", "death.attack.freeze.player": "%1$s bụ oyi kpọnwụ<PERSON><PERSON> ọnwụ %2$s", "death.attack.generic": "%1$s nwụrụ", "death.attack.generic.player": "%1$s nwụrụ n'ihi %2$s", "death.attack.genericKill": "%1$s was killed", "death.attack.genericKill.player": "%1$s was killed while fighting %2$s", "death.attack.hotFloor": "%1$s achọpụta n'ala bụ lava", "death.attack.hotFloor.player": "%1$s walked into the danger zone due to %2$s", "death.attack.inFire": "%1$s rigoro n'igwe oku", "death.attack.inFire.player": "%1$s jere ije n'ime ọkụ mgbe ị na-alụ ọgụ %2$s", "death.attack.inWall": "%1$s mu gude obu mu mee", "death.attack.inWall.player": "%1$s toro ume na mgbidi ebe a na-alụ ọgụ %2$s", "death.attack.indirectMagic": "%1$s gburu site na %2$s iji anwansi", "death.attack.indirectMagic.item": "%1$s gburu site na %2$s eji %3$s", "death.attack.lava": "%1$s gbalịrị igwu mmiri na lava", "death.attack.lava.player": "%1$s gbalịrị igwu mmiri na lava iji gbapụ %2$s", "death.attack.lightningBolt": "%1$s gbagburu onwe ya", "death.attack.lightningBolt.player": "%1$s merụr<PERSON> ka ọ na-al<PERSON>ụ %2$s", "death.attack.mace_smash": "%1$s was smashed by %2$s", "death.attack.mace_smash.item": "%1$s was smashed by %2$s with %3$s", "death.attack.magic": "%1$s gburu anwansi", "death.attack.magic.player": "<PERSON><PERSON><PERSON> gburu $ 1 $ s ebe ị na-anwa ịg<PERSON>ụ% 2 $ s", "death.attack.message_too_long": "Actually, the message was too long to deliver fully. Sorry! Here's a stripped version: %s", "death.attack.mob": "%1$s gburu ya %2$s", "death.attack.mob.item": "%1$s gburu ya %2$s eji %3$s", "death.attack.onFire": "%1$s akpọ ya oku", "death.attack.onFire.item": "%1$s gbara <PERSON><PERSON><PERSON> ka ọ na-al<PERSON> %2$s na-eji %3$s", "death.attack.onFire.player": "%1$s emere ya ka o sie ike mgbe ona agha %2$s", "death.attack.outOfWorld": "%1$s dapụr<PERSON> n'ụwa", "death.attack.outOfWorld.player": "%1$s achọghị ibi n'otu <PERSON>wa dịka %2$s", "death.attack.outsideBorder": "%1$s left the confines of this world", "death.attack.outsideBorder.player": "%1$s left the confines of this world while fighting %2$s", "death.attack.player": "%1$s was slain by %2$s", "death.attack.player.item": "%1$s was slain by %2$s using %3$s", "death.attack.sonic_boom": "%1$s ebibiri shreik n'ike mmụọ", "death.attack.sonic_boom.item": "%1$s kpochapụrụ site n'iti mkpu eboro ebubo ka ọ na-agbalị ịgbanarị %2$s iji %3$s", "death.attack.sonic_boom.player": "%1$s Ebibiri shreik n'ike mmụọ ka ọ na-ag<PERSON>ụ %2$s", "death.attack.stalagmite": "A kwụgburu %1$s na stalagmite", "death.attack.stalagmite.player": "%1$s a kpọgidere ya na stalagmite ka ọ na -al<PERSON>ụ %2$s", "death.attack.starve": "%1$s ụnwu iya", "death.attack.starve.player": "%1$s agụụ nwụọ mgbe ị na-alụ ọgụ %2$s", "death.attack.sting": "%1$s ama akpa", "death.attack.sting.item": "%2$s gbagburu %1$s n'iji %3$s", "death.attack.sting.player": "%1$s dabara na site na %2$s", "death.attack.sweetBerryBush": "%1$s bya anw<PERSON>hu lẹ mkpụkpu buredi", "death.attack.sweetBerryBush.player": "%1$s e jiri osisi beri dị ụtọ tụgbuo ya ka ọ na-achọ ịgba<PERSON>ụ %2$s", "death.attack.thorns": "%1$s nọ gbue egbu im<PERSON><PERSON> ahụ %2$s", "death.attack.thorns.item": "%1$s gburu site na %3$s na-anwa im<PERSON><PERSON> ah<PERSON> %2$s", "death.attack.thrown": "%1$s gburu site na %2$s", "death.attack.thrown.item": "%1$s gburu site na %2$s eji %3$s", "death.attack.trident": "%1$s kpọgburu ya %2$s", "death.attack.trident.item": "%1$s kpọgburu ya %2$s na %3$s", "death.attack.wither": "%1$s akpọnwụ", "death.attack.wither.player": "%1$s akpọnwụwo mgbe ị na-alụ ọ<PERSON>ụ %2$s", "death.attack.witherSkull": "%1$s gbagburu site na okpokoro isi si %2$s", "death.attack.witherSkull.item": "%1$s gbagburu site na okpokoro isi site na %2$s iji %3$s", "death.fell.accident.generic": "%1$s si n'ebe di elu dapu", "death.fell.accident.ladder": "%1$s dapụrụ n'ọkwá", "death.fell.accident.other_climbable": "%1$s dara mgbe ha na-arị ugwu", "death.fell.accident.scaffolding": "%1$s dapụrụ scaffolding", "death.fell.accident.twisting_vines": "%1$s dara site na ụfọdụ mkpụrụ vaịn na-agbagọ agbagọ", "death.fell.accident.vines": "%1$s dap<PERSON><PERSON><PERSON> osisi vaịn", "death.fell.accident.weeping_vines": "%1$s dara na <PERSON>f<PERSON> osisi vaịn na-akwa <PERSON>kw<PERSON>", "death.fell.assist": "%1$s na-aga ibibi daa %2$s", "death.fell.assist.item": "%1$s na-aga ibibi daa %2$s eji %3$s", "death.fell.finish": "%1$s daalu oke ma mechaa %2$s", "death.fell.finish.item": "%1$s daalu oke ma mechaa %2$s eji %3$s", "death.fell.killer": "%1$s na-aga ibibi daa", "deathScreen.quit.confirm": "O doro gị anya na ị chọrọ ịkwụsị ya?", "deathScreen.respawn": "<PERSON><PERSON><PERSON><PERSON>", "deathScreen.score": "Akara", "deathScreen.score.value": "Score: %s", "deathScreen.spectate": "Spect World", "deathScreen.title": "Di Nwụ<PERSON>ụ!", "deathScreen.title.hardcore": "Ngwụcha egwuregwu!", "deathScreen.titleScreen": "Title Screen", "debug.advanced_tooltips.help": "F3 + H = <PERSON><PERSON><PERSON><PERSON><PERSON> dị elu", "debug.advanced_tooltips.off": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ngwaọrụ dị elu: zoro ezo", "debug.advanced_tooltips.on": "Ntụziaka ngwa<PERSON><PERSON><PERSON> dị elu: egosiri", "debug.chunk_boundaries.help": "F3 + G = <PERSON><PERSON> oke <PERSON>", "debug.chunk_boundaries.off": "Oke chunk: zoro ezo", "debug.chunk_boundaries.on": "Oke ala chunk: egosiri", "debug.clear_chat.help": "F3 + D = Kpochap<PERSON> nkata", "debug.copy_location.help": "F3 + C = <PERSON>uo ọn<PERSON><PERSON><PERSON> dị ka /tp iwu, jide F3 + C iji mebie egwuregwu ahụ", "debug.copy_location.message": "<PERSON><PERSON><PERSON> ka klipb<PERSON>", "debug.crash.message": "F3 + C na -agbada. Nke a ga -emebi egwure<PERSON>wu ahụ belụsọ na ewepụtara ya.", "debug.crash.warning": "Na -akụtu na %s...", "debug.creative_spectator.error": "Unable to switch game mode; no permission", "debug.creative_spectator.help": "F3 + N = Cycle previous game mode <-> spectator", "debug.dump_dynamic_textures": "Saved dynamic textures to %s", "debug.dump_dynamic_textures.help": "F3 + S = Dump dynamic textures", "debug.gamemodes.error": "Enwegh<PERSON> ike imeghe switcher <PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>; enwegh<PERSON> ikike", "debug.gamemodes.help": "F3 + F4 = <PERSON><PERSON><PERSON> ọn<PERSON>dụ ọnọdụ egwuregwu", "debug.gamemodes.press_f4": "[ F4 ]", "debug.gamemodes.select_next": "%s Osote", "debug.help.help": "F3 + Q = <PERSON><PERSON> nde<PERSON>ta a", "debug.help.message": "Key njikọ:", "debug.inspect.client.block": "Epomiri data ngọngọ ndị ahịa n'ak<PERSON><PERSON><PERSON> klipbọọdụ", "debug.inspect.client.entity": "Epomiri data otu onye ahịa n'akụ<PERSON>ụ klipb<PERSON>dụ", "debug.inspect.help": "F3 + I = Detuo ihe ma ọ bụ gbochie data na klipbọọdụ", "debug.inspect.server.block": "Eomiri data ngọngọ n'akụkụ ihe nkesa na klipbọọdụ", "debug.inspect.server.entity": "Epomiri data ihe dị n'akụkụ ihe nkesa na klipbọọdụ", "debug.pause.help": "F3 + Esc = Kwụsị na -enweghị nkwụsịtụ menu (ọ bụrụ na ị kwụsịtụ ga -ekwe omume)", "debug.pause_focus.help": "F3 + P = Kwụsị na mgbado anya", "debug.pause_focus.off": "Kwụsịtụ na mgbado anya furu efu: enwere nkwarụ", "debug.pause_focus.on": "Kwụsị na mgbado anya: agbanyere", "debug.prefix": "[Debug]:", "debug.profiling.help": "F3 + L = Malite/kwụsị profaịlụ", "debug.profiling.start": "Ịmepụta ihe bidoro maka nkeji %s. <PERSON>ri F3 + L kwụsị n'isi", "debug.profiling.stop": "Nrụpụta akwụ<PERSON>. Nsonaazụ echekwara na %s", "debug.reload_chunks.help": "F3 + A = Bugharịa chunks", "debug.reload_chunks.message": "Na -<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>ile", "debug.reload_resourcepacks.help": "F3 + T = <PERSON><PERSON><PERSON><PERSON><PERSON> ng<PERSON> a<PERSON>", "debug.reload_resourcepacks.message": "Ak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ngwungwu ak<PERSON>", "debug.show_hitboxes.help": "F3 + B = Gosi igbe igbe", "debug.show_hitboxes.off": "Igbe igbe: zoro ezo", "debug.show_hitboxes.on": "Igbe igbe: egosiri", "debug.version.header": "Client version info:", "debug.version.help": "F3 + V = Client version info", "demo.day.1": "<PERSON><PERSON> ngosi a ga-ewe <PERSON>b<PERSON>chị ise egwuregwu. Mee ike gị!", "demo.day.2": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "demo.day.3": "<PERSON><PERSON><PERSON><PERSON><PERSON> nke <PERSON>", "demo.day.4": "<PERSON><PERSON><PERSON><PERSON><PERSON> nke anọ", "demo.day.5": "Nke a bụ ụb<PERSON>chị ik<PERSON>azụ gị!", "demo.day.6": "You gafeela <PERSON> nke ise. Jiri%s chek<PERSON><PERSON> n<PERSON>a i<PERSON>yo nke ihe okike gi.", "demo.day.warning": "Oge gị agwụla!", "demo.demoExpired": "Demo oge agwụla!", "demo.help.buy": "<PERSON>ụta ugbu a!", "demo.help.fullWrapped": "<PERSON>he ngosi a ga - ewe <PERSON>b<PERSON>ch<PERSON> 5 n'ime egwuregwu (ihe dịka elekere 1 na nkeji iri anọ nke ezigbo oge). <PERSON><PERSON>gan<PERSON>u maka hints! Nwee anụrị!", "demo.help.inventory": "Jiri %1$s isi iji mepee ngwa ahia gi", "demo.help.jump": "Bịa site na ịpị %1$s igodo", "demo.help.later": "Nọgide na-egwu!", "demo.help.movement": "Jiri %1$s, %2$s, %3$s, %4$s igodo na ò<PERSON>é ka <PERSON>ị", "demo.help.movementMouse": "<PERSON><PERSON> okiri<PERSON>ri mee ihe gburugburu", "demo.help.movementShort": "Bugharịa site na ịpị %1$s, %2$s, %3$s, %4$s igodo", "demo.help.title": "Ọnọdụ <PERSON><PERSON><PERSON><PERSON><PERSON>", "demo.remainingTime": "<PERSON><PERSON> f<PERSON><PERSON><PERSON>: %s", "demo.reminder": "Oge ngosi emeb<PERSON>. <PERSON><PERSON><PERSON> egwuregwu iji gaa n'ihu ma ọ bụ ịmalite ụwa ọhụrụ!", "difficulty.lock.question": "Ì ji n’aka na ịchọr<PERSON> igbachi ike nke ụwa a? Nke a ga-edo ụwa a ka ọ dịrị mgbe niile %1$s, will gaghị enwe ike ịgbanwe nke ahụ ọzọ.", "difficulty.lock.title": "Mkpọ<PERSON> Mkpọchi Worldwa", "disconnect.endOfStream": "Njedebe nke iyi", "disconnect.exceeded_packet_rate": "<PERSON><PERSON><PERSON> oke <PERSON> ng<PERSON>wu", "disconnect.genericReason": "%s", "disconnect.ignoring_status_request": "Ignoring status request", "disconnect.loginFailedInfo": "Emezighi abanye na: %s", "disconnect.loginFailedInfo.insufficientPrivileges": "Ọtụtụ mmadụ nwere nkwarụ. <PERSON><PERSON> nt<PERSON> akaụntụ Microsoft gị.", "disconnect.loginFailedInfo.invalidSession": "<PERSON><PERSON> (Gbal<PERSON><PERSON> egwuregwu gị na onye nrụpụta)", "disconnect.loginFailedInfo.serversUnavailable": "<PERSON><PERSON><PERSON><PERSON> ike <PERSON> sava nyocha ugbu a. <PERSON><PERSON>.", "disconnect.loginFailedInfo.userBanned": "Amachibidoro gị igwu egwu n'<PERSON>ị", "disconnect.lost": "Njikọ Etufuru", "disconnect.packetError": "Network Protocol Error", "disconnect.spam": "Egburu maka spamming", "disconnect.timeout": "Gbalị<PERSON>", "disconnect.transfer": "Transferred to another server", "disconnect.unknownHost": "<PERSON><PERSON><PERSON> onye n<PERSON>a", "download.pack.failed": "%s out of %s pack(s) failed to download", "download.pack.progress.bytes": "Progress: %s (total size unknown)", "download.pack.progress.percent": "Progress: %s%%", "download.pack.title": "Downloading resource pack %s/%s", "editGamerule.default": "Ndabara: %s", "editGamerule.title": "<PERSON><PERSON> i<PERSON> eg<PERSON><PERSON>wu", "effect.duration.infinite": "∞", "effect.minecraft.absorption": "<PERSON><PERSON><PERSON>", "effect.minecraft.bad_omen": "<PERSON><PERSON>", "effect.minecraft.blindness": "Ìsì", "effect.minecraft.conduit_power": "<PERSON><PERSON>", "effect.minecraft.darkness": "Ọchịchịrị", "effect.minecraft.dolphins_grace": "<PERSON><PERSON> Do<PERSON>", "effect.minecraft.fire_resistance": "<PERSON><PERSON><PERSON><PERSON> oku", "effect.minecraft.glowing": "<PERSON><PERSON><PERSON>wu enwu", "effect.minecraft.haste": "Ng<PERSON> ngwa", "effect.minecraft.health_boost": "<PERSON><PERSON> ike", "effect.minecraft.hero_of_the_village": "<PERSON><PERSON>", "effect.minecraft.hunger": "<PERSON><PERSON><PERSON><PERSON>", "effect.minecraft.infested": "Infested", "effect.minecraft.instant_damage": "<PERSON><PERSON><PERSON>", "effect.minecraft.instant_health": "<PERSON><PERSON><PERSON>", "effect.minecraft.invisibility": "<PERSON><PERSON><PERSON> ume", "effect.minecraft.jump_boost": "A<PERSON>ụlikwa elu", "effect.minecraft.levitation": "Itationgba mbọ", "effect.minecraft.luck": "Chioma", "effect.minecraft.mining_fatigue": "Ngwuputa I<PERSON>", "effect.minecraft.nausea": "Ọgbụgbọ", "effect.minecraft.night_vision": "Ọhụụ abalị", "effect.minecraft.oozing": "Oozing", "effect.minecraft.poison": "Nsi", "effect.minecraft.raid_omen": "Raid Omen", "effect.minecraft.regeneration": "<PERSON><PERSON><PERSON>", "effect.minecraft.resistance": "Nguzog<PERSON>", "effect.minecraft.saturation": "Saturation", "effect.minecraft.slow_falling": "<PERSON><PERSON>", "effect.minecraft.slowness": "Dị nwayọ<PERSON>", "effect.minecraft.speed": "Ọsọ", "effect.minecraft.strength": "<PERSON><PERSON>", "effect.minecraft.trial_omen": "Trial Omen", "effect.minecraft.unluck": "Ọdachi ọj<PERSON><PERSON>", "effect.minecraft.water_breathing": "<PERSON><PERSON><PERSON> ume", "effect.minecraft.weakness": "<PERSON><PERSON><PERSON> ike", "effect.minecraft.weaving": "Weaving", "effect.minecraft.wind_charged": "Wind Charged", "effect.minecraft.wither": "<PERSON>er", "effect.none": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.level.1": "<PERSON><PERSON>", "enchantment.level.10": "<PERSON><PERSON>", "enchantment.level.2": "II", "enchantment.level.3": "III", "enchantment.level.4": "IV", "enchantment.level.5": "V", "enchantment.level.6": "VI", "enchantment.level.7": "<PERSON><PERSON>", "enchantment.level.8": "<PERSON><PERSON><PERSON>", "enchantment.level.9": "Iteghete", "enchantment.minecraft.aqua_affinity": "Njikọ Mmiri", "enchantment.minecraft.bane_of_arthropods": "Bane nke ogbu na nkwonkwo", "enchantment.minecraft.binding_curse": "Nkọcha nke indke", "enchantment.minecraft.blast_protection": "Gbawara Nchedo", "enchantment.minecraft.breach": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.channeling": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.density": "Density", "enchantment.minecraft.depth_strider": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.efficiency": "<PERSON><PERSON><PERSON> Ọrụ", "enchantment.minecraft.feather_falling": "Nkpu nku ya", "enchantment.minecraft.fire_aspect": "Akụ<PERSON><PERSON>", "enchantment.minecraft.fire_protection": "Nchedo Ọkụ", "enchantment.minecraft.flame": "<PERSON><PERSON>", "enchantment.minecraft.fortune": "Uba", "enchantment.minecraft.frost_walker": "<PERSON><PERSON>", "enchantment.minecraft.impaling": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.infinity": "Njedebe", "enchantment.minecraft.knockback": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.looting": "Na-<PERSON><PERSON><PERSON><PERSON><PERSON> ihe", "enchantment.minecraft.loyalty": "Igu<PERSON><PERSON> ike n'ihe", "enchantment.minecraft.luck_of_the_sea": "<PERSON><PERSON>", "enchantment.minecraft.lure": "Agara", "enchantment.minecraft.mending": "Na-agbazinye ihe", "enchantment.minecraft.multishot": "Nnukwugbara", "enchantment.minecraft.piercing": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.power": "<PERSON><PERSON>", "enchantment.minecraft.projectile_protection": "Nchekwa oru", "enchantment.minecraft.protection": "Nchedo", "enchantment.minecraft.punch": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.quick_charge": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.respiration": "<PERSON><PERSON><PERSON><PERSON>", "enchantment.minecraft.riptide": "Riptide", "enchantment.minecraft.sharpness": "Nkọ", "enchantment.minecraft.silk_touch": "Silk Aka", "enchantment.minecraft.smite": "Tie", "enchantment.minecraft.soul_speed": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ọsọ", "enchantment.minecraft.sweeping": "Ibul<PERSON>", "enchantment.minecraft.sweeping_edge": "Sweeping Edge", "enchantment.minecraft.swift_sneak": "Ngwa ngwa Sinik", "enchantment.minecraft.thorns": "<PERSON><PERSON><PERSON>", "enchantment.minecraft.unbreaking": "Na-agbag<PERSON>", "enchantment.minecraft.vanishing_curse": "Nkọcha nke <PERSON>", "enchantment.minecraft.wind_burst": "<PERSON> Burst", "entity.minecraft.acacia_boat": "Acacia Boat", "entity.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "<PERSON><PERSON><PERSON> o<PERSON>", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Armor Stand", "entity.minecraft.arrow": "Arrow", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "entity.minecraft.bamboo_raft": "Bamboo Raft", "entity.minecraft.bat": "<PERSON><PERSON><PERSON>", "entity.minecraft.bee": "Bee", "entity.minecraft.birch_boat": "<PERSON> Boat", "entity.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "entity.minecraft.blaze": "Ọkụ", "entity.minecraft.block_display": "Block Display", "entity.minecraft.boat": "Boat", "entity.minecraft.bogged": "Bogged", "entity.minecraft.breeze": "<PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Wind Charge", "entity.minecraft.camel": "Camel", "entity.minecraft.cat": "Nwamba", "entity.minecraft.cave_spider": "<PERSON><PERSON><PERSON> ududo", "entity.minecraft.cherry_boat": "Cherry Boat", "entity.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "Minecart with Chest", "entity.minecraft.chicken": "Ọkụkọ", "entity.minecraft.cod": "<PERSON><PERSON><PERSON>", "entity.minecraft.command_block_minecart": "Minecart with Command Block", "entity.minecraft.cow": "<PERSON><PERSON>", "entity.minecraft.creaking": "Creaking", "entity.minecraft.creaking_transient": "Creaking", "entity.minecraft.creeper": "<PERSON><PERSON> na-ak<PERSON><PERSON> akpụ", "entity.minecraft.dark_oak_boat": "Dark Oak Boat", "entity.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "entity.minecraft.dolphin": "Dolphin", "entity.minecraft.donkey": "Nwa ịnyịnya ibu", "entity.minecraft.dragon_fireball": "Dragọn Ọkụ ọkụ", "entity.minecraft.drowned": "<PERSON><PERSON><PERSON>", "entity.minecraft.egg": "Etufu<PERSON>", "entity.minecraft.elder_guardian": "Okenye okenye", "entity.minecraft.end_crystal": "End Crystal", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON> Ender Ọla", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.evoker_fangs": "Onyekachi Jọ", "entity.minecraft.experience_bottle": "<PERSON><PERSON><PERSON>Na-adọr<PERSON> adọrọ", "entity.minecraft.experience_orb": "<PERSON><PERSON><PERSON>", "entity.minecraft.eye_of_ender": "Eye of <PERSON>er", "entity.minecraft.falling_block": "Mgbochi Blockda", "entity.minecraft.falling_block_type": "Falling %s", "entity.minecraft.fireball": "Ọkụ ọkụ", "entity.minecraft.firework_rocket": "Firework Rocket", "entity.minecraft.fishing_bobber": "<PERSON><PERSON><PERSON>", "entity.minecraft.fox": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.frog": "Ele", "entity.minecraft.furnace_minecart": "Minecart with Furnace", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.glow_item_frame": "G<PERSON> Item <PERSON>", "entity.minecraft.glow_squid": "<PERSON><PERSON><PERSON>", "entity.minecraft.goat": "<PERSON><PERSON>", "entity.minecraft.guardian": "Onye nche", "entity.minecraft.happy_ghast": "<PERSON>", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Minecart with <PERSON>", "entity.minecraft.horse": "Nyịnya", "entity.minecraft.husk": "okwo", "entity.minecraft.illusioner": "Onye maara ihe", "entity.minecraft.interaction": "Interaction", "entity.minecraft.iron_golem": ".<PERSON><PERSON><PERSON>", "entity.minecraft.item": "<PERSON>he", "entity.minecraft.item_display": "<PERSON><PERSON>", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "Jungle Boat", "entity.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "entity.minecraft.killer_bunny": "Egburu <PERSON>bu", "entity.minecraft.leash_knot": "<PERSON><PERSON>", "entity.minecraft.lightning_bolt": "<PERSON><PERSON><PERSON>", "entity.minecraft.lingering_potion": "Lingering Potion", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "<PERSON>lama i<PERSON>", "entity.minecraft.magma_cube": "Magma Cube", "entity.minecraft.mangrove_boat": "Mangrove Boat", "entity.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "entity.minecraft.marker": "<PERSON><PERSON><PERSON>", "entity.minecraft.minecart": "Minecart", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "Inyinya mule", "entity.minecraft.oak_boat": "Oak Boat", "entity.minecraft.oak_chest_boat": "Oak Boat with Chest", "entity.minecraft.ocelot": "Ocelot", "entity.minecraft.ominous_item_spawner": "Ominous <PERSON>em <PERSON>wner", "entity.minecraft.painting": "Painting", "entity.minecraft.pale_oak_boat": "Pale Oak Boat", "entity.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "Akara", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "<PERSON><PERSON>", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> B<PERSON>", "entity.minecraft.pillager": "Onye na-ap<PERSON>nar<PERSON> mmadụ ihe", "entity.minecraft.player": "<PERSON><PERSON> egwu", "entity.minecraft.polar_bear": "pola agba", "entity.minecraft.potion": "Potion", "entity.minecraft.pufferfish": "Pufferfish", "entity.minecraft.rabbit": "<PERSON><PERSON> o<PERSON>", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.sheep": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "Mgba<PERSON>", "entity.minecraft.silverfish": "<PERSON><PERSON><PERSON>", "entity.minecraft.skeleton": "Ọkpụkpụ", "entity.minecraft.skeleton_horse": "Snyịnya Ọkpụkpụ", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "Obere Ọkụ ọkụ", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Snow Golem", "entity.minecraft.snowball": "Snowball", "entity.minecraft.spawner_minecart": "<PERSON><PERSON><PERSON> with <PERSON> Spawner", "entity.minecraft.spectral_arrow": "Spectral Arrow", "entity.minecraft.spider": "<PERSON><PERSON><PERSON>", "entity.minecraft.splash_potion": "Splash Potion", "entity.minecraft.spruce_boat": "Spruce Boat", "entity.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "entity.minecraft.squid": "Skwid", "entity.minecraft.stray": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "Tadpole", "entity.minecraft.text_display": "Text Display", "entity.minecraft.tnt": "Eburu TNT", "entity.minecraft.tnt_minecart": "Minecart with TNT", "entity.minecraft.trader_llama": "<PERSON><PERSON> ah<PERSON>", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "Tropical Fish", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "<PERSON><PERSON> ojii", "entity.minecraft.tropical_fish.predefined.10": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.11": "<PERSON><PERSON>ch<PERSON> mma nru ububa", "entity.minecraft.tropical_fish.predefined.12": "Icheoku", "entity.minecraft.tropical_fish.predefined.13": "Nwanyị Ndị mmụọ", "entity.minecraft.tropical_fish.predefined.14": "<PERSON><PERSON> chichlid", "entity.minecraft.tropical_fish.predefined.15": "<PERSON><PERSON> Egbugbere ọ<PERSON>", "entity.minecraft.tropical_fish.predefined.16": "<PERSON><PERSON>a", "entity.minecraft.tropical_fish.predefined.17": "Threadfin", "entity.minecraft.tropical_fish.predefined.18": "<PERSON>ato <PERSON>", "entity.minecraft.tropical_fish.predefined.19": "<PERSON><PERSON><PERSON> na-a<PERSON>", "entity.minecraft.tropical_fish.predefined.2": "Bulu Tang", "entity.minecraft.tropical_fish.predefined.20": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.21": "Agba odo", "entity.minecraft.tropical_fish.predefined.3": "<PERSON><PERSON> ububa", "entity.minecraft.tropical_fish.predefined.4": "chichlid", "entity.minecraft.tropical_fish.predefined.5": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.6": "<PERSON><PERSON> s<PERSON>", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "<PERSON><PERSON> <PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.9": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>\n", "entity.minecraft.tropical_fish.type.clayfish": "ụr<PERSON>", "entity.minecraft.tropical_fish.type.dasher": "Kr", "entity.minecraft.tropical_fish.type.flopper": "Onye na-agba mmiri", "entity.minecraft.tropical_fish.type.glitter": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.spotty": "Ntụpọ", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "<PERSON><PERSON><PERSON><PERSON> anwụ", "entity.minecraft.turtle": "<PERSON><PERSON>", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "Villager", "entity.minecraft.villager.armorer": "Onyekwere", "entity.minecraft.villager.butcher": "<PERSON><PERSON> na-e<PERSON><PERSON>", "entity.minecraft.villager.cartographer": "Onye na-ese foto", "entity.minecraft.villager.cleric": "<PERSON><PERSON>", "entity.minecraft.villager.farmer": "<PERSON>ye oru ugbo", "entity.minecraft.villager.fisherman": "Onye <PERSON>", "entity.minecraft.villager.fletcher": "<PERSON>", "entity.minecraft.villager.leatherworker": "Onye na-ar<PERSON> a<PERSON>ụ", "entity.minecraft.villager.librarian": "Onye Ọbá akwụkwọ", "entity.minecraft.villager.mason": "<PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "Obodo ime obodo", "entity.minecraft.villager.shepherd": "<PERSON><PERSON> Ọzụzụ Atụrụ", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "<PERSON><PERSON><PERSON>", "entity.minecraft.vindicator": "<PERSON>ye mmegide", "entity.minecraft.wandering_trader": "<PERSON><PERSON> ah<PERSON>", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Wind Charge", "entity.minecraft.witch": "<PERSON><PERSON><PERSON>", "entity.minecraft.wither": "Akpọrọ", "entity.minecraft.wither_skeleton": "Ọkpụkpụ <PERSON>kp<PERSON>rọ", "entity.minecraft.wither_skull": "Okpokoro Isi", "entity.minecraft.wolf": "<PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "Omnyịnya Zombie", "entity.minecraft.zombie_villager": "Obodo Zombie", "entity.minecraft.zombified_piglin": "<PERSON><PERSON>", "entity.not_summonable": "Can't summon entity of type %s", "event.minecraft.raid": "Wakporo", "event.minecraft.raid.defeat": "Imeri", "event.minecraft.raid.defeat.full": "Raid - Defeat", "event.minecraft.raid.raiders_remaining": "Raiders fọdụrụ:%s", "event.minecraft.raid.victory": "Mmeri", "event.minecraft.raid.victory.full": "Raid - Victory", "filled_map.buried_treasure": "Ẹbụk <PERSON><PERSON><PERSON>", "filled_map.explorer_jungle": "Jungle Explorer Map", "filled_map.explorer_swamp": "Swamp Explorer Map", "filled_map.id": "Nka #%s", "filled_map.level": "(Ọkwa %s/%s)", "filled_map.locked": "Locked", "filled_map.mansion": "Maapụ Ọhịa Nyocha", "filled_map.monument": "<PERSON><PERSON><PERSON> nke <PERSON> O<PERSON>mir<PERSON>", "filled_map.scale": "Ebuli na 1. 1:%s", "filled_map.trial_chambers": "Trial Explorer Map", "filled_map.unknown": "<PERSON><PERSON><PERSON> a na-amagh<PERSON>", "filled_map.village_desert": "Desert Village Map", "filled_map.village_plains": "Plains Village Map", "filled_map.village_savanna": "Savanna Village Map", "filled_map.village_snowy": "Snowy Village Map", "filled_map.village_taiga": "Taiga Village Map", "flat_world_preset.minecraft.bottomless_pit": "<PERSON>'ala mmiri", "flat_world_preset.minecraft.classic_flat": "Classical Flat", "flat_world_preset.minecraft.desert": "Ọzara", "flat_world_preset.minecraft.overworld": "<PERSON><PERSON><PERSON><PERSON>", "flat_world_preset.minecraft.redstone_ready": "Redstone Jikere", "flat_world_preset.minecraft.snowy_kingdom": "Ala Iku Eku", "flat_world_preset.minecraft.the_void": "Ndị Void", "flat_world_preset.minecraft.tunnelers_dream": "Nrọ nke ndi Tunnelers", "flat_world_preset.minecraft.water_world": "<PERSON><PERSON>i <PERSON>", "flat_world_preset.unknown": "What???", "gameMode.adventure": "Ọnọdụ <PERSON><PERSON><PERSON>", "gameMode.changed": "Ewelitela <PERSON>n<PERSON><PERSON><PERSON> egwuregwu gị ka ọ ruo na %s", "gameMode.creative": "Ọnọdụ okike", "gameMode.hardcore": "Ọnọdụ Okponku!", "gameMode.spectator": "Ọnọdụ nlélé anya", "gameMode.survival": "Ọnọdụ N<PERSON>hu", "gamerule.allowFireTicksAwayFromPlayer": "Tick fire away from players", "gamerule.allowFireTicksAwayFromPlayer.description": "Controls whether or not fire and lava should be able to tick further than 8 chunks away from any player", "gamerule.announceAdvancements": "Gbasaa ozi a<PERSON>", "gamerule.blockExplosionDropDecay": "In block interaction explosions, some blocks won't drop their loot", "gamerule.blockExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by block interactions are lost in the explosion.", "gamerule.category.chat": "Nkata", "gamerule.category.drops": "<PERSON><PERSON><PERSON>", "gamerule.category.misc": "Miscellaneous", "gamerule.category.mobs": "<PERSON><PERSON>", "gamerule.category.player": "Player", "gamerule.category.spawning": "<PERSON><PERSON><PERSON><PERSON><PERSON>ar<PERSON>", "gamerule.category.updates": "<PERSON><PERSON><PERSON> uwa", "gamerule.commandBlockOutput": "<PERSON>wu mg<PERSON>a ozi na-egbochi mpụtara", "gamerule.commandModificationBlockLimit": "Command modification block limit", "gamerule.commandModificationBlockLimit.description": "Number of blocks that can be changed at once by one command, such as fill or clone.", "gamerule.disableElytraMovementCheck": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> nke ngaghari elytra", "gamerule.disablePlayerMovementCheck": "Disable player movement check", "gamerule.disableRaids": "Gbochie raids", "gamerule.doDaylightCycle": "<PERSON><PERSON> oge nke <PERSON>", "gamerule.doEntityDrops": "Togbo ngwa otu", "gamerule.doEntityDrops.description": "Njikwa na esi na minecart adap<PERSON> (nke ritere ihe mbata), okpokolo agba, ụgbọ, etc.", "gamerule.doFireTick": "<PERSON><PERSON>", "gamerule.doImmediateRespawn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.doInsomnia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> phantoms", "gamerule.doLimitedCrafting": "Rio ngwa nka", "gamerule.doLimitedCrafting.description": "If enabled, players will be able to craft only unlocked recipes.", "gamerule.doMobLoot": "Kwụs<PERSON> ohi oha", "gamerule.doMobLoot.description": "Controls resource drops from mobs, including experience orbs.", "gamerule.doMobSpawning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ihe oha", "gamerule.doMobSpawning.description": "Some entities might have separate rules.", "gamerule.doPatrolSpawning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ng<PERSON> ihe n<PERSON>ko", "gamerule.doTileDrops": "<PERSON><PERSON> ngongo", "gamerule.doTileDrops.description": "Controls resource drops from blocks, including experience orbs.", "gamerule.doTraderSpawning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ng<PERSON> nd<PERSON> ah<PERSON>a", "gamerule.doVinesSpread": "Vines spread", "gamerule.doVinesSpread.description": "Controls whether or not the Vines block spreads randomly to adjacent blocks. Does not affect other types of vine blocks such as Weeping Vines, Twisting Vines, etc.", "gamerule.doWardenSpawning": "Tụgharịa ndị nchekwa", "gamerule.doWeatherCycle": "Melite ekuku", "gamerule.drowningDamage": "<PERSON><PERSON> mmebi na-eri n<PERSON>", "gamerule.enderPearlsVanishOnDeath": "<PERSON><PERSON><PERSON> Ender <PERSON> vanish on death", "gamerule.enderPearlsVanishOnDeath.description": "Whether Ender <PERSON> thrown by a player vanish when that player dies.", "gamerule.entitiesWithPassengersCanUsePortals": "Entities with passengers can use portals", "gamerule.entitiesWithPassengersCanUsePortals.description": "Allow entities with passengers to teleport through Nether Portals, End Portals, and End Gateways.", "gamerule.fallDamage": "<PERSON><PERSON> mmebi odida n<PERSON>", "gamerule.fireDamage": "<PERSON><PERSON> mmebi <PERSON> nk<PERSON>", "gamerule.forgiveDeadPlayers": "Gbaghara ndị egwuregwu nw<PERSON><PERSON><PERSON> anwụ", "gamerule.forgiveDeadPlayers.description": "Ọha ndị iwe enweghị ngalaba ga-akwusi iwe iwe mgbe onye egwuregwu a na achọ achọ nwụrụ nso.", "gamerule.freezeDamage": "Kpebie mmebi n'efu", "gamerule.globalSoundEvents": "Global sound events", "gamerule.globalSoundEvents.description": "When certain game events happen, like a boss spawning, the sound is heard everywhere.", "gamerule.keepInventory": "Debe ngwa ahịa mgbe ọnwụ gachara", "gamerule.lavaSourceConversion": "Lava converts to source", "gamerule.lavaSourceConversion.description": "When flowing lava is surrounded on two sides by lava sources it converts into a source.", "gamerule.locatorBar": "Enable player <PERSON><PERSON><PERSON>", "gamerule.locatorBar.description": "When enabled, a bar is shown on the screen to indicate the direction of players.", "gamerule.logAdminCommands": "Gbasaa iwu nd<PERSON> n<PERSON>zi", "gamerule.maxCommandChainLength": "<PERSON>ye iwu maka njedebe ogologo nko", "gamerule.maxCommandChainLength.description": "Applies to command block chains and functions.", "gamerule.maxCommandForkCount": "Command context limit", "gamerule.maxCommandForkCount.description": "Maximum number of contexts that can be used by commands like 'execute as'.", "gamerule.maxEntityCramming": "<PERSON><PERSON><PERSON>ta <PERSON>", "gamerule.minecartMaxSpeed": "Minecart max speed", "gamerule.minecartMaxSpeed.description": "Maximum default speed of a moving Minecart on land.", "gamerule.mobExplosionDropDecay": "In mob explosions, some blocks won't drop their loot", "gamerule.mobExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by mobs are lost in the explosion.", "gamerule.mobGriefing": "Nye ikike mmebi ihe omume iwe", "gamerule.naturalRegeneration": "<PERSON><PERSON><PERSON><PERSON> ah<PERSON> ike", "gamerule.playersNetherPortalCreativeDelay": "Player's Nether portal delay in creative mode", "gamerule.playersNetherPortalCreativeDelay.description": "Time (in ticks) that a creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersNetherPortalDefaultDelay": "Player's Nether portal delay in non-creative mode", "gamerule.playersNetherPortalDefaultDelay.description": "Time (in ticks) that a non-creative mode player needs to stand in a Nether portal before changing dimensions.", "gamerule.playersSleepingPercentage": "Pasent ụra", "gamerule.playersSleepingPercentage.description": "Pasent nke ndị egwuregwu na-aghaghị <PERSON>ra iji maafe abal<PERSON>.", "gamerule.projectilesCanBreakBlocks": "Projectiles can break blocks", "gamerule.projectilesCanBreakBlocks.description": "Controls whether impact projectiles will destroy blocks that are destructible by them.", "gamerule.randomTickSpeed": "Ike ọsọ ákàrà ndebanye ike ọsọ", "gamerule.reducedDebugInfo": "Belata ozi mm<PERSON>", "gamerule.reducedDebugInfo.description": "Limits contents of debug screen.", "gamerule.sendCommandFeedback": "Ziga ozi nzichighata iwu", "gamerule.showDeathMessages": "<PERSON><PERSON> oz<PERSON>", "gamerule.snowAccumulationHeight": "Snow accumulation height", "gamerule.snowAccumulationHeight.description": "When it snows, layers of snow form on the ground up to at most this number of layers.", "gamerule.spawnChunkRadius": "Spawn chunk radius", "gamerule.spawnChunkRadius.description": "Amount of chunks that stay loaded around the overworld spawn position.", "gamerule.spawnRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gamerule.spawnRadius.description": "Controls the size of the area around the spawn point that players can spawn in.", "gamerule.spectatorsGenerateChunks": "<PERSON>we ka ndị nkiri mep<PERSON>ta ala", "gamerule.tntExplodes": "Allow TNT to be activated and to explode", "gamerule.tntExplosionDropDecay": "In TNT explosions, some blocks won't drop their loot", "gamerule.tntExplosionDropDecay.description": "Some of the drops from blocks destroyed by explosions caused by TNT are lost in the explosion.", "gamerule.universalAnger": "<PERSON><PERSON> g<PERSON>", "gamerule.universalAnger.description": "Ọha ndị iwe enweghị ngalaba na alụso onye egwuregwu ọbụla nọ nso, ọ<PERSON><PERSON><PERSON> naanị onye egwuregwu kpasuru ha iwe. Ọ ka-arụ ọrụ ma e gbochie ndị egwuregwu gbaghara ndị nwụrụ anwụ.", "gamerule.waterSourceConversion": "Water converts to source", "gamerule.waterSourceConversion.description": "When flowing water is surrounded on two sides by water sources it converts into a source.", "generator.custom": "Omenala", "generator.customized": "<PERSON><PERSON><PERSON>", "generator.minecraft.amplified": "EGO", "generator.minecraft.amplified.info": "Mara: <PERSON><PERSON>! Ch<PERSON>rọ kọmpụta amịrị amịrị.", "generator.minecraft.debug_all_block_states": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generator.minecraft.flat": "Superflat", "generator.minecraft.large_biomes": "Nnukwu Biomes", "generator.minecraft.normal": "Ndi an-kpọ", "generator.minecraft.single_biome_surface": "Single Biome", "generator.single_biome_caves": "Caves", "generator.single_biome_floating_islands": "Agwaetiti na-ese n’elu mmiri", "gui.abuseReport.attestation": "By submitting this report, you confirm that the information you have provided is accurate and complete to the best of your knowledge.", "gui.abuseReport.comments": "Comments", "gui.abuseReport.describe": "Sharing details will help us make a well-informed decision.", "gui.abuseReport.discard.content": "If you leave, you'll lose this report and your comments.\nAre you sure you want to leave?", "gui.abuseReport.discard.discard": "Leave and Discard Report", "gui.abuseReport.discard.draft": "Save as Draft", "gui.abuseReport.discard.return": "Continue Editing", "gui.abuseReport.discard.title": "Discard report and comments?", "gui.abuseReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.abuseReport.draft.discard": "Discard", "gui.abuseReport.draft.edit": "Continue Editing", "gui.abuseReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.abuseReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.abuseReport.draft.title": "Edit draft chat report?", "gui.abuseReport.error.title": "Nsogbu i<PERSON>pu akụkọ gị", "gui.abuseReport.message": "Where did you observe the bad behavior?\nThis will help us in researching your case.", "gui.abuseReport.more_comments": "Please describe what happened:", "gui.abuseReport.name.comment_box_label": "Please describe why you want to report this name:", "gui.abuseReport.name.reporting": "You are reporting \"%s\".", "gui.abuseReport.name.title": "Report Inappropriate Player Name", "gui.abuseReport.observed_what": "Why are you reporting this?", "gui.abuseReport.read_info": "Learn About Reporting", "gui.abuseReport.reason.alcohol_tobacco_drugs": "Ọgwụ ọjọọ ma ọ bụ mmanya na aba n'anya", "gui.abuseReport.reason.alcohol_tobacco_drugs.description": "<PERSON><PERSON><PERSON> na-agba ndị ọzọ ume ikere òkè n'ihe omume met<PERSON><PERSON> ọgwụ ọjọọ ma ọ bụ ịgba ume ịṅụbiga mmanya ókè.", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse": "<PERSON><PERSON><PERSON> a<PERSON> ma <PERSON> bụ mmegbu <PERSON>", "gui.abuseReport.reason.child_sexual_exploitation_or_abuse.description": "<PERSON><PERSON><PERSON> na-ekwu maka ma ọ bụ n'ụzọ ọzọ na-akwalite omume r<PERSON><PERSON><PERSON> arụ met<PERSON><PERSON>.", "gui.abuseReport.reason.defamation_impersonation_false_information": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ma <PERSON> bụ ozi asi", "gui.abuseReport.reason.defamation_impersonation_false_information.description": "<PERSON><PERSON><PERSON> na-a<PERSON><PERSON><PERSON> onye ọzọ, mee ka à ga-asị na ọ bụ onye ọ na-abụghị, ma <PERSON> bụ na-eker<PERSON>ta ozi <PERSON> n’ebumnobi nke irigbu ma ọ bụ duhie ndị ọzọ.", "gui.abuseReport.reason.description": "Nkọwa:", "gui.abuseReport.reason.false_reporting": "<PERSON><PERSON><PERSON>", "gui.abuseReport.reason.generic": "I want to report them", "gui.abuseReport.reason.generic.description": "I'm annoyed with them / they have done something I do not like.", "gui.abuseReport.reason.harassment_or_bullying": "<PERSON><PERSON><PERSON> ma <PERSON> bụ <PERSON>bu", "gui.abuseReport.reason.harassment_or_bullying.description": "<PERSON><PERSON><PERSON> na-emenye gị i<PERSON>, na-a<PERSON><PERSON><PERSON> ma ọ bụ na-akparị gị ma ọ bụ onye ọzọ. Nke a gụnyere mgbe mmadụ na-agbalị ugboro ugboro ịkpọtụrụ gị ma ọ bụ onye ọzọ na-enweghị nkwenye ma ọ bụ biputere ozi nkeonwe gbasara gị ma ọ bụ onye ọzọ na-enweghị nkwenye (\"doxing\").", "gui.abuseReport.reason.hate_speech": "<PERSON><PERSON> ị<PERSON>ọ<PERSON>ị", "gui.abuseReport.reason.hate_speech.description": "<PERSON><PERSON><PERSON> na-a<PERSON><PERSON><PERSON> gị ma ọ bụ onye ọkpụkpọ ọzọ dabere na njirimara njirimara ha, d<PERSON> ka ok<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON>, ma <PERSON> bụ mmek<PERSON>ah<PERSON>.", "gui.abuseReport.reason.imminent_harm": "Mmerụ dị nso - egwu imerụ ndị <PERSON>", "gui.abuseReport.reason.imminent_harm.description": "<PERSON><PERSON><PERSON> na-eyi gị egwu imerụ gị ma ọ bụ onye ọzọ na ndụ n'ezie.", "gui.abuseReport.reason.narration": "%s: %s", "gui.abuseReport.reason.non_consensual_intimate_imagery": "Onyonyo chiri anya na-enwegh<PERSON> nkwekọ<PERSON>ta", "gui.abuseReport.reason.non_consensual_intimate_imagery.description": "<PERSON><PERSON><PERSON> na-ekwu maka ya, na-<PERSON><PERSON><PERSON><PERSON> ma ọ bụ na-akwalite onyonyo nzuzo na nzuzo.", "gui.abuseReport.reason.self_harm_or_suicide": "<PERSON><PERSON><PERSON> dị nso - imerụ onwe onye ma ọ bụ igbu onwe ya", "gui.abuseReport.reason.self_harm_or_suicide.description": "<PERSON><PERSON><PERSON> na-eyi onwe ya egwu imerụ onwe ya na ndụ n'ezie ma ọ bụ na-ekwu maka imerụ onwe ya na ndụ n'ezie.", "gui.abuseReport.reason.sexually_inappropriate": "Sexually inappropriate", "gui.abuseReport.reason.sexually_inappropriate.description": "Skins that are graphic in nature relating to sexual acts, sexual organs, and sexual violence.", "gui.abuseReport.reason.terrorism_or_violent_extremism": "<PERSON><PERSON> <PERSON><PERSON> egwu ma ọ bụ extremism ime ihe ike", "gui.abuseReport.reason.terrorism_or_violent_extremism.description": "<PERSON><PERSON><PERSON> na-ekwu maka, na<PERSON><PERSON><PERSON><PERSON><PERSON>, ma <PERSON> bụ na-eyi egwu ime omume iyi ọha egwu ma ọ bụ ime ihe ike maka ndọrọ ndọrọ ọchịchị, ok<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ma <PERSON> bụ ihe ndị ọzọ.", "gui.abuseReport.reason.title": "Select Report Category", "gui.abuseReport.report_sent_msg": "We've successfully received your report. Thank you!\n\nOur team will review it as soon as possible.", "gui.abuseReport.select_reason": "Select Report Category", "gui.abuseReport.send": "Send Report", "gui.abuseReport.send.comment_too_long": "Please shorten the comment", "gui.abuseReport.send.error_message": "Eweghachiri mperi mgbe ị na-eziga akụkọ gị: '%s'", "gui.abuseReport.send.generic_error": "Enwetara mperi na-atụghị anya ya mgbe ị na-enye mkpesa gị.", "gui.abuseReport.send.http_error": "<PERSON><PERSON><PERSON><PERSON>TTP a na-atụghị anya ya mere mgbe ị na-eziga akụkọ gị.", "gui.abuseReport.send.json_error": "Zute oke akwụghị ụgwọ mgbe ị na-eziga akụkọ gị.", "gui.abuseReport.send.no_reason": "Please select a report category", "gui.abuseReport.send.not_attested": "Please read the text above and tick the checkbox to be able to send the report", "gui.abuseReport.send.service_unavailable": "Enweghị ike iru ọrụ mkpesa mmejọ. <PERSON><PERSON> hụ na ejikọrọ na ịntanetị wee nwaa ọzọ.", "gui.abuseReport.sending.title": "Na-<PERSON><PERSON>a <PERSON> g<PERSON>...", "gui.abuseReport.sent.title": "Zitere a<PERSON>ụ<PERSON>", "gui.abuseReport.skin.title": "Report Player Skin", "gui.abuseReport.title": "Report Player", "gui.abuseReport.type.chat": "Chat Messages", "gui.abuseReport.type.name": "Player Name", "gui.abuseReport.type.skin": "Player Skin", "gui.acknowledge": "<PERSON><PERSON>wu<PERSON><PERSON>", "gui.advancements": "Advancements", "gui.all": "<PERSON><PERSON>", "gui.back": "<PERSON><PERSON><PERSON>", "gui.banned.description": "%s\n%s\nMụtakwuo na njikọ a: %s", "gui.banned.description.permanent": "Amachibidoro akaụnt<PERSON> gị k<PERSON>m, nke pụtara na ị nweghị ike igwu egwu n'ịnta<PERSON>ị ma ọ bụ sonye na Realms.", "gui.banned.description.reason": "Anyị nwetara akụkọ ọjọọ n'oge na-adịbeghị anya site na akaụntụ gị. Ndị nhazi anyị enyochala ikpe gị wee chọpụta na ọ bụ %s, nke megidere Iwu Obodo Minecraft.", "gui.banned.description.reason_id": "Koodu: %s", "gui.banned.description.reason_id_message": "Koodu: %s-%s", "gui.banned.description.temporary": "%s <PERSON><PERSON>, ị ​​nweghị ike igwu egwu n'ịntanetị ma ọ bụ sonye na Realms.", "gui.banned.description.temporary.duration": "Akw<PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON> g<PERSON> nwa oge, a ga-emegharị ya ọzọ %s.", "gui.banned.description.unknownreason": "Anyị nwetara akụkọ na nso nso a maka omume ọjọọ site na akaụntụ gị. Ndị nhazi anyị enyochala ikpe gị ugbu a wee chọpụta na ọ megidere ụkpụ<PERSON><PERSON> obodo Minecraft.", "gui.banned.name.description": "Your current name - \"%s\" - violates our Community Standards. You can play singleplayer, but will need to change your name to play online.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.name.title": "Name Not Allowed in Multiplayer", "gui.banned.reason.defamation_impersonation_false_information": "Impersonation or sharing information to exploit or mislead others", "gui.banned.reason.drugs": "References to illegal drugs", "gui.banned.reason.extreme_violence_or_gore": "Depictions of real-life excessive violence or gore", "gui.banned.reason.false_reporting": "Excessive false or inaccurate reports", "gui.banned.reason.fraud": "Fraudulent acquisition or use of content", "gui.banned.reason.generic_violation": "Violating Community Standards", "gui.banned.reason.harassment_or_bullying": "Abusive language used in a directed, harmful manner", "gui.banned.reason.hate_speech": "Hate speech or discrimination", "gui.banned.reason.hate_terrorism_notorious_figure": "References to hate groups, terrorist organizations, or notorious figures", "gui.banned.reason.imminent_harm_to_person_or_property": "Intent to cause real-life harm to persons or property", "gui.banned.reason.nudity_or_pornography": "Displaying lewd or pornographic material", "gui.banned.reason.sexually_inappropriate": "Topics or content of a sexual nature", "gui.banned.reason.spam_or_advertising": "Spam or advertising", "gui.banned.skin.description": "Your current skin violates our Community Standards. You can still play with a default skin, or select a new one.\n\nLearn more or submit a case review at the following link: %s", "gui.banned.skin.title": "Skin Not Allowed", "gui.banned.title.permanent": "Amachibidoro akaụ<PERSON><PERSON> k<PERSON>m", "gui.banned.title.temporary": "Akwụ<PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON> nwa oge", "gui.cancel": "<PERSON><PERSON><PERSON><PERSON>", "gui.chatReport.comments": "Comments", "gui.chatReport.describe": "Ịkekọrịta nk<PERSON>wa ga-enyere <PERSON> aka ime mkpebi nke <PERSON>.", "gui.chatReport.discard.content": "Ọ bụrụ na ị pụọ, I ga at<PERSON>fu mkpesa a na nkwupụta gị.\nỊ ji n'aka na ị chọrọ ịpụ?", "gui.chatReport.discard.discard": "<PERSON><PERSON><PERSON> ma tụfuo mkpesa", "gui.chatReport.discard.draft": "Save as Draft", "gui.chatReport.discard.return": "Gaa n'ihu na-edezi", "gui.chatReport.discard.title": "<PERSON><PERSON><PERSON> na nkwu<PERSON>ụta?", "gui.chatReport.draft.content": "Would you like to continue editing the existing report or discard it and create a new one?", "gui.chatReport.draft.discard": "Discard", "gui.chatReport.draft.edit": "Continue Editing", "gui.chatReport.draft.quittotitle.content": "Would you like to continue editing it or discard it?", "gui.chatReport.draft.quittotitle.title": "You have a draft chat report that will be lost if you quit", "gui.chatReport.draft.title": "Edit draft chat report?", "gui.chatReport.more_comments": "<PERSON><PERSON> meren<PERSON>:", "gui.chatReport.observed_what": "Gịnị mere i ji na-ekpesa ihe a?", "gui.chatReport.read_info": "<PERSON>ụta maka mkpesa", "gui.chatReport.report_sent_msg": "Anyị enwetala mkpesa gị. Daalụ!\n\nNdị òtù anyị ga eleru ya anya ozigbo anyị nwere ike.", "gui.chatReport.select_chat": "<PERSON><PERSON><PERSON><PERSON> ozi nkata ị ga-akọ", "gui.chatReport.select_reason": "Họr<PERSON> udi mkpesa", "gui.chatReport.selected_chat": "%s (<PERSON><PERSON> nkata ah<PERSON> ka ọ kọọ)", "gui.chatReport.send": "<PERSON>ụta maka mkpesa", "gui.chatReport.send.comments_too_long": "<PERSON><PERSON> belata ok<PERSON> ah<PERSON>", "gui.chatReport.send.no_reason": "<PERSON><PERSON> o<PERSON> mk<PERSON>a", "gui.chatReport.send.no_reported_messages": "<PERSON><PERSON> h<PERSON> opekata mpe otu ozi nkata ị ga-ekpesa", "gui.chatReport.send.too_many_messages": "Na-<PERSON>wa itinye <PERSON> ozi n'ime mkpesa ahụ", "gui.chatReport.title": "<PERSON>ụta maka mkpesa", "gui.chatSelection.context": "A ga-etinye ozi gbasara nhọrọ a iji nyekwuo ọnọdụ", "gui.chatSelection.fold": "%s message(s) hidden", "gui.chatSelection.heading": "%s %s", "gui.chatSelection.join": "%s joined the chat", "gui.chatSelection.message.narrate": "%s kwuru: %s na %s", "gui.chatSelection.selected": "%s/%s ozi(s) họrọ", "gui.chatSelection.title": "Select Chat Messages to Report", "gui.continue": "Continue", "gui.copy_link_to_clipboard": "Copy Link to Clipboard", "gui.days": "%s ụbọchị", "gui.done": "Done", "gui.down": "Ala", "gui.entity_tooltip.type": "Pịnye:%s", "gui.experience.level": "%s", "gui.fileDropFailure.detail": "Rejected %s files", "gui.fileDropFailure.title": "Failed to add files", "gui.hours": "%s awa", "gui.loadingMinecraft": "Loading Minecraft", "gui.minutes": "%s nkeji", "gui.multiLineEditBox.character_limit": "%s/%s", "gui.narrate.button": "%s mpiaka", "gui.narrate.editBox": "%s igbe edezi %s", "gui.narrate.slider": "%s mmịfe", "gui.narrate.tab": "%s tab", "gui.no": "Mba", "gui.none": "Ọ nweghị", "gui.ok": "Ok", "gui.open_report_dir": "Open Report Directory", "gui.proceed": "Gaa n'iru", "gui.recipebook.moreRecipes": "<PERSON><PERSON><PERSON> aka nri maka <PERSON>", "gui.recipebook.page": "%s/%s", "gui.recipebook.search_hint": "Chọ<PERSON>...", "gui.recipebook.toggleRecipes.all": "Na-<PERSON><PERSON>", "gui.recipebook.toggleRecipes.blastable": "Na-ego<PERSON> mkpunwu", "gui.recipebook.toggleRecipes.craftable": "Na-egosi mmenwu nka", "gui.recipebook.toggleRecipes.smeltable": "<PERSON><PERSON><PERSON>", "gui.recipebook.toggleRecipes.smokable": "<PERSON>-<PERSON><PERSON> n<PERSON>wu", "gui.report_to_server": "Report To Server", "gui.socialInteractions.blocking_hint": "Jikwaa ya na aka<PERSON><PERSON>", "gui.socialInteractions.empty_blocked": "Enweghị ndị egwu egbochiri na nkata", "gui.socialInteractions.empty_hidden": "Ọ dịghị ndị egwuregwu zoro ezo na nkata", "gui.socialInteractions.hidden_in_chat": "A ga-ezochi ozi nkata si na %s", "gui.socialInteractions.hide": "Zoo na Nkata", "gui.socialInteractions.narration.hide": "Zoo ozi sitere na %s", "gui.socialInteractions.narration.report": "Kpesa onye egwuregwu %s", "gui.socialInteractions.narration.show": "Gosi ozi sitere na %s", "gui.socialInteractions.report": "Mkpesa", "gui.socialInteractions.search_empty": "<PERSON><PERSON><PERSON><PERSON> ike <PERSON> ndị egwuregwu <PERSON> nwere aha ahụ", "gui.socialInteractions.search_hint": "Search...", "gui.socialInteractions.server_label.multiple": "Onye egwuregwu%s-%s", "gui.socialInteractions.server_label.single": "Onye egwuregwu %s-%s", "gui.socialInteractions.show": "Gosi na Nkata", "gui.socialInteractions.shown_in_chat": "A ga-egosi ozi nkata si na%s", "gui.socialInteractions.status_blocked": "Blocked", "gui.socialInteractions.status_blocked_offline": "Egbochiri - anọghị n<PERSON>ị<PERSON>netị", "gui.socialInteractions.status_hidden": "Hidden", "gui.socialInteractions.status_hidden_offline": "Ezoro ezo - Na-anọghị n'ịntanetị", "gui.socialInteractions.status_offline": "Na-<PERSON><PERSON><PERSON><PERSON> n'<PERSON>", "gui.socialInteractions.tab_all": "All", "gui.socialInteractions.tab_blocked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gui.socialInteractions.tab_hidden": "<PERSON><PERSON>", "gui.socialInteractions.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "gui.socialInteractions.tooltip.hide": "Zoo ozi", "gui.socialInteractions.tooltip.report": "Kpesa onye egwuregwu", "gui.socialInteractions.tooltip.report.disabled": "Ọrụ mkpesa adịgh<PERSON>", "gui.socialInteractions.tooltip.report.no_messages": "Enweghị ozi enwere ike ịkọ site na onye egwuregwu%s", "gui.socialInteractions.tooltip.report.not_reportable": "Enweghị ike ikpesa onye egwuregwu a, n'ihi na enweghị ike ịch<PERSON>pụta ozi nkata ha na sava a", "gui.socialInteractions.tooltip.show": "<PERSON><PERSON> ozi", "gui.stats": "<PERSON><PERSON><PERSON>", "gui.toMenu": "Lachighaa na ndepụta server", "gui.toRealms": "Back to Realms List", "gui.toTitle": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>enyo aha", "gui.toWorld": "Back to World List", "gui.togglable_slot": "Click to disable slot", "gui.up": "<PERSON><PERSON>", "gui.waitingForResponse.button.inactive": "Back (%ss)", "gui.waitingForResponse.title": "Waiting for Server", "gui.yes": "Ee", "hanging_sign.edit": "Edit Hanging Sign Message", "instrument.minecraft.admire_goat_horn": "<PERSON>wee mmasị", "instrument.minecraft.call_goat_horn": "Kpọọ", "instrument.minecraft.dream_goat_horn": "Nrọ", "instrument.minecraft.feel_goat_horn": "Imetuta", "instrument.minecraft.ponder_goat_horn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instrument.minecraft.seek_goat_horn": "Chọọ", "instrument.minecraft.sing_goat_horn": "<PERSON><PERSON><PERSON>", "instrument.minecraft.yearn_goat_horn": "<PERSON><PERSON><PERSON><PERSON>", "inventory.binSlot": "<PERSON><PERSON> ihe", "inventory.hotbarInfo": "Chekwaa hotbar na %1$s+%2$s", "inventory.hotbarSaved": "Ihe echekwara hotbar (weghachite na %1$s +%2$s)", "item.canBreak": "Nwere <PERSON>:", "item.canPlace": "Enwere ike itinye ya na:", "item.canUse.unknown": "Unknown", "item.color": "Agba: %s", "item.components": "%s component(s)", "item.disabled": "Disabled item", "item.durability": "Anwụ ngwa ngwa: %s / %s", "item.dyed": "<PERSON><PERSON><PERSON>", "item.minecraft.acacia_boat": "Atgbọ mmiri Acacia", "item.minecraft.acacia_chest_boat": "Ugbọ mmiri acacia na obi", "item.minecraft.allay_spawn_egg": "Akwa na-akpọ Allay", "item.minecraft.amethyst_shard": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.angler_pottery_shard": "Angler Pottery Shard", "item.minecraft.angler_pottery_sherd": "Angler Pottery Sherd", "item.minecraft.apple": "a<PERSON><PERSON><PERSON>", "item.minecraft.archer_pottery_shard": "Archer Pottery Shard", "item.minecraft.archer_pottery_sherd": "Archer <PERSON>y Sherd", "item.minecraft.armadillo_scute": "<PERSON><PERSON><PERSON>", "item.minecraft.armadillo_spawn_egg": "Armadillo Spawn Egg", "item.minecraft.armor_stand": "Ekike agha Guz<PERSON>ie", "item.minecraft.arms_up_pottery_shard": "Arms Up Pottery Shard", "item.minecraft.arms_up_pottery_sherd": "Arms Up Pottery Sherd", "item.minecraft.arrow": "Ta", "item.minecraft.axolotl_bucket": "Bọketị nke <PERSON>", "item.minecraft.axolotl_spawn_egg": "Axolotl Spawn Egg", "item.minecraft.baked_potato": "<PERSON><PERSON><PERSON>", "item.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "item.minecraft.bamboo_raft": "Bamboo Raft", "item.minecraft.bat_spawn_egg": "Akwa na-akpọ bat", "item.minecraft.bee_spawn_egg": "Bee Spawn Egg", "item.minecraft.beef": "<PERSON><PERSON>", "item.minecraft.beetroot": "<PERSON><PERSON><PERSON>", "item.minecraft.beetroot_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.beetroot_soup": "<PERSON><PERSON><PERSON>", "item.minecraft.birch_boat": "Chgbọ mmiri <PERSON> ", "item.minecraft.birch_chest_boat": "Ugbọ mmiri birch na obi", "item.minecraft.black_bundle": "Black Bundle", "item.minecraft.black_dye": "Nwa Agba", "item.minecraft.black_harness": "<PERSON> Harness", "item.minecraft.blade_pottery_shard": "Blade Pottery Shard", "item.minecraft.blade_pottery_sherd": "Blade Pottery Sherd", "item.minecraft.blaze_powder": "Ọkụ ntụ ntụ", "item.minecraft.blaze_rod": "Ọkụ ọkụ", "item.minecraft.blaze_spawn_egg": "Akwa na-akpọ Blaze", "item.minecraft.blue_bundle": "Blue Bundle", "item.minecraft.blue_dye": "Bulu Agba", "item.minecraft.blue_egg": "Blue Egg", "item.minecraft.blue_harness": "<PERSON>", "item.minecraft.bogged_spawn_egg": "Bogged Spawn Egg", "item.minecraft.bolt_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.bolt_armor_trim_smithing_template.new": "Bolt Armor Trim", "item.minecraft.bone": "Ọkpụkpụ", "item.minecraft.bone_meal": "Ọkpụkpụ Nri", "item.minecraft.book": "Akwụkwọ", "item.minecraft.bordure_indented_banner_pattern": "Bordure Indented Banner Pattern", "item.minecraft.bow": "Ta", "item.minecraft.bowl": "Bowl", "item.minecraft.bread": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.breeze_rod": "<PERSON><PERSON>", "item.minecraft.breeze_spawn_egg": "Breeze Spawn Egg", "item.minecraft.brewer_pottery_shard": "Brewer Pottery Shard", "item.minecraft.brewer_pottery_sherd": "Brewer Pottery Sherd", "item.minecraft.brewing_stand": "Brewing Stand", "item.minecraft.brick": "Adobe", "item.minecraft.brown_bundle": "<PERSON> Bundle", "item.minecraft.brown_dye": "Agba aja Agba", "item.minecraft.brown_egg": "<PERSON>", "item.minecraft.brown_harness": "<PERSON>", "item.minecraft.brush": "Brush", "item.minecraft.bucket": "Ịwụ", "item.minecraft.bundle": "Ibu", "item.minecraft.bundle.empty": "Empty", "item.minecraft.bundle.empty.description": "Can hold a mixed stack of items", "item.minecraft.bundle.full": "Full", "item.minecraft.bundle.fullness": "%s/%s", "item.minecraft.burn_pottery_shard": "Burn Pottery Shard", "item.minecraft.burn_pottery_sherd": "Burn Pottery Sherd", "item.minecraft.camel_spawn_egg": "Camel Spawn Egg", "item.minecraft.carrot": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.carrot_on_a_stick": "Karọt na Mkpisi", "item.minecraft.cat_spawn_egg": "Nwambat ezigbo cakwa", "item.minecraft.cauldron": "<PERSON><PERSON><PERSON>", "item.minecraft.cave_spider_spawn_egg": "Akwa na-akpọ oside ududo", "item.minecraft.chainmail_boots": "Akpụkpọ ụkwụ Chain", "item.minecraft.chainmail_chestplate": "Mgbochi obi Chain", "item.minecraft.chainmail_helmet": "<PERSON><PERSON> nko ozi ikuku", "item.minecraft.chainmail_leggings": "Traụza Chain", "item.minecraft.charcoal": "<PERSON><PERSON>", "item.minecraft.cherry_boat": "Cherry Boat", "item.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "item.minecraft.chest_minecart": "Ngwongwo na Obi", "item.minecraft.chicken": "Ọkụkọ ọkụkọ", "item.minecraft.chicken_spawn_egg": "Akwa na-akpọ ọkpọ", "item.minecraft.chorus_fruit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.clay_ball": "Bọọ<PERSON>ụ Clay", "item.minecraft.clock": "<PERSON>ek<PERSON>", "item.minecraft.coal": "<PERSON><PERSON>", "item.minecraft.coast_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.coast_armor_trim_smithing_template.new": "Coast Armor Trim", "item.minecraft.cocoa_beans": "<PERSON><PERSON> a<PERSON>wa", "item.minecraft.cod": "Cod esibeghi esi", "item.minecraft.cod_bucket": "Bucket na-emetụ usoro ntanetị", "item.minecraft.cod_spawn_egg": "Akwa na-akpọ usoro ntanetị", "item.minecraft.command_block_minecart": "Ngwongwo na Iwu Gbochie", "item.minecraft.compass": "Kompas", "item.minecraft.cooked_beef": "<PERSON>be an<PERSON>", "item.minecraft.cooked_chicken": "<PERSON><PERSON> ya Ọkụkọ", "item.minecraft.cooked_cod": "Cod esiri esi", "item.minecraft.cooked_mutton": "Anụmanụ esiri esi", "item.minecraft.cooked_porkchop": "Ezi ụlọ esiri esi ", "item.minecraft.cooked_rabbit": "<PERSON><PERSON> o<PERSON>", "item.minecraft.cooked_salmon": "Salmọn esiri esi", "item.minecraft.cookie": "<PERSON><PERSON>", "item.minecraft.copper_ingot": "Ọla k<PERSON>pa Ingot", "item.minecraft.cow_spawn_egg": "Akwa na-akpọ Ehi", "item.minecraft.creaking_spawn_egg": "Creaking Spawn Egg", "item.minecraft.creeper_banner_pattern": "Nerkpụ<PERSON><PERSON>", "item.minecraft.creeper_banner_pattern.desc": "<PERSON><PERSON> na<PERSON>ak<PERSON><PERSON> ak<PERSON>", "item.minecraft.creeper_banner_pattern.new": "Creeper Charge Banner Pattern", "item.minecraft.creeper_spawn_egg": "Akwa na-akpọ Creeper", "item.minecraft.crossbow": "<PERSON><PERSON>", "item.minecraft.crossbow.projectile": "<PERSON>he oru ngo:", "item.minecraft.crossbow.projectile.multiple": "Projectile: %s x %s", "item.minecraft.crossbow.projectile.single": "Projectile: %s", "item.minecraft.cyan_bundle": "<PERSON><PERSON>", "item.minecraft.cyan_dye": "<PERSON><PERSON>", "item.minecraft.cyan_harness": "<PERSON><PERSON>", "item.minecraft.danger_pottery_shard": "Danger Pottery Shard", "item.minecraft.danger_pottery_sherd": "Danger Pottery Sherd", "item.minecraft.dark_oak_boat": "Ọchịchịrị akpu gbara <PERSON>ch<PERSON>ch<PERSON>rị", "item.minecraft.dark_oak_chest_boat": "Ọchịchịrị ugbọ mmiri oak na obi", "item.minecraft.debug_stick": "Osisi debug", "item.minecraft.debug_stick.empty": "%s enweghị ihe", "item.minecraft.debug_stick.select": "họrọ\"%s\" (%s)", "item.minecraft.debug_stick.update": "\"%s\" na %s", "item.minecraft.diamond": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_axe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_boots": "<PERSON><PERSON><PERSON><PERSON>k<PERSON>", "item.minecraft.diamond_chestplate": "Daimọn Chestplate", "item.minecraft.diamond_helmet": "<PERSON><PERSON>", "item.minecraft.diamond_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_horse_armor": "Ebube Anyị<PERSON>", "item.minecraft.diamond_leggings": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_pickaxe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_shovel": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.diamond_sword": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.disc_fragment_5": "<PERSON><PERSON><PERSON><PERSON> diski", "item.minecraft.disc_fragment_5.desc": "Egwu Diski - 5", "item.minecraft.dolphin_spawn_egg": "Akwa na-akpọ Dolphin", "item.minecraft.donkey_spawn_egg": "Akwa na-akpọ Inyịnya Ibu", "item.minecraft.dragon_breath": "<PERSON><PERSON>", "item.minecraft.dried_kelp": "<PERSON><PERSON><PERSON><PERSON> na-acha ah<PERSON> ndụ", "item.minecraft.drowned_spawn_egg": "Akwa na-akpọ Mmiri <PERSON>", "item.minecraft.dune_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.dune_armor_trim_smithing_template.new": "<PERSON>ne Armor <PERSON>", "item.minecraft.echo_shard": "Ikwughachi Shard", "item.minecraft.egg": "Akwa", "item.minecraft.elder_guardian_spawn_egg": "Akwa na-akpọ ndị Okenye Nche", "item.minecraft.elytra": "Elytra", "item.minecraft.emerald": "Emerald", "item.minecraft.enchanted_book": "Akwụkwọ enwasị", "item.minecraft.enchanted_golden_apple": "<PERSON><PERSON><PERSON><PERSON>wu", "item.minecraft.end_crystal": "Kwụsị Crystal", "item.minecraft.ender_dragon_spawn_egg": "Ender Dragon Spawn Egg", "item.minecraft.ender_eye": "<PERSON> n<PERSON>", "item.minecraft.ender_pearl": "Ender Ọla", "item.minecraft.enderman_spawn_egg": "Akwa na-akpọ Enderman", "item.minecraft.endermite_spawn_egg": "Akwa na-akpọ Endermite", "item.minecraft.evoker_spawn_egg": "Egg na-akpọ onye Na-akpọ Oku", "item.minecraft.experience_bottle": "Kalama ọ 'Na-ad<PERSON><PERSON> adọrọ", "item.minecraft.explorer_pottery_shard": "Explorer <PERSON><PERSON> Shard", "item.minecraft.explorer_pottery_sherd": "Explorer <PERSON><PERSON> She<PERSON>", "item.minecraft.eye_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.eye_armor_trim_smithing_template.new": "Eye Armor Trim", "item.minecraft.feather": "Nku", "item.minecraft.fermented_spider_eye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.field_masoned_banner_pattern": "Field Masoned Banner Pattern", "item.minecraft.filled_map": "<PERSON><PERSON><PERSON>", "item.minecraft.fire_charge": "Ọkụ ọkụ", "item.minecraft.firework_rocket": "Igwe <PERSON>", "item.minecraft.firework_rocket.flight": "<PERSON><PERSON>:", "item.minecraft.firework_rocket.multiple_stars": "%s x %s", "item.minecraft.firework_rocket.single_star": "%s", "item.minecraft.firework_star": "Igwe <PERSON>", "item.minecraft.firework_star.black": "Nwa", "item.minecraft.firework_star.blue": "Bulu", "item.minecraft.firework_star.brown": "Agba aja", "item.minecraft.firework_star.custom_color": "Custom", "item.minecraft.firework_star.cyan": "<PERSON><PERSON>", "item.minecraft.firework_star.fade_to": "<PERSON><PERSON> na-", "item.minecraft.firework_star.flicker": "Twinkle\n", "item.minecraft.firework_star.gray": "Agba ntụ", "item.minecraft.firework_star.green": "Agba akwụkwọ ndụ", "item.minecraft.firework_star.light_blue": "Ìhè Bulu", "item.minecraft.firework_star.light_gray": "Ìhè Agba", "item.minecraft.firework_star.lime": "Oroma <PERSON>", "item.minecraft.firework_star.magenta": "Ma<PERSON><PERSON>", "item.minecraft.firework_star.orange": "oroma", "item.minecraft.firework_star.pink": "Pink", "item.minecraft.firework_star.purple": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.red": "<PERSON><PERSON>", "item.minecraft.firework_star.shape": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.burst": "Gbawara", "item.minecraft.firework_star.shape.creeper": "<PERSON><PERSON> na-ak<PERSON><PERSON> akpụ-udi", "item.minecraft.firework_star.shape.large_ball": "Bọọlụ buru ibu", "item.minecraft.firework_star.shape.small_ball": "<PERSON><PERSON><PERSON>", "item.minecraft.firework_star.shape.star": "Ọdịdị kpakpando", "item.minecraft.firework_star.trail": "Nzọ ụkwụ", "item.minecraft.firework_star.white": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.firework_star.yellow": "Edo-edo", "item.minecraft.fishing_rod": "<PERSON><PERSON><PERSON>", "item.minecraft.flint": "<PERSON><PERSON><PERSON>", "item.minecraft.flint_and_steel": "Nkume na ígwè", "item.minecraft.flow_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.flow_armor_trim_smithing_template.new": "Flow Armor Trim", "item.minecraft.flow_banner_pattern": "<PERSON>", "item.minecraft.flow_banner_pattern.desc": "Flow", "item.minecraft.flow_banner_pattern.new": "<PERSON> <PERSON> Pattern", "item.minecraft.flow_pottery_sherd": "Flow Pottery Sherd", "item.minecraft.flower_banner_pattern": "<PERSON>", "item.minecraft.flower_banner_pattern.desc": "Ifuru n'Aka", "item.minecraft.flower_banner_pattern.new": "Flower Charge Banner Pattern", "item.minecraft.flower_pot": "Flower Pot", "item.minecraft.fox_spawn_egg": "<PERSON><PERSON><PERSON><PERSON> spawn akwa", "item.minecraft.friend_pottery_shard": "<PERSON> <PERSON><PERSON>", "item.minecraft.friend_pottery_sherd": "<PERSON> <PERSON>", "item.minecraft.frog_spawn_egg": "Akwa na-akpọ Ele", "item.minecraft.furnace_minecart": "Ngwongwo na Ọkụ", "item.minecraft.ghast_spawn_egg": "Akwa na-akpọ Ghast", "item.minecraft.ghast_tear": "<PERSON><PERSON><PERSON>", "item.minecraft.glass_bottle": "<PERSON><PERSON>", "item.minecraft.glistering_melon_slice": "Iberi Egusi na-egbuke egbuke", "item.minecraft.globe_banner_pattern": "<PERSON>", "item.minecraft.globe_banner_pattern.desc": "Ụwa", "item.minecraft.globe_banner_pattern.new": "Globe Banner Pattern", "item.minecraft.glow_berries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.glow_ink_sac": "Akpa Ink Glow", "item.minecraft.glow_item_frame": "Okpokoro Nkebi Dị <PERSON>", "item.minecraft.glow_squid_spawn_egg": "Obere squid kpọọ akwa", "item.minecraft.glowstone_dust": "<PERSON><PERSON><PERSON> ustj<PERSON>", "item.minecraft.goat_horn": "<PERSON><PERSON>", "item.minecraft.goat_spawn_egg": "Ewu Spawn Egg", "item.minecraft.gold_ingot": "Gold ọla", "item.minecraft.gold_nugget": "<PERSON><PERSON>", "item.minecraft.golden_apple": "Ap<PERSON>l <PERSON> edo ", "item.minecraft.golden_axe": "Axe ọla edo", "item.minecraft.golden_boots": "Akpụkpọ ụkwụ ọla edo", "item.minecraft.golden_carrot": "Kar<PERSON>t Ọlaedo", "item.minecraft.golden_chestplate": "<PERSON><PERSON> nchekwa ọla edo ", "item.minecraft.golden_helmet": "Okpu-a<PERSON><PERSON> edo", "item.minecraft.golden_hoe": "Ọha ọ<PERSON>edo", "item.minecraft.golden_horse_armor": "<PERSON><PERSON> a<PERSON>", "item.minecraft.golden_leggings": "<PERSON><PERSON> edo ", "item.minecraft.golden_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.golden_shovel": "<PERSON><PERSON><PERSON><PERSON> edo", "item.minecraft.golden_sword": "M<PERSON> agha <PERSON> edo", "item.minecraft.gray_bundle": "<PERSON>", "item.minecraft.gray_dye": "Agba ntụ", "item.minecraft.gray_harness": "<PERSON>", "item.minecraft.green_bundle": "Green Bundle", "item.minecraft.green_dye": "Agba akwụkwọ ndụ Agba", "item.minecraft.green_harness": "<PERSON>ss", "item.minecraft.guardian_spawn_egg": "Akwa na-akpọ Onye Nche", "item.minecraft.gunpowder": "Egbe", "item.minecraft.guster_banner_pattern": "<PERSON>", "item.minecraft.guster_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.guster_banner_pattern.new": "<PERSON><PERSON>", "item.minecraft.guster_pottery_sherd": "<PERSON><PERSON>y <PERSON>", "item.minecraft.happy_ghast_spawn_egg": "Happy Ghast Spawn Egg", "item.minecraft.harness": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_of_the_sea": "<PERSON><PERSON><PERSON>", "item.minecraft.heart_pottery_shard": "Heart Pottery Shard", "item.minecraft.heart_pottery_sherd": "Heart Pottery Sherd", "item.minecraft.heartbreak_pottery_shard": "Heartbreak Pottery Shard", "item.minecraft.heartbreak_pottery_sherd": "Heartbreak Pottery Sherd", "item.minecraft.hoglin_spawn_egg": "Hoglin Spawn Egg", "item.minecraft.honey_bottle": "<PERSON><PERSON>a mman<PERSON> a .<PERSON>", "item.minecraft.honeycomb": "<PERSON><PERSON><PERSON> a .<PERSON>", "item.minecraft.hopper_minecart": "Ngwong<PERSON> na <PERSON>", "item.minecraft.horse_spawn_egg": "Akwa na-akpọ Ịnyịnya", "item.minecraft.host_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.host_armor_trim_smithing_template.new": "Host <PERSON><PERSON>", "item.minecraft.howl_pottery_shard": "Howl Pottery Shard", "item.minecraft.howl_pottery_sherd": "Howl Pottery Sherd", "item.minecraft.husk_spawn_egg": "Akwa na-akpọ Husk", "item.minecraft.ink_sac": "Ink Nwa", "item.minecraft.iron_axe": "Axgwè Axe", "item.minecraft.iron_boots": "Bogwè a<PERSON>ụkpọ ụkwụ", "item.minecraft.iron_chestplate": "<PERSON><PERSON> mk<PERSON>chi <PERSON>", "item.minecraft.iron_golem_spawn_egg": "Iron Golem Spawn Egg", "item.minecraft.iron_helmet": "<PERSON><PERSON><PERSON>we", "item.minecraft.iron_hoe": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_horse_armor": "Horsnyịnya Ebube", "item.minecraft.iron_ingot": "Inggwè Ingot", "item.minecraft.iron_leggings": "Leggba Irongwè", "item.minecraft.iron_nugget": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.iron_pickaxe": "Pgwè Pickaxe", "item.minecraft.iron_shovel": "<PERSON><PERSON><PERSON><PERSON> sh<PERSON>", "item.minecraft.iron_sword": "<PERSON><PERSON><PERSON> <PERSON> a<PERSON>a", "item.minecraft.item_frame": "Nkebi Ụba", "item.minecraft.jungle_boat": "Unggbọ mmiri nke Ọhịa", "item.minecraft.jungle_chest_boat": "Ụgbọ mmiri ọh<PERSON>a nwere obi", "item.minecraft.knowledge_book": "Akwụkwọ Ihe Ọmụma", "item.minecraft.lapis_lazuli": "<PERSON><PERSON>", "item.minecraft.lava_bucket": "<PERSON><PERSON>", "item.minecraft.lead": "<PERSON><PERSON>", "item.minecraft.leather": "Akpụkp<PERSON>ụ", "item.minecraft.leather_boots": "Akpụkpọ anụ akp<PERSON>kpọ ụkwụ", "item.minecraft.leather_chestplate": "Akpụkpọ anụ <PERSON>", "item.minecraft.leather_helmet": "Akpụkpọ anụ okpu", "item.minecraft.leather_horse_armor": "Akpụkpọ anụ ịnyịnya Ekike agha", "item.minecraft.leather_leggings": "Akpụkpọ anụ akp<PERSON>kpọ ụkwụ", "item.minecraft.light_blue_bundle": "Light Blue Bundle", "item.minecraft.light_blue_dye": "Ìhè Bulu Dai", "item.minecraft.light_blue_harness": "Light Blue Harness", "item.minecraft.light_gray_bundle": "Light Gray Bundle", "item.minecraft.light_gray_dye": "Ìhè Agba Agba", "item.minecraft.light_gray_harness": "<PERSON> <PERSON>", "item.minecraft.lime_bundle": "Lime Bundle", "item.minecraft.lime_dye": "<PERSON><PERSON> dai", "item.minecraft.lime_harness": "<PERSON><PERSON>", "item.minecraft.lingering_potion": "<PERSON>-egbu oge", "item.minecraft.lingering_potion.effect.awkward": "Nsogbu <PERSON><PERSON>", "item.minecraft.lingering_potion.effect.empty": "<PERSON>-egbu oge", "item.minecraft.lingering_potion.effect.fire_resistance": "Mgbatị Na-egbu <PERSON> Ọkụ", "item.minecraft.lingering_potion.effect.harming": "Ogologo Oge Na<PERSON>i", "item.minecraft.lingering_potion.effect.healing": "<PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.lingering_potion.effect.infested": "Lingering Potion of Infestation", "item.minecraft.lingering_potion.effect.invisibility": "<PERSON><PERSON> n<PERSON>", "item.minecraft.lingering_potion.effect.leaping": "<PERSON><PERSON><PERSON> oge nke <PERSON>", "item.minecraft.lingering_potion.effect.levitation": "Na<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.luck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.mundane": "Ebee Na-egbu oge Mgba", "item.minecraft.lingering_potion.effect.night_vision": "Oge Na-egbu Oge nke Ọhụụ Night", "item.minecraft.lingering_potion.effect.oozing": "Lingering Potion of Oozing", "item.minecraft.lingering_potion.effect.poison": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.regeneration": "Oge Na-egbu Oge", "item.minecraft.lingering_potion.effect.slow_falling": "Oge Na-egbu Oge nke Sda Ngwa", "item.minecraft.lingering_potion.effect.slowness": "Oge Na-egbu Oge", "item.minecraft.lingering_potion.effect.strength": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.swiftness": "Oge Na-egbu Oge", "item.minecraft.lingering_potion.effect.thick": "<PERSON><PERSON>", "item.minecraft.lingering_potion.effect.turtle_master": "Oge Na-egbu Oge nke Nna-ukwu Agwa", "item.minecraft.lingering_potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.lingering_potion.effect.water_breathing": "Ogologo oge nke mmiri na-eku ume", "item.minecraft.lingering_potion.effect.weakness": "Na-egbu <PERSON>ge", "item.minecraft.lingering_potion.effect.weaving": "Lingering Potion of Weaving", "item.minecraft.lingering_potion.effect.wind_charged": "Lingering Potion of Wind Charging", "item.minecraft.llama_spawn_egg": "Ntụgharị akwa lIama", "item.minecraft.lodestone_compass": "Lodestone kompas", "item.minecraft.mace": "Mace", "item.minecraft.magenta_bundle": "Magenta Bundle", "item.minecraft.magenta_dye": "Magenta dai", "item.minecraft.magenta_harness": "<PERSON><PERSON><PERSON>", "item.minecraft.magma_cream": "Ude Magma", "item.minecraft.magma_cube_spawn_egg": "Egg na-akpọ Magma Cube", "item.minecraft.mangrove_boat": "Ụgbọ mmiri Mangrove", "item.minecraft.mangrove_chest_boat": "Ụgbọ mmiri Mangrove nwere obi", "item.minecraft.map": "Map efu", "item.minecraft.melon_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.melon_slice": "<PERSON><PERSON><PERSON> e<PERSON>i", "item.minecraft.milk_bucket": "Bụkeeti mmiri ara ehi", "item.minecraft.minecart": "<PERSON><PERSON><PERSON><PERSON>", "item.minecraft.miner_pottery_shard": "Miner <PERSON><PERSON>", "item.minecraft.miner_pottery_sherd": "Miner <PERSON>", "item.minecraft.mojang_banner_pattern": "<PERSON>", "item.minecraft.mojang_banner_pattern.desc": "<PERSON>he", "item.minecraft.mojang_banner_pattern.new": "<PERSON> Banner <PERSON>", "item.minecraft.mooshroom_spawn_egg": "Akwa na-akpọ Toadstool", "item.minecraft.mourner_pottery_shard": "Mourner Pottery Shard", "item.minecraft.mourner_pottery_sherd": "Mourner Pottery Sherd", "item.minecraft.mule_spawn_egg": "Akwa na-akpọ Inyinya Mule", "item.minecraft.mushroom_stew": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_11": "Music Disc", "item.minecraft.music_disc_11.desc": "C418 --11", "item.minecraft.music_disc_13": "<PERSON><PERSON><PERSON>", "item.minecraft.music_disc_13.desc": "C418 - 13", "item.minecraft.music_disc_5": "Music Disc", "item.minecraft.music_disc_5.desc": "<PERSON> - 5", "item.minecraft.music_disc_blocks": "Music Disc", "item.minecraft.music_disc_blocks.desc": "C418 - blocks", "item.minecraft.music_disc_cat": "Music Disc", "item.minecraft.music_disc_cat.desc": "C418 - cat", "item.minecraft.music_disc_chirp": "Music Disc", "item.minecraft.music_disc_chirp.desc": "C418 - chirp", "item.minecraft.music_disc_creator": "Music Disc", "item.minecraft.music_disc_creator.desc": "<PERSON> - <PERSON><PERSON>", "item.minecraft.music_disc_creator_music_box": "Music Disc", "item.minecraft.music_disc_creator_music_box.desc": "<PERSON> - <PERSON><PERSON> (Music Box)", "item.minecraft.music_disc_far": "Music Disc", "item.minecraft.music_disc_far.desc": "C418 - far", "item.minecraft.music_disc_lava_chicken": "Music Disc", "item.minecraft.music_disc_lava_chicken.desc": "Hyper Potions - Lava Chicken", "item.minecraft.music_disc_mall": "Music Disc", "item.minecraft.music_disc_mall.desc": "C418 - mall", "item.minecraft.music_disc_mellohi": "Music Disc", "item.minecraft.music_disc_mellohi.desc": "C418 -- me<PERSON><PERSON>", "item.minecraft.music_disc_otherside": "Music Disc", "item.minecraft.music_disc_otherside.desc": "<PERSON> - <PERSON>ide", "item.minecraft.music_disc_pigstep": "Music Disc", "item.minecraft.music_disc_pigstep.desc": "<PERSON> - Pigstep", "item.minecraft.music_disc_precipice": "Music Disc", "item.minecraft.music_disc_precipice.desc": "<PERSON> - Precipice", "item.minecraft.music_disc_relic": "Music Disc", "item.minecraft.music_disc_relic.desc": "<PERSON>", "item.minecraft.music_disc_stal": "Music Disc", "item.minecraft.music_disc_stal.desc": "C418 - sto<PERSON>", "item.minecraft.music_disc_strad": "Music Disc", "item.minecraft.music_disc_strad.desc": "C418 - strad", "item.minecraft.music_disc_tears": "Music Disc", "item.minecraft.music_disc_tears.desc": "<PERSON> - Tears", "item.minecraft.music_disc_wait": "Music Disc", "item.minecraft.music_disc_wait.desc": "C418 - wait", "item.minecraft.music_disc_ward": "Music Disc", "item.minecraft.music_disc_ward.desc": "C418 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.mutton": "<PERSON>", "item.minecraft.name_tag": "<PERSON><PERSON>", "item.minecraft.nautilus_shell": "Nauti<PERSON> shei", "item.minecraft.nether_brick": "Adobe brik", "item.minecraft.nether_star": "<PERSON><PERSON>", "item.minecraft.nether_wart": "Nether Wart", "item.minecraft.netherite_axe": "Axiteite Netherite", "item.minecraft.netherite_boots": "Boolu Netherite", "item.minecraft.netherite_chestplate": "Chestplate Netherite", "item.minecraft.netherite_helmet": "Helite Netherite", "item.minecraft.netherite_hoe": "Onye Ho<PERSON>", "item.minecraft.netherite_ingot": "Netherite Ingot", "item.minecraft.netherite_leggings": "Legitings Netherite", "item.minecraft.netherite_pickaxe": "Pickaxe nke Netherland", "item.minecraft.netherite_scrap": "Scan Netherite", "item.minecraft.netherite_shovel": "Shoezaiteite", "item.minecraft.netherite_sword": "<PERSON><PERSON>", "item.minecraft.netherite_upgrade_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.netherite_upgrade_smithing_template.new": "Netherite Upgrade", "item.minecraft.oak_boat": "Oak ụgbọ mmiri", "item.minecraft.oak_chest_boat": "Ugbọ mmiri oak na obi", "item.minecraft.ocelot_spawn_egg": "Akwa na-akpọ Ocelot", "item.minecraft.ominous_bottle": "Ominous <PERSON>", "item.minecraft.ominous_trial_key": "Ominous Trial Key", "item.minecraft.orange_bundle": "Orange Bundle", "item.minecraft.orange_dye": "Oroma dai", "item.minecraft.orange_harness": "Orange Harness", "item.minecraft.painting": "<PERSON><PERSON> o<PERSON>e", "item.minecraft.pale_oak_boat": "Pale Oak Boat", "item.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "item.minecraft.panda_spawn_egg": "Panda spawn akwa", "item.minecraft.paper": "Akwụkwọ", "item.minecraft.parrot_spawn_egg": "Akwa na-akpọ Okooko", "item.minecraft.phantom_membrane": "Phantom akpụkpọ", "item.minecraft.phantom_spawn_egg": "Akwa na-akpọ Uda", "item.minecraft.pig_spawn_egg": "Akwa na-akpọ Ezi", "item.minecraft.piglin_banner_pattern": "<PERSON>", "item.minecraft.piglin_banner_pattern.desc": "<PERSON><PERSON>", "item.minecraft.piglin_banner_pattern.new": "Snout Banner Pattern", "item.minecraft.piglin_brute_spawn_egg": "Piglin Brute spawn akwa", "item.minecraft.piglin_spawn_egg": "Piglin Spawn Egg", "item.minecraft.pillager_spawn_egg": "Akwa akwa", "item.minecraft.pink_bundle": "Pink Bundle", "item.minecraft.pink_dye": "Pink agba", "item.minecraft.pink_harness": "<PERSON>", "item.minecraft.pitcher_plant": "Pitcher Plant", "item.minecraft.pitcher_pod": "Pitcher Pod", "item.minecraft.plenty_pottery_shard": "Plenty Pottery Shard", "item.minecraft.plenty_pottery_sherd": "Plenty Pottery Sherd", "item.minecraft.poisonous_potato": "Nsi na-egbu egbu", "item.minecraft.polar_bear_spawn_egg": "Akwa nke na-akpọ Okpọ Pola", "item.minecraft.popped_chorus_fruit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.porkchop": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "item.minecraft.potato": "<PERSON><PERSON><PERSON>", "item.minecraft.potion": "<PERSON><PERSON>", "item.minecraft.potion.effect.awkward": "Nsogbu <PERSON>", "item.minecraft.potion.effect.empty": "Ugbo ala", "item.minecraft.potion.effect.fire_resistance": "Mgba nke <PERSON> Ọkụ", "item.minecraft.potion.effect.harming": "Mgba nke <PERSON>", "item.minecraft.potion.effect.healing": "<PERSON><PERSON>", "item.minecraft.potion.effect.infested": "Potion of Infestation", "item.minecraft.potion.effect.invisibility": "<PERSON><PERSON> n<PERSON>", "item.minecraft.potion.effect.leaping": "<PERSON><PERSON>g<PERSON> ọsọ", "item.minecraft.potion.effect.levitation": "<PERSON><PERSON> nke Itationgba mbọ", "item.minecraft.potion.effect.luck": "Ndepụta n<PERSON>", "item.minecraft.potion.effect.mundane": "<PERSON><PERSON>", "item.minecraft.potion.effect.night_vision": "<PERSON><PERSON> a<PERSON>", "item.minecraft.potion.effect.oozing": "Potion of Oozing", "item.minecraft.potion.effect.poison": "Ndepụta nke nsi", "item.minecraft.potion.effect.regeneration": "Ugba nke imu", "item.minecraft.potion.effect.slow_falling": "Ulo nke nwayọ nwayọ", "item.minecraft.potion.effect.slowness": "<PERSON><PERSON>", "item.minecraft.potion.effect.strength": "Ikike nke Ike", "item.minecraft.potion.effect.swiftness": "<PERSON><PERSON><PERSON> nke Ọsọ", "item.minecraft.potion.effect.thick": "<PERSON><PERSON> o<PERSON>i", "item.minecraft.potion.effect.turtle_master": "<PERSON><PERSON> nke <PERSON>-ukwu mbe", "item.minecraft.potion.effect.water": "<PERSON><PERSON><PERSON>", "item.minecraft.potion.effect.water_breathing": "Mpekere nke mmiri na-eku ume", "item.minecraft.potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON>ke", "item.minecraft.potion.effect.weaving": "Potion of Weaving", "item.minecraft.potion.effect.wind_charged": "Potion of Wind Charging", "item.minecraft.pottery_shard_archer": "Archer Pottery Shard", "item.minecraft.pottery_shard_arms_up": "Arms Up Pottery Shard", "item.minecraft.pottery_shard_prize": "Prize <PERSON><PERSON> Shard", "item.minecraft.pottery_shard_skull": "Skull Pottery Shard", "item.minecraft.powder_snow_bucket": "Ịwụ Snow ịwụ", "item.minecraft.prismarine_crystals": "<PERSON><PERSON>", "item.minecraft.prismarine_shard": "<PERSON><PERSON><PERSON><PERSON><PERSON> Prismarine", "item.minecraft.prize_pottery_shard": "Prize <PERSON><PERSON> Shard", "item.minecraft.prize_pottery_sherd": "Prize <PERSON><PERSON> Sherd", "item.minecraft.pufferfish": "Pufferfish", "item.minecraft.pufferfish_bucket": "Bọket nke Pufferfish", "item.minecraft.pufferfish_spawn_egg": "Akwa na-akpọ Onye Nwere Nsogbu", "item.minecraft.pumpkin_pie": "<PERSON><PERSON>", "item.minecraft.pumpkin_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.purple_bundle": "Purple Bundle", "item.minecraft.purple_dye": "<PERSON><PERSON><PERSON><PERSON><PERSON> dai", "item.minecraft.purple_harness": "<PERSON>", "item.minecraft.quartz": "<PERSON><PERSON>", "item.minecraft.rabbit": "<PERSON><PERSON> o<PERSON>", "item.minecraft.rabbit_foot": "<PERSON><PERSON><PERSON> kwụ", "item.minecraft.rabbit_hide": "<PERSON>e oyibo zoro", "item.minecraft.rabbit_spawn_egg": "Akwa na-akpọ Oke Bekee", "item.minecraft.rabbit_stew": "<PERSON><PERSON> o<PERSON>", "item.minecraft.raiser_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.raiser_armor_trim_smithing_template.new": "Raiser Armor Trim", "item.minecraft.ravager_spawn_egg": "Akwa na agba egbe", "item.minecraft.raw_copper": "Ọla kọpa", "item.minecraft.raw_gold": "Ọla edo", "item.minecraft.raw_iron": "Iron dị <PERSON>cha", "item.minecraft.recovery_compass": "Kompas Mgbake", "item.minecraft.red_bundle": "Red Bundle", "item.minecraft.red_dye": "<PERSON>ie dai", "item.minecraft.red_harness": "<PERSON>", "item.minecraft.redstone": "<PERSON><PERSON><PERSON> ust<PERSON> ", "item.minecraft.resin_brick": "Resin Brick", "item.minecraft.resin_clump": "<PERSON><PERSON>", "item.minecraft.rib_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.rib_armor_trim_smithing_template.new": "<PERSON><PERSON>", "item.minecraft.rotten_flesh": "<PERSON><PERSON> rere ere", "item.minecraft.saddle": "<PERSON><PERSON>", "item.minecraft.salmon": "Salmọn Raw", "item.minecraft.salmon_bucket": "Bucket na-emet<PERSON>", "item.minecraft.salmon_spawn_egg": "Akwa na-akpọ Maka Salmon", "item.minecraft.scrape_pottery_sherd": "Scrape Pottery Sherd", "item.minecraft.scute": "<PERSON><PERSON>", "item.minecraft.sentry_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.sentry_armor_trim_smithing_template.new": "Sentry Armor Trim", "item.minecraft.shaper_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.shaper_armor_trim_smithing_template.new": "<PERSON><PERSON><PERSON> Armor <PERSON>", "item.minecraft.sheaf_pottery_shard": "<PERSON><PERSON>", "item.minecraft.sheaf_pottery_sherd": "<PERSON><PERSON>", "item.minecraft.shears": "<PERSON><PERSON>", "item.minecraft.sheep_spawn_egg": "Akwa na-akpọ Atụrụ", "item.minecraft.shelter_pottery_shard": "Shelter Pottery Shard", "item.minecraft.shelter_pottery_sherd": "<PERSON>lter Pottery Sherd", "item.minecraft.shield": "Ọta", "item.minecraft.shield.black": "Ọta ojii", "item.minecraft.shield.blue": "Bulu Ọta", "item.minecraft.shield.brown": "Ọta Agba aja", "item.minecraft.shield.cyan": "Ọta <PERSON><PERSON>", "item.minecraft.shield.gray": "Ọta Agba ntụ", "item.minecraft.shield.green": "Ọta Agba akwụkwọ ndụ", "item.minecraft.shield.light_blue": "Ìhè Bulu Ọta", "item.minecraft.shield.light_gray": "Ọkụ Agba ntụ Ọta", "item.minecraft.shield.lime": "<PERSON><PERSON> Ọta", "item.minecraft.shield.magenta": "Ọta Magenta", "item.minecraft.shield.orange": "Ọta oroma", "item.minecraft.shield.pink": "Pink Ọta", "item.minecraft.shield.purple": "Ọta <PERSON><PERSON><PERSON>l", "item.minecraft.shield.red": "<PERSON><PERSON> Ọta", "item.minecraft.shield.white": "Ọta ọcha", "item.minecraft.shield.yellow": "Ọta Edo-edo", "item.minecraft.shulker_shell": "<PERSON><PERSON><PERSON><PERSON> shei", "item.minecraft.shulker_spawn_egg": "Akwa na-<PERSON><PERSON><PERSON><PERSON> Shulker", "item.minecraft.sign": "Sign", "item.minecraft.silence_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.silence_armor_trim_smithing_template.new": "Silence Armor Trim", "item.minecraft.silverfish_spawn_egg": "Akwa na-akpọ A<PERSON>ụ <PERSON>", "item.minecraft.skeleton_horse_spawn_egg": "Akwa na-akpọ Okpụkpụ Ịnyịnya", "item.minecraft.skeleton_spawn_egg": "<PERSON><PERSON>ị na-ak<PERSON> Okpụ<PERSON>pụ", "item.minecraft.skull_banner_pattern": "<PERSON>", "item.minecraft.skull_banner_pattern.desc": "Ebube okpokoro isi", "item.minecraft.skull_banner_pattern.new": "Skull Charge Banner Pattern", "item.minecraft.skull_pottery_shard": "Skull Pottery Shard", "item.minecraft.skull_pottery_sherd": "Skull Pottery Sherd", "item.minecraft.slime_ball": "<PERSON><PERSON><PERSON> e<PERSON>", "item.minecraft.slime_spawn_egg": "Akwa na-akpọ Sludge", "item.minecraft.smithing_template": "<PERSON><PERSON> Template", "item.minecraft.smithing_template.applies_to": "Applies to:", "item.minecraft.smithing_template.armor_trim.additions_slot_description": "Add ingot or crystal", "item.minecraft.smithing_template.armor_trim.applies_to": "Armor", "item.minecraft.smithing_template.armor_trim.base_slot_description": "Add a piece of armor", "item.minecraft.smithing_template.armor_trim.ingredients": "Ingots & Crystals", "item.minecraft.smithing_template.ingredients": "Ingredients:", "item.minecraft.smithing_template.netherite_upgrade.additions_slot_description": "Add Netherite Ingot", "item.minecraft.smithing_template.netherite_upgrade.applies_to": "Diamond Equipment", "item.minecraft.smithing_template.netherite_upgrade.base_slot_description": "Add diamond armor, weapon, or tool", "item.minecraft.smithing_template.netherite_upgrade.ingredients": "Netherite Ingot", "item.minecraft.smithing_template.upgrade": "Upgrade: ", "item.minecraft.sniffer_spawn_egg": "Sniffer Spawn Egg", "item.minecraft.snort_pottery_shard": "Snort Pottery Shard", "item.minecraft.snort_pottery_sherd": "<PERSON><PERSON><PERSON> Pottery Sherd", "item.minecraft.snout_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.snout_armor_trim_smithing_template.new": "Snout Armor Trim", "item.minecraft.snow_golem_spawn_egg": "Snow Golem Spawn Egg", "item.minecraft.snowball": "Snowball", "item.minecraft.spectral_arrow": "Akụkụ Spectral", "item.minecraft.spider_eye": "<PERSON>", "item.minecraft.spider_spawn_egg": "Akwa na-akpọ Ududo", "item.minecraft.spire_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.spire_armor_trim_smithing_template.new": "Spire Arm<PERSON>", "item.minecraft.splash_potion": "Mgbapu <PERSON>pu", "item.minecraft.splash_potion.effect.awkward": "Nsogbu na-adịghị mma", "item.minecraft.splash_potion.effect.empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.minecraft.splash_potion.effect.fire_resistance": "Ikwomm<PERSON> nke Ọkụ Nguzogide", "item.minecraft.splash_potion.effect.harming": "<PERSON>gba<PERSON> nke <PERSON>", "item.minecraft.splash_potion.effect.healing": "Mgbapu <PERSON>pu nke Agwọ", "item.minecraft.splash_potion.effect.infested": "Splash Potion of Infestation", "item.minecraft.splash_potion.effect.invisibility": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.splash_potion.effect.leaping": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.splash_potion.effect.levitation": "Mgbapu <PERSON> nke Levgba mbọ", "item.minecraft.splash_potion.effect.luck": "Mgbapu <PERSON> nke Luck", "item.minecraft.splash_potion.effect.mundane": "Ebee Ikwommiri <PERSON>", "item.minecraft.splash_potion.effect.night_vision": "Ikwommiri <PERSON> nke Night Night", "item.minecraft.splash_potion.effect.oozing": "Splash Potion of Oozing", "item.minecraft.splash_potion.effect.poison": "Mgbapu <PERSON>", "item.minecraft.splash_potion.effect.regeneration": "Mgba<PERSON> nke imu", "item.minecraft.splash_potion.effect.slow_falling": "Mgbapu <PERSON> nke nwayọ nwayọ", "item.minecraft.splash_potion.effect.slowness": "Mgba<PERSON> nke <PERSON>", "item.minecraft.splash_potion.effect.strength": "Mgbapu Mgbapu nke Ike", "item.minecraft.splash_potion.effect.swiftness": "Ikwommiri <PERSON> nke Ng<PERSON> ngwa", "item.minecraft.splash_potion.effect.thick": "Oke <PERSON>", "item.minecraft.splash_potion.effect.turtle_master": "Mgbapu <PERSON> nke <PERSON>wa", "item.minecraft.splash_potion.effect.water": "Igbapu mmiri kalama", "item.minecraft.splash_potion.effect.water_breathing": "Mgba<PERSON> nke <PERSON>eku ume", "item.minecraft.splash_potion.effect.weakness": "<PERSON><PERSON><PERSON><PERSON>ke Adịghị Ike", "item.minecraft.splash_potion.effect.weaving": "Splash Potion of Weaving", "item.minecraft.splash_potion.effect.wind_charged": "Splash Potion of Wind Charging", "item.minecraft.spruce_boat": "Rugbọ mmiri Spruce", "item.minecraft.spruce_chest_boat": "Spruce ụgbọ mmiri na obi", "item.minecraft.spyglass": "Spyglass", "item.minecraft.squid_spawn_egg": "Akwa na-akpọ Squid", "item.minecraft.stick": "Kọọ", "item.minecraft.stone_axe": "Nkume Ax", "item.minecraft.stone_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.stone_sword": "<PERSON><PERSON><PERSON>", "item.minecraft.stray_spawn_egg": "Akwa na-akpọ Wanderer", "item.minecraft.strider_spawn_egg": "Strider Spawn Egg", "item.minecraft.string": "<PERSON><PERSON><PERSON>", "item.minecraft.sugar": "Sugar\n\n", "item.minecraft.suspicious_stew": "<PERSON>yo enyo", "item.minecraft.sweet_berries": "<PERSON><PERSON>", "item.minecraft.tadpole_bucket": "Bucket na-emet<PERSON>", "item.minecraft.tadpole_spawn_egg": "Akwa na-akpọ Inyinya Tadpole", "item.minecraft.tide_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.tide_armor_trim_smithing_template.new": "Tide Armor Trim", "item.minecraft.tipped_arrow": "Ntuziaka", "item.minecraft.tipped_arrow.effect.awkward": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.empty": "Ugboro Akwadoghị <PERSON>ara", "item.minecraft.tipped_arrow.effect.fire_resistance": "<PERSON><PERSON><PERSON> nke <PERSON> Ọkụ", "item.minecraft.tipped_arrow.effect.harming": "<PERSON><PERSON><PERSON> nke emebi", "item.minecraft.tipped_arrow.effect.healing": "<PERSON><PERSON><PERSON> nke <PERSON>", "item.minecraft.tipped_arrow.effect.infested": "Arrow of Infestation", "item.minecraft.tipped_arrow.effect.invisibility": "<PERSON><PERSON><PERSON> nke <PERSON>", "item.minecraft.tipped_arrow.effect.leaping": "<PERSON><PERSON><PERSON> nke <PERSON>ụ elu", "item.minecraft.tipped_arrow.effect.levitation": "Akụ nke Itationgba mbọ", "item.minecraft.tipped_arrow.effect.luck": "<PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.mundane": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.night_vision": "<PERSON><PERSON><PERSON> n<PERSON>", "item.minecraft.tipped_arrow.effect.oozing": "Arrow of Oozing", "item.minecraft.tipped_arrow.effect.poison": "<PERSON><PERSON><PERSON> nke nsi", "item.minecraft.tipped_arrow.effect.regeneration": "<PERSON><PERSON><PERSON> nke imu", "item.minecraft.tipped_arrow.effect.slow_falling": "<PERSON>k<PERSON> nke ngwa ngwa ada", "item.minecraft.tipped_arrow.effect.slowness": "Akụ nke ngwa ngwa", "item.minecraft.tipped_arrow.effect.strength": "<PERSON><PERSON><PERSON> nke ike", "item.minecraft.tipped_arrow.effect.swiftness": "Akụ nke ngwa ngwa", "item.minecraft.tipped_arrow.effect.thick": "Tipped Arrow", "item.minecraft.tipped_arrow.effect.turtle_master": "<PERSON><PERSON><PERSON> nke <PERSON>-ukwu <PERSON>-ukwu", "item.minecraft.tipped_arrow.effect.water": "<PERSON><PERSON><PERSON> nke <PERSON>", "item.minecraft.tipped_arrow.effect.water_breathing": "Akụ nke mmiri iku ume", "item.minecraft.tipped_arrow.effect.weakness": "<PERSON><PERSON><PERSON> nke adịgh<PERSON> ike", "item.minecraft.tipped_arrow.effect.weaving": "Arrow of Weaving", "item.minecraft.tipped_arrow.effect.wind_charged": "Arrow of Wind Charging", "item.minecraft.tnt_minecart": "Minecart na TNT", "item.minecraft.torchflower_seeds": "Torchflower Seeds", "item.minecraft.totem_of_undying": "Totem nke Ekwenye", "item.minecraft.trader_llama_spawn_egg": "Onye ahia Llama akwa akwa", "item.minecraft.trial_key": "Trial Key", "item.minecraft.trident": "Trident", "item.minecraft.tropical_fish": "<PERSON><PERSON><PERSON> dị ebe ok<PERSON>", "item.minecraft.tropical_fish_bucket": "Bucket na-emet<PERSON>ụ ebe okpomọkụ", "item.minecraft.tropical_fish_spawn_egg": "Akwa na-akpọ Azù <PERSON>ọ<PERSON>ụ", "item.minecraft.turtle_helmet": "Shei shei", "item.minecraft.turtle_scute": "<PERSON>", "item.minecraft.turtle_spawn_egg": "Akwa na-akpọ Nnụnụ", "item.minecraft.vex_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.vex_armor_trim_smithing_template.new": "Vex Armor Trim", "item.minecraft.vex_spawn_egg": "Akwa na-akpọ Onye Omekome Ahụ", "item.minecraft.villager_spawn_egg": "Akwa na-akpọ Obị<PERSON>", "item.minecraft.vindicator_spawn_egg": "Akwa na-akpọ Onye Nchebe", "item.minecraft.wandering_trader_spawn_egg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Onye ahia spawn akwa", "item.minecraft.ward_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.ward_armor_trim_smithing_template.new": "<PERSON>", "item.minecraft.warden_spawn_egg": "Akwa na-akpọ Warden", "item.minecraft.warped_fungus_on_a_stick": "Fungus achara na osisi", "item.minecraft.water_bucket": "<PERSON><PERSON><PERSON><PERSON> mmiri", "item.minecraft.wayfinder_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.wayfinder_armor_trim_smithing_template.new": "Wayfinder Armor Trim", "item.minecraft.wheat": "Wheat", "item.minecraft.wheat_seeds": "<PERSON><PERSON><PERSON><PERSON><PERSON> witi", "item.minecraft.white_bundle": "White Bundle", "item.minecraft.white_dye": "<PERSON><PERSON><PERSON><PERSON>cha dai", "item.minecraft.white_harness": "<PERSON> Harness", "item.minecraft.wild_armor_trim_smithing_template": "<PERSON><PERSON> Template", "item.minecraft.wild_armor_trim_smithing_template.new": "Wild Armor Trim", "item.minecraft.wind_charge": "Wind Charge", "item.minecraft.witch_spawn_egg": "Akwa na-akpọ Onye Amoosu", "item.minecraft.wither_skeleton_spawn_egg": "<PERSON><PERSON><PERSON> na-a<PERSON><PERSON><PERSON> <PERSON>", "item.minecraft.wither_spawn_egg": "Wither Spawn Egg", "item.minecraft.wolf_armor": "<PERSON>or", "item.minecraft.wolf_spawn_egg": "Akwa na-akpọ Anụ <PERSON>", "item.minecraft.wooden_axe": "Osisi Ax", "item.minecraft.wooden_hoe": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_pickaxe": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_shovel": "<PERSON><PERSON><PERSON>", "item.minecraft.wooden_sword": "<PERSON><PERSON>", "item.minecraft.writable_book": "Akwụkwọ na njedebe", "item.minecraft.written_book": "Akwụkwọ ederede", "item.minecraft.yellow_bundle": "Yellow Bundle", "item.minecraft.yellow_dye": "Agba odo", "item.minecraft.yellow_harness": "Yellow Harness", "item.minecraft.zoglin_spawn_egg": "Akwa <PERSON>", "item.minecraft.zombie_horse_spawn_egg": "Akwa na-akpọ Zombie Ịnyịnya", "item.minecraft.zombie_spawn_egg": "Akwa na-akpọ Zombie", "item.minecraft.zombie_villager_spawn_egg": "Akwa na-akpọ Zombie <PERSON><PERSON>", "item.minecraft.zombified_piglin_spawn_egg": "Zighara Piglin Spawn Egg", "item.modifiers.any": "When equipped:", "item.modifiers.armor": "When worn:", "item.modifiers.body": "When equipped:", "item.modifiers.chest": "Mgbe Na Ahụ:", "item.modifiers.feet": "Mgbe na ụkwụ:", "item.modifiers.hand": "When held:", "item.modifiers.head": "Mgbe na ụkwụ:", "item.modifiers.legs": "Mgbe na ụkwụ:", "item.modifiers.mainhand": "Mgbe Ọnọdụ Aka:", "item.modifiers.offhand": "Mgbe Na-adịghị Aka:", "item.modifiers.saddle": "When saddled:", "item.nbt_tags": "NBT: %s mkpado(s)", "item.op_block_warning.line1": "Warning:", "item.op_block_warning.line2": "Use of this item might lead to command execution", "item.op_block_warning.line3": "Do not use unless you know the exact contents!", "item.unbreakable": "Ak<PERSON>ụ<PERSON>", "itemGroup.buildingBlocks": "<PERSON><PERSON><PERSON> e ji aru", "itemGroup.coloredBlocks": "Colored Blocks", "itemGroup.combat": "<PERSON><PERSON>", "itemGroup.consumables": "Consumables", "itemGroup.crafting": "Crafting", "itemGroup.foodAndDrink": "Food & Drinks", "itemGroup.functional": "Functional Blocks", "itemGroup.hotbar": "Zọpụtara Hotbars", "itemGroup.ingredients": "Ingredients", "itemGroup.inventory": "<PERSON><PERSON> e ji echekwa ndu", "itemGroup.natural": "Natural Blocks", "itemGroup.op": "Operator Utilities", "itemGroup.redstone": "Redstone Blocks", "itemGroup.search": "<PERSON><PERSON> <PERSON>he", "itemGroup.spawnEggs": "Spawn Eggs", "itemGroup.tools": "Tools & Utilities", "item_modifier.unknown": "<PERSON>he mgbanwe ihe amaghi ama: %s", "jigsaw_block.final_state": "Na-abanye n'ime:", "jigsaw_block.generate": "Na<PERSON><PERSON><PERSON><PERSON><PERSON>", "jigsaw_block.joint.aligned": "Kwadoro", "jigsaw_block.joint.rollable": "Rollable", "jigsaw_block.joint_label": "<PERSON><PERSON> nkwonkwo:", "jigsaw_block.keep_jigsaws": "<PERSON><PERSON>", "jigsaw_block.levels": "Etoju: %s", "jigsaw_block.name": "Aha:", "jigsaw_block.placement_priority": "Placement Priority:", "jigsaw_block.placement_priority.tooltip": "When this Jigsaw block connects to a piece, this is the order in which that piece is processed for connections in the wider structure.\n\nPieces will be processed in descending priority with insertion order breaking ties.", "jigsaw_block.pool": "Ezubere iche ọdọ mmiri:", "jigsaw_block.selection_priority": "Selection Priority:", "jigsaw_block.selection_priority.tooltip": "When the parent piece is being processed for connections, this is the order in which this Jigsaw block attempts to connect to its target piece.\n\nJigsaws will be processed in descending priority with random ordering breaking ties.", "jigsaw_block.target": "<PERSON>a zube:", "jukebox_song.minecraft.11": "C418 - 11", "jukebox_song.minecraft.13": "C418 - 13", "jukebox_song.minecraft.5": "<PERSON> - 5", "jukebox_song.minecraft.blocks": "C418 - blocks", "jukebox_song.minecraft.cat": "C418 - cat", "jukebox_song.minecraft.chirp": "C418 - chirp", "jukebox_song.minecraft.creator": "<PERSON> - <PERSON><PERSON>", "jukebox_song.minecraft.creator_music_box": "<PERSON> - <PERSON><PERSON> (Music Box)", "jukebox_song.minecraft.far": "C418 - far", "jukebox_song.minecraft.lava_chicken": "Hyper Potions - Lava Chicken", "jukebox_song.minecraft.mall": "C418 - mall", "jukebox_song.minecraft.mellohi": "C418 - me<PERSON><PERSON>", "jukebox_song.minecraft.otherside": "<PERSON> - <PERSON>ide", "jukebox_song.minecraft.pigstep": "<PERSON> - Pigstep", "jukebox_song.minecraft.precipice": "<PERSON> - Precipice", "jukebox_song.minecraft.relic": "<PERSON>", "jukebox_song.minecraft.stal": "C418 - stal", "jukebox_song.minecraft.strad": "C418 - strad", "jukebox_song.minecraft.tears": "<PERSON> - Tears", "jukebox_song.minecraft.wait": "C418 - wait", "jukebox_song.minecraft.ward": "C418 - ward", "key.advancements": "Ọga", "key.attack": "Mwakpo / ibibi", "key.back": "<PERSON><PERSON> azu", "key.categories.creative": "Creative Mode", "key.categories.gameplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> egwu", "key.categories.inventory": "<PERSON><PERSON>", "key.categories.misc": "Iche iche", "key.categories.movement": "<PERSON><PERSON><PERSON><PERSON>", "key.categories.multiplayer": "Multiplayer", "key.categories.ui": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key.chat": "<PERSON><PERSON><PERSON>", "key.command": "<PERSON><PERSON><PERSON>", "key.drop": "<PERSON><PERSON><PERSON><PERSON>", "key.forward": "Gaanụ n<PERSON>ru", "key.fullscreen": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>yo", "key.hotbar.1": "Hotbar oghere 1", "key.hotbar.2": "Hotbar oghere 2", "key.hotbar.3": "Hotbar oghere 3", "key.hotbar.4": "Hotbar oghere 4", "key.hotbar.5": "Hotbar oghere 5", "key.hotbar.6": "<PERSON>bar oghere 6", "key.hotbar.7": "Hotbar oghere 7", "key.hotbar.8": "Hotbar oghere 8", "key.hotbar.9": "Hotbar oghere 9", "key.inventory": "<PERSON><PERSON><PERSON> / <PERSON><PERSON>e", "key.jump": "<PERSON><PERSON><PERSON><PERSON>", "key.keyboard.apostrophe": "'", "key.keyboard.backslash": "\\", "key.keyboard.backspace": "Backspace", "key.keyboard.caps.lock": "Caps Lock", "key.keyboard.comma": ",", "key.keyboard.delete": "Delete", "key.keyboard.down": "Ala", "key.keyboard.end": "End", "key.keyboard.enter": "<PERSON><PERSON>", "key.keyboard.equal": "=", "key.keyboard.escape": "Gbafu", "key.keyboard.f1": "F1", "key.keyboard.f10": "F10", "key.keyboard.f11": "F11", "key.keyboard.f12": "F12", "key.keyboard.f13": "F13", "key.keyboard.f14": "F14", "key.keyboard.f15": "F15", "key.keyboard.f16": "F16", "key.keyboard.f17": "F17", "key.keyboard.f18": "F18", "key.keyboard.f19": "F19", "key.keyboard.f2": "F2", "key.keyboard.f20": "F20", "key.keyboard.f21": "F21", "key.keyboard.f22": "F22", "key.keyboard.f23": "F23", "key.keyboard.f24": "F24", "key.keyboard.f25": "F25", "key.keyboard.f3": "F3", "key.keyboard.f4": "F4", "key.keyboard.f5": "F5", "key.keyboard.f6": "F6", "key.keyboard.f7": "F7", "key.keyboard.f8": "F8", "key.keyboard.f9": "F9", "key.keyboard.grave.accent": "`", "key.keyboard.home": "Pos 1", "key.keyboard.insert": "Einfg", "key.keyboard.keypad.0": "Num 0", "key.keyboard.keypad.1": "Num 1", "key.keyboard.keypad.2": "Num 2", "key.keyboard.keypad.3": "Num 3", "key.keyboard.keypad.4": "Num 4", "key.keyboard.keypad.5": "Num 5", "key.keyboard.keypad.6": "Num 6", "key.keyboard.keypad.7": "Num 7", "key.keyboard.keypad.8": "Num 8", "key.keyboard.keypad.9": "Num 9", "key.keyboard.keypad.add": "Num +", "key.keyboard.keypad.decimal": "<PERSON><PERSON> ,", "key.keyboard.keypad.divide": "Num /", "key.keyboard.keypad.enter": "<PERSON><PERSON>", "key.keyboard.keypad.equal": "Num =", "key.keyboard.keypad.multiply": "Num *", "key.keyboard.keypad.subtract": "Num -", "key.keyboard.left": "<PERSON>'aka ekpe", "key.keyboard.left.alt": "N'aka ek<PERSON>", "key.keyboard.left.bracket": "[", "key.keyboard.left.control": "N'aka ekpe <PERSON>l", "key.keyboard.left.shift": "<PERSON>'aka e<PERSON><PERSON>", "key.keyboard.left.win": "<PERSON><PERSON>aka <PERSON><PERSON><PERSON>", "key.keyboard.menu": "<PERSON><PERSON>", "key.keyboard.minus": "-", "key.keyboard.num.lock": "Num Lock", "key.keyboard.page.down": "Page N'ada", "key.keyboard.page.up": "<PERSON>", "key.keyboard.pause": "Pause", "key.keyboard.period": ".", "key.keyboard.print.screen": "Druck", "key.keyboard.right": "Ụda Aka", "key.keyboard.right.alt": "Nri Alt", "key.keyboard.right.bracket": "]", "key.keyboard.right.control": "Nri Ctrl", "key.keyboard.right.shift": "Nri Shift", "key.keyboard.right.win": "<PERSON><PERSON>", "key.keyboard.scroll.lock": "<PERSON><PERSON> Lock", "key.keyboard.semicolon": ";", "key.keyboard.slash": "/", "key.keyboard.space": "<PERSON><PERSON>", "key.keyboard.tab": "Tab", "key.keyboard.unknown": "Not Bound", "key.keyboard.up": "<PERSON><PERSON><PERSON>", "key.keyboard.world.1": "Ụwa 1", "key.keyboard.world.2": "Ụwa 2", "key.left": "<PERSON><PERSON><PERSON> aka e<PERSON><PERSON>", "key.loadToolbarActivator": "\n<PERSON><PERSON>", "key.mouse": "Bọtịnụ %1$s", "key.mouse.left": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "key.mouse.middle": "<PERSON><PERSON><PERSON><PERSON>", "key.mouse.right": "<PERSON><PERSON>", "key.pickItem": "Soro Gbochie", "key.playerlist": "<PERSON>uo nd<PERSON> e<PERSON>wu", "key.quickActions": "Quick Actions", "key.right": "Nnennen Right", "key.saveToolbarActivator": "Chekwa onye na aru oru Hotbar", "key.screenshot": "Were Screenshot", "key.smoothCamera": "Toggle Cinematic Camera", "key.sneak": "<PERSON><PERSON><PERSON>", "key.socialInteractions": "<PERSON>lek<PERSON><PERSON> mmekọ<PERSON><PERSON><PERSON> mmekọr<PERSON>", "key.spectatorOutlines": "Ndị na-eme egwuregwu na-egwu egwu (Ndị na-ah<PERSON> ya n'anya)", "key.sprint": "Sprint", "key.swapOffhand": "Gbanwee Ihe na Offhand", "key.togglePerspective": "Gbanwee Echiche", "key.use": "<PERSON><PERSON>", "known_server_link.announcements": "Announcements", "known_server_link.community": "Community", "known_server_link.community_guidelines": "Community Guidelines", "known_server_link.feedback": "<PERSON><PERSON><PERSON>", "known_server_link.forums": "Forums", "known_server_link.news": "News", "known_server_link.report_bug": "Report Server Bug", "known_server_link.status": "Status", "known_server_link.support": "Support", "known_server_link.website": "Website", "lanServer.otherPlayers": "Ntọala maka ndị egwuregwu ndị <PERSON>", "lanServer.port": "Port Number", "lanServer.port.invalid": "Not a valid port.\nLeave the edit box empty or enter a number between 1024 and 65535.", "lanServer.port.invalid.new": "Not a valid port.\nLeave the edit box empty or enter a number between %s and %s.", "lanServer.port.unavailable": "Port not available.\nLeave the edit box empty or enter a different number between 1024 and 65535.", "lanServer.port.unavailable.new": "Port not available.\nLeave the edit box empty or enter a different number between %s and %s.", "lanServer.scanning": "Na-en<PERSON><PERSON> egwuregwu na netwọkụ dị na mpaghara gị", "lanServer.start": "Bido LAN World", "lanServer.title": "LAN World", "language.code": "ibo_NG", "language.name": "Igbo", "language.region": "Naiger<PERSON>", "lectern.take_book": "Were akwo", "loading.progress": "%s%%", "mco.account.privacy.info": "Gụkwuo ban<PERSON>e iwu Mojang na nzuzo", "mco.account.privacy.info.button": "Read more about GDPR", "mco.account.privacy.information": "Mojang implements certain procedures to help protect children and their privacy including complying with the Children's Online Privacy Protection Act (COPPA) and General Data Protection Regulation (GDPR).\n\nYou may need to obtain parental consent before accessing your Realms account.", "mco.account.privacyinfo": "Mojang na-egosi<PERSON><PERSON><PERSON> ụfọdụ usoro iji nyere aka kpuchido ụ<PERSON>ụ<PERSON> na nzuzo ha gụnyere ịgbaso Iwu Nchedo Nzuzo Onlinemụaka (COPPA) na Iwu Nchedo Data General (GDPR).\n\nNwere ike nwere ike ịnweta nkwenye nne na nna tupu ịbanye na akaụntụ gọọmentị gị.\n\nỌ bụrụ na ị nwere akaụntụ Minecraft merela agadi (i ji aha njirimara gị banye), ịkwesịrị ịkwaga akaụntụ ahụ na akaụntụ Mojang iji banye Msdị.", "mco.account.update": "Mel<PERSON>", "mco.activity.noactivity": "No activity for the past %s day(s)", "mco.activity.title": "Player activity", "mco.backup.button.download": "<PERSON><PERSON>", "mco.backup.button.reset": "Reset World", "mco.backup.button.restore": "<PERSON><PERSON>achi", "mco.backup.button.upload": "Upload World", "mco.backup.changes.tooltip": "Mgbanwe", "mco.backup.entry": "Backup (%s)", "mco.backup.entry.description": "Description", "mco.backup.entry.enabledPack": "Enabled Pack(s)", "mco.backup.entry.gameDifficulty": "Game Difficulty", "mco.backup.entry.gameMode": "Game Mode", "mco.backup.entry.gameServerVersion": "Game Server Version", "mco.backup.entry.name": "<PERSON>a", "mco.backup.entry.seed": "Seed", "mco.backup.entry.templateName": "Template Name", "mco.backup.entry.undefined": "Undefined Change", "mco.backup.entry.uploaded": "Uploaded", "mco.backup.entry.worldType": "World Type", "mco.backup.generate.world": "N'ịwa ụwa", "mco.backup.info.title": "Changes From Last Backup", "mco.backup.narration": "Backup from %s", "mco.backup.nobackups": "<PERSON><PERSON> al<PERSON> enwegh<PERSON> nkwado ọ bụla ugbu a.", "mco.backup.restoring": "Weghachi obodo gị", "mco.backup.unknown": "UNKNOWN", "mco.brokenworld.download": "Bud<PERSON>", "mco.brokenworld.downloaded": "Na nbudata", "mco.brokenworld.message.line1": "<PERSON><PERSON>a ma <PERSON> bụ họr<PERSON> ụwa ọzọ.", "mco.brokenworld.message.line2": "Nwekwara ike ịhọrọ ibudata ụwa na-ebugharị naanị.", "mco.brokenworld.minigame.title": "A naghị akwado egwuregwu Obere a", "mco.brokenworld.nonowner.error": "<PERSON>iko chere ka onye nwe ụlọ tọ<PERSON><PERSON><PERSON><PERSON>", "mco.brokenworld.nonowner.title": "<PERSON><PERSON> a<PERSON>a", "mco.brokenworld.play": "Play", "mco.brokenworld.reset": "Reset", "mco.brokenworld.title": "A naghị akwadozi <PERSON>wa gị ugbu a", "mco.client.incompatible.msg.line1": "<PERSON><PERSON> ah<PERSON>a bụ ihe ochie na ọ bụghị dakọtara naalm.", "mco.client.incompatible.msg.line2": "<PERSON><PERSON> jiri <PERSON> mbipụta nke Minecraft kachas<PERSON>.", "mco.client.incompatible.msg.line3": "<PERSON><PERSON> na n<PERSON>a ovu.", "mco.client.incompatible.title": "<PERSON><PERSON><PERSON> ahịa e<PERSON>!", "mco.client.outdated.stable.version": "Your client version (%s) is not compatible with Realms.\n\nPlease use the most recent version of Minecraft.", "mco.client.unsupported.snapshot.version": "Your client version (%s) is not compatible with Realms.\n\nRealms is not available for this snapshot version.", "mco.compatibility.downgrade": "Downgrade", "mco.compatibility.downgrade.description": "This world was last played in version %s; you are on version %s. Downgrading a world could cause corruption - we cannot guarantee that it will load or work.\n\nA backup of your world will be saved under \"World Backups\". Please restore your world if needed.", "mco.compatibility.incompatible.popup.title": "Incompatible version", "mco.compatibility.incompatible.releaseType.popup.message": "The world you are trying to join is incompatible with the version you are on.", "mco.compatibility.incompatible.series.popup.message": "This world was last played in version %s; you are on version %s.\n\nThese series are not compatible with each other. A new world is needed to play on this version.", "mco.compatibility.unverifiable.message": "The version this world was last played in could not be verified. If the world gets upgraded or downgraded, a backup will be automatically created and saved under \"World Backups\".", "mco.compatibility.unverifiable.title": "Compatibility not verifiable", "mco.compatibility.upgrade": "Upgrade", "mco.compatibility.upgrade.description": "This world was last played in version %s; you are on version %s.\n\nA backup of your world will be saved under \"World Backups\".\n\nPlease restore your world if needed.", "mco.compatibility.upgrade.friend.description": "This world was last played in version %s; you are on version %s.\n\nA backup of the world will be saved under \"World Backups\".\n\nThe owner of the Realm can restore the world if needed.", "mco.compatibility.upgrade.title": "Do you really want to upgrade this world?", "mco.configure.current.minigame": "Current", "mco.configure.world.activityfeed.disabled": "<PERSON><PERSON> n<PERSON> egwu egwu na-adịgh<PERSON> nwa oge", "mco.configure.world.backup": "Nkwado ụwa", "mco.configure.world.buttons.activity": "Onye na-eme e<PERSON><PERSON>wu", "mco.configure.world.buttons.close": "Temporarily Close Realm", "mco.configure.world.buttons.delete": "Delete", "mco.configure.world.buttons.done": "Done", "mco.configure.world.buttons.edit": "Settings", "mco.configure.world.buttons.invite": "<PERSON>ye <PERSON>ụkpọ òkù", "mco.configure.world.buttons.moreoptions": "<PERSON>o ndi ozo", "mco.configure.world.buttons.newworld": "New World", "mco.configure.world.buttons.open": "Reopen Realm", "mco.configure.world.buttons.options": "Nhọrọ <PERSON>", "mco.configure.world.buttons.players": "Players", "mco.configure.world.buttons.region_preference": "Select Region...", "mco.configure.world.buttons.resetworld": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.buttons.save": "Save", "mco.configure.world.buttons.settings": "Settings", "mco.configure.world.buttons.subscription": "<PERSON><PERSON><PERSON><PERSON> aha", "mco.configure.world.buttons.switchminigame": "Gbanwee Obere egwu<PERSON>gwu", "mco.configure.world.close.question.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON> gi agagh adi adi.", "mco.configure.world.close.question.line2": "Are you sure you want to continue?", "mco.configure.world.close.question.title": "Need to make changes without disruption?", "mco.configure.world.closing": "I<PERSON>chi ala eze...", "mco.configure.world.commandBlocks": "<PERSON><PERSON>", "mco.configure.world.delete.button": "<PERSON><PERSON><PERSON><PERSON> obodo", "mco.configure.world.delete.question.line1": "A ga-ewepụ alaeze gị kpamkpam", "mco.configure.world.delete.question.line2": "Are you sure you want to continue?", "mco.configure.world.description": "Nkọ<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.edit.slot.name": "<PERSON><PERSON><PERSON>", "mco.configure.world.edit.subscreen.adventuremap": "Enwere <PERSON><PERSON><PERSON><PERSON><PERSON> ntọala n'ihi na <PERSON>wa a bụ ihe njem", "mco.configure.world.edit.subscreen.experience": "Enwere <PERSON><PERSON><PERSON><PERSON><PERSON> ntọala n'ihi na <PERSON>wa a bụ ihe ahụmịhe", "mco.configure.world.edit.subscreen.inspiration": "Enwere <PERSON><PERSON><PERSON><PERSON><PERSON> ntọala n'ihi na <PERSON>wa a bụ ihe mkpali", "mco.configure.world.forceGameMode": "Egwuregwu mode ike", "mco.configure.world.invite.narration": "You have %s new invite(s)", "mco.configure.world.invite.profile.name": "<PERSON>a", "mco.configure.world.invited": "Akpọọ", "mco.configure.world.invited.number": "Invited (%s)", "mco.configure.world.invites.normal.tooltip": "<PERSON>ye ọ<PERSON>", "mco.configure.world.invites.ops.tooltip": "<PERSON><PERSON>", "mco.configure.world.invites.remove.tooltip": "<PERSON><PERSON>", "mco.configure.world.leave.question.line1": "Ọ bụrụ n’ịhapụ obodo a, ị gaghị ahụ ya ọ gwụla ma a kpọrọ gị ọzọ", "mco.configure.world.leave.question.line2": "Are you sure you want to continue?", "mco.configure.world.loading": "Loading Realm", "mco.configure.world.location": "<PERSON><PERSON>", "mco.configure.world.minigame": "Current: %s", "mco.configure.world.name": "<PERSON><PERSON> al<PERSON><PERSON>", "mco.configure.world.opening": "Imepe ala eze...", "mco.configure.world.players.error": "<PERSON><PERSON> nke nwere aha enyere adịghị adị", "mco.configure.world.players.inviting": "Inviting player...", "mco.configure.world.players.title": "Players", "mco.configure.world.pvp": "Onye ọkpụkpọ Na Onye ọkpụkpọ", "mco.configure.world.region_preference": "Region Preference", "mco.configure.world.region_preference.title": "Region Preference Selection", "mco.configure.world.reset.question.line1": "Willwa gị ga-adị <PERSON>ụ ma <PERSON>wa gị ugbu a ga-efu", "mco.configure.world.reset.question.line2": "Are you sure you want to continue?", "mco.configure.world.resourcepack.question": "You need a custom resource pack to play on this Realm\n\nDo you want to download it and play?", "mco.configure.world.resourcepack.question.line1": "<PERSON><PERSON><PERSON><PERSON> ebe a na-ewu ewu nke obodo a", "mco.configure.world.resourcepack.question.line2": "Gi chọrọ ibudata ya ma kpọọ ya?", "mco.configure.world.restore.download.question.line1": "A ga-ebudata <PERSON>wa wee tinye na ụwa ọkpụkpọ gị.", "mco.configure.world.restore.download.question.line2": "Gi ch<PERSON>?", "mco.configure.world.restore.question.line1": "A ga-eweghachi ụwa gị na ụbọchị '%s' (%s)", "mco.configure.world.restore.question.line2": "Are you sure you want to continue?", "mco.configure.world.settings.expired": "You cannot edit settings of an expired Realm", "mco.configure.world.settings.title": "Ntọala", "mco.configure.world.slot": "Eluuwa %s", "mco.configure.world.slot.empty": "Efu", "mco.configure.world.slot.switch.question.line1": "A ga-agbanwere alaeze gị ụwa ọzọ", "mco.configure.world.slot.switch.question.line2": "Are you sure you want to continue?", "mco.configure.world.slot.tooltip": "Gbanye n’ụwa", "mco.configure.world.slot.tooltip.active": "<PERSON>ro", "mco.configure.world.slot.tooltip.minigame": "Sapeta Obere e<PERSON>wu<PERSON>wu", "mco.configure.world.spawnAnimals": "Anụ<PERSON><PERSON>e<PERSON> efe", "mco.configure.world.spawnMonsters": "<PERSON><PERSON><PERSON> spawn", "mco.configure.world.spawnNPCs": "Spawn NPCs", "mco.configure.world.spawnProtection": "Nchedo spawn", "mco.configure.world.spawn_toggle.message": "Ịgbanyụ nhọrọ a ga-ewepụ otu niile dị n'ụdị ahụ", "mco.configure.world.spawn_toggle.message.npc": "Ịgbanyụ nhọrọ a ga-ewepụ ụdị otu niile dị adị, dị ka ndị obodo", "mco.configure.world.spawn_toggle.title": "Warning!", "mco.configure.world.status": "Ọnọdụ", "mco.configure.world.subscription.day": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.days": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.subscription.expired": "Em<PERSON><PERSON><PERSON>", "mco.configure.world.subscription.extend": "<PERSON><PERSON><PERSON><PERSON> ndenye aha", "mco.configure.world.subscription.less_than_a_day": "<PERSON><PERSON> na<PERSON>er<PERSON><PERSON> o<PERSON>", "mco.configure.world.subscription.month": "ọnwa", "mco.configure.world.subscription.months": "Ọnwa", "mco.configure.world.subscription.recurring.daysleft": "Ndi ohuru ohuru in", "mco.configure.world.subscription.recurring.info": "Changes made to your Realms subscription such as stacking time or turning off recurring billing will not be reflected until your next bill date.", "mco.configure.world.subscription.remaining.days": "%1$s day(s)", "mco.configure.world.subscription.remaining.months": "%1$s month(s)", "mco.configure.world.subscription.remaining.months.days": "%1$s month(s), %2$s day(s)", "mco.configure.world.subscription.start": "Ụbọchị mmalite", "mco.configure.world.subscription.tab": "Subscription", "mco.configure.world.subscription.timeleft": "<PERSON><PERSON>", "mco.configure.world.subscription.title": "<PERSON><PERSON>ye aha gi", "mco.configure.world.subscription.unknown": "<PERSON><PERSON><PERSON> ama", "mco.configure.world.switch.slot": "<PERSON><PERSON><PERSON><PERSON>", "mco.configure.world.switch.slot.subtitle": "Wa a bụ ihe efu, họr<PERSON> otu esi emep<PERSON>ta <PERSON>wa gị", "mco.configure.world.title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>:", "mco.configure.world.uninvite.player": "Are you sure that you want to uninvite '%s'?", "mco.configure.world.uninvite.question": "O doro gị anya na <PERSON> ịkpọ", "mco.configure.worlds.title": "Ụwa", "mco.connect.authorizing": "Logging in...", "mco.connect.connecting": "Ijikọ ụlọ...", "mco.connect.failed": "Enweghi ike ijikọ n'ógbè", "mco.connect.region": "Server region: %s", "mco.connect.success": "Done", "mco.create.world": "<PERSON><PERSON><PERSON><PERSON>", "mco.create.world.error": "I gha achoghi aha!", "mco.create.world.failed": "Failed to create world!", "mco.create.world.reset.title": "<PERSON><PERSON><PERSON><PERSON>...", "mco.create.world.skip": "<PERSON><PERSON><PERSON>", "mco.create.world.subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, họ<PERSON><PERSON>wa ị ga-etinye n’obodo gị", "mco.create.world.wait": "<PERSON><PERSON><PERSON><PERSON> aleaze...", "mco.download.cancelled": "Nbudata kag<PERSON>o", "mco.download.confirmation.line1": "Uwa ị na-aga ibudata karịrị %s", "mco.download.confirmation.line2": "Gaghị enwe ike ibugo <PERSON>wa a na alaeze gị ọzọ", "mco.download.confirmation.oversized": "The world you are going to download is larger than %s\n\nYou won't be able to upload this world to your Realm again", "mco.download.done": "<PERSON><PERSON> emeela", "mco.download.downloading": "Nbudata", "mco.download.extracting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mco.download.failed": "<PERSON><PERSON><PERSON> dara", "mco.download.percent": "%s %%", "mco.download.preparing": "Na-akwadebe nbudata", "mco.download.resourcePack.fail": "Failed to download resource pack!", "mco.download.speed": "(%s/s)", "mco.download.speed.narration": "%s/s", "mco.download.title": "Na nbudata ụwa <PERSON>ụ<PERSON>", "mco.error.invalid.session.message": "<PERSON><PERSON> nwaa <PERSON>ị Minecraft", "mco.error.invalid.session.title": "<PERSON><PERSON> e<PERSON>hi ezi", "mco.errorMessage.6001": "<PERSON><PERSON><PERSON>", "mco.errorMessage.6002": "Anabataghị usoro <PERSON>r<PERSON>", "mco.errorMessage.6003": "Ibudata nbudata ruru", "mco.errorMessage.6004": "Ibugo ebugote ruru", "mco.errorMessage.6005": "World locked", "mco.errorMessage.6006": "World is out of date", "mco.errorMessage.6007": "User in too many Realms", "mco.errorMessage.6008": "Invalid Realm name", "mco.errorMessage.6009": "Invalid Realm description", "mco.errorMessage.connectionFailure": "<PERSON><PERSON><PERSON> mere, biko nwaa ọz<PERSON>.", "mco.errorMessage.generic": "An error occurred: ", "mco.errorMessage.initialize.failed": "Failed to initialize Realm", "mco.errorMessage.noDetails": "No error details provided", "mco.errorMessage.realmsService": "An error occurred (%s):", "mco.errorMessage.realmsService.configurationError": "An unexpected error occurred while editing world options", "mco.errorMessage.realmsService.connectivity": "Could not connect to Realms: %s", "mco.errorMessage.realmsService.realmsError": "Realms (%s):", "mco.errorMessage.realmsService.unknownCompatibility": "Could not check compatible version, got response: %s", "mco.errorMessage.retry": "Retry operation", "mco.errorMessage.serviceBusy": "Realms na-ar<PERSON> ọ<PERSON> n'oge a.\n<PERSON><PERSON> nwaa <PERSON>ye na mpaghara gị ọzọ n'ime nkeji ole na ole.", "mco.gui.button": "<PERSON><PERSON>tị<PERSON>", "mco.gui.ok": "Ok", "mco.info": "Info!", "mco.invited.player.narration": "Invited player %s", "mco.invites.button.accept": "<PERSON><PERSON><PERSON>", "mco.invites.button.reject": "Jụ", "mco.invites.nopending": "Ọ dịghị òkù na-eche!", "mco.invites.pending": "New invite(s)!", "mco.invites.title": "Iteskpọ oku", "mco.minigame.world.changeButton": "Họrọ egwuregwu mini ọzọ", "mco.minigame.world.info.line1": "Nke a ga-eji obere ihe egwuregwu dochie <PERSON>wa gị!", "mco.minigame.world.info.line2": "Inwere ike <PERSON> n’<PERSON>wa mbụ gị n’enweghị ihe funahụrụ gị.", "mco.minigame.world.noSelection": "<PERSON><PERSON> mee nh<PERSON>", "mco.minigame.world.restore": "<PERSON><PERSON><PERSON> egwu<PERSON>gwu na-akwụsị...", "mco.minigame.world.restore.question.line1": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ah<PERSON> ga-akw<PERSON>, al<PERSON><PERSON><PERSON><PERSON><PERSON> alaeze gị.", "mco.minigame.world.restore.question.line2": "Ì ji n’aka na ị chọrọ n’ihu?", "mco.minigame.world.selected": "Egwuregwu obere mini ahọpụtara:", "mco.minigame.world.slot.screen.title": "Chingwa na-agbagharị...", "mco.minigame.world.startButton": "Gbanwee", "mco.minigame.world.starting.screen.title": "<PERSON><PERSON>o obere egwure<PERSON>wu...", "mco.minigame.world.stopButton": "Kwụsị obere egwure<PERSON>wu", "mco.minigame.world.switch.new": "H<PERSON><PERSON><PERSON> egwuregwu obere ọzọ?", "mco.minigame.world.switch.title": "Switch Minigame", "mco.minigame.world.title": "Gbanye alaeze n'obere egwuregwu", "mco.news": "Akụkọ n'ezie", "mco.notification.dismiss": "<PERSON><PERSON><PERSON>", "mco.notification.transferSubscription.buttonText": "Transfer Now", "mco.notification.transferSubscription.message": "Java Realms subscriptions are moving to the Microsoft Store. Do not let your subscription expire!\nTransfer now and get 30 days of Realms for free.\nGo to Profile on minecraft.net to transfer your subscription.", "mco.notification.visitUrl.buttonText.default": "Open Link", "mco.notification.visitUrl.message.default": "Please visit the link below", "mco.onlinePlayers": "Online Players", "mco.play.button.realm.closed": "Realm is closed", "mco.question": "Question", "mco.reset.world.adventure": "Ah<PERSON>", "mco.reset.world.experience": "<PERSON><PERSON><PERSON>", "mco.reset.world.generate": "Ọhụrụ <PERSON>wa", "mco.reset.world.inspiration": "Mk<PERSON><PERSON>", "mco.reset.world.resetting.screen.title": "<PERSON><PERSON><PERSON>...", "mco.reset.world.seed": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Nhọrọ)", "mco.reset.world.template": "<PERSON><PERSON><PERSON><PERSON>", "mco.reset.world.title": "Reset World", "mco.reset.world.upload": "<PERSON><PERSON><PERSON>", "mco.reset.world.warning": "Nke a ga-an<PERSON><PERSON>wa nke ugbu a", "mco.selectServer.buy": "Zụta n'ógbè!", "mco.selectServer.close": "<PERSON><PERSON><PERSON>", "mco.selectServer.closed": "O<PERSON>do emechiri emechi", "mco.selectServer.closeserver": "Obodo di nso", "mco.selectServer.configure": "<PERSON><PERSON>", "mco.selectServer.configureRealm": "Configure Realm", "mco.selectServer.create": "<PERSON><PERSON><PERSON><PERSON>", "mco.selectServer.create.subtitle": "Select what world to put on your new Realm", "mco.selectServer.expired": "<PERSON><PERSON> em<PERSON>", "mco.selectServer.expiredList": "<PERSON><PERSON><PERSON> aha gi ekwu<PERSON>la", "mco.selectServer.expiredRenew": "<PERSON><PERSON><PERSON>", "mco.selectServer.expiredSubscribe": "<PERSON><PERSON><PERSON> aha", "mco.selectServer.expiredTrial": "Ọnwụnwa gị agw<PERSON>la", "mco.selectServer.expires.day": "<PERSON><PERSON> ga-<PERSON><PERSON>", "mco.selectServer.expires.days": "Ewepuga n'ime %s ụbọchị", "mco.selectServer.expires.soon": "<PERSON><PERSON><PERSON> n'isi nso", "mco.selectServer.leave": "<PERSON><PERSON><PERSON>", "mco.selectServer.loading": "Loading Realms List", "mco.selectServer.mapOnlySupportedForVersion": "Akwadoghi map a %s", "mco.selectServer.minigame": "Minigame:", "mco.selectServer.minigameName": "Minigame: %s", "mco.selectServer.minigameNotSupportedInVersion": "Enweghị ike <PERSON> minigame a %s", "mco.selectServer.noRealms": "You don't seem to have a Realm. Add a Realm to play together with your friends.", "mco.selectServer.note": "Mara:", "mco.selectServer.open": "<PERSON><PERSON><PERSON> emepe", "mco.selectServer.openserver": "Open Realm", "mco.selectServer.play": "<PERSON><PERSON><PERSON>", "mco.selectServer.popup": "Realms bụ nchekwa dị mfe ma dị mfe iji nwee ọ enjoyụ na ụwa ịnọnyere Minecraft nke ihe ruru ndị enyi iri n'otu oge. Ọ na-akwado ọtụtụ minigames na ụwa nke ọtụtụ omenala! Naanị onye nwe ụlọ chọrọ ịkwụ ụgwọ.", "mco.selectServer.purchase": "<PERSON><PERSON>", "mco.selectServer.trial": "Nweta ikpe!", "mco.selectServer.uninitialized": "Pịa iji malite n'ógbè <PERSON>ụ<PERSON> g<PERSON>!", "mco.snapshot.createSnapshotPopup.text": "You are about to create a free Snapshot Realm that will be paired with your paid Realms subscription. This new Snapshot Realm will be accessible for as long as the paid subscription is active. Your paid Realm will not be affected.", "mco.snapshot.createSnapshotPopup.title": "Create Snapshot Realm?", "mco.snapshot.creating": "Creating Snapshot Realm...", "mco.snapshot.description": "Paired with \"%s\"", "mco.snapshot.friendsRealm.downgrade": "You need to be on version %s to join this Realm", "mco.snapshot.friendsRealm.upgrade": "%s needs to upgrade their Realm before you can play from this version", "mco.snapshot.paired": "This Snapshot Realm is paired with \"%s\"", "mco.snapshot.parent.tooltip": "Use the latest release of Minecraft to play on this Realm", "mco.snapshot.start": "Start free Snapshot Realm", "mco.snapshot.subscription.info": "This is a Snapshot Realm that is paired to the subscription of your Realm '%s'. It will stay active for as long as its paired Realm is.", "mco.snapshot.tooltip": "Use Snapshot Realms to get a sneak peek at upcoming versions of Minecraft, which might include new features and other changes.\n\nYou can find your normal Realms in the release version of the game.", "mco.snapshotRealmsPopup.message": "Realms are now available in Snapshots starting with Snapshot 23w41a. Every Realms subscription comes with a free Snapshot Realm that is separate from your normal Java Realm!", "mco.snapshotRealmsPopup.title": "Realms now available in Snapshots", "mco.snapshotRealmsPopup.urlText": "Learn More", "mco.template.button.publisher": "<PERSON><PERSON> mbip<PERSON> akw<PERSON>kw<PERSON>", "mco.template.button.select": "<PERSON><PERSON><PERSON><PERSON>", "mco.template.button.trailer": "Trailer nke ihe nkiri", "mco.template.default.name": "Agba ụwa", "mco.template.info.tooltip": "Weebụ<PERSON><PERSON><PERSON> onye mgbasa ozi", "mco.template.name": "Eser<PERSON>", "mco.template.select.failure": "Anyị enweghị ike ibudata listi ọdịnaya maka ụdị a.\n<PERSON> njik<PERSON> Ịntanetị ma ọ bụ gbalịa ọzọ mgbe e mesịrị.", "mco.template.select.narrate.authors": "Ndị edemede: %s", "mco.template.select.narrate.version": "ụdị %s", "mco.template.select.none": "<PERSON>, ọ dị ka ụdị ọdịnaya a dị ugbu a.\n<PERSON><PERSON> azụ mgbe e mesịrị maka ọdịnaya ọhụrụ ma ọ bụrụ na ị bụ onye okike,\n%s.", "mco.template.select.none.linkTitle": "t<PERSON><PERSON> izipu ihe n'ime mmadụ", "mco.template.title": "World templates", "mco.template.title.minigame": "<PERSON><PERSON><PERSON> e<PERSON>", "mco.template.trailer.tooltip": "<PERSON><PERSON><PERSON>", "mco.terms.buttons.agree": "Kwer<PERSON>", "mco.terms.buttons.disagree": "Ek<PERSON><PERSON><PERSON>", "mco.terms.sentence.1": "<PERSON><PERSON><PERSON><PERSON><PERSON> m na Minecraft Realms", "mco.terms.sentence.2": "<PERSON><PERSON>", "mco.terms.title": "Realms Usoro ọrụ", "mco.time.daysAgo": "%1$s day(s) ago", "mco.time.hoursAgo": "%1$s hour(s) ago", "mco.time.minutesAgo": "%1$s minute(s) ago", "mco.time.now": "right now", "mco.time.secondsAgo": "%1$s second(s) ago", "mco.trial.message.line1": "<PERSON><PERSON><PERSON><PERSON> alae<PERSON> nke gị?", "mco.trial.message.line2": "<PERSON><PERSON>a ebe a maka ozi ndị ọzọ!", "mco.upload.button.name": "Bulite", "mco.upload.cancelled": "<PERSON>g<PERSON>o bulite ya", "mco.upload.close.failure": "<PERSON>we<PERSON><PERSON> ike imechi obodo g<PERSON>, biko n<PERSON>a <PERSON>", "mco.upload.done": "<PERSON>ulite emee", "mco.upload.entry.cheats": "%1$s, %2$s", "mco.upload.entry.commands": "%1$s, %2$s", "mco.upload.entry.id": "%1$s (%2$s)", "mco.upload.failed": "Mbugo ada ada! (%s)", "mco.upload.failed.too_big.description": "The selected world is too big. The maximum allowed size is %s.", "mco.upload.failed.too_big.title": "World too big", "mco.upload.hardcore": "<PERSON>we<PERSON>i ike itinye <PERSON>wa nke ike!", "mco.upload.percent": "%s %%", "mco.upload.preparing": "Pre na-akwadebe ụwa gị", "mco.upload.select.world.none": "O nweghi <PERSON>wa hụrụ naanị otu!", "mco.upload.select.world.subtitle": "<PERSON><PERSON> nke <PERSON>", "mco.upload.select.world.title": "Upload World", "mco.upload.size.failure.line1": "'%s' buru oke ibu!", "mco.upload.size.failure.line2": "Ọ bụ %s. <PERSON>go ogo kachasị kwere bụ %s.", "mco.upload.uploading": "Na-ebugote '%s'", "mco.upload.verifying": "Inyocha ụwa gị", "mco.version": "Version: %s", "mco.warning": "Warning!", "mco.worldSlot.minigame": "Minigame", "menu.custom_options": "Custom Options...", "menu.custom_options.title": "Custom Options", "menu.custom_options.tooltip": "Note: Custom options are provided by third-party servers and/or content.\nHandle with care!", "menu.custom_screen_info.button_narration": "This is a custom screen. Learn more.", "menu.custom_screen_info.contents": "The contents of this screen are controlled by third-party servers and maps that are not owned, operated, or supervised by Mojang Studios or Microsoft.\n\nHandle with care! Always be careful when following links and never give away your personal information, including login details.\n\nIf this screen prevents you from playing, you can also disconnect from the current server by using the button below.", "menu.custom_screen_info.disconnect": "Custom screen rejected", "menu.custom_screen_info.title": "Note about custom screens", "menu.custom_screen_info.tooltip": "This is a custom screen. Click here to learn more.", "menu.disconnect": "Leave", "menu.feedback": "Feedback...", "menu.feedback.title": "<PERSON><PERSON><PERSON>", "menu.game": "Global Menu", "menu.modded": "(Modded)", "menu.multiplayer": "Online Game", "menu.online": "Minecraft Realms", "menu.options": "Options...", "menu.paused": "<PERSON><PERSON><PERSON> kw<PERSON>", "menu.playdemo": "<PERSON><PERSON><PERSON> ngosi", "menu.playerReporting": "Onye ejije nwaanyi A<PERSON>ụ<PERSON>", "menu.preparingSpawn": "Na-akwa<PERSON>be mpaghara spawn", "menu.quick_actions": "Quick Actions...", "menu.quick_actions.title": "Quick Actions", "menu.quit": "Leave", "menu.reportBugs": "Bipụta mkpọ", "menu.resetdemo": "Tọgharia ngosi <PERSON>", "menu.returnToGame": "Continue to play", "menu.returnToMenu": "Chekwaa ma <PERSON>ụ<PERSON>ị n’isi", "menu.savingChunks": "Ịchekwa chunk", "menu.savingLevel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.sendFeedback": "<PERSON><PERSON>", "menu.server_links": "Server Links...", "menu.server_links.title": "Server Links", "menu.shareToLan": "<PERSON><PERSON>ghe maka netwọk mpaghara", "menu.singleplayer": "Single Game", "menu.working": "Na-<PERSON><PERSON><PERSON>...", "merchant.deprecated": "<PERSON>d<PERSON> obodo na-e<PERSON><PERSON><PERSON><PERSON> ugboro abụọ kwa ụbọchị.", "merchant.level.1": "Nnọọ", "merchant.level.2": "N<PERSON><PERSON>", "merchant.level.3": "Journeyman", "merchant.level.4": "Ọkachamara", "merchant.level.5": "<PERSON><PERSON>", "merchant.title": "%s - %s", "merchant.trades": "<PERSON><PERSON><PERSON>", "mirror.front_back": "↑ ↓", "mirror.left_right": "← →", "mirror.none": "<PERSON><PERSON>", "mount.onboard": "Pịa%1$s ka ịwepu", "multiplayer.applyingPack": "<PERSON> <PERSON><PERSON><PERSON><PERSON> ngwu<PERSON>wu ak<PERSON>", "multiplayer.confirm_command.parse_errors": "You are trying to execute an unrecognized or invalid command.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.permissions_required": "You are trying to execute a command that requires elevated permissions.\nThis might negatively affect your game.\nAre you sure?\nCommand: %s", "multiplayer.confirm_command.title": "Confirm Command Execution", "multiplayer.disconnect.authservers_down": "<PERSON>he nkesa nkwenye agbadaala. <PERSON><PERSON> nwaa ọzọ ma emechaa!", "multiplayer.disconnect.bad_chat_index": "Detected missed or reordered chat message from server", "multiplayer.disconnect.banned": "A machibidoro gị na ihe nkesa a", "multiplayer.disconnect.banned.expiration": "\nA ga-ewepụ mmachibido iwu gị na %s", "multiplayer.disconnect.banned.reason": "A machibidoro gị na ihe nkesa a. \nIhe kpatara: %s", "multiplayer.disconnect.banned_ip.expiration": "\nYour ban will be removed on %s", "multiplayer.disconnect.banned_ip.reason": "A machibidoro adreesị IP gị site na ihe nkesa a.\nIhe kpatara: %s", "multiplayer.disconnect.chat_validation_failed": "Ọdịda nkwado ozi nkata", "multiplayer.disconnect.duplicate_login": "I si n’ebe <PERSON> bata", "multiplayer.disconnect.expired_public_key": "Igodo ọha profa<PERSON>ụ emebiela. <PERSON><PERSON> na oge sistemụ gị emekọr<PERSON><PERSON>, wee nwaa <PERSON> egwuregwu gị.", "multiplayer.disconnect.flying": "Enyeghi ikike ife efe na ihe nkesa a", "multiplayer.disconnect.generic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "multiplayer.disconnect.idling": "I nọọla nkịtị ogologo oge!", "multiplayer.disconnect.illegal_characters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gh<PERSON> na nkata", "multiplayer.disconnect.incompatible": "Onye ahịa dakọtaghi! Biko jiri%s", "multiplayer.disconnect.invalid_entity_attacked": "<PERSON><PERSON> otu a<PERSON> uru", "multiplayer.disconnect.invalid_packet": "<PERSON><PERSON> nkesa zip<PERSON><PERSON><PERSON> ngwungwu na -adịgh<PERSON> mma", "multiplayer.disconnect.invalid_player_data": "Data onye egwuregwu na ezighi ezi", "multiplayer.disconnect.invalid_player_movement": "<PERSON><PERSON><PERSON> m<PERSON> adịghi mma nke ngwugwu onye egwuregwu", "multiplayer.disconnect.invalid_public_key_signature": "<PERSON><PERSON><PERSON> aka na ezighi ezi maka igodo <PERSON>ha profaịlụ.\n<PERSON><PERSON><PERSON><PERSON> egwuregwu gị.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "<PERSON><PERSON><PERSON> m<PERSON> adịghi mma nke ngwugwu <PERSON>g<PERSON>", "multiplayer.disconnect.ip_banned": "Enyela gị mmachi IP site n'ihe nkesa a", "multiplayer.disconnect.kicked": "Agbara ukwu site n'aka Onye ọrụ", "multiplayer.disconnect.missing_tags": "<PERSON>kwu ihe mkpado ezughi ezu enwetara n'aka ihe nkesa.\n<PERSON>iko kpọtụr<PERSON> onye na-ahụ maka ihe nkesa.", "multiplayer.disconnect.name_taken": "<PERSON> <PERSON>la aha ahụ", "multiplayer.disconnect.not_whitelisted": "Ị naghị acha ọcha edepụtara na ihe nkesa a!", "multiplayer.disconnect.out_of_order_chat": "<PERSON><PERSON><PERSON> ngwugwu nkata anaghị akwụ ụgwọ. Oge sistemụ gị ọ gbanwere?", "multiplayer.disconnect.outdated_client": "Incompatible client! Please use %s", "multiplayer.disconnect.outdated_server": "Incompatible client! Please use %s", "multiplayer.disconnect.server_full": "Ihe nkesa ejula!", "multiplayer.disconnect.server_shutdown": "<PERSON>he n<PERSON>a mechiri", "multiplayer.disconnect.slow_login": "O were ogologo oge <PERSON>ye", "multiplayer.disconnect.too_many_pending_chats": "Ọtụtụ ozi nkata anaghị anabata", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "Data odinala <PERSON>ụghị anya sitere na aka onye ahịa", "multiplayer.disconnect.unsigned_chat": "<PERSON><PERSON><PERSON> ngwugwu nkata nwere m<PERSON>ye aka na-efu efu ma ọ bụ na-e<PERSON>hi ezi.", "multiplayer.disconnect.unverified_username": "Enwegh<PERSON> ike <PERSON>ta aha n<PERSON>!", "multiplayer.downloadingStats": "Na-<PERSON><PERSON><PERSON><PERSON><PERSON> ọ<PERSON>...", "multiplayer.downloadingTerrain": "<PERSON>ru ala...", "multiplayer.lan.server_found": "New server found: %s", "multiplayer.message_not_delivered": "Enwegh<PERSON> ike i<PERSON>pu ozi nkata, le<PERSON> ndek<PERSON> sava:%s", "multiplayer.player.joined": "%s son<PERSON><PERSON> eg<PERSON><PERSON>wu", "multiplayer.player.joined.renamed": "%s (nke a na-akpọbu %s) sonyeere egwuregwu", "multiplayer.player.left": "%s hap<PERSON><PERSON><PERSON> e<PERSON>wu", "multiplayer.player.list.hp": "%shp", "multiplayer.player.list.narration": "Online players: %s", "multiplayer.requiredTexturePrompt.disconnect": "<PERSON><PERSON> ch<PERSON><PERSON> ngwungwu ak<PERSON> omenala", "multiplayer.requiredTexturePrompt.line1": "<PERSON><PERSON> n<PERSON>a a chọrọ ka ejiri ngwungwu ak<PERSON> omenala.", "multiplayer.requiredTexturePrompt.line2": "Ịjụ ngwungwu akụ omenala a ga -ewepụ gị na sava a.", "multiplayer.socialInteractions.not_available": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mmek<PERSON>r<PERSON><PERSON> dị naan<PERSON> na <PERSON>wa <PERSON>wu<PERSON>", "multiplayer.status.and_more": "... na %s Ọzọ ...", "multiplayer.status.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.cannot_connect": "Enweghi ike ijikọ na sava", "multiplayer.status.cannot_resolve": "<PERSON><PERSON><PERSON>i ike idozi aha nna<PERSON>a", "multiplayer.status.finished": "<PERSON><PERSON><PERSON>", "multiplayer.status.incompatible": "Comdị adabagh<PERSON>!", "multiplayer.status.motd.narration": "Message of the day: %s", "multiplayer.status.no_connection": "(enwe<PERSON><PERSON> n<PERSON>kọ)", "multiplayer.status.old": "<PERSON><PERSON><PERSON>", "multiplayer.status.online": "Online", "multiplayer.status.ping": "%s ms", "multiplayer.status.ping.narration": "Ping %s milliseconds", "multiplayer.status.pinging": "Na-etinye...", "multiplayer.status.player_count": "%s/%s", "multiplayer.status.player_count.narration": "%s out of %s players online", "multiplayer.status.quitting": "<PERSON><PERSON><PERSON><PERSON>", "multiplayer.status.request_handled": "<PERSON><PERSON><PERSON> a<PERSON><PERSON>", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "<PERSON><PERSON>", "multiplayer.status.version.narration": "Server version: %s", "multiplayer.stopSleeping": "Leave", "multiplayer.texturePrompt.failure.line1": "Enweghị ike itinye n'ọrụ ihe ntinye aka", "multiplayer.texturePrompt.failure.line2": "Ọrụ ọ bụla nke chọrọ akụrụngwa nwere ike ọ gaghị arụ ọrụ dịka a tụrụ anya ya", "multiplayer.texturePrompt.line1": "<PERSON>he nkesa a na-akwado ka ojiji nke otu ihe enyemaka omenala.", "multiplayer.texturePrompt.line2": "You ga - achọ ibudata ma wụnye ya na akpaghị aka?", "multiplayer.texturePrompt.serverPrompt": "%s\n\n<PERSON>i si na sava:\n%s", "multiplayer.title": "Kpọọ otutu", "multiplayer.unsecureserver.toast": "Enwere ike gbanwee ozi ezitere na nkesa a nwere ike ọ gaghị egosipụta ozi izizi", "multiplayer.unsecureserver.toast.title": "Enwegh<PERSON> ike <PERSON> ozi nkata", "multiplayerWarning.check": "<PERSON><PERSON><PERSON><PERSON> ihuenyo ozo", "multiplayerWarning.header": "Nkpachara anya: Playgba egwu Ntanetị Ndị Ntanetị", "multiplayerWarning.message": "Ikpachara anya: Ndị na-enye ozi atọ dị n'ịntanetị na-enye ihe nkesa nke ndị na-enweghị, ar<PERSON> ọrụ ma ọ bụ ndị Mojang Studios ma ọ bụ Microsoft na-elekọta. N’egwuregwu ịnta<PERSON>ị, enwere ike igosi gị ozi nkata na-anaghị edozi ma ọ bụ ụdị ọdịnaya nke emebere nke onye ọrụ na-adabaghịrị onye ọ bụla.", "music.game.a_familiar_room": "<PERSON> - A Familiar Room", "music.game.an_ordinary_day": "<PERSON><PERSON> - An Ordinary Day", "music.game.ancestry": "<PERSON>", "music.game.below_and_above": "<PERSON> - Below and Above", "music.game.broken_clocks": "<PERSON> - Broken Clocks", "music.game.bromeliad": "<PERSON> Bromeliad", "music.game.clark": "C418 - <PERSON>", "music.game.comforting_memories": "<PERSON><PERSON> - Comforting Memories", "music.game.creative.aria_math": "C418 - <PERSON> Math", "music.game.creative.biome_fest": "C418 - Biome Fest", "music.game.creative.blind_spots": "C418 - Blind Spots", "music.game.creative.dreiton": "C418 - <PERSON><PERSON><PERSON>", "music.game.creative.haunt_muskie": "C418 - <PERSON><PERSON>", "music.game.creative.taswell": "C418 - <PERSON><PERSON><PERSON>", "music.game.crescent_dunes": "<PERSON> - Crescent Dunes", "music.game.danny": "C418 - <PERSON>", "music.game.deeper": "<PERSON> - <PERSON>", "music.game.dry_hands": "C418 - Dry Hands", "music.game.echo_in_the_wind": "<PERSON> - Echo in the Wind", "music.game.eld_unknown": "<PERSON> - <PERSON><PERSON>", "music.game.end.alpha": "C418 - Alpha", "music.game.end.boss": "C418 - <PERSON>", "music.game.end.the_end": "C418 - The End", "music.game.endless": "<PERSON> - End<PERSON>", "music.game.featherfall": "<PERSON> - Featherfall", "music.game.fireflies": "<PERSON> - Fireflies", "music.game.floating_dream": "<PERSON><PERSON> - Floating Dream", "music.game.haggstrom": "C418 - <PERSON><PERSON><PERSON>", "music.game.infinite_amethyst": "<PERSON> - Infinite Amethyst", "music.game.key": "C418 - Key", "music.game.komorebi": "<PERSON><PERSON> k<PERSON><PERSON>", "music.game.left_to_bloom": "<PERSON> - Left to Bloom", "music.game.lilypad": "<PERSON> - <PERSON><PERSON>", "music.game.living_mice": "C418 - <PERSON>", "music.game.mice_on_venus": "C418 - <PERSON><PERSON> on Venus", "music.game.minecraft": "C418 - Minecraft", "music.game.nether.ballad_of_the_cats": "C418 - Ballad of the Cats", "music.game.nether.concrete_halls": "C418 - Concrete Halls", "music.game.nether.crimson_forest.chrysopoeia": "<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "music.game.nether.dead_voxel": "C418 - <PERSON> Voxel", "music.game.nether.nether_wastes.rubedo": "<PERSON> - <PERSON><PERSON><PERSON>", "music.game.nether.soulsand_valley.so_below": "<PERSON> - So Below", "music.game.nether.warmth": "C418 - Warm<PERSON>", "music.game.one_more_day": "<PERSON> - One More Day", "music.game.os_piano": "<PERSON> - O's Piano", "music.game.oxygene": "C418 - Oxygène", "music.game.pokopoko": "<PERSON><PERSON><PERSON><PERSON>", "music.game.puzzlebox": "<PERSON> - Puzzlebox", "music.game.stand_tall": "<PERSON> - Stand Tall", "music.game.subwoofer_lullaby": "C418 - <PERSON><PERSON><PERSON><PERSON>by", "music.game.swamp.aerie": "<PERSON>", "music.game.swamp.firebugs": "<PERSON> - Firebugs", "music.game.swamp.labyrinthine": "<PERSON> - Labyrinthine", "music.game.sweden": "C418 - Sweden", "music.game.watcher": "<PERSON> - Watcher", "music.game.water.axolotl": "C418 - <PERSON><PERSON><PERSON><PERSON>", "music.game.water.dragon_fish": "C418 - <PERSON> Fish", "music.game.water.shuniji": "C418 - <PERSON><PERSON><PERSON>", "music.game.wending": "<PERSON>", "music.game.wet_hands": "C418 - <PERSON> Hands", "music.game.yakusoku": "<PERSON><PERSON><PERSON>", "music.menu.beginning_2": "C418 - Beginning 2", "music.menu.floating_trees": "C418 - Floating Trees", "music.menu.moog_city_2": "C418 - Moog City 2", "music.menu.mutation": "C418 - Mutation", "narration.button": "Mpiaka:%s", "narration.button.usage.focused": "Pịa banye ka ikpalite", "narration.button.usage.hovered": "<PERSON><PERSON><PERSON> aka<PERSON> ka ikpalite", "narration.checkbox": "Igbe nlele: %s", "narration.checkbox.usage.focused": "<PERSON><PERSON><PERSON> banye ka igbanwee", "narration.checkbox.usage.hovered": "<PERSON><PERSON><PERSON>", "narration.component_list.usage": "Pịa <PERSON> ka <PERSON>arịa na mmewere ọzọ", "narration.cycle_button.usage.focused": "Pịa banye ka <PERSON>gbanwee gaa na %s", "narration.cycle_button.usage.hovered": "P<PERSON>a aka<PERSON> ka <PERSON>e gaa na %s", "narration.edit_box": "Igbe nhazi: %s", "narration.item": "Item: %s", "narration.recipe": "Akurungwa maka %s", "narration.recipe.usage": "<PERSON><PERSON><PERSON>", "narration.recipe.usage.more": "<PERSON><PERSON><PERSON> aka nri iji gosi usoro nri nd<PERSON>", "narration.selection.usage": "Pịa mpiaka elu na ala ka igaa na mbanye ọzọ", "narration.slider.usage.focused": "<PERSON><PERSON><PERSON> aka e<PERSON><PERSON> b<PERSON> aka nri iji g<PERSON>wee uru", "narration.slider.usage.hovered": "Dọr<PERSON> ihe mmịfe iji gbanwee uru", "narration.suggestion": "Alo hotara %s n'ime %s: %s", "narration.suggestion.tooltip": "Alo ahotara %s n'ime %s: %s (%s)", "narration.suggestion.usage.cycle.fixed": "Press Tab to cycle to the next suggestion", "narration.suggestion.usage.cycle.hidable": "Press Tab to cycle to the next suggestion, or Escape to leave suggestions", "narration.suggestion.usage.fill.fixed": "Press Tab to use suggestion", "narration.suggestion.usage.fill.hidable": "Press Tab to use suggestion, or Escape to leave suggestions", "narration.tab_navigation.usage": "Press Ctrl and Tab to switch between tabs", "narrator.button.accessibility": "Ikennweta", "narrator.button.difficulty_lock": "<PERSON>gbachi siri ike", "narrator.button.difficulty_lock.locked": "Akpochiela", "narrator.button.difficulty_lock.unlocked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "narrator.button.language": "<PERSON><PERSON><PERSON>", "narrator.controls.bound": "ejikọtara %s na %s", "narrator.controls.reset": "Dozigharia Mpiaka %s", "narrator.controls.unbound": "ejikotaghi %s", "narrator.joining": "Njikọ", "narrator.loading": "Mkponye: %s", "narrator.loading.done": "<PERSON><PERSON><PERSON><PERSON>", "narrator.position.list": "Ahọorọ ndepụta ah<PERSON> %s n'ime %s", "narrator.position.object_list": "Ahịrị mmewere ahọpụtara %s site na %s", "narrator.position.screen": "Hoputa ihe %s site na%s", "narrator.position.tab": "Selected tab %s out of %s", "narrator.ready_to_play": "Ready to play", "narrator.screen.title": "<PERSON><PERSON><PERSON><PERSON> aha", "narrator.screen.usage": "<PERSON><PERSON> cursor ma <PERSON> b<PERSON> iji họrọ mmewere", "narrator.select": "Ahoọrọ: %s", "narrator.select.world": "Ahoọrọ: %s egwuregwu ikpeazụ: %s, %s, %s, version: %s", "narrator.select.world_info": "Selected %s, last played: %s, %s", "narrator.toast.disabled": "Onye Nwepu <PERSON>", "narrator.toast.enabled": "<PERSON><PERSON><PERSON> aka", "optimizeWorld.confirm.description": "Nke a ga - anwa ime ka ụwa gị dịkwuo mma site n'ịhụ na echekwara data niile n'ụdị egwuregwu kacha nso. Nke a nwere ike iwe ogologo oge, dabere na ụwa gị. <PERSON><PERSON><PERSON>, ụwa gị nwere ike igwu egwu ngwa ngwa mana ọ gaghị enwe ike dakọtara na ụdị egwuregwu nke ochie. Ì ji n'aka na ị chọrọ ịga n'ihu?", "optimizeWorld.confirm.proceed": "Create Backup and Optimize", "optimizeWorld.confirm.title": "Kpalie <PERSON>wa", "optimizeWorld.info.converted": "<PERSON><PERSON><PERSON> akwukwu: %s", "optimizeWorld.info.skipped": "Sikized anwụrụ: %s", "optimizeWorld.info.total": "Mkpokọta chunks: %s", "optimizeWorld.progress.counter": "%s / %s", "optimizeWorld.progress.percentage": "%s%%", "optimizeWorld.stage.counting": "Na-aguta chunks...", "optimizeWorld.stage.failed": "Error! :(", "optimizeWorld.stage.finished": "Finishing up...", "optimizeWorld.stage.finished.chunks": "Finishing up upgrading chunks...", "optimizeWorld.stage.finished.entities": "Finishing up upgrading entities...", "optimizeWorld.stage.finished.poi": "Finishing up upgrading points of interest...", "optimizeWorld.stage.upgrading": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.chunks": "Upgrading all chunks...", "optimizeWorld.stage.upgrading.entities": "Upgrading all entities...", "optimizeWorld.stage.upgrading.poi": "Upgrading all points of interest...", "optimizeWorld.title": "Optwa kachasị elu '%s'", "options.accessibility": "Accessibility Settings...", "options.accessibility.high_contrast": "High Contrast", "options.accessibility.high_contrast.error.tooltip": "High Contrast resource pack is not available.", "options.accessibility.high_contrast.tooltip": "Enhances the contrast of UI elements.", "options.accessibility.high_contrast_block_outline": "High Contrast Block Outlines", "options.accessibility.high_contrast_block_outline.tooltip": "Enhances the block outline contrast of the targeted block.", "options.accessibility.link": "Ntuzia<PERSON>", "options.accessibility.menu_background_blurriness": "<PERSON><PERSON> Blur", "options.accessibility.menu_background_blurriness.tooltip": "Changes the blurriness of menu backgrounds.", "options.accessibility.narrator_hotkey": "Narrator <PERSON>", "options.accessibility.narrator_hotkey.mac.tooltip": "Allows the Narrator to be toggled on and off with 'Cmd+B'.", "options.accessibility.narrator_hotkey.tooltip": "Allows the Narrator to be toggled on and off with 'Ctrl+B'.", "options.accessibility.panorama_speed": "Panorama Scroll Speed", "options.accessibility.text_background": "<PERSON><PERSON><PERSON> ederede", "options.accessibility.text_background.chat": "Cha<PERSON>", "options.accessibility.text_background.everywhere": "<PERSON><PERSON> b<PERSON>", "options.accessibility.text_background_opacity": "Ederede Akpata Ederede Ederede", "options.accessibility.title": "Ntọala nnweta...", "options.allowServerListing": "<PERSON><PERSON><PERSON> nde<PERSON><PERSON> n<PERSON>a", "options.allowServerListing.tooltip": "Ndị sava nwere ike depụta ndị egwuregwu ịntanetị dịka akụkụ nke ọkwa <PERSON>ha ha.\nNa nhọrọ a kwụsịrị aha gị agaghị apụta na listi ndị dị otú ahụ.", "options.ao": "Light Light", "options.ao.max": "<PERSON>e", "options.ao.min": "Opekempe", "options.ao.off": "OFF", "options.attack.crosshair": "<PERSON><PERSON><PERSON>", "options.attack.hotbar": "Hotbar", "options.attackIndicator": "<PERSON><PERSON><PERSON><PERSON>", "options.audioDevice": "<PERSON><PERSON><PERSON><PERSON>", "options.audioDevice.default": "Sistem<PERSON> n<PERSON>", "options.autoJump": "Na-awụlikwa elu", "options.autoSuggestCommands": "<PERSON><PERSON> maka iwu", "options.autosaveIndicator": "<PERSON><PERSON><PERSON><PERSON>", "options.biomeBlendRadius": "Ngwakọta Biome", "options.biomeBlendRadius.1": "<PERSON><PERSON><PERSON><PERSON> (kachasị ọsọ)", "options.biomeBlendRadius.11": "11x11 (<PERSON><PERSON>)", "options.biomeBlendRadius.13": "13x13 (Showoff)", "options.biomeBlendRadius.15": "15x15 (<PERSON><PERSON>)", "options.biomeBlendRadius.3": "3x3 (ngwa ngwa)", "options.biomeBlendRadius.5": "5x5 (Nkịtị)", "options.biomeBlendRadius.7": "\n7x7 (<PERSON>u)", "options.biomeBlendRadius.9": "9x9 (<PERSON><PERSON><PERSON><PERSON> oke)", "options.chat": "Cha<PERSON>s...", "options.chat.color": "Agba", "options.chat.delay": "Oge nkata: %s sekọnd", "options.chat.delay_none": "Oge nkata: <PERSON><PERSON><PERSON><PERSON>", "options.chat.height.focused": "<PERSON><PERSON><PERSON><PERSON>", "options.chat.height.unfocused": "<PERSON><PERSON><PERSON> edozi", "options.chat.line_spacing": "Spacing Line", "options.chat.links": "Njikọ ntanetị", "options.chat.links.prompt": "Bido na njikọ", "options.chat.opacity": "<PERSON><PERSON><PERSON> Ederede Nkata", "options.chat.scale": "Nkata ederede Eke", "options.chat.title": "Ntọala nkata...", "options.chat.visibility": "Nkata", "options.chat.visibility.full": "Shown", "options.chat.visibility.hidden": "Hidden", "options.chat.visibility.system": "<PERSON><PERSON><PERSON>", "options.chat.width": "<PERSON><PERSON>", "options.chunks": "%s chunks", "options.clouds.fancy": "Fancy", "options.clouds.fast": "Fast", "options.controls": "Njikwa...", "options.credits_and_attribution": "Credits & Attribution...", "options.damageTiltStrength": "Damage Tilt", "options.damageTiltStrength.tooltip": "The amount of camera shake caused by being hurt.", "options.darkMojangStudiosBackgroundColor": "Njirimara monochrome", "options.darkMojangStudiosBackgroundColor.tooltip": "Na -agbanwe agba agba ihuenyo ihuenyo Mojang Studios ka <PERSON> bụr<PERSON> oji.", "options.darknessEffectScale": "Ọchịchịrị Pulsing", "options.darknessEffectScale.tooltip": "Na achịkwa ole mmetụta Ọchịchịrị na-eru mgbe Warden ma <PERSON> bụ <PERSON>k <PERSON>er nyere gị ya.", "options.difficulty": "<PERSON>he isi ike", "options.difficulty.easy": "<PERSON><PERSON>", "options.difficulty.easy.info": "Hostile mobs spawn but deal less damage. Hunger bar depletes and drains health down to 5 hearts.", "options.difficulty.hard": "Ihe siri ike", "options.difficulty.hard.info": "Hostile mobs spawn and deal more damage. Hunger bar depletes and drains all health.", "options.difficulty.hardcore": "Hardcore", "options.difficulty.normal": "Normal", "options.difficulty.normal.info": "Hostile mobs spawn and deal standard damage. Hunger bar depletes and drains health down to half a heart.", "options.difficulty.online": "<PERSON>he isi ike n<PERSON>a", "options.difficulty.peaceful": "<PERSON><PERSON>", "options.difficulty.peaceful.info": "No hostile mobs and only some neutral mobs spawn. Hunger bar doesn't deplete and health replenishes over time.", "options.directionalAudio": "Audio ntụziaka", "options.directionalAudio.off.tooltip": "Omuma Stereo", "options.directionalAudio.on.tooltip": "Na eji ọdịyo ntụzịaka dabere na HRTF iji kwalite ịme anwansị nke ụda 3D. Ch<PERSON>r<PERSON> ngwaike ọdịyo dakọtara HRTF, yana ah<PERSON> kacha<PERSON> mma na ekweisi.", "options.discrete_mouse_scroll": "Akwụkwọ mpịakọta pụ<PERSON>ụ iche", "options.entityDistanceScaling": "<PERSON>", "options.entityShadows": "Ọnọdụ ndò", "options.font": "Font Settings...", "options.font.title": "Font Settings", "options.forceUnicodeFont": "Force Unicode Font", "options.fov": "FOV", "options.fov.max": "<PERSON><PERSON><PERSON> ji<PERSON>", "options.fov.min": "Normal", "options.fovEffectScale": "FOV mmet<PERSON>ta", "options.fovEffectScale.tooltip": "Na-achịkwa ihe nlele anya nwere ike ịgbanwe site na mmetụta e<PERSON>wuregwu.", "options.framerate": "%s fps", "options.framerateLimit": "Okpokoro Max", "options.framerateLimit.max": "Enweghi oke", "options.fullscreen": "<PERSON><PERSON><PERSON><PERSON> zuru oke", "options.fullscreen.current": "Ug<PERSON> a", "options.fullscreen.entry": "%sx%s@%s (%sbit)", "options.fullscreen.resolution": "<PERSON><PERSON><PERSON> zuru oke", "options.fullscreen.unavailable": "Isetị<PERSON><PERSON> adịghị adị", "options.gamma": "<PERSON><PERSON>", "options.gamma.default": "<PERSON><PERSON><PERSON>", "options.gamma.max": "<PERSON><PERSON> mma", "options.gamma.min": "<PERSON>", "options.generic_value": "%s: %s", "options.glintSpeed": "<PERSON><PERSON>", "options.glintSpeed.tooltip": "Controls how fast the visual glint shimmers across enchanted items.", "options.glintStrength": "<PERSON><PERSON>", "options.glintStrength.tooltip": "Controls how transparent the visual glint is on enchanted items.", "options.graphics": "<PERSON>he eserese", "options.graphics.fabulous": "Ihe magburu onwe ya!", "options.graphics.fabulous.tooltip": "%s ndịna-emeputa na-eji ihe ngebichi maka ịb<PERSON>hu <PERSON>we, ig<PERSON> oji<PERSON>, na ihe ndị dị na mgbochi translucent na mmiri.\nNke a nwere ike imetụta mmetụta dị ukwuu maka ngwaọrụ mkpanaaka na ngosipụta 4K.", "options.graphics.fancy": "<PERSON><PERSON><PERSON>", "options.graphics.fancy.tooltip": "Fancy ndịna na-edozi ar<PERSON> na ogo maka ọtụtụ igwe.\n<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> o<PERSON>, na i<PERSON>hiri ihe nwere ike ghara <PERSON>p<PERSON>ta n'azụ mgbochi ma ọ bụ mmiri.", "options.graphics.fast": "Ng<PERSON> ngwa", "options.graphics.fast.tooltip": "Ngwa ngwa ngwa na-ebelata ọnụ ọgụgụ nke mmiri ozuzo na snow.\nEnweghi ike ime ihe ngosi maka ihe di iche iche dika akwukwo.", "options.graphics.warning.accept": "Continue Without Support", "options.graphics.warning.cancel": "Take Me Back", "options.graphics.warning.message": "Achọpụtara ihe osise gị dị ka nke akwadoghi maka %s ndịna nhọrọ.\nYou nwere ike ileghara nke a anya ma gaa n'ihu, agban<PERSON>ghị nkwado agaghị enye maka ngwaọrụ gị ma ọ bụrụ na ịhọrọ iji %s ndịna.", "options.graphics.warning.renderer": "<PERSON><PERSON>ọp<PERSON><PERSON> Renderer: [%s]", "options.graphics.warning.title": "Akwadoghi Graphics Device", "options.graphics.warning.vendor": "<PERSON><PERSON>ọpụtara ndị na-ere ere: [%s]", "options.graphics.warning.version": "Achọpụtara OpenGL Version: [%s]", "options.guiScale": "Ọkpụkpụ GUI", "options.guiScale.auto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aka", "options.hidden": "Hidden", "options.hideLightningFlashes": "Zoo ọkụ <PERSON>", "options.hideLightningFlashes.tooltip": "Prevents Lightning Bolts from making the sky flash. The bolts themselves will still be visible.", "options.hideMatchedNames": "Zoo aha ndị dabara adaba", "options.hideMatchedNames.tooltip": "3rd-party Servers may send chat messages in non-standard formats.\nWith this option on, hidden players will be matched based on chat sender names.", "options.hideSplashTexts": "Hide Splash Texts", "options.hideSplashTexts.tooltip": "Hides the yellow splash text in the main menu.", "options.inactivityFpsLimit": "Reduce FPS when", "options.inactivityFpsLimit.afk": "AFK", "options.inactivityFpsLimit.afk.tooltip": "Limits framerate to 30 when the game is not getting any player input for more than a minute. Further limits it to 10 after 9 more minutes.", "options.inactivityFpsLimit.minimized": "Minimized", "options.inactivityFpsLimit.minimized.tooltip": "Limits framerate only when the game window is minimized.", "options.invertMouse": "Invert Mouse", "options.japaneseGlyphVariants": "Japanese Glyph Variants", "options.japaneseGlyphVariants.tooltip": "Uses Japanese variants of CJK characters in the default font.", "options.key.hold": "<PERSON><PERSON>", "options.key.toggle": "<PERSON><PERSON><PERSON>", "options.language": "<PERSON><PERSON><PERSON>ụ...", "options.language.title": "Language", "options.languageAccuracyWarning": "(Language translations may not be 100%% accurate)", "options.languageWarning": "Nsụgharị as<PERSON><PERSON><PERSON> nwere ike ọ gaghị abụ 100%%%% ezi", "options.mainHand": "<PERSON><PERSON> n'<PERSON>", "options.mainHand.left": "<PERSON>'aka ekpe", "options.mainHand.right": "<PERSON><PERSON>aka nri", "options.mipmapLevels": "Ọkwa Mipmap", "options.modelPart.cape": "Cape", "options.modelPart.hat": "<PERSON><PERSON>", "options.modelPart.jacket": "Jetetị", "options.modelPart.left_pants_leg": "Left Pants Leg", "options.modelPart.left_sleeve": "Aka ekpe", "options.modelPart.right_pants_leg": "Pkwụ Pants Right", "options.modelPart.right_sleeve": "<PERSON><PERSON> nri", "options.mouseWheelSensitivity": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "options.mouse_settings": "Ntọala òké...", "options.mouse_settings.title": "Ntọala òké", "options.multiplayer.title": "Ntọala ọtụtụ…", "options.multiplier": "%sx", "options.music_frequency": "Music Frequency", "options.music_frequency.constant": "Constant", "options.music_frequency.default": "<PERSON><PERSON><PERSON>", "options.music_frequency.frequent": "Frequent", "options.music_frequency.tooltip": "Changes how frequently music plays while in a game world.", "options.narrator": "<PERSON><PERSON><PERSON>", "options.narrator.all": "Na-<PERSON>k<PERSON><PERSON>", "options.narrator.chat": "Nkwupụta Nkata", "options.narrator.notavailable": "<PERSON><PERSON><PERSON>", "options.narrator.off": "OFF", "options.narrator.system": "Sistemụ Nkọwa", "options.notifications.display_time": "Notification Time", "options.notifications.display_time.tooltip": "Affects the length of time that all notifications stay visible on the screen.", "options.off": "Gbanyụọ", "options.off.composed": "%s: g<PERSON><PERSON><PERSON><PERSON>", "options.on": "NA", "options.on.composed": "%s: <PERSON><PERSON><PERSON><PERSON>", "options.online": "Ọnlaịnụ...", "options.online.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.onlyShowSecureChat": "<PERSON><PERSON><PERSON> nkata e<PERSON>wara", "options.onlyShowSecureChat.tooltip": "<PERSON><PERSON><PERSON> gosipụta ozi sitere n'aka ndị egwuregwu ndị ọzọ enwere ike <PERSON>ta na onye ọkpụkpọ ahụ zitere ya, ma agbanwebegh<PERSON> ya.", "options.operatorItemsTab": "Operator Items Tab", "options.particles": "<PERSON><PERSON>", "options.particles.all": "All", "options.particles.decreased": "Agbadala", "options.particles.minimal": "Nke pere mpe", "options.percent_add_value": "%s: +%s%%", "options.percent_value": "%s: %s%%", "options.pixel_value": "%s: %spx", "options.prioritizeChunkUpdates": "Chunk Nrụpụ<PERSON>", "options.prioritizeChunkUpdates.byPlayer": "Ọkara mgbochi", "options.prioritizeChunkUpdates.byPlayer.tooltip": "Ụfọdụ omume n'ime mkpọ ga-achịkọtakwa mkpọ ahụ ozugbo. Nke a gụnyere ngọngọ itinye & ibibi.", "options.prioritizeChunkUpdates.nearby": "Mgbo<PERSON> zuru oke", "options.prioritizeChunkUpdates.nearby.tooltip": "A na-achịkọta iberibe ndị dị nso ozugbo. Nke a nwere ike imetụta arụ<PERSON><PERSON><PERSON><PERSON> egwuregwu mgbe etinyere ma ọ bụ bibie ihe mgbochi.", "options.prioritizeChunkUpdates.none": "<PERSON><PERSON><PERSON>", "options.prioritizeChunkUpdates.none.tooltip": "A na-achịkọta iberibe ndị dị nso na eri yiri ya. Nke a nwere ike ibute oghere anya dị nkenke mgbe a na-ebibi ihe mgbochi.", "options.rawMouseInput": "Ntinye Raw", "options.realmsNotifications": "Realms News & Invites", "options.realmsNotifications.tooltip": "Fetches Realms news and invites in the title screen and displays their respective icon on the Realms button.", "options.reducedDebugInfo": "<PERSON><PERSON>", "options.renderClouds": "<PERSON><PERSON><PERSON> o<PERSON>i", "options.renderCloudsDistance": "Cloud Distance", "options.renderDistance": "<PERSON>", "options.resourcepack": "Akwa ngwongwo...", "options.rotateWithMinecart": "Rotate with Minecarts", "options.rotateWithMinecart.tooltip": "Whether the player's view should rotate with a turning Minecart. Only available in worlds with the 'Minecart Improvements' experimental setting turned on.", "options.screenEffectScale": "<PERSON><PERSON><PERSON><PERSON>", "options.screenEffectScale.tooltip": "<PERSON>ke nke ọgbụgbọ na Nether portal na-emebi nsogbu.\nNa ụkpụrụ ndị dị ala, a na-eji akwụkwọ ndụ akwụkwọ ndụ dochie mmetụta ọgbụgbọ ahụ.", "options.sensitivity": "<PERSON><PERSON>", "options.sensitivity.max": "HYPERSPEED !!!", "options.sensitivity.min": "*yawn*", "options.showNowPlayingToast": "Show Music Toast", "options.showNowPlayingToast.tooltip": "Displays a toast whenever a song starts playing. The same toast is constantly displayed in the in-game pause menu while a song is playing.", "options.showSubtitles": "<PERSON><PERSON>", "options.simulationDistance": "Ogologo ihe ngosi", "options.skinCustomisation": "<PERSON><PERSON><PERSON> ahụ...", "options.skinCustomisation.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options.sounds": "<PERSON><PERSON>wu & ...da...", "options.sounds.title": "Nhọrọ Music & ụda", "options.telemetry": "Telemetry Data...", "options.telemetry.button": "Data Collection", "options.telemetry.button.tooltip": "\"%s\" includes only the required data.\n\"%s\" includes optional, as well as the required data.", "options.telemetry.disabled": "<PERSON><PERSON><PERSON><PERSON> is disabled.", "options.telemetry.state.all": "All", "options.telemetry.state.minimal": "Minimal", "options.telemetry.state.none": "None", "options.title": "Nhọrọ", "options.touchscreen": "Ọnọdụ ihuenyo mmetụ", "options.video": "Ntọala vidiyo...", "options.videoTitle": "Ntọala vidiyo", "options.viewBobbing": "<PERSON><PERSON><PERSON><PERSON>", "options.visible": "Egosiputa", "options.vsync": "Nhọrọ. vsync", "outOfMemory.message": "Minecraft agwụla ebe nchekwa.\n\nEnwere ike bute nke a site na ahụhụ dị na egwuregwu ahụ ma ọ bụ site na igwe Java Virtual Machine ana-enyeghị ebe nchekwa zuru oke.\n\n<PERSON><PERSON><PERSON><PERSON><PERSON> aka d<PERSON> lar<PERSON>, egwuregwu dị ugbu a akwụsịla. Anyị agbalịrị ịtọhapụ ebe nchekwa zuru oke ka ị laghachi na isi menu wee laghachi na egwu, mana nke a nwere ike ọ gaghị arụ ọrụ.\n\n<PERSON><PERSON> malitegharịa egwuregwu ahụ ma ọ bụrụ na ị hụ ozi a ọzọ.", "outOfMemory.title": "Onweghizi ebe nchekwa!", "pack.available.title": "Dị", "pack.copyFailure": "<PERSON><PERSON> ng<PERSON> aga<PERSON>", "pack.dropConfirm": "Wantchọr<PERSON> mkpọ ndị a na Minecraft?", "pack.dropInfo": "Dọrọ na dobe faịlụ na windo a iji tinye ngwugwu", "pack.dropRejected.message": "The following entries were not valid packs and were not copied:\n %s", "pack.dropRejected.title": "Non-pack entries", "pack.folderInfo": "(Ebe faịlụ mkpọ ebe a)", "pack.incompatible": "Kwekọ ekwekọ", "pack.incompatible.confirm.new": "<PERSON><PERSON><PERSON> ngwungwu a maka ụdị nke Minecraft ọhụụ ma ọ nwere ike ọ gaghị arụ ọrụ <PERSON>.", "pack.incompatible.confirm.old": "Emere mkpọ a maka ụdị Minecraft ochie ma ghara ịrụ ọ<PERSON>.", "pack.incompatible.confirm.title": "O doro gị anya na ị chọrọ iburu mkpọ a?", "pack.incompatible.new": "(<PERSON><PERSON> ma<PERSON> Minecraft ọ<PERSON>ụ<PERSON>ụ)", "pack.incompatible.old": "(<PERSON><PERSON> ma<PERSON> ochie nke <PERSON>)", "pack.nameAndSource": "%s (%s)", "pack.openFolder": "<PERSON><PERSON><PERSON>a nchekwa", "pack.selected.title": "<PERSON><PERSON><PERSON><PERSON>", "pack.source.builtin": "wuru n<PERSON>me", "pack.source.feature": "feature", "pack.source.local": "otù ebe", "pack.source.server": "nj<PERSON><PERSON><PERSON>", "pack.source.world": "world", "painting.dimensions": "%sx%s", "painting.minecraft.alban.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.alban.title": "Albanian", "painting.minecraft.aztec.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec.title": "de_aztec", "painting.minecraft.aztec2.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.aztec2.title": "de_aztec", "painting.minecraft.backyard.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.backyard.title": "Backyard", "painting.minecraft.baroque.author": "<PERSON>", "painting.minecraft.baroque.title": "Baroque", "painting.minecraft.bomb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bomb.title": "Target Successfully Bombed", "painting.minecraft.bouquet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bouquet.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.burning_skull.title": "Skull On Fire", "painting.minecraft.bust.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.bust.title": "Bust", "painting.minecraft.cavebird.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cavebird.title": "Cavebird", "painting.minecraft.changing.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.changing.title": "Changing", "painting.minecraft.cotan.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.cotan.title": "Cotán", "painting.minecraft.courbet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.courbet.title": "Bon<PERSON>r <PERSON>", "painting.minecraft.creebet.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.creebet.title": "Creebet", "painting.minecraft.dennis.author": "<PERSON>", "painting.minecraft.dennis.title": "<PERSON>", "painting.minecraft.donkey_kong.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.donkey_kong.title": "Kong", "painting.minecraft.earth.author": "Mojan<PERSON>", "painting.minecraft.earth.title": "Earth", "painting.minecraft.endboss.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.endboss.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fern.title": "Fern", "painting.minecraft.fighters.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.fighters.title": "Fighters", "painting.minecraft.finding.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.finding.title": "Finding", "painting.minecraft.fire.author": "Mojan<PERSON>", "painting.minecraft.fire.title": "Fire", "painting.minecraft.graham.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.graham.title": "<PERSON>", "painting.minecraft.humble.author": "<PERSON>", "painting.minecraft.humble.title": "<PERSON>mble", "painting.minecraft.kebab.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.kebab.title": "Kebab med tre pepperoni", "painting.minecraft.lowmist.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.lowmist.title": "Lowmist", "painting.minecraft.match.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.match.title": "Match", "painting.minecraft.meditative.author": "<PERSON>", "painting.minecraft.meditative.title": "Meditative", "painting.minecraft.orb.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.orb.title": "<PERSON><PERSON>", "painting.minecraft.owlemons.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.owlemons.title": "<PERSON>wl<PERSON>s", "painting.minecraft.passage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.passage.title": "Passage", "painting.minecraft.pigscene.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pigscene.title": "Pigscene", "painting.minecraft.plant.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.plant.title": "Paradisträd", "painting.minecraft.pointer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pointer.title": "Pointer", "painting.minecraft.pond.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pond.title": "Pond", "painting.minecraft.pool.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.pool.title": "The Pool", "painting.minecraft.prairie_ride.author": "<PERSON>", "painting.minecraft.prairie_ride.title": "Prairie Ride", "painting.minecraft.sea.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sea.title": "Seaside", "painting.minecraft.skeleton.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skeleton.title": "Mortal Coil", "painting.minecraft.skull_and_roses.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.skull_and_roses.title": "Skull and Roses", "painting.minecraft.stage.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.stage.title": "The Stage Is Set", "painting.minecraft.sunflowers.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunflowers.title": "Sunflowers", "painting.minecraft.sunset.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.sunset.title": "sunset_dense", "painting.minecraft.tides.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.tides.title": "Tides", "painting.minecraft.unpacked.author": "<PERSON>", "painting.minecraft.unpacked.title": "Unpacked", "painting.minecraft.void.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.void.title": "The void", "painting.minecraft.wanderer.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wanderer.title": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.author": "<PERSON><PERSON><PERSON>", "painting.minecraft.wasteland.title": "Wasteland", "painting.minecraft.water.author": "Mojan<PERSON>", "painting.minecraft.water.title": "Water", "painting.minecraft.wind.author": "Mojan<PERSON>", "painting.minecraft.wind.title": "Wind", "painting.minecraft.wither.author": "Mojan<PERSON>", "painting.minecraft.wither.title": "<PERSON>er", "painting.random": "Random variant", "parsing.bool.expected": "Boolean at<PERSON><PERSON><PERSON>a", "parsing.bool.invalid": "Boolean abaghị uru, at<PERSON><PERSON><PERSON> anya 'e<PERSON>kwu' ma <PERSON> bụ 'asị' mana ahụrụ ' %s'", "parsing.double.expected": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>a", "parsing.double.invalid": "Ab<PERSON><PERSON> abaghị uru' %s'", "parsing.expected": "<PERSON><PERSON><PERSON><PERSON> anya' %s'", "parsing.float.expected": "<PERSON><PERSON> n'elu at<PERSON><PERSON><PERSON>a", "parsing.float.invalid": "Ise elu abaghi uru' %s'", "parsing.int.expected": "Ogbe <PERSON> at<PERSON><PERSON><PERSON>a", "parsing.int.invalid": "Ogbe ọnụ<PERSON> agaghị uru' %s'", "parsing.long.expected": "<PERSON><PERSON><PERSON> at<PERSON><PERSON><PERSON>a", "parsing.long.invalid": "Ogologo agaghị uru' %s'", "parsing.quote.escape": "Usoro mgbapu abaghị uru'\\%s' na mkpụr<PERSON> okwu eserese", "parsing.quote.expected.end": "Mk<PERSON><PERSON><PERSON><PERSON> okwu eserese emechighi emechi", "parsing.quote.expected.start": "<PERSON><PERSON><PERSON> atụrụ anya iji bido mkpụ<PERSON><PERSON> okw", "particle.invalidOptions": "Can't parse particle options: %s", "particle.notFound": "<PERSON><PERSON><PERSON><PERSON> amaghị ama: %s", "permissions.requires.entity": "<PERSON><PERSON><PERSON><PERSON><PERSON> otu ga etiny iwu ndị a ébé a", "permissions.requires.player": "A chọrọ onye egwuruegwu ka ọ tinye iwu ndị ebe", "potion.potency.1": "<PERSON><PERSON><PERSON>", "potion.potency.2": "Atọ", "potion.potency.3": "Anọ", "potion.potency.4": "<PERSON><PERSON>", "potion.potency.5": "<PERSON><PERSON>", "potion.whenDrank": "Mgbe etinyere:", "potion.withAmplifier": "%s %s", "potion.withDuration": "%s (%s)", "predicate.unknown": "<PERSON><PERSON><PERSON> ama: %s", "quickplay.error.invalid_identifier": "Could not find world with the provided identifier", "quickplay.error.realm_connect": "Could not connect to Realm", "quickplay.error.realm_permission": "Lacking permission to connect to this Realm", "quickplay.error.title": "Failed to Quick Play", "realms.configuration.region.australia_east": "New South Wales, Australia", "realms.configuration.region.australia_southeast": "Victoria, Australia", "realms.configuration.region.brazil_south": "Brazil", "realms.configuration.region.central_india": "India", "realms.configuration.region.central_us": "Iowa, USA", "realms.configuration.region.east_asia": "Hong Kong", "realms.configuration.region.east_us": "Virginia, USA", "realms.configuration.region.east_us_2": "North Carolina, USA", "realms.configuration.region.france_central": "France", "realms.configuration.region.japan_east": "Eastern Japan", "realms.configuration.region.japan_west": "Western Japan", "realms.configuration.region.korea_central": "South Korea", "realms.configuration.region.north_central_us": "Illinois, USA", "realms.configuration.region.north_europe": "Ireland", "realms.configuration.region.south_central_us": "Texas, USA", "realms.configuration.region.southeast_asia": "Singapore", "realms.configuration.region.sweden_central": "Sweden", "realms.configuration.region.uae_north": "United Arab Emirates (UAE)", "realms.configuration.region.uk_south": "Southern England", "realms.configuration.region.west_central_us": "Utah, USA", "realms.configuration.region.west_europe": "Netherlands", "realms.configuration.region.west_us": "California, USA", "realms.configuration.region.west_us_2": "Washington, USA", "realms.configuration.region_preference.automatic_owner": "Automatic (Realm owner ping)", "realms.configuration.region_preference.automatic_player": "Automatic (first to join session)", "realms.missing.snapshot.error.text": "<PERSON><PERSON><PERSON><PERSON> alaeze ikike na snapshot", "recipe.notFound": "<PERSON>wa nri amaghị ama: %s", "recipe.toast.description": "Tulee akwụk<PERSON> nhazi gi", "recipe.toast.title": "<PERSON><PERSON> nhazi a gbaghere!", "record.nowPlaying": "<PERSON><PERSON><PERSON> a na-egwu egwu: %s", "recover_world.bug_tracker": "Report a Bug", "recover_world.button": "Attempt to Recover", "recover_world.done.failed": "Failed to recover from previous state.", "recover_world.done.success": "Recovery was successful!", "recover_world.done.title": "Recovery done", "recover_world.issue.missing_file": "Missing file", "recover_world.issue.none": "No issues", "recover_world.message": "The following issues occurred while trying to read world folder \"%s\".\nIt might be possible to restore the world from an older state or you can report this issue on the bug tracker.", "recover_world.no_fallback": "No state to recover from available", "recover_world.restore": "Attempt to Restore", "recover_world.restoring": "Attempting to restore world...", "recover_world.state_entry": "State from %s: ", "recover_world.state_entry.unknown": "unknown", "recover_world.title": "Failed to load world", "recover_world.warning": "Failed to load world summary", "resourcePack.broken_assets": "ATA BROKEN ATA", "resourcePack.high_contrast.name": "High Contrast", "resourcePack.load_fail": "<PERSON><PERSON><PERSON><PERSON> nweta ihe dara ada", "resourcePack.programmer_art.name": "Programmer Art", "resourcePack.runtime_failure": "Resource pack error detected", "resourcePack.server.name": "<PERSON><PERSON> o<PERSON>a nke", "resourcePack.title": "<PERSON><PERSON><PERSON><PERSON> ngwugwu a na-akpọ Resource", "resourcePack.vanilla.description": "The default look and feel of Minecraft", "resourcePack.vanilla.name": "<PERSON><PERSON><PERSON>", "resourcepack.downloading": "Na nbudata ngwungwu a<PERSON>", "resourcepack.progress": "Na -ebudata faịlụ (%s MB)...", "resourcepack.requesting": "Na -arịọ...", "screenshot.failure": "<PERSON><PERSON><PERSON><PERSON> ike <PERSON> n<PERSON>a i<PERSON>enyo: %s", "screenshot.success": "Ichekwa ihuenyo dị ka %s", "selectServer.add": "<PERSON>e sava", "selectServer.defaultName": "<PERSON><PERSON> nkesa Minecraft", "selectServer.delete": "Delete", "selectServer.deleteButton": "Delete", "selectServer.deleteQuestion": "Ì ji n'aka na ịchọrọ iwepụ sava a?", "selectServer.deleteWarning": "'%s' will be lost forever! (A long time!)", "selectServer.direct": "Njikọ Dịrị", "selectServer.edit": "Edit", "selectServer.hiddenAddress": "(<PERSON><PERSON>)", "selectServer.refresh": "Nwee ume", "selectServer.select": "Jikọọ sava", "selectWorld.access_failure": "<PERSON><PERSON><PERSON><PERSON> ike inweta uwa", "selectWorld.allowCommands": "<PERSON><PERSON><PERSON>", "selectWorld.allowCommands.info": "Iwu dika  /gamemode, /experience", "selectWorld.allowCommands.new": "Allow Commands", "selectWorld.backupEraseCache": "Hichapụ data echekwara", "selectWorld.backupJoinConfirmButton": "\nMepụta ndabere na ibu", "selectWorld.backupJoinSkipButton": "<PERSON>aara m ihe m na-eme!", "selectWorld.backupQuestion.customized": "<PERSON><PERSON><PERSON><PERSON> akwado nd<PERSON> ahaziri", "selectWorld.backupQuestion.downgrade": "<PERSON><PERSON><PERSON> <PERSON><PERSON> adighi a<PERSON>do", "selectWorld.backupQuestion.experimental": "A kwadobeghị swa ndị eji Ntọala Nlele", "selectWorld.backupQuestion.snapshot": "Do you really want to load this world?", "selectWorld.backupWarning.customized": "O bu ihe nwute, anyi adighi akwado uwa ndi ozo na version nke Minecraft. Anyị ka nwere ike ibu ụwa a ma debe ihe niile dịka ọ dị, mana ebe a ga-emezigharị agaghị emegharị. Anyị rịọ mgbaghara maka nsogbu ahụ!", "selectWorld.backupWarning.downgrade": "Oge ikpeazụ akpọrọ uwaa bu na agba %s; ị nọ agba %s. I kelu uwa anya nwere ike ibute nruru aka. Ọ bụrụ na ị ka chọrọ iga n'ihu, biko mee ndabere!", "selectWorld.backupWarning.experimental": "Ụwa a na-eji ntọala nnwale nke nwere ike ịkwụsị ịrụ ọrụ n'oge ọ bụla. Anyị enweghị ike isi n’aka na ọ ga-ebu ma ọ bụ rụọ ọrụ. Lee dragons!", "selectWorld.backupWarning.snapshot": "<PERSON>gwu<PERSON>wa a ikpeazụ na ụdị %s; ị nọ na ụdị %s. <PERSON><PERSON> mee ndabere ma <PERSON> bụrụ na ị na-enweta nrụrụ aka n'ụwa!", "selectWorld.bonusItems": "<PERSON><PERSON><PERSON>", "selectWorld.cheats": "<PERSON><PERSON><PERSON>", "selectWorld.commands": "Commands", "selectWorld.conversion": "Ga-tọghatara!", "selectWorld.conversion.tooltip": "Ọ dị mkpa ka emepe <PERSON>wa a n'<PERSON><PERSON><PERSON> o<PERSON>e (dịka 1.6.4) ka a sụgharịa ya nke <PERSON>ma", "selectWorld.create": "<PERSON><PERSON><PERSON><PERSON> Worldwa Ọhụrụ", "selectWorld.customizeType": "Mee nke onwe", "selectWorld.dataPacks": "Ngwugwu data", "selectWorld.data_read": "\nNa-agụ data ụwa.", "selectWorld.delete": "Delete it", "selectWorld.deleteButton": "Delete", "selectWorld.deleteQuestion": "<PERSON><PERSON> n'aka na I chọrọ iwepu uwaa?", "selectWorld.deleteWarning": "Ụwa \"%s\" ga-efu ruo mgbe ebighị ebi! (ọ bụ ogologo oge!)", "selectWorld.delete_failure": "<PERSON><PERSON><PERSON><PERSON> uwa", "selectWorld.edit": "<PERSON><PERSON> ya", "selectWorld.edit.backup": "<PERSON><PERSON> n<PERSON>bere", "selectWorld.edit.backupCreated": "Ejiri elu: %s", "selectWorld.edit.backupFailed": "<PERSON><PERSON>bere a<PERSON>ra", "selectWorld.edit.backupFolder": "<PERSON><PERSON><PERSON> folda ndabere", "selectWorld.edit.backupSize": "size: %s MB", "selectWorld.edit.export_worldgen_settings": "Mbupụ Ntọala Ọgbọ Uwa", "selectWorld.edit.export_worldgen_settings.failure": "Mmbup<PERSON>", "selectWorld.edit.export_worldgen_settings.success": "ebupụ", "selectWorld.edit.openFolder": "Me<PERSON>e nchekwa Ụwa", "selectWorld.edit.optimize": "Optimize World", "selectWorld.edit.resetIcon": "<PERSON><PERSON><PERSON><PERSON><PERSON> akara ngosi", "selectWorld.edit.save": "Chekwaa", "selectWorld.edit.title": "<PERSON><PERSON>", "selectWorld.enterName": "<PERSON><PERSON> uwa", "selectWorld.enterSeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> maka <PERSON>wa Ihe mmụta uwa", "selectWorld.experimental": "Experimental", "selectWorld.experimental.details": "Details", "selectWorld.experimental.details.entry": "Required experimental features: %s", "selectWorld.experimental.details.title": "Experimental Feature Requirements", "selectWorld.experimental.message": "Be careful!\nThis configuration requires features that are still under development. Your world might crash, break, or not work with future updates.", "selectWorld.experimental.title": "Experimental Features Warning", "selectWorld.experiments": "Experiments", "selectWorld.experiments.info": "Experiments are potential new features. Be careful as things might break. Experiments can't be turned off after world creation.", "selectWorld.futureworld.error.text": "Ọ nwere ihe mebiri emebi mgbe ị na-achọ ịkwanye ụwa site n'ụdị ọdịnihu. Nke a bụ ọrụ dị ize ndụ iji bido; ndo ya anaghị arụ ọrụ.", "selectWorld.futureworld.error.title": "\nN<PERSON><PERSON>e mere!", "selectWorld.gameMode": "Ọnọdụ egwuregwu", "selectWorld.gameMode.adventure": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.adventure.info": "Same as Survival Mode, but blocks can't be added or removed.", "selectWorld.gameMode.adventure.line1": "<PERSON><PERSON> ka Ọnọdụ ndihu, mana ihe mgbochi enwegh<PERSON> ike", "selectWorld.gameMode.adventure.line2": "tinye ma ọ bụ wepu ya", "selectWorld.gameMode.creative": "Nkeputa", "selectWorld.gameMode.creative.info": "Create, build, and explore without limits. You can fly, have endless materials, and can't be hurt by monsters.", "selectWorld.gameMode.creative.line1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> enweghi ng<PERSON>, ife efe n'efu na", "selectWorld.gameMode.creative.line2": "bibie ngongo <PERSON>bo", "selectWorld.gameMode.hardcore": "Okponku", "selectWorld.gameMode.hardcore.info": "Survival Mode locked to 'Hard' difficulty. You can't respawn if you die.", "selectWorld.gameMode.hardcore.line1": "<PERSON><PERSON> ka Ọnọdụ ndihu, k<PERSON><PERSON><PERSON><PERSON> ebe siri ike", "selectWorld.gameMode.hardcore.line2": "ihe isi ike, na ofu ndu naanị", "selectWorld.gameMode.spectator": "Spectator", "selectWorld.gameMode.spectator.info": "You can look but don't touch.", "selectWorld.gameMode.spectator.line1": "I nwere ike ile anya mana anaghị emetụ aka", "selectWorld.gameMode.survival": "<PERSON><PERSON><PERSON>", "selectWorld.gameMode.survival.info": "Explore a mysterious world where you build, collect, craft, and fight monsters.", "selectWorld.gameMode.survival.line1": "Chọọ maka akụ<PERSON>, nka, uru", "selectWorld.gameMode.survival.line2": "<PERSON><PERSON><PERSON>, ah<PERSON><PERSON> na agụụ", "selectWorld.gameRules": "<PERSON><PERSON>", "selectWorld.import_worldgen_settings": "Nbudata ntọala", "selectWorld.import_worldgen_settings.failure": "Mperi na mbubata ntọala", "selectWorld.import_worldgen_settings.select_file": "<PERSON><PERSON>r<PERSON> faị<PERSON>ụ n<PERSON> (.json)", "selectWorld.incompatible.description": "This world cannot be opened in this version.\nIt was last played in version %s.", "selectWorld.incompatible.info": "Incompatible version: %s", "selectWorld.incompatible.title": "Incompatible version", "selectWorld.incompatible.tooltip": "This world cannot be opened because it was created by an incompatible version.", "selectWorld.incompatible_series": "Ụdị na-ekwekọghị ekwekọ mepụtara", "selectWorld.load_folder_access": "Enweghi ike igu ogu ma obu nweta folda ebe echekwara uwa di iche iche!", "selectWorld.loading_list": "Loading ndep<PERSON><PERSON>", "selectWorld.locked": "<PERSON>he nlere nke ozo nke Minecraft kpochiri", "selectWorld.mapFeatures": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> mwube", "selectWorld.mapFeatures.info": "Villages, Shipwrecks, etc.", "selectWorld.mapType": "Ụdị <PERSON>wa", "selectWorld.mapType.normal": "Nkịtị", "selectWorld.moreWorldOptions": "<PERSON><PERSON> nh<PERSON> uwa ndị ọzọ...", "selectWorld.newWorld": "Ụwa <PERSON><PERSON><PERSON><PERSON>ụ", "selectWorld.recreate": "Kegharia", "selectWorld.recreate.customized.text": "Akwụsịghị ịhazi <PERSON>wa niile na nke a nke Minecraft. Anyị nwere ike ịnwa ịmepụta ya na otu ọka na akụr<PERSON><PERSON><PERSON> ah<PERSON>, mana nhazi <PERSON> bụla nke ala ọ bụla ga-efu. Anyị rịọ mgbaghara maka nsogbu ahụ!", "selectWorld.recreate.customized.title": "Customized worlds are no longer supported", "selectWorld.recreate.error.text": "Ihe mere ihe na-ezighi ezi mgbe o na-anwa ichota uwa.", "selectWorld.recreate.error.title": "An error occurred!", "selectWorld.resource_load": "Preparing Resources...", "selectWorld.resultFolder": "Ga-echekwa na:", "selectWorld.search": "ch<PERSON><PERSON>", "selectWorld.seedInfo": "Hapu Nkịtị maka nkpuru aghara aghara", "selectWorld.select": "Kpọọ <PERSON> ah<PERSON>", "selectWorld.targetFolder": "Save folder: %s", "selectWorld.title": "<PERSON><PERSON><PERSON><PERSON>", "selectWorld.tooltip.fromNewerVersion1": "E chebere ụwa n'ụ<PERSON>,", "selectWorld.tooltip.fromNewerVersion2": "ịkpọ ụwa a nwere ike ibute nsogbu!", "selectWorld.tooltip.snapshot1": "\nEchefula ịk<PERSON>do ụwa a", "selectWorld.tooltip.snapshot2": "tupu i bido ya na foto a.", "selectWorld.unable_to_load": "<PERSON><PERSON><PERSON><PERSON> ike ibu uwa nile", "selectWorld.version": "Agba:", "selectWorld.versionJoinButton": "Kponye ka osiladi", "selectWorld.versionQuestion": "Really choro Ibu uwa a?", "selectWorld.versionUnknown": "unknown", "selectWorld.versionWarning": "Egwu a nke i<PERSON>azụ a na-ebi na %s ma na-ebu ya na mbipute a nwere ike <PERSON> nrụ<PERSON>ụ aka!", "selectWorld.warning.deprecated.question": "Some features used are deprecated and will stop working in the future. Do you wish to proceed?", "selectWorld.warning.deprecated.title": "Warning! These settings are using deprecated features", "selectWorld.warning.experimental.question": "These settings are experimental and could one day stop working. Do you wish to proceed?", "selectWorld.warning.experimental.title": "Warning! These settings are using experimental features", "selectWorld.warning.lowDiskSpace.description": "There is not much space left on your device.\nRunning out of disk space while in game can lead to your world being damaged.", "selectWorld.warning.lowDiskSpace.title": "Warning! Low disk space!", "selectWorld.world": "Ụwa", "sign.edit": "<PERSON><PERSON>", "sleep.not_possible": "Ọ nweghị ezumike ọ bụla nwere ike <PERSON>fe n'abalị a", "sleep.players_sleeping": "%s/%s ndị na -ere <PERSON>ra", "sleep.skipping_night": "Na -eh<PERSON> n'ime a<PERSON> a", "slot.only_single_allowed": "Only single slots allowed, got '%s'", "slot.unknown": "<PERSON><PERSON><PERSON> amaghị ama'%s'", "snbt.parser.empty_key": "Key cannot be empty", "snbt.parser.expected_binary_numeral": "Expected a binary number", "snbt.parser.expected_decimal_numeral": "Expected a decimal number", "snbt.parser.expected_float_type": "Expected a floating point number", "snbt.parser.expected_hex_escape": "Expected a character literal of length %s", "snbt.parser.expected_hex_numeral": "Expected a hexadecimal number", "snbt.parser.expected_integer_type": "Expected an integer number", "snbt.parser.expected_non_negative_number": "Expected a non-negative number", "snbt.parser.expected_number_or_boolean": "Expected a number or a boolean", "snbt.parser.expected_string_uuid": "Expected a string representing a valid UUID", "snbt.parser.expected_unquoted_string": "Expected a valid unquoted string", "snbt.parser.infinity_not_allowed": "Non-finite numbers are not allowed", "snbt.parser.invalid_array_element_type": "Invalid array element type", "snbt.parser.invalid_character_name": "Invalid Unicode character name", "snbt.parser.invalid_codepoint": "Invalid Unicode character value: %s", "snbt.parser.invalid_string_contents": "Invalid string contents", "snbt.parser.invalid_unquoted_start": "Unquoted strings can't start with digits 0-9, + or -", "snbt.parser.leading_zero_not_allowed": "Decimal numbers can't start with 0", "snbt.parser.no_such_operation": "No such operation: %s", "snbt.parser.number_parse_failure": "Failed to parse number: %s", "snbt.parser.undescore_not_allowed": "Underscore characters are not allowed at the start or end of a number", "soundCategory.ambient": "Ambient/gburugburu", "soundCategory.block": "<PERSON><PERSON>", "soundCategory.hostile": "<PERSON><PERSON> okike iro", "soundCategory.master": "Onye isi olu", "soundCategory.music": "<PERSON><PERSON><PERSON>", "soundCategory.neutral": "<PERSON><PERSON>he En<PERSON> na Enyi", "soundCategory.player": "Nd<PERSON> na-egwu eg<PERSON>re<PERSON>wu", "soundCategory.record": "Jukebox / Blocks Blocks", "soundCategory.ui": "UI", "soundCategory.voice": "Olu/Okwu", "soundCategory.weather": ".<PERSON><PERSON><PERSON><PERSON> mmiri", "spectatorMenu.close": "<PERSON><PERSON><PERSON> ihe nguputa", "spectatorMenu.next_page": "Next Page", "spectatorMenu.previous_page": "Previous Page", "spectatorMenu.root.prompt": "<PERSON><PERSON><PERSON> <PERSON> i<PERSON> h<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> iji ya.", "spectatorMenu.team_teleport": "Wubaa na onye otu", "spectatorMenu.team_teleport.prompt": "<PERSON><PERSON><PERSON><PERSON> otu I ga awuba", "spectatorMenu.teleport": "<PERSON><PERSON><PERSON> ye egwuregwu", "spectatorMenu.teleport.prompt": "Họrọ Onye egwuregwu i ga awuba na nke ya", "stat.generalButton": "Izugbe", "stat.itemsButton": "<PERSON>he", "stat.minecraft.animals_bred": "Redm<PERSON> anụ<PERSON>ụ na-eri nri", "stat.minecraft.aviate_one_cm": "Anya site Elytra", "stat.minecraft.bell_ring": "<PERSON><PERSON> mkpu", "stat.minecraft.boat_one_cm": "Ogologo ụgbọ mmiri", "stat.minecraft.clean_armor": "Ekike Mpekere k<PERSON>ra", "stat.minecraft.clean_banner": "Ọkọlọtọ kpochara", "stat.minecraft.clean_shulker_box": "A kpochara Igbe Onyekachi", "stat.minecraft.climb_one_cm": "Ogologo ị<PERSON>ị<PERSON>", "stat.minecraft.crouch_one_cm": "<PERSON>", "stat.minecraft.damage_absorbed": "<PERSON><PERSON><PERSON>", "stat.minecraft.damage_blocked_by_shield": "Mmebi ah<PERSON> emere ya na mg<PERSON>chi", "stat.minecraft.damage_dealt": "<PERSON><PERSON><PERSON> mebiri", "stat.minecraft.damage_dealt_absorbed": "<PERSON><PERSON><PERSON> emebi (emebiwo)", "stat.minecraft.damage_dealt_resisted": "<PERSON><PERSON><PERSON> (eguzogide)", "stat.minecraft.damage_resisted": "<PERSON><PERSON><PERSON> emebi", "stat.minecraft.damage_taken": "<PERSON><PERSON><PERSON>", "stat.minecraft.deaths": "Ọnụọgụ nke Ọnwụ", "stat.minecraft.drop": "<PERSON><PERSON>", "stat.minecraft.eat_cake_slice": "<PERSON><PERSON><PERSON>", "stat.minecraft.enchant_item": "<PERSON><PERSON>", "stat.minecraft.fall_one_cm": "Ole a dara", "stat.minecraft.fill_cauldron": "Ite a gbajuru", "stat.minecraft.fish_caught": "<PERSON><PERSON> e jidere", "stat.minecraft.fly_one_cm": "Ole e fere", "stat.minecraft.happy_ghast_one_cm": "Distance by <PERSON>", "stat.minecraft.horse_one_cm": "Ole inyinya gara", "stat.minecraft.inspect_dispenser": "Akpa e nyoro", "stat.minecraft.inspect_dropper": "Ntusa e nyoro", "stat.minecraft.inspect_hopper": "<PERSON><PERSON> e nyoro", "stat.minecraft.interact_with_anvil": "Mmekorita ya na Anvil", "stat.minecraft.interact_with_beacon": "<PERSON><PERSON> nke ncha ọ<PERSON>", "stat.minecraft.interact_with_blast_furnace": "Nkari nke akwa ọkụ", "stat.minecraft.interact_with_brewingstand": "<PERSON><PERSON> nke ebe mmanya", "stat.minecraft.interact_with_campfire": "<PERSON><PERSON>", "stat.minecraft.interact_with_cartography_table": "<PERSON><PERSON> nke ogodo", "stat.minecraft.interact_with_crafting_table": "<PERSON><PERSON> nke ogodo nhazi", "stat.minecraft.interact_with_furnace": "<PERSON><PERSON> nke <PERSON>wu <PERSON>", "stat.minecraft.interact_with_grindstone": "Mmekorita ya na Grindstone", "stat.minecraft.interact_with_lectern": "<PERSON><PERSON> nke ogodo nkuzi", "stat.minecraft.interact_with_loom": "Nkari nke ndụ akwa", "stat.minecraft.interact_with_smithing_table": "Mmekorita ya na table Smithing", "stat.minecraft.interact_with_smoker": "<PERSON><PERSON> nke <PERSON>wu an<PERSON>", "stat.minecraft.interact_with_stonecutter": "<PERSON><PERSON> nke nkewa okwute", "stat.minecraft.jump": "<PERSON><PERSON><PERSON> elu", "stat.minecraft.leave_game": "Puta n’ama eg<PERSON><PERSON>wu", "stat.minecraft.minecart_one_cm": "Nkewa nke Minecart", "stat.minecraft.mob_kills": "Ogbugbu nke ototoo", "stat.minecraft.open_barrel": "Ite e meghere", "stat.minecraft.open_chest": "Akpati e meghere", "stat.minecraft.open_enderchest": "Akpati nke Ender e meghere", "stat.minecraft.open_shulker_box": "Akpati nke Shulker e meghere", "stat.minecraft.pig_one_cm": "Nkewa site n’ezi", "stat.minecraft.play_noteblock": "Okpụr<PERSON><PERSON> akwụkwo a gbara", "stat.minecraft.play_record": "<PERSON><PERSON><PERSON> a gbara", "stat.minecraft.play_time": "<PERSON><PERSON>", "stat.minecraft.player_kills": "Ndị e<PERSON>wu e gburu", "stat.minecraft.pot_flower": "Osisi a koro", "stat.minecraft.raid_trigger": "Ndagbo a e bidoro", "stat.minecraft.raid_win": "Ndagbo e meriri", "stat.minecraft.sleep_in_bed": "Ugboro ole a rahụrụ n’elu akwa", "stat.minecraft.sneak_time": "Oge a eji m<PERSON>", "stat.minecraft.sprint_one_cm": "<PERSON>be ole a gbaruru", "stat.minecraft.strider_one_cm": "Anya site Strider", "stat.minecraft.swim_one_cm": "<PERSON>be ole e gwururu", "stat.minecraft.talked_to_villager": "<PERSON><PERSON> ndi obodo", "stat.minecraft.target_hit": "<PERSON><PERSON>", "stat.minecraft.time_since_death": "Oge Kemgbe Ọnwụ Ọnwụ Ikpeazụ", "stat.minecraft.time_since_rest": "<PERSON><PERSON>be izu ike gara aga", "stat.minecraft.total_world_time": "<PERSON>ge na <PERSON>wa Openụwa", "stat.minecraft.traded_with_villager": "<PERSON>ia ya na ndị obodo", "stat.minecraft.trigger_trapped_chest": "Onya a kpakara", "stat.minecraft.tune_noteblock": "<PERSON><PERSON>", "stat.minecraft.use_cauldron": "M<PERSON>i e weere n’ite", "stat.minecraft.walk_on_water_one_cm": "Ebe ole a gara n’elu mmiri", "stat.minecraft.walk_one_cm": "<PERSON>be ole a gara", "stat.minecraft.walk_under_water_one_cm": "<PERSON>'okpuru <PERSON>", "stat.mobsButton": "ìgwè mmadụ", "stat_type.minecraft.broken": "<PERSON><PERSON> em<PERSON>", "stat_type.minecraft.crafted": "<PERSON><PERSON> deere", "stat_type.minecraft.dropped": "<PERSON><PERSON><PERSON>", "stat_type.minecraft.killed": "I gburu %s %s", "stat_type.minecraft.killed.none": "Gburu nwetụbeghị mmadụ %s", "stat_type.minecraft.killed_by": "%s gburu gị %s oge(oge)", "stat_type.minecraft.killed_by.none": "Ọ dịghị onye gburu gị %s", "stat_type.minecraft.mined": "<PERSON>ge <PERSON> a na-egwu<PERSON>ụ<PERSON>", "stat_type.minecraft.picked_up": "<PERSON>ụtụ<PERSON>", "stat_type.minecraft.used": "<PERSON><PERSON>", "stats.none": "-", "structure_block.button.detect_size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.button.load": "Ibu", "structure_block.button.save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.custom_data": "Aha njirimara data omenala", "structure_block.detect_size": "Detect Structure Size and Position:", "structure_block.hover.corner": "Nkuku: %s", "structure_block.hover.data": "Data: %s", "structure_block.hover.load": "Ibu: %s", "structure_block.hover.save": "Chekwaa: %s", "structure_block.include_entities": "Include Entities:", "structure_block.integrity": "Iguzosi ike n'ezi ihe na Mkpụrụ", "structure_block.integrity.integrity": "Iguzosi ike n'ezi ihe", "structure_block.integrity.seed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "structure_block.invalid_structure_name": "aha n<PERSON>rimara na ezighi ezi '%s'", "structure_block.load_not_found": "Nhazi '%s' adịgh<PERSON>", "structure_block.load_prepare": "Nhazi '%s' ọn<PERSON><PERSON><PERSON> k<PERSON>e", "structure_block.load_success": "<PERSON><PERSON> owuwu si '%s'", "structure_block.mode.corner": "Nkuku", "structure_block.mode.data": "Data", "structure_block.mode.load": "Ibu", "structure_block.mode.save": "Save", "structure_block.mode_info.corner": "Nkuku Ọnọdụ - Ntinye na Akara Akara", "structure_block.mode_info.data": "Ọnọdụ data - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> E<PERSON>wu <PERSON>wu", "structure_block.mode_info.load": "Rụ ọrụ mode - <PERSON>bu <PERSON>", "structure_block.mode_info.save": "Ngwongwo Kwalite", "structure_block.position": "<PERSON><PERSON><PERSON> Ọnọdụ", "structure_block.position.x": "ikwu Ọnọdụ x", "structure_block.position.y": "<PERSON><PERSON><PERSON> y", "structure_block.position.z": "<PERSON><PERSON><PERSON> <PERSON>", "structure_block.save_failure": "Enweghi ike <PERSON> usoro '%s'", "structure_block.save_success": "Nzọpụta e<PERSON>k<PERSON> dị ka '%s'", "structure_block.show_air": "<PERSON><PERSON>:", "structure_block.show_boundingbox": "Gosi Box Bounding:", "structure_block.size": "<PERSON><PERSON>", "structure_block.size.x": "Ọdịdị nhazi x", "structure_block.size.y": "Ọdịdị nha y", "structure_block.size.z": "Ọdịdị nha z", "structure_block.size_failure": "<PERSON><PERSON><PERSON><PERSON> ike <PERSON>ha n<PERSON>. <PERSON><PERSON> nkuku nwere aha dabara na aha ya", "structure_block.size_success": "<PERSON><PERSON>ọpụ<PERSON> nha nke ọma maka '%s'", "structure_block.strict": "Strict Placement:", "structure_block.structure_name": "<PERSON><PERSON>", "subtitles.ambient.cave": "<PERSON><PERSON><PERSON><PERSON><PERSON> egwu", "subtitles.ambient.sound": "Eerie noise", "subtitles.block.amethyst_block.chime": "Amethyst na -ekwu", "subtitles.block.amethyst_block.resonate": "Amethyst resonates", "subtitles.block.anvil.destroy": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.anvil.land": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.anvil.use": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.barrel.close": "<PERSON><PERSON><PERSON> gb<PERSON>mgb<PERSON>m", "subtitles.block.barrel.open": "E meghee Gbọmgbọm", "subtitles.block.beacon.activate": "Mgbama <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.block.beacon.ambient": "<PERSON> na -ama jijiji", "subtitles.block.beacon.deactivate": "Mgbama na -a<PERSON>banyụ", "subtitles.block.beacon.power_select": "A họrọ ike mg<PERSON>ma", "subtitles.block.beehive.drip": "<PERSON><PERSON><PERSON>", "subtitles.block.beehive.enter": "Bee abanye ekwo Ekwo", "subtitles.block.beehive.exit": "Bee na-ekwo ekwo ekwo", "subtitles.block.beehive.shear": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "subtitles.block.beehive.work": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.bell.resonate": "Bell na -agbagha", "subtitles.block.bell.use": "Ọla mgbịrịgba", "subtitles.block.big_dripleaf.tilt_down": "Dripleaf na -agbada", "subtitles.block.big_dripleaf.tilt_up": "Dripleaf na -a<PERSON>badata", "subtitles.block.blastfurnace.fire_crackle": "Blast Ọkụ crackles", "subtitles.block.brewing_stand.brew": "Ụfụfụ guzosiri ike", "subtitles.block.bubble_column.bubble_pop": "Afọ agbapụta", "subtitles.block.bubble_column.upwards_ambient": "Afọ na -asọ", "subtitles.block.bubble_column.upwards_inside": "Afọ woosh", "subtitles.block.bubble_column.whirlpool_ambient": "Afọ na -efegharị", "subtitles.block.bubble_column.whirlpool_inside": "<PERSON><PERSON><PERSON> m<PERSON>", "subtitles.block.button.click": "<PERSON><PERSON><PERSON>", "subtitles.block.cake.add_candle": "<PERSON><PERSON><PERSON> achicha", "subtitles.block.campfire.crackle": "Ọkụ ọgbụgba na -agbawa", "subtitles.block.candle.crackle": "Kand<PERSON>l <PERSON>", "subtitles.block.candle.extinguish": "Candle extinguishes", "subtitles.block.chest.close": "Igbe na -emechi", "subtitles.block.chest.locked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> obi", "subtitles.block.chest.open": "Igbe na -emepe", "subtitles.block.chorus_flower.death": "Ifuru Chorus na -akpọnwụ", "subtitles.block.chorus_flower.grow": "Ifuru Chorus na -eto", "subtitles.block.comparator.click": "Pịa na onye ntụnyere", "subtitles.block.composter.empty": "Composter tọgbọ chakoo", "subtitles.block.composter.fill": "Composter jup<PERSON>", "subtitles.block.composter.ready": "A na-akpọ Composter", "subtitles.block.conduit.activate": "Conduit <PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.block.conduit.ambient": "Ọkụ okirikiri", "subtitles.block.conduit.attack.target": "Mwakpo ime mmụọ", "subtitles.block.conduit.deactivate": "Conduit na -agbanyụ", "subtitles.block.copper_bulb.turn_off": "Copper Bulb turns off", "subtitles.block.copper_bulb.turn_on": "Copper Bulb turns on", "subtitles.block.copper_trapdoor.close": "Trapdoor closes", "subtitles.block.copper_trapdoor.open": "Trapdoor opens", "subtitles.block.crafter.craft": "Crafter crafts", "subtitles.block.crafter.fail": "Crafter fails crafting", "subtitles.block.creaking_heart.hurt": "Creaking Heart grumbles", "subtitles.block.creaking_heart.idle": "Eerie noise", "subtitles.block.creaking_heart.spawn": "Creaking Heart awakens", "subtitles.block.deadbush.idle": "Dry sounds", "subtitles.block.decorated_pot.insert": "Decorated Pot fills", "subtitles.block.decorated_pot.insert_fail": "Decorated Pot wobbles", "subtitles.block.decorated_pot.shatter": "Decorated Pot shatters", "subtitles.block.dispenser.dispense": "<PERSON><PERSON>", "subtitles.block.dispenser.fail": "<PERSON>ye nkesa ihe dara", "subtitles.block.door.toggle": "Ọnụ <PERSON>z<PERSON>", "subtitles.block.dried_ghast.ambient": "Sounds of dryness", "subtitles.block.dried_ghast.ambient_water": "<PERSON><PERSON> rehydrates", "subtitles.block.dried_ghast.place_in_water": "Dr<PERSON> soaks", "subtitles.block.dried_ghast.transition": "<PERSON><PERSON> feels better", "subtitles.block.dry_grass.ambient": "Windy sounds", "subtitles.block.enchantment_table.use": "<PERSON><PERSON><PERSON> enchanting", "subtitles.block.end_portal.spawn": "Portal ngwụcha ga -emepe", "subtitles.block.end_portal_frame.fill": "<PERSON> nke <PERSON>", "subtitles.block.eyeblossom.close": "Eyeblossom closes", "subtitles.block.eyeblossom.idle": "Eyeblossom whispers", "subtitles.block.eyeblossom.open": "Eyeblossom opens", "subtitles.block.fence_gate.toggle": "Ọnụ <PERSON><PERSON><PERSON> ngere", "subtitles.block.fire.ambient": "Ọkụ na -agbawa", "subtitles.block.fire.extinguish": "Ọkụ ny<PERSON>rụ", "subtitles.block.firefly_bush.idle": "Fireflies buzz", "subtitles.block.frogspawn.hatch": "Tadpole k<PERSON>ụtara", "subtitles.block.furnace.fire_crackle": "Ọkụ na -agbawa", "subtitles.block.generic.break": "Okwute e kewaala", "subtitles.block.generic.fall": "Something falls on a block", "subtitles.block.generic.footsteps": "<PERSON><PERSON>", "subtitles.block.generic.hit": "<PERSON><PERSON><PERSON>", "subtitles.block.generic.place": "Okwute e debere", "subtitles.block.grindstone.use": "<PERSON><PERSON>ji", "subtitles.block.growing_plant.crop": "Akpụchapụ<PERSON><PERSON>", "subtitles.block.hanging_sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.honey_block.slide": "Na-agbadata mkpọchi mmanụ a honeyụ", "subtitles.block.iron_trapdoor.close": "Mkpakana e mechie", "subtitles.block.iron_trapdoor.open": "Mkpakana e emeghee", "subtitles.block.lava.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.lava.extinguish": "<PERSON><PERSON><PERSON> al<PERSON> mee mkpọtụ", "subtitles.block.lever.click": "<PERSON><PERSON><PERSON> mee mkpọtụ", "subtitles.block.note_block.note": "<PERSON><PERSON><PERSON> egwu k<PERSON>o", "subtitles.block.pale_hanging_moss.idle": "Eerie noise", "subtitles.block.piston.move": "<PERSON><PERSON>", "subtitles.block.pointed_dripstone.drip_lava": "<PERSON><PERSON> ntapu", "subtitles.block.pointed_dripstone.drip_lava_into_cauldron": "Lava na -amaba n'ime ite", "subtitles.block.pointed_dripstone.drip_water": "Water drops", "subtitles.block.pointed_dripstone.drip_water_into_cauldron": "<PERSON><PERSON><PERSON> na <PERSON><PERSON><PERSON> n'ite", "subtitles.block.pointed_dripstone.land": "Stalactite na -a<PERSON>ụda", "subtitles.block.portal.ambient": "<PERSON><PERSON><PERSON> mee mkp<PERSON>", "subtitles.block.portal.travel": "Mkpọtụ Portal na -ebelata", "subtitles.block.portal.trigger": "Mkpọtụ ọn<PERSON>z<PERSON> na -akawanye njọ", "subtitles.block.pressure_plate.click": "<PERSON><PERSON><PERSON> mee mkp<PERSON>", "subtitles.block.pumpkin.carve": "<PERSON><PERSON> p<PERSON> ap<PERSON>", "subtitles.block.redstone_torch.burnout": "<PERSON><PERSON><PERSON> mee mkp<PERSON>", "subtitles.block.respawn_anchor.ambient": "<PERSON><PERSON><PERSON><PERSON> whooshes", "subtitles.block.respawn_anchor.charge": "<PERSON><PERSON><PERSON> arịl<PERSON>", "subtitles.block.respawn_anchor.deplete": "<PERSON><PERSON><PERSON> na-a<PERSON>bad<PERSON>", "subtitles.block.respawn_anchor.set_spawn": "<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.block.sand.idle": "Sandy sounds", "subtitles.block.sand.wind": "Windy sounds", "subtitles.block.sculk.charge": "Afụ sculk", "subtitles.block.sculk.spread": "Sculk na-agbasa", "subtitles.block.sculk_catalyst.bloom": "Sculk Ihe na-akpali akpali na-agba agba", "subtitles.block.sculk_sensor.clicking": "<PERSON><PERSON><PERSON> Sculk na -amali<PERSON>", "subtitles.block.sculk_sensor.clicking_stop": "<PERSON><PERSON><PERSON> akw<PERSON><PERSON><PERSON><PERSON>", "subtitles.block.sculk_shrieker.shriek": "Sculk Mkpu ákwá", "subtitles.block.shulker_box.close": "<PERSON><PERSON><PERSON> mechie", "subtitles.block.shulker_box.open": "<PERSON><PERSON><PERSON>", "subtitles.block.sign.waxed_interact_fail": "Sign wobbles", "subtitles.block.smithing_table.use": "<PERSON><PERSON><PERSON>", "subtitles.block.smoker.smoke": "<PERSON><PERSON><PERSON> k<PERSON>", "subtitles.block.sniffer_egg.crack": "Sniffer Egg cracks", "subtitles.block.sniffer_egg.hatch": "Sniffer Egg hatches", "subtitles.block.sniffer_egg.plop": "Sniffer plops", "subtitles.block.sponge.absorb": "Sponge sucks", "subtitles.block.sweet_berry_bush.pick_berries": "Tomato pop", "subtitles.block.trapdoor.close": "Trapdoor closes", "subtitles.block.trapdoor.open": "Trapdoor opens", "subtitles.block.trapdoor.toggle": "<PERSON><PERSON> mkpakana mee mkpọt<PERSON>", "subtitles.block.trial_spawner.about_to_spawn_item": "Ominous item prepares", "subtitles.block.trial_spawner.ambient": "Trial Spawner crackles", "subtitles.block.trial_spawner.ambient_charged": "Ominous crackling", "subtitles.block.trial_spawner.ambient_ominous": "Ominous crackling", "subtitles.block.trial_spawner.charge_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.close_shutter": "Trial Spawner closes", "subtitles.block.trial_spawner.detect_player": "Trial Spawner charges up", "subtitles.block.trial_spawner.eject_item": "Trial Spawner ejects items", "subtitles.block.trial_spawner.ominous_activate": "Omen engulfs Trial Spawner", "subtitles.block.trial_spawner.open_shutter": "Trial Spawner opens", "subtitles.block.trial_spawner.spawn_item": "Ominous item drops", "subtitles.block.trial_spawner.spawn_item_begin": "Ominous item appears", "subtitles.block.trial_spawner.spawn_mob": "Trial Spawner spawns a mob", "subtitles.block.tripwire.attach": "<PERSON><PERSON><PERSON><PERSON> jide", "subtitles.block.tripwire.click": "<PERSON><PERSON><PERSON><PERSON> mee mkp<PERSON>", "subtitles.block.tripwire.detach": "<PERSON><PERSON><PERSON><PERSON> sep<PERSON>", "subtitles.block.vault.activate": "Vault ignites", "subtitles.block.vault.ambient": "Vault crackles", "subtitles.block.vault.close_shutter": "Vault closes", "subtitles.block.vault.deactivate": "<PERSON>ault extinguishes", "subtitles.block.vault.eject_item": "Vault ejects item", "subtitles.block.vault.insert_item": "<PERSON><PERSON> unlocks", "subtitles.block.vault.insert_item_fail": "Vault rejects item", "subtitles.block.vault.open_shutter": "Vault opens", "subtitles.block.vault.reject_rewarded_player": "<PERSON><PERSON> rejects player", "subtitles.block.water.ambient": "<PERSON><PERSON><PERSON>", "subtitles.block.wet_sponge.dries": "Sponge dries", "subtitles.chiseled_bookshelf.insert": "Book placed", "subtitles.chiseled_bookshelf.insert_enchanted": "Enchanted Book placed", "subtitles.chiseled_bookshelf.take": "Book taken", "subtitles.chiseled_bookshelf.take_enchanted": "Enchanted Book taken", "subtitles.enchant.thorns.hit": "Ogwu duo", "subtitles.entity.allay.ambient_with_item": "<PERSON>ay na-<PERSON><PERSON>", "subtitles.entity.allay.ambient_without_item": "<PERSON><PERSON>", "subtitles.entity.allay.death": "<PERSON>ay anw<PERSON>", "subtitles.entity.allay.hurt": "<PERSON>ay na <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.allay.item_given": "Ibelata <PERSON>ị", "subtitles.entity.allay.item_taken": "<PERSON><PERSON><PERSON> egwu", "subtitles.entity.allay.item_thrown": "Ibelata ntụgharị", "subtitles.entity.armadillo.ambient": "<PERSON><PERSON><PERSON> grunts", "subtitles.entity.armadillo.brush": "<PERSON>ute is brushed off", "subtitles.entity.armadillo.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.armadillo.eat": "<PERSON><PERSON><PERSON> eats", "subtitles.entity.armadillo.hurt": "<PERSON><PERSON><PERSON> hurts", "subtitles.entity.armadillo.hurt_reduced": "Armadillo shields itself", "subtitles.entity.armadillo.land": "Armadillo lands", "subtitles.entity.armadillo.peek": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armadillo.roll": "Armadillo rolls up", "subtitles.entity.armadillo.scute_drop": "Armadillo sheds scute", "subtitles.entity.armadillo.unroll_finish": "Armadillo unrolls", "subtitles.entity.armadillo.unroll_start": "<PERSON><PERSON><PERSON> peeks", "subtitles.entity.armor_stand.fall": "Ihe daa", "subtitles.entity.arrow.hit": "Ube gbaa", "subtitles.entity.arrow.hit_player": "<PERSON><PERSON> e<PERSON>", "subtitles.entity.arrow.shoot": "<PERSON>be a gbara", "subtitles.entity.axolotl.attack": "Mwakpo <PERSON>", "subtitles.entity.axolotl.death": "<PERSON>x<PERSON><PERSON> anwụ", "subtitles.entity.axolotl.hurt": "Axolotl na-afụ <PERSON>ụ", "subtitles.entity.axolotl.idle_air": "Axolotl na-egwu egwu", "subtitles.entity.axolotl.idle_water": "Axolotl chirps", "subtitles.entity.axolotl.splash": "Axolotl na-efe efe", "subtitles.entity.axolotl.swim": "Axolotl na-egwu mmiri", "subtitles.entity.bat.ambient": "Usụ bee", "subtitles.entity.bat.death": "<PERSON><PERSON> nw<PERSON>", "subtitles.entity.bat.hurt": "<PERSON><PERSON> me<PERSON>", "subtitles.entity.bat.takeoff": "<PERSON><PERSON> f<PERSON>", "subtitles.entity.bee.ambient": "Bee egbe", "subtitles.entity.bee.death": "<PERSON>", "subtitles.entity.bee.hurt": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.bee.loop": "Bee buzzes", "subtitles.entity.bee.loop_aggressive": "<PERSON> na-ewe iwe iwe", "subtitles.entity.bee.pollinate": "<PERSON> <PERSON><PERSON>enwe obi <PERSON>", "subtitles.entity.bee.sting": "<PERSON> ah<PERSON>", "subtitles.entity.blaze.ambient": "<PERSON><PERSON> kuo ume", "subtitles.entity.blaze.burn": "<PERSON><PERSON> mee mkp<PERSON>", "subtitles.entity.blaze.death": "<PERSON><PERSON> nw<PERSON>", "subtitles.entity.blaze.hurt": "<PERSON><PERSON> me<PERSON>", "subtitles.entity.blaze.shoot": "<PERSON><PERSON>", "subtitles.entity.boat.paddle_land": "Ịkwọ ụgbọ", "subtitles.entity.boat.paddle_water": "Rowing", "subtitles.entity.bogged.ambient": "Bogged rattles", "subtitles.entity.bogged.death": "Bogged dies", "subtitles.entity.bogged.hurt": "Bogged hurts", "subtitles.entity.breeze.charge": "Breeze charges", "subtitles.entity.breeze.death": "<PERSON><PERSON> dies", "subtitles.entity.breeze.deflect": "<PERSON><PERSON> deflects", "subtitles.entity.breeze.hurt": "Breeze hurts", "subtitles.entity.breeze.idle_air": "Breeze flies", "subtitles.entity.breeze.idle_ground": "Breeze whirs", "subtitles.entity.breeze.inhale": "Breeze inhales", "subtitles.entity.breeze.jump": "<PERSON><PERSON> jumps", "subtitles.entity.breeze.land": "Breeze lands", "subtitles.entity.breeze.shoot": "Breeze shoots", "subtitles.entity.breeze.slide": "Breeze slides", "subtitles.entity.breeze.whirl": "Breeze whirls", "subtitles.entity.breeze.wind_burst": "Wind Charge bursts", "subtitles.entity.camel.ambient": "Camel grunts", "subtitles.entity.camel.dash": "Camel yeets", "subtitles.entity.camel.dash_ready": "Camel recovers", "subtitles.entity.camel.death": "Camel dies", "subtitles.entity.camel.eat": "Camel eats", "subtitles.entity.camel.hurt": "Camel hurts", "subtitles.entity.camel.saddle": "Saddle equips", "subtitles.entity.camel.sit": "Camel sits down", "subtitles.entity.camel.stand": "Camel stands up", "subtitles.entity.camel.step": "Camel steps", "subtitles.entity.camel.step_sand": "Camel sands", "subtitles.entity.cat.ambient": "Buusu bee", "subtitles.entity.cat.beg_for_food": "Cat na -ar<PERSON>ọ arịr<PERSON>", "subtitles.entity.cat.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cat.eat": "<PERSON> na -eri nri", "subtitles.entity.cat.hiss": "<PERSON> na -ekwu okwu", "subtitles.entity.cat.hurt": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "subtitles.entity.cat.purr": "<PERSON>", "subtitles.entity.chicken.ambient": "Ọkụkọ kwaa", "subtitles.entity.chicken.death": "Ọkụkọ nwụọ", "subtitles.entity.chicken.egg": "Ọkụkọ daba", "subtitles.entity.chicken.hurt": "Ọkụkọ mer<PERSON><PERSON> ahụ", "subtitles.entity.cod.death": "Cod nwụr<PERSON>", "subtitles.entity.cod.flop": "Cod na -agbaji", "subtitles.entity.cod.hurt": "Cod na -a<PERSON><PERSON>", "subtitles.entity.cow.ambient": "<PERSON><PERSON> moos", "subtitles.entity.cow.death": "<PERSON><PERSON>", "subtitles.entity.cow.hurt": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.cow.milk": "A na -aowụ ehi", "subtitles.entity.creaking.activate": "Creaking watches", "subtitles.entity.creaking.ambient": "Creaking creaks", "subtitles.entity.creaking.attack": "Creaking attacks", "subtitles.entity.creaking.deactivate": "Creaking calms", "subtitles.entity.creaking.death": "Creaking crumbles", "subtitles.entity.creaking.freeze": "Creaking stops", "subtitles.entity.creaking.spawn": "Creaking manifests", "subtitles.entity.creaking.sway": "Creaking is hit", "subtitles.entity.creaking.twitch": "Creaking twitches", "subtitles.entity.creaking.unfreeze": "Creaking moves", "subtitles.entity.creeper.death": "<PERSON><PERSON><PERSON> nw<PERSON>", "subtitles.entity.creeper.hurt": "Creeper na -a<PERSON><PERSON>", "subtitles.entity.creeper.primed": "<PERSON><PERSON><PERSON> na -ama jijiji", "subtitles.entity.dolphin.ambient": "Azụ dolphin", "subtitles.entity.dolphin.ambient_water": "Dolphin na <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.attack": "Mwakpo dolphin", "subtitles.entity.dolphin.death": "Dolphin anwụ", "subtitles.entity.dolphin.eat": "Dolphin na -eri nri", "subtitles.entity.dolphin.hurt": "Dolphin na <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.dolphin.jump": "Dolphin na -awụlikwa elu", "subtitles.entity.dolphin.play": "Dolphin ejije", "subtitles.entity.dolphin.splash": "Dolphin na -efe efe", "subtitles.entity.dolphin.swim": "Dolphin na -egwu mmiri", "subtitles.entity.donkey.ambient": "Ịnyịnya ibu hee-<PERSON>kwụ", "subtitles.entity.donkey.angry": "Ịnyịnya ibu agbata obi", "subtitles.entity.donkey.chest": "Igbe ịnyịnya ibu na -akwadebe", "subtitles.entity.donkey.death": "Ịnyịnya ibu nw<PERSON>", "subtitles.entity.donkey.eat": "Ịnyịnya ibu na -eri nri", "subtitles.entity.donkey.hurt": "Ịnyịnya ibu na -afụ ụfụ", "subtitles.entity.donkey.jump": "<PERSON><PERSON> jumps", "subtitles.entity.drowned.ambient": "<PERSON><PERSON><PERSON> n<PERSON> mmiri", "subtitles.entity.drowned.ambient_water": "Drowned gurgles", "subtitles.entity.drowned.death": "<PERSON><PERSON><PERSON> r<PERSON> na -anw<PERSON>", "subtitles.entity.drowned.hurt": "<PERSON><PERSON><PERSON> r<PERSON> na -a<PERSON><PERSON>", "subtitles.entity.drowned.shoot": "Mmiri riri na -atụba Trident", "subtitles.entity.drowned.step": "<PERSON><PERSON><PERSON>", "subtitles.entity.drowned.swim": "<PERSON><PERSON><PERSON> rik<PERSON>u na -egwu mmiri", "subtitles.entity.egg.throw": "Akwa na -efe efe", "subtitles.entity.elder_guardian.ambient": "Okenye Guardian na -as<PERSON> ude", "subtitles.entity.elder_guardian.ambient_land": "Okenye Guardian flaps", "subtitles.entity.elder_guardian.curse": "Onye nlekọta okenye", "subtitles.entity.elder_guardian.death": "Elder Guardian n<PERSON><PERSON><PERSON>", "subtitles.entity.elder_guardian.flop": "Okenye Guardian flops", "subtitles.entity.elder_guardian.hurt": "Okenye Guardian na -a<PERSON><PERSON>", "subtitles.entity.ender_dragon.ambient": "Dragọn na -ebigbọ", "subtitles.entity.ender_dragon.death": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.flap": "Ọkụ dragọn", "subtitles.entity.ender_dragon.growl": "Dragọn na -eme mk<PERSON>", "subtitles.entity.ender_dragon.hurt": "Dragọn na <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ender_dragon.shoot": "<PERSON>ag<PERSON><PERSON> ome", "subtitles.entity.ender_eye.death": "<PERSON>", "subtitles.entity.ender_eye.launch": "<PERSON>", "subtitles.entity.ender_pearl.throw": "<PERSON><PERSON> efe", "subtitles.entity.enderman.ambient": "<PERSON><PERSON> na -ekwu okwu", "subtitles.entity.enderman.death": "<PERSON><PERSON> anwụ", "subtitles.entity.enderman.hurt": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.enderman.scream": "<PERSON><PERSON> screams", "subtitles.entity.enderman.stare": "Enderman tiri mkpu", "subtitles.entity.enderman.teleport": "<PERSON><PERSON> teli<PERSON>n", "subtitles.entity.endermite.ambient": "Endermite na -agba mmiri", "subtitles.entity.endermite.death": "End<PERSON><PERSON> anwụ", "subtitles.entity.endermite.hurt": "Endermite na -a<PERSON><PERSON>", "subtitles.entity.evoker.ambient": "Evoker na -at<PERSON>u ntamu", "subtitles.entity.evoker.cast_spell": "Evoker na -agba afa", "subtitles.entity.evoker.celebrate": "Evoker na -akwado", "subtitles.entity.evoker.death": "Evoker anw<PERSON>", "subtitles.entity.evoker.hurt": "Evoker <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.evoker.prepare_attack": "Evoker na -<PERSON><PERSON><PERSON><PERSON><PERSON> mwakpo", "subtitles.entity.evoker.prepare_summon": "Evoker na -<PERSON><PERSON><PERSON><PERSON> oku", "subtitles.entity.evoker.prepare_wololo": "Evoker na -akwa<PERSON>be maa mma", "subtitles.entity.evoker_fangs.attack": "Ntucha <PERSON>", "subtitles.entity.experience_orb.pickup": "<PERSON><PERSON><PERSON>", "subtitles.entity.firework_rocket.blast": "Ọkụ ọkụ na -agbawa", "subtitles.entity.firework_rocket.launch": "A na -amụnye ọ<PERSON>", "subtitles.entity.firework_rocket.twinkle": "Ọkụ ọkụ na -ama jijiji", "subtitles.entity.fish.swim": "Splashes", "subtitles.entity.fishing_bobber.retrieve": "Ewepụ<PERSON>", "subtitles.entity.fishing_bobber.splash": "<PERSON><PERSON> a<PERSON> a<PERSON> p<PERSON>", "subtitles.entity.fishing_bobber.throw": "<PERSON><PERSON> a<PERSON> a<PERSON> a tụ<PERSON>", "subtitles.entity.fox.aggro": "Fox na -ewe iwe", "subtitles.entity.fox.ambient": "<PERSON> na -ama jijiji", "subtitles.entity.fox.bite": "Fox na -ata", "subtitles.entity.fox.death": "Fox na -anw<PERSON>", "subtitles.entity.fox.eat": "Fox na -eri nri", "subtitles.entity.fox.hurt": "Fox na -a<PERSON><PERSON>", "subtitles.entity.fox.screech": "Nkịta n<PERSON><PERSON>", "subtitles.entity.fox.sleep": "<PERSON> na -eku ume", "subtitles.entity.fox.sniff": "<PERSON> na -ama jijiji", "subtitles.entity.fox.spit": "Fox na -asọ asọ", "subtitles.entity.fox.teleport": "Nkịta ọhịa na e telepọtụ", "subtitles.entity.frog.ambient": "Awọ na-akwa", "subtitles.entity.frog.death": "Awọ na-akwa anwụ", "subtitles.entity.frog.eat": "Awọ na-eri nri", "subtitles.entity.frog.hurt": "<PERSON><PERSON><PERSON> na- <PERSON><PERSON><PERSON>", "subtitles.entity.frog.lay_spawn": "Awọ na-eyi akwa", "subtitles.entity.frog.long_jump": "<PERSON><PERSON><PERSON> na-amali elu", "subtitles.entity.generic.big_fall": "Something fell", "subtitles.entity.generic.burn": "<PERSON> -ere <PERSON>", "subtitles.entity.generic.death": "<PERSON> <PERSON><PERSON><PERSON><PERSON> anwụ", "subtitles.entity.generic.drink": "Ọkụ", "subtitles.entity.generic.eat": "Na -eri nri", "subtitles.entity.generic.explode": "Mgbawa", "subtitles.entity.generic.extinguish_fire": "Ọkụ na -emenyụ", "subtitles.entity.generic.hurt": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.generic.small_fall": "<PERSON><PERSON> na -eme njem", "subtitles.entity.generic.splash": "Na -efesa", "subtitles.entity.generic.swim": "<PERSON><PERSON><PERSON>", "subtitles.entity.generic.wind_burst": "Wind Charge bursts", "subtitles.entity.ghast.ambient": "<PERSON><PERSON><PERSON> na-ebe <PERSON>", "subtitles.entity.ghast.death": "<PERSON><PERSON><PERSON> anw<PERSON>", "subtitles.entity.ghast.hurt": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ghast.shoot": "<PERSON><PERSON><PERSON> gbara", "subtitles.entity.ghastling.ambient": "Ghastling coos", "subtitles.entity.ghastling.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.ghastling.hurt": "Ghastling hurts", "subtitles.entity.ghastling.spawn": "<PERSON><PERSON><PERSON> appears", "subtitles.entity.glow_item_frame.add_item": "<PERSON><PERSON> ihe na-egbuke egbuke", "subtitles.entity.glow_item_frame.break": "Ọkụ ihe na-agbaji", "subtitles.entity.glow_item_frame.place": "Edobere okpokolo a<PERSON> ọkụ", "subtitles.entity.glow_item_frame.remove_item": "Igwe ihe na-egbuke egbuke na-apụ apụ", "subtitles.entity.glow_item_frame.rotate_item": "Igwe ihe na-egbuke egbuke pịa", "subtitles.entity.glow_squid.ambient": "Glow Squid na-egwu mmiri", "subtitles.entity.glow_squid.death": "Glow Squid anwụ", "subtitles.entity.glow_squid.hurt": "Glow Squid na-afụ <PERSON>", "subtitles.entity.glow_squid.squirt": "Glow squid na-agba ink", "subtitles.entity.goat.ambient": "<PERSON><PERSON> tie mkpu", "subtitles.entity.goat.death": "<PERSON><PERSON> n<PERSON>", "subtitles.entity.goat.eat": "<PERSON><PERSON> rie", "subtitles.entity.goat.horn_break": "<PERSON><PERSON> <PERSON><PERSON> na<PERSON>a<PERSON>", "subtitles.entity.goat.hurt": "<PERSON><PERSON>", "subtitles.entity.goat.long_jump": "<PERSON><PERSON> jumps", "subtitles.entity.goat.milk": "<PERSON><PERSON> na-enweta mmiri ara ehi", "subtitles.entity.goat.prepare_ram": "<PERSON><PERSON> stomp", "subtitles.entity.goat.ram_impact": "<PERSON><PERSON><PERSON>", "subtitles.entity.goat.screaming.ambient": "<PERSON><PERSON> bellow", "subtitles.entity.goat.step": "<PERSON><PERSON>", "subtitles.entity.guardian.ambient": "Onye nche na -asụ ude", "subtitles.entity.guardian.ambient_land": "Ndị na -eche nche", "subtitles.entity.guardian.attack": "Onye nche na -agbapụ", "subtitles.entity.guardian.death": "Onye nche anwụ", "subtitles.entity.guardian.flop": "<PERSON><PERSON> n<PERSON>kọ<PERSON> flops", "subtitles.entity.guardian.hurt": "Onye nche na -a<PERSON>ụ <PERSON>", "subtitles.entity.happy_ghast.ambient": "<PERSON> croons", "subtitles.entity.happy_ghast.death": "<PERSON><PERSON> dies", "subtitles.entity.happy_ghast.equip": "Ha<PERSON>ss equips", "subtitles.entity.happy_ghast.harness_goggles_down": "Happy <PERSON><PERSON><PERSON> is ready", "subtitles.entity.happy_ghast.harness_goggles_up": "<PERSON> stops", "subtitles.entity.happy_ghast.hurt": "<PERSON> hurts", "subtitles.entity.happy_ghast.unequip": "<PERSON><PERSON><PERSON> unequips", "subtitles.entity.hoglin.ambient": "A na-akpọ Composter", "subtitles.entity.hoglin.angry": "Hoglin ji oke iwe", "subtitles.entity.hoglin.attack": "Hoglin ji oke iwe", "subtitles.entity.hoglin.converted_to_zombified": "<PERSON><PERSON><PERSON> Zoglin", "subtitles.entity.hoglin.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.hurt": "<PERSON><PERSON><PERSON> na<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.hoglin.retreat": "Hoglin na-<PERSON><PERSON><PERSON> a<PERSON>", "subtitles.entity.hoglin.step": "Hoglin na-agbago", "subtitles.entity.horse.ambient": "Ịgba ịny<PERSON>nya", "subtitles.entity.horse.angry": "Horse neighs", "subtitles.entity.horse.armor": "Ngwá agha ị<PERSON>ị<PERSON> na -akwadebe", "subtitles.entity.horse.breathe": "Ịnyịnya na -eku ume", "subtitles.entity.horse.death": "Ịnyịnya na -anwụ", "subtitles.entity.horse.eat": "Ịnyịnya na -eri nri", "subtitles.entity.horse.gallop": "Ịgba ịny<PERSON>nya", "subtitles.entity.horse.hurt": "Ịnyịnya na -afụ ụfụ", "subtitles.entity.horse.jump": "Ịnyịnya na -awụlikwa elu", "subtitles.entity.horse.saddle": "Saddle na -akwadebe", "subtitles.entity.husk.ambient": "Husk na -asụ ude", "subtitles.entity.husk.converted_to_zombie": "Husk na -atụgharị na Zombie", "subtitles.entity.husk.death": "Husk anwụ", "subtitles.entity.husk.hurt": "Husk na -afụ <PERSON>", "subtitles.entity.illusioner.ambient": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "subtitles.entity.illusioner.cast_spell": "<PERSON><PERSON><PERSON> na <PERSON>a<PERSON><PERSON> afa", "subtitles.entity.illusioner.death": "<PERSON><PERSON><PERSON> <PERSON>", "subtitles.entity.illusioner.hurt": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.mirror_move": "<PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.prepare_blindness": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.illusioner.prepare_mirror": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> onyo enyo", "subtitles.entity.iron_golem.attack": "Iron Golem na -awakpo", "subtitles.entity.iron_golem.damage": "Iron Golem mebie", "subtitles.entity.iron_golem.death": "Iron Golem anwụ", "subtitles.entity.iron_golem.hurt": "Iron Golem na -a<PERSON><PERSON>", "subtitles.entity.iron_golem.repair": "Iron Golem rụ<PERSON><PERSON>", "subtitles.entity.item.break": "<PERSON><PERSON> nk<PERSON>", "subtitles.entity.item.pickup": "Ng<PERSON>he", "subtitles.entity.item_frame.add_item": "Okpokoro Ihe na -ejuputa", "subtitles.entity.item_frame.break": "Okpokoro Nkebi na -agbaji", "subtitles.entity.item_frame.place": "<PERSON>tin<PERSON><PERSON>", "subtitles.entity.item_frame.remove_item": "Okpokoro Ihe na -atọgbọ", "subtitles.entity.item_frame.rotate_item": "<PERSON>he pịa ihe", "subtitles.entity.leash_knot.break": "<PERSON><PERSON> broken", "subtitles.entity.leash_knot.place": "<PERSON><PERSON> tied", "subtitles.entity.lightning_bolt.impact": "Àmụ̀mà etiwap<PERSON>rụ", "subtitles.entity.lightning_bolt.thunder": "Gbè eluigwe na -ebigbọ", "subtitles.entity.llama.ambient": "Llama na -eti mkpu", "subtitles.entity.llama.angry": "Llama na -eti mkpu iwe", "subtitles.entity.llama.chest": "Llama Chest na -akwadebe", "subtitles.entity.llama.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.llama.eat": "<PERSON><PERSON><PERSON> na -eri nri", "subtitles.entity.llama.hurt": "<PERSON>lam<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.llama.spit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.llama.step": "Nzọụkwụ <PERSON>", "subtitles.entity.llama.swag": "A na -achọkwa Llama mma", "subtitles.entity.magma_cube.death": "Magma Cube anwụ", "subtitles.entity.magma_cube.hurt": "Magma Cube na -afụ <PERSON>ụ", "subtitles.entity.magma_cube.squish": "Magma Cube na -agba ọsọ", "subtitles.entity.minecart.inside": "<PERSON><PERSON><PERSON> jangles", "subtitles.entity.minecart.inside_underwater": "Minecart jangles underwater", "subtitles.entity.minecart.riding": "Minecart na -apụta", "subtitles.entity.mooshroom.convert": "Mooshroom na -agbanwe", "subtitles.entity.mooshroom.eat": "Mooshroom na -eri nri", "subtitles.entity.mooshroom.milk": "Mooshroom na -arah<PERSON> ara", "subtitles.entity.mooshroom.suspicious_milk": "Mooshroom na -arahụ ara na enyo", "subtitles.entity.mule.ambient": "Inyinya ibu-haws", "subtitles.entity.mule.angry": "Inyinya ibu", "subtitles.entity.mule.chest": "Inyinya ibu Inyinya na -akwadebe", "subtitles.entity.mule.death": "Inyinya mule na -anwụ", "subtitles.entity.mule.eat": "Inyinya ibu na -eri nri", "subtitles.entity.mule.hurt": "Inyinya ibu na -a<PERSON><PERSON>", "subtitles.entity.mule.jump": "<PERSON><PERSON> jumps", "subtitles.entity.painting.break": "<PERSON><PERSON> nkwụ<PERSON> agba", "subtitles.entity.painting.place": "<PERSON><PERSON> eserese", "subtitles.entity.panda.aggressive_ambient": "Panda na -acha uhie uhie", "subtitles.entity.panda.ambient": "Panda <PERSON>", "subtitles.entity.panda.bite": "Panda na -ata ah<PERSON>", "subtitles.entity.panda.cant_breed": "Panda na -eti mkpu", "subtitles.entity.panda.death": "Panda nwụ<PERSON>", "subtitles.entity.panda.eat": "Panda na -eri nri", "subtitles.entity.panda.hurt": "Panda na -a<PERSON><PERSON>", "subtitles.entity.panda.pre_sneeze": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.panda.sneeze": "Panda na -eme mkpọ<PERSON>ụ", "subtitles.entity.panda.step": "Nzọụkwụ Panda", "subtitles.entity.panda.worried_ambient": "Panda na -acha uhie uhie", "subtitles.entity.parrot.ambient": "<PERSON><PERSON><PERSON> kwuo okwu", "subtitles.entity.parrot.death": "Icheoku nwụọ", "subtitles.entity.parrot.eats": "Icheoku rie nri", "subtitles.entity.parrot.fly": "Parrot na -<PERSON>ị", "subtitles.entity.parrot.hurts": "<PERSON><PERSON><PERSON> mer<PERSON> ah<PERSON>", "subtitles.entity.parrot.imitate.blaze": "<PERSON><PERSON> na -eku ume", "subtitles.entity.parrot.imitate.bogged": "Parrot rattles", "subtitles.entity.parrot.imitate.breeze": "Parrot whirs", "subtitles.entity.parrot.imitate.creaking": "Parrot creaks", "subtitles.entity.parrot.imitate.creeper": "<PERSON><PERSON><PERSON> na -ama ya aka", "subtitles.entity.parrot.imitate.drowned": "<PERSON><PERSON><PERSON> na -ama jijiji", "subtitles.entity.parrot.imitate.elder_guardian": "<PERSON><PERSON><PERSON> na -<PERSON><PERSON> ude", "subtitles.entity.parrot.imitate.ender_dragon": "Okeo na -ebigbọ", "subtitles.entity.parrot.imitate.endermite": "<PERSON><PERSON> na -ama jijiji", "subtitles.entity.parrot.imitate.evoker": "<PERSON><PERSON> na -atamu ntamu", "subtitles.entity.parrot.imitate.ghast": "<PERSON><PERSON> na -ebe akwa", "subtitles.entity.parrot.imitate.guardian": "<PERSON><PERSON><PERSON> moans", "subtitles.entity.parrot.imitate.hoglin": "Parrot na -eme mkp<PERSON>", "subtitles.entity.parrot.imitate.husk": "<PERSON><PERSON><PERSON> na -<PERSON><PERSON> ude", "subtitles.entity.parrot.imitate.illusioner": "Parrot murmurs", "subtitles.entity.parrot.imitate.magma_cube": "Parrot na -agba ọsọ", "subtitles.entity.parrot.imitate.phantom": "<PERSON><PERSON><PERSON> parrot", "subtitles.entity.parrot.imitate.piglin": "Okeọ na -eku ume", "subtitles.entity.parrot.imitate.piglin_brute": "<PERSON><PERSON><PERSON> snorts", "subtitles.entity.parrot.imitate.pillager": "Parrot murmurs", "subtitles.entity.parrot.imitate.ravager": "<PERSON><PERSON><PERSON> na-atamu", "subtitles.entity.parrot.imitate.shulker": "<PERSON>eo na -ezo", "subtitles.entity.parrot.imitate.silverfish": "Parrot hisses", "subtitles.entity.parrot.imitate.skeleton": "<PERSON><PERSON><PERSON> na -ama jijiji", "subtitles.entity.parrot.imitate.slime": "Parrot squishes", "subtitles.entity.parrot.imitate.spider": "Parrot hisses", "subtitles.entity.parrot.imitate.stray": "Parrot rattles", "subtitles.entity.parrot.imitate.vex": "<PERSON><PERSON><PERSON> na -ata ah<PERSON>", "subtitles.entity.parrot.imitate.vindicator": "Parrot na -ekwu okwu", "subtitles.entity.parrot.imitate.warden": "Icheoku na-ebe akwa", "subtitles.entity.parrot.imitate.witch": "<PERSON><PERSON><PERSON> na -<PERSON><PERSON><PERSON>", "subtitles.entity.parrot.imitate.wither": "<PERSON>e iwe", "subtitles.entity.parrot.imitate.wither_skeleton": "Parrot rattles", "subtitles.entity.parrot.imitate.zoglin": "<PERSON>rro<PERSON> growls", "subtitles.entity.parrot.imitate.zombie": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.parrot.imitate.zombie_villager": "<PERSON><PERSON><PERSON> groans", "subtitles.entity.phantom.ambient": "Phantom na -eti mkpu", "subtitles.entity.phantom.bite": "<PERSON> na -ata ah<PERSON>", "subtitles.entity.phantom.death": "Phantom anwụ", "subtitles.entity.phantom.flap": "<PERSON><PERSON> mk<PERSON>chi <PERSON>", "subtitles.entity.phantom.hurt": "<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.phantom.swoop": "Phantom na -agba ọsọ", "subtitles.entity.pig.ambient": "<PERSON><PERSON> o<PERSON>s", "subtitles.entity.pig.death": "<PERSON><PERSON>", "subtitles.entity.pig.hurt": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.pig.saddle": "Saddle equips", "subtitles.entity.piglin.admiring_item": "<PERSON>lin nwere mmasị n'ihe", "subtitles.entity.piglin.ambient": "<PERSON><PERSON> snorts", "subtitles.entity.piglin.angry": "<PERSON>lin ji iwe were iwe", "subtitles.entity.piglin.celebrate": "<PERSON><PERSON> na-eme ememe", "subtitles.entity.piglin.converted_to_zombified": "Piglin tọghatara Zombified Piglin", "subtitles.entity.piglin.death": "<PERSON><PERSON> nw<PERSON>", "subtitles.entity.piglin.hurt": "<PERSON><PERSON> snorts ji <PERSON><PERSON>", "subtitles.entity.piglin.jealous": "<PERSON><PERSON> snorts enviously", "subtitles.entity.piglin.retreat": "<PERSON><PERSON>", "subtitles.entity.piglin.step": "Nzọụkw<PERSON>", "subtitles.entity.piglin_brute.ambient": "<PERSON>lin Brute na -eme mkp<PERSON>ụ", "subtitles.entity.piglin_brute.angry": "<PERSON>lin Brute ji iwe na -eku ume", "subtitles.entity.piglin_brute.converted_to_zombified": "Piglin Brute na -atụgharị gaa Piglin Zombified", "subtitles.entity.piglin_brute.death": "<PERSON><PERSON> anw<PERSON>", "subtitles.entity.piglin_brute.hurt": "Piglin Brute na -a<PERSON><PERSON>", "subtitles.entity.piglin_brute.step": "Nzọụkwụ <PERSON> Bru<PERSON>", "subtitles.entity.pillager.ambient": "<PERSON>lla<PERSON> na -atamu ntamu", "subtitles.entity.pillager.celebrate": "Pillager na -aersụrị ọ. ụ", "subtitles.entity.pillager.death": "<PERSON><PERSON><PERSON> anwụ", "subtitles.entity.pillager.hurt": "Pillager na -a<PERSON><PERSON>", "subtitles.entity.player.attack.crit": "<PERSON><PERSON><PERSON><PERSON> dị oke egwu", "subtitles.entity.player.attack.knockback": "<PERSON><PERSON><PERSON>", "subtitles.entity.player.attack.strong": "<PERSON>wak<PERSON> siri ike", "subtitles.entity.player.attack.sweep": "Mwakpo na <PERSON>", "subtitles.entity.player.attack.weak": "Mwakpo <PERSON> -<PERSON>ị<PERSON> ike", "subtitles.entity.player.burp": "Agba", "subtitles.entity.player.death": "<PERSON><PERSON>ụkpọ nwụr<PERSON>", "subtitles.entity.player.freeze_hurt": "<PERSON><PERSON>ụkpọ kwụsịr<PERSON>", "subtitles.entity.player.hurt": "Onye na -ere ahịa na -a<PERSON><PERSON>", "subtitles.entity.player.hurt_drown": "<PERSON><PERSON><PERSON> na -ere nd<PERSON> e<PERSON>wu", "subtitles.entity.player.hurt_on_fire": "Onye na -ere ọ<PERSON> na -ere ọ<PERSON>", "subtitles.entity.player.levelup": "<PERSON><PERSON><PERSON> na -ere <PERSON>", "subtitles.entity.player.teleport": "Player teleports", "subtitles.entity.polar_bear.ambient": "Polar Bear na -as<PERSON> ude", "subtitles.entity.polar_bear.ambient_baby": "Polar Bear na -egwu egwu", "subtitles.entity.polar_bear.death": "Polar Bear anwụ", "subtitles.entity.polar_bear.hurt": "Polar Bear na <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.polar_bear.warning": "Polar Bear na -ebigbọ", "subtitles.entity.potion.splash": "Ka<PERSON><PERSON> na <PERSON><PERSON><PERSON>", "subtitles.entity.potion.throw": "A tụbara karama", "subtitles.entity.puffer_fish.blow_out": "Pufferfish na -agbagha", "subtitles.entity.puffer_fish.blow_up": "Pufferfish na <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.death": "Puffer<PERSON> nwụrụ", "subtitles.entity.puffer_fish.flop": "Pufferfish na -efe efe", "subtitles.entity.puffer_fish.hurt": "Pufferfish na <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.puffer_fish.sting": "Pufferfish na -agba agba", "subtitles.entity.rabbit.ambient": "Oke bekee na -amaja", "subtitles.entity.rabbit.attack": "<PERSON><PERSON><PERSON><PERSON> oke oyibo", "subtitles.entity.rabbit.death": "<PERSON>e oyibo na -an<PERSON>", "subtitles.entity.rabbit.hurt": "<PERSON>e oyibo na -an<PERSON>", "subtitles.entity.rabbit.jump": "<PERSON>e oyibo na -an<PERSON>", "subtitles.entity.ravager.ambient": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.ravager.attack": "Ra<PERSON>r na -ata", "subtitles.entity.ravager.celebrate": "Ọtụrụkpokpọọ na-akwado", "subtitles.entity.ravager.death": "Ọtụrụkpokpọọ na-anwu anwụ", "subtitles.entity.ravager.hurt": "Ọtụrụkpokpọọ na-emer<PERSON> ahụ", "subtitles.entity.ravager.roar": "Ọtụrụkpokpọọ na-ebigbo", "subtitles.entity.ravager.step": "Ọtụrụkpokpọọ na-aga ije", "subtitles.entity.ravager.stunned": "Ọtụrụkpokpọọ na-atụ n'anya", "subtitles.entity.salmon.death": "<PERSON>m<PERSON><PERSON> na<PERSON>anw<PERSON> anwụ", "subtitles.entity.salmon.flop": "<PERSON><PERSON><PERSON>n na-ada ada", "subtitles.entity.salmon.hurt": "<PERSON><PERSON><PERSON><PERSON> na-emeru ah<PERSON>", "subtitles.entity.sheep.ambient": "<PERSON><PERSON><PERSON><PERSON> na-ebe baaaaa", "subtitles.entity.sheep.death": "<PERSON><PERSON><PERSON><PERSON> na-anw<PERSON> anwụ", "subtitles.entity.sheep.hurt": "<PERSON><PERSON><PERSON><PERSON> na emerụ ah<PERSON>", "subtitles.entity.shulker.ambient": "<PERSON><PERSON><PERSON> na-ezo ezo", "subtitles.entity.shulker.close": "<PERSON><PERSON><PERSON> closes", "subtitles.entity.shulker.death": "<PERSON><PERSON><PERSON> na<PERSON>an<PERSON><PERSON> anwụ", "subtitles.entity.shulker.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.shulker.open": "<PERSON><PERSON><PERSON> opens", "subtitles.entity.shulker.shoot": "<PERSON><PERSON><PERSON> na-agba", "subtitles.entity.shulker.teleport": "Shulker na etelepọtụ", "subtitles.entity.shulker_bullet.hit": "Mgb<PERSON>", "subtitles.entity.shulker_bullet.hurt": "<PERSON><PERSON><PERSON> mg<PERSON> a<PERSON>", "subtitles.entity.silverfish.ambient": "<PERSON>zuolaocha na-ama <PERSON>ụ", "subtitles.entity.silverfish.death": "Silverfish anwụ", "subtitles.entity.silverfish.hurt": "Silverfish na <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.skeleton.ambient": "Ọkpụkpụ na -ama jijiji", "subtitles.entity.skeleton.converted_to_stray": "Ọkpụkpụ tụgharịrị gaa Stray", "subtitles.entity.skeleton.death": "Okpụk<PERSON><PERSON> na-anwụ anwụ", "subtitles.entity.skeleton.hurt": "Ng<PERSON> ngwa", "subtitles.entity.skeleton.shoot": "Ọkpụkpụ gbara", "subtitles.entity.skeleton_horse.ambient": "Skeleton Horse na -ebe akwa", "subtitles.entity.skeleton_horse.death": "Skeleton Horse na -anwụ", "subtitles.entity.skeleton_horse.hurt": "Skeleton Horse na <PERSON>a<PERSON><PERSON>ụ", "subtitles.entity.skeleton_horse.jump_water": "Skeleton Horse jumps", "subtitles.entity.skeleton_horse.swim": "Skeleton Horse na -egwu mmiri", "subtitles.entity.slime.attack": "Mwakpo slime", "subtitles.entity.slime.death": "Slime nwụ<PERSON>ụ", "subtitles.entity.slime.hurt": "Slime <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.slime.squish": "Slime na -amị amị", "subtitles.entity.sniffer.death": "<PERSON><PERSON><PERSON> dies", "subtitles.entity.sniffer.digging": "Sniffer digs", "subtitles.entity.sniffer.digging_stop": "<PERSON><PERSON><PERSON> stands up", "subtitles.entity.sniffer.drop_seed": "Sniffer drops seed", "subtitles.entity.sniffer.eat": "Sniffer eats", "subtitles.entity.sniffer.egg_crack": "Sniffer Egg cracks", "subtitles.entity.sniffer.egg_hatch": "Sniffer Egg hatches", "subtitles.entity.sniffer.happy": "Sniffer delights", "subtitles.entity.sniffer.hurt": "Sniffer hurts", "subtitles.entity.sniffer.idle": "Sniffer grunts", "subtitles.entity.sniffer.scenting": "Sniffer scents", "subtitles.entity.sniffer.searching": "Sniffer searches", "subtitles.entity.sniffer.sniffing": "Sniffer sniffs", "subtitles.entity.sniffer.step": "Sniffer steps", "subtitles.entity.snow_golem.death": "<PERSON> Golem anwụ", "subtitles.entity.snow_golem.hurt": "Snow Golem na -a<PERSON><PERSON>", "subtitles.entity.snowball.throw": "Snowball na -efe efe", "subtitles.entity.spider.ambient": "<PERSON><PERSON><PERSON> -ama ji<PERSON>ji", "subtitles.entity.spider.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.spider.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.squid.ambient": "Squid na -egwu mmiri", "subtitles.entity.squid.death": "<PERSON><PERSON> nw<PERSON>", "subtitles.entity.squid.hurt": "Squid na -a<PERSON><PERSON>", "subtitles.entity.squid.squirt": "Skwid na-agba ịnkị", "subtitles.entity.stray.ambient": "<PERSON><PERSON> mkpafu na-eme mkp<PERSON>", "subtitles.entity.stray.death": "<PERSON><PERSON><PERSON>an<PERSON><PERSON> anwụ", "subtitles.entity.stray.hurt": "Ịkpafu na -afụ ụfụ", "subtitles.entity.strider.death": "<PERSON><PERSON><PERSON> anw<PERSON>", "subtitles.entity.strider.eat": "Strider na -eri nri", "subtitles.entity.strider.happy": "Strider na -ama ji<PERSON>ji", "subtitles.entity.strider.hurt": "Strider <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.strider.idle": "Strider na -eti mkpu", "subtitles.entity.strider.retreat": "Strider <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.tadpole.death": "<PERSON><PERSON><PERSON><PERSON> na-anw<PERSON> anwụ", "subtitles.entity.tadpole.flop": "Mgbur<PERSON> na-ada ada", "subtitles.entity.tadpole.grow_up": "Tadpole grows up", "subtitles.entity.tadpole.hurt": "<PERSON><PERSON><PERSON><PERSON> na-<PERSON><PERSON><PERSON>", "subtitles.entity.tnt.primed": "Dinatime fizzes", "subtitles.entity.tropical_fish.death": "Azụ Tropical na -anwụ", "subtitles.entity.tropical_fish.flop": "Azụ̀ Tropical flops", "subtitles.entity.tropical_fish.hurt": "Azụ̀ Tropical na -afụ ụfụ", "subtitles.entity.turtle.ambient_land": "Mbe mbe", "subtitles.entity.turtle.death": "Mbe na -an<PERSON>", "subtitles.entity.turtle.death_baby": "Nwa mbe na -anwụ", "subtitles.entity.turtle.egg_break": "Akwa mbe na -agbaji", "subtitles.entity.turtle.egg_crack": "Akwa mbe na -agbawa", "subtitles.entity.turtle.egg_hatch": "Akwa Egtle na -amalite", "subtitles.entity.turtle.hurt": "<PERSON><PERSON> na <PERSON>a<PERSON><PERSON>", "subtitles.entity.turtle.hurt_baby": "Nwa mbe na -a<PERSON><PERSON>", "subtitles.entity.turtle.lay_egg": "Mbe na -etinye akwa", "subtitles.entity.turtle.shamble": "<PERSON> na -ama jijiji", "subtitles.entity.turtle.shamble_baby": "<PERSON>wa mbe na -ama ji<PERSON>ji", "subtitles.entity.turtle.swim": "Mbe na -egwu mmiri", "subtitles.entity.vex.ambient": "Na -agba ọsọ", "subtitles.entity.vex.charge": "Vex na -eti mkpu", "subtitles.entity.vex.death": "Vex anwụ", "subtitles.entity.vex.hurt": "Vex na -afụ <PERSON>", "subtitles.entity.villager.ambient": "Onye obodo ahụ na -akụda mmụọ", "subtitles.entity.villager.celebrate": "<PERSON>ye obodo nta", "subtitles.entity.villager.death": "Obodo bi", "subtitles.entity.villager.hurt": "<PERSON>ye obodo na -a<PERSON><PERSON>", "subtitles.entity.villager.no": "<PERSON>ye obodo ah<PERSON> e<PERSON>", "subtitles.entity.villager.trade": "<PERSON><PERSON><PERSON> ah<PERSON> obodo", "subtitles.entity.villager.work_armorer": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_butcher": "<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_cartographer": "Onye na -ese ihe na -ar<PERSON>", "subtitles.entity.villager.work_cleric": "<PERSON><PERSON><PERSON> -<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_farmer": "<PERSON>ye ọ<PERSON>ụ ugbo na -ar<PERSON>", "subtitles.entity.villager.work_fisherman": "Onye ọkụ azụ na -arụ <PERSON>", "subtitles.entity.villager.work_fletcher": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_leatherworker": "Onye na -arụ ọrụ ak<PERSON>ụkpọ anụ na -arụ ọrụ", "subtitles.entity.villager.work_librarian": "Onye na -ede akwụkwọ", "subtitles.entity.villager.work_mason": "<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_shepherd": "<PERSON><PERSON> at<PERSON><PERSON><PERSON> na -arụ <PERSON>", "subtitles.entity.villager.work_toolsmith": "Ng<PERSON><PERSON><PERSON><PERSON> -<PERSON><PERSON><PERSON>", "subtitles.entity.villager.work_weaponsmith": "<PERSON><PERSON><PERSON> agha na -a<PERSON><PERSON>", "subtitles.entity.villager.yes": "<PERSON><PERSON><PERSON> wee kweta", "subtitles.entity.vindicator.ambient": "Ndị na -eme mkp<PERSON>", "subtitles.entity.vindicator.celebrate": "Vindicator na -<PERSON><PERSON> <PERSON><PERSON>", "subtitles.entity.vindicator.death": "Vindicator nw<PERSON><PERSON><PERSON>", "subtitles.entity.vindicator.hurt": "Vindicator <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wandering_trader.ambient": "<PERSON><PERSON>nta ego na -agagharị agagharị", "subtitles.entity.wandering_trader.death": "<PERSON><PERSON>nta ego na -agagharị agagharị nwụrụ", "subtitles.entity.wandering_trader.disappeared": "<PERSON><PERSON> ah<PERSON> na -a<PERSON> aga<PERSON>ga", "subtitles.entity.wandering_trader.drink_milk": "Onye ahịa na -awagharị awagharị na -a milkụ mmiri ara", "subtitles.entity.wandering_trader.drink_potion": "Onye ahịa na -agagharị agagharị na -a potụ ọgwụ", "subtitles.entity.wandering_trader.hurt": "<PERSON><PERSON>nta ego na -awagharị awagharị", "subtitles.entity.wandering_trader.no": "<PERSON><PERSON>nta ego na -agagharị agagharị ekwenyeghị", "subtitles.entity.wandering_trader.reappeared": "<PERSON><PERSON> ahịa na -awagharị awagharị pụtara", "subtitles.entity.wandering_trader.trade": "Onye na -ere ahịa na -aga<PERSON> agagh<PERSON>", "subtitles.entity.wandering_trader.yes": "<PERSON>ye ahịa na -agagharị agagharị kwetara", "subtitles.entity.warden.agitated": "Wod<PERSON> na-as<PERSON> ude n'iwe", "subtitles.entity.warden.ambient": "Wodin na-eti mkpu", "subtitles.entity.warden.angry": "<PERSON><PERSON><PERSON> na-ewe iwe", "subtitles.entity.warden.attack_impact": "<PERSON>od<PERSON> na-aku ihe", "subtitles.entity.warden.death": "<PERSON><PERSON><PERSON> na<PERSON> anw<PERSON> anwụ", "subtitles.entity.warden.dig": "<PERSON><PERSON><PERSON> na-egwu ala", "subtitles.entity.warden.emerge": "<PERSON><PERSON><PERSON> na-a<PERSON>a", "subtitles.entity.warden.heartbeat": "<PERSON>bi wodin na-eti eti", "subtitles.entity.warden.hurt": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.warden.listening": "Woden na-a<PERSON>ọ<PERSON>", "subtitles.entity.warden.listening_angry": "Wodin na-a<PERSON><PERSON> n'iwe", "subtitles.entity.warden.nearby_close": "<PERSON>odin na-abiaru<PERSON>", "subtitles.entity.warden.nearby_closer": "<PERSON>od<PERSON> na-abia nso", "subtitles.entity.warden.nearby_closest": "<PERSON>od<PERSON> na-akpu<PERSON>be nso", "subtitles.entity.warden.roar": "Wodin na-ebigbo", "subtitles.entity.warden.sniff": "<PERSON>od<PERSON> na-ama imi", "subtitles.entity.warden.sonic_boom": "<PERSON><PERSON><PERSON> na-e<PERSON>u", "subtitles.entity.warden.sonic_charge": "<PERSON><PERSON><PERSON> na-ewe iwe", "subtitles.entity.warden.step": "<PERSON>odin na-aga ihe", "subtitles.entity.warden.tendril_clicks": "<PERSON>me wodin na-apị", "subtitles.entity.wind_charge.throw": "Wind Charge flies", "subtitles.entity.wind_charge.wind_burst": "Wind Charge bursts", "subtitles.entity.witch.ambient": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.witch.celebrate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. ụ", "subtitles.entity.witch.death": "<PERSON><PERSON><PERSON>", "subtitles.entity.witch.drink": "<PERSON><PERSON><PERSON> na -a drinksụ", "subtitles.entity.witch.hurt": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.witch.throw": "<PERSON><PERSON><PERSON>", "subtitles.entity.wither.ambient": "<PERSON> <PERSON><PERSON>we iwe", "subtitles.entity.wither.death": "<PERSON>er <PERSON> <PERSON><PERSON><PERSON>", "subtitles.entity.wither.hurt": "<PERSON>er <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.wither.shoot": "Mwak<PERSON>ku", "subtitles.entity.wither.spawn": "A t<PERSON>hapụr<PERSON> ya", "subtitles.entity.wither_skeleton.ambient": "Wither Skeleton na -agbawa", "subtitles.entity.wither_skeleton.death": "<PERSON><PERSON> nw<PERSON>", "subtitles.entity.wither_skeleton.hurt": "Wither Skeleton na -a<PERSON><PERSON>", "subtitles.entity.wolf.ambient": "<PERSON><PERSON><PERSON> wolf", "subtitles.entity.wolf.bark": "Wolf barks", "subtitles.entity.wolf.death": "<PERSON>", "subtitles.entity.wolf.growl": "<PERSON><PERSON> wolf na -eme mkp<PERSON>", "subtitles.entity.wolf.hurt": "<PERSON><PERSON> wolf na -a<PERSON><PERSON>", "subtitles.entity.wolf.pant": "Wolf pants", "subtitles.entity.wolf.shake": "<PERSON><PERSON> na -ama ji<PERSON>ji", "subtitles.entity.wolf.whine": "Wolf whines", "subtitles.entity.zoglin.ambient": "Zoglin na -agba ọsọ", "subtitles.entity.zoglin.angry": "Zoglin ji iwe na -ewe iwe", "subtitles.entity.zoglin.attack": "<PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.death": "<PERSON><PERSON><PERSON> anw<PERSON>", "subtitles.entity.zoglin.hurt": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zoglin.step": "Nzọụkw<PERSON>", "subtitles.entity.zombie.ambient": "Zombie na -as<PERSON> ude", "subtitles.entity.zombie.attack_wooden_door": "Ọnụ <PERSON>z<PERSON> na -ama jijiji", "subtitles.entity.zombie.break_wooden_door": "Ọnụ <PERSON>z<PERSON> mebiri", "subtitles.entity.zombie.converted_to_drowned": "Zombie na -atụgharị ka mmiri riri", "subtitles.entity.zombie.death": "<PERSON> nwụrụ", "subtitles.entity.zombie.destroy_egg": "Egg mbe na -agba ụkwụ", "subtitles.entity.zombie.hurt": "Zombie na -a<PERSON><PERSON>", "subtitles.entity.zombie.infect": "Zombie na -efe efe", "subtitles.entity.zombie_horse.ambient": "Zombie Horse na -ebe <PERSON>", "subtitles.entity.zombie_horse.death": "Zombie Horse nwụrụ", "subtitles.entity.zombie_horse.hurt": "Zombie Horse na -a<PERSON><PERSON>", "subtitles.entity.zombie_villager.ambient": "Zombie Villager na -<PERSON><PERSON> ude", "subtitles.entity.zombie_villager.converted": "Zombie Villager na -ekwu okwu", "subtitles.entity.zombie_villager.cure": "Zombie Villager na -amị amị", "subtitles.entity.zombie_villager.death": "Zombie Villager nwụrụ", "subtitles.entity.zombie_villager.hurt": "Zombie Villager na <PERSON><PERSON><PERSON><PERSON>", "subtitles.entity.zombified_piglin.ambient": "<PERSON><PERSON> gomenti dị egwu", "subtitles.entity.zombified_piglin.angry": "<PERSON><PERSON> zombified na -ewe iwe", "subtitles.entity.zombified_piglin.death": "Zombified Piglin nwụrụ", "subtitles.entity.zombified_piglin.hurt": "<PERSON><PERSON> na-eyi egwu", "subtitles.event.mob_effect.bad_omen": "Omen takes hold", "subtitles.event.mob_effect.raid_omen": "Raid looms nearby", "subtitles.event.mob_effect.trial_omen": "Ominous trial looms nearby", "subtitles.event.raid.horn": "<PERSON><PERSON><PERSON> onwe ya na -ap<PERSON>ta", "subtitles.item.armor.equip": "Gear na -akwadebe", "subtitles.item.armor.equip_chain": "<PERSON>les ngwa agha", "subtitles.item.armor.equip_diamond": "<PERSON><PERSON> mkpuchi agha diamond", "subtitles.item.armor.equip_elytra": "<PERSON><PERSON> pụtara aha", "subtitles.item.armor.equip_gold": "Ngwongwo ọla edo ọla edo", "subtitles.item.armor.equip_iron": "Ngwongwo ígwè nke ígwè", "subtitles.item.armor.equip_leather": "Akpụkpọ anụ na -eme mkpọtụ", "subtitles.item.armor.equip_netherite": "Ng<PERSON><PERSON> agha nke ndị ezinụlọ Netherland", "subtitles.item.armor.equip_turtle": "Shell Turtle na -ama jijiji", "subtitles.item.armor.equip_wolf": "Wolf Armor is fastened", "subtitles.item.armor.unequip_wolf": "Wolf Armor snips away", "subtitles.item.axe.scrape": "Anyụike scraps", "subtitles.item.axe.strip": "<PERSON><PERSON><PERSON> ibe", "subtitles.item.axe.wax_off": "<PERSON><PERSON><PERSON><PERSON> waksị", "subtitles.item.bone_meal.use": "Nri ọ<PERSON>ụkpụ na-agbaji", "subtitles.item.book.page_turn": "<PERSON> na -ama jijiji", "subtitles.item.book.put": "Ịgụ akwụkwọ", "subtitles.item.bottle.empty": "Kalama na -atọgbọ", "subtitles.item.bottle.fill": "<PERSON><PERSON><PERSON>", "subtitles.item.brush.brushing.generic": "Brushing", "subtitles.item.brush.brushing.gravel": "Brushing Gravel", "subtitles.item.brush.brushing.gravel.complete": "Brushing Gravel completed", "subtitles.item.brush.brushing.sand": "Brushing Sand", "subtitles.item.brush.brushing.sand.complete": "Brushing Sand completed", "subtitles.item.bucket.empty": "Ịwụ na -ebup<PERSON>ta ihe", "subtitles.item.bucket.fill": "Ịwụ jup<PERSON>tara", "subtitles.item.bucket.fill_axolotl": "Axolotl pụtara", "subtitles.item.bucket.fill_fish": "E jidere a<PERSON>", "subtitles.item.bucket.fill_tadpole": "<PERSON><PERSON><PERSON>", "subtitles.item.bundle.drop_contents": "Ịwụ na -ebup<PERSON>ta ihe", "subtitles.item.bundle.insert": "Akwajuru ihe", "subtitles.item.bundle.insert_fail": "Bundle full", "subtitles.item.bundle.remove_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> ihe", "subtitles.item.chorus_fruit.teleport": "Teleports onye ọkpụkpọ", "subtitles.item.crop.plant": "<PERSON><PERSON><PERSON>", "subtitles.item.crossbow.charge": "Crossbow na -akwụ ụgwọ", "subtitles.item.crossbow.hit": "Arrow hits", "subtitles.item.crossbow.load": "Crossbow na -ebu ibu", "subtitles.item.crossbow.shoot": "Ọkụ Crossbow", "subtitles.item.dye.use": "Ụcha agba agba", "subtitles.item.elytra.flying": "Swoosh", "subtitles.item.firecharge.use": "Fireball na -egwu egwu", "subtitles.item.flintandsteel.use": "Flint na Steel pịa", "subtitles.item.glow_ink_sac.use": "Akpa Ink na-egbuke egbuke splotches", "subtitles.item.goat_horn.play": "<PERSON><PERSON> ewu na-egwu egwu", "subtitles.item.hoe.till": "<PERSON><PERSON> na -ekwu", "subtitles.item.honey_bottle.drink": "<PERSON><PERSON><PERSON><PERSON><PERSON> ndụ", "subtitles.item.honeycomb.wax_on": "Mee na", "subtitles.item.horse_armor.unequip": "Horse Armor snips away", "subtitles.item.ink_sac.use": "Akpa ink splotches", "subtitles.item.lead.break": "Lead snaps", "subtitles.item.lead.tied": "Lead tied", "subtitles.item.lead.untied": "Lead untied", "subtitles.item.llama_carpet.unequip": "Carpet snips away", "subtitles.item.lodestone_compass.lock": "Lodestone kompas akpọchi na Lodestone", "subtitles.item.mace.smash_air": "<PERSON> smashes", "subtitles.item.mace.smash_ground": "<PERSON> smashes", "subtitles.item.nether_wart.plant": "Crop planted", "subtitles.item.ominous_bottle.dispose": "Bottle breaks", "subtitles.item.saddle.unequip": "Saddle snips away", "subtitles.item.shears.shear": "<PERSON><PERSON> pịa", "subtitles.item.shears.snip": "<PERSON><PERSON> snip", "subtitles.item.shield.block": "<PERSON><PERSON>", "subtitles.item.shovel.flatten": "Sh<PERSON>vel na -a<PERSON>batị", "subtitles.item.spyglass.stop_using": "Spyglass na-eweghachi azụ", "subtitles.item.spyglass.use": "Spyglass na-agbasawanye", "subtitles.item.totem.use": "Totem na -<PERSON><PERSON>", "subtitles.item.trident.hit": "Ọkpụkpụ Trident", "subtitles.item.trident.hit_ground": "Trident na -ama jijiji", "subtitles.item.trident.return": "Trident al<PERSON>", "subtitles.item.trident.riptide": "Trident na -abịa", "subtitles.item.trident.throw": "Mkpọchi Trident", "subtitles.item.trident.thunder": "Trident égbè eluigwe g<PERSON> agbawa", "subtitles.item.wolf_armor.break": "<PERSON>or breaks", "subtitles.item.wolf_armor.crack": "Wolf Armor cracks", "subtitles.item.wolf_armor.damage": "Wolf Armor takes damage", "subtitles.item.wolf_armor.repair": "Wolf Armor is repaired", "subtitles.particle.soul_escape": "<PERSON><PERSON><PERSON><PERSON><PERSON> obi ag<PERSON>", "subtitles.ui.cartography_table.take_result": "Esere eserese", "subtitles.ui.hud.bubble_pop": "Breath meter dropping", "subtitles.ui.loom.take_result": "<PERSON><PERSON><PERSON>", "subtitles.ui.stonecutter.take_result": "<PERSON><PERSON> a<PERSON>", "subtitles.weather.rain": "<PERSON><PERSON><PERSON> na <PERSON>e<PERSON>", "symlink_warning.message": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.pack": "Loading packs with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.message.world": "Loading worlds from folders with symbolic links can be unsafe if you don't know exactly what you are doing. Please visit %s to learn more.", "symlink_warning.more_info": "<PERSON><PERSON> nd<PERSON>", "symlink_warning.title": "World folder contains symbolic links", "symlink_warning.title.pack": "Added pack(s) contain(s) symbolic links", "symlink_warning.title.world": "The world folder contains symbolic links", "team.collision.always": "Mgbe niile", "team.collision.never": "Ọ dịgh<PERSON> mgbe", "team.collision.pushOtherTeams": "Push ndị otu nd<PERSON>", "team.collision.pushOwnTeam": "<PERSON><PERSON> otu", "team.notFound": "<PERSON><PERSON> am<PERSON>ị ama'%s'", "team.visibility.always": "Mgbe niile", "team.visibility.hideForOtherTeams": "Zoo maka otu nd<PERSON>", "team.visibility.hideForOwnTeam": "Zoo maka otu nke gị", "team.visibility.never": "Ọ dịghị", "telemetry.event.advancement_made.description": "Understanding the context behind receiving an advancement can help us better understand and improve the progression of the game.", "telemetry.event.advancement_made.title": "Advancement Made", "telemetry.event.game_load_times.description": "This event can help us figure out where startup performance improvements are needed by measuring the execution times of the startup phases.", "telemetry.event.game_load_times.title": "Game Load Times", "telemetry.event.optional": "%s (Nhọrọ)", "telemetry.event.optional.disabled": "%s (Nhọrọ) - Gbanyụọ", "telemetry.event.performance_metrics.description": "Knowing the overall performance profile of Minecraft helps us tune and optimize the game for a wide range of machine specifications and operating systems. \nGame version is included to help us compare the performance profile for new versions of Minecraft.", "telemetry.event.performance_metrics.title": "Performance Metrics", "telemetry.event.required": "%s (Required)", "telemetry.event.world_load_times.description": "It's important for us to understand how long it takes to join a world, and how that changes over time. For example, when we add new features or do larger technical changes, we need to see what impact that had on load times.", "telemetry.event.world_load_times.title": "World Load Times", "telemetry.event.world_loaded.description": "Knowing how players play Minecraft (such as Game Mode, client or server modded, and game version) allows us to focus game updates to improve the areas that players care about most.\nThe World Loaded event is paired with the World Unloaded event to calculate how long the play session has lasted.", "telemetry.event.world_loaded.title": "World Loaded", "telemetry.event.world_unloaded.description": "This event is paired with the World Loaded event to calculate how long the world session has lasted.\nThe duration (in seconds and ticks) is measured when a world session has ended (quitting to title, disconnecting from a server).", "telemetry.event.world_unloaded.title": "World Unloaded", "telemetry.property.advancement_game_time.title": "Game Time (Ticks)", "telemetry.property.advancement_id.title": "Advancement ID", "telemetry.property.client_id.title": "Client ID", "telemetry.property.client_modded.title": "<PERSON><PERSON>", "telemetry.property.dedicated_memory_kb.title": "Dedicated Memory (kB)", "telemetry.property.event_timestamp_utc.title": "Event Timestamp (UTC)", "telemetry.property.frame_rate_samples.title": "Frame Rate Samples (FPS)", "telemetry.property.game_mode.title": "Game Mode", "telemetry.property.game_version.title": "Game Version", "telemetry.property.launcher_name.title": "Launcher Name", "telemetry.property.load_time_bootstrap_ms.title": "Bootstrap Time (Milliseconds)", "telemetry.property.load_time_loading_overlay_ms.title": "Time in Loading Screen (Milliseconds)", "telemetry.property.load_time_pre_window_ms.title": "Time Before Window Opens (Milliseconds)", "telemetry.property.load_time_total_time_ms.title": "Total Load Time (Milliseconds)", "telemetry.property.minecraft_session_id.title": "Minecraft Session ID", "telemetry.property.new_world.title": "New World", "telemetry.property.number_of_samples.title": "Sample Count", "telemetry.property.operating_system.title": "Operating System", "telemetry.property.opt_in.title": "Opt-In", "telemetry.property.platform.title": "Platform", "telemetry.property.realms_map_content.title": "Realms Map Content (Minigame Name)", "telemetry.property.render_distance.title": "Render Distance", "telemetry.property.render_time_samples.title": "Render Time Samples", "telemetry.property.seconds_since_load.title": "Time Since Load (Seconds)", "telemetry.property.server_modded.title": "Server Modded", "telemetry.property.server_type.title": "Server Type", "telemetry.property.ticks_since_load.title": "Time Since Load (Ticks)", "telemetry.property.used_memory_samples.title": "Used Random Access Memory", "telemetry.property.user_id.title": "User ID", "telemetry.property.world_load_time_ms.title": "World Load Time (Milliseconds)", "telemetry.property.world_session_id.title": "World Session ID", "telemetry_info.button.give_feedback": "<PERSON><PERSON>", "telemetry_info.button.privacy_statement": "Privacy Statement", "telemetry_info.button.show_data": "View My Data", "telemetry_info.opt_in.description": "I consent to sending optional telemetry data", "telemetry_info.property_title": "Included Data", "telemetry_info.screen.description": "Collecting this data helps us improve Minecraft by guiding us in directions that are relevant to our players.\nYou can also send in additional feedback to help us keep improving Minecraft.", "telemetry_info.screen.title": "Telemetry Data Collection", "test.error.block_property_mismatch": "Expected property %s to be %s, was %s", "test.error.block_property_missing": "Block property missing, expected property %s to be %s", "test.error.entity_property": "Entity %s failed test: %s", "test.error.entity_property_details": "Entity %s failed test: %s, expected: %s, was: %s", "test.error.expected_block": "Expected block %s, got %s", "test.error.expected_block_tag": "Expected block in #%s, got %s", "test.error.expected_container_contents": "Container should contain: %s", "test.error.expected_container_contents_single": "Container should contain a single: %s", "test.error.expected_empty_container": "Container should be empty", "test.error.expected_entity": "Expected %s", "test.error.expected_entity_around": "Expected %s to exist around %s, %s, %s", "test.error.expected_entity_count": "Expected %s entities of type %s, found %s", "test.error.expected_entity_data": "Expected entity data to be: %s, was: %s", "test.error.expected_entity_data_predicate": "Entity data mismatch for %s", "test.error.expected_entity_effect": "Expected %s to have effect %s %s", "test.error.expected_entity_having": "Entity inventory should contain %s", "test.error.expected_entity_holding": "Entity should be holding %s", "test.error.expected_entity_in_test": "Expected %s to exist in test", "test.error.expected_entity_not_touching": "Did not expect %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_entity_touching": "Expected %s touching %s, %s, %s (relative: %s, %s, %s)", "test.error.expected_item": "Expected item of type %s", "test.error.expected_items_count": "Expected %s items of type %s, found %s", "test.error.fail": "Fail conditions met", "test.error.invalid_block_type": "Unexpected block type found: %s", "test.error.missing_block_entity": "Missing block entity", "test.error.position": "%s at %s, %s, %s (relative: %s, %s, %s) on tick %s", "test.error.sequence.condition_already_triggered": "Condition already triggered at %s", "test.error.sequence.condition_not_triggered": "Condition not triggered", "test.error.sequence.invalid_tick": "Succeeded in invalid tick: expected %s", "test.error.sequence.not_completed": "Test timed out before sequence completed", "test.error.set_biome": "Failed to set biome for test", "test.error.spawn_failure": "Failed to create entity %s", "test.error.state_not_equal": "Incorrect state. Expected %s, was %s", "test.error.structure.failure": "Failed to place test structure for %s", "test.error.tick": "%s on tick %s", "test.error.ticking_without_structure": "Ticking test before placing structure", "test.error.timeout.no_result": "Didn't succeed or fail within %s ticks", "test.error.timeout.no_sequences_finished": "No sequences finished within %s ticks", "test.error.too_many_entities": "Expected only one %s to exist around %s, %s, %s but found %s", "test.error.unexpected_block": "Did not expect block to be %s", "test.error.unexpected_entity": "Did not expect %s to exist", "test.error.unexpected_item": "Did not expect item of type %s", "test.error.unknown": "Unknown internal error: %s", "test.error.value_not_equal": "Expected %s to be %s, was %s", "test.error.wrong_block_entity": "Wrong block entity type: %s", "test_block.error.missing": "Test structure missing %s block", "test_block.error.too_many": "Too many %s blocks", "test_block.invalid_timeout": "Invalid timeout (%s) - must be a positive number of ticks", "test_block.message": "Message:", "test_block.mode.accept": "Accept", "test_block.mode.fail": "Fail", "test_block.mode.log": "Log", "test_block.mode.start": "Start", "test_block.mode_info.accept": "Accept Mode - Accept success for (part of) a test", "test_block.mode_info.fail": "Fail Mode - Fail the test", "test_block.mode_info.log": "Log Mode - Log a message", "test_block.mode_info.start": "Start Mode - The starting point for a test", "test_instance.action.reset": "Reset and Load", "test_instance.action.run": "Load and Run", "test_instance.action.save": "Save Structure", "test_instance.description.batch": "Batch: %s", "test_instance.description.failed": "Failed: %s", "test_instance.description.function": "Function: %s", "test_instance.description.invalid_id": "Invalid test ID", "test_instance.description.no_test": "No such test", "test_instance.description.structure": "Structure: %s", "test_instance.description.type": "Type: %s", "test_instance.type.block_based": "Block-Based Test", "test_instance.type.function": "Built-in Function Test", "test_instance_block.entities": "Entities:", "test_instance_block.error.no_test": "Unable to run test instance at %s, %s, %s since it has an undefined test", "test_instance_block.error.no_test_structure": "Unable to run test instance at %s, %s, %s since it has no test structure", "test_instance_block.error.unable_to_save": "Unable to save test structure template for test instance at %s, %s, %s", "test_instance_block.invalid": "[invalid]", "test_instance_block.reset_success": "Reset succeeded for test: %s", "test_instance_block.rotation": "Rotation:", "test_instance_block.size": "Test Structure Size", "test_instance_block.starting": "Starting test %s", "test_instance_block.test_id": "Test Instance ID", "title.32bit.deprecation": "Achọpụtara usoro biiti iri atọ na abụọ: nkea were ike igbochi gị igwu egwu n'odịnihu maka na a ga achọ usoro biiti iri isii na anọ!", "title.32bit.deprecation.realms": "N'oge na-ad<PERSON><PERSON><PERSON> anya Minecraft ga-achọ sistemụ 64-bit, nke ga-egbochi gị igwu egwu ma ọ bụ iji Realms na ngwaọrụ a. Ị ga-achọ iji aka g<PERSON> kagbuo ndenye aha Realms ọ bụla.", "title.32bit.deprecation.realms.check": "Do not show this screen again", "title.32bit.deprecation.realms.header": "Achọpụtara usoro biiti iri atọ na abụọ", "title.credits": "Copyright Mojang AB. Do not distribute!", "title.multiplayer.disabled": "Multiplayer is disabled. Please check your Microsoft account settings.", "title.multiplayer.disabled.banned.name": "You must change your name before you can play online", "title.multiplayer.disabled.banned.permanent": "Akwụs<PERSON><PERSON> akaụnt<PERSON> gị na egwuregwu ịntanetị kpamkpam", "title.multiplayer.disabled.banned.temporary": "Akwụ<PERSON><PERSON><PERSON> akaụ<PERSON><PERSON> gị nwa oge na egwuregwu <PERSON>ntanetị", "title.multiplayer.lan": "<PERSON><PERSON><PERSON> (LAN)", "title.multiplayer.other": "<PERSON><PERSON><PERSON> (<PERSON><PERSON> otu ato nke ato)", "title.multiplayer.realms": "<PERSON><PERSON><PERSON> (Realms)", "title.singleplayer": "Singleplayer", "translation.test.args": "%s %s", "translation.test.complex": "Nganiihu, %2$s ọzọ %s na %1$s ikpeazu %s nakwa %1$s ọzọ!", "translation.test.escape": "%%%%s %%%s %%%%s %%%%%s", "translation.test.invalid": "ndewo %", "translation.test.invalid2": "ndewo %s", "translation.test.none": "Ndewo, Ụwa!", "translation.test.world": "ụ<PERSON>", "trim_material.minecraft.amethyst": "Amethyst Material", "trim_material.minecraft.copper": "Copper Material", "trim_material.minecraft.diamond": "Diamond Material", "trim_material.minecraft.emerald": "Emerald Material", "trim_material.minecraft.gold": "Gold Material", "trim_material.minecraft.iron": "<PERSON>g<PERSON><PERSON> ihe", "trim_material.minecraft.lapis": "Lapis Material", "trim_material.minecraft.netherite": "<PERSON><PERSON> onwu<PERSON>her<PERSON>", "trim_material.minecraft.quartz": "Quartz Material", "trim_material.minecraft.redstone": "Redstone Material", "trim_material.minecraft.resin": "Resin Material", "trim_pattern.minecraft.bolt": "Bolt Armor Trim", "trim_pattern.minecraft.coast": "Coast Armor Trim", "trim_pattern.minecraft.dune": "<PERSON>ne Armor <PERSON>", "trim_pattern.minecraft.eye": "Eye Armor Trim", "trim_pattern.minecraft.flow": "Flow Armor Trim", "trim_pattern.minecraft.host": "Host <PERSON><PERSON>", "trim_pattern.minecraft.raiser": "Raiser Armor Trim", "trim_pattern.minecraft.rib": "<PERSON><PERSON>", "trim_pattern.minecraft.sentry": "Sentry Armor Trim", "trim_pattern.minecraft.shaper": "<PERSON><PERSON><PERSON> Armor <PERSON>", "trim_pattern.minecraft.silence": "Silence Armor Trim", "trim_pattern.minecraft.snout": "Snout Armor Trim", "trim_pattern.minecraft.spire": "Spire Arm<PERSON>", "trim_pattern.minecraft.tide": "Tide Armor Trim", "trim_pattern.minecraft.vex": "Vex Armor Trim", "trim_pattern.minecraft.ward": "<PERSON>", "trim_pattern.minecraft.wayfinder": "Wayfinder Armor Trim", "trim_pattern.minecraft.wild": "Wild Armor Trim", "tutorial.bundleInsert.description": "Right <PERSON><PERSON><PERSON>he", "tutorial.bundleInsert.title": "<PERSON><PERSON>", "tutorial.craft_planks.description": "Akwụkwọ nri nwere ike inye aka", "tutorial.craft_planks.title": "<PERSON><PERSON><PERSON> osisi e ji osisi rụọ", "tutorial.find_tree.description": "Punch ya <PERSON>k<PERSON> nkụ", "tutorial.find_tree.title": "<PERSON><PERSON><PERSON>", "tutorial.look.description": "<PERSON><PERSON> tụ<PERSON>", "tutorial.look.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> anya", "tutorial.move.description": "Wụsa na %s", "tutorial.move.title": "Jiri %s, %s, %s na %s kwaghari", "tutorial.open_inventory.description": "Pịa %s", "tutorial.open_inventory.title": "<PERSON><PERSON><PERSON> ngwa ah<PERSON>a gị", "tutorial.punch_tree.description": "Jide %s", "tutorial.punch_tree.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> ah<PERSON>", "tutorial.socialInteractions.description": "Pịa %s ka imepee", "tutorial.socialInteractions.title": "Social Interactions", "upgrade.minecraft.netherite_upgrade": "Nkwalite Netherite"}